package com.newnary.gsp.center.tpsi.api.haiying.response.shopee;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingShopeeSubCategoryInfoDTO {

    /**
     * 类目ID
     */
    private String cid;

    /**
     * 类目名称
     */
    private String cname;

    /**
     * 类目等级
     */
    private Integer level;

    /**
     * 一级类目id
     */
    private String p_l1_id;

    /**
     * 一级类目名称
     */
    private String p_l1_name;

    /**
     * 二级类目id
     */
    private String p_l2_id;

    /**
     * 二级类目名称
     */
    private String p_l2_name;

    /**
     * 类目最新抓取时间
     */
    private Long insert_time;

    /**
     * 类目统计时间
     */
    private Long stat_time;

    /**
     * 类目销售件数占一级类目销售件数百分比
     */
    private BigDecimal products_num_sub_top_percent;

    /**
     * 类目销售额占一级类目销售额百分比
     */
    private BigDecimal historical_sold_sub_top_percent;

    /**
     * 类目商品总数
     */
    private Integer products_num;

    /**
     * 类目商品总数(本地)
     */
    private Integer local_products_num;

    /**
     * 类目商品总数(海外)
     */
    private Integer overseas_products_num;

    /**
     * 类目总销售件数
     */
    private Integer historical_sold;

    /**
     * 类目总销售件数(本地)
     */
    private Integer local_historical_sold;

    /**
     * 类目总销售件数(海外)
     */
    private Integer overseas_historical_sold;

    /**
     * 类目前30天销售件数占一级类目前30天销售件数百分比
     */
    private BigDecimal sold_sub_top_percent;

    /**
     * 类目前30天销售件数
     */
    private Integer sold;

    /**
     * 类目前30天销售件数(本地)
     */
    private Integer local_sold;

    /**
     * 类目前30天销售件数(海外)
     */
    private Integer overseas_sold;

    /**
     * 类目前30天销售金额占一级类目前30天总销售金额百分比
     */
    private BigDecimal payment_sub_top_percent;

    /**
     * 类目前30天销售金额
     */
    private BigDecimal payment;

    /**
     * 类目前30天销售金额(本地)
     */
    private BigDecimal local_payment;

    /**
     * 类目前30天销售金额(海外)
     */
    private BigDecimal overseas_payment;

}
