package com.newnary.gsp.center.tpsi.api.jt.request.cn;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * 获取面单 JT-cn 物流运输单，请求体
 *
 * <AUTHOR>
 * @since Created on 2023-08-11
 **/
@Data
public class SheetOrderCnJTCommand {

    /**
     * 多个 pdf 是否合并为一个。0:否。1:是。目前版本固定为1
     */
    @NotNull(message = "mergePdf(不能为空)")
    private Integer mergePdf = 1;

    @NotEmpty(message = "nos(不能为空)")
    private List<String> nos = new ArrayList<>();


}
