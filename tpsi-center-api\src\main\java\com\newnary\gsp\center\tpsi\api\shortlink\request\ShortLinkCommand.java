package com.newnary.gsp.center.tpsi.api.shortlink.request;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 缩链短链转换
 *  * <AUTHOR>
 *  * @since Created on 2024-01-19
 **/
@ApiModel
@Data
public class ShortLinkCommand {

    @NotNull(message = "url(不能为空)")
    private String url;

    @NotNull(message = "商城域名(不能为空)")
    private String sourceDomain;

    private String groupSid;

}
