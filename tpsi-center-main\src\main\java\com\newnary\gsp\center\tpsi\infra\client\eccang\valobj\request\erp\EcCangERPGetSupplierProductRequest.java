package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Setter
@Getter
public class EcCangERPGetSupplierProductRequest {

    /**
     * 页码
     */
    private Integer page;

    /**
     * 每页数据量 默认20，最大200
     */
    private Integer pageSize;

    /**
     * 产品sku【格式示例"sku": "sku1 sku2 sku3"】
     */
    private String sku;

    /**
     * 默认供应商代码
     */
    private String defaultSupplierCode;

    /**
     * 产品名称
     */
    private String productTitle;

    /**
     * 供应商品号
     */
    private String spSupplierSku;

    /**
     * 供应商Id， 请参考业务接口>供应商>供应商列表
     */
    private Integer supplierId;

    /**
     * 供应商代码， 请参考业务接口>供应商>供应商列表(待上线)
     */
    private String supplierCode;

    /**
     * 默认采购员用户id
     */
    private Integer buyerId;

    /**
     * 品类id
     */
    private Integer categoryId;

    /**
     * 销售状态, 请参考业务接口>产品>获取产品销售状态
     *  字段ps_id
     */
    private Integer saleStatus;

    /**
     * 是否维护地址， 0否 1是
     */
    private String isHasAddress;

    /**
     * 供应商产品
     */
    private SupplierProduct supplierProduct;

    /**
     * 全部：ALL、不可用：0、 可用：1、开发中产品：2（不传默可用【1】）
     */
    private String productStatus;

    public static class SupplierProduct {

    }

}
