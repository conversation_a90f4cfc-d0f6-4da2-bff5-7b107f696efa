package com.newnary.gsp.center.tpsi.service.shortlink.Impl;

import com.alibaba.fastjson.JSONObject;
import com.newnary.gsp.center.tpsi.api.shortlink.request.ShortLinkCommand;
import com.newnary.gsp.center.tpsi.api.shortlink.response.ConvertShortLinkResp;
import com.newnary.gsp.center.tpsi.infra.client.soulink.SouLinkApiClient;
import com.newnary.gsp.center.tpsi.infra.client.soulink.valobj.Response.SouLinkDataApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.LogisticsServiceConstants;
import com.newnary.gsp.center.tpsi.infra.model.vo.LogisticsServiceContext;
import com.newnary.gsp.center.tpsi.infra.model.vo.ShortLinkServiceConstants;
import com.newnary.gsp.center.tpsi.infra.model.vo.ShortLinkServiceContext;
import com.newnary.gsp.center.tpsi.service.impl.SystemClientSve;
import com.newnary.gsp.center.tpsi.service.shortlink.IShortLinkiSve;
import com.newnary.spring.cloud.extend.Extend;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Extend("SUOLINK")
@Component
public class SuoLinkApiSveImpl extends SystemClientSve implements IShortLinkiSve {

    public static final Logger LOGGER = LoggerFactory.getLogger(SuoLinkApiSveImpl.class);

    private static final String thirdPartySystemId = "SUOLINK";

    private SouLinkApiClient client;

    private void init() {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        //获取apiClient
        client = getClient(thirdPartySystem.getParams());
    }

    private SouLinkApiClient getClient(String maBangParams) {
        return new SouLinkApiClient(maBangParams);
    }

    @Override
    public void convertShortLink() {
        init();
        ShortLinkCommand command = (ShortLinkCommand) ShortLinkServiceContext.getCurrentContext().get(ShortLinkServiceConstants.SHORTLINK_CONVERT_DOMAIN);
        SouLinkDataApiBaseResult<String> result = client.convertShortLink(command.getUrl(),command.getSourceDomain());
        ShortLinkServiceContext.getCurrentContext().put(ShortLinkServiceConstants.SHORTLINK_CONVERT_RESULT_DOMAIN, JSONObject.parseObject(result.getResult(), ConvertShortLinkResp.class));
    }
}
