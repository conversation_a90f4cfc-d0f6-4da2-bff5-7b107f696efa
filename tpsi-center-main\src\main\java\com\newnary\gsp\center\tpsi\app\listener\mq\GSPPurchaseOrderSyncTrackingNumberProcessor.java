package com.newnary.gsp.center.tpsi.app.listener.mq;

import com.newnary.api.base.constants.HeaderNameConstants;
import com.newnary.gsp.center.tpsi.api.eccang.request.GspSyncPo2EccangCommand;
import com.newnary.gsp.center.tpsi.app.service.eccang.EccangPoMgmtApp;
import com.newnary.gsp.center.tpsi.app.service.eccang.StockoutPoMgmtApp;
import com.newnary.gsp.center.tpsi.infra.mq.consumer.GSPPurchaseOrderMQConsumer;
import com.newnary.messagebody.gsp.purchase.GSPPurchaseOrderTopic;
import com.newnary.messagebody.gsp.purchase.mo.GspPurchaseOrderTakeStockMO;
import com.newnary.mq.starter.consumer.AbstractMQProcessor;
import com.newnary.mq.starter.consumer.MQMessage;
import com.newnary.spring.cloud.context.RequestContext;
import com.newnary.tenant.context.TenantCarrier;
import com.newnary.tenant.context.TenantID;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

@Slf4j
@Component
public class GSPPurchaseOrderSyncTrackingNumberProcessor extends AbstractMQProcessor<GspPurchaseOrderTakeStockMO> {

    @Resource
    private EccangPoMgmtApp eccangPoMgmtApp;

    @Resource
    private StockoutPoMgmtApp stockoutPoMgmtApp;

    @Value("${purchase-center.sync.trackingNumber.tenant_id}")
    private String tenantId;

    @Override
    public boolean doProcess(MQMessage<GspPurchaseOrderTakeStockMO> message) {
        try {
            String currentTenantId = TenantCarrier.getTenantID().get().toString();
            log.info("同步采购单物流单号消费开始，当前租户信息为：{}",tenantId);
            GspPurchaseOrderTakeStockMO mo = message.getContent();
            GspSyncPo2EccangCommand command = new GspSyncPo2EccangCommand();
            command.setPurchaseOrderId(mo.getPurchaseOrderId());
            command.setCurrOptUserId(mo.getCurrOptUserId());

            // todo czh 融科采购系统--CXM项目同步物流单号到出库单
            if (StringUtils.equals(tenantId,currentTenantId)) {
                log.info("采购单快递单号同步出库单的物流单号消息消费开始：purchaseOrderId: {}", mo.getPurchaseOrderId());
                try {
                    // 消息处理
                    stockoutPoMgmtApp.syncTrackingNumber2StockoutOrder(command);
                } catch (Exception e) {
                    log.info("采购单快递单号同步出库单的物流单号消息消费异常！purchaseOrderId={}, e={}", mo.getPurchaseOrderId(), e.getMessage());
                }
            }else{
                log.info("采购单快递单号信息同步ecang采购单快递单号消息消费开始：purchaseOrderId: {}", mo.getPurchaseOrderId());
                try {
                    // 消息处理
                    eccangPoMgmtApp.syncTrackingNumber2EcangPurchaseOrder(command);
                } catch (Exception e) {
                    log.info("采购单快递单号信息同步ecang采购单快递单号消息消费异常！purchaseOrderId={}, e={}", mo.getPurchaseOrderId(), e.getMessage());
                }
            }
        } catch (Exception e) {
            log.info("获取当前租户异常 {}",e.getMessage());
        }

        return true;
    }

    /**
     * 消费者类型
     *
     * @return
     */
    @Override
    public Class<?> consumerClz() {
        return GSPPurchaseOrderMQConsumer.class;
    }

    /**
     * 标签
     *
     * @return
     */
    @Override
    public String tag() {
        return GSPPurchaseOrderTopic.Tag.SYNC_TRACKINGNUMBER;
    }
}
