package com.newnary.gsp.center.tpsi.api.haiying.request.amazon;

import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingStation;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingAmazonProductDetailInfoCommand {

    /**
     * 站点
     */
    @NotNull(message = "站点不能为空")
    private HaiYingStation station;

    /**
     * 商品id
     * (多个商品id用逗号分隔，最多100个商品id)
     */
    @NotNull(message = "商品id不能为空")
    private List<String> asins;

    /**
     * 商品排名抓取时间起始值(string型,格式:年-月-日)
     */
    private Long cate_rank_date_start;

    /**
     * 商品排名抓取时间结束值(string型,格式:年-月-日)
     */
    private Long cate_rank_date_end;

    /**
     * 商品走势数据时间起始值(string型,格式:年-月-日)
     */
    private Long asin_info_date_start;

    /**
     * 商品走势数据时间结束值(string型,格式:年-月-日)
     */
    private Long asin_info_date_end;

}
