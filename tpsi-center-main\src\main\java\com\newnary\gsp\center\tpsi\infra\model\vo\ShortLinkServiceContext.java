package com.newnary.gsp.center.tpsi.infra.model.vo;

import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: jack
 * @CreateTime: 2023-11-17
 */
public class ShortLinkServiceContext extends ConcurrentHashMap<String, Object> {

    private static final ThreadLocal<ShortLinkServiceContext> threadLocal = ThreadLocal.withInitial(ShortLinkServiceContext::new);

    public static ShortLinkServiceContext getCurrentContext() {
        return threadLocal.get();
    }

}
