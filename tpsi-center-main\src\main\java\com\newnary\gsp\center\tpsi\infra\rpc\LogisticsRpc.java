package com.newnary.gsp.center.tpsi.infra.rpc;

import com.newnary.gsp.center.logistics.api.carrier.feign.LogisticsFeignApi;
import com.newnary.gsp.center.logistics.api.carrier.response.LogisticsInfo;
import com.newnary.gsp.center.logistics.api.carrier.response.LogisticsServiceMappingInfo;
import com.newnary.gsp.center.logistics.api.warehouse.WarehouseApi;
import com.newnary.gsp.center.logistics.api.warehouse.response.WarehouseLiteRes;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2024/4/17
 */
@Component
public class LogisticsRpc {

    @Resource
    private LogisticsFeignApi logisticsFeignApi;

    @Resource
    private WarehouseApi warehouseApi;

    public List<LogisticsServiceMappingInfo> queryLogisticServiceMappingByProvider(String serviceProvider) {
        return logisticsFeignApi.queryLogisticsServiceMappingListByProvider(serviceProvider).mustSuccessOrThrowOriginal();
    }

    public List<LogisticsInfo> queryLogisticListByCode(String logisticCode) {
        return logisticsFeignApi.queryListByCode(logisticCode).mustSuccessOrThrowOriginal();
    }

    public List<WarehouseLiteRes> findWarehouseByThirdInfo(String warehouseId, String shipperId) {
        return warehouseApi.findWarehouseByThirdInfo(warehouseId,shipperId).mustSuccessOrThrowOriginal();
    }
}
