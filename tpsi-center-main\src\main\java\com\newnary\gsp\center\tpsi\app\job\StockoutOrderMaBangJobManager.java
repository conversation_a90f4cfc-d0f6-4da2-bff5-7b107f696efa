package com.newnary.gsp.center.tpsi.app.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.distributed.tools.concurrent.template.DConcurrentTemplate;
import com.newnary.gsp.center.logistics.api.delivery.dto.TrackInfoDTO;
import com.newnary.gsp.center.logistics.api.delivery.enums.CancelType;
import com.newnary.gsp.center.logistics.api.delivery.enums.StockoutOrderState;
import com.newnary.gsp.center.logistics.api.delivery.request.StockoutOrderConfirmCancelCommand;
import com.newnary.gsp.center.logistics.api.delivery.request.StockoutOrderThirdPushedCommand;
import com.newnary.gsp.center.logistics.api.delivery.request.StockoutOrderUpdateTrackCommand;
import com.newnary.gsp.center.logistics.api.delivery.request.StockoutOrderWithDeliveryQueryCommand;
import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderDetailInfo;
import com.newnary.gsp.center.logistics.api.delivery.response.StockoutOrderInfo;
import com.newnary.gsp.center.tpsi.infra.client.mabang.MaBangParams;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.MaBangOrder;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.order.MaBangDoCreateOrder;
import com.newnary.gsp.center.tpsi.infra.model.ApiRequestParams;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.ApiRequestParamsType;
import com.newnary.gsp.center.tpsi.infra.repository.IApiRequestParamsRepository;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.infra.rpc.DeliveryOrderRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.StockoutOrderRpc;
import com.newnary.gsp.center.tpsi.service.IMaBangApiDataTranslateSve;
import com.newnary.gsp.center.tpsi.service.mabang.IMaBangOrderApiSve;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.job.core.handler.annotation.Job;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class StockoutOrderMaBangJobManager {

    private static final String AUTO_CREATE_MABANG_ORDER_PREFIX = "AUTO_CREATE_MABANG_ORDER";

    private static final String AUTO_CHECK_MABANG_ORDER_PREFIX = "AUTO_CHECK_MABANG_ORDER";

    @Resource
    private StockoutOrderRpc stockoutOrderRpc;
    @Resource
    private DeliveryOrderRpc deliveryOrderRpc;

    @Resource
    private IMaBangOrderApiSve maBangOrderApiSve;
    @Resource
    private IMaBangApiDataTranslateSve maBangApiDataTranslateSve;

    @Resource
    private IApiRequestParamsRepository apiOrderCreateParamsRepository;
    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    /**
     * 查待出库出库单
     * 1、推送第三方
     * 2、成功就调用受理方法
     * 3、受理后调用thirdPushed方法
     * 4、如果2,3步骤出错，则马帮上作废订单
     *
     * @param param
     * @return
     */
    @Job("autoCreateMaBangOrder")
    public ReturnT<String> autoCreateMaBangOrder(String param) {
        log.info("定时任务推送系统待发货出库单到马帮订单 - 开始 --- {}", new Date());
        //根据参数获取需要执行的第三方系统id
        JSONObject paramObject = JSONObject.parseObject(param);
        String thirdPartySystemId = paramObject.getString("thirdPartySystemId");

        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        MaBangParams params = JSON.parseObject(thirdPartySystem.getParams(), MaBangParams.class);

        //第一步 查询租户下的马帮店铺，国内仓收货地址
        log.info("开始获取租户的马帮店铺，国内收货地址");
        Optional<ApiRequestParams> maBangOrderCreateParams = apiOrderCreateParamsRepository.loadBySystemBizIdAndType(thirdPartySystemId, ApiRequestParamsType.ORDER.name());
        if (maBangOrderCreateParams.isPresent()) {

            DConcurrentTemplate.tryLockMode(
                AUTO_CREATE_MABANG_ORDER_PREFIX.concat(param),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    log.info("执行 核心逻辑--- {}", new Date());
                    //第二步 调用出库单getListWithDelivery获取所有未发货的出库单
                    StockoutOrderWithDeliveryQueryCommand queryCommand = new StockoutOrderWithDeliveryQueryCommand();
                    queryCommand.setStockoutOrderState(StockoutOrderState.WAIT_STOCKOUT.getState());
                    queryCommand.setDeliveryOrderSupplierId(params.getSupplierId());
                    //queryCommand.setDeliveryOrderType(OrderType.SO.name());
                    List<StockoutOrderInfo> stockoutOrderInfoList = stockoutOrderRpc.getListWithDelivery(queryCommand);
                    if (CollectionUtils.isNotEmpty(stockoutOrderInfoList)) {
                        int resultSize = stockoutOrderInfoList.size();
                        log.info("获取的待出库状态出库单数量为{}", resultSize);
                        stockoutOrderInfoList.forEach(stockoutOrderInfo -> {
                            log.info("现在获取出库单{}关联的发货单{}", stockoutOrderInfo.getStockoutOrderId(), stockoutOrderInfo.getReferenceId());
                            //第三步 获取发货单详细信息
                            DeliveryOrderDetailInfo deliveryOrderDetailInfo = deliveryOrderRpc.getDetailInfo(stockoutOrderInfo.getReferenceId());
                            if (null != deliveryOrderDetailInfo) {
                                log.info("发货单{}详情已获取", stockoutOrderInfo.getReferenceId());
                                //第四步 创建马帮订单
                                MaBangDoCreateOrder req = maBangApiDataTranslateSve.buildMaBangDoCreateOrder(stockoutOrderInfo.getStockoutOrderId(), deliveryOrderDetailInfo, maBangOrderCreateParams.get());
                                String apiResult = maBangOrderApiSve.orderDoCreateOrder(thirdPartySystemId, req);

                                if (apiResult.equals("SUCCESS")) {
                                    log.info("{}创建马帮订单成功", stockoutOrderInfo.getStockoutOrderId());
                                    //第五步 调用出库单受理接口(考虑如果报错应该怎么回退)

                                    StockoutOrderThirdPushedCommand stockoutOrderThirdPushedCommand = new StockoutOrderThirdPushedCommand();
                                    stockoutOrderThirdPushedCommand.setStockoutOrderId(stockoutOrderInfo.getStockoutOrderId());
                                    stockoutOrderThirdPushedCommand.setThirdOrderId(req.getPlatformOrderId());

                                    try {
                                        stockoutOrderRpc.accept(stockoutOrderInfo.getStockoutOrderId());
                                        stockoutOrderRpc.thirdPushed(stockoutOrderThirdPushedCommand);
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                        log.error("{}调用受理方法出错", stockoutOrderInfo.getStockoutOrderId());
                                        //受理失败则作废订单
                                        maBangOrderApiSve.invalidOrder(thirdPartySystemId, req.getPlatformOrderId());
                                    }
                                } else {
                                    log.error("{}创建马帮订单失败{}", stockoutOrderInfo.getStockoutOrderId(), apiResult);
                                }
                            } else {
                                log.warn("出库单{}获取不到关联发货单{}", stockoutOrderInfo.getStockoutOrderId(), stockoutOrderInfo.getReferenceId());
                            }

                        });
                    }
                }
            );

        } else {
            log.error("该租户缺失创建马帮订单的必要参数");
            return ReturnT.FAIL;
        }
        log.info("定时任务推送系统待发货出库单到马帮订单 - 结束 --- {}", new Date());
        return ReturnT.SUCCESS;
    }

    /**
     * 查出库中出库单
     * 1、如果未推送第三方，则先推送第三方
     * 2、如果第三方订单是否已作废，是则调用取消方法
     * 3、如果第三方订单未作废，则判断第三方是否已有tn,是则调用发货方法
     *
     * @param param
     * @return
     */
    @Job("autoCheckMaBangOrderByStockoutOrder")
    public ReturnT<String> autoCheckMaBangOrderByStockoutOrder(String param) {
        log.info("定时任务检查马帮订单 - 开始 --- {}", new Date());
        //从参数中获取需要执行的第三方系统id
        JSONObject paramObject = JSONObject.parseObject(param);
        String thirdPartySystemId = paramObject.getString("thirdPartySystemId");
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        MaBangParams params = JSON.parseObject(thirdPartySystem.getParams(), MaBangParams.class);

        DConcurrentTemplate.tryLockMode(
            AUTO_CHECK_MABANG_ORDER_PREFIX.concat(param),
            lock -> lock.tryLock(3, TimeUnit.SECONDS),
            () -> {
                log.info("执行 核心逻辑--- {}", new Date());
                //调用出库单getListWithDelivery获取所有出库中的出库单
                StockoutOrderWithDeliveryQueryCommand queryCommand = new StockoutOrderWithDeliveryQueryCommand();
                queryCommand.setStockoutOrderState(StockoutOrderState.STOCKOUTING.getState());
                queryCommand.setDeliveryOrderSupplierId(params.getSupplierId());
                //queryCommand.setDeliveryOrderType(OrderType.SO.name());
                List<StockoutOrderInfo> stockoutOrderInfoList = stockoutOrderRpc.getListWithDelivery(queryCommand);
                if (CollectionUtils.isNotEmpty(stockoutOrderInfoList)) {
                    int resultSize = stockoutOrderInfoList.size();
                    log.info("获取的出库中状态出库单数量为{}", resultSize);
                    log.info("开始获取租户的马帮店铺，国内收货地址");
                    Optional<ApiRequestParams> maBangOrderCreateParams = apiOrderCreateParamsRepository.loadBySystemBizIdAndType(thirdPartySystemId, ApiRequestParamsType.ORDER.name());
                    //调用马帮查询订单接口
                    stockoutOrderInfoList.forEach(stockoutOrderInfo -> {
                        String stockoutOrderId = stockoutOrderInfo.getStockoutOrderId();
                        log.info("现在获取出库单{}关联的发货单{}", stockoutOrderId, stockoutOrderInfo.getReferenceId());
                        //获取出库单详细信息
                        DeliveryOrderDetailInfo deliveryOrderDetailInfo = deliveryOrderRpc.getDetailInfo(stockoutOrderInfo.getReferenceId());
                        if (null != deliveryOrderDetailInfo) {
                            //如果未推送第三方，则先推送第三方
                            if (!stockoutOrderInfo.getThirdPushed()) {
                                log.info("发货单{}详情已获取", stockoutOrderInfo.getReferenceId());
                                if (maBangOrderCreateParams.isPresent()) {
                                    //创建马帮订单
                                    MaBangDoCreateOrder req = maBangApiDataTranslateSve.buildMaBangDoCreateOrder(stockoutOrderId, deliveryOrderDetailInfo, maBangOrderCreateParams.get());
                                    String apiResult = maBangOrderApiSve.orderDoCreateOrder(thirdPartySystemId, req);

                                    if (apiResult.equals("SUCCESS")) {
                                        log.info("{}创建马帮订单成功", stockoutOrderId);
                                        //调用出库单ThirdPushed接口
                                        StockoutOrderThirdPushedCommand stockoutOrderThirdPushedCommand = new StockoutOrderThirdPushedCommand();
                                        stockoutOrderThirdPushedCommand.setStockoutOrderId(stockoutOrderId);
                                        stockoutOrderThirdPushedCommand.setThirdOrderId(req.getPlatformOrderId());

                                        try {
                                            stockoutOrderRpc.thirdPushed(stockoutOrderThirdPushedCommand);
                                            stockoutOrderInfo.setThirdOrderId(req.getPlatformOrderId());
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                            log.error("{}调用受理方法出错", stockoutOrderId);
                                            //受理失败则作废订单
                                            maBangOrderApiSve.invalidOrder(thirdPartySystemId, req.getSalesRecordNumber());
                                            return; //跳过执行后续步骤
                                        }
                                    } else {
                                        log.error("{}创建马帮订单失败{}", stockoutOrderId, apiResult);
                                        return; //跳过执行后续步骤
                                    }
                                } else {
                                    log.error("该租户缺失创建马帮订单的必要参数");
                                    return; //跳过执行后续步骤
                                }
                            }

                            log.info("判断出库单{}在马帮上的状态是否已作废", stockoutOrderId);
                            //判断订单状态是否已作废,已作废的调用取消
                            MaBangOrder maBangInvalidOrder = maBangOrderApiSve.orderGetOrderListByIdStatus(thirdPartySystemId, stockoutOrderInfo.getThirdOrderId(), "5");
                            if (maBangInvalidOrder != null) {
                                log.info("出库单{}状态已作废，调用取消方法", stockoutOrderId);
                                StockoutOrderConfirmCancelCommand stockoutOrderConfirmCancelCommand = new StockoutOrderConfirmCancelCommand();
                                stockoutOrderConfirmCancelCommand.setStockoutOrderId(stockoutOrderId);
                                stockoutOrderConfirmCancelCommand.setCancelType(CancelType.SUPPLIER_CANCEL.name());
                                stockoutOrderRpc.confirmCancel(stockoutOrderConfirmCancelCommand);
                            } else {
                                log.info("判断出库单{}在马帮上是否已有tn", stockoutOrderId);
                                //根据出库单号查询马帮上的跟踪单号
                                MaBangOrder maBangOrder = maBangOrderApiSve.orderGetOrderListByIdStatus(thirdPartySystemId, stockoutOrderInfo.getThirdOrderId(), "7");
                                if (maBangOrder != null) {
                                    if (StringUtils.isNotEmpty(maBangOrder.getMyLogisticsChannelName()) || StringUtils.isNotEmpty(maBangOrder.getTrackNumber())) {
                                        log.info("出库单{}已有tn，调用发货方法", stockoutOrderId);
                                        //调用出库单发货接口（国内tn）
                                        StockoutOrderUpdateTrackCommand stockoutOrderUpdateTrackCommand = new StockoutOrderUpdateTrackCommand();
                                        stockoutOrderUpdateTrackCommand.setStockoutOrderId(stockoutOrderId);
                                        stockoutOrderUpdateTrackCommand.setTrackInfos(Arrays.asList(new TrackInfoDTO(maBangOrder.getMyLogisticsChannelName(), maBangOrder.getTrackNumber())));
                                        stockoutOrderRpc.updateTrack(stockoutOrderUpdateTrackCommand);
                                    }
                                    //如果马帮订单状态已完成，标记出库单已出库
                                    if (maBangOrder.getOrderStatus().equalsIgnoreCase("3") || maBangOrder.getOrderStatus().equalsIgnoreCase("4")) {
                                        stockoutOrderRpc.deliver(stockoutOrderId);
                                    }
                                } else {
                                    log.info("出库单{}未有tn，跳过", stockoutOrderId);
                                }
                            }
                        } else {
                            log.warn("出库单{}获取不到关联发货单{}", stockoutOrderId, stockoutOrderInfo.getReferenceId());
                        }
                    });
                }
            }
        );
        log.info("定时任务检查马帮订单 - 结束 --- {}", new Date());
        return ReturnT.SUCCESS;
    }

    private ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }
}
