package com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.request;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class VVICCancelOrderReq {

    /**
     *订单号，与外部订单号不能同时为空
     */
    private String order_no;

    /**
     *	外部订单号，与订单号不能同时为空
     */
    private String out_order_no;

    /**
     * 取消原因
     */
    @NotNull(message = "取消原因不能为空")
    private String cancel_reason;

    /**
     *备注
     */
    @NotNull(message = "备注不能为空")
    private String remark;
}
