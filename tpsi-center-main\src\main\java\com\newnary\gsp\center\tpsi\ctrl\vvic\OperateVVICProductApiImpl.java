package com.newnary.gsp.center.tpsi.ctrl.vvic;

import com.alibaba.fastjson.JSON;
import com.newnary.api.base.common.CommonResponse;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.product.api.product.request.SupplierSpuCreateV2Command;
import com.newnary.gsp.center.product.api.product.request.SupplierSpuUpdateV2Command;
import com.newnary.gsp.center.tpsi.api.vvic.OperateVVICProductApi;
import com.newnary.gsp.center.tpsi.app.job.VVICJobManager;
import com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.request.VVICGetItemDetialReq;
import com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.response.VVICGetItemDetialResponse;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingInfo;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingType;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.ThirdPartyMappingManager;
import com.newnary.gsp.center.tpsi.infra.rpc.CategoryRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.OpenSupplierProductRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.SupplierSkuRpc;
import com.newnary.gsp.center.tpsi.service.vvic.VVICApiSev;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@Slf4j
public class OperateVVICProductApiImpl implements OperateVVICProductApi {

    @Resource
    private VVICApiSev vvicApiSevImpl;

    @Resource
    private OpenSupplierProductRpc openSupplierProductRpc;

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Resource
    private ThirdPartyMappingManager thirdPartyMappingManager;

    @Resource
    private CategoryRpc categoryRpc;

    @Resource
    private SupplierSkuRpc supplierSkuRpc;

    @Autowired
    private VVICJobManager vvicJobManager;

    @Value("${tpsi.bizIds}")
    private String bizIds;

    @Override
    public CommonResponse<String> updateProductInfo(@RequestBody List<String> customCodeList) {
        if (CollectionUtils.isNotEmpty(customCodeList)) {
            String vvic = JSON.parseObject(bizIds).getString("VVIC");
            ThirdPartySystem thirdPartySystem = loadSystem(vvic);
            List<List<String>> lists = vvicJobManager.groupListByQuantity(customCodeList, 20);
            lists.forEach(list->{
                StringBuilder stringBuilder = new StringBuilder();
                list.forEach(spuId -> stringBuilder.append(spuId).append(","));
                String spuIds = stringBuilder.toString();
                VVICGetItemDetialReq req = new VVICGetItemDetialReq();
                req.setItem_vid(spuIds);
                req.setLang("en");
                try {
                    VVICGetItemDetialResponse itemDetial = vvicApiSevImpl.getItemDetial(vvic, req);
                    if (ObjectUtils.isNotEmpty(itemDetial) && CollectionUtils.isNotEmpty(itemDetial.getItem_list())) {
                        List<String> categoryList = itemDetial.getItem_list().stream().map(item -> item.getCategory_name_one().concat("/").concat(item.getCategory_name_sub().concat("/").concat(item.getCategory_name_two()))).distinct().collect(Collectors.toList());
                        Map<String, ThirdPartyMappingInfo> categoryMapping = thirdPartyMappingManager.batchGetIdMappingInfoByTargetCategoryPath("GSP", "VVIC", categoryList, ThirdPartyMappingType.CATEGORY);
                        if (null == categoryMapping || categoryMapping.size() == 0) {
                            log.error("类目映射map为空，请求参数：{}  categoryMapping：{}", JSON.toJSONString(categoryList), categoryMapping);
                            return;
                        }
                        itemDetial.getItem_list().forEach(item -> {
                            try {
                                ThirdPartyMappingInfo newestInfoByTarget = thirdPartyMappingManager.getNewestInfoByTarget("VVIC", "GSP", item.getItem_vid(), ThirdPartyMappingType.PRODUCT_ID);
                                if (ObjectUtils.isNotEmpty(newestInfoByTarget)) {
                                    SupplierSpuCreateV2Command spuCreateV2Command = vvicJobManager.buildCreateSpuReq(thirdPartySystem, categoryMapping, item);
                                    SupplierSpuUpdateV2Command supplierSpuUpdateV2Command = buildSupplierSpuUpdateV2Command(newestInfoByTarget.getSourceId(), spuCreateV2Command);
                                    //TODO 更新商品信息

                                    //更新供货价
                                    vvicJobManager.updatePrice(thirdPartySystem,item);

                                    //更新库存
                                    vvicJobManager.updateStock(thirdPartySystem,item);
                                }
                            } catch (Exception e) {
                                log.info("搜款网商品sku更新失败：{}",JSON.toJSONString(item));
                            }
                        });
                    }
                } catch (Exception e) {
                    log.info("搜款网商品更新失败：{}",spuIds);
                }

            });

            return CommonResponse.success("商品更新成功");
        }
        return CommonResponse.success("customCodeList 不能为空");
    }


    private SupplierSpuUpdateV2Command buildSupplierSpuUpdateV2Command(String supplierSpuId, SupplierSpuCreateV2Command spuCreateV2Command) {
        SupplierSpuUpdateV2Command supplierSpuUpdateV2Command = new SupplierSpuUpdateV2Command();
        supplierSpuUpdateV2Command.setSupplierSpuId(supplierSpuId);
        supplierSpuUpdateV2Command.setSupplierId(spuCreateV2Command.getSupplierId());
        supplierSpuUpdateV2Command.setDescInfos(spuCreateV2Command.getDescInfos());
        supplierSpuUpdateV2Command.setDefaultLocale(spuCreateV2Command.getDefaultLocale());
        supplierSpuUpdateV2Command.setCustomCode(spuCreateV2Command.getCustomCode());
        supplierSpuUpdateV2Command.setCustomBrandId(spuCreateV2Command.getCustomBrandId());
        supplierSpuUpdateV2Command.setCategoryId(spuCreateV2Command.getCategoryId());
        supplierSpuUpdateV2Command.setCustomCategoryId(spuCreateV2Command.getCustomCategoryId());
        supplierSpuUpdateV2Command.setMgmtCategoryLevel(spuCreateV2Command.getMgmtCategoryLevel());
        supplierSpuUpdateV2Command.setMgmtCategoryId(spuCreateV2Command.getMgmtCategoryId());
        supplierSpuUpdateV2Command.setMainImages(spuCreateV2Command.getMainImages());
        supplierSpuUpdateV2Command.setDetailImages(spuCreateV2Command.getDetailImages());
        supplierSpuUpdateV2Command.setVideos(spuCreateV2Command.getVideos());
        supplierSpuUpdateV2Command.setCountryOfOriginCode(spuCreateV2Command.getCountryOfOriginCode());
        supplierSpuUpdateV2Command.setLogisticsAttrInfo(spuCreateV2Command.getLogisticsAttrInfo());
        supplierSpuUpdateV2Command.setSkuList(spuCreateV2Command.getSkuList());
        supplierSpuUpdateV2Command.setParamsInfo(spuCreateV2Command.getParamsInfo());
        supplierSpuUpdateV2Command.setIsAutoAuditPass(spuCreateV2Command.getIsAutoAuditPass());
        supplierSpuUpdateV2Command.setIsCheckAttribute(spuCreateV2Command.getIsCheckAttribute());
        supplierSpuUpdateV2Command.setOperator(spuCreateV2Command.getOperator());

        return supplierSpuUpdateV2Command;
    }


    public ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }


}
