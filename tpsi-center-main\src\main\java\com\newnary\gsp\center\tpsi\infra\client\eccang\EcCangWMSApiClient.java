package com.newnary.gsp.center.tpsi.infra.client.eccang;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.common.utils.httpmethod.ApiBaseResult;
import com.newnary.common.utils.httpmethod.HttpMethodUtil;
import com.newnary.common.utils.httpmethod.XmlHandleUtil;
import com.newnary.common.utils.xml.JaxbUtil;
import com.newnary.gsp.center.tpsi.infra.client.eccang.params.EcCangWMSParams;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangWmsBaseRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.URLDecoder;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class EcCangWMSApiClient {

    private String appToken;

    private String appKey;

    private String url;

    private String freightUrl;

    private static Integer RETRY_COUNT = 3;

    public EcCangWMSApiClient(String ecCangWMSParams) {
        EcCangWMSParams params = JSON.parseObject(ecCangWMSParams, EcCangWMSParams.class);
        this.appToken = params.getAppToken();
        this.appKey = params.getAppkey();
        this.url = params.getUrl();
        this.freightUrl = params.getFreightUrl();
    }

    public EcCangWMSApiClient(EcCangWMSParams params) {
        this.appToken = params.getAppToken();
        this.appKey = params.getAppkey();
        this.url = params.getUrl();
        this.freightUrl = params.getFreightUrl();
    }

    public EcCangApiBaseResult<String> sendWMSRequest(Object param, String service) {
        EcCangWmsBaseRequest baseRequest = getWMSBaseRequest(param, service);

        String requestXml = JaxbUtil.toXml(baseRequest);
        ApiBaseResult apiBaseResult = null;
        try {
            apiBaseResult = HttpMethodUtil.syncPostMethodBySetBodyPara(url,
                    3,
                    null,
                    "application/xml",
                    null, null, requestXml);

            //返回的是xml,需要进行处理得到里面的response
            JSONObject o = XmlHandleUtil.xmlHandle(apiBaseResult.getRet());
            String responseStr = o.getJSONObject("Body").getJSONObject("callServiceResponse").getJSONObject("response").toString();
            responseStr = decodeUnicode(responseStr);
            responseStr = URLDecoder.decode(responseStr, "UTF-8");
            log.info("[ecangERP] 请求结束, requestXml={}, service={}, response={}", requestXml, service, responseStr);

            EcCangApiBaseResult<String> ecCangApiBaseResult = buildWMSBaseResult(responseStr);
            return ecCangApiBaseResult;
        } catch (Exception e) {
            e.printStackTrace();
            EcCangApiBaseResult<String> ret = new EcCangApiBaseResult<String>();
            ret.setCode("000");
            ret.setMessage("异常".concat(e.getMessage()));
            return null;
        }
    }

    private EcCangWmsBaseRequest getWMSBaseRequest(Object param, String service) {
        EcCangWmsBaseRequest request = new EcCangWmsBaseRequest();
        request.body = new EcCangWmsBaseRequest.RequestBody();
        request.body.callService = new EcCangWmsBaseRequest.CallService();
        request.body.callService.appToken = appToken;
        request.body.callService.appKey = appKey;
        request.body.callService.service = service;
        if (null != param) {
            request.body.callService.paramJson = JSON.toJSONString(param);
        }
        return request;
    }

    public EcCangApiBaseResult<String> sendFreightRequest(Object param, String service, String type) {
        Map<String, String> header = getWMSFreightRequest(param, service);

        String dataParas = JSON.toJSONString(param);
        ApiBaseResult apiBaseResult = null;
        try {
            apiBaseResult = HttpMethodUtil.syncPostMethodBySetBodyPara(freightUrl,
                    3,
                    service,
                    "application/json",
                    header, null, dataParas);

            EcCangApiBaseResult<String> ecCangApiBaseResult = buildFreightBaseResult(apiBaseResult.getRet());

            log.info("[ecangERP] 请求结束, service={}, data={}, message={}, dataParas={}", service, ecCangApiBaseResult.getData(), ecCangApiBaseResult.getMessage(), dataParas);
            return ecCangApiBaseResult;
        } catch (Exception e) {
            e.printStackTrace();
            EcCangApiBaseResult<String> ret = new EcCangApiBaseResult<String>();
            ret.setCode("000");
            ret.setMessage("异常".concat(e.getMessage()));
            return null;
        }
    }

    private Map<String, String> getWMSFreightRequest(Object param, String service) {
        Map<String, String> header = new HashMap<>(2);
        header.put("appToken", appToken);
        header.put("appKey", appKey);

        return header;
    }

    EcCangApiBaseResult<String> buildFreightBaseResult(String resultStr) {
        EcCangApiBaseResult<String> apiBaseResult = new EcCangApiBaseResult<>();
        JSONObject resultObj = JSONObject.parseObject(resultStr);

        if (StringUtils.isBlank(resultStr)) {
            apiBaseResult.setCode("Failure");
            apiBaseResult.setMessage("ecang WMS 接口调用失败");
            return apiBaseResult;
        }

        apiBaseResult.setCode(resultObj.getString("code"));
        apiBaseResult.setMessage(resultObj.getString("message"));
        if (resultObj.containsKey("data")) {
            apiBaseResult.setData(resultObj.getString("data"));
        } else {
            apiBaseResult.setData(resultStr);
        }
        apiBaseResult.setError(resultObj.getString("error"));
        return apiBaseResult;
    }

    EcCangApiBaseResult<String> buildWMSBaseResult(String resultStr) {
        EcCangApiBaseResult<String> apiBaseResult = new EcCangApiBaseResult<>();
        JSONObject resultObj = JSONObject.parseObject(resultStr);

        if (StringUtils.isBlank(resultStr)) {
            apiBaseResult.setCode("Failure");
            apiBaseResult.setMessage("ecang WMS 接口调用失败");
            return apiBaseResult;
        }

        apiBaseResult.setMessage(resultObj.getString("message"));
        if (resultObj.containsKey("data")) {
            apiBaseResult.setData(resultObj.getString("data"));
        } else {
            apiBaseResult.setData(resultStr);
        }

        if (null != resultObj.get("pagination") && !resultObj.isEmpty()) {
            JSONObject pageJson = resultObj.getJSONObject("pagination");
            apiBaseResult.setPageSize(Integer.valueOf(pageJson.get("pageSize") == null ? "0" : pageJson.getString("pageSize")));
            apiBaseResult.setPage(Integer.valueOf(pageJson.get("page") == null ? "0" : pageJson.getString("page")));
        }
        apiBaseResult.setTotalCount(Integer.valueOf(resultObj.get("count") == null ? "0" : resultObj.getString("count")));

        if (null != resultObj.get("error") && !resultObj.isEmpty()) {
            JSONObject errorJson = resultObj.getJSONObject("error");
            apiBaseResult.setCode(errorJson.getString("errCode"));
            apiBaseResult.setError(errorJson.getString("errMessage"));
        } else {
            apiBaseResult.setCode(resultObj.getString("ask"));
        }
        return apiBaseResult;
    }

    private String decodeUnicode(String str) {
        Charset set = Charset.forName("UTF-16");
        Pattern p = Pattern.compile("\\\\u([0-9a-fA-F]{4})");
        Matcher m = p.matcher(str);
        int start = 0;
        int start2 = 0;
        StringBuffer sb = new StringBuffer();
        while (m.find(start)) {
            start2 = m.start();
            if (start2 > start) {
                String seg = str.substring(start, start2);
                sb.append(seg);
            }
            String code = m.group(1);
            int i = Integer.valueOf(code, 16);
            byte[] bb = new byte[4];
            bb[0] = (byte) ((i >> 8) & 0xFF);
            bb[1] = (byte) (i & 0xFF);
            ByteBuffer b = ByteBuffer.wrap(bb);
            sb.append(String.valueOf(set.decode(b)).trim());
            start = m.end();
        }
        start2 = str.length();
        if (start2 > start) {
            String seg = str.substring(start, start2);
            sb.append(seg);
        }
        return sb.toString();
    }

}
