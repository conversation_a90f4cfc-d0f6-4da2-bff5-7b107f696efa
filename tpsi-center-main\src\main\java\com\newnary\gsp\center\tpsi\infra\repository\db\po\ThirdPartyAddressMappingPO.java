package com.newnary.gsp.center.tpsi.infra.repository.db.po;

import com.newnary.dao.base.po.TenantBasePO;
import com.newnary.gsp.center.tpsi.infra.model.vo.SystemId;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2021/12/14 14:20
 */
@Getter
@Setter
public class ThirdPartyAddressMappingPO extends TenantBasePO {
    private static final long serialVersionUID = 2111923491117227707L;

    /**
     * 系统Id
     */
    private String systemId;

    /**
     * 内部邮编
     */
    private String virtualPostcode;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 国家
     */
    private String country;

    /**
     * 省州
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * 镇/街
     */
    private String town;

    private String address;
}
