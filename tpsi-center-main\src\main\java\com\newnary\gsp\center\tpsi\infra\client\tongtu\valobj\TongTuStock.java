package com.newnary.gsp.center.tpsi.infra.client.tongtu.valobj;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/12/23
 */
@Data
public class TongTuStock {

    private String goodsIdKey;

    private String goodsSku;

    private String warehouseIdKey;

    private String warehouseName;

    private Integer availableStockQuantity;

    private Integer intransitStockQuantity;

    private Integer safetyStock;

    private Integer waitingShipmentStockQuantity;

    private Integer defectsStockQuantity;

    private BigDecimal firstShippingFeeUnit;

    private BigDecimal firstTariff;

    private BigDecimal otherFee;

    private BigDecimal goodsAvgCost;

    private BigDecimal goodsCurCost;

    private String cargoSpace;

    private List<TongTuGoodsShelfStock> goodsShelfStockList;
}
