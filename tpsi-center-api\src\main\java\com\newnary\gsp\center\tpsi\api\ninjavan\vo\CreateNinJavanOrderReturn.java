package com.newnary.gsp.center.tpsi.api.ninjavan.vo;

import lombok.Data;

import javax.validation.constraints.Size;

/**
 * 创建能者物流 return
 *
 * <AUTHOR>
 * @since Created on 2023-08-11
 **/
@Data
public class CreateNinJavanOrderReturn {

    /**
     *
     **/
    @Size(min = 3, max = 255)
    private String name;

    /**
     * 请在此栏中填写楼栋号码、楼栋名称及街道名称。 例如 30 Jalan Kilang Barat.
     **/
    @Size(max = 255)
    private String addressLine1 ;


    /**
     * 若国家代码为：MY, PH, ID或VN, 则必须填写城市。
     **/
    @Size(max = 255)
    private String city;


    /**
     * 若国家代码为：MY, PH, TH或ID, 则必须填写省份：
     **/
    @Size(max = 255)
    private String stateProvince;


    /**
     * 若国家代码为：SG, MY, TH, PH 或 ID，则必须填写该栏。PH: 4 digits
     * 菲律宾：四位
     **/
    private String postCode;


    /**
     * 国家代码:两个字符
     **/
    @Size(max = 2)
    private String countryCode;

    /**
     * 联系号码
     **/
    @Size(min = 6, max = 32)
    private String contactNumber;


    /**
     * 联系邮箱
     **/
    @Size(max = 255)
    private String contactEmail;

}
