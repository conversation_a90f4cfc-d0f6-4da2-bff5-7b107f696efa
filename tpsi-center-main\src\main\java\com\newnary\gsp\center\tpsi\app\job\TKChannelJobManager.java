package com.newnary.gsp.center.tpsi.app.job;


import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.common.PageCondition;
import com.newnary.api.base.common.PageList;
import com.newnary.distributed.tools.concurrent.template.DConcurrentTemplate;
import com.newnary.gsp.center.logistics.api.delivery.request.DeliveryOrderPageQueryCommand;
import com.newnary.gsp.center.logistics.api.delivery.request.TransportOrderWithDeliveryQueryCommand;
import com.newnary.gsp.center.logistics.api.delivery.response.*;
import com.newnary.gsp.center.product.api.product.request.ChannelSaleItemPageQueryDetailCommand;
import com.newnary.gsp.center.tpsi.ctrl.tk.TKApiImpl;
import com.newnary.gsp.center.tpsi.infra.client.tk.dto.TKFetchOrderReq;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.infra.rpc.DeliveryOrderRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.TransportOrderRpc;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.job.core.handler.annotation.Job;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class TKChannelJobManager {

    private static final String SYSTEM_PROVIDER = "TIKTOK";
    @Resource
    private TKApiImpl tkApi;

    @Resource
    private DeliveryOrderRpc deliveryOrderRpc;

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Resource
    private TransportOrderRpc transportOrderRpc;

    @Job("autoFetchOrder2Erp")
    public ReturnT<String> autoFetchOrder2Erp(String param) {
        DConcurrentTemplate.tryLockMode(
                "TK:FETCH_ORDER",
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    log.info("tiktok-同步订单到ERP 定时任务开始, param={}", param);
                    List<ThirdPartySystem> systemsByProvider = thirdPartySystemRepository.getSystemsByProvider(SYSTEM_PROVIDER);
                    if (CollectionUtils.isEmpty(systemsByProvider)) {
                        return;
                    }
                    TKFetchOrderReq req = new TKFetchOrderReq() ;
                    // req.setUpdate_time_from((int)(System.currentTimeMillis()/ 1000 - 96 * 60 * 60));
                    if (StringUtils.isNotBlank(param)) {
                        req = JSONObject.parseObject(param, TKFetchOrderReq.class);
                    }

                    for (ThirdPartySystem system : systemsByProvider) {
                        tkApi.doFetchOrder(req, system.getBizId());
                    }

                }
        );
        return ReturnT.SUCCESS;
    }

    @Job("autoFetchSaleAfterOrder")
    public ReturnT<String> autoFetchSaleAfterOrder(String param) {
        DConcurrentTemplate.tryLockMode(
                "TK:FETCH_ORDER",
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    log.info("tiktok-同步售后订单到ERP 定时任务开始, param={}", param);
                    List<ThirdPartySystem> systemsByProvider = thirdPartySystemRepository.getSystemsByProvider(SYSTEM_PROVIDER);
                    if (CollectionUtils.isEmpty(systemsByProvider)) {
                        return;
                    }

                    TKFetchOrderReq req = new TKFetchOrderReq();
                    if (StringUtils.isNotBlank(param)) {
                        req = JSONObject.parseObject(param, TKFetchOrderReq.class);
                    }
                    req.setOrder_status(140);
                    for (ThirdPartySystem system : systemsByProvider) {
                        tkApi.doFetchSaleAfterOrder(req, system.getBizId());
                    }

                }
        );
        return ReturnT.SUCCESS;
    }


    @Job("autoSyncProducts2TK")
    public ReturnT<String> autoSyncProducts2TK(String param) {
        try {
            DConcurrentTemplate.tryLockMode(
                    "TK:PUSH_PRODUCT",
                    lock -> lock.tryLock(3, TimeUnit.SECONDS),
                    () -> {
                        log.info("tiktok-推送商品到店铺 定时任务开始, param={}", param);
                        ChannelSaleItemPageQueryDetailCommand command = new ChannelSaleItemPageQueryDetailCommand();
                        if (StringUtils.isNotBlank(param)) {
                            command = JSONObject.parseObject(param, ChannelSaleItemPageQueryDetailCommand.class);
                        }

                        if (null == command.getPageCondition()) {
                            command.setPageCondition(new PageCondition(1, 50));
                        }

                        List<ThirdPartySystem> systemsByProvider = thirdPartySystemRepository.getSystemsByProvider(SYSTEM_PROVIDER);
                        if (CollectionUtils.isEmpty(systemsByProvider)) {
                            return;
                        }
                        for (ThirdPartySystem system : systemsByProvider) {
                            command.setChannelId(system.getBizId());
                            tkApi.syncProducts(command, system.getBizId());
                        }

                    }
            );
        } catch (Exception e) {
            log.error("tiktok-推送商品到店铺 定时任务异常, param={}", param, e);
        }
        return ReturnT.SUCCESS;
    }


    /**
     * 待废弃
     *
     * @param param
     * @return
     */
    @Deprecated
    @Job("autoNotifyOrderSplit")
    public ReturnT<String> autoNotifyOrderSplit(String param) {
        try {
            DConcurrentTemplate.tryLockMode(
                    "TK:NOTIFY_ORDER_SPLIT",
                    lock -> lock.tryLock(3, TimeUnit.SECONDS),
                    () -> {
                        log.info("tiktok-定时查询GSP已创建的发货单 通知tiktok渠道订单已拆分, param={}", param);

                        List<ThirdPartySystem> systems = thirdPartySystemRepository.getSystemsByProvider(SYSTEM_PROVIDER);

                        if (CollectionUtils.isEmpty(systems)) {
                            return;
                        }
                        DeliveryOrderPageQueryCommand command = new DeliveryOrderPageQueryCommand();

                        if (StringUtils.isNotBlank(param)) {
                            command = JSONObject.parseObject(param, DeliveryOrderPageQueryCommand.class);
                        }
                        if (null == command.getPageCondition()) {
                            command.setPageCondition(new PageCondition(1, 50));
                        }
                        for (ThirdPartySystem system : systems) {
                            command.setChannleId(system.getBizId());
                            command.setOrderState("WAIT_DELIVERY");
                            PageList<DeliveryOrderLiteInfo> deliveryOrderLiteInfoPageList = deliveryOrderRpc.pageQuery(command);

                            List<DeliveryOrderLiteInfo> items = deliveryOrderLiteInfoPageList.getItems();
                            while (CollectionUtils.isNotEmpty(items)) {
                                items.forEach(order -> {
                                    List<DeliveryOrderDetailInfo> details = deliveryOrderRpc.getDetailsByOrderId(order.getTradeOrderId());
                                    tkApi.notifyOrderSplit(details, system.getBizId());
                                });
                                int pageNum = command.getPageCondition().getPageNum();
                                pageNum = pageNum + 1;
                                command.getPageCondition().setPageNum(pageNum);
                                items = deliveryOrderRpc.pageQuery(command).getItems();
                            }
                        }

                    }
            );
        } catch (Exception e) {
            log.error("tiktok-定时查询GSP已创建的发货单 定时任务异常, param={}", param, e);
        }

        return ReturnT.SUCCESS;
    }


    @Job("autoFetchOrderLabel")
    public ReturnT<String> autoFetchOrderLabel(String param) {
        try {
            DConcurrentTemplate.tryLockMode(
                    "TK:FETCH_LABEL",
                    lock -> lock.tryLock(3, TimeUnit.SECONDS),
                    () -> {
                        log.info("tiktok-定时获取面单 定时任务开始, param={}", param);

                        List<ThirdPartySystem> systems = thirdPartySystemRepository.getSystemsByProvider(SYSTEM_PROVIDER);
                        if (CollectionUtils.isEmpty(systems)) {
                            return;
                        }
                        TransportOrderWithDeliveryQueryCommand command = new TransportOrderWithDeliveryQueryCommand();
                        if (StringUtils.isNotBlank(param)) {
                            command = JSONObject.parseObject(param, TransportOrderWithDeliveryQueryCommand.class);
                        }

                        TKFetchOrderReq req = new TKFetchOrderReq();
                        req.setOrder_status(112);


                        for (ThirdPartySystem system : systems) {
                            command.setDeliveryOrderChannelId(system.getBizId());
                            command.setTransportOrderState("CREATED");
                            List<TransportOrderLiteInfo> transportOrderInfos = transportOrderRpc.getListWithDelivery(command);
                            if (CollectionUtils.isEmpty(transportOrderInfos)) {
                                continue;
                            }

                            // fixme:linjiamei-tiktok 确认查询逻辑对不对，【减少查询次数】
                            transportOrderInfos.forEach(liteInfo -> {
                                try {
                                    if (CollectionUtils.isEmpty(liteInfo.getReferenceIds())) {
                                        log.error("运输单{}下找不到关联单号", liteInfo.getTransportOrderId());
                                        return;
                                    }
                                    String deliveryOrderId = liteInfo.getReferenceIds().get(0);
                                    DeliveryOrderDetailInfo detailInfo = deliveryOrderRpc.getDetailInfo(deliveryOrderId);
                                    String tradeOrderId = detailInfo.getTradeOrderId();
                                    TransportOrderDetailInfo transportOrderDetailInfo = transportOrderRpc.getDetail(liteInfo.getTransportOrderId());
                                    if (CollectionUtils.isEmpty(transportOrderDetailInfo.getOrderPackages()) || transportOrderDetailInfo.getOrderPackages().size() > 1) {
                                        //TODO 基于运输单只有一个包裹逻辑
                                        log.error("找不到运输单下的包裹 或 运输单多于一个包裹");
                                        return;
                                    }
                                    TransportOrderPackageInfo transportPackageInfo = transportOrderRpc.loadTransportPackage(transportOrderDetailInfo.getOrderPackages().get(0).getTransportOrderPackageId());
                                    tkApi.fetchTrackInfo2GSP(tradeOrderId, transportPackageInfo, deliveryOrderId, system.getBizId(), detailInfo.getItems());
                                } catch (Exception e) {
                                    log.error("tiktok-回传面单异常", e);
                                }

                            });
                        }
                    }
            );
        } catch (Exception e) {
            log.info("tiktok-定时获取面单 定时任务异常, param={}", param, e);
        }
        return ReturnT.SUCCESS;
    }


    /**
     * 待废弃
     *
     * @param param
     * @return
     */
    @Deprecated
    @Job("autoNotifyOrderShipped")
    public ReturnT<String> autoNotifyOrderShipped(String param) {
        try {
            DConcurrentTemplate.tryLockMode(
                    "TK:FETCH_LABEL",
                    lock -> lock.tryLock(3, TimeUnit.SECONDS),
                    () -> {
                        log.info("tiktok-查询GSP 已发货订单 通知订单已发货, param={}", param);
                        List<ThirdPartySystem> systems = thirdPartySystemRepository.getSystemsByProvider(SYSTEM_PROVIDER);
                        if (CollectionUtils.isEmpty(systems)) {
                            return;
                        }
                        TransportOrderWithDeliveryQueryCommand command = new TransportOrderWithDeliveryQueryCommand();
                        if (StringUtils.isNotBlank(param)) {
                            command = JSONObject.parseObject(param, TransportOrderWithDeliveryQueryCommand.class);
                        }
                        for (ThirdPartySystem system : systems) {
                            command.setDeliveryOrderChannelId(system.getBizId());
                            command.setTransportOrderState("CREATED");
                            List<TransportOrderLiteInfo> transportOrderInfos = transportOrderRpc.getListWithDelivery(command);
                            if (CollectionUtils.isEmpty(transportOrderInfos)) {
                                continue;
                            }

                            // fixme:linjiamei-tiktok 运输单里面少个orderId和商户订单号，不知道能不能加个transOrderId，减少查询次数
                            transportOrderInfos.forEach(liteInfo -> {
                                try {
                                    if (CollectionUtils.isEmpty(liteInfo.getReferenceIds())) {
                                        log.error("运输单{}下找不到关联单号", liteInfo.getTransportOrderId());
                                        return;
                                    }
                                    DeliveryOrderDetailInfo detailInfo = deliveryOrderRpc.getDetailInfo(liteInfo.getReferenceIds().get(0));
                                    TransportOrderDetailInfo transportOrderDetailInfo = transportOrderRpc.getDetail(liteInfo.getTransportOrderId());
                                    if (CollectionUtils.isEmpty(transportOrderDetailInfo.getOrderPackages()) || transportOrderDetailInfo.getOrderPackages().size() > 1) {
                                        //TODO 基于运输单只有一个包裹逻辑
                                        log.error("找不到运输单下的包裹 或 运输单多于一个包裹");
                                        return;
                                    }
                                    TransportOrderPackageInfo transportPackageInfo = transportOrderRpc.loadTransportPackage(transportOrderDetailInfo.getOrderPackages().get(0).getTransportOrderPackageId());
                                    tkApi.notifyOrderShippedAndReturnGSPLabel(detailInfo.getTradeOrderId(), detailInfo.getDeliveryOrderId(), transportPackageInfo, system.getBizId(), detailInfo.getItems());
                                } catch (Exception e) {
                                    log.error("通知渠道发货异常, transportOrderId={}", liteInfo.getTransportOrderId(), e);
                                }

                            });
                        }

                    }
            );
        } catch (Exception e) {
            log.info("tiktok-查询GSP 已发货订单 通知订单已发货,异常, param={}", param, e);
        }
        return ReturnT.SUCCESS;
    }


    @Job("autoNotifyGspOrderFinished")
    public ReturnT<String> autoNotifyGspOrderFinished(String param) {
        try {
            DConcurrentTemplate.tryLockMode(
                    "TK:FETCH_SHIPPING_INFO",
                    lock -> lock.tryLock(3, TimeUnit.SECONDS),
                    () -> {
                        log.info("tiktok-查询包裹物流轨迹信息, param={}", param);
                        List<ThirdPartySystem> systems = thirdPartySystemRepository.getSystemsByProvider(SYSTEM_PROVIDER);
                        if (CollectionUtils.isEmpty(systems)) {
                            return;
                        }
                        TransportOrderWithDeliveryQueryCommand command = new TransportOrderWithDeliveryQueryCommand();
                        if (StringUtils.isNotBlank(param)) {
                            command = JSONObject.parseObject(param, TransportOrderWithDeliveryQueryCommand.class);
                        }
                        for (ThirdPartySystem system : systems) {
                            command.setDeliveryOrderChannelId(system.getBizId());
                            command.setTransportOrderState("DISPATCH");
                            List<TransportOrderLiteInfo> transportOrderInfos = transportOrderRpc.getListWithDelivery(command);
                            if (CollectionUtils.isEmpty(transportOrderInfos)) {
                                continue;
                            }

                            // fixme:linjiamei-tiktok 运输单里面少个orderId和商户订单号，不知道能不能加个transOrderId，减少查询次数
                            transportOrderInfos.forEach(liteInfo -> {
                                try {
                                    DeliveryOrderDetailInfo detailInfo = deliveryOrderRpc.getDetailInfo(liteInfo.getReferenceIds().get(0));
                                    TransportOrderDetailInfo transportOrderDetailInfo = transportOrderRpc.getDetail(liteInfo.getTransportOrderId());
                                    if (CollectionUtils.isEmpty(transportOrderDetailInfo.getOrderPackages()) || transportOrderDetailInfo.getOrderPackages().size() > 1) {
                                        //TODO 基于运输单只有一个包裹逻辑
                                        log.error("找不到运输单下的包裹 或 运输单多于一个包裹");
                                        return;
                                    }
                                    TransportOrderPackageInfo transportPackageInfo = transportOrderRpc.loadTransportPackage(transportOrderDetailInfo.getOrderPackages().get(0).getTransportOrderPackageId());
                                    tkApi.finishGspOrder(detailInfo.getTradeOrderId(), detailInfo.getDeliveryOrderId(), transportPackageInfo, system.getBizId(), detailInfo.getItems(), system.getParams(), system.getBizId());
                                } catch (Exception e) {
                                    log.error("查询包裹物流轨迹信息, transportOrderId={}", liteInfo.getTransportOrderId(), e);
                                }

                            });
                        }

                    }
            );
        } catch (Exception e) {
            log.info("tiktok-查询包裹物流轨迹信息,异常, param={}", param, e);
        }
        return ReturnT.SUCCESS;
    }

    @Job("autoRefreshToken")
    public ReturnT<String> autoRefreshToken(String param) {
        List<ThirdPartySystem> systemsByProvider = thirdPartySystemRepository.getSystemsByProvider(SYSTEM_PROVIDER);
        if (CollectionUtils.isEmpty(systemsByProvider)) {
            return ReturnT.FAIL;
        }
        for (ThirdPartySystem system : systemsByProvider) {
            try {
                tkApi.refreshToken(system.getBizId());
            } catch (IOException e) {
                e.printStackTrace();
                return new ReturnT<>(500, "token刷新失败");
            }
        }
        return ReturnT.SUCCESS;
    }
}
