package com.newnary.gsp.center.tpsi.eccang;

import com.alibaba.fastjson.JSON;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp.EcCangERPGetSupplierListRequest;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp.EcCangERPGetSupplierProductRequest;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.erp.EcCangERPGetSupplierProductResponse;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.creator.ThirdPartySystemCreator;
import com.newnary.gsp.center.tpsi.service.eccang.IEccangERPApiSve;
import com.newnary.test.starter.BaseTestInjectTenant;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since Created on 2025-06-04
 **/
public class ECClientTest extends BaseTestInjectTenant {

    @Resource
    private IEccangERPApiSve eccangERPApiSveImpl;

    @Override
    protected String tenantId() {
        return "TENANT3924117688555169382400";
    }

    @Test
    public void testNameTest() {
        EcCangERPGetSupplierListRequest getSupplierListRequest = new EcCangERPGetSupplierListRequest();
        EcCangERPGetSupplierListRequest.Condition condition = new EcCangERPGetSupplierListRequest.Condition();
//        condition.setSupplierCode("RA14647");
        condition.setSupplierName("六安市金安区旭阳电子商务商行（个体工商户）");
        getSupplierListRequest.setCondition(condition);
        EcCangApiBaseResult<String> getSupplierListResp = eccangERPApiSveImpl.getSupplierList(buildSystemParams(), getSupplierListRequest);

        System.out.println(">>>>>>>>>>>>>> " + getSupplierListResp);
    }

    @Test
    public void testNameTest2() {
//        String[] skus = "RA07603A RA09527A RA08712A RA04129A H842503 RA08655A ST000088 RA10443A RA14724A RA09502A RA03513A RA03678A RA03921A RA09542A RA11175A RA03890A RA05223B RA03563A RA09506A RA16410A RA15132A RA03508A RA08724A RA15136A RA10446A VM000789 RA03895A RA07005A RA10406A RA12809A RA14816A RA07976A RA11005A RA02699A AUT-H-FTC RA10796A RA02849A RA07062A RA08727B RA03507A RA04598A RA07054A RA08658A RA08868A RA05053A ST000107 RA09504A RA17160A DB04882A RA03713A RA07612A RA16387A RA11189A RA13108B RA10440A RA10484A VM002069 ST000058 RA17133A RA17153A RA11192A RA09503A RA03506A".split(" ");
        String[] skus = "RA07603A RA09527A".split(" ");
        Set<String> skuSet = Stream.of(skus).collect(Collectors.toSet());

        EcCangERPGetSupplierProductRequest getSupplierProductRequest = new EcCangERPGetSupplierProductRequest();
        getSupplierProductRequest.setSupplierId(9435);
        getSupplierProductRequest.setSku(String.join(" ", skuSet));
        EcCangApiBaseResult<String> getSupplierProductResp = eccangERPApiSveImpl.getSupplierProductList(buildSystemParams(), getSupplierProductRequest);

        List<EcCangERPGetSupplierProductResponse> getSupplierProductListResponse = JSON.parseArray(getSupplierProductResp.getData(), EcCangERPGetSupplierProductResponse.class);
        Map<String, EcCangERPGetSupplierProductResponse> ecSkuMap = getSupplierProductListResponse.stream().collect(Collectors.toMap(
                EcCangERPGetSupplierProductResponse::getProduct_sku,
                Function.identity()
        ));

        for (String sku : skuSet) {
            EcCangERPGetSupplierProductResponse ecSku = ecSkuMap.get(sku);
            System.out.println(sku + " -- " + (ecSku == null ? null : ecSku.getProduct_sku()));
        }

        System.out.println(String.join(" ", ecSkuMap.keySet()));
    }

    private ThirdPartySystem buildSystemParams() {
        ThirdPartySystemCreator creator = new ThirdPartySystemCreator();
        creator.setSystemId("S5701146366596120576085");
        creator.setName("融合代采易仓ERP系统对接");
        creator.setBizId("RMECCANGERP0003");
        creator.setProvider("ECCANG");
        creator.setBizType("SUPPLIER_SYSTEM");
        creator.setStatus("ENABLED");
        creator.setParams("{\"userName\":\"apitest\",\"userPass\":\"api123++\",\"ebUrl\":\"http://vm-eb.eccang.com/default/svc-open/web-service-v2\",\"wmsUrl\":\"http://vm.eccang.com/default/svc-open/web-service-v2\",\"supplierId\":\"VD0656215283016703021056\",\"channelId\":\"C1910215288272463728640\",\"supplierIds\":\"VD0656215283016703021056,VD0355324360496037892096,VD1193322510132334432256\"}");
        return ThirdPartySystem.createWith(creator);
    }

}
