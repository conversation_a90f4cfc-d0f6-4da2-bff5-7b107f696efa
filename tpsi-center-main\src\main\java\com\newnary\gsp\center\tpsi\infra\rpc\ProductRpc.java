package com.newnary.gsp.center.tpsi.infra.rpc;

import com.newnary.gsp.center.product.api.product.SupplierSkuApi;
import com.newnary.gsp.center.product.api.product.request.SupplierSkuSearchDetailsCommand;
import com.newnary.gsp.center.product.api.product.response.SupplierSkuDetailInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class ProductRpc {

    @Resource
    private SupplierSkuApi supplierSkuApi;

    public SupplierSkuDetailInfo getSupplierSku(String supplierSkuId) {
        SupplierSkuSearchDetailsCommand command = new SupplierSkuSearchDetailsCommand();
        command.setSupplierSkuId(supplierSkuId);
        List<SupplierSkuDetailInfo> supplierSkuDetailInfos = supplierSkuApi.searchDetails(command).mustSuccessOrThrowOriginal();
        return supplierSkuDetailInfos.stream().findAny().get();
    }
}
