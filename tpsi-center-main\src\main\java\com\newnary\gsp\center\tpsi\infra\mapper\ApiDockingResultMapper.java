package com.newnary.gsp.center.tpsi.infra.mapper;

import com.newnary.gsp.center.tpsi.infra.model.creator.ApiDockingResultCreator;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.ApiDockingResultPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ApiDockingResultMapper {

    ApiDockingResultMapper INSTANCE = Mappers.getMapper(ApiDockingResultMapper.class);

    ApiDockingResultCreator po2ModelCreator(ApiDockingResultPO po);
}
