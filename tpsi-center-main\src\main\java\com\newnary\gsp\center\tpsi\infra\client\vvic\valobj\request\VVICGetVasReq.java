package com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class VVICGetVasReq {

    /**
     * 市场编码
     */
    @NotNull(message = "市场编码不能为空")
    private String market_code;
    /**
     * 商品列表
     */
    @NotNull(message = "商品列表不能为空")
    private List<Item> items;

    @Getter
    @Setter
    public static class Item {
        /**
         * 商品vid
         */
        @NotNull(message = "商品vid不能为空")
        private String item_vid;

        /**
         * 商品数量
         */
        @NotNull(message = "商品数量不能为空")
        private Integer quantity;
    }

}
