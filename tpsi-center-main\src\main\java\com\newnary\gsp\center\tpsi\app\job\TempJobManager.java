package com.newnary.gsp.center.tpsi.app.job;

import com.newnary.gsp.center.purchase.api.supplier.response.SupplierGoodsInfo;
import com.newnary.gsp.center.tpsi.app.service.eccang.EccangGoodsMgmtApp;
import com.newnary.gsp.center.tpsi.infra.rpc.PurchaseRpc;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.job.core.handler.annotation.Job;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since Created on 2025-09-02
 **/
@Slf4j
@Component
public class TempJobManager {


    @Resource
    private PurchaseRpc purchaseRpc;
    @Resource
    private EccangGoodsMgmtApp eccangGoodsMgmtApp;

    @Job("tempSupplierGoodsSyncJob")
    public ReturnT<String> tempSupplierGoodsSyncJob(String param) throws InterruptedException {
        Long startId = null;
        if (StringUtils.isNotBlank(param)) {
            startId = Long.valueOf(param);
        }
        Long[] goodsIds = new Long[]{1585093233672260L, 1585099909955675L, 1585124894375969L, 1585257193209862L, 1585800146911242L, 1585821971972161L, 1585965065896034L, 1586034881134626L, 1586034881134627L, 1586034881134628L, 1586034881134629L, 1586038562684997L, 1586038563733511L, 1586038563733512L, 1586038563733513L, 1586777797230690L, 1586993119166517L, 1586993121263688L, 1586993122312244L, 1586993124409406L, 1586993125457975L, 1586993127555089L, 1586993129652282L, 1586993130700810L, 1586993132798033L, 1586993133846592L, 1586993135943684L, 1586993136992287L, 1586993148526646L, 1586993150623767L, 1586993152720974L, 1586993154818096L, 1590296655757402L, 1590296657854510L, 1590296661000290L, 1590296662048864L, 1590368558710843L, 1590502678921293L, 1590512054239306L, 1590512260808802L, 1590512364617768L, 1590532978573358L, 1590551345430598L, 1590564114989147L, 1591288685199387L, 1591421451698265L, 1591433817554965L, 1591459612524566L, 1592209988190226L, 1592218974486583L, 1592308886732830L, 1592369311973405L, 1592399029665824L, 1593041405149255L, 1593278740889644L, 1593292013764688L, 1593322996039737L, 1593322997088285L, 1593322999185434L, 1593323003379760L, 1593323023302729L, 1593991087849485L, 1594119428309074L, 1594122866589792L, 1594149838061627L, 1594162511151121L, 1595779325165629L, 1596020578385982L, 1596022380888118L, 1596659022757965L, 1596845354713093L, 1596845500465235L, 1597838091943999L, 1598483149684803L, 1598489978011675L, 1598648907530292L, 1598684997419041L, 1599433857892427L, 1599433877815349L, 1599433878863891L, 1599433880961056L, 1599433882009619L, 1599433902981167L, 1599433905078368L, 1599433915564096L, 1599433917661243L, 1599433918709827L, 1599433919758368L, 1599434383228939L, 1600317151051842L, 1600340525908035L, 1603026367348762L, 1603026368397392L, 1603026384126038L, 1603026385174622L, 1603026387271755L, 1603026388320282L, 1603183523725367L, 1603200142606372L, 1603200142606373L, 1603204428136501L, 1606027471552600L, 1608485144952895L, 1608513783660631L, 1608515670048841L, 1608518293585939L, 1608624158867522L, 1608732618326029L, 1608732620423207L, 1608732647686213L, 1608732655026189L, 1608732657123397L, 1608732658171913L, 1608732659220547L, 1608732679143459L, 1608732682289186L, 1608732684386389L, 1608732685434967L, 1608732686483510L, 1608732688580681L, 1608732689629191L, 1608732692774920L, 1608732694872160L, 1608732696969297L, 1608732700114949L, 1608732701163534L, 1608732703260753L, 1608732704309288L, 1608732706406492L, 1608732707454995L, 1608732708503607L, 1609352017412180L, 1611333460099095L, 1611384108417085L, 1611458265808898L, 1612104904802356L, 1612109344473106L, 1612250974584855L, 1612256951468118L, 1612257108754458L, 1612257109803036L, 1612257111900166L, 1612257116094551L, 1612257117143123L, 1612257119240247L, 1612257120288773L, 1612293566693438L, 1612311897899040L, 1612339735494710L, 1612347944796248L, 1612348504735758L, 1612349198893101L, 1612349655023707L, 1612379578237011L, 1613024681066531L, 1614868047265833L, 1614934411640887L, 1614942495113269L, 1615057021632539L, 1615066188283956L, 1615066215546889L, 1615066216595508L, 1615066235469860L, 1615887882846225L, 1615915529601104L, 1615920596320354L, 1615920596320355L, 1616643189964822L, 1616643771924521L, 1616818645041238L, 1616818647138397L, 1616818703761508L, 1616818704810007L, 1616818706907150L, 1616818711101499L, 1616818715295794L, 1616818717392937L, 1616818718441507L, 1617550674821131L, 1617653901885456L, 1617657412517890L, 1617784939282473L, 1618419923353663L, 1618423365828702L, 1618507233034323L, 1618612741799999L, 1618612850851863L, 1618643796426806L, 1619331855220739L, 1619503427420243L, 1619562858610692L, 1621148165013530L, 1621308103262254L, 1621346926788692L, 1621347378724963L, 1622016093388831L, 1622020786815035L, 1622026119872533L, 1622026119872534L, 1622312415723567L, 1623010118270986L, 1623010213691418L, 1623177294839886L, 1623177393406016L, 1623210766434361L, 1623210774822975L, 1623210776920088L, 1623210780065891L, 1623210781114405L, 1623210783211583L, 1623210785308739L, 1623210786357325L, 1623210788454416L, 1623210789503056L, 1623210790551599L, 1623210801037360L, 1623210802085968L, 1623210803134552L, 1623210805231672L, 1623210807328804L, 1623210808377429L, 1623210810474541L, 1623210812571736L, 1623210813620276L, 1623210846126156L, 1623210848223235L, 1623210849271838L, 1623210851368984L, 1623210852417543L, 1623819266621532L, 1623840245481476L, 1623867448688732L, 1623919576547364L, 1623919757950983L, 1623998280564834L, 1624094132994100L, 1624094184374299L, 1624910015299607L, 1624941681246222L, 1625020535210027L, 1627682637938742L, 1627682753282066L, 1627682864431131L, 1627684831559776L, 1627694260355170L, 1628530437849107L, 1628530610864207L, 1628535014883376L, 1628535124983878L, 1628540775759967L, 1628662681108556L, 1628673328349202L, 1629264378134594L, 1629298833293337L, 1629456770859023L, 1629457318215745L, 1629472632668219L, 1629477693096013L, 1629477694144533L, 1629541042815022L, 1629541042815023L, 1629559160111123L, 1629559167451225L, 1629559169548338L, 1629559191568441L, 1629559192616995L, 1629559194714139L, 1629559196811326L, 1629559198908473L, 1629559214637084L, 1629559216734280L, 1629559218831437L, 1629559219880013L, 1629559221977121L, 1629596604760105L, 1630381758545949L, 1630413099434020L, 1631084940951597L, 1631269462016013L, 1631269597282340L, 1631269747228676L, 1631269894029368L, 1631270010421278L, 1631328520962128L, 1632008244625444L, 1632183718576215L, 1632183808753669L, 1633961150316609L, 1633963743445064L, 1633963745542235L, 1633963748687897L, 1633963750785088L, 1634111309545506L, 1634767300788246L, 1634786180399150L, 1634977906229291L, 1634977908326485L, 1634977909375062L, 1634977910423633L, 1635654638305378L, 1635676790521919L, 1635679638454307L, 1635788716572712L, 1635800936677429L, 1635801034194946L, 1635872945537108L, 1635872946585696L, 1635872958119989L, 1635873010548827L, 1635873011597379L, 1635873013694550L, 1635873062977600L, 1635873065074744L, 1636590031011902L, 1636592711172166L, 1636599430447120L, 1636678818136134L, 1636697591840828L, 1636813548617769L, 1636813549666335L, 1636813552812046L, 1636864812449881L, 1636864813498394L, 1636864813498442L, 1636864813498445L, 1637611455184907L, 1637628543828043L, 1637632704577626L, 1637669165662292L, 1637702884720732L, 1637702886817848L, 1637702888915025L, 1637702889963619L, 1637702892060759L, 1637702893109281L, 1637702907789327L, 1637702908837976L, 1637702930858037L, 1638371181002806L, 1640374598697027L, 1640470624141381L, 1640470624141387L, 1640470628335619L, 1641068670025753L, 1641105600872505L, 1641343298371655L, 1641343343460375L, 1641356506234938L, 1641356507283478L, 1642136488443908L, 1642177538097211L, 1642238648057940L, 1642238655397925L, 1642238656446518L, 1642238658543645L, 1642238659592277L, 1642238661689354L, 1642238662737925L, 1642238664835089L, 1642238666932321L, 1642238681612340L, 1642238682660913L, 1642898970968153L, 1643044972593185L, 1643056731324510L, 1643845119967279L, 1643943689257026L, 1643969108836448L, 1643974101106768L, 1644068141596751L, 1644892150366225L, 1646523574190094L, 1646523583627277L, 1646523633958980L, 1646527055462467L, 1646555264254006L, 1646661527994400L, 1646677389803571L, 1646691204792396L, 1646768233185343L, 1648308826210350L, 1648311068065847L, 1648346204798993L, 1648401110335499L, 1648473997901835L, 1648556393955336L, 1649189635293235L, 1649189636341794L, 1649189636341800L, 1649189638438943L, 1649189640536137L, 1649189641584718L, 1649235477987397L, 1650311984906261L, 1650312117026876L, 1650312246001685L, 1650336360103965L, 1650336772194364L, 1650378337746998L, 1650395624570937L, 1650396781150281L, 1651181605683234L, 1651181761921098L, 1651181917110339L, 1652992504823810L, 1653014065643543L, 1653015358537775L, 1653015755948060L, 1653015983489063L, 1653016136581171L, 1653016312741987L, 1653017719930885L, 1653019418624012L, 1653019536064518L, 1653019662942289L, 1653019776188427L, 1653019914600540L, 1653020036235293L, 1653023003705425L, 1653038851883012L, 1653760130613273L, 1653807870181470L, 1653807882764359L, 1653807883812872L, 1653807885910023L, 1653807886958612L, 1653807889055776L, 1653807890104374L, 1653807892201479L, 1653807894298702L, 1653807895347298L, 1653807897444369L, 1653807898492932L, 1653810055413854L, 1653810057510922L, 1653918703616047L, 1653921356513343L, 1653921507508307L, 1653921675280387L, 1653932221857824L, 1653969282727999L, 1653981667459127L, 1654684761784376L, 1654723799220252L, 1654802033475638L, 1655586498347089L, 1655619653271594L, 1655860515373109L, 1655860525858839L, 1655860527956046L, 1655860529004555L, 1655860531101792L, 1655860561510462L, 1655860562559024L, 1655862044196910L, 1656451894411274L, 1656667587543091L, 1657369615466500L, 1657369827278942L, 1657390350008370L, 1657390352105546L, 1657390362591267L, 1657390364688478L, 1657390365737020L, 1659171153969188L, 1659405291552833L, 1659434005758037L, 1659437920092223L, 1659448887148563L, 1660293713297431L, 1660293846466645L, 1660293971247165L, 1660294093930534L, 1660311827447886L, 1661057513881645L, 1661140246528085L, 1661175232266264L, 1661175343415339L, 1661897183133761L, 1661897339371545L, 1661897485123633L, 1661897627730018L, 1661897776627775L, 1661897910845534L, 1661898278895697L, 1661898385850407L, 1661951313707055L, 1662187087069238L, 1662195953893410L, 1662195984302149L, 1662195986333716L, 1662195987447889L, 1662195995836488L, 1662196008419362L, 1662196013662246L, 1662196023099422L, 1662196025196581L, 1662995287572568L, 1663762897633329L, 1665552712990722L, 1665554696962138L, 1665555013632045L, 1665555151978563L, 1665555265290317L, 1665555406848057L, 1665683799670877L, 1665683911868466L, 1665684031406163L, 1665684139409490L, 1666421623947356L, 1666601885630513L, 1666615757307989L, 1666679535829037L, 1666692484759603L, 1667316709392442L, 1667316888633359L, 1667357270409277L, 1667507254525993L, 1667507449561106L, 1667507945472062L, 1667508094435330L, 1667508246478872L, 1667513494536278L, 1667513497747459L, 1667513499844704L, 1667513500893192L, 1667513512427561L, 1667513517670461L, 1667513518653532L, 1667513520750621L, 1667513522847792L, 1667513525010511L, 1667513525993492L, 1667513528090636L, 1667513529139246L, 1667513531301962L, 1667513533333583L, 1667513534382156L, 1667513536544803L, 1667513537593361L, 1667513539690513L, 1667513542770747L, 1667513544933466L, 1667513545981958L, 1667513549062189L, 1667513551224851L, 1667513597296667L, 1667513599459382L, 1667513600442412L, 1667513620365388L, 1667513623511070L, 1667513624559706L, 1667513626722334L, 1667513627770954L, 1667513647628376L, 1667513648742458L, 1668435077496850L, 1668458526867480L, 1668471451615260L, 1669312459898944L, 1669351089373219L, 1669351193247820L, 1669354934501439L, 1670122474307612L, 1670242287812703L, 1671862119628849L, 1671963644788792L, 1671963806269487L, 1671964045410324L, 1672035152429074L, 1672036622598161L, 1672078930477118L, 1672089900744728L, 1672124732342320L, 1672131562766344L, 1672935290961955L, 1672935466074191L, 1672935596097585L, 1672944622239763L, 1672950224781339L, 1673019712929825L, 1673019715026971L, 1673019718172711L, 1673019723415579L, 1673019731738630L, 1673019734884400L, 1673019735932938L, 1673019738030124L, 1673019743273010L, 1673019745435710L, 1673019746418776L, 1673019748581452L, 1673019749629965L, 1673019751661636L, 1673019752710228L, 1673019754807395L, 1673019755855874L, 1673019758018591L, 1673019759001630L, 1673019788361746L, 1673019807301695L, 1673019809398826L, 1673019810447415L, 1673019812544609L, 1673019813593176L, 1673019815624792L, 1673023199445064L};
        int startIndex = 0;
        if (startId != null) {
            for (int i = 0; i < goodsIds.length; i++) {
                if (goodsIds[i].equals(startId)) {
                    startIndex = i;
                }
            }
        }

        for (int i = startIndex; i < goodsIds.length; i++) {
            try {
                SupplierGoodsInfo supplierGoodsInfo = purchaseRpc.getSupplierGoodsById(goodsIds[i]);
                if (Objects.nonNull(supplierGoodsInfo)) {
                    eccangGoodsMgmtApp.syncSupplierGoods(supplierGoodsInfo);
                } else {
                    log.error("找不到对应SupplierGoods - {}", goodsIds[i]);
                }
            } catch (Exception e) {
                log.error("出现异常, 中断任务, 当前ID: {}", goodsIds[i], e);
                return ReturnT.FAIL;
            }
            Thread.sleep(1000L);
        }

        return ReturnT.SUCCESS;
    }

}
