package com.newnary.gsp.center.tpsi.app.listener.mq;


import com.alibaba.fastjson.JSONObject;
import com.newnary.gsp.center.purchase.api.product.response.SkuDetailInfo;
import com.newnary.gsp.center.tpsi.app.service.eccang.EccangGoodsMgmtApp;
import com.newnary.gsp.center.tpsi.infra.mq.consumer.GSPPurchaseGoodsMQConsumer;
import com.newnary.gsp.center.tpsi.infra.rpc.PurchaseRpc;
import com.newnary.messagebody.gsp.purchase.GSPPurchaseGoodsTopic;
import com.newnary.messagebody.gsp.purchase.mo.GSPPurchaseGoodsMO;
import com.newnary.mq.starter.consumer.AbstractMQProcessor;
import com.newnary.mq.starter.consumer.MQMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@Component
public class GSPPurchaseSkuChangeProcessor extends AbstractMQProcessor<GSPPurchaseGoodsMO> {

    @Resource
    private PurchaseRpc purchaseRpc;
    @Resource
    private EccangGoodsMgmtApp eccangGoodsMgmtApp;

    @Override
    public boolean doProcess(MQMessage<GSPPurchaseGoodsMO> message) {
        GSPPurchaseGoodsMO mo = message.getContent();
        log.info("接收采购系统商品SKU变更消息处理 - {}", JSONObject.toJSONString(mo));

        try {
            SkuDetailInfo skuDetailInfo = purchaseRpc.getBySkuId(mo.getSkuId());
            if (Objects.nonNull(skuDetailInfo)) {
                //TODO 商品同步字段对照未完成，暂不处理
            } else {
                log.error("找不到对应SKU - {}", mo.getSkuId());
            }
        } catch (Exception e) {
            log.error("接收采购系统商品SKU变更消息消费异常！mo={}, e={}", JSONObject.toJSONString(mo), e);
        }
        return true;
    }

    /**
     * 消费者类型
     *
     * @return
     */
    @Override
    public Class<?> consumerClz() {
        return GSPPurchaseGoodsMQConsumer.class;
    }

    /**
     * 标签
     *
     * @return
     */
    @Override
    public String tag() {
        return GSPPurchaseGoodsTopic.Tag.SKU_CHANGE;
    }

}
