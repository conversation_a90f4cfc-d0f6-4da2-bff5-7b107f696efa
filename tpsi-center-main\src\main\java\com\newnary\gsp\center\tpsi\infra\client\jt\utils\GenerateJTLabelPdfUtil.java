package com.newnary.gsp.center.tpsi.infra.client.jt.utils;

import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.itextpdf.text.Document;
import com.itextpdf.text.Element;
import com.itextpdf.text.Font;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.newnary.gsp.center.tpsi.infra.client.common.CreatePdfUtil;
import com.newnary.gsp.center.tpsi.infra.client.jt.valobj.request.GenerateJTPdfParam;
import com.newnary.gsp.center.tpsi.infra.client.common.PDFDirection;
import lombok.extern.slf4j.Slf4j;

import java.awt.*;
import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * @Author: jack
 * @CreateTime: 2023/12/26
 */
@Slf4j
public class GenerateJTLabelPdfUtil {

    public static ByteArrayOutputStream generatePdf(GenerateJTPdfParam pdfParam, String filePath) {

        String[] fixStr = {"EZ", "Order No: ", "RECEIVER:", "SENDER:", "COD: ", "Goods: ", "Price: ", "Weight: ", "DELIVERY ATTEMPTS", "1", "2", "3", "Remarks/Signature:"};
        String dateFormat = "yyyy/MM/dd HH:mm:ss";
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(dateFormat);
        String dateTimeStr = dtf.format(now);
        String[] dateTimeStrs = dateTimeStr.split(" ");
        String dateStr = dateTimeStrs[0];
        String timeStr = dateTimeStrs[1];

        Document document = null;
        PdfWriter writer = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {

            document = new Document(new Rectangle(283f, 425f), 0, 0, 7.08f, 7.08f);

            BaseFont blackFont = BaseFont.createFont(filePath + "HarmonyOS_Sans_SC_Black.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            BaseFont regularFont = BaseFont.createFont(filePath + "HarmonyOS_Sans_SC_Regular.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            BaseFont mediumFont = BaseFont.createFont(filePath + "HarmonyOS_Sans_SC_Medium.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);

            //写pdf---start
            writer = PdfWriter.getInstance(document, byteArrayOutputStream);  // Do this BEFORE document.open()
            document.open();

            PdfPTable table = new PdfPTable(20);
            table.setSplitLate(false);
            table.setWidthPercentage(100);
//            table.setPaddingTop(2.5f);
//            table.setLockedWidth(true);
            float mmPx = 2.83f;
            float halfCmPx = 14.16f;

            table.setWidths(new float[]{halfCmPx, halfCmPx, halfCmPx, halfCmPx, halfCmPx, halfCmPx, halfCmPx, halfCmPx, halfCmPx, halfCmPx, halfCmPx, halfCmPx, halfCmPx, halfCmPx, halfCmPx, halfCmPx, halfCmPx, halfCmPx, halfCmPx, halfCmPx});

            Font black23Chine = new Font(blackFont, 23);
            Font black30Chine = new Font(blackFont, 30);

            Font regular6Chine = new Font(regularFont, 6);
            Font regular6BoldChine = new Font(regularFont, 6, Font.BOLD);
            Font regular7Chine = new Font(regularFont, 7);
            Font regular8Chine = new Font(regularFont, 8);
            Font regular8BoldChine = new Font(regularFont, 8, Font.BOLD);
            Font regular15Chine = new Font(regularFont, 15);
            Font regular16Chine = new Font(regularFont, 16);

            Font medium8Chine = new Font(mediumFont, 8);

            String logoPath = filePath + "JT_logo.png";
            String codPath = filePath + "cod.png";

            QrConfig qrConfig = QrConfig.create();
            qrConfig.setBackColor(Color.WHITE);
            qrConfig.setForeColor(Color.BLACK);

            CreatePdfUtil.addCell(table, "", Element.ALIGN_CENTER, new Font(), 0, 0, 2, halfCmPx);
            CreatePdfUtil.addCell(table, pdfParam.getWayBillNo(), Element.ALIGN_LEFT, medium8Chine, 0, 9, 0, halfCmPx);
            CreatePdfUtil.addCell(table, pdfParam.getWayBillNo(), Element.ALIGN_RIGHT, medium8Chine, 0, 9, 0, halfCmPx);
            CreatePdfUtil.addCell(table, "", Element.ALIGN_CENTER, new Font(), 0, 0, 2, halfCmPx);

            CreatePdfUtil.addImageCell(table, logoPath, 1, 11, 0, 14 * mmPx - 5, 11 * 5 * mmPx - 3, Element.ALIGN_MIDDLE);
            CreatePdfUtil.addCell(table, fixStr[0], Element.ALIGN_CENTER, black30Chine, 1, 7, 0, 14 * mmPx);
            CreatePdfUtil.addCell(table, pdfParam.getWayBillNo(), Element.ALIGN_LEFT, medium8Chine, 0, 7, 270, halfCmPx, PDFDirection.RIGHT);
            CreatePdfUtil.addCell(table, fixStr[1].concat(pdfParam.getOrderNo()), Element.ALIGN_LEFT, regular6Chine, 1, 11, 0, halfCmPx);
            CreatePdfUtil.addCell(table, dateStr, Element.ALIGN_LEFT, regular6Chine, 0, 4, 0, halfCmPx);
            CreatePdfUtil.addCell(table, timeStr, Element.ALIGN_RIGHT, regular6Chine, 0, 3, 0, halfCmPx);
            CreatePdfUtil.addCell(table, pdfParam.getWayBillNo(), Element.ALIGN_LEFT, medium8Chine, 0, 7, 270, halfCmPx, PDFDirection.LEFT);
            CreatePdfUtil.addCell(table, pdfParam.getPickupNo(), Element.ALIGN_CENTER, black23Chine, 1, 18, 0, 3 * halfCmPx);
            BitMatrix bitMatrix = new MultiFormatWriter().encode(pdfParam.getWayBillNo(), BarcodeFormat.CODE_128, 230, 38);
            CreatePdfUtil.addImageCell(table, MatrixToImageWriter.toBufferedImage(bitMatrix), 1, 15, 0, 14 * mmPx);
            CreatePdfUtil.addImageCell(table, MatrixToImageWriter.toBufferedImage(bitMatrix), 1, 3, 11, 3 * halfCmPx, 90);
            CreatePdfUtil.addCell(table, pdfParam.getReceiverAddressDetail(), Element.ALIGN_LEFT, regular16Chine, 1, 15, 0, 8 * mmPx);
            CreatePdfUtil.addCell(table, fixStr[2], Element.ALIGN_LEFT, regular8Chine, 0, 15, 0, halfCmPx);
            CreatePdfUtil.addCell(table, pdfParam.getReceiverName(), Element.ALIGN_LEFT, regular8BoldChine, 0, 15, 0, halfCmPx);
            CreatePdfUtil.addParagraphCell(table, pdfParam.getReceiverAddress(), Element.ALIGN_LEFT, regular8Chine, 15, 0, 2 * halfCmPx, PDFDirection.DOWN);
            CreatePdfUtil.addCell(table, pdfParam.getWayBillNo(), Element.ALIGN_LEFT, medium8Chine, 0, 8, 270, halfCmPx, PDFDirection.RIGHT);
            CreatePdfUtil.addCell(table, fixStr[3], Element.ALIGN_LEFT, regular6Chine, 0, 15, 0, 4 * mmPx);
            CreatePdfUtil.addCell(table, pdfParam.getWayBillNo(), Element.ALIGN_LEFT, medium8Chine, 0, 8, 270, halfCmPx, PDFDirection.LEFT);
            CreatePdfUtil.addCell(table, pdfParam.getSenderName(), Element.ALIGN_LEFT, regular6BoldChine, 0, 15, 0, 4 * mmPx);
            CreatePdfUtil.addCell(table, pdfParam.getSenderAddress(), Element.ALIGN_LEFT, regular6Chine, 15, 0, 4 * mmPx, PDFDirection.DOWN);
            CreatePdfUtil.addCell(table, fixStr[4].concat(pdfParam.getIsCod() ? pdfParam.getCodPrice() : "0"), Element.ALIGN_LEFT, regular6BoldChine, 0, 9, 0, halfCmPx);
            CreatePdfUtil.addCell(table, fixStr[8], Element.ALIGN_CENTER, regular7Chine, 1, 6, 0, halfCmPx);
            CreatePdfUtil.addParagraphCell(table, fixStr[5].concat(pdfParam.getGoodsName()), Element.ALIGN_LEFT, regular6Chine, 0, 9, 0, halfCmPx);
            CreatePdfUtil.addCell(table, fixStr[9], Element.ALIGN_CENTER, regular15Chine, 1, 2, 2, 2 * halfCmPx);
            CreatePdfUtil.addCell(table, fixStr[10], Element.ALIGN_CENTER, regular15Chine, 1, 2, 2, 2 * halfCmPx);
            CreatePdfUtil.addCell(table, fixStr[11], Element.ALIGN_CENTER, regular15Chine, 1, 2, 2, 2 * halfCmPx);
            CreatePdfUtil.addCell(table, fixStr[6].concat(/*pdfParam.getPriceValue()*/""), Element.ALIGN_LEFT, regular6Chine, 0, 5, 0, halfCmPx);
            CreatePdfUtil.addCell(table, fixStr[7].concat(pdfParam.getWeightValue()), Element.ALIGN_LEFT, regular6Chine, 0, 4, 0, halfCmPx);
            CreatePdfUtil.addCell(table, fixStr[12], Element.ALIGN_LEFT, regular6Chine, 11, 0, 4 * mmPx, PDFDirection.UP);
            CreatePdfUtil.addImageCell(table, QrCodeUtil.generate(pdfParam.getWayBillNo(), qrConfig), 1, 7, 2, 32 * mmPx);
            if (pdfParam.getIsCod()) {
                CreatePdfUtil.addWaterMaskCell(table, codPath, 0, 11, 0, 28 * mmPx);
            } else {
                CreatePdfUtil.addCell(table, "", Element.ALIGN_CENTER, new Font(), 0, 11, 0, 28 * mmPx);
            }
            CreatePdfUtil.addCell(table, "", Element.ALIGN_CENTER, new Font(), 0, 0, 0, halfCmPx);
            CreatePdfUtil.addCell(table, pdfParam.getWayBillNo(), Element.ALIGN_LEFT, medium8Chine, 9, 0, halfCmPx, PDFDirection.UP);
            CreatePdfUtil.addCell(table, pdfParam.getWayBillNo(), Element.ALIGN_RIGHT, medium8Chine, 9, 0, halfCmPx, PDFDirection.UP);
            CreatePdfUtil.addCell(table, "", Element.ALIGN_CENTER, new Font(), 0, 0, 0, halfCmPx);

            document.add(table);
            byteArrayOutputStream.close();

            document.close();

            return byteArrayOutputStream;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (document != null) {
                document.close();
            }
            if (writer != null) {
                writer.close();
            }
        }
        return new ByteArrayOutputStream();

    }

    public static void main(String[] args) {
//        generatePdf();
    }

}
