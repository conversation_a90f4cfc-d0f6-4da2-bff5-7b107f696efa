package com.newnary.gsp.center.tpsi.eccang;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp.*;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.erp.EcCangERPGetProductBySkuResponse;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.erp.EcCangERPGetPurchaseOrdersResponse;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.service.eccang.IEccangERPApiSve;
import com.newnary.test.starter.BaseTestInjectTenant;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: jack
 * @CreateTime: 2025/5/9
 */
public class EccangERPTest extends BaseTestInjectTenant {

    @Override
    protected String tenantId() {
        return "TENANT3924117688555169382400";
    }

    @Resource
    private IEccangERPApiSve eccangERPApiSve;

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Test
    public void testGetWarehouseList() {
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGERP0003");
        EcCangApiBaseResult<String> result = eccangERPApiSve.getWarehouseList(thirdPartySystem);

        System.out.println(JSON.toJSONString(result.getData()));
    }

    @Test
    public void testGetUserList() {
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGERP0003");
        EcCangApiBaseResult<String> result = eccangERPApiSve.getUserList(thirdPartySystem);

        System.out.println(JSON.toJSONString(result.getData()));
    }

    @Test
    public void testGetPurchaseOrders() {
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGERP0003");
        EcCangApiBaseResult<String> result = eccangERPApiSve.getPurchaseOrdersNoDecode("R25040100278", thirdPartySystem, null, null);
        if (result.getCode().equals("200")) {
            //更新关联信息
            Map<String, List<String>> map = new ConcurrentHashMap<>();
            List<EcCangERPGetPurchaseOrdersResponse> responsePurchaseOrders = JSONObject.parseArray(result.getData(), EcCangERPGetPurchaseOrdersResponse.class);
            if (CollectionUtils.isNotEmpty(responsePurchaseOrders)) {
                responsePurchaseOrders.forEach(responsePurchaseOrder -> {
                    List<String> refNoList = map.get(responsePurchaseOrder.getRef_no());
                    if (CollectionUtils.isEmpty(refNoList)) {
                        refNoList = new ArrayList<>();
                        refNoList.add(responsePurchaseOrder.getPo_code());
                        map.put(responsePurchaseOrder.getRef_no(), refNoList);
                    } else {
                        refNoList.add(responsePurchaseOrder.getPo_code());
                    }
                });
                //输出map中refNoList>1的记录
                map.forEach((k, v) -> {
                    if (v.size() > 1) {
                        System.out.println(k + ":" + v);
                    }
                });
            }
//            EcCangERPGetPurchaseOrdersResponse ecCangERPGetPurchaseOrdersResponse = responsePurchaseOrders.get(0);
//            List<EcCangERPGetPurchaseOrdersResponse.Detail> detailList = ecCangERPGetPurchaseOrdersResponse.getDetail();
//            for (EcCangERPGetPurchaseOrdersResponse.Detail detail : detailList) {
//                EcCangERPGetProductBySkuRequest request = new EcCangERPGetProductBySkuRequest();
//                request.setProductSku(Collections.singletonList(detail.getProduct_sku()));
//                EcCangApiBaseResult<String> skuResult = eccangERPApiSve.getProductBySku(thirdPartySystem, request);
//                List<EcCangERPGetProductBySkuResponse> ecCangERPGetProductBySkuResponses = JSON.parseArray(result.getData(), EcCangERPGetProductBySkuResponse.class);
//                System.out.println(ecCangERPGetProductBySkuResponses);
//            }
//            EcCangERPSyncPurchaseOrdersRequest request = new EcCangERPSyncPurchaseOrdersRequest();
////            request.action_type = "EDIT";
//            request.po_code = ecCangERPGetPurchaseOrdersResponse.po_code;
//            request.warehouse_id = ecCangERPGetPurchaseOrdersResponse.warehouse_id;
//            request.tracking_no = "testUpdate99";

//            EcCangApiBaseResult<String> stringEcCangApiBaseResult = eccangERPApiSveImpl.syncPurchaseOrders(t,request);
//            System.out.println(stringEcCangApiBaseResult);
        }
    }

    @Test
    public void testGetProductBySku() {
        EcCangERPGetProductBySkuRequest ecCangERPGetProductBySkuRequest = new EcCangERPGetProductBySkuRequest();
        List<String> list = new ArrayList<>();
        list.add("AUT-WRL-515G");
        ecCangERPGetProductBySkuRequest.setProductSku(list);
        //ecCangERPGetProductBySkuRequest.setProductSku("SKU000031");
        //ecCangERPGetProductBySkuRequest.setProductSku("SKU000031,SKU000033");
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGERP0003");
        EcCangApiBaseResult<String> result = eccangERPApiSve.getProductBySku(thirdPartySystem, ecCangERPGetProductBySkuRequest);
        List<EcCangERPGetProductBySkuResponse> ecCangERPGetProductBySkuResponses = JSON.parseArray(result.getData(), EcCangERPGetProductBySkuResponse.class);
        System.out.println(ecCangERPGetProductBySkuResponses);
    }

    @Test
    public void testGetProductList() {
        EcCangERPGetProductListRequest ecCangERPGetProductListRequest = new EcCangERPGetProductListRequest();
        ecCangERPGetProductListRequest.setPage(1);
        ecCangERPGetProductListRequest.setPageSize(50);
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGERP0003");
        EcCangApiBaseResult<String> result = eccangERPApiSve.getProductList(thirdPartySystem, ecCangERPGetProductListRequest);
        System.out.println(result);
    }

    //获取供应商测试
    @Test
    public void testGetSupplier() {
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGERP0003");
        EcCangApiBaseResult<String> supplier = eccangERPApiSve.getSupplier(thirdPartySystem);
        System.out.println(supplier.getData());
    }

    //测试绑定供应商
    @Test
    public void testSyncSupplierProductResponse() {
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGERP0003");
        EcCangERPSyncSupplierProductRequest ecCangERPSyncSupplierProductRequest = new EcCangERPSyncSupplierProductRequest();
        ecCangERPSyncSupplierProductRequest.setActionType("ADD");
        ecCangERPSyncSupplierProductRequest.setSupplierId(21886);
        ecCangERPSyncSupplierProductRequest.setSpSupplierProductCode("708729917696");
        ecCangERPSyncSupplierProductRequest.setSpPurchaseUnit(0);
        ecCangERPSyncSupplierProductRequest.setSpDefault(0);
        ecCangERPSyncSupplierProductRequest.setSpMinQty(1);
        ecCangERPSyncSupplierProductRequest.setSpEtaTime(1);
        ecCangERPSyncSupplierProductRequest.setBuyerId(908);
        ecCangERPSyncSupplierProductRequest.setCurrencyCode("RMB");
        EcCangApiBaseResult<String> result = eccangERPApiSve.syncSupplierProduct(thirdPartySystem, ecCangERPSyncSupplierProductRequest);
        System.out.println(JSON.toJSONString(result));
    }

    //获取库位测试
    @Test
    public void testGetWarehouseLocation() {
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGERP0003");
        EcCangERPGetWarehouseLocationRequest ecCangERPGetWarehouseLocationRequest = new EcCangERPGetWarehouseLocationRequest();
        ecCangERPGetWarehouseLocationRequest.setPage(1);
        ecCangERPGetWarehouseLocationRequest.setPageSize(1);
        EcCangApiBaseResult<String> warehouseLocation = eccangERPApiSve.getWarehouseLocation(thirdPartySystem, ecCangERPGetWarehouseLocationRequest);
        System.out.println(JSON.toJSONString(warehouseLocation));
    }

    @Test
    public void testVerifyPurchaseTest() {
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGERP0003");
        EcCangERPPurchaseOrderVerifyPurchaseRequest verifyPurchaseRequest = new EcCangERPPurchaseOrderVerifyPurchaseRequest();
        verifyPurchaseRequest.setPoCode(Collections.singletonList("PO823112330264"));
        verifyPurchaseRequest.setVerify(1);
        verifyPurchaseRequest.setVerifyUserId("855");
        EcCangApiBaseResult<String> verifyPurchase = eccangERPApiSve.verifyPurchase(thirdPartySystem, verifyPurchaseRequest);
        System.out.println(verifyPurchase);
    }

    @Test
    public void testSyncConfirmReceiving() {
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGERP0003");
        EcCangERPPurchaseOrderSyncConfirmReceivingRequest request = new EcCangERPPurchaseOrderSyncConfirmReceivingRequest();
        request.setReason("强制完成");
        request.setPo_Code("PO823112730116");
        request.setReceiving_code("R823112730085");
        EcCangApiBaseResult<String> verifyPurchase = eccangERPApiSve.syncConfirmReceiving(thirdPartySystem, request);
        System.out.println(verifyPurchase);
    }

    @Test
    public void testSyncPurchaseTrackingNote() {
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGERP0003");
        EcCangERPPurchaseOrderSyncPurchaseTrackingNoteRequest request = new EcCangERPPurchaseOrderSyncPurchaseTrackingNoteRequest();
        request.setPoCode("PO22823121430001");
        request.setSupplierMethodId("2");
        request.setPurchaseShipperId(27);
        request.setTrackNote("test12183");
        request.setTrackingNoteRecord(Collections.singletonList(new EcCangERPPurchaseOrderSyncPurchaseTrackingNoteRequest.TrackingNoteRecord("test12182")));
        EcCangApiBaseResult<String> stringEcCangApiBaseResult = eccangERPApiSve.syncPurchaseTrackingNote(thirdPartySystem, request);
        System.out.println(stringEcCangApiBaseResult);
    }

    @Test
    public void testSyncProduct2() {
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGERP0003");
        EcCangERPSyncProductRequest request = new EcCangERPSyncProductRequest();

        request.actionType = "EDIT";
        request.productSku = "4874922100795";

        request.productLength = 4.0;
        request.productWidth = 4.0;
        request.productHeight = 4.0;
        request.pdNetWeight = 4.0;

        EcCangApiBaseResult<String> stringEcCangApiBaseResult = eccangERPApiSve.syncProduct(thirdPartySystem, request);
        System.out.println(stringEcCangApiBaseResult);
    }

    private ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }

}
