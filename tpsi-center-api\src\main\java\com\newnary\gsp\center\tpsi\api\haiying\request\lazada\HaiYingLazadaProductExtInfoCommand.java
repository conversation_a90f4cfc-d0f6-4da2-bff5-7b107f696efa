package com.newnary.gsp.center.tpsi.api.haiying.request.lazada;

import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingStation;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingLazadaProductExtInfoCommand {

    /**
     * 站点
     */
    @NotNull(message = "站点不能为空")
    private List<HaiYingStation> stations;

    /**
     * 商品id
     * (多个商品id用逗号分隔，最多100个商品id)
     */
    @NotNull(message = "商品id不能为空")
    private List<String> item_ids;

}
