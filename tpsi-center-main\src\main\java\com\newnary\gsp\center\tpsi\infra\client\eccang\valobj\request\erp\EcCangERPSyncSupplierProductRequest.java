package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp;

import lombok.Data;

@Data
public class EcCangERPSyncSupplierProductRequest {

    //操作类型
    private String actionType;

    //供应商产品id
    private Integer spId;

    //供应商id
    private Integer supplierId;

    //供应商产品代码
    private String spSupplierProductCode;

    //最低采购量
    private Integer spMinQty;

    //默认采购员
    private Integer buyerId;

    //供应商产品，当选择整件时，增加1：补货取整，0：补货不取整
    private Integer spPurchaseRounding;

    //供应商产品地址
    private Object spProductAddress;

    //0	采购单位 0:散件 1:整件
    private Integer spPurchaseUnit;

    //采购交期
    private Integer spEtaTime;

    //供应商sku或者货号
    private String spSupplierSku;

    //供应商单价
    private Float spUnitPrice;

    //含税单价
    private Float spContractPrice;

    //是否启用产品有效期，0否 1是
    private Integer isValidate;

    //单价有效期, 例:"2020-08-21"
    private String priceValidate;

    //采购参考价
    private Float spReferenceUnitPrice;

    //税率
    private Float spTaxRate;

    //（仓库产品:1688产品比例，例如1/12）仓库产品
    private Integer sp_proportion1688_1;

    //（仓库产品:1688产品比例，例如1/12）1688产品
    private Integer sp_proportion1688_2;

    //是否默认供应商
    private Integer spDefault;
    //币种
    private String currencyCode;

    //是否启用价格区间的有效期 0否 1是
    private Integer isValidateMap;

    //-	价格区间，最小采购量
    private Object min;

    //价格区间，最大采购量
    private Object max;

    //-	价格区间，价格
    private Object price;

    //-	价格区间，有效期
    private Object validate;

}
