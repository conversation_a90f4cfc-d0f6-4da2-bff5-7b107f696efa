package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.wms;

import java.util.List;

public class EcCangFreightDealClaimOrdersRequest {

    public Integer warehouse_id;
    public String claim_code;
    public Integer handle_method;
    public String handle_remark;

    public List<Order> new_order;
    public String return_name;
    public String return_phone;
    public String return_address;


    public static class Order {
        public String order_code;
        public Integer pid;
        public Integer count;
        public Integer type;
        public String product_standard;
    }
}
