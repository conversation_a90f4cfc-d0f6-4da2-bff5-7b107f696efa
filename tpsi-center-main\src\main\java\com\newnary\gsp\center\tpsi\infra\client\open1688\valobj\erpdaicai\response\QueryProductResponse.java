package com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Data
public class QueryProductResponse {

    /**
     * 商品ID
     */
    private Long  productID;

    /**
     * 商品类型，在线批发商品(wholesale)或者询盘商品(sourcing)，1688网站缺省为wholesale
     */
    private String productType;

    /**
     * 类目ID，标识商品所属叶子类目
     */
    private Long   categoryID;

    /**
     * 分组ID，确定商品所属分组。1688可传入多个分组ID，国际站同一个商品只能属于一个分组，因此默认只取第一个
     */
    private List<Long> groupID;

    /**
     * 商品状态。published:上网状态;member expired:会员撤销;auto expired:自然过期;expired:过期(包含手动过期与自动过期);member deleted:会员删除;modified:修改;new:新发;deleted:删除;TBD:to be delete;approved:审批通过;auditing:审核中;untread:审核不通过;
     */
    private String status;

    /**
     * 商品标题，最多128个字符
     */
    private String subject;

    /**
     * 商品详情描述，可包含图片中心的图片URL
     */
    private String description;

    /**
     * 语种，参见FAQ 语种枚举值，1688网站默认传入CHINESE
     */
    private String language;

    /**
     * 信息有效期，按天计算，国际站无此信息
     */
    private Integer periodOfValidity;

    /**
     * 业务类型。1：商品，2：加工，3：代理，4：合作，5：商务服务。国际站按默认商品。
     */
    private Integer bizType;

    /**
     * 是否图片私密信息，国际站此字段无效
     */
    private Boolean pictureAuth;

    /**
     * 供应商用户ID
     */
    private String supplierUserId;

    /**
     * 质量星级(0-5)
     */
    private Integer qualityLevel;

    /**
     * 供应商loginId
     */
    private String supplierLoginId;

    /**
     * 类目名
     */
    private String categoryName;

    /**
     * 主图视频播放地址
     */
    private String mainVedio;

    /**
     * 商品货号，产品属性中的货
     */
    private String productCargoNumber;

    /**
     * 是否海外代发
     */
    private Boolean crossBorderOffer;

    /**
     * 参考价格，返回价格区间，可能为空
     */
    private String referencePrice;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 最后操作时间
     */
    private String lastUpdateTime;

    /**
     * 过期时间
     */
    private String expireTime;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 审核时间
     */
    private String approvedTime;

    /**
     * 最后重发时间
     */
    private String lastRepostTime;

    /**
     * 成交量
     */
    private Integer bookedCount;

    /**
     * 产品线
     */
    private String productLine;

    /**
     * 详情视频
     */
    private String detailVedio;

    /**
     * 卖家旺旺ID
     */
    private String sellerLoginId;

    /**
     * 产品属性
     */
    private List<ProductAttribute> productAttribute;

    /**
     * 商品主图
     */
    private ProductImage productImage;

    /**
     * sku信息
     */
    private List<ProductSkuInfo> productSkuInfos;

    /**
     * 商品销售信息
     */
    private ProductSaleInfo productSaleInfo;

    /**
     * 商品物流信息
     */
    private ProductShippingInfo productShippingInfo;

    /**
     * 商品扩展信息
     */
    private List<ProductExtendInfo> productExtendInfos;

    /**
     * 产品业务的支持信息,support为false说明不支持.
     */
    private List<ProductBizGroupInfo> productBizGroupInfos;

    /**
     * 卖家ID
     */
    private Long sellerId;

    /**
     * 预售开售时间
     */
    private String sellStartTime;

    /**
     * 限售区域code
     */
    private SaleLimitAddress saleLimitAddress;


    @Setter
    @Getter
    public static class ProductAttribute {

        /**
         * 属性ID
         */
        private Long attributeID;

        /**
         * 属性名称
         */
        private String attributeName;

        /**
         * 属性值ID
         */
        private Long valueID;

        /**
         * 属性值
         */
        private String value;

        /**
         * 是否为自定义属性，国际站无需关注
         */
        private Boolean isCustom;
    }

    @Setter
    @Getter
    public static class ProductImage {

        /**
         * 主图列表，使用相对路径，需要增加域名：https://cbu01.alicdn.com/
         */
        private List<String> images;

        /**
         * 是否打水印，是(true)或否(false)，1688无需关注此字段，1688的水印信息在上传图片时处理
         */
        private Boolean isWatermark;

        /**
         * 水印是否有边框，有边框(true)或者无边框(false)，1688无需关注此字段，1688的水印信息在上传图片时处理
         */
        private Boolean isWatermarkFrame;

        /**
         * 水印位置，在中间(center)或者在底部(bottom)，1688无需关注此字段，1688的水印信息在上传图片时处理
         */
        private String watermarkPosition;

    }

    @Setter
    @Getter
    public static class ProductSkuInfo {

        /**
         *
         * 指定规格的货号
         */
        private String cargoNumber;

        /**
         * 可销售数量
         */
        private Integer amountOnSale;

        /**
         * 建议零售价
         */
        private BigDecimal retailPrice;

        /**
         * skuId,该规格在所有商品中的唯一标记
         */
        private Long skuId;

        /**
         * specId,该规格在本商品内的唯一标记
         */
        private String specId;

        /**
         * 报价时该规格的单价
         */
        private BigDecimal price;

        /**
         * 分销基准价。代销场景均使用该价格。无SKU商品查看saleInfo中的consignPrice
         */
        private BigDecimal consignPrice;

        /**
         * CPS建议价（单位：元）
         */
        private BigDecimal cpsSuggestPrice;

        /**
         * 厂货通渠道专享价（单位：元）
         */
        private BigDecimal channelPrice;

        /**
         * 批发团价
         */
        private BigDecimal pftPrice;

        /**
         * 精选货源价
         */
        private BigDecimal jxhyPrice;

        /**
         * SKU属性值，可填多组信息
         */
        private List<Attribute> attributes;
    }

    @Setter
    @Getter
    public static class Attribute {
        /**
         * sku属性ID
         */
        private Long attributeID;

        /**
         * sku值内容，国际站不用关注
         */
        private String attributeValue;

        /**
         * sku图片
         */
        private String skuImageUrl;

        /**
         * sku属性ID所对应的显示名，比如颜色，尺码
         */
        private String attributeDisplayName;

        /**
         * 属性类型
         */
        private String attrType;

        /**
         * sku属性ID所对应的显示名，比如颜色，尺码
         */
        private String attributeName;
    }

    @Setter
    @Getter
    public static class ProductSaleInfo {

        /**
         * 是否支持网上交易。true：支持 false：不支持
         */
        private Boolean supportOnlineTrade;

        /**
         * 是否支持混批
         */
        private Boolean mixWholeSale;

        /**
         * 是否价格私密信息
         */
        private Boolean priceAuth;

        /**
         * 可售数量
         */
        private Integer amountOnSale;

        /**
         * 计量单位
         */
        private String unit;

        /**
         * 最小起订量，范围是1-99999。
         */
        private Integer minOrderQuantity;

        /**
         * 每批数量，默认为空或者非零值，该属性不为空时sellunit为必填
         */
        private Integer batchNumber;

        /**
         * 建议零售价
         */
        private BigDecimal retailprice;

        /**
         * 售卖单位，如果为批量售卖，代表售卖的单位，该属性不为空时batchNumber为必填，例如1"手"=12“件"的"手"
         */
        private String sellunit;

        /**
         * 0-无SKU按数量报价,1-有SKU按规格报价,2-有SKU按数量报价
         */
        private Integer quoteType;

        /**
         * 分销基准价。代销场景均使用该价格。有SKU商品查看skuInfo中的consignPrice
         */
        private BigDecimal consignPrice;

        /**
         * CPS建议价（单位：元）
         */
        private BigDecimal cpsSuggestPrice;

        /**
         * 厂货通渠道专享价（单位：元）
         */
        private BigDecimal channelPrice;

        /**
         * 区间价格。按数量范围设定的区间价格
         */
        private List<PriceRange> priceRanges;

        /**
         * 精选货源价
         */
        private BigDecimal pftPrice;

    }

    @Setter
    @Getter
    public static class PriceRange {

        /**
         * 起批量
         */
        private Integer startQuantity;

        /**
         * 商品价格
         */
        private BigDecimal price;

    }

    @Setter
    @Getter
    public static class ProductShippingInfo {
        /**
         * 运费模板ID，0表示运费说明，1表示卖家承担运费，其他值表示使用运费模版。此参数可调用运费模板相关API获取
         */
        private Long freightTemplateID;

        /**
         * 重量/毛重
         */
        private BigDecimal unitWeight;

        /**
         * 尺寸，单位是厘米，长宽高范围是1-9999999。1688无需关注此字段
         */
        private String packageSize;

        /**
         * 体积，单位是立方厘米，范围是1-9999999，1688无需关注此字段
         */
        private Integer volume;

        /**
         * 备货期，单位是天，范围是1-60。1688无需处理此字段
         */
        private Integer handlingTime;

        /**
         * 发货地址ID
         */
        private Long sendGoodsAddressId;

        /**
         * 发货地描述
         */
        private String sendGoodsAddressText;

        /**
         * 净重
         */
        private BigDecimal suttleWeight;

        /**
         * 宽度
         */
        private BigDecimal width;

        /**
         * 高度
         */
        private BigDecimal height;

        /**
         * 长度
         */
        private BigDecimal length;

        /**
         * 商品运费费率
         */
        //private List<FreightTemplate> freightTemplate;

        /**
         * 厂货通渠道专享价是否包邮，要结合非包邮地址，如果收货地址在非包邮地区则商品为不包邮
         */
        private Boolean channelPriceFreePostage;


    }

    @Setter
    @Getter
    public static class FreightTemplate {

        /**
         * 地址区域编码对应的文本（包括省市区，用空格隔开）
         */
        private String addressCodeText;

        /**
         * 发货地址地区码
         */
        private String fromAreaCode;

        /**
         * 运费模板ID
         */
        private Long id;

        /**
         * 快递子模版
         */
        private ExpressSubTemplate expressSubTemplate;

        /**
         * 货运子模版
         */
        private LogisticsSubTemplate logisticsSubTemplate;

        /**
         * 货到付款子模版
         */
        private CodSubTemplate codSubTemplate;
    }

    @Setter
    @Getter
    public static class ExpressSubTemplate {

        /**
         * 子模板
         */
        private SubTemplateDTO subTemplateDTO;

        /**
         * 费率
         */
        private List<Rate> rateList;

        @Setter
        @Getter
        public static class SubTemplateDTO {

            /**
             * 计件类型。0:重量 1:件数 2:体积
             */
            private Integer chargeType;

            /**
             * 是否系统模板
             */
            private Boolean isSysTemplate;
            /**
             * 运费承担类型 卖家承担：0；买家承担：1。
             */
            private Integer serviceChargeType;

            /**
             * 服务类型。0:快递 1:货运 2:货到付款
             */
            private Integer serviceType;

            /**
             * 子模板类型 0基准 1增值。默认0。
             */
            private Integer type;
        }

        @Setter
        @Getter
        public static class Rate {

            /**
             * 是否系统模板
             */
            private Boolean isSysRate;

            /**
             * 地址编码文本，用顿号隔开。例如：上海、福建省、广东省
             */
            private String toAreaCodeText;

            /**
             * 普通子模板费率
             */
            private RateDTO rateDTO;

            /**
             * 系统子模板费率
             */
            private SysRateDTO sysRateDTO;
        }

        @Setter
        @Getter
        public static class RateDTO {

            /**
             * 首重（单位：克）或首件（单位：件）
             */
            private Long firstUnit;

            /**
             * 首重或首件的价格（单位：分）
             */
            private Long firstUnitFee;

            /**
             * 最低一票
             */
            private Long leastExpenses;

            /**
             * 续重件单位
             */
            private Long nextUnit;

            /**
             * 续重件价格（单位：分）
             */
            private Long nextUnitFee;

            /**
             * 重量下限
             */
            private Long lowerRange;

            /**
             * 重量上限
             */
            private Long upperRange;
        }

        @Setter
        @Getter
        public static class SysRateDTO {

            /**
             * 首重（单位：克）或首件（单位：件）
             */
            private Long firstUnit;

            /**
             * 首重或首件的价格（单位：分）
             */
            private Long firstUnitFee;

            /**
             * 最低一票
             */
            private Long leastExpenses;

            /**
             * 续重（单位：克）或续件（单位：件）单位
             */
            private Long nextUnit;

            /**
             * 续重件价格（单位：分）
             */
            private Long nextUnitFee;

            /**
             * 重量下限
             */
            private Long lowerRange;

            /**
             * 重量上限
             */
            private Long upperRange;

        }
    }

    @Setter
    @Getter
    public static class LogisticsSubTemplate {

        /**
         * 子模版
         */
        private SubTemplateDTO subTemplateDTO;

        /**
         * 费率
         */
        private List<Rate> rateList;

        @Setter
        @Getter
        public static class SubTemplateDTO {

            /**
             * 计件类型。0:重量 1:件数 2:体积
             */
            private Integer chargeType;

            /**
             * 是否系统模板
             */
            private Boolean isSysTemplate;
            /**
             * 运费承担类型 卖家承担：0；买家承担：1。
             */
            private Integer serviceChargeType;

            /**
             * 服务类型。0:快递 1:货运 2:货到付款
             */
            private Integer serviceType;

            /**
             * 子模板类型 0基准 1增值。默认0。
             */
            private Integer type;
        }

        @Setter
        @Getter
        public static class Rate {

            /**
             * 是否系统模板
             */
            private Boolean isSysRate;

            /**
             * 地址编码文本，用顿号隔开。例如：上海、福建省、广东省
             */
            private String toAreaCodeText;

            /**
             * 普通子模板费率
             */
            private RateDTO rateDTO;

            /**
             * 系统子模板费率
             */
            private SysRateDTO sysRateDTO;
        }

        @Setter
        @Getter
        public static class RateDTO {

            /**
             * 首重（单位：克）或首件（单位：件）
             */
            private Long firstUnit;

            /**
             * 首重或首件的价格（单位：分）
             */
            private Long firstUnitFee;

            /**
             * 最低一票
             */
            private Long leastExpenses;

            /**
             * 续重件单位
             */
            private Long nextUnit;

            /**
             * 续重件价格（单位：分）
             */
            private Long nextUnitFee;

            /**
             * 重量下限
             */
            private Long lowerRange;

            /**
             * 重量上限
             */
            private Long upperRange;
        }

        @Setter
        @Getter
        public static class SysRateDTO {

            /**
             * 首重（单位：克）或首件（单位：件）
             */
            private Long firstUnit;

            /**
             * 首重或首件的价格（单位：分）
             */
            private Long firstUnitFee;

            /**
             * 最低一票
             */
            private Long leastExpenses;

            /**
             * 续重（单位：克）或续件（单位：件）单位
             */
            private Long nextUnit;

            /**
             * 续重件价格（单位：分）
             */
            private Long nextUnitFee;

            /**
             * 重量下限
             */
            private Long lowerRange;

            /**
             * 重量上限
             */
            private Long upperRange;

        }
    }

    @Setter
    @Getter
    public static class CodSubTemplate {

        /**
         * 子模版
         */
        private SubTemplateDTO subTemplateDTO;

        /**
         * 费率
         */
        private List<Rate> rateList;

        @Setter
        @Getter
        public static class SubTemplateDTO {

            /**
             * 计件类型。0:重量 1:件数 2:体积
             */
            private Integer chargeType;

            /**
             * 是否系统模板
             */
            private Boolean isSysTemplate;
            /**
             * 运费承担类型 卖家承担：0；买家承担：1。
             */
            private Integer serviceChargeType;

            /**
             * 服务类型。0:快递 1:货运 2:货到付款
             */
            private Integer serviceType;

            /**
             * 子模板类型 0基准 1增值。默认0。
             */
            private Integer type;
        }

        @Setter
        @Getter
        public static class Rate {

            /**
             * 是否系统模板
             */
            private Boolean isSysRate;

            /**
             * 地址编码文本，用顿号隔开。例如：上海、福建省、广东省
             */
            private String toAreaCodeText;

            /**
             * 普通子模板费率
             */
            private RateDTO rateDTO;

            /**
             * 系统子模板费率
             */
            private SysRateDTO sysRateDTO;
        }

        @Setter
        @Getter
        public static class RateDTO {

            /**
             * 首重（单位：克）或首件（单位：件）
             */
            private Long firstUnit;

            /**
             * 首重或首件的价格（单位：分）
             */
            private Long firstUnitFee;

            /**
             * 最低一票
             */
            private Long leastExpenses;

            /**
             * 续重件单位
             */
            private Long nextUnit;

            /**
             * 续重件价格（单位：分）
             */
            private Long nextUnitFee;

            /**
             * 重量下限
             */
            private Long lowerRange;

            /**
             * 重量上限
             */
            private Long upperRange;
        }

        @Setter
        @Getter
        public static class SysRateDTO {

            /**
             * 首重（单位：克）或首件（单位：件）
             */
            private Long firstUnit;

            /**
             * 首重或首件的价格（单位：分）
             */
            private Long firstUnitFee;

            /**
             * 最低一票
             */
            private Long leastExpenses;

            /**
             * 续重（单位：克）或续件（单位：件）单位
             */
            private Long nextUnit;

            /**
             * 续重件价格（单位：分）
             */
            private Long nextUnitFee;

            /**
             * 重量下限
             */
            private Long lowerRange;

            /**
             * 重量上限
             */
            private Long upperRange;

        }
    }

    @Setter
    @Getter
    public static class ProductExtendInfo {

        /**
         * 扩展结构的key
         */
        private String key;

        /**
         * 扩展结构的value
         */
        private String value;
    }

    @Setter
    @Getter
    public static class ProductBizGroupInfo {

        /**
         * 是否支持,isConsignMarketOffer=ture表示代销offer
         */
        private Boolean support;

        /**
         * 垂直市场名字
         */
        private String description;

        /**
         * 垂直市场标记
         */
        private String code;
    }

    @Setter
    @Getter
    public static class SaleLimitAddress {

        /**
         *
         * 禁售区域
         */
        private String limitAddressCodes;

        /**
         * 状态
         */
        private Integer status;

        /**
         * 备注
         */
        private String remark;
    }

}
