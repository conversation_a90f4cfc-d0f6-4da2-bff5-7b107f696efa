package com.newnary.gsp.center.tpsi.api.common.enums;

/**
 * 三方集成事件类型
 */
public enum ThirdPartEventType {

    GSP_2_ECCANG_PO_CREATE("GSP", "ECCANG", "从GSP同步创建采购单到易仓，并同步GSP收货明细"),
    GSP_2_ECCANG_PO_CREATE_PRODUCT_SUB("GSP", "ECCANG", "从GSP同步创建采购单到易仓(产品同步子任务)"),
    GSP_2_ECCANG_SYNC_TRACKING_NUMBER("GSP","ECCANG","从GSP同步快递单号到易仓"),

    TMS_TO_CN_JT_CREATE_ORDER("TMS","CN-JT","从TMS向CN-JT 创建并预报订单"),
    TMS_TO_CN_JT_QUERY_LABEL("TMS","CN-JT","从TMS向CN-JT 获取面单"),
    TMS_TO_CN_JT_QUERY_LOGISTICS("TMS","CN-JT","从TMS向CN-JT 获取所有渠道产品"),
    TMS_TO_CN_JT_QUERY_TRACK("TMS","CN-JT","从TMS向CN-JT 获取轨迹"),
    TMS_TO_CN_JT_CANCEL_ORDER("TMS","CN-JT","从TMS向CN-JT 取消预报"),
    ;

    /** 请求发起系统 */
    private String reqSys;
    /** 响应系统 */
    private String respSys;
    /** 事件描述 */
    private String eventDesc;

    ThirdPartEventType(String reqSys, String respSys, String eventDesc) {
        this.reqSys = reqSys;
        this.respSys = respSys;
        this.eventDesc = eventDesc;
    }
}
