package com.newnary.gsp.center.tpsi.infra.model;

import com.newnary.common.utils.idgenerator.core.UUIDUtils;
import com.newnary.gsp.center.tpsi.infra.model.creator.CrawlerProductCreate;
import com.newnary.gsp.center.tpsi.infra.model.event.CrawlerProduct_FailEvent;
import com.newnary.gsp.center.tpsi.infra.model.event.CrawlerProduct_FillCrawlerReturnEvent;
import com.newnary.gsp.center.tpsi.infra.model.event.CrawlerProduct_StoreProductIdEvent;
import com.newnary.gsp.center.tpsi.infra.model.id.CrawlerProductId;
import com.newnary.gsp.center.tpsi.infra.model.vo.CrawlerProductReturnState;
import com.newnary.gsp.center.tpsi.infra.model.vo.CrawlerProductState;
import com.newnary.spring.cloud.DomainEventBusHolder;
import com.newnary.spring.cloud.domain.Aggregate;
import lombok.Getter;

import javax.validation.constraints.NotNull;


/**
 * 爬虫获取商品详情id记录表
 *
 * <AUTHOR>
 * @since Created on 2023-03-17
 **/
@Getter
public class CrawlerProduct extends Aggregate {

    public static final String CRAWLER_PRODUCT_LOCK_PREFIX = "CRAWLER_PRODUCT_LOCK:";

    private CrawlerProductId crawlerProductId;

    /**
     *  类目名称
     */
    private String categoryName;

    /**
     * 页码
     */
    private Integer beginPage;

    /**
     * 页大小
     */
    private Integer pageSize;

    /**
     * 商品id
     */
    private String productId;

    /**
     *  是否入库，0未，1已入库
     */
    private Integer state;

    /**
     *  是否需要 重置价格以及库存状态标识
     */
    private Integer flushState;

    /**
     *  重置价格以及库存版本号
     */
    private Integer flushVersion;

    /**
     * 爬虫返回值
     */
    private String crawlerReturn;

    /**
     * 是否存储详情快照
     */
    private Integer crawlerReturnState;

    /**
     * 失败原因
     */
    private String failReason;

    public static CrawlerProduct createWith(@NotNull CrawlerProductCreate creator) {
        creator.setCrawlerProductId(new CrawlerProductId());
        return new CrawlerProduct(creator);
    }

    public static CrawlerProduct loadWith(@NotNull Long id, @NotNull CrawlerProductCreate creator) {
        return new CrawlerProduct(id, creator);
    }

    private CrawlerProduct(CrawlerProductCreate creator) {
        initBasic(creator);
    }

    private CrawlerProduct(Long id, CrawlerProductCreate creator) {
        super(id);
        initBasic(creator);
    }

    private void initBasic(CrawlerProductCreate creator) {
        setCrawlerProductId(creator.getCrawlerProductId());
        setCategoryName(creator.getCategoryName());
        setBeginPage(creator.getBeginPage());
        setPageSize(creator.getPageSize());
        setProductId(creator.getProductId());
        setState(creator.getState());
        setFlushState(creator.getFlushState());
        setFlushVersion(creator.getFlushVersion());
        setCrawlerReturn(creator.getCrawlerReturn());
        setCrawlerReturnState(creator.getCrawlerReturnState());
        setFailReason(creator.getFailReason());
    }


    public void setCrawlerProductId(CrawlerProductId crawlerProductId) {
        this.crawlerProductId = crawlerProductId;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public void setBeginPage(Integer beginPage) {
        this.beginPage = beginPage;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public void setCrawlerReturn(String crawlerReturn) {
        this.crawlerReturn = crawlerReturn;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public void setFlushState(Integer flushState) {
        this.flushState = flushState;
    }

    public void setFlushVersion(Integer flushVersion) {
        this.flushVersion = flushVersion;
    }

    public void storeProductId() {
        DomainEventBusHolder.bus().publish(new CrawlerProduct_StoreProductIdEvent(this, UUIDUtils.getUUID_32()));
    }

    public void setCrawlerReturnState(Integer crawlerReturnState) {
        this.crawlerReturnState = crawlerReturnState;
    }

    public void storeProductDetail() {
        setCrawlerReturnState(CrawlerProductReturnState.ALREADY_DETAIL_RETURN.getDetailState());
        //快照信息存储
        DomainEventBusHolder.bus().publish(new CrawlerProduct_FillCrawlerReturnEvent(this,UUIDUtils.getUUID_32()));
    }

    public void failProductDetail() {
        setState(CrawlerProductState.FAILURE_PRODUCT.getState());
        DomainEventBusHolder.bus().publish(new CrawlerProduct_FailEvent(this, UUIDUtils.getUUID_32()));
    }

    public void failWarehoused() {
        setState(CrawlerProductState.FAIL_WAREHOUSED.getState());
        DomainEventBusHolder.bus().publish(new CrawlerProduct_FailEvent(this,UUIDUtils.getUUID_32()));
    }
}
