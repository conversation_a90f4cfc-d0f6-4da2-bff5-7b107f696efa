package com.newnary.gsp.center.tpsi.app.service.eccang;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.distributed.tools.concurrent.template.DConcurrentTemplate;
import com.newnary.gsp.center.logistics.api.delivery.dto.TrackInfoDTO;
import com.newnary.gsp.center.logistics.api.delivery.enums.StockoutOrderState;
import com.newnary.gsp.center.logistics.api.delivery.request.StockoutOrderUpdateTrackCommand;
import com.newnary.gsp.center.logistics.api.delivery.request.StockoutOrderWithDeliveryQueryCommand;
import com.newnary.gsp.center.logistics.api.delivery.response.StockoutOrderInfo;
import com.newnary.gsp.center.purchase.api.order.request.BatchTakeStockCommand;
import com.newnary.gsp.center.purchase.api.order.request.EntryScanningCommand;
import com.newnary.gsp.center.purchase.api.order.response.PurchaseOrderGoodsInfo;
import com.newnary.gsp.center.purchase.api.order.response.PurchaseOrderInfo;
import com.newnary.gsp.center.tpsi.api.eccang.request.GspSyncPo2EccangCommand;
import com.newnary.gsp.center.tpsi.infra.client.eccang.params.EcCangERPParams;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.infra.rpc.PurchaseRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.StockoutOrderRpc;
import com.newnary.spring.cloud.domain.Asserts;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class StockoutPoMgmtApp {

    private static String MGMT_PREFIX = EccangPoMgmtApp.class.getName();

    @Value("${gsp.stockout-order.mxc.biz-id}")
    private String erpTpsId;

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Resource
    private StockoutOrderRpc stockoutOrderRpc;

    @Resource
    private PurchaseRpc purchaseRpc;

    public void syncTrackingNumber2StockoutOrder(GspSyncPo2EccangCommand command) {

        DConcurrentTemplate.tryLockMode(
                MGMT_PREFIX.concat(command.getPurchaseOrderId()),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    try {
                        PurchaseOrderInfo purchaseOrderInfo = purchaseRpc.queryOrderDetail(Long.valueOf(command.getPurchaseOrderId()));
                        if (StringUtils.isNotBlank(purchaseOrderInfo.getTrackingNumber())) {
                            StockoutOrderUpdateTrackCommand updateTrackCommand = new StockoutOrderUpdateTrackCommand();
                            updateTrackCommand.setStockoutOrderId(purchaseOrderInfo.getRefOrderNumber());
                            String trackingNumber = purchaseOrderInfo.getTrackingNumber();
                            updateTrackCommand.setTrackInfos(Collections.singletonList(new TrackInfoDTO(null, trackingNumber)));
                            stockoutOrderRpc.addTrack(updateTrackCommand);
                        }
                    } catch (Exception e) {
                        log.error("采购快递单号同步出库单快递单号消息处理失败：id: {}", command.getPurchaseOrderId(), e);
                    }
                }
        );
    }

    public void checkStockoutFinishSyncPurchaseOrder(String stockoutOrderId) {
        DConcurrentTemplate.tryLockMode(
                MGMT_PREFIX.concat(stockoutOrderId),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    log.info("执行 核心逻辑--- {}", new Date());
                    StockoutOrderWithDeliveryQueryCommand queryCommand = new StockoutOrderWithDeliveryQueryCommand();
                    queryCommand.setStockoutOrderId(stockoutOrderId);
                    queryCommand.setStockoutOrderState(StockoutOrderState.STOCKOUT_FINISH.getState());
                    List<StockoutOrderInfo> stockoutOrderInfoList = stockoutOrderRpc.getListWithDelivery(queryCommand);
                    if (CollectionUtils.isNotEmpty(stockoutOrderInfoList)) {
                        int resultSize = stockoutOrderInfoList.size();
                        log.info("获取的已完成状态出库单数量为{}", resultSize);
                        stockoutOrderInfoList.forEach(stockoutOrderInfo -> {
                            try {
                                //用于完结采购系统的采购单
                                completePurchaseOrder(stockoutOrderInfo);
                            } catch (Exception e) {
                                log.error("{}处理已出库单出错{}", stockoutOrderInfo.getStockoutOrderId(), e.getMessage());
                            }
                        });
                    }
                });

        log.info("完成的出库单同步完成采购单 - 结束 --- {}", new Date());
    }

    private void completePurchaseOrder(StockoutOrderInfo stockoutOrderInfo) {
        log.info("现在获取出库单{} 关联的采购单", stockoutOrderInfo.getStockoutOrderId());
        //获取采购单详细信息
        List<PurchaseOrderInfo> purchaseOrderInfo = purchaseRpc.getPurchaseOrderByRefOrderNumber(stockoutOrderInfo.getStockoutOrderId());
        if (CollectionUtils.isNotEmpty(purchaseOrderInfo)) {
            purchaseOrderInfo.forEach(purchaseOrder -> {
                if (purchaseOrder.getOrderStatus() == 50 || purchaseOrder.getOrderStatus() == 55) {
                    //已经完结的采购单
                    return;
                }
                //签收
                EntryScanningCommand scanningCommand = new EntryScanningCommand();
                scanningCommand.setScanArrivalType("trackingNumber");
                scanningCommand.setCode(purchaseOrder.getTrackingNumber());
                scanningCommand.setPurchaseOrderId(purchaseOrder.getId());
                purchaseRpc.scanSignList4ThirdParty(scanningCommand);

                BatchTakeStockCommand takeStockCommand = new BatchTakeStockCommand();
                List<PurchaseOrderGoodsInfo> goodsInfos = new ArrayList<>();
                purchaseOrder.getPurchaseOrderGoodsList().forEach(info -> {
                    //收货
                    info.setReceivingQuantity(info.getPurchaseQuantity());
                    goodsInfos.add(info);
                    takeStockCommand.setOrderGoodsList(goodsInfos);
                });
                purchaseRpc.batchTakeStock4ThirdParty(takeStockCommand);
            });
        }
    }

    public ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }
}
