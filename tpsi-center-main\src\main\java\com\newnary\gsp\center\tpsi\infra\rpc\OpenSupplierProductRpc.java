package com.newnary.gsp.center.tpsi.infra.rpc;

import com.newnary.gsp.center.product.api.open.feign.OpenSupplierProductFeignApi;
import com.newnary.gsp.center.product.api.open.request.*;
import com.newnary.gsp.center.product.api.product.SupplierSpuApi;
import com.newnary.gsp.center.product.api.product.request.SupplierSpuCreateV2Command;
import com.newnary.gsp.center.product.api.product.vo.SupplierSpuDetailInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class OpenSupplierProductRpc {

    @Resource
    private OpenSupplierProductFeignApi openSupplierProductFeignApi;

    @Resource
    private SupplierSpuApi supplierSpuApi;

    public String create(OpenSupplierProductCreateReq req) {
        return openSupplierProductFeignApi.create(req).mustSuccessOrThrowOriginal();
    }

    public String createSpu(SupplierSpuCreateV2Command req) {
        return openSupplierProductFeignApi.createSpu(req).mustSuccessOrThrowOriginal();
    }

    public String createSpu4StockAsync(SupplierSpuCreateV2Command req) {
        return openSupplierProductFeignApi.createSpu4StockAsync(req).mustSuccessOrThrowOriginal();
    }

    public void adjustSupplyPrice(OpenSupplierAdjustSupplyPriceReq req) {
        openSupplierProductFeignApi.adjustSupplyPrice(req).mustSuccessOrThrowOriginal();
    }

    public void updateStock(OpenSupplierUpdateStockReq req) {
        openSupplierProductFeignApi.updateStock(req);
    }

    public void updateStockBatch(OpenSupplierUpdateStock4BatchReq req) {
        openSupplierProductFeignApi.updateStock4Batch(req);
    }

    public void stopSupply(OpenSupplierSkuStopSupplyReq req) {
        openSupplierProductFeignApi.stopSupply(req);
    }

    public void startSupply(OpenSupplierSkuStartSupplyReq req) {
        openSupplierProductFeignApi.startSupply(req);
    }

    public void stopSpuSupply(OpenSupplierSpuOptSupplyStateReq req) {
        openSupplierProductFeignApi.stopSpuSupply(req);
    }

    public void startSpuSupply(OpenSupplierSpuOptSupplyStateReq req) {
        openSupplierProductFeignApi.startSpuSupply(req);
    }

    //获取商品详情
    public SupplierSpuDetailInfo getDetail(String productId){
        return supplierSpuApi.getDetail(productId).mustSuccessOrThrowOriginal();
    }
}
