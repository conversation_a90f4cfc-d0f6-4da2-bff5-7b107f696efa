package com.newnary.gsp.center.tpsi.infra.model.vo;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DeliveryOrderItemEcCangApiAssociation {

    private String associationId;

    /** 销售编码 */
    private String saleItemCode;

    /** 数量 */
    private Integer quantity;

    /** 供应商skuId */
    private String supplierSkuId;

    /** 供应商自定义SKU编号 */
    private String customCode;

    private String supplyPrice;

    /**
     * erp采购订单id
     */
    private String erpPurchaseOrderCode;

    /**
     * erp采购单状态
     */
    private Integer erpPurchaseOrderStatus;

    /**
     * erp采购单跟踪号
     */
    private String erpPurchaseOrderTrackingNo;

    /**
     * erp采购单运费
     */
    private Double erpPurchaseOrderShippingFee;

    /**
     * erp采购单应付费用
     */
    private Double erpPurchaseOrderPayableAmount;

    /**
     * wms集运订单下的产品pid
     */
    private Integer wmsProductPid;

    private String wmsChildCode;

    /**
     * wms集运订单-产品采购包裹号
     */
    private String wmsProductPackageCode;

    private String wmsProductStandard;
    private String wmsProductImgUrl;
    private Integer wmsProductCount;
    private String wmsProductCategory;
    private String wmsProductNameCn;
    private String wmsProductNameEn;
    private Double wmsProductDeclared;

}
