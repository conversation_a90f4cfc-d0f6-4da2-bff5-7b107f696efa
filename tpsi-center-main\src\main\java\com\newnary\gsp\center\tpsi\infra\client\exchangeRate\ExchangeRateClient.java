package com.newnary.gsp.center.tpsi.infra.client.exchangeRate;

import com.newnary.common.utils.httpmethod.ApiBaseResult;
import com.newnary.common.utils.httpmethod.HttpMethodUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ExchangeRateClient {

    public String getExchangeRateBySourceCurrency(String sourceCurrency) {
        try {
            ApiBaseResult apiBaseResult = HttpMethodUtil.syncGetMethod(
                    "https://v6.exchangerate-api.com/v6/************************/latest/",
                    3,
                    sourceCurrency,
                    "application/json", null, null);

            return apiBaseResult.getRet();
        } catch (Exception e) {
            log.error(e.getMessage());
            e.printStackTrace();
        }
        return null;
    }
}
