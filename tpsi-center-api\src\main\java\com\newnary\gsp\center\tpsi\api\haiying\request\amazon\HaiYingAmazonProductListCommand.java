package com.newnary.gsp.center.tpsi.api.haiying.request.amazon;

import com.newnary.api.base.common.PageCondition;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingAmazonProductListOrderBy;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingOrderByType;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingStation;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingAmazonProductListCommand {

    /**
     * 站点
     */
    @NotNull(message = "站点不能为空")
    private HaiYingStation station;

    /**
     * 商品类型(默认全部商品)(int 型)
     * (1: 所有子类排名最高的商品)
     */
    private Integer best_rank_flag;

    /**
     * 商品Id(string型)
     * (多个商品id用英文逗号分隔,单次最多500个商品id)
     */
    private List<String> asins;

    /**
     * 商品标题(string型)
     */
    private String title;

    /**
     * 商品标题查询类型(int 型):
     * 1:整句语句(默认)
     * 2:多个搜索词,”与”关系
     * 3:多个搜索词,”或”关系
     */
    private Integer title_type;

    /**
     * 商品不包含标题(string型)
     */
    private String not_exist_title;

    /**
     * 商品不包含标题查询类型(int 型):
     * 1:整句语句(默认)
     * 2:多个搜索词,”与”关系
     * 3:多个搜索词,”或”关系
     */
    private Integer not_exist_title_type;

    /**
     * 1级类目id(string型)
     * (当仅使用1级类目调用时，可以多个1级类目，多个以逗号分隔)
     */
    private String p_l1_id;

    /**
     * 2级类目id(string型)
     */
    private String p_l2_id;

    /**
     * 3级类目id(string型)
     */
    private String p_l3_id;

    /**
     * 4级类目id(string型)
     */
    private String p_l4_id;

    /**
     * 5级类目id(string型)
     */
    private String p_l5_id;

    /**
     * 6级类目id(string型)
     */
    private String p_l6_id;

    /**
     * 7级类目id(string型)
     */
    private String p_l7_id;

    /**
     * 8级类目id(string型)
     */
    private String p_l8_id;

    /**
     * 9级类目id(string型)
     */
    private String p_l9_id;

    /**
     * 10级类目id(string型)
     */
    private String p_l10_id;

    /**
     * 行业现排名起始值(int 型)
     */
    private Integer rank_begin;

    /**
     * 行业现排名结束值(int 型)
     */
    private Integer rank_end;

    /**
     * 3天前行业排名起始值(int 型)(已取消)
     */
    @Deprecated
    private Integer three_day_rank_begin;

    /**
     * 3天前行业排名结束值(int 型)(已取消)
     */
    @Deprecated
    private Integer three_day_rank_end;

    /**
     * 前3天排名变化起始值(int 型)(已取消)
     */
    @Deprecated
    private Integer three_day_rank_change_start;

    /**
     * 前3天排名变化结束值(int 型)(已取消)
     */
    @Deprecated
    private Integer three_day_rank_change_end;

    /**
     * 前3天排名变化率起始值(int 型)(已取消)
     */
    @Deprecated
    private Integer three_day_rank_change_rate_start;

    /**
     * 前3天排名变化率结束值(int 型)(已取消)
     */
    @Deprecated
    private Integer three_day_rank_change_rate_end;

    /**
     * 前3天新增评论数起始值(int 型)
     */
    private Integer three_day_new_reviews_begin;

    /**
     * 前3天新增评论数结束值(int 型)
     */
    private Integer three_day_new_reviews_end;

    /**
     * 商品价格起始值(double型)
     * (需配合price_status使用,默认:不限制)
     */
    private BigDecimal asin_price_begin;

    /**
     * 商品价格结束值(double型)
     * (需配合price_status使用,默认:不限制)
     */
    private BigDecimal asin_price_end;

    /**
     * 商品总评论数起始值(int 型)
     */
    private Integer customer_reviews_begin;

    /**
     * 商品总评论数结束值(int 型)
     */
    private Integer customer_reviews_end;

    /**
     * 商品评分起始值(double型)
     */
    private BigDecimal score_begin;

    /**
     * 商品评分结束值(double型)
     */
    private BigDecimal score_end;

    /**
     * 商品Q&A总数起始值(int 型)
     */
    private Integer answered_questions_begin;

    /**
     * 商品Q&A总数结束值(int 型)
     */
    private Integer answered_questions_end;

    /**
     * 卖家数量起始值(int 型)
     */
    private Integer follow_sellers_num_begin;

    /**
     * 卖家数量起始值(int 型)
     */
    private Integer follow_sellers_num_end;

    /**
     * 商品重量起始值(double型)
     */
    private BigDecimal shipping_weight_begin;

    /**
     * 商品重量结束值(double型)
     */
    private BigDecimal shipping_weight_end;

    /**
     * 商品最新上架时间起始值(string型 格式:年-月-日)
     */
    private Long fir_arrival_begin;

    /**
     * 商品最新上架时间结束值(string型 格式:年-月-日)
     */
    private Long fir_arrival_end;

    /**
     * 是否Best Seller(默认全部)(int 型)
     * (-1:不确定  0:否   1:是)
     */
    private Boolean best_seller;

    /**
     * buy box所有者(默认全部)(int 型)
     * (0:第三方   1:Amazon自营)
     */
    private Integer buy_box;

    /**
     * 配送方式(默认全部)(int 型)
     * (0:Amazon自营
     * 1:FBA
     * 2:FBM
     * 5:未知)
     */
    private Integer delivery_type;

    /**
     * 支持prime(默认全部)(int 型)
     * (0:否   1:是)
     */
    private Boolean prime;

    /**
     * Amazon’s Choice(默认全部)(int 型)
     * (0:否   1:是)
     */
    private Boolean amazon_choice;

    /**
     * 前9天排名持续上升(int 型)
     * (0:否   1:是)
     */
    private Boolean sign_of_rank_rise;

    /**
     * 最新修改时间起始值
     * (string型 格式:年-月-日时:分:秒)
     */
    private Long last_upd_date_begin;

    /**
     * 最新修改时间结束值
     * (string型 格式:年-月-日时:分:秒)
     */
    private Long last_upd_date_end;

    /**
     * 前9天的排名均值变化起始值(int 型)
     */
    private Integer rank_rise_avg_change_begin;

    /**
     * 前9天的排名均值变化结束值(int 型)
     */
    private Integer rank_rise_avg_change_end;

    /**
     * 商品品牌(string型)
     */
    private String brand;

    /**
     * 店铺名(string型)
     */
    private String merchant;

    /**
     * 商品价格状态值(int 型)
     * (-1:价格未知
     * 其它:价格不限)
     */
    private Integer price_status;

    /**
     * 中国商家(int 型)
     * (默认:不限
     * 0:未发现
     * 1:曾经出现
     * 2:未知)
     */
    private Integer chinese_sellers_in_merhants;

    /**
     * 品牌是否注册
     * (默认:全部
     * 0为未注册
     * 1为注册
     * 2为无品牌
     * 3为未核对)
     */
    private Integer is_registered;

    /**
     * 公司注册地类型
     * (默认:全部
     * 1: 大陆和香港
     * 2: NOMESSAGE无信息
     * 3: NOCHECKED未核对)
     */
    private Integer registration_type;

    /**
     * 店铺code(多个店铺code以逗号分隔)
     */
    private List<String> merchant_codes;

    /**
     * 商品品牌是否注册(wipo)(已取消)
     * 默认全部
     * 0为未注册
     * 1为注册
     * 2为无品牌
     * 3为未核对
     */
    @Deprecated
    private Integer is_registered_wipo;

    /**
     * 商品品牌是否注册(tmview)(已取消)
     * 默认全部
     * 0为未注册
     * 1为注册
     * 2为无品牌
     * 3为未核对
     */
    @Deprecated
    private Integer is_registered_tmview;

    /**
     * 商品父类asin
     */
    private String parent_asin;

    /**
     * 商品日销量起始值(int 型)
     */
    private Integer day_sales_start;

    /**
     * 商品日销量结束值(int 型)
     */
    private Integer day_sales_end;

    /**
     * 商品月销量起始值(int 型)
     */
    private Integer month_sales_start;

    /**
     * 商品月销量结束值(int 型)
     */
    private Integer month_sales_end;

    /**
     * 排序方式:(string型)
     * top_sellers_rank(行业现排名)
     * three_day_rank(3天前行业排名)(已取消)
     * three_day_rank_change(前3天排名变化)(已取消)
     * three_day_rank_change_rate(前3天排名变化率)(已取消)
     * three_day_new_reviews(前3天新增评论数)
     * asin_price_max(最高价格)
     * asin_price_min(最低价格)
     * customer_reviews(总评论数)
     * score(评分)
     * answered_questions(Q&A数)
     * follow_sellers_num(卖家数)
     * shipping_weight(重量)
     * fir_arrival(商家时间)
     * rank_rise_avg_change(前9天的排名均值变化)
     */
    private HaiYingAmazonProductListOrderBy order_by;

    /**
     * 排序类型:
     * ASC 升序
     * DESC 降序
     */
    private HaiYingOrderByType order_by_type;

    /**
     * 每一页的商品数(默认海鹰设置)(int 型)
     * 数值范围[1-1000]
     */
    private PageCondition pageCondition;

}
