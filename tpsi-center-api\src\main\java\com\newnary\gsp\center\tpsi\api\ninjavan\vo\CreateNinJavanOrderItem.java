package com.newnary.gsp.center.tpsi.api.ninjavan.vo;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import javax.validation.constraints.Size;

/**
 * 创建能者物流 item
 *
 * <AUTHOR>
 * @since Created on 2023-08-11
 **/
@Data
public class CreateNinJavanOrderItem {

    /**
     * 描述。
     **/
    @Size(max = 1000)
    private String description;


    /**
     * 商品价值:单件商品价值以海关货币表示。
     **/
    @Size(max = 1000)
    private Float unitValue;

    /**
     * 数量。
     **/
    private Integer quantity;

    /**
     * 商品重量：商品重量（千克）。
     **/
    private Float unitWeight;




}
