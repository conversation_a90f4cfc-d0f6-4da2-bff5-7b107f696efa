package com.newnary.gsp.center.tpsi.api.ninjavan;

import com.newnary.api.base.common.CommonResponse;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @Author: jack
 * @CreateTime: 2023-8-9
 * message: 能者物流
 */
@RequestMapping("tpsi-center/ninjavan")
public interface NinjavanOpenApi {

    @PostMapping("ninjavan-webhooks/{tenantID}")
    CommonResponse<String> ninjavanWebhooks(@PathVariable(value = "tenantID") String tenantID);

}
