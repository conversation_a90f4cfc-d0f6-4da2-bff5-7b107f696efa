package com.newnary.gsp.center.tpsi.ctrl.jt;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.gsp.center.tpsi.api.jt.JTCnApi;
import com.newnary.gsp.center.tpsi.api.jt.request.cn.BillOrderCnJTCommand;
import com.newnary.gsp.center.tpsi.api.jt.request.cn.CancelOrderCnJTCommand;
import com.newnary.gsp.center.tpsi.api.jt.request.cn.CreateOrderCnJTCommand;
import com.newnary.gsp.center.tpsi.api.jt.request.cn.SheetOrderCnJTCommand;
import com.newnary.gsp.center.tpsi.api.jt.response.cn.JTCnCreateResultResp;
import com.newnary.gsp.center.tpsi.api.jt.response.cn.JTCnPrintSheetResp;
import com.newnary.gsp.center.tpsi.api.jt.response.cn.JTCnQueryOrderBillResp;
import com.newnary.gsp.center.tpsi.infra.rpc.RouteInfoRpc;
import com.newnary.gsp.center.tpsi.service.JT.impl.JTCnLogisticsApiSvelmpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Author: jack
 * @CreateTime: 2023-8-9
 */
@RestController
@Slf4j
public class JTCnController implements JTCnApi {

    private static final String PREFIX = "JT_CN_API:";

    @Resource
    private RouteInfoRpc routeInfoRpc;

    @Resource
    private JTCnLogisticsApiSvelmpl jtCnLogisticsApiSvelmpl;

    @Override
    public CommonResponse<JTCnCreateResultResp> create(CreateOrderCnJTCommand command) {
        return CommonResponse.success(jtCnLogisticsApiSvelmpl.createOrder(command));
    }

    @Override
    public CommonResponse<JTCnPrintSheetResp> printSheet(SheetOrderCnJTCommand command) {
        return CommonResponse.success(jtCnLogisticsApiSvelmpl.printSheet(command));
    }


    @Override
    public CommonResponse<Void> cancelOrder(CancelOrderCnJTCommand command) {
        jtCnLogisticsApiSvelmpl.cancelOrder(command);
        return CommonResponse.successWithoutBody();
    }


    @Override
    public CommonResponse<JTCnQueryOrderBillResp> queryBill(BillOrderCnJTCommand command) {
        return CommonResponse.success(jtCnLogisticsApiSvelmpl.queryBill(command));
    }
}
