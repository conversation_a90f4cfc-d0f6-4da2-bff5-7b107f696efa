package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.lazada;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingLazadaSubCategoryInfoRequest {

    /**
     * 站点
     */
    @NotNull(message = "站点不能为空")
    private String station;

    /**
     * 类目名
     * (存在特殊字符,转码UTF-8)
     */
    private String cname;

    /**
     * 当前页码(int 型)
     */
    private String current_page;

    /**
     * 每一页的数据量(默认海鹰设置 全部)(int 型)
     * 数值范围[1-10000]
     */
    private String page_size;
}
