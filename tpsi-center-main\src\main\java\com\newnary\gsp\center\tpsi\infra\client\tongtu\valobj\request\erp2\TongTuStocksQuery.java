package com.newnary.gsp.center.tpsi.infra.client.tongtu.valobj.request.erp2;

import lombok.Data;

import java.util.List;

@Data
public class TongTuStocksQuery {

    /**
     * 商户ID (必填)
     */
    private String merchantId;

    /**
     * 查询页数
     */
    private Integer pageNo;

    /**
     * 每页数量,默认值：100,最大值100，超过最大值以最大值数量返回
     */
    private Integer pageSize;

    /**
     *
     */
    private List<String> skus;

    /**
     * 更新开始时间
     */
    private String updatedDateFrom;

    /**
     * 更新结束时间
     */
    private String updatedDateTo;

    /**
     * 仓库名称 (必填)
     */
    private String warehouseName;
}
