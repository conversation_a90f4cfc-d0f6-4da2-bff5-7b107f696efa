package com.newnary.gsp.center.tpsi.api.logisticsService.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 物流服务打印标签请求体
 *
 * <AUTHOR>
 * @since Created on 2023-11-17
 **/
@Data
public class LogisticsPrintOrderCommand {


    /**
     * 物流返回的追踪号。
     **/
    @NotBlank(message = "物流追踪号不能为空")
    private String trackingId;

    /**
     * 1 : 订单    2 ：包裹
     **/
    @NotBlank(message = "物流追踪号不能为空")
    private Integer templateId = 2;

}
