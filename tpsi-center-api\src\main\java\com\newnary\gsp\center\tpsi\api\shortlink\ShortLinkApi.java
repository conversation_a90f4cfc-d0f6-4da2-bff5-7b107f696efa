package com.newnary.gsp.center.tpsi.api.shortlink;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.gsp.center.tpsi.api.shortlink.request.ShortLinkCommand;
import com.newnary.gsp.center.tpsi.api.shortlink.response.ConvertShortLinkResp;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @Author: <PERSON><PERSON>u<PERSON>ao
 * @CreateTime: 2024-01-18
 * message:
 */
@RequestMapping("tpsi-center/xiaomark")
public interface ShortLinkApi {

    @PostMapping("convertShortLink")
    CommonResponse<ConvertShortLinkResp> convertShortLink(@RequestBody ShortLinkCommand command);

}
