package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj;

import com.newnary.common.utils.xml.JaxbUtil;

import javax.xml.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2022/5/12
 */
@XmlRootElement(name = "SOAP-ENV:Envelope")
public class EcCangErpBaseRequest {
    @XmlAttribute(name = "xmlns:SOAP-ENV")
    public String soapenv = "http://schemas.xmlsoap.org/soap/envelope/";

    @XmlAttribute(name = "xmlns:ns1")
    public String ns1 = "http://www.example.org/Ec/";

    @XmlElement(required = true, name = "SOAP-ENV:Body")
    public RequestBody body;


    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "SOAP-ENV:Body")
    public static class RequestBody {
        @XmlElement(required = true, name = "ns1:callService")
        public CallService callService;
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "ns1:callService")
    public static class CallService {
        @XmlElement(required = true, name = "paramsJson")
        public String paramJson;
        @XmlElement(required = true, name = "userName")
        public String userName;
        @XmlElement(required = true, name = "userPass")
        public String userPass;
        @XmlElement(required = true, name = "service")
        public String service;
    }

    public static void main(String[] args) {
        EcCangErpBaseRequest request = new EcCangErpBaseRequest();
        request.body = new RequestBody();
        request.body.callService = new CallService();
        request.body.callService.userName = "a";
        request.body.callService.userPass = "b";
        request.body.callService.service = "c";
        request.body.callService.paramJson = "e";
        String xml = JaxbUtil.toXml(request);
        System.out.println(xml);
    }
}
