package com.newnary.gsp.center.tpsi.infra.client.mabang.valobj;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/1/5
 */
@Data
public class MaBangOrder {

    private String platformOrderId;
    private String erpOrderId;
    private String orderStatus;
    private String salesRecordNumber;
    private String isNewOrder;
    private String isWms;
    private String isUnion;
    private String isSplit;
    private String isReturned;
    private String isRefund;
    private String isResend;
    private String hasGoods;
    private String hasBattery;
    private String myLogisticsChannelId;
    private String myLogisticsChannelName;
    private String myLogisticsId;
    private String myLogisticsName;
    private String trackNumber;
    private String trackNumber1;
    private String trackNumber2;
    private String orderWeight;
    private String shippingWeight;
    private String buyerUserId;
    private String buyerName;
    private String shopId;
    private String shopName;
    private String companyId;
    private String countryCode;
    private String countryNameEN;
    private String countryNameCN;
    private String orderCost;
    private String paidTime;
    private String createDate;
    private String orderFee;
    private String platformId;
    private String expressTime;
    private String closeDate;
    private String street1;
    private String street2;
    private String city;
    private String province;
    private String postCode;
    private String phone1;
    private String phone2;
    private String email;
    private String doorcode;
    private String fbaFlag;
    private String fbaStartDateTime;
    private String fbaEndDateTime;
    private String CarrierCode;
    private String operTime;
    private String currencyId;
    private String currencyRate;
    private String itemTotal;
    private String itemTotalOrigin;
    private String shippingFee;
    private String shippingTotalOrigin;
    private String platformFee;
    private String platformFeeOrigin;
    private String insuranceFee;
    private String insuranceFeeOrigin;
    private String paypalFee;
    private String paypalFeeOrigin;
    private String itemTotalCost;
    private String shippingCost;
    private String shippingPreCost;
    private String packageFee;
    private String fbaPerOrderFulfillmentFee;
    private String fbaCommission;
    private String refundFeeCurrencyId;
    private String originFax;
    private String promotionAmount;
    private String CODCharge;
    private String allianceFee;
    private String allianceFeeOrigin;
    private String fbaPerUnitFulfillmentFee;
    private String fbaWeightBasedFee;
    private String isSyncLogisticsDescr;
    private String isSyncLogistics;
    private String isSyncPlatform;
    private String isSyncPlatformDescr;
    private String paypalId;
    private String shippingService;
    private String packageWeight;
    private String hasMagnetic;
    private String hasPowder;
    private String hasTort;
    private String remark;
    private String sellerMessage;
    private String paypalEmail;
    private String payType;
    private String voucherPrice;
    private String voucherPriceOrigin;
    private String subsidyAmount;
    private String subsidyAmountOrigin;
    private String abnnumber;
    private String district;
    private String VendorID;
    private String isVirtual;
    private String canSend;
    private String quickPickTime;
    private String transportTime;
    private String retailId;
    private String retailPlatformId;
    private String retailShopId;
    private List<MaBangOrderItem> orderItem;
    private String extendAttr;


}
