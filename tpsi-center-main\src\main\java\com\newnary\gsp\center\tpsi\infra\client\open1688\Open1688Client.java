package com.newnary.gsp.center.tpsi.infra.client.open1688;

import com.alibaba.fastjson.JSON;
import com.newnary.gsp.center.tpsi.infra.client.open1688.params.ClientBaseParams;
import com.newnary.gsp.center.tpsi.infra.client.open1688.util.CommonUtil;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.comon.response.Category1688;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Setter
@Getter
public abstract class  Open1688Client {

    private String urlHead;
    private String appSecretKey;
    private String token;
    private String appKey;

    /**
     * 根据类目Id查询类目
     * com.alibaba.product/alibaba.category.get/
     */
    private String getCategoryById;

    public Open1688Client(String params) {
        ClientBaseParams clientBaseParams = JSON.parseObject(params, ClientBaseParams.class);
        setAppKey(clientBaseParams.getAppKey());
        setAppSecretKey(clientBaseParams.getAppSecretKey());
        setToken(clientBaseParams.getToken());
        setUrlHead(clientBaseParams.getUrlHead());
        setGetCategoryById("param2/1/com.alibaba.product/alibaba.category.get/".concat(clientBaseParams.getAppKey()));
    }

    public String callApi( String urlPath, Map<String, String> params){
        final HttpClient httpClient = new HttpClient();
        final PostMethod method = new PostMethod(urlHead+urlPath);
        method.setRequestHeader("Content-type", "application/x-www-form-urlencoded; charset=UTF-8");
        params.put("_aop_timestamp", String.valueOf(new Date().getTime()));
        params.put("access_token",token);
        params.put("param", JSON.toJSONString(params));

        for (Map.Entry<String, String> entry : params.entrySet()) {
            method.setParameter(entry.getKey(), entry.getValue());
        }

        if(appSecretKey != null){
            method.setParameter("_aop_signature", CommonUtil.signatureWithParamsAndUrlPath(urlPath, params, appSecretKey));
        }
        String response = "";
        try{
            //long l = new Date().getTime();
            int status = httpClient.executeMethod(method);
            if(status >= 300 || status < 200){
                throw new RuntimeException("invoke api failed, urlPath:" + urlPath
                        + " status:" + status + " response:" + method.getResponseBodyAsString());
            }
            response = CommonUtil.parserResponse(method);
            //System.out.println(new Date().getTime()-l);
        } catch (IOException e) {
            System.out.println(e.getMessage());
        } finally{
            method.releaseConnection();
        }
        return response;
    }

    public Category1688 getCategoryById(String categoryId) {
        Map<String, String> params = new HashMap<>();
        params.put("categoryID", categoryId);
        String result = callApi(getCategoryById, params);
        if (StringUtils.isNotEmpty(result)) {
            return JSON.parseObject(result, Category1688.class);
        }
        return null;
    }
}
