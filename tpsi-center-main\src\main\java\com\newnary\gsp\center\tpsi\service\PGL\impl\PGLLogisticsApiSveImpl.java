package com.newnary.gsp.center.tpsi.service.PGL.impl;

import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.logistics.api.LogisticsCenterErrorInfo;
import com.newnary.gsp.center.logistics.api.delivery.dto.TransportConsigneeDTO;
import com.newnary.gsp.center.logistics.api.delivery.response.TransportOrderPackageInfo;
import com.newnary.gsp.center.product.api.product.response.SupplierSkuDetailInfo;
import com.newnary.gsp.center.tpsi.api.logisticsService.request.LogisticsPrintOrderCommand;
import com.newnary.gsp.center.tpsi.api.logisticsService.response.LogisticsOrderResp;
import com.newnary.gsp.center.tpsi.api.logisticsService.response.LogisticsPrintSheetResp;
import com.newnary.gsp.center.tpsi.infra.client.common.CreatePdfUtil;
import com.newnary.gsp.center.tpsi.infra.client.pgl.dto.GeneratePGLPdfParam;
import com.newnary.gsp.center.tpsi.infra.client.pgl.dto.GoodsParam;
import com.newnary.gsp.center.tpsi.infra.client.pgl.utils.GeneratePGLLabelPdfUtil;
import com.newnary.gsp.center.tpsi.infra.model.vo.LogisticsServiceConstants;
import com.newnary.gsp.center.tpsi.infra.model.vo.LogisticsServiceContext;
import com.newnary.gsp.center.tpsi.infra.rpc.ExchangeRateRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.ProductRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.SpaceFileRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.TransportOrderRpc;
import com.newnary.gsp.center.tpsi.service.ILogisticsApiSve;
import com.newnary.gsp.center.tpsi.service.impl.SystemClientSve;
import com.newnary.spring.cloud.extend.Extend;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.math.BigDecimal;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @Author: jack
 * @CreateTime: 2024/1/22
 */

@Extend("PGL")
@Component
@Slf4j
public class PGLLogisticsApiSveImpl extends SystemClientSve implements ILogisticsApiSve {

    @Resource
    private SpaceFileRpc spaceFileRpc;
    @Resource
    private TransportOrderRpc transportOrderRpc;
    @Resource
    private ProductRpc productRpc;
    @Resource
    private ExchangeRateRpc exchangeRateRpc;

    @Override
    public void createOrder() {
        //TODO 无实际第三方对接,暂时直接返回
        LogisticsOrderResp ret = new LogisticsOrderResp();
        TransportOrderPackageInfo transportPackageInfo = (TransportOrderPackageInfo) LogisticsServiceContext.getCurrentContext().get(LogisticsServiceConstants.LOGISTICS_TRANSPORT_DOMAIN);
        ret.setTrackingId(transportPackageInfo.getTransportOrderPackageId());
        LogisticsServiceContext.getCurrentContext().put(LogisticsServiceConstants.CALL_CREATE_RESPONSE, ret);
    }

    @Override
    public void doCancel() {

    }

    @Override
    public void printSheet() {
        GeneratePGLPdfParam param = new GeneratePGLPdfParam();
        LogisticsPrintOrderCommand printOrderCommand = (LogisticsPrintOrderCommand) LogisticsServiceContext.getCurrentContext()
                .get(LogisticsServiceConstants.LOGISTICS_PRINT_DOMAIN);
        TransportOrderPackageInfo transportPackageInfo = transportOrderRpc.loadTransportPackage(printOrderCommand.getTrackingId());
        buildGenerateSheetFileParams(param, transportPackageInfo);

        try {
            buildPrintResponse(param);
        } catch (URISyntaxException e) {
            throw new ServiceException(LogisticsCenterErrorInfo.ERROR_5010_LOGISTICS_SERVICE_API_ERROR);
        }
    }

    @Override
    public void queryTrack() {

    }

    private void buildGenerateSheetFileParams(GeneratePGLPdfParam param, TransportOrderPackageInfo transportOrderPackageInfo) {
        param.setOrderNum(transportOrderPackageInfo.getTransportOrderId());
        param.setLogisticsCode("PK099");    //TODO PGL暂时只用一个物流代码
        param.setPcs("1/1");    //TODO 包裹数暂时写死
        param.setIsCod(transportOrderPackageInfo.getIsCod());
        param.setTransportPrice(transportOrderPackageInfo.getTransportAmount());
        //TODO 发件人信息按需求暂时固定
        param.setSenderName("Vincen");
        param.setSenderPhone("09669905496");
        param.setSenderAddress("unit 2104, Sunprime Tower, 354 Juan Luna Street, Barangay 289, Binondo, Manila");

        TransportConsigneeDTO consigneeDTO = transportOrderPackageInfo.getConsignee();
        if (ObjectUtils.isEmpty(consigneeDTO)) {
            throw new ServiceException(CommonErrorInfo.ERROR_101_QUERY_DATA_ERROR, "找不到收件地址");
        }
        param.setReceiverName(consigneeDTO.getName());
        param.setReceiverPhone(consigneeDTO.getContactNumber());
        param.setReceiverAddress(consigneeDTO.getAddrDetail()
                + ", " + consigneeDTO.getAddrDistrict()
                + ", " + consigneeDTO.getAddrCity()
                + ", " + consigneeDTO.getAddrProvince()
                + ", " + consigneeDTO.getAddrCountry());
        param.setReceiverPostcode(consigneeDTO.getZipCode());

        List<GoodsParam> goodsList = new ArrayList<>();
        transportOrderPackageInfo.getItems().forEach(transportOrderItem -> {
            GoodsParam goodsInfo = new GoodsParam();
            SupplierSkuDetailInfo skuInfo = productRpc.getSupplierSku(transportOrderItem.getSupplierSkuId());
            goodsInfo.setGoodsName(transportOrderItem.getCategoryName());
            goodsInfo.setGoodsQuantity(transportOrderItem.getQuantity());

//            BigDecimal itemValue = transportOrderItem.getSalePrice()
//                    .multiply(new BigDecimal(transportOrderItem.getQuantity()));
//            //币种兑换
//            BigDecimal phpAmount = exchangeRateRpc.currencyConvert(itemValue, transportOrderItem.getSalePriceCurrency(), "PHP");
//            goodsInfo.setPriceValue(phpAmount);
            goodsInfo.setWeightValue(ObjectUtils.isEmpty(skuInfo.getSkuInfo().getGrossWeight()) ? BigDecimal.ZERO :
                    skuInfo.getSkuInfo().getGrossWeight().multiply(new BigDecimal(transportOrderItem.getQuantity())));
            goodsList.add(goodsInfo);
        });
        param.setGoodsList(goodsList);
    }

    private void buildPrintResponse(GeneratePGLPdfParam param) throws URISyntaxException {
        URI uri = this.getClass().getProtectionDomain().getCodeSource().getLocation().toURI();
        String tempPath = ".";
        String sourceDir = "PGL";  //资源文件夹
        String filePath = "";
        if (uri.toString().startsWith("file")) {
            // IDEA运行时，进行资源复制
            filePath = CreatePdfUtil.copyLocalResourcesFileToTemp(sourceDir + File.separator, "*", tempPath + File.separator + sourceDir);
        } else {
            // 获取jar包所在路径
            String jarPath = uri.toString();
            uri = URI.create(jarPath.substring(jarPath.indexOf("file:"),jarPath.indexOf(".jar") + 4));
            // 打成jar包后，进行资源复制
            filePath = CreatePdfUtil.copyJarResourcesFileToTemp(uri, tempPath, "BOOT-INF/classes/" + sourceDir);
        }
        //pdf地址响应
        ByteArrayOutputStream outputStream = GeneratePGLLabelPdfUtil.generatePdf(param, filePath + File.separator);
        String sheetPdfFileUrl = this.generateSheetFile(param.getOrderNum(), outputStream);
        LogisticsPrintSheetResp ret = new LogisticsPrintSheetResp();
        ret.setLabelBase64("");
        ret.setPdfFileUrl(sheetPdfFileUrl);
        log.info("printResp:{}", ret);
        LogisticsServiceContext.getCurrentContext().put(LogisticsServiceConstants.CALL_PRINT_RESPONSE, ret);
    }

    /**
     * base64 转成 pdf
     *
     * @param trackingId
     * @param byteArrayOutputStream
     * @return
     */
    private String generateSheetFile(String trackingId, ByteArrayOutputStream byteArrayOutputStream) {
        try {
            String fileId = spaceFileRpc.createSpaceFile(trackingId, byteArrayOutputStream.toByteArray(), ".pdf");
            Map<String, String> fileIdMap = spaceFileRpc.queryFileId4FileUrlMapByFileId(Collections.singletonList(fileId));
            String sheetPdfFileUrl = fileIdMap.get(fileId);
            return sheetPdfFileUrl;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
