package com.newnary.gsp.center.tpsi.infra.client.vvic.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Maps;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;

public class VVICSignUtil {

    private static final String HMACSHA256_ENCRYPT_NAME = "HmacSHA256";

    private static final String DEFAULT_ENCODING = "UTF-8";

    public static void main(String[] args) {

    }
    //校验siqn
    public static boolean openVerificationSign(String appSecret,String sign,Map<String, String> params) {
        String sortedParams = getSortedParams(params);
        String message = sortedParams + appSecret + Long.valueOf(params.get("timestamp"));
        String s = encryptForString(message, appSecret);
        return s.equals(sign);
    }

    public static String getPostSign(String appId,String appSecret,Long timestamp,Object req) {

        JSONObject postParam = JSON.parseObject(JSON.toJSONString(req));
        postParam.put("app_id", appId);
        postParam.put("timestamp", timestamp);

        String sortParamStr = JSONObject.toJSONString(postParam, SerializerFeature.MapSortField);
        HashMap<String, String> map = Maps.newHashMap();
        map.put("data", sortParamStr);

        String sortedParams = getSortedParams(map);
        String message = sortedParams + appSecret + timestamp;
        return encryptForString(message, appSecret);

    }

    public static String getGetSign(String app_id, String app_secret, long timestamp, Object req) {

        Map<String, String> params = new HashMap<>();
        params.put("app_id", app_id);
        params.put("timestamp", String.valueOf(timestamp));
/*            //通过getDeclaredFields()方法获取对象类中的所有属性（含私有）
            Field[] fields = req.getClass().getDeclaredFields();
            for (Field field : fields) {
                //设置允许通过反射访问私有变量
                field.setAccessible(true);
                //获取字段的值
                String value = null == field.get(req)? "":field.get(req).toString();
                //获取字段属性名称
                String name = field.getName();
                //其他自定义操作
                params.put(name, value);
            }*/

        try {
            Map<String, String> request = BeanUtils.describe(req);
            params.putAll(request);
        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            e.printStackTrace();
        }
        String sortedParams = getSortedParams(params);
        String message = sortedParams + app_secret + timestamp;
        return encryptForString(message, app_secret);
    }

    private static Mac getHMACSHA256Digest() {
        try {
            return Mac.getInstance(HMACSHA256_ENCRYPT_NAME);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            throw new RuntimeException("不支持该加密算法");
        }
    }

    private static SecretKeySpec getSecretKey(byte[] secretBytes) {
        return new SecretKeySpec(secretBytes, HMACSHA256_ENCRYPT_NAME);
    }

    private static SecretKeySpec getSecretKey(String secret, String encoding) {
        try {
            byte[] secretBytes = secret.getBytes(encoding);
            return getSecretKey(secretBytes);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String encryptForString(String message, String secret, String encoding) {
        try {
            Mac digest = getHMACSHA256Digest();
            SecretKeySpec secretKey = getSecretKey(secret, encoding);
            digest.init(secretKey);
            byte[] encrypt = digest.doFinal(message.getBytes(encoding));
            if (encrypt == null) {
                return null;
            }
            StringBuilder result = new StringBuilder();
            for (byte oneByte : encrypt) {
                if (Integer.toHexString(0xFF & oneByte).length() == 1) {
                    result.append("0").append(Integer.toHexString(0xFF & oneByte));
                } else {
                    result.append(Integer.toHexString(0xFF & oneByte));
                }
            }
            return result.toString().toLowerCase();
        } catch (UnsupportedEncodingException | InvalidKeyException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String encryptForString(String message, String secret) {
        return encryptForString(message, secret, DEFAULT_ENCODING);
    }

    private static String getSortedParams(Map<String, String> params) {
        String[] keys = params.keySet().toArray(new String[0]);
        Arrays.sort(keys);
        StringBuilder query = new StringBuilder();
        for (String key : keys) {
            String value = params.get(key);
            if (StringUtils.isNoneEmpty(key, value)) {
                query.append(key).append(value);
            }
        }
        return query.toString();
    }

}