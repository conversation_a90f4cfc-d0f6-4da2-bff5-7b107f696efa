package com.newnary.gsp.center.tpsi.jt;

import com.alibaba.fastjson.JSONObject;
import com.newnary.gsp.center.logistics.api.delivery.response.TransportOrderPackageInfo;
import com.newnary.gsp.center.product.api.category.response.CategoryInfo;
import com.newnary.gsp.center.tpsi.api.logisticsService.request.LogisticsPrintOrderCommand;
import com.newnary.gsp.center.tpsi.api.logisticsService.request.LogisticsTrackOrderCommand;
import com.newnary.gsp.center.tpsi.api.logisticsService.response.LogisticsPrintSheetResp;
import com.newnary.gsp.center.tpsi.app.job.JTTMSJobManager;
import com.newnary.gsp.center.tpsi.app.listener.mq.TransportOrder_ThirdPartyCancelProcessor;
import com.newnary.gsp.center.tpsi.app.listener.mq.TransportOrder_ThirdPartyLabelProcessor;
import com.newnary.gsp.center.tpsi.app.listener.mq.TransportOrder_ThirdPartyPushProcessor;
import com.newnary.gsp.center.tpsi.app.service.transport.TransportOrderCommandApp;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartyAddressMapping;
import com.newnary.gsp.center.tpsi.infra.model.creator.ThirdPartyAddressMappingCreator;
import com.newnary.gsp.center.tpsi.infra.model.vo.LogisticsServiceConstants;
import com.newnary.gsp.center.tpsi.infra.model.vo.LogisticsServiceContext;
import com.newnary.gsp.center.tpsi.infra.model.vo.SystemId;
import com.newnary.gsp.center.tpsi.infra.repository.ThirdPartyAddressMappingRepository;
import com.newnary.gsp.center.tpsi.infra.rpc.CategoryRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.TransportOrderRpc;
import com.newnary.gsp.center.tpsi.service.JT.impl.JTLogisticsApiSvelmpl;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.messagebody.gsp.logistics.mo.TransportOrderThirdPartyMO;
import com.newnary.mq.starter.consumer.MQMessage;
import com.newnary.test.starter.BaseTestInjectTenant;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.junit.Test;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Iterator;

/**
 * 极兔物流对接
 */
public class JTApiTest extends BaseTestInjectTenant {

    private static final SimpleDateFormat SIMPLE_TIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    protected String tenantId() {
        return "TENANT3924117688555169382400";
    }

    @Resource
    private JTLogisticsApiSvelmpl jtLogisticsApiSvelmpl;
    @Resource
    private JTTMSJobManager jttmsJobManager;

    @Resource
    private TransportOrder_ThirdPartyPushProcessor pushProcessor;
    @Resource
    private TransportOrder_ThirdPartyCancelProcessor cancelProcessor;
    @Resource
    private TransportOrder_ThirdPartyLabelProcessor labelProcessor;
    @Resource
    private CategoryRpc categoryRpc;
    @Resource
    private ThirdPartyAddressMappingRepository thirdPartyAddressMappingRepository;
    @Resource
    private TransportOrderRpc transportOrderRpc;
    @Resource
    private TransportOrderCommandApp transportOrderCommandApp;


    @Test
    public void test_printSheet() {
        LogisticsPrintOrderCommand printSheetReq = new LogisticsPrintOrderCommand();
        printSheetReq.setTrackingId("910182439753");
        LogisticsServiceContext.getCurrentContext().put(LogisticsServiceConstants.LOGISTICS_PRINT_DOMAIN, printSheetReq);
        jtLogisticsApiSvelmpl.printSheet();
        LogisticsPrintSheetResp printSheetResp = (LogisticsPrintSheetResp) LogisticsServiceContext.getCurrentContext().get(LogisticsServiceConstants.CALL_PRINT_RESPONSE);
        System.out.println(JSONObject.toJSONString(printSheetResp));
    }
    @Test
    public void test_queryTransport() {
        TransportOrderPackageInfo info = transportOrderRpc.loadTransportPackage("TO151706634616913");
        System.out.println(JSONObject.toJSONString(info));

    }


    @Test
    public void test() {
        JSONObject jsonObject = JSONObject.parseObject("{\"data\":{\"requested_tracking_id\":\"\",\"tracking_id\":\"PRO231143094472434405376\",\"delivery\":{\"delivery_start_date\":\"2023-08-23\",\"allow_self_collection\":false}}}");
        System.out.println(jsonObject);
    }


    @Test
    public void test_processorPush() {
        MQMessage<TransportOrderThirdPartyMO> objectMQMessage = new MQMessage<>();
        TransportOrderThirdPartyMO transportOrderThirdPartyMO = new TransportOrderThirdPartyMO();
        transportOrderThirdPartyMO.setTransportOrderPackageId("PN166263866327123");
        transportOrderThirdPartyMO.setThirdPartyCode("JT");
        objectMQMessage.setContent(transportOrderThirdPartyMO);
        pushProcessor.doProcess(objectMQMessage);
    }

    @Test
    public void test_processorCancel() {
        MQMessage<TransportOrderThirdPartyMO> objectMQMessage = new MQMessage<>();
        TransportOrderThirdPartyMO transportOrderThirdPartyMO = new TransportOrderThirdPartyMO();
        transportOrderThirdPartyMO.setTransportOrderId("TO152311105126445");
        transportOrderThirdPartyMO.setThirdOrderId("910180112517");
        transportOrderThirdPartyMO.setThirdPartyCode("JT");
        transportOrderThirdPartyMO.setPushOrderId("TO152311105126445-1");
        transportOrderThirdPartyMO.setCancelReason("测试");
        objectMQMessage.setContent(transportOrderThirdPartyMO);
        cancelProcessor.doProcess(objectMQMessage);
    }

    @Test
    public void test_processorPrintSheet() {
        MQMessage<TransportOrderThirdPartyMO> objectMQMessage = new MQMessage<>();
        TransportOrderThirdPartyMO transportOrderThirdPartyMO = new TransportOrderThirdPartyMO();
        transportOrderThirdPartyMO.setTransportOrderPackageId("PN166263866327123");
        transportOrderThirdPartyMO.setThirdOrderId("910208287359");
        transportOrderThirdPartyMO.setThirdPartyCode("JT");
        objectMQMessage.setContent(transportOrderThirdPartyMO);
        labelProcessor.doProcess(objectMQMessage);
//        transportOrderCommandApp.thirdPrintLabel("PN166263866327123", "910208287359", "JT");
    }

    @Test
    public void test_categoryRpc() {
        CategoryInfo categoryById = categoryRpc.getCategoryById("124734050");
        System.out.println(JSONObject.toJSONString(categoryById));
    }


    @Test
    public void test_queryTrack() {
        LogisticsTrackOrderCommand command = new LogisticsTrackOrderCommand();
        command.setTrackingId("910176596575,941401807769");
        command.setLanguage("en");
        LogisticsServiceContext.getCurrentContext().put(LogisticsServiceConstants.LOGISTICS_TRACK_DOMAIN, command);
        jtLogisticsApiSvelmpl.queryTrack();
    }

    @Test
    public void test_ManagerJob() {
        ReturnT<String> stringReturnT = jttmsJobManager.queryTrack("{\n" +
                "\"thirdPartySystemId\":\"TEST_JT\"\n" +
                "}");

    }

    @Test
    public void test_addressImport() throws IOException {

        File file = new File("/Users/<USER>/Downloads/补充邮编.xlsx");
        int titleRow = 1;
        try {
            Workbook wb = WorkbookFactory.create(file);
            Sheet sheet = wb.getSheet("Sheet1");
            if (sheet == null) {
                throw new RuntimeException("Sheet1无数据");
            }

            Iterator<Row> iterator = sheet.iterator();

            while (iterator.hasNext()){
                Row row = iterator.next();
                if (null == row) {
                    return;
                }
                String province = getCall(row, 2);
                String city = getCall(row, 3);
                String area = getCall(row, 4);
                String virtualPostcode = getCall(row, 5);
                String postCode = getCall(row, 7);
                if (StringUtils.isBlank(virtualPostcode))continue;
//                String postCode = row.getCell(7).getStringCellValue();
                System.out.println(row.getRowNum());

                /**step ·2  同步落库*/
                ThirdPartyAddressMappingCreator creator = new ThirdPartyAddressMappingCreator();
                creator.setSystemId(new SystemId("S5701146366596120576084"));
                creator.setVirtualPostcode(virtualPostcode);
                creator.setPostCode( String.valueOf( postCode));
                creator.setCountry("PH");
                creator.setProvince(province);
                creator.setCity(city);
                creator.setArea(area);
                ThirdPartyAddressMapping with = ThirdPartyAddressMapping.createWith(creator);
                thirdPartyAddressMappingRepository.store(with);
            }

        } catch (InvalidFormatException | IOException e) {
            e.printStackTrace();
        }


    }

    private String getCall(Row row, int i) {
        String cellValue = "";
        // 以下是判断数据的类型
        Cell cell = row.getCell(i);
        switch (cell.getCellTypeEnum()) {
            case NUMERIC: // 数字
                if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    cellValue = sdf.format(org.apache.poi.ss.usermodel.DateUtil.getJavaDate(cell.getNumericCellValue())).toString();
                } else {
                    DataFormatter dataFormatter = new DataFormatter();
                    cellValue = dataFormatter.formatCellValue(cell);
                }
                break;
            case STRING: // 字符串
                cellValue = cell.getStringCellValue();
                break;
            case BOOLEAN: // Boolean
                cellValue = Integer.valueOf(String.valueOf(cell.getBooleanCellValue())) + "";
                break;
            case FORMULA: // 公式
                cellValue = cell.getCellFormula() + "";
                break;
            case BLANK: // 空值
                cellValue = "";
                break;
            case ERROR: // 故障
                cellValue = "非法字符";
                break;
            default:
                cellValue = "未知类型";
                break;
        }
        return cellValue;
    }





}
