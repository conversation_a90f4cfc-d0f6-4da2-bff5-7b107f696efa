package com.newnary.gsp.center.tpsi.service.vvic;

import com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.request.*;
import com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.response.*;

import java.util.List;

public interface VVICApiSev {
    /**
     * 查询商品列表
     */

    VVICGetItemListResponse getItemList(String thirdPartySystemId,VVICGetItemListReq req);

    /**
     * 查询商品明细
     */
    VVICGetItemDetialResponse getItemDetial(String thirdPartySystemId,VVICGetItemDetialReq req);

    /**
     * 查询商品缺货状态
     */
    VVICGetItemStatusResponse getItemStatus(String thirdPartySystemId,VVICGetItemStatusReq req);

    /**
     * 创建订单
     */
    VVICCreateOrderResponse createOrder(String thirdPartySystemId,VVICCreateOrderReq req);

    /**
     * 取消订单
     */
    VVICCancelOrderResponse cancelOrder(String thirdPartySystemId,VVICCancelOrderReq req);

    /**
     * 获取订单状态
     */
    VVICGetOrderStatusResponse getOrderStatus(String thirdPartySystemId,VVICGetOrderStatusReq req);

    /**
     * 获取订单列表
     */
    VVICGetOrderListResponse getOrderList(String thirdPartySystemId,VVICGetOrderListReq req);

    /**
     * 查询代发服务
     */
    VVICGetVasResponse getVas(String thirdPartySystemId,VVICGetVasReq req);

    /**
     * 快递清单查询
     */
    VVICGetExpressResponse getExpressList(String thirdPartySystemId, VVICGetExpressReq req);

    /**
     * 获取档口商品
     */
    VVICGetShopItemListResponse getShopItemList(String thirdPartySystemId, VVICGetShopItemListReq req);
}
