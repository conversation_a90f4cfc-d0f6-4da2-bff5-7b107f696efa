package com.newnary.gsp.center.tpsi.app.listener.mq.domain;

import com.newnary.distributed.tools.concurrent.template.DConcurrentTemplate;
import com.newnary.gsp.center.tpsi.infra.model.CrawlerProduct;
import com.newnary.gsp.center.tpsi.infra.model.event.CrawlerProduct_WarehousingEvent;
import com.newnary.gsp.center.tpsi.infra.repository.ICrawProductRepository;
import com.newnary.spring.cloud.domain.DomainEventListener;
import com.newnary.spring.cloud.domain.DomainEventListenerMode;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since Created on 2023-03-17
 **/
@Component
public class CrawlerProduct_WarehousingEventListener implements DomainEventListener<CrawlerProduct_WarehousingEvent> {

    @Resource
    private ICrawProductRepository crawProductRepository;

    @Override
    public void onListen(CrawlerProduct_WarehousingEvent crawlerProduct_warehousingEvent) {
        CrawlerProduct source = crawlerProduct_warehousingEvent.source();
        // 入库成功后修改商品状态

        DConcurrentTemplate.tryLockMode(
                CrawlerProduct.CRAWLER_PRODUCT_LOCK_PREFIX.concat(source.getProductId()),
                lock -> lock.tryLock(1, TimeUnit.SECONDS),
                () -> {
                    crawProductRepository.update(source);
                }
        );

    }

    @Override
    public Class<CrawlerProduct_WarehousingEvent> eventClz() {
        return CrawlerProduct_WarehousingEvent.class;
    }

//    @Override
//    public DomainEventListenerMode mode() {
//        return DomainEventListenerMode.ASYNCHRONOUS;
//    }
}
