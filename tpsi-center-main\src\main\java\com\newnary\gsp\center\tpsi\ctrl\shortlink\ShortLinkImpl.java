package com.newnary.gsp.center.tpsi.ctrl.shortlink;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.distributed.tools.concurrent.template.DConcurrentTemplate;
import com.newnary.gsp.center.tpsi.api.shortlink.ShortLinkApi;
import com.newnary.gsp.center.tpsi.api.shortlink.request.ShortLinkCommand;
import com.newnary.gsp.center.tpsi.api.shortlink.response.ConvertShortLinkResp;
import com.newnary.gsp.center.tpsi.infra.model.vo.ShortLinkServiceConstants;
import com.newnary.gsp.center.tpsi.infra.model.vo.ShortLinkServiceContext;
import com.newnary.gsp.center.tpsi.service.shortlink.IShortLinkiSve;
import com.newnary.spring.cloud.extend.SpringExtendLoader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

/**
 * @Author: jack
 * @CreateTime: 2023-8-9
 */
@RestController
@Slf4j
public class ShortLinkImpl implements ShortLinkApi {

    private static final String PREFIX = "XiaoMark_API";

    @Value("${shortlink.sysid}")
    private String sysCode;

    @Override
    public CommonResponse<ConvertShortLinkResp> convertShortLink(ShortLinkCommand command) {
        DConcurrentTemplate.tryLockMode(
                PREFIX.concat(command.getUrl()),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    IShortLinkiSve shortLinkiSve = SpringExtendLoader.getExtensionLoader(IShortLinkiSve.class).getExtension(sysCode);
                    ShortLinkServiceContext.getCurrentContext().put(ShortLinkServiceConstants.SHORTLINK_CONVERT_DOMAIN, command);
                    shortLinkiSve.convertShortLink();
                });
        ConvertShortLinkResp convertShortLinkResp = (ConvertShortLinkResp) ShortLinkServiceContext
                .getCurrentContext()
                .get(ShortLinkServiceConstants.SHORTLINK_CONVERT_RESULT_DOMAIN);
        return CommonResponse.success(convertShortLinkResp);
    }
}
