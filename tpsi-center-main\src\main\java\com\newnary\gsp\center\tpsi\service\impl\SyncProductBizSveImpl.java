package com.newnary.gsp.center.tpsi.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.newnary.api.base.common.PageCondition;
import com.newnary.api.base.common.PageList;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.common.utils.locale.LanguageLocaleType;
import com.newnary.gsp.center.logistics.api.warehouse.response.WarehouseLiteRes;
import com.newnary.gsp.center.product.api.common.dto.MultimediaInfo;
import com.newnary.gsp.center.product.api.common.dto.MultimediaType;
import com.newnary.gsp.center.product.api.open.request.*;
import com.newnary.gsp.center.product.api.product.request.SupplierSpuCreateV2Command;
import com.newnary.gsp.center.product.api.product.request.SupplierSpuLogisticsAttrInfo;
import com.newnary.gsp.center.product.api.product.vo.SupplierSpuDescInfo;
import com.newnary.gsp.center.purchase.api.product.response.SpuInfo;
import com.newnary.gsp.center.tpsi.api.mabang.request.SyncProductFromMaBangCommand;
import com.newnary.gsp.center.tpsi.api.mabang.request.SyncProductPriceFromMaBangCommand;
import com.newnary.gsp.center.tpsi.infra.client.mabang.MaBangParams;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.MaBangStockSku;
import com.newnary.gsp.center.tpsi.infra.client.tongtu.TongTuApiClient;
import com.newnary.gsp.center.tpsi.infra.client.tongtu.TongTuParams;
import com.newnary.gsp.center.tpsi.infra.client.tongtu.valobj.TongTuApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.tongtu.valobj.TongTuGoods;
import com.newnary.gsp.center.tpsi.infra.client.tongtu.valobj.TongTuGoodsDetail;
import com.newnary.gsp.center.tpsi.infra.client.tongtu.valobj.TongTuGoodsImage;
import com.newnary.gsp.center.tpsi.infra.model.ApiDockingResult;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.creator.ApiDockingResultCreator;
import com.newnary.gsp.center.tpsi.infra.model.vo.ApiDockingResultType;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingInfo;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingType;
import com.newnary.gsp.center.tpsi.infra.repository.IApiDockingResultRepository;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.ThirdPartyMappingManager;
import com.newnary.gsp.center.tpsi.infra.rpc.OpenSupplierProductRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.WarehouseRpc;
import com.newnary.gsp.center.tpsi.service.IMaBangApiDataTranslateSve;
import com.newnary.gsp.center.tpsi.service.ISyncProductBizSve;
import com.newnary.gsp.center.tpsi.service.mabang.IMaBangStockApiSve;
import com.newnary.gsp.center.tpsi.service.tongtu.ITongTuListingSve;
import com.newnary.gsp.transfer.sunway.api.goods.feign.SwGoodsFeignApi;
import com.newnary.gsp.transfer.sunway.api.goods.response.SwGoodsInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SyncProductBizSveImpl extends SystemClientSve implements ISyncProductBizSve {

    @Resource
    private OpenSupplierProductRpc openSupplierProductRpc;

    @Resource
    private SwGoodsFeignApi swGoodsFeignApi;

    @Resource
    private ITongTuListingSve tongTuListingSve;

    @Resource
    private IMaBangStockApiSve maBangStockApiSve;

    @Resource
    private IApiDockingResultRepository apiDockingResultRepository;

    @Resource
    private IMaBangApiDataTranslateSve maBangApiDataTranslateSve;

    @Resource
    private WarehouseRpc warehouseRpc;

    @Resource
    private ThirdPartyMappingManager thirdPartyMappingManager;

    private static final String PRODUCT_TYPE_NORMAL = "0";
    private static final String PRODUCT_TYPE_VAR_ARGS = "1";

    @Override
    public void syncTongTuProduct(String thirdPartySystemId) {
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);

        //获取apiClient
        TongTuApiClient tongTuApiClient = getClient(thirdPartySystem.getParams());

        //先同步一遍普通销售类型的商品，再同步变参销售的商品
        String[] productTypes = {PRODUCT_TYPE_NORMAL, PRODUCT_TYPE_VAR_ARGS};
        Arrays.asList(productTypes).forEach(productType -> {
            //全部结果
            int currentPageSize;
            int pageSize = 100;
            int pageNo = 1;

            do {
                Map<String, Object> bodyParas = new HashMap<>();
                bodyParas.put("productType", productType);
                bodyParas.put("pageNo", pageNo);
                //循环获取分页
                TongTuApiBaseResult<String> ret = tongTuApiClient.goodsQuery(bodyParas);
                JSONArray productList = JSON.parseObject(ret.getDatas()).getJSONArray("array");
                currentPageSize = productList.size();
                // 三、遍历参数并发送商品创建的mq消息(消费者中进行存库和调用供应链创建商品接口)
                productList.forEach(item -> {
                    //加入获取描述的请求，把描述数据加上后在转成string进行传递
                    tongTuListingSve.doGetListingSaleProduct(tongTuApiClient, item);

                    this.broadcast(thirdPartySystemId, "PRODUCT_SYNC", item.toString());
                });
                pageNo++;
            } while (currentPageSize == pageSize);
        });
    }

    @Override
    public void doTongTuProductSync(ThirdPartySystem thirdPartySystem, String data) {
        TongTuGoods tongTuGoods = JSON.parseObject(data, TongTuGoods.class);
        OpenSupplierProductCreateReq req = buildErpProductCreateRequest(thirdPartySystem, tongTuGoods);
        openSupplierProductRpc.create(req);
        //库存同步
        //tongTuErp2Sve.stocksQuery(thirdPartySystem.getSystemId(), tongTuGoods.getSku());
    }

    private OpenSupplierProductCreateReq buildErpProductCreateRequest(ThirdPartySystem system, TongTuGoods tongTuGoods) {
        TongTuParams params = JSON.parseObject(system.getParams(), TongTuParams.class);
        OpenSupplierProductCreateReq req = new OpenSupplierProductCreateReq();
        req.supplierId = params.getSupplierId();
        req.title = tongTuGoods.getProductName();
        req.zhTitle = tongTuGoods.getProductName();
        req.customSpuCode = tongTuGoods.getSku();
        req.customCategoryName = tongTuGoods.getCategoryName();
        req.mainImageUrls = tongTuGoods.getProductImgList().stream().map(TongTuGoodsImage::getImageGroupId).collect(Collectors.toList());
        req.skuList = tongTuGoods.getGoodsDetail().stream().map(this::buildErpProductSku).collect(Collectors.toList());
        return req;
    }

    private OpenSupplierSkuCreateInfo buildErpProductSku(TongTuGoodsDetail goodsDetail) {
        SwGoodsInfo swGoodsInfo = querySunWayGoodsInfo(goodsDetail.getGoodsSku());
        OpenSupplierSkuCreateInfo sku = new OpenSupplierSkuCreateInfo();
        sku.customSkuCode = goodsDetail.getGoodsSku();
        if (StringUtils.isNotBlank(swGoodsInfo.getAttributeName()) && StringUtils.isNotBlank(swGoodsInfo.getAttributeValue())) {
            OpenSupplierSkuSpecInfo spec = new OpenSupplierSkuSpecInfo();
            spec.specName = swGoodsInfo.getAttributeName();
            spec.specValue = swGoodsInfo.getAttributeValue();
            sku.specs = Lists.newArrayList(spec);
        }
        sku.measuringUnit = swGoodsInfo.getUnit();
        sku.sizeLength = swGoodsInfo.getLength();
        sku.sizeWidth = swGoodsInfo.getWidth();
        sku.sizeHeight = swGoodsInfo.getHeight();
        sku.grossWeight = swGoodsInfo.getWeight();
        sku.netWeight = swGoodsInfo.getWeight();
        sku.retailPriceCurrency = swGoodsInfo.getCurrency();
        return sku;
    }

    private SwGoodsInfo querySunWayGoodsInfo(String sku) {
        return swGoodsFeignApi.queryBySku(sku).mustSuccessOrThrowOriginal();
    }

    @Override
    public void syncProductFromMaBang(SyncProductFromMaBangCommand req) {
        //1、先用查询主商品接口查spu，筛选服饰类类目，然后再用查询商品接口查spu下的stockSku列表，进行创建商品。
        maBangStockApiSve.syncSalesSku(req);
        //2、用查询商品接口查询查询stockSku,筛选服饰类类目以及无sales-sku的产品去供应链分销erp进行创建商品.
        //PS: 如果所有库存sku都已经有spu，则不需要执行这一步
        //maBangStockApiSve.syncStockSku(req);

    }

    @Override
    public void doOpenSupplierProductCreateReq(String reqStr, String valueKey, String valueJson, String type) {
        log.info("MQ处理{}类型sku：{}创建开始", type, valueKey);
        try {
            //OpenSupplierProductCreateReq req = JSONObject.parseObject(reqStr, OpenSupplierProductCreateReq.class);
            SupplierSpuCreateV2Command req = JSONObject.parseObject(reqStr, SupplierSpuCreateV2Command.class);
            ThirdPartyMappingInfo newestInfoByTarget = thirdPartyMappingManager.getNewestInfoByTarget("MABANG", "GSP", req.getCustomCode(), ThirdPartyMappingType.PRODUCT_ID);
            if (newestInfoByTarget != null) {
                return;
            }
            String spuId = openSupplierProductRpc.createSpu4StockAsync(req);
            if (StringUtils.isNotBlank(spuId)) {
                thirdPartyMappingManager.insertOrUpdate("GSP", "MABANG", spuId, req.getCustomCode(), ThirdPartyMappingType.PRODUCT_ID.name(), req);
                saveApiDockingResult(valueJson, valueKey, "saved", type);
            }
        } catch (ServiceException se) {
            se.printStackTrace();
            if (se.getMessage().equals("同商家编码的商品已存在")) {
                saveApiDockingResult(valueJson, valueKey, "saved", type);
            } else {
                log.info(se.getMessage());
                saveApiDockingResult(valueJson, valueKey, se.getMessage(), type);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info(e.getMessage());
            saveApiDockingResult(valueJson, valueKey, e.getMessage(), type);
        }
        log.info("MQ处理{}类型sku：{}创建结束", type, valueKey);
    }

    private void saveApiDockingResult(String valueJson, String valueKey, String dataStatus, String type) {
        //出错应该更新data_status
        ApiDockingResultCreator apiDockingResultCreator = new ApiDockingResultCreator();
        apiDockingResultCreator.setValueKey(valueKey);
        apiDockingResultCreator.setValueJson(valueJson);
        apiDockingResultCreator.setValueType(type);
        apiDockingResultCreator.setDataStatus(StringUtils.isEmpty(dataStatus) ? "nullMessage" : dataStatus);
        Optional<ApiDockingResult> existResult = apiDockingResultRepository.loadByKeyAndType(apiDockingResultCreator.getValueKey(), apiDockingResultCreator.getValueType());
        if (existResult.isPresent()) {
            ApiDockingResult apiDockingResult = existResult.get();
            apiDockingResult.setValueKey(apiDockingResultCreator.getValueKey());
            apiDockingResult.setValueType(ApiDockingResultType.valueOf(apiDockingResultCreator.getValueType()));
            apiDockingResult.setValueJson(apiDockingResult.getValueJson());
            apiDockingResult.setDataStatus(apiDockingResultCreator.getDataStatus());
            apiDockingResultRepository.store(apiDockingResult);
        } else {
            ApiDockingResult apiDockingResult = ApiDockingResult.createWith(apiDockingResultCreator);
            apiDockingResultRepository.store(apiDockingResult);
        }
    }

    @Override
    public void syncProductPriceFromMaBang(SyncProductPriceFromMaBangCommand req) {
        log.info("查询秘钥参数信息");
        ThirdPartySystem thirdPartySystem = loadSystem(req.getThirdPartySystemId());
        MaBangParams params = JSON.parseObject(thirdPartySystem.getParams(), MaBangParams.class);
        //获取"默认仓库"
        WarehouseLiteRes warehouseLiteRes = warehouseRpc.getLiteByCustomCode(params.getSupplierId(), "默认仓库");

        if (StringUtils.isNotEmpty(req.getAppointSku())) {
            log.info("指定sku:{}", req.getAppointSku());
            Optional<ApiDockingResult> appoint = apiDockingResultRepository.loadByKeyAndType(req.getAppointSku(), "MaBangStockSku");
            if (appoint.isPresent()) {
                priceHandle(appoint.get(), params, warehouseLiteRes);
            }
        } else {
            log.info("分页获取sku");
            PageList<ApiDockingResult> pageList = new PageList<ApiDockingResult>();
            PageCondition pageCondition = new PageCondition();
            pageCondition.setPageSize(50);
            int pageNum = 1;
            do {
                pageCondition.setPageNum(pageNum);
                pageList = apiDockingResultRepository.pageQueryByTypeAndStatus("MaBangStockSku", "child-saved", pageCondition);
                log.info("第{}页,有{}条", pageNum, pageList.getItems().size());
                pageList.getItems().forEach(apiDockingResult -> {
                    //核心处理
                    priceHandle(apiDockingResult, params, warehouseLiteRes);
                });

                pageNum++;
            } while (pageList.getItems().size() == 50);
        }

        log.info("同步马帮商品价格处理完毕");
    }

    private void priceHandle(ApiDockingResult apiDockingResult, MaBangParams params, WarehouseLiteRes warehouseLiteRes) {
        //MaBangStockSku maBangStockSku = maBangStockApiSve.stockDoSearchSkuList(req.thirdPartySystemId, sku);
        MaBangStockSku maBangStockSku = JSON.parseObject(apiDockingResult.getValueJson(), MaBangStockSku.class);
        //第三步把马帮ERP上的库存信息同步到供应链分销ERP
        log.info("开始获取库存sku{}的仓库价格信息", apiDockingResult.getValueKey());
        if (maBangStockSku != null) {
            maBangStockSku.getWarehouse().forEach(warehouseInfo -> {
                //暂时只是获取默认仓库
                if (warehouseInfo.getWarehouseName().equals("默认仓库")) {
                    OpenSupplierAdjustSupplyPriceReq openSupplierAdjustSupplyPriceReq = maBangApiDataTranslateSve.buildOpenSupplierAdjustSupplyPriceReq(params.getSupplierId(), warehouseInfo, apiDockingResult.getValueKey(), warehouseLiteRes.getCountry());
                    log.info("库存sku{}-默认仓库的价格为{}", apiDockingResult.getValueKey(), warehouseInfo.getStockCost());
                    try {
                        openSupplierProductRpc.adjustSupplyPrice(openSupplierAdjustSupplyPriceReq);
                        log.info("库存sku{}-默认仓库更新价格成功", apiDockingResult.getValueKey());
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("库存sku{}-默认仓库更新价格失败,原因：{}", apiDockingResult.getValueKey(), e.getMessage());
                    }

                } else {
                    log.info("库存sku{}-仓库{}非默认仓库，不处理", apiDockingResult.getValueKey(), warehouseInfo.getWarehouseName());
                }
            });

        }
    }

    private TongTuApiClient getClient(String tongTuParams) {
        return new TongTuApiClient(tongTuParams);
    }

}
