package com.newnary.gsp.center.tpsi.api.jt.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 创建能者物流 item
 *
 * <AUTHOR>
 * @since Created on 2023-08-11
 **/
@Data
public class CreateJTOrderItems {

    /**
     * 物品名称。
     **/
    @Size(max = 500,message = "itemname(最大500个字符)")
    @NotBlank(message = "itemname(不能为空)")
    private String itemname;

    /**
     * 件数
     **/
    @Size(max = 10,message = "number(最大10个字符)")
    @NotBlank(message = "number(不能为空)")
    private String number;

    /**
     * 物品价值(菲律宾货币:PHP)
     **/
    @Size(max = 20,message = "number(最大20个字符)")
    @NotBlank(message = "itemvalue(不能为空)")
    private String itemvalue;

    /**
     * 物品描述
     **/
    private String desc;

    /**
     * 物品URL
     **/
    private String itemurl;


}
