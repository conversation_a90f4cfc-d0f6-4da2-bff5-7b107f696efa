package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.wms;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Getter
@Setter
public class EcCangGetProductStockRequest {
    @NotNull
    private Integer pageSize;
    @NotNull
    private Integer page;
    private String product_sku;
    private String[] product_sku_arr;
    private String warehouse_code;
    private String[] warehouse_code_arr;
    private String update_start_time;
    private String is_warning;
    private String product_title;
    private String search_type;
}
