package com.newnary.gsp.center.tpsi.service.impl;

import com.alibaba.fastjson.JSON;
import com.newnary.common.utils.idgenerator.IDGenerator;
import com.newnary.common.utils.idgenerator.enums.LengthType;
import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderDetailInfo;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierAdjustSupplyPriceReq;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierItemCreateCombineDetailInfo;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierUpdateStockReq;
import com.newnary.gsp.center.tpsi.infra.client.mabang.MaBangParams;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.MaBangStockQuantityWarehouse;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.MaBangStockSkuWarehouseInfo;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.order.MaBangDoCreateOrder;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.order.MaBangDoCreateOrderOrderItem;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.order.MaBangDoDeliverOrder;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.stock.MaBangDoSearchSkuList;
import com.newnary.gsp.center.tpsi.infra.model.ApiRequestParams;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.service.IMaBangApiDataTranslateSve;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class MaBangApiDataTranslateSve implements IMaBangApiDataTranslateSve {

    // 时间格式为：yyyy-MM-dd HH:mm:ss
    private static final DateTimeFormatter DEFAULT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    // 获取字符串的现在时间
    public static String formatNow() {
        return LocalDateTime.now().format(DEFAULT_FORMATTER);
    }

    @Override
    public MaBangDoCreateOrder buildMaBangDoCreateOrder(String stockoutOrderId, DeliveryOrderDetailInfo deliveryOrderDetailInfo, ApiRequestParams apiRequestParams) {
        MaBangDoCreateOrder params = JSON.parseObject(apiRequestParams.getParams(), MaBangDoCreateOrder.class);

        MaBangDoCreateOrder ret = new MaBangDoCreateOrder();
        ret.setPlatformOrderId(IDGenerator.generateBizNidWithPrefix("", LengthType.SIXTEEN));
        ret.setSalesRecordNumber(stockoutOrderId);

        //店铺名称、买家名称、电话号码、国家、街道、币种
        ret.setShopName(params.getShopName());
        ret.setBuyerUserId(params.getBuyerUserId());
        ret.setBuyerName(params.getBuyerName());
        ret.setPhone1(params.getPhone1());
        //ret.setCountry(params.getCountry());
        ret.setCountryCode(deliveryOrderDetailInfo.getGroupWarehouseAddrInfo().addrCountry);
        ret.setStreet1(deliveryOrderDetailInfo.getGroupWarehouseAddrInfo().addrDetail);
        ret.setCurrencyId(deliveryOrderDetailInfo.getAmountInfo().getTradeCurrency());
        //付款时间-gmtChannelOrderCreate
        ret.setPaidTime(formatNow());

        List<MaBangDoCreateOrderOrderItem> orderItemList = new ArrayList<>();
        ret.setOrderItemList(deliveryOrderDetailInfo.getItems().stream().map(detailItemInfo -> {
            MaBangDoCreateOrderOrderItem maBangDoCreateOrderOrderItem = new MaBangDoCreateOrderOrderItem();
            maBangDoCreateOrderOrderItem.setQuantity(detailItemInfo.getQuantity());
            maBangDoCreateOrderOrderItem.setPlatformSku(detailItemInfo.getCustomCode());
            maBangDoCreateOrderOrderItem.setTitle(detailItemInfo.getSupplierSkuTitle());
            //图片url（必填）
            if (CollectionUtils.isNotEmpty(detailItemInfo.getSupplierSkuPicUrl())) {
                maBangDoCreateOrderOrderItem.setPictureUrl(detailItemInfo.getSupplierSkuPicUrl().get(0));
            } else {
                maBangDoCreateOrderOrderItem.setPictureUrl(detailItemInfo.getSupplierSkuTitle());
            }
            maBangDoCreateOrderOrderItem.setSellPrice(detailItemInfo.getSupplyPrice());

            return maBangDoCreateOrderOrderItem;
        }).collect(Collectors.toList()));

/*        maBangDoCreateOrder.setPlatformTrackNumber();
        maBangDoCreateOrder.setShippingType();
        maBangDoCreateOrder.setWarehouseName();
        maBangDoCreateOrder.setMyLogisticsChannelId();*/

        ret.setDistrict(deliveryOrderDetailInfo.getGroupWarehouseAddrInfo().addrDistrict);
        ret.setProvince(deliveryOrderDetailInfo.getGroupWarehouseAddrInfo().addrProvince);
        ret.setCity(deliveryOrderDetailInfo.getGroupWarehouseAddrInfo().addrCity);
/*        maBangDoCreateOrder.setAbnnumber();
        maBangDoCreateOrder.setEmail();
        maBangDoCreateOrder.setDoorcode();*/
        ret.setPostCode(deliveryOrderDetailInfo.getGroupWarehouseAddrInfo().zipCode);
        /*maBangDoCreateOrder.setBuyerMessage();*/
        /*ret.setVoucherPrice();*/
        /*maBangDoCreateOrder.setShippingCost();*/
        ret.setShippingFee(deliveryOrderDetailInfo.getAmountInfo().getFreightFee());
        /*maBangDoCreateOrder.setPlatformFee();*/
        ret.setItemTotal(deliveryOrderDetailInfo.getAmountInfo().getItemTotalAmount());
/*        maBangDoCreateOrder.setOtherIncome();
        maBangDoCreateOrder.setOtherExpend();
        maBangDoCreateOrder.setInsuranceFee();
        maBangDoCreateOrder.setPaypalFee();
        maBangDoCreateOrder.setPaymentName();
        maBangDoCreateOrder.setCodflag();
        maBangDoCreateOrder.setShippingService();
        maBangDoCreateOrder.setDlypName();
        maBangDoCreateOrder.setPaypalId();
        maBangDoCreateOrder.setTransNumber();
        maBangDoCreateOrder.setDeliveryRemark();
        ret.setRemark();*/

        return ret;
    }

    @Override
    public MaBangDoDeliverOrder buildMaBangDoDeliveryOrder() {
        MaBangDoDeliverOrder ret = new MaBangDoDeliverOrder();
        return ret;
    }

    @Override
    public MaBangDoSearchSkuList buildMaBangDoSearchSkuList() {
        MaBangDoSearchSkuList param = new MaBangDoSearchSkuList();
        param.setRowsPerPage(100);
        param.setShowVirtualSku(1);
        param.setShowLabel(1);
        param.setShowProvider(1);
        param.setShowWarehouse(1);
        param.setShowattributes(1);
        return param;
    }

    @Override
    public OpenSupplierUpdateStockReq buildOpenSupplierUpdateStockReq(ThirdPartySystem system, MaBangStockQuantityWarehouse maBangStockQuantityWarehouse, String sku) {
        MaBangParams params = JSON.parseObject(system.getParams(), MaBangParams.class);
        OpenSupplierUpdateStockReq req = new OpenSupplierUpdateStockReq();
        //业务操作流水
        //流水号为空，如果没找到批次会自动创建批次
        req.bizSerialNumber = "";
        req.supplierId = params.getSupplierId();
        req.customSkuCode = sku;
        req.customWarehouseCode = maBangStockQuantityWarehouse.getWarehouseName();
        req.stockNum = maBangStockQuantityWarehouse.getStockQuantity();
        return req;
    }

    @Override
    public OpenSupplierAdjustSupplyPriceReq buildOpenSupplierAdjustSupplyPriceReq(String supplierId, MaBangStockSkuWarehouseInfo warehouseInfo, String sku, String whCountry) {
        OpenSupplierAdjustSupplyPriceReq req = new OpenSupplierAdjustSupplyPriceReq();
        req.supplierId = supplierId;
        req.customSkuCode = sku;
        req.country = whCountry;
        req.supplyPrice = warehouseInfo.getStockCost();
        req.costPrice = warehouseInfo.getStockCost();
        //TODO 币种
        req.supplyPriceCurrency = "CNY";
        req.costPriceCurrency = "CNY";

        OpenSupplierItemCreateCombineDetailInfo combineItem = new OpenSupplierItemCreateCombineDetailInfo();
        combineItem.setSupplyPrice(warehouseInfo.getStockCost());
        //TODO 币种
        combineItem.setSupplyPriceCurrency("CNY");
        //马帮数量为1
        combineItem.setCombineNum(1);

        List<OpenSupplierItemCreateCombineDetailInfo> perDataList = new ArrayList<>();
        perDataList.add(combineItem);
        req.combineDetails = perDataList;

        return req;
    }


}
