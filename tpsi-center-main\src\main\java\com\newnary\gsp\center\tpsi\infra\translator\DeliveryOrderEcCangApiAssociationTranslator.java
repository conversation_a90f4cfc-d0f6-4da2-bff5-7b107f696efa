package com.newnary.gsp.center.tpsi.infra.translator;

import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderDetailInfo;
import com.newnary.gsp.center.tpsi.infra.mapper.DeliveryOrderItemEcCangApiAssociationMapper;
import com.newnary.gsp.center.tpsi.infra.model.DeliveryOrderEcCangApiAssociation;
import com.newnary.gsp.center.tpsi.infra.model.creator.DeliveryOrderEcCangApiAssociationCreator;
import com.newnary.gsp.center.tpsi.infra.model.vo.DeliveryOrderItemEcCangApiAssociation;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.DeliveryOrderEcCangApiAssociationPO;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.DeliveryOrderItemEcCangApiAssociationPO;
import com.newnary.gsp.center.trade.api.order.response.OrderDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class DeliveryOrderEcCangApiAssociationTranslator {

    public static DeliveryOrderEcCangApiAssociationCreator transCreator(
            DeliveryOrderEcCangApiAssociationPO associationPO,
            List<DeliveryOrderItemEcCangApiAssociationPO> itemPOS
    ) {
        DeliveryOrderEcCangApiAssociationCreator creator = new DeliveryOrderEcCangApiAssociationCreator();

        creator.setAssociationId(associationPO.getAssociationId());
        // 基本信息
        creator.setDeliveryOrderId(associationPO.getDeliveryOrderId());
        creator.setStockoutOrderId(associationPO.getStockoutOrderId());
        creator.setTransportOrderId(associationPO.getTransportOrderId());
        creator.setErpTpsId(associationPO.getErpTpsId());
        creator.setWmsTpsId(associationPO.getWmsTpsId());
        creator.setTradeOrderId(associationPO.getTradeOrderId());
        creator.setOrderRefNo(associationPO.getOrderRefNo());

        creator.setErpOrderSaleOrderCode(associationPO.getErpOrderSaleOrderCode());
        creator.setErpOrderStatus(associationPO.getErpOrderStatus());
        creator.setErpOrderShippingMethodNo(associationPO.getErpOrderShippingMethodNo());

        creator.setWmsOrderCode(associationPO.getWmsOrderCode());
        creator.setWmsOrderLabelCode(associationPO.getWmsOrderLabelCode());
        creator.setWmsBoxCode(associationPO.getWmsBoxCode());
        creator.setWmsWarehouseId(associationPO.getWmsWarehouseId());
        creator.setWmsFreigntOrderStatus(associationPO.getWmsFreigntOrderStatus());
        creator.setWmsCblValue(associationPO.getWmsCblValue());
        creator.setWmsCblValueCurrency(associationPO.getWmsCblValueCurrency());
        creator.setWmsFtNameCn(associationPO.getWmsFtNameCn());
        creator.setWmsWeight(associationPO.getWmsWeight());
        creator.setWmsVolume(associationPO.getWmsVolume());
        creator.setWmsVolumeWeight(associationPO.getWmsVolumeWeight());

        // 明细信息
        creator.setItems(DeliveryOrderItemEcCangApiAssociationMapper.INSTANCE.pos2Models(itemPOS));

        return creator;
    }

    public static DeliveryOrderEcCangApiAssociationCreator buildCreator(OrderDTO order, DeliveryOrderDetailInfo deliveryOrderDetailInfo, String stockoutOrderId, String transportOrderId, String saleOrderCode,
                                                                        String erpTpsId, String wmsTpsId) {
        DeliveryOrderEcCangApiAssociationCreator creator = new DeliveryOrderEcCangApiAssociationCreator();
        creator.setDeliveryOrderId(deliveryOrderDetailInfo.deliveryOrderId);
        creator.setStockoutOrderId(stockoutOrderId);
        creator.setTransportOrderId(transportOrderId);
        creator.setErpTpsId(erpTpsId);
        creator.setWmsTpsId(wmsTpsId);
        creator.setErpOrderSaleOrderCode(saleOrderCode);
        if (null == deliveryOrderDetailInfo.tradeOrderId || null == deliveryOrderDetailInfo.tradeSubOrderId){
            creator.setOrderRefNo(deliveryOrderDetailInfo.tradeOrderId);
        }else {
            creator.setOrderRefNo(deliveryOrderDetailInfo.tradeOrderId.concat("-").concat(deliveryOrderDetailInfo.tradeSubOrderId));
        }

        creator.setErpOrderStatus(2);

        creator.setWmsFreigntOrderStatus(1);
        creator.setTradeOrderId(deliveryOrderDetailInfo.tradeOrderId);

        // 明细信息
        List<DeliveryOrderItemEcCangApiAssociation> items = new ArrayList<>();
        deliveryOrderDetailInfo.items.forEach(detailItemInfo -> {
                    DeliveryOrderItemEcCangApiAssociation item = new DeliveryOrderItemEcCangApiAssociation();
                    item.setSaleItemCode(detailItemInfo.saleItemCode);
                    item.setSupplierSkuId(detailItemInfo.supplierSkuId);
                    item.setQuantity(detailItemInfo.quantity);
                    item.setCustomCode(StringUtils.isEmpty(detailItemInfo.customCode) ? detailItemInfo.supplierSkuId : detailItemInfo.customCode);
                    item.setSupplyPrice(detailItemInfo.supplyPrice);

                    //根据创建集运订单时填写的子订单号来决定
/*                    item.setWmsProductPid(productMap.get(detailItemInfo.getCustomCode()));
                    EcCangFreightCreateOrderRequest.ProductData productData = productDataMap.get(detailItemInfo.getCustomCode());
                    item.setWmsChildCode(productData.child_code);
                    item.setWmsProductCategory(productData.product_category);
                    item.setWmsProductCount(productData.product_count);
                    item.setWmsProductDeclared(productData.product_declared);
                    item.setWmsProductImgUrl(productData.product_img_url);
                    item.setWmsProductNameCn(productData.product_name_cn);
                    item.setWmsProductNameEn(productData.product_name_en);
                    item.setWmsProductStandard(productData.product_standard);*/
                    items.add(item);
                }
        );
        creator.setItems(items);
        return creator;
    }

    public static DeliveryOrderEcCangApiAssociationCreator renew(DeliveryOrderEcCangApiAssociation association) {
        DeliveryOrderEcCangApiAssociationCreator creator = new DeliveryOrderEcCangApiAssociationCreator();
        creator.setDeliveryOrderId(association.getDeliveryOrderId());
        creator.setStockoutOrderId(association.getStockoutOrderId());
        creator.setTransportOrderId(association.getTransportOrderId());
        creator.setErpTpsId(association.getErpTpsId());
        creator.setWmsTpsId(association.getWmsTpsId());
        creator.setWmsOrderLabelCode(association.getWmsOrderLabelCode());   //暂时借用国内段出库面单跟踪号用作新的国外集运单的包裹号

        // 明细信息
        List<DeliveryOrderItemEcCangApiAssociation> items = new ArrayList<>();
        association.getItems().forEach(detailItemInfo -> {
                    DeliveryOrderItemEcCangApiAssociation item = new DeliveryOrderItemEcCangApiAssociation();
                    item.setSaleItemCode(detailItemInfo.getSaleItemCode());
                    item.setSupplierSkuId(detailItemInfo.getSupplierSkuId());
                    item.setQuantity(detailItemInfo.getQuantity());
                    item.setCustomCode(StringUtils.isEmpty(detailItemInfo.getCustomCode()) ? detailItemInfo.getSupplierSkuId() : detailItemInfo.getCustomCode());
                    item.setSupplyPrice(detailItemInfo.getSupplyPrice());

                    items.add(item);
                }
        );
        creator.setItems(items);
        return creator;
    }

}
