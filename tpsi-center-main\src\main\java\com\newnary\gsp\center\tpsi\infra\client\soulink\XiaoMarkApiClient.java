package com.newnary.gsp.center.tpsi.infra.client.soulink;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.common.utils.httpmethod.ApiBaseResult;
import com.newnary.common.utils.httpmethod.HttpMethodUtil;
import com.newnary.gsp.center.tpsi.api.shortlink.response.ConvertShortLinkResp;
import com.newnary.gsp.center.tpsi.api.shortlink.response.XiaoMarkGroupVO;
import com.newnary.gsp.center.tpsi.infra.client.soulink.params.XiaoMarkParam;
import com.newnary.gsp.center.tpsi.infra.client.soulink.valobj.Response.XiaoMarkDataApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.soulink.valobj.Response.XiaoMarkResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: WangRUTao
 * 小码短链
 */
@Slf4j
public class XiaoMarkApiClient {

    // 设置日期格式为"yyyy-MM-dd HH:mm:ss"
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /*短链转换接口*/
    private static String convertShortLink = "/v1/link/create";

    private XiaoMarkParam xiaoMarkParam;

    public XiaoMarkApiClient(String jtJsonParam) {
        XiaoMarkParam params = JSON.parseObject(jtJsonParam, XiaoMarkParam.class);
        this.xiaoMarkParam = params;
    }

    /**
     * 请求缩短链接
     *
     * @return SouLinkDataApiBaseResult
     */
    public XiaoMarkDataApiBaseResult<String> convertShortLink(String url, String sourceDomain,String groupSid) {

        Map<String, Object> bodyParas = getParam(url,groupSid);
        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");

        ApiBaseResult apiBaseResult = null;
        try {
            log.info("[请求小码短链Params]{} " , JSONObject.toJSONString(bodyParas));

            apiBaseResult = HttpMethodUtil.syncPostMethod(xiaoMarkParam.getBaseUrl(),
                    0,
                    convertShortLink,
                    "application/json",
                    header, null, bodyParas);
            XiaoMarkDataApiBaseResult<String> baseResult = buildDataBaseResult(apiBaseResult, bodyParas);

            System.out.println("[请求小码短链baseResult {} " + baseResult);
            log.info("[请求小码短链baseResult]请求结束, code={}, message={}, result={}, dataParas={}", baseResult.getCode(), baseResult.getMessage(), baseResult.getResult(), JSONObject.toJSONString(bodyParas));
            return baseResult;
        } catch (Exception e) {
            log.error(e.toString());
            XiaoMarkDataApiBaseResult<String> ret = new XiaoMarkDataApiBaseResult<>();
            ret.setCode(200);
            ConvertShortLinkResp resp = new ConvertShortLinkResp();
            resp.setShortLink(url);
            resp.setDomain(sourceDomain);
            resp.setExpireDate((String) bodyParas.get("expireDate"));
            resp.setGroup(new XiaoMarkGroupVO("", groupSid));
            ret.setResult(JSONObject.toJSONString(resp));
            return ret;
        }
    }


    private Map<String, Object> getParam(String url,String groupSid) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("origin_url",url);
        params.put("apikey", xiaoMarkParam.getKey());
        if (StringUtils.isNotBlank(groupSid)){
            params.put("group_sid", groupSid);
        }
        if (StringUtils.isNotBlank(xiaoMarkParam.getDomain())){
            params.put("domain", xiaoMarkParam.getDomain());
        }
        /**
         * 以下为预留参数，暂时还用不到
         */
        /*params.put("domain",xiaoMarkParam.getDomain());
        params.put("group_sid",dataAfter(xiaoMarkParam.getExpireDays()));
        params.put("report",dataAfter(xiaoMarkParam.getExpireDays()));
        params.put("webhook",dataAfter(xiaoMarkParam.getExpireDays()));
        params.put("webhook_scene",dataAfter(xiaoMarkParam.getExpireDays()));*/
        return params;
    }

    private String dataAfter(Long date) {
        // 获取当前日期
        LocalDateTime currentDate = LocalDateTime.now();
        // 将日期加上7天
        LocalDateTime newDate = currentDate.plusDays(date);
        return newDate.format(formatter);
    }


    private XiaoMarkDataApiBaseResult<String> buildDataBaseResult(ApiBaseResult resultStr, Map<String, Object> bodyParas) {
        XiaoMarkDataApiBaseResult<String> apiBaseResult = new XiaoMarkDataApiBaseResult<>();
        apiBaseResult.setCode(resultStr.getCode());

        JSONObject jsonObject = JSONObject.parseObject(resultStr.getRet());
        XiaoMarkResp data = JSONObject.parseObject(jsonObject.getString("data"), XiaoMarkResp.class);
        ConvertShortLinkResp resp = new ConvertShortLinkResp();

        resp.setShortLink(data.getLink().getUrl());
        resp.setDomain(xiaoMarkParam.getDomain());
        resp.setExpireDate((String) bodyParas.get("expireDate"));
        resp.setGroup(new XiaoMarkGroupVO(data.getGroup().getName(), data.getGroup().getSid()));
        apiBaseResult.setResult(JSONObject.toJSONString(resp));
        return apiBaseResult;
    }


}
