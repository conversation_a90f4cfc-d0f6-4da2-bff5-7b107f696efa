package com.newnary.gsp.center.tpsi.ctrl.haiying;

import com.newnary.gsp.center.tpsi.api.haiying.request.lazada.*;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.lazada.*;
import com.newnary.gsp.center.tpsi.infra.mapper.HaiYingLazadaDataMapper;

/**
 * <AUTHOR>
 * @Date 2021/12/14 14:52
 */
public class HaiYingLazadaCommand2RequestTranslator {

    public static HaiYingLazadaProductListRequest transLazadaProductList(HaiYingLazadaProductListCommand command) {
        HaiYingLazadaProductListRequest request = HaiYingLazadaDataMapper.INSTANCE.transLazadaProductListRequest(command);

        return request;
    }

    public static HaiYingLazadaProductDetailInfoRequest transLazadaProductDetailInfo(HaiYingLazadaProductDetailInfoCommand command) {
        HaiYingLazadaProductDetailInfoRequest request = HaiYingLazadaDataMapper.INSTANCE.transLazadaProductDetailRequest(command);

        return request;
    }

    public static HaiYingLazadaProductExtInfoRequest transLazadaProductExtInfo(HaiYingLazadaProductExtInfoCommand command) {
        HaiYingLazadaProductExtInfoRequest request = HaiYingLazadaDataMapper.INSTANCE.transLazadaProductExtRequest(command);

        return request;
    }

    public static HaiYingLazadaProductHistoryInfoRequest transLazadaProductHistoryInfo(HaiYingLazadaProductHistoryInfoCommand command) {
        HaiYingLazadaProductHistoryInfoRequest request = HaiYingLazadaDataMapper.INSTANCE.transLazadaProductHistoryRequest(command);

        return request;
    }

    public static HaiYingLazadaProductHistoryDailyReviewRequest transLazadaProductHistoryDailyReview(HaiYingLazadaProductHistoryDailyReviewCommand command) {
        HaiYingLazadaProductHistoryDailyReviewRequest request = HaiYingLazadaDataMapper.INSTANCE.transLazadaProductHistoryDailyReviewRequest(command);

        return request;
    }

    public static HaiYingLazadaCategoryTreeRequest transLazadaCategoryTree(HaiYingLazadaCategoryTreeCommand command) {
        HaiYingLazadaCategoryTreeRequest request = HaiYingLazadaDataMapper.INSTANCE.transLazadaCategoryTreeRequest(command);

        return request;
    }

    public static HaiYingLazadaTopCategoryInfoRequest transLazadaTopCategoryInfo(HaiYingLazadaTopCategoryInfoCommand command) {
        HaiYingLazadaTopCategoryInfoRequest request = HaiYingLazadaDataMapper.INSTANCE.transLazadaTopCategoryRequest(command);

        return request;
    }

    public static HaiYingLazadaSubCategoryInfoRequest transLazadaSubCategoryInfo(HaiYingLazadaSubCategoryInfoCommand command) {
        HaiYingLazadaSubCategoryInfoRequest request = HaiYingLazadaDataMapper.INSTANCE.transLazadaSubCategoryRequest(command);

        return request;
    }

}
