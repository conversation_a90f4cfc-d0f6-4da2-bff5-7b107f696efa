package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.ebay;

import lombok.Data;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingEbayTopCategoryInfoResponse {

    /**
     * 类目ID
     */
    private String cid;

    /**
     * 类目名称
     */
    private String cname;

    /**
     * 有销量的商品总数
     */
    private String product_count;

    /**
     * 店铺总数
     */
    private String merchant_count;

    /**
     * 前1天类目销售件数
     */
    private String sold_the_previous_day;

    /**
     * 前1天类目销售金额
     */
    private String payment_the_previous_day;

    /**
     * 前1天类目销售增幅
     */
    private String sold_the_previous_growth;

    /**
     * 前1-7天类目销售件数
     */
    private String sales_week1;

    /**
     * 前8-14天类目销售件数
     */
    private String sales_week2;

    /**
     * 前1-7天类目销售金额
     */
    private String payment_week1;

    /**
     * 前7天类目销售增幅
     */
    private String sales_week_growth;

    /**
     * 统计时间
     */
    private String stat_date;

    /**
     * 存入时间
     */
    private String ins_date;

    /**
     * 前1-3天类目销售件数
     */
    private String sales_three_day1;

    /**
     * 前4-6天类目销售件数
     */
    private String sales_three_day2;

    /**
     * 前1-3天类目销售金额
     */
    private String payment_three_day1;

    /**
     * 前3天类目销售增幅
     */
    private String sales_three_day_growth;

    /**
     * 商品总数(已取消)
     */
    @Deprecated
    private String all_product_count;

    /**
     * 销量为0的商品总数(已取消)
     */
    @Deprecated
    private String unsold_product_count;

}
