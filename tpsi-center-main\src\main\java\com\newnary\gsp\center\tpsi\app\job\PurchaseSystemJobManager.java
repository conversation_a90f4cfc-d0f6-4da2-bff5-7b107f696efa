package com.newnary.gsp.center.tpsi.app.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.newnary.api.base.common.CommonResponse;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.purchase.api.category.response.CategoryInfo;
import com.newnary.gsp.center.purchase.api.common.dto.MultimediaInfo;
import com.newnary.gsp.center.purchase.api.common.dto.MultimediaType;
import com.newnary.gsp.center.purchase.api.product.request.*;
import com.newnary.gsp.center.purchase.api.product.response.spu.SpuTextDesc;
import com.newnary.gsp.center.purchase.api.product.vo.SkuMainSpecInfo;
import com.newnary.gsp.center.purchase.api.product.vo.SkuSpecInfo;
import com.newnary.gsp.center.purchase.api.product.vo.SpuCategoryInfo;
import com.newnary.gsp.center.tpsi.api.eccang.request.GspSyncPo2EccangCommand;
import com.newnary.gsp.center.tpsi.app.listener.mq.GSPPurchaseOrderTakeStockProcessor;
import com.newnary.gsp.center.tpsi.app.service.eccang.EccangPoMgmtApp;
import com.newnary.gsp.center.tpsi.infra.client.eccang.mapping.EcCangERPMapping;
import com.newnary.gsp.center.tpsi.infra.client.eccang.params.EcCangERPParams;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp.EcCangERPGetProductBySkuRequest;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp.EcCangERPGetProductListRequest;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.erp.EcCangERPGetProductBySkuResponse;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.erp.EcCangERPGetProductListResponse;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.erp.EcCangERPGetSupplierResponse;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingInfo;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingType;
import com.newnary.gsp.center.tpsi.infra.repository.IApiRequestParamsRepository;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.ThirdPartyMappingManager;
import com.newnary.gsp.center.tpsi.infra.rpc.PurchaseRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.UserRpc;
import com.newnary.gsp.center.tpsi.service.eccang.IEccangERPApiSve;
import com.newnary.gsp.center.user.api.identity.response.UserDTO;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.job.core.handler.annotation.Job;
import com.newnary.tenant.context.TenantCarrier;
import com.newnary.tenant.context.TenantID;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
@Slf4j
public class PurchaseSystemJobManager {
    private static final String getSupplierInfoUrl = "http://10.23.32.49:8340/get_1688ShopUrl";

    @Resource
    private PurchaseRpc purchaseRpc;
    @Resource
    private UserRpc userRpc;

    @Resource
    private ThirdPartyMappingManager thirdPartyMappingManager;

    @Resource
    private IEccangERPApiSve eccangERPApiSveImpl;

    @Resource
    private EccangPoMgmtApp eccangPoMgmtApp;

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    private final ThreadPoolExecutor processExecutor = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors() * 20,
            Runtime.getRuntime().availableProcessors() * 20,
            20L, TimeUnit.SECONDS,
            new LinkedBlockingDeque<>(100),
            new ThreadPoolExecutor.CallerRunsPolicy());

/*    private String supplierId = null;
    private String warehouse = null;

    @Job("autoSyncShengWeiProduct")
    public ReturnT<String> syncShengWeiProduct(String param) {
        JSONObject paramObject = JSONObject.parseObject(param);
        Integer perGroupCount = paramObject.getInteger("perGroupCount");
        supplierId = paramObject.getString("supplierId");
        warehouse = paramObject.getString("warehouse");
        categoryId = paramObject.getString("categoryId");

        //指定插入页数
        Integer insertPage = paramObject.getInteger("insertPage");
        //指定从第几页开始插入
        Integer indexPage = paramObject.getInteger("indexPage");
        //指定每页条数
        Integer pageSize = paramObject.getInteger("pageSize");
        int current = 0;
        if (indexPage != null) {
            current = indexPage;
        }
        SpuPageQueryCommand spuPageQueryCommand = new SpuPageQueryCommand();
        PageCondition pageCondition = new PageCondition();
        pageCondition.setPageSize(pageSize);
        spuPageQueryCommand.setPageCondition(pageCondition);
        List<SpuInfo> spuInfoList = null;
        Map<String, ThirdPartyMappingInfo> categoryMapping = null;
        do {
            pageCondition.setPageNum(current);
            try {
                spuInfoList = purchaseSpuRpc.getSpuInfoList(spuPageQueryCommand);
                if (StringUtils.isBlank(categoryId)) {
                    categoryMapping = thirdPartyMappingManager.batchGetIdMappingInfoByTargetIds("GSP", "SHENGWEI", spuInfoList.stream().map(SpuInfo::getCategoryId).distinct().collect(Collectors.toList()), ThirdPartyMappingType.CATEGORY);
                }
                processBatch(spuInfoList, perGroupCount, categoryMapping);
                log.info("升威商品页数：{}", current);
                current += 1;
            } catch (Exception e) {
                e.printStackTrace();
                log.error("[{}] 升威商品同步任务异常！e={}", "升威商品同步任务", e);
                spuInfoList = null;
            }
        } while (CollectionUtils.isNotEmpty(spuInfoList));

        return ReturnT.SUCCESS;
    }

    //使用多线程进行同步
    private void processBatch(List<SpuInfo> spuInfoList, Integer perGroupCount, Map<String, ThirdPartyMappingInfo> categoryMapping) throws Exception {
        final Optional<TenantID> tenantIdKept = TenantCarrier.getTenantID();
        // 1. 批量任务分组
        //List<String> skuIdList = productList.stream().map(EcCangERPGetProductListResponse::getProductSku).collect(Collectors.toList());
        List<List<SpuInfo>> groupSkuIds = ListUtils.partition(spuInfoList, perGroupCount);
        CountDownLatch processCountDownLatch = new CountDownLatch(groupSkuIds.size());

        // 2. 线程池执行
        groupSkuIds.forEach(groupItem -> {

            CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> {
                boolean tenantIdMiss = false;
                try {
                    if (!TenantCarrier.getTenantID().isPresent()) {
                        TenantID tenantID = tenantIdKept.orElse(null);
                        TenantCarrier.setTenantID(tenantID);
                        tenantIdMiss = tenantID != null;
                    }
                    syncProduct(groupItem, categoryMapping);
                } catch (Exception e) {
                    log.error("[{}] 升威商品同步任务异常！e={}", "升威商品同步任务", e);
                } finally {
                    processCountDownLatch.countDown();
                    if (tenantIdMiss) {
                        TenantCarrier.clearTenantID();
                    }
                }
            }, processExecutor);

        });

        // 3. 等待批次商品同步处理完成
        processCountDownLatch.await();
    }

    //同步ec商品
    public void syncProduct(List<SpuInfo> spuInfoList, Map<String, ThirdPartyMappingInfo> categoryMapping) {
        spuInfoList.forEach(spuInfo -> {
            ThirdPartyMappingInfo newestInfoByTarget = thirdPartyMappingManager.getNewestInfoByTarget("SHERNGWEI", "GSP", spuInfo.getSpuId(), ThirdPartyMappingType.PRODUCT_ID);
            if (ObjectUtils.isNotEmpty(newestInfoByTarget)) return;
            try {
                String spuId = openSupplierProductRpc.createSpu4StockAsync(buildSupplierSpuCreateV2Command(categoryMapping, spuInfo));
                if (spuId != null) {
                    thirdPartyMappingManager.insertOrUpdate("GSP", "SHERNGWEI", spuId, spuInfo.getSpuId(), ThirdPartyMappingType.PRODUCT_ID.name(), spuInfo);
                }
            } catch (Exception e) {
                log.error("shengwei创建商品失败：{}", e.getMessage());
            }
        });
    }

    //构建商品类目信息
    private void setCategoryInfo(SupplierSpuCreateV2Command supplierSpuCreateV2Command, Map<String, ThirdPartyMappingInfo> categoryMapping, SpuInfo spuInfo) {
        //String path = item.getCategory_name_one().concat("/".concat(item.getCategory_name_sub()).concat("/").concat(item.getCategory_name_two()));
        String platformCategoryId = spuInfo.getCategoryId();
        try {
            if (StringUtils.isNotBlank(platformCategoryId)) {
                ThirdPartyMappingInfo thirdPartyMappingInfo = categoryMapping.get(platformCategoryId);
                CategoryInfo categoryInfo = categoryRpc.getCategoryById(thirdPartyMappingInfo.getSourceId());
                if (ObjectUtils.isNotEmpty(categoryInfo)) {
                    supplierSpuCreateV2Command.setCategoryId(categoryInfo.getCategoryId());
                    supplierSpuCreateV2Command.setMgmtCategoryLevel(CategoryLevel.getByValue(categoryInfo.getCategoryLevel()));
                }
            } else {
                throw new RuntimeException();
            }
        } catch (Exception e) {
            log.error("SHENGWEI商品设置类目失败，goodId: {} categorlyMapping: {} targetId: {}", spuInfo.getSpuId(), JSON.toJSONString(categoryMapping), platformCategoryId);
        }
    }

    public SupplierSpuCreateV2Command buildSupplierSpuCreateV2Command(Map<String, ThirdPartyMappingInfo> categoryMapping, SpuInfo spuInfo) {
        SupplierSpuCreateV2Command spuCreateV2Command = new SupplierSpuCreateV2Command();
        spuCreateV2Command.setSupplierId(supplierId);

        spuCreateV2Command.setDescInfos(buildSpuInfo(spuInfo));

        spuCreateV2Command.setDefaultLocale(LanguageLocaleType.zh_CN);

        spuCreateV2Command.setCustomCode(spuInfo.getSpu());

        spuCreateV2Command.setCustomBrandId(spuInfo.getBrandId());

        spuCreateV2Command.setCustomCategoryId(spuInfo.getCategoryId());

        if (categoryMapping != null) {
            setCategoryInfo(spuCreateV2Command, categoryMapping, spuInfo);
        } else {
            spuCreateV2Command.setCategoryId(categoryId);
        }

        List<MultimediaInfo> mainImages = new ArrayList<>();
        List<com.newnary.gsp.center.purchase.api.common.dto.MultimediaInfo> images = spuInfo.getMainImages();
        if (CollectionUtils.isNotEmpty(images)) {
            images.forEach(image -> {
                MultimediaInfo mainImage = new MultimediaInfo();
                mainImage.setType(MultimediaType.IMAGE);
                mainImage.setFileUrl(image.getFileUrl());
                mainImages.add(mainImage);
            });
        } else {
            MultimediaInfo mainImage = new MultimediaInfo();
            mainImage.setType(MultimediaType.IMAGE);
            mainImage.setFileUrl(spuInfo.getMainImage());
            mainImages.add(mainImage);
        }
        spuCreateV2Command.setMainImages(mainImages);

        spuCreateV2Command.setSkuList(buildSkuInfo(spuInfo));

        //是否尝试创建
        spuCreateV2Command.setIsTryCreate(true);
        //操作人
        spuCreateV2Command.setOperator("tpsi");

        return spuCreateV2Command;
    }

    public List<SupplierSpuDescInfo> buildSpuInfo(SpuInfo spuInfo) {
        List<SupplierSpuDescInfo> descInfos = new ArrayList<>();
        SupplierSpuDescInfo supplierSpuDescInfo = new SupplierSpuDescInfo();

        supplierSpuDescInfo.setLocale(LanguageLocaleType.zh_CN);
        supplierSpuDescInfo.setTitle(spuInfo.getTitle());
        supplierSpuDescInfo.setCustomMeasuringUnit(spuInfo.getUnit());
        supplierSpuDescInfo.setCustomBrandName(spuInfo.getBrandName());
        supplierSpuDescInfo.setCustomCategoryName(spuInfo.getCategoryName());
        supplierSpuDescInfo.setTextDesc(CollectionUtils.isNotEmpty(spuInfo.getTextDescs()) ? spuInfo.getTextDescs().get(0).textDesc : null);

        supplierSpuDescInfo.setTransSourceType("tpsi");
        supplierSpuDescInfo.setTransProvider("SHENGWEI");
        supplierSpuDescInfo.setTransBaseLocate(LanguageLocaleType.zh_CN);
        descInfos.add(supplierSpuDescInfo);
        return descInfos;
    }

    public List<SupplierSkuCreateInfo> buildSkuInfo(SpuInfo spuInfo) {
        if (CollectionUtils.isEmpty(spuInfo.getSkus()))
            throw new ServiceException(new BaseErrorInfo("SKU_ISNULL", "商品sku为空"));
        List<SupplierSkuCreateInfo> skuList = new ArrayList<>();
        spuInfo.getSkus().forEach(skuInfo -> {
            SupplierSkuCreateInfo skuCreateInfo = new SupplierSkuCreateInfo();

            skuCreateInfo.setCustomCode(skuInfo.getSku());
            skuCreateInfo.setRefProductLink(skuInfo.getRefProductLink());
            skuCreateInfo.setCodeUPC(skuInfo.getCodeUPC());
            skuCreateInfo.setCodeEAN(skuInfo.getCodeEAN());
            skuCreateInfo.setMoq(1);
            skuCreateInfo.setNetWeight(skuInfo.getNetWeight());
            skuCreateInfo.setSizeLength(skuInfo.getSizeLength());
            skuCreateInfo.setSizeWidth(skuInfo.getSizeWidth());
            skuCreateInfo.setSizeHeight(skuInfo.getSizeHeight());
            skuCreateInfo.setGrossWeight(skuInfo.getGrossWeight());
            skuCreateInfo.setPackingHeight(skuInfo.getPackingHeight());
            skuCreateInfo.setPackingWidth(skuInfo.getPackingWidth());
            skuCreateInfo.setPackingLength(skuInfo.getPackingLength());
            skuCreateInfo.setMeasuringUnit(skuInfo.getMeasuringUnit());

            //主规格
            SkuMainSpecInfo mainSpecInfo1 = skuInfo.getMainSpecInfo();
            SupplierSkuMainSpecInfo mainSpecInfo = new SupplierSkuMainSpecInfo();
            if (mainSpecInfo1 != null) {
                com.newnary.gsp.center.purchase.api.common.dto.MultimediaInfo image = mainSpecInfo1.getImage();
                SkuSpecInfo spec = mainSpecInfo1.getSpec();
                if (image != null) {
                    MultimediaInfo multimediaInfo = new MultimediaInfo();
                    multimediaInfo.setFileUrl(image.getFileUrl());
                    multimediaInfo.setType(MultimediaType.IMAGE);
                    mainSpecInfo.setImage(multimediaInfo);
                }
                if (spec != null) {
                    SupplierSkuSpecInfo supplierSkuSpecInfo = new SupplierSkuSpecInfo();
                    supplierSkuSpecInfo.setSpecValue(spec.getSpecValue());
                    supplierSkuSpecInfo.setSpecName(spec.getSpecName());
                    mainSpecInfo.setSpec(supplierSkuSpecInfo);
                }
            }
            skuCreateInfo.setMainSpecInfo(mainSpecInfo);

            //规格
            List<SkuSpecInfo> specs = skuInfo.getSpecs();
            List<SupplierSkuSpecInfo> specInfoList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(specs)) {
                specs.forEach(spec -> {
                    SupplierSkuSpecInfo supplierSkuSpecInfo = new SupplierSkuSpecInfo();
                    supplierSkuSpecInfo.setSpecName(spec.getSpecName());
                    supplierSkuSpecInfo.setSpecValue(spec.getSpecValue());
                    specInfoList.add(supplierSkuSpecInfo);
                });
            }
            skuCreateInfo.setSpecs(specInfoList);

            //扩展信息，价格和库存
            SupplierSkuCreateExtendInfo extendInfo = new SupplierSkuCreateExtendInfo();
            //库存
            extendInfo.setReferenceStockNum(0);

            //供货信息
            List<SupplierItemCreateOrUpdate4SpuCommand> supplierItems = new ArrayList<>();
            SupplierItemCreateOrUpdate4SpuCommand supplierItemCreateOrUpdate4SpuCommand = new SupplierItemCreateOrUpdate4SpuCommand();
            supplierItemCreateOrUpdate4SpuCommand.setSaleMode(SupplierItemSaleMode.COUNTRY);
            supplierItemCreateOrUpdate4SpuCommand.setCountry("CN");
            supplierItemCreateOrUpdate4SpuCommand.setWarehouseId(warehouse);
            supplierItemCreateOrUpdate4SpuCommand.setSupplyPriceCurrency("CNY");
            supplierItemCreateOrUpdate4SpuCommand.setSupplyPrice(BigDecimal.valueOf(skuInfo.getReferenceCost()));
            supplierItems.add(supplierItemCreateOrUpdate4SpuCommand);
            extendInfo.setSupplierItems(supplierItems);
            skuCreateInfo.setExtendInfo(extendInfo);

            skuList.add(skuCreateInfo);
        });
        return skuList;
    }*/


    private String thirdPartySystemId = null;
    private String categoryId = null;

    @Resource
    private IApiRequestParamsRepository apiOrderCreateParamsRepository;

    //易仓商品同步到升威采购系统
    @Job("autoSyncECangProductToShengWei")
    public ReturnT<String> syncECangProductToShengWei(String param) {
        log.info("同步易仓商品到升威采购系统定时任务开始, param={}", param);
        try {
            //根据参数获取需要执行的第三方系统id
            JSONObject paramObject = JSONObject.parseObject(param);
            log.info("param={}", param);
            thirdPartySystemId = paramObject.getString("thirdPartySystemId");

            Integer perGroupCount = paramObject.getInteger("perGroupCount");
            String supplierId = paramObject.getString("supplierId");
            String warehouse = paramObject.getString("warehouse");
            categoryId = paramObject.getString("categoryId");
            String products = paramObject.getString("products");

            //根据thirdPartySystemId获取第三方系统参数
            ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);

            if (StringUtils.isNotBlank(products)) {
                List<String> list = JSON.parseArray(products, String.class);
                for (String s : list) {
                    syncEcCangProduct(thirdPartySystem, warehouse, supplierId, s);
                }
                return ReturnT.SUCCESS;
            }
            //指定插入页数
            Integer insertPage = paramObject.getInteger("insertPage");
            //指定从第几页开始插入
            Integer indexPage = paramObject.getInteger("indexPage");
            //指定每页条数
            Integer pageSize = paramObject.getInteger("pageSize");

            EcCangERPParams params = JSON.parseObject(thirdPartySystem.getParams(), EcCangERPParams.class);
            params.setSupplierId(supplierId);
            params.setWarehouse(warehouse);
            thirdPartySystem.setParams(JSON.toJSONString(params));
            EcCangERPGetProductListRequest ecCangERPGetProductListRequest = new EcCangERPGetProductListRequest();
            //ecCangERPGetProductListRequest.setProductStatus(1);
            ecCangERPGetProductListRequest.setPageSize(pageSize);
            List<EcCangERPGetProductListResponse> productList = null;
            //当前页数
            int currentPage = 0;
            if (indexPage != null && indexPage > 0) {
                currentPage = indexPage - 1;
            }
            //Map<String, ThirdPartyMappingInfo> categoryMapping = null;
            do {
                long startTime = System.currentTimeMillis();
                ecCangERPGetProductListRequest.setPage(currentPage += 1);
                try {
                    EcCangApiBaseResult<String> productListStr = eccangERPApiSveImpl.getProductList(thirdPartySystem, ecCangERPGetProductListRequest);
                    assert productListStr != null;
                    if (StringUtils.isBlank(productListStr.getData())) continue;
                    productList = JSON.parseArray(productListStr.getData(), EcCangERPGetProductListResponse.class);
                } catch (Exception e) {
                    log.error("[{}] 易仓商品同步任务异常！e={}", "易仓商品同步任务", e);
                    log.info("当前页数为：{}", currentPage);
                    continue;
                }

                if (CollectionUtils.isEmpty(productList)) continue;

/*                //获取类目信息
                if (StringUtils.isBlank(categoryId)) {
                    categoryMapping = thirdPartyMappingManager.batchGetIdMappingInfoByTargetIds("SHENGWEI", "ECCANG", productList.stream().map(item -> {
                        if (StringUtils.isNotBlank(item.getProcutCategoryCode3())) {
                            return item.getProcutCategoryCode3();
                        } else if (StringUtils.isNotBlank(item.getProcutCategoryCode2())) {
                            return item.getProcutCategoryCode2();
                        } else if (StringUtils.isNotBlank(item.getProcutCategoryCode1())) {
                            return item.getProcutCategoryCode1();
                        } else {
                            return item.getProcutCategoryCode3();
                        }
                    }).distinct().collect(Collectors.toList()), ThirdPartyMappingType.CATEGORY);
                }*/
                //syncProduct(productList);
                processBatch(productList, perGroupCount);
                productList = ((indexPage != null) ? ((currentPage - indexPage) >= insertPage) : (currentPage >= insertPage)) ? null : productList;
                long endTime = System.currentTimeMillis();

                long diffTime = endTime - startTime;
                long ecInterfaceInvokeLimitTime = 5 * 1000L;
                if (diffTime < ecInterfaceInvokeLimitTime) {
                    try {
                        Thread.sleep(ecInterfaceInvokeLimitTime - diffTime + 1 * 1000L);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
                log.info("当前页数为：{}", currentPage);
            } while (CollectionUtils.isNotEmpty(productList));

        } catch (Exception e) {
            log.error("[{}] 易仓商品同步任务异常！e={}", "易仓商品同步任务", e);
            return new ReturnT<>(500, e.getMessage());
        }
        return ReturnT.SUCCESS;
    }

    //根据products同步商品
    public void syncEcCangProduct(ThirdPartySystem thirdPartySystem, String warehouse, String supplierId, String products) {
        EcCangERPParams params = JSON.parseObject(thirdPartySystem.getParams(), EcCangERPParams.class);
        params.setSupplierId(supplierId);
        params.setWarehouse(warehouse);
        thirdPartySystem.setParams(JSON.toJSONString(params));
        EcCangERPGetProductListRequest ecCangERPGetProductListRequest = new EcCangERPGetProductListRequest();
        ecCangERPGetProductListRequest.setProductSku(products);
        EcCangApiBaseResult<String> productListStr = eccangERPApiSveImpl.getProductList(thirdPartySystem, ecCangERPGetProductListRequest);
        assert productListStr != null;
        if (StringUtils.isBlank(productListStr.getData())) return;
        List<EcCangERPGetProductListResponse> productList = JSON.parseArray(productListStr.getData(), EcCangERPGetProductListResponse.class);
        try {
            processBatch(productList, 2);
        } catch (Exception e) {
            //e.printStackTrace();
            log.error("[{}] 易仓商品同步任务异常！e={}", "易仓商品同步任务", e);
        }
    }

    //使用多线程进行同步
    private void processBatch(List<EcCangERPGetProductListResponse> productList, Integer perGroupCount) throws Exception {
        final Optional<TenantID> tenantIdKept = TenantCarrier.getTenantID();
        // 1. 批量任务分组
        //List<String> skuIdList = productList.stream().map(EcCangERPGetProductListResponse::getProductSku).collect(Collectors.toList());
        List<List<EcCangERPGetProductListResponse>> groupSkuIds = ListUtils.partition(productList, perGroupCount);
        CountDownLatch processCountDownLatch = new CountDownLatch(groupSkuIds.size());

        // 2. 线程池执行
        groupSkuIds.forEach(groupItem -> {
            CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> {
                boolean tenantIdMiss = false;
                try {
                    if (!TenantCarrier.getTenantID().isPresent()) {
                        TenantID tenantID = tenantIdKept.orElse(null);
                        TenantCarrier.setTenantID(tenantID);
                        tenantIdMiss = tenantID != null;
                    }
                    syncProduct(groupItem);
                } catch (Exception e) {
                    log.error("[{}] 易仓商品同步任务异常！e={}", "易仓商品同步任务", e);
                } finally {
                    processCountDownLatch.countDown();
                    if (tenantIdMiss) {
                        TenantCarrier.clearTenantID();
                    }
                }
            }, processExecutor);

        });

        // 3. 等待批次商品同步处理完成
        processCountDownLatch.await();
    }

    public void syncProduct(List<EcCangERPGetProductListResponse> productList) {
        productList.forEach(sku -> {
            ThirdPartyMappingInfo newestInfoByTarget = thirdPartyMappingManager.getNewestInfoByTarget("ECCANG", "SHENGWEI", sku.getProductSku(), ThirdPartyMappingType.PRODUCT_ID);
            if (ObjectUtils.isNotEmpty(newestInfoByTarget)) return;
            try {
                String spuId = purchaseRpc.syncProductAndSupplier(buildSpuSkuSupplierCommand(sku));
                thirdPartyMappingManager.insertOrUpdate("SHENGWEI", "ECCANG", spuId, sku.getProductSku(), ThirdPartyMappingType.PRODUCT_ID.name(), sku);
                if (spuId != null) {
                    //打标签
                    SkuTagsCommand skuTagsCommand = new SkuTagsCommand();
                    skuTagsCommand.setSku(sku.getProductSku());
                    skuTagsCommand.setTagsName(EcCangERPMapping.SaleStatusNameMapping.get(sku.getSaleStatus()));
                    purchaseRpc.saveSkuTags(skuTagsCommand);
                }
            } catch (Exception e) {
                log.error("ecang创建商品失败：{}，skuid:{}", e.getMessage(), sku.getProductSku());
            }

        });
    }

    public SpuSkuSupplierCommand buildSpuSkuSupplierCommand(EcCangERPGetProductListResponse response) {
        SpuSkuSupplierCommand spuSkuSupplierCommand = new SpuSkuSupplierCommand();
        ThirdPartyMappingInfo userMapping = thirdPartyMappingManager.getNewestInfoByTarget("ECCANG", "GSP", String.valueOf(response.getPersonOpraterId()), ThirdPartyMappingType.USER_ID);
        //ThirdPartyMappingInfo userMapping = thirdPartyMappingManager.getNewestInfoByTarget("ECCANG", "GSP", "944", ThirdPartyMappingType.USER_ID);
        //采购员
        if (userMapping == null) {
            spuSkuSupplierCommand.setPurchaser("黄郭兵");
        } else {
            CommonResponse<UserDTO> user = userRpc.getUser(userMapping.getSourceId());
            spuSkuSupplierCommand.setPurchaser(!user.isSuccess() ? "黄郭兵" : user.getBody().getNickName());
        }

        //SPU编码
        spuSkuSupplierCommand.setSpu(response.getProductSku());

        //商品标题
        spuSkuSupplierCommand.setTitle(response.getProductTitle());

        //商品图片
        String productImages = response.getProductImages();
        if (StringUtils.isNotBlank(productImages)) {
            String[] split = productImages.split(",");
            spuSkuSupplierCommand.setSpuImgUrl(split[0]);
            spuSkuSupplierCommand.setSkuImgUrl(split[0]);
        }

        //sku编码和名称
        spuSkuSupplierCommand.setSku(response.getProductSku());
        spuSkuSupplierCommand.setSkuName(response.getProductTitle());
        //参考号(用的供应商品号)
        spuSkuSupplierCommand.setRefSkuId(response.getSupplierSku());

        //重量信息
        //单位换算
        BigDecimal bigDecimal = new BigDecimal("1000");
        BigDecimal productNetWeight = response.getProductNetWeight();
        BigDecimal productWeight = response.getProductWeight();
        spuSkuSupplierCommand.setNetWeight(productNetWeight == null ? BigDecimal.ZERO.toString() : productNetWeight.multiply(bigDecimal).toString());
        spuSkuSupplierCommand.setGrossWeight(productWeight == null ? BigDecimal.ZERO.toString() : productWeight.multiply(bigDecimal).toString());

        //尺寸
        BigDecimal productLength = response.getProductLength();
        BigDecimal productWidth = response.getProductWidth();
        BigDecimal productHeight = response.getProductHeight();
//        spuSkuSupplierCommand.setSizeHeight(productHeight == null ? BigDecimal.ZERO : productHeight);
//        spuSkuSupplierCommand.setSizeWidth(productWidth == null ? BigDecimal.ZERO : productWidth);
//        spuSkuSupplierCommand.setSizeLength(productLength == null ? BigDecimal.ZERO : productLength);

        //sku销售状态
//        spuSkuSupplierCommand.setSalesStatus(EcCangERPMapping.SaleStatusMapping.get(response.getSaleStatus()));

        //供应商名称
        ThirdPartyMappingInfo supplierMapping = thirdPartyMappingManager.getNewestInfoByTarget("ECCANG", "SHENGWEI", response.getDefaultSupplierCode(), ThirdPartyMappingType.GSP_EC_SUP_N_C);
        spuSkuSupplierCommand.setSupplierName(supplierMapping.getSourceId());

        //平台
        spuSkuSupplierCommand.setPlatform("ECANG");

        //商家类目名称
        CategoryInfo categoryInfo = purchaseRpc.getCategoryById(categoryId);
        spuSkuSupplierCommand.setCategoryName(categoryInfo.getName());

        //规格
        String productSpecs = response.getProductSpecs();
        if (StringUtils.isNotBlank(productSpecs)) {
            productSpecs = productSpecs.replace("'", "");
            productSpecs = productSpecs.replace("]", "");
            productSpecs = productSpecs.replace("[", "");
            productSpecs = productSpecs.replace("}", "");
            productSpecs = productSpecs.replace("{", "");
            String[] split = productSpecs.split(",");
            StringBuilder specName = new StringBuilder();
            StringBuilder specValue = new StringBuilder();
            for (String specStr : split) {
                String[] spec = specStr.split(":");
                if (spec.length <= 1) {
                    continue;
                }
                specName.append(spec[0]).append("+");
                specValue.append(spec[1]).append("+");
            }
            spuSkuSupplierCommand.setSkuAttrName(specName.deleteCharAt(specName.length() - 1).toString());
            spuSkuSupplierCommand.setSkuAttrValue(specValue.deleteCharAt(specValue.length() - 1).toString());
        }

        //参考价（取得供应商产品单价）
        spuSkuSupplierCommand.setReferenceCost(response.getSp_unit_price() == null ? BigDecimal.ZERO.toString() : response.getSp_unit_price().toString());

        //申报名称，申报价值
        spuSkuSupplierCommand.setZhDeclaredName(response.getPdOverseaTypeCn());
        spuSkuSupplierCommand.setEnDeclaredName(response.getPdOverseaTypeEn());
        if (response.getProductDeclaredValue() != null) {
            spuSkuSupplierCommand.setDeclaredPrice(response.getProductDeclaredValue());
        }

        //最小采购两
        spuSkuSupplierCommand.setMinPurchaseQuantity("1");

        //参考链接
        if (StringUtils.isNotBlank(response.getRefUrl()) && response.getRefUrl().contains("1688")) {
            spuSkuSupplierCommand.setAliLink(response.getRefUrl());
        }

        return spuSkuSupplierCommand;
    }

    public SpuCreateCommand buildSpuCreateCommand(EcCangERPGetProductListResponse response, Map<String, ThirdPartyMappingInfo> categoryMapping) {

        //ThirdPartyMappingInfo supplierMapping = thirdPartyMappingManager.getNewestInfoByTarget("ECCANG", "SHENGWEI", response.getDefaultSupplierCode(), ThirdPartyMappingType.SUPPLIER_ID);
        ThirdPartyMappingInfo userMapping = thirdPartyMappingManager.getNewestInfoByTarget("ECCANG", "SHENGWEI", String.valueOf(response.getPersonOpraterId()), ThirdPartyMappingType.USER_ID);
        //UserDTO user = userRpc.getUser(userMapping.getSourceId());
        SpuCreateCommand spuCreateCommand = new SpuCreateCommand();
        spuCreateCommand.setCustomCode(response.getProductSku());
        spuCreateCommand.setSpu(response.getProductSku());
        spuCreateCommand.setTitle(response.getProductTitle());
        //spuCreateCommand.setPurchaser(user.getNickName());
        spuCreateCommand.setBrandId(response.getBrandCode());
        spuCreateCommand.setZhDeclaredName(response.getPdOverseaTypeCn());
        spuCreateCommand.setEnDeclaredName(response.getPdOverseaTypeEn());
        spuCreateCommand.setDeclaredPrice(response.getProductDeclaredValue());
        spuCreateCommand.setPlatform("ECANG");
        //商家类目名称
        CategoryInfo categoryInfo = purchaseRpc.getCategoryById(categoryId);
        SpuCategoryInfo spuCategoryInfo = new SpuCategoryInfo();
        spuCategoryInfo.setCategoryId(categoryInfo.getCategoryId());
        spuCategoryInfo.setCategoryName(categoryInfo.getName());
        spuCreateCommand.setCategoryInfo(spuCategoryInfo);

        //主图
        String productImages = response.getProductImages();
        List<MultimediaInfo> mainImages = new ArrayList<>();
        if (StringUtils.isNotBlank(productImages)) {
            String[] split = productImages.split(",");
            for (String Image : split) {
                MultimediaInfo multimediaInfo = new MultimediaInfo();
                multimediaInfo.setType(MultimediaType.IMAGE);
                multimediaInfo.setFileUrl(Image);
                mainImages.add(multimediaInfo);
            }
        }
        spuCreateCommand.setMainImages(mainImages);

        //sku
        //spuCreateCommand.setSkus(buildSkus(response, user));
        //操作人
        spuCreateCommand.setOperator("系统推送");
        //文本描述
        List<SpuTextDesc> textDescs = new ArrayList<>();
        SpuTextDesc spuTextDesc = new SpuTextDesc("zh_CN", response.getPdDesc());
        textDescs.add(spuTextDesc);
        spuCreateCommand.setTextDescs(textDescs);

        return spuCreateCommand;
    }

    private List<SkuCreateCommand> buildSkus(EcCangERPGetProductListResponse response, UserDTO user) {
        List<SkuCreateCommand> skus = new ArrayList<>();
        SkuCreateCommand skuCreateCommand = new SkuCreateCommand();

        skuCreateCommand.setSku(response.getProductSku());
        skuCreateCommand.setCustomCode(response.getProductSku());
        skuCreateCommand.setSkuName(response.getProductTitle());
        skuCreateCommand.setSkuAttrName("颜色");
        skuCreateCommand.setSkuAttrValue(response.getProductColorName());

        skuCreateCommand.setPurchaser(user.getNickName());
        skuCreateCommand.setRefProductLink(response.getRefUrl());
        skuCreateCommand.setSalesStatus(EcCangERPMapping.SaleStatusMapping.get(response.getSaleStatus()));
        skuCreateCommand.setReferenceCost(response.getSp_unit_price());
        skuCreateCommand.setPlatform("ECANG");

        //重量尺寸
        BigDecimal bigDecimal = new BigDecimal("100");
        if (ObjectUtils.isNotEmpty(response.getProductNetWeight())) {
            skuCreateCommand.setNetWeight(response.getProductNetWeight());
        }

        if (ObjectUtils.isNotEmpty(response.getProductWeight())) {
            skuCreateCommand.setGrossWeight(response.getProductWeight());
        }

        if (ObjectUtils.isNotEmpty(response.getProductLength())) {
            skuCreateCommand.setPackingLength(response.getProductLength().divide(bigDecimal, 3, RoundingMode.HALF_UP));
        }

        if (ObjectUtils.isNotEmpty(response.getProductWidth())) {
            skuCreateCommand.setPackingWidth(response.getProductWidth().divide(bigDecimal, 3, RoundingMode.HALF_UP));
        }

        if (ObjectUtils.isNotEmpty(response.getProductHeight())) {
            skuCreateCommand.setPackingHeight(response.getProductHeight().divide(bigDecimal, 3, RoundingMode.HALF_UP));
        }

        //规格
        SkuMainSpecInfo mainSpecInfo = new SkuMainSpecInfo();
        if (StringUtils.isNotBlank(response.getProductImages())) {
            MultimediaInfo image = new MultimediaInfo();
            String[] split = response.getProductImages().split(",");
            image.setType(MultimediaType.IMAGE);
            image.setFileUrl(split[0]);
            mainSpecInfo.setImage(image);
        }
        skuCreateCommand.setMainSpecInfo(mainSpecInfo);

        List<SkuSpecInfo> specs = new ArrayList<>();
        String productSpecs = response.getProductSpecs();
        if (StringUtils.isNotBlank(productSpecs)) {
            productSpecs = productSpecs.replace("'", "");
            productSpecs = productSpecs.replace("]", "");
            productSpecs = productSpecs.replace("[", "");
            productSpecs = productSpecs.replace("}", "");
            productSpecs = productSpecs.replace("{", "");
            String[] split = productSpecs.split(",");
            for (String specStr : split) {
                String[] spec = specStr.split(":");
                SkuSpecInfo skuSpecInfo = new SkuSpecInfo();
                skuSpecInfo.setSpecName(spec[0]);
                skuSpecInfo.setSpecValue(spec[1]);
                specs.add(skuSpecInfo);
                if (mainSpecInfo.getSpec() == null) {
                    mainSpecInfo.setSpec(skuSpecInfo);
                }
            }
            skuCreateCommand.setSpecs(specs);
        }
        skus.add(skuCreateCommand);
        return skus;
    }


    //获取供应商信息进行映射
    @Job("autoSyncEcSupplierInfoMapping")
    public ReturnT<String> syncEcSupplierInfoMapping(String param) {
        log.info("同步易仓供应商任务开始, param={}", param);
        try {
            JSONObject paramObject = JSON.parseObject(param);
            thirdPartySystemId = paramObject.getString("thirdPartySystemId");
            String sourceBizId = paramObject.getString("sourceBizId");
            String targetBizId = paramObject.getString("targetBizId");
            ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
            EcCangApiBaseResult<String> supplierResponse = eccangERPApiSveImpl.getSupplier(thirdPartySystem);

            if (!"200".equals(supplierResponse.getCode())) {
                log.error("易仓供应商信息获取失败：message:{}", supplierResponse.getMessage());
                return ReturnT.SUCCESS;
            }

            if (StringUtils.isBlank(supplierResponse.getData())) {
                log.error("易仓供应商信息为空：data:{}", supplierResponse.getData());
                return ReturnT.SUCCESS;
            }

            Map<String, EcCangERPGetSupplierResponse> map = JSONObject.parseObject(supplierResponse.getData(), new TypeReference<Map<String, EcCangERPGetSupplierResponse>>() {
            });
            for (Map.Entry<String, EcCangERPGetSupplierResponse> next : map.entrySet()) {
                EcCangERPGetSupplierResponse value = next.getValue();
                ThirdPartyMappingInfo newestInfoByTarget = thirdPartyMappingManager.getNewestInfoByTarget(targetBizId, sourceBizId, String.valueOf(value.getSupplier_id()), ThirdPartyMappingType.GSP_EC_SUP_N_ID);
                if (newestInfoByTarget != null) {
                    continue;
                }
                thirdPartyMappingManager.insertOrUpdate(sourceBizId, targetBizId, value.getSupplier_name(), value.getSupplier_code(), ThirdPartyMappingType.GSP_EC_SUP_N_C.name(), null);
                thirdPartyMappingManager.insertOrUpdate(sourceBizId, targetBizId, value.getSupplier_name(), String.valueOf(value.getSupplier_id()), ThirdPartyMappingType.GSP_EC_SUP_N_ID.name(), value);
            }
        } catch (Exception e) {
            log.error("易仓供应商信息映射失败: {}", e.getMessage(), e);
        }
        log.info("供应商映射完成");
        return ReturnT.SUCCESS;
    }


    @Resource
    private GSPPurchaseOrderTakeStockProcessor gspPurchaseOrderTakeStockProcessor;

//    @Value("${purchaseOrders2EcCang.purchaseOrdersInfo}")
//    private String purchaseOrdersInfo;

    //定时消费漏掉的采购单（手动配置）
    @Job("autoSyncPurchaseOrders2EcCang")
    public ReturnT<String> syncPurchaseOrders2EcCang(String purchaseOrdersInfo) {
        log.info("采购单重新推送到易仓开始：{}", purchaseOrdersInfo);
        try {
            if (StringUtils.isBlank(purchaseOrdersInfo)) {
                log.info("采购单信息为空：{}", purchaseOrdersInfo);
                return ReturnT.SUCCESS;
            }

            List<GspSyncPo2EccangCommand> commands = JSON.parseArray(purchaseOrdersInfo, GspSyncPo2EccangCommand.class);

            if (CollectionUtils.isEmpty(commands)) {
                log.info("采购单信息为空：{}", purchaseOrdersInfo);
                return ReturnT.SUCCESS;
            }
            //执行同步逻辑
            commands.forEach(command -> {
                if (StringUtils.isBlank(command.getPurchaseOrderId())) {
                    log.info("采购单ID为空：{}", command);
                    return;
                }
                eccangPoMgmtApp.doSync(command);
            });

            log.info("采购单重新推送到易仓完成");
        } catch (Exception e) {
            log.error("采购单同步易仓失败：{}", purchaseOrdersInfo);
        }
        return ReturnT.SUCCESS;
    }

    //更新绑定供应商链接
    @Job("autoSyncSkuSupplierLink")
    public ReturnT<String> syncSkuSupplierLink(String param) {
        log.info("同步易仓商品供应商链接任务开始, param={}", param);
        try {
            //根据参数获取需要执行的第三方系统id
            JSONObject paramObject = JSON.parseObject(param);
            String thirdPartySystemId = paramObject.getString("thirdPartySystemId");
            Integer perGroupCount = paramObject.getInteger("perGroupCount");

            //指定插入页数
            Integer insertPage = paramObject.getInteger("insertPage");
            //指定从第几页开始插入
            Integer indexPage = paramObject.getInteger("indexPage");
            //指定每页条数
            Integer pageSize = paramObject.getInteger("pageSize");
            //根据thirdPartySystemId获取第三方系统参数
            ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
            EcCangERPParams params = JSON.parseObject(thirdPartySystem.getParams(), EcCangERPParams.class);
            thirdPartySystem.setParams(JSON.toJSONString(params));
            EcCangERPGetProductListRequest ecCangERPGetProductListRequest = new EcCangERPGetProductListRequest();
            ecCangERPGetProductListRequest.setPageSize(pageSize);
            List<EcCangERPGetProductListResponse> productList = null;
            //当前页数
            int currentPage = 0;
            if (indexPage != null && indexPage > 0) {
                currentPage = indexPage - 1;
            }
            //Map<String, ThirdPartyMappingInfo> categoryMapping = null;
            do {
                ecCangERPGetProductListRequest.setPage(currentPage += 1);
                try {
                    EcCangApiBaseResult<String> productListStr = eccangERPApiSveImpl.getProductList(thirdPartySystem, ecCangERPGetProductListRequest);
                    assert productListStr != null;
                    if (StringUtils.isBlank(productListStr.getData())) continue;
                    productList = JSON.parseArray(productListStr.getData(), EcCangERPGetProductListResponse.class);
                } catch (Exception e) {
                    log.error("易仓商品供应商链接同步任务异常！e={}", e.getMessage(), e);
                    log.info("当前页数为：{}", currentPage);
                    continue;
                }

                if (CollectionUtils.isEmpty(productList)) {
                    continue;
                }

                try {
                    processBatch2(productList, perGroupCount, thirdPartySystem);
                } catch (Exception e) {
                    log.error("[{}] 易仓商品供应商链接同步任务异常！e={}", "易仓商品同步任务", e);
                }

                if (insertPage != null && insertPage > 0 && currentPage - (indexPage == null ? 0 : indexPage) >= insertPage) {
                    return ReturnT.SUCCESS;
                }
                log.info("当前页数为：{}", currentPage);
            } while (CollectionUtils.isNotEmpty(productList));
            log.info("易仓商品供应商链接同步完成");
        } catch (Exception e) {
            log.error("[{}] 易仓商品供应商链接同步任务异常！e={}", "易仓商品同步任务", e);
            return new ReturnT<>(500, e.getMessage());
        }
        return ReturnT.SUCCESS;
    }

    private void processBatch2(List<EcCangERPGetProductListResponse> productList, Integer perGroupCount, ThirdPartySystem thirdPartySystem) throws Exception {
        final Optional<TenantID> tenantIdKept = TenantCarrier.getTenantID();

        //获取单个商品信息
        List<String> collect = productList.stream().map(EcCangERPGetProductListResponse::getProductSku).collect(Collectors.toList());
        EcCangERPGetProductBySkuRequest ecCangERPGetProductBySkuRequest = new EcCangERPGetProductBySkuRequest();
        ecCangERPGetProductBySkuRequest.setProductSku(collect);
        long startTime = System.currentTimeMillis();
        EcCangApiBaseResult<String> result = eccangERPApiSveImpl.getProductBySku(thirdPartySystem, ecCangERPGetProductBySkuRequest);
        if (!"200".equals(result.getCode())) {
            log.info("获取商品失败：{}", result.getMessage());
            return;
        }
        List<EcCangERPGetProductBySkuResponse> ecCangERPGetProductBySkuResponses = JSON.parseArray(result.getData(), EcCangERPGetProductBySkuResponse.class);

        if (CollectionUtils.isEmpty(ecCangERPGetProductBySkuResponses)) {
            return;
        }
        // 1. 批量任务分组
        //List<String> skuIdList = productList.stream().map(EcCangERPGetProductListResponse::getProductSku).collect(Collectors.toList());
        List<List<EcCangERPGetProductBySkuResponse>> groupSkuIds = ListUtils.partition(ecCangERPGetProductBySkuResponses, perGroupCount);
        CountDownLatch processCountDownLatch = new CountDownLatch(groupSkuIds.size());

        // 2. 线程池执行
        groupSkuIds.forEach(groupItem -> {
            CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> {
                boolean tenantIdMiss = false;
                try {
                    if (!TenantCarrier.getTenantID().isPresent()) {
                        TenantID tenantID = tenantIdKept.orElse(null);
                        TenantCarrier.setTenantID(tenantID);
                        tenantIdMiss = tenantID != null;
                    }
                    groupItem.forEach(item -> {
                        UpdateSkuAlilinkCommand updateSkuAlilinkCommand = new UpdateSkuAlilinkCommand();
                        updateSkuAlilinkCommand.setCustomCode(item.getProductSku());
                        if (CollectionUtils.isEmpty(item.getSpProductAddress())) {
                            return;
                        }
                        List<String> address = item.getSpProductAddress().stream().filter(addr -> addr.contains("1688")).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(address)) {
                            return;
                        }
                        ThirdPartyMappingInfo supplierMapping = thirdPartyMappingManager.getNewestInfoByTarget("ECCANG", "SHENGWEI", item.getDefaultSupplierCode(), ThirdPartyMappingType.GSP_EC_SUP_N_C);
                        updateSkuAlilinkCommand.setSupplierName(supplierMapping.getSourceId());
                        updateSkuAlilinkCommand.setAliLink(address.get(0));

/*                        try {
                            //设置供应商信息 通过爬虫获取到返回
                            Map<String, String> params = new HashMap<>();
                            params.put("productId",getProductIdFromUrl(updateSkuAlilinkCommand.getAliLink()));
                            ApiBaseResult apiBaseResult = syncGetMethod(getSupplierInfoUrl,0,null,null,null,params);
                            JSONObject jsonObject = JSONObject.parseObject(apiBaseResult.getRet());
                            if (jsonObject.get("code").equals("0")) {
                                //成功获取到1688供应商信息
                                updateSkuAlilinkCommand.setAliStoreLink(jsonObject.getJSONObject("data").getString("winportUrl"));
                                //updateSkuAlilinkCommand.setSupplierName(jsonObject.getJSONObject("data").getString("companyName"));
                            }
                        } catch (Exception e) {
                            log.info("供应商链接获取失败：{}",e.getMessage());
                        }*/
//                        purchaseRpc.updateAli(updateSkuAlilinkCommand);
                    });
                } catch (Exception e) {
                    log.error("[{}]！e={}", "易仓商品供应商链接同步任务异常", e.getMessage(), e);
                } finally {
                    processCountDownLatch.countDown();
                    if (tenantIdMiss) {
                        TenantCarrier.clearTenantID();
                    }
                }
            }, processExecutor);

        });

        // 3. 等待批次商品同步处理完成
        processCountDownLatch.await();

        long endTime = System.currentTimeMillis();
        long diffTime = endTime - startTime;
        long ecInterfaceInvokeLimitTime = 5 * 1000L;
        if (diffTime < ecInterfaceInvokeLimitTime) {
            try {
                Thread.sleep(ecInterfaceInvokeLimitTime - diffTime + 1 * 1000L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    private String getProductIdFromUrl(String url) {
        String productId = StringUtils.EMPTY;
        Matcher m1 = Pattern.compile("(offer/)([0-9]*)(\\.html)").matcher(url);
        while (m1.find()) {
            productId = m1.group(2);
            if (StringUtils.isNotBlank(productId)) {
                break;
            }
        }
        return productId;
    }

    @Job("syncPurchaseGoodsChange")
    public ReturnT<String> syncPurchaseGoodsChange(String param) {
        StopWatch stopWatch = new StopWatch("同步采购系统商品变更补充任务");
        stopWatch.start("同步采购系统商品变更开始执行");
        JSONObject jsonObject = JSONObject.parseObject(param);
        if (Objects.nonNull(jsonObject.get("syncDate"))) {
            Integer syncDate = jsonObject.getInteger("syncDate");
            //TODO 查找指定天数内的商品表修改时间，获取对应商品表数据

        }
        stopWatch.stop();
        log.info("定时任务", stopWatch.prettyPrint());
        return ReturnT.SUCCESS;
    }

    private ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }
}
