package com.newnary.gsp.center.tpsi.service.tongtu.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.newnary.gsp.center.tpsi.infra.client.tongtu.TongTuApiClient;
import com.newnary.gsp.center.tpsi.infra.client.tongtu.valobj.TongTuApiBaseResult;
import com.newnary.gsp.center.tpsi.service.tongtu.ITongTuListingSve;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class TongTuListingSveImpl implements ITongTuListingSve {

    public static final Logger LOGGER = LoggerFactory.getLogger(TongTuListingSveImpl.class);

    @Override
    public void doGetListingSaleProduct(TongTuApiClient tongTuApiClient, Object item) {
        JSONObject object = (JSONObject) item;
        String sku = object.getString("sku");
        Map<String, Object> bodyParas = new HashMap<>();
        bodyParas.put("sku", sku);
        LOGGER.info("现在获{}的描述", sku);
        TongTuApiBaseResult<String> ret = tongTuApiClient.listingSaleProductQuery(bodyParas);
        if (ret.getCode().equals("200")) {
            if (ret.getDatas().equals("[]")) {
                LOGGER.info("请求成功,但缺失数据");
            } else {
                LOGGER.info("获取成功");
                //处理数据
                JSONArray listingSaleProductArrays = JSONArray.parseArray(ret.getDatas());
                object.put("describeList", listingSaleProductArrays.getJSONObject(0).getString("describeList"));
            }
        }
    }
}
