package com.newnary.gsp.center.tpsi.service.lingxing.impl;

import com.alibaba.fastjson.JSON;
import com.newnary.api.base.exception.BaseErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.tpsi.infra.client.lingxing.LingXingApiClient;
import com.newnary.gsp.center.tpsi.infra.client.lingxing.params.LingXingDataParam;
import com.newnary.gsp.center.tpsi.service.lingxing.ILingXingApiService;
import com.newnary.gsp.center.tpsi.service.lingxing.request.QueryLingXingProductInfoListRequest;
import com.newnary.gsp.center.tpsi.service.lingxing.request.QueryLingXingProductInfoRequest;
import com.newnary.gsp.center.tpsi.service.lingxing.request.QueryLingXingProductListRequest;
import com.newnary.gsp.center.tpsi.service.lingxing.response.QueryLingXingProductInfoListResponse;
import com.newnary.gsp.center.tpsi.service.lingxing.response.QueryLingXingProductInfoResponse;
import com.newnary.gsp.center.tpsi.service.lingxing.response.QueryLingXingProductListResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class ILingXingApiServiceImpl implements ILingXingApiService {

    @Override
    public QueryLingXingProductListResponse queryProductList(LingXingDataParam lingXingDataParam, QueryLingXingProductListRequest request) {
        LingXingApiClient lingXingApiClient = new LingXingApiClient(lingXingDataParam);
        Map<String, Object> body = new HashMap<>();
        body.put("offset", request.getOffset());
        body.put("length", request.getLength());
        String result = lingXingApiClient.executePost("erp/sc/routing/data/local_inventory/productList", body);
        if (StringUtils.isNotBlank(result)) {
            return JSON.parseObject(result, QueryLingXingProductListResponse.class);
        } else {
            throw new ServiceException(new BaseErrorInfo("LINGXING_PRODUCT_EXCEPTION","领星获取商品异常"));
        }
    }

    @Override
    public QueryLingXingProductInfoListResponse queryProductInfoList(LingXingDataParam lingXingDataParam, QueryLingXingProductInfoListRequest request) {
        LingXingApiClient lingXingApiClient = new LingXingApiClient(lingXingDataParam);
        Map<String, Object> body = new HashMap<>();
        body.put("skus", JSON.toJSONString(request.getSkus()));
        String result = lingXingApiClient.executePost("erp/sc/routing/data/local_inventory/batchGetProductInfo", body);
        if (StringUtils.isNotBlank(result)) {
            return JSON.parseObject(result, QueryLingXingProductInfoListResponse.class);
        } else {
            throw new ServiceException(new BaseErrorInfo("LINGXING_PRODUCT_EXCEPTION","领星获取商品异常"));
        }
    }

    @Override
    public QueryLingXingProductInfoResponse queryProductInfo(LingXingDataParam lingXingDataParam, QueryLingXingProductInfoRequest request) {
        LingXingApiClient lingXingApiClient = new LingXingApiClient(lingXingDataParam);
        Map<String, Object> body = new HashMap<>();
        body.put("sku", request.getSku());
        String result = lingXingApiClient.executePost("erp/sc/routing/data/local_inventory/productInfo", body);
        if (StringUtils.isNotBlank(result)) {
            return JSON.parseObject(result, QueryLingXingProductInfoResponse.class);
        } else {
            throw new ServiceException(new BaseErrorInfo("LINGXING_PRODUCT_EXCEPTION","领星获取商品异常"));
        }
    }
}
