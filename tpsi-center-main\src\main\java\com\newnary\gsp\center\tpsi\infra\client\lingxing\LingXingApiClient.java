package com.newnary.gsp.center.tpsi.infra.client.lingxing;

import com.alibaba.fastjson.JSON;
import com.asinking.com.openapi.sdk.core.Config;
import com.asinking.com.openapi.sdk.core.HttpMethod;
import com.asinking.com.openapi.sdk.core.HttpRequest;
import com.asinking.com.openapi.sdk.core.HttpResponse;
import com.asinking.com.openapi.sdk.okhttp.AKRestClientBuild;
import com.asinking.com.openapi.sdk.okhttp.HttpExecutor;
import com.asinking.com.openapi.sdk.sign.ApiSign;
import com.newnary.gsp.center.tpsi.infra.client.lingxing.params.LingXingDataParam;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

@Getter
@Setter
@Slf4j
public class LingXingApiClient{
    private String appId;
    private String appSecret;
    private String endpoint;

    public LingXingApiClient(LingXingDataParam lingXingDataParam) {
        setAppId(lingXingDataParam.getAppId());
        setAppSecret(lingXingDataParam.getAppSecret());
        setEndpoint(lingXingDataParam.getEndpoint());
    }

    public String executePost(String service,Map<String, Object> body) {
        Map<String, Object> queryParam = new HashMap<>();
        try {
            queryParam.put("timestamp", System.currentTimeMillis() / 1000 + "");
            queryParam.put("access_token", ((LinkedHashMap) AKRestClientBuild.builder().endpoint(endpoint).getAccessToken(appId, appSecret).getData()).get("access_token"));
            queryParam.put("app_key", appId);

            Map<String, Object> signMap = new HashMap<>();
            signMap.putAll(queryParam);
            signMap.putAll(body);

            String sign = ApiSign.sign(signMap, appId);
            queryParam.put("sign", sign);
            log.info("sign:{}", sign);

            HttpRequest<Object> build = HttpRequest.builder(Object.class)
                    .method(HttpMethod.POST)
                    .endpoint(endpoint)
                    .path(service)
                    .queryParams(queryParam)
                    .json(JSON.toJSONString(body))
                    .config(Config.DEFAULT.withConnectionTimeout(30000).withReadTimeout(30000))
                    .build();

            HttpResponse execute = HttpExecutor.create().execute(build);
            Object result = execute.readEntity(Object.class);
            return JSON.toJSONString(result);
        } catch (Exception e) {
            log.error("领星接口请求异常：{}",e.getMessage(),e);
            //e.printStackTrace();
        }
        return null;
    }
}
