package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.shopee;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingShopeeProductListRequest {

    /**
     * 当前页码(int 型)
     */
    private String current_page;

    /**
     * 站点(默认:马来西亚站)
     */
    @NotNull(message = "站点不能为空")
    private String station;

    /**
     * 标题(string型)
     */
    private String title;

    /**
     * 商品标题查询类型(int 型):
     * 1:整句语句(默认)
     * 2:多个搜索词,”与”关系
     * 3:多个搜索词,”或”关系
     */
    private String title_type;

    /**
     * 商品不包含标题(string型)
     */
    private String not_exist_title;

    /**
     * 商品不包含标题查询类型(int 型):
     * 1:整句语句(默认)
     * 2:多个搜索词,”与”关系
     * 3:多个搜索词,”或”关系
     */
    private String not_exist_title_type;

    /**
     * 商品id(string型)
     * (多个商品id用逗号分隔,单次最多500个商品id)
     */
    private String pid;

    /**
     * 店铺名称或用户名称
     */
    private String merchant;

    /**
     * 商品总价起始值(double型)
     */
    private String price_start;

    /**
     * 商品总价结束值(double型)
     */
    private String price_end;

    /**
     * 商品评分最小值(double型)
     */
    private String rating_min;

    /**
     * 商品评分最大值(double型)
     */
    private String rating_max;

    /**
     * 商品上架时间起始值(格式:年-月-日)
     */
    private String gen_time_from;

    /**
     * 商品上架时间结束值(格式:年-月-日)
     */
    private String gen_time_end;

    /**
     * 商品所属类目的路径:(string型)
     * 当传入子类目时，须同时给出所属的路径
     * 查询方式举例:
     * p_l1_id
     */
    private String p_l1_id;

    /**
     * 商品所属类目的路径:(string型)
     * 当传入子类目时，须同时给出所属的路径
     * 查询方式举例:
     * p_l1_id+p_l2_id
     */
    private String p_l2_id;

    /**
     * 商品所属类目的路径:(string型)
     * 当传入子类目时，须同时给出所属的路径
     * 查询方式举例:
     * p_l1_id+p_l2_id+p_l3_id
     */
    private String p_l3_id;

    /**
     * 收藏数起始值(int 型)
     */
    private String favorite_start;

    /**
     * 收藏数结束值(int 型)
     */
    private String favorite_end;

    /**
     * 评分数起始值(int 型)
     */
    private String ratings_start;

    /**
     * 评分数结束值(int 型)
     */
    private String ratings_end;

    /**
     * 店铺所在地(默认全部)：(string型)
     * 1:海外
     * 0:本地
     */
    private String shipping_icon_type;

    /**
     * 虾皮优选:(int 型)
     * 0:非优选   1:优选
     */
    private String is_shopee_verified;

    /**
     * 前30天销售件数起始值(int 型)
     */
    private String sold_start;

    /**
     * 前30天销售件数结束值(int 型)
     */
    private String sold_end;

    /**
     * 前30天销售金额起始值(double型)
     */
    private String payment_start;

    /**
     * 前30天销售金额结束值(double型)
     */
    private String payment_end;

    /**
     * 商品总销售件数起始值(int 型)
     */
    private String historical_sold_start;

    /**
     * 商品总销售件数结束值(int 型)
     */
    private String historical_sold_end;

    /**
     * 统计时间起始值(格式:年-月-日时:分:秒)
     */
    private String stat_time_start;

    /**
     * 统计时间结束值(格式:年-月-日时:分:秒)
     */
    private String stat_time_end;

    /**
     * 商品店铺id(多个以逗号分隔)
     */
    private String shop_ids;

    /**
     * 商品不包含的店铺id(多个以逗号分隔)
     */
    private String not_exist_shop_ids;

    /**
     * 商品是否热销(int 型)(预留字段)
     * 0:非热销   1:热销
     */
    private String is_hot_sales;

    /**
     * 商品所属店铺是否官方店铺(int 型)
     * 0:否   1:是
     */
    private String is_official_shop;

    /**
     * 每一页的商品数(默认海鹰设置)(int 型)
     * 数值范围[1-1000]
     */
    private String page_size;

    /**
     * 类目id查询，多个类目id，英文分号(;)分隔
     * 查询方式举例:
     * AAA,BBB,CCC;ABC,BBC
     */
    private String cids;

    /**
     * 店铺开张时间起始值(格式:年-月-日)
     */
    private String approved_date_start;

    /**
     * 店铺开张时间结束值(格式:年-月-日)
     */
    private String approved_date_end;

    /**
     * 最新抓取时间起始值(格式:年-月-日)
     */
    private String last_modi_time_start;

    /**
     * 最新抓取时间结束值(格式:年-月-日)
     */
    private String last_modi_time_end;

    /**
     * 排序方式:
     * rating(评分)
     * price(价格)
     * historical_sold(商品总销售件数)
     * sold(前30天销售件数)
     * payment(前30天销售金额)
     * favorite(Favorite数)
     * ratings(Ratings数)
     * gen_time(商品上架时间)
     */
    private String order_by;

    /**
     * 排序类型:
     * ASC 升序
     * DESC 降序
     */
    private String order_by_type;

}
