package com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class QueryOpen1688JXHYProductResponse {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误码
     */
    private String code ;

    /**
     * 错误信息
     */
    private String message ;

    /**
     * 结果
     */
    private Result result;

    @Getter
    @Setter
    public static class Result{

        /**
         * 页码
         */
        private Integer pageIndex;

        /**
         * 总条数
         */
        private Integer totalRecords;

        /**
         * 每页大小
         */
        private Integer sizePerPage;

        /**
         * 结果列表
         */
        private List<QueryResult> resultList;

        @Getter
        @Setter
        public static class QueryResult{

            /**
             * 商品id
             */
            private Long itemId;

            /**
             * 图片链接
             */
            private String imgUrl;

            /**
             * 商品标题
             */
            private String title;

            /**
             * 商品90天销量
             */
            private Integer salesCnt90d;

            /**
             * 最大价格（分）
             */
            private Long maxPrice;

            /**
             * 最小价格（分）
             */
            private Long minPrice;

            /**
             * 服务列表
             */
            private List<Service> serviceList;

            @Getter
            @Setter
            public static class Service{

                /**
                 * 名称码
                 */
                private String code;

                /**
                 * 名称
                 */
                private String name;
            }
        }
    }

}
