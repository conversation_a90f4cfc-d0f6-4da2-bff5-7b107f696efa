package com.newnary.gsp.center.tpsi.infra.model.creator;

import com.newnary.gsp.center.tpsi.infra.model.vo.SystemId;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2021/12/14 11:31
 */
@Getter
@Setter
public class ThirdPartyAddressMappingCreator {

    private Long id;

    /**
     * 系统Id
     */
    private SystemId systemId;

    /**
     * 内部邮编
     */
    private String virtualPostcode;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 国家
     */
    private String country;

    /**
     * 省州
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * 街/镇
     */
    private String town;

    private String address;
}
