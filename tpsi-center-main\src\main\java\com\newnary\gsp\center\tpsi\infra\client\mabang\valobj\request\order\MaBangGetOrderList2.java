package com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.order;

import lombok.Data;

import java.util.List;

@Data
public class MaBangGetOrderList2 {

    /**
     * 当前页
     */
    private Integer page;

    /**
     * 每页显示10 最大 2000
     */
    private Integer pageSize;

    /**
     * 标签页 1 未付款 2 待审核 3 待合并 4待处理 5 配货中 6 已发货 7 已妥投 8 已作废 9 FBA 80 真实发货同步失败 81 虚假发货同步失败 99 全部订单 272 分销待处理 273 分销待供应商发货
     */
    private Integer orderTab;

    /**
     * 2 待处理/配货中 3 已发货 4 已完成 5 作废
     */
    private Integer orderStatus;

    /**
     * 店铺id数组
     */
    private String shopIds;

    /**
     * 渠道id数组
     */
    private String myLogisticsChannelIds;

    /**
     * 自定义分类id数组
     */
    private String labels;

    /**
     * 自定义分类类型数组 数组长度必须与labels长度一致 一一对应
     */
    private String labelRanges;

    /**
     * 是否返回 订单商品 默认 false ，如果需要返回 传 true
     */
    private Integer showItems;

    /**
     * 平台Id数组
     */
    private List<String> platformIds;

    /**
     * 发货天数 -1 昨天 0 今天 其他大于0得数字 为 多少天内
     */
    private Integer sendDay;

    /**
     * 付款天数 -1 昨天 0 今天 其他大于0得数字 为 多少天内
     */
    private Integer paidDay;
}
