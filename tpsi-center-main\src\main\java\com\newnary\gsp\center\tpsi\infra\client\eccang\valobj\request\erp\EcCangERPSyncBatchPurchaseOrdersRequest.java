package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp;

import java.math.BigDecimal;
import java.util.List;

public class EcCangERPSyncBatchPurchaseOrdersRequest {

    public Integer po_platform_1688;
    public String currency_code;
    public Integer operator_purchase;
    public Integer operation_type;
    public Integer pts_oprater;
    public Integer po_type;
    public Double freight;
    public String ref_no;
    public String po_remark;

    public String warehouseCode;

    public List<ProductList> productList;

    public static class ProductList {
        public String 采购仓库代码;
        public String 中转仓代码;
        public String SKU;
        public BigDecimal 采购单价;
        public Integer 采购数量;
        public String 备注;
        public Integer 是否质检;
        public Integer 是否赠品;
        public String 供应商代码;
        public String 参考号;
        public String 预计到达日期;
        public String 采购员;
        public String 跟单员;
        public BigDecimal 采购参考价;
        public String 组织机构代码;
        public String 网采单号;
        public String 跟踪单号;
        public Integer 供应商运输方式;
        public Double 税率;
        public String 采购公司;
        public String 采购原因;
        public String 外部单号;
    }


}
