package com.newnary.gsp.center.tpsi.api.haiying.request.shopee;

import com.newnary.api.base.common.PageCondition;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingStation;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingShopeeKeywordInfoCommand {

    /**
     * 站点(默认:马来西亚站)
     */
    @NotNull(message = "站点不能为空")
    private HaiYingStation station;

    /**
     * 关键词(string型)
     */
    private String keyword;

    /**
     * 每一页的商品数(默认海鹰设置1000)(int 型)
     * 数值范围[1-5000]
     */
    private PageCondition pageCondition;

}
