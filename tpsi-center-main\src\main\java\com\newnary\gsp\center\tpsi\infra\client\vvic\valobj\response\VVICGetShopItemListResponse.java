package com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class VVICGetShopItemListResponse {
    /**
     * 返回语言
     */
    private String lang;

    /**
     * 返回商品列表
     */
    private List<Item> item_list;

    @Getter
    @Setter
    public static class Item{
        /**
         * 商品vid
         */
        private String item_vid;

        /**
         * 商品id，后续不再提供，建议使用item_vid
         */
        private Long item_id;

        /**
         * 商品标题
         */
        private String item_title;

        /**
         * 商品首图
         */
        private String item_view_image;

        /**
         * 创建时间
         */
        private String create_time;

        /**
         * 商品更新时间
         */
        private String update_time;

        /**
         * 商品上架时间
         */
        private String up_time;

        /**
         * 档口Vid
         */
        private String shop_vid;
    }
}
