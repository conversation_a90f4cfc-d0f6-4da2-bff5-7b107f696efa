package com.newnary.gsp.center.tpsi.infra.client.tk.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TKAfterSaleOrderFetchReq {
    public Integer update_time_from;
    public Integer update_time_to;
    /**
     * REFUND_ONLY = 2;
     * RETURN_AND_REFUND = 3;
     * REQUEST_CANCEL = 4;
     */
    public Integer reverse_type;
    /**
     * Available value:
     * REQUEST_TIME = 0; (default)
     * UPDATE_TIME = 1;
     * REFUND_TOTAL = 2;
     */
    public Integer sort_by;
    /**
     * Available value:
     * ASCE = 0;
     * DESC = 1; (default)
     */
    public Integer sort_type;
    /**
     *
     * Use this field to specify the offset of the order. Must be greater than or equal to 0.
     * If "offset" is less than 0, then "offset" is assigned the default value of 0.
     */
    public Integer offset;
    public Integer size;
    /**
     * Available value: AFTERSALE_APPLYING = 1;
     * AFTERSALE_REJECT_APPLICATION = 2;
     * AFTERSALE_RETURNING = 3;
     * AFTERSALE_BUYER_SHIPPED = 4;
     * AFTERSALE_SELLER_REJECT_RECEIVE = 5;
     * AFTERSALE_SUCCESS = 50;
     * CANCEL_SUCCESS = 51;
     * CLOSED = 99;
     * COMPLETE = 100;
     */
    public Integer reverse_order_status;
    public Integer order_id;
    public Integer reverse_order_id;
}
