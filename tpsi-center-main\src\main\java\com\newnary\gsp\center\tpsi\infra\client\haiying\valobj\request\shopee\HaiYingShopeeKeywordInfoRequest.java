package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.shopee;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingShopeeKeywordInfoRequest {

    /**
     * 站点(默认:马来西亚站)
     */
    @NotNull(message = "站点不能为空")
    private String station;

    /**
     * 当前页码(int 型)(默认第一页)
     */
    private String current_page;

    /**
     * 每一页的商品数(默认海鹰设置1000)(int 型)
     * 数值范围[1-5000]
     */
    private String page_size;

    /**
     * 关键词(string型)
     */
    private String keyword;

}
