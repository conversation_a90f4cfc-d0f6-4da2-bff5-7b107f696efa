package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.ebay;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingEbayProductDetailInfoRequest {

    /**
     * 站点
     */
    @NotNull(message = "站点不能为空")
    private String station;

    /**
     * 商品id(string型)
     * (多个商品id用英文逗号分隔,单次最多100个商品id)
     */
    @NotNull(message = "商品id不能为空")
    private String item_ids;

    /**
     * 商品数据时间起始值(string型,格式:年-月-日)
     */
    private String main_his_date_start;

    /**
     * 商品数据时间结束值(string型,格式:年-月-日)
     */
    private String main_his_date_end;

    /**
     * 商品销量数据时间起始值(string型,格式:年-月-日)
     */
    private String sold_his_date_start;

    /**
     * 商品销量数据时间结束值(string型,格式:年-月-日)
     */
    private String sold_his_date_end;

}
