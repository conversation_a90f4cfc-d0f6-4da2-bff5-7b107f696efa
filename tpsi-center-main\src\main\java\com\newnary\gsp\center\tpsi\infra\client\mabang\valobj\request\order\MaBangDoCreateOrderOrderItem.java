package com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.order;

import lombok.Data;

@Data
public class MaBangDoCreateOrderOrderItem {

    /**
     * 商品标题 (必填)
     */
    private String title;

    /**
     * 平台原始SKU (必填)
     */
    private String platformSku;

    /**
     * 商品数量 (必填)
     */
    private Integer quantity;

    /**
     * 商品图片,没有按照商品对应的数据" (必填)
     */
    private String pictureUrl;

    /**
     * 平台订单商品交易编号
     */
    private String itemId;

    /**
     * sellPrice
     */
    private String sellPrice;

    /**
     * 产品单位
     */
    private String productUnit;

    /**
     * 商品多属性
     */
    private String specifics;

    /**
     * 商品留言
     */
    private String message;

    /**
     * 商品链接
     */
    private String productUrl;

    /**
     * 商品交易号，指定平台需要可以根据业务判断
     */
    private String salesRecordNumber;

}
