package com.newnary.gsp.center.tpsi.infra.repository;

import com.newnary.dao.base.helper.DaoHelper;
import com.newnary.dao.base.po.BaseQuery;
import com.newnary.gsp.center.tpsi.infra.mapper.CrawlerProductMapper;
import com.newnary.gsp.center.tpsi.infra.model.CrawlerProduct;
import com.newnary.gsp.center.tpsi.infra.model.creator.CrawlerProductCreate;
import com.newnary.gsp.center.tpsi.infra.repository.db.dao.CrawlerProductDao;
import com.newnary.gsp.center.tpsi.infra.repository.db.dao.CrawlerProductReturnDao;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.CrawlerProductPO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class CrawProductRepository implements ICrawProductRepository {

    @Resource
    private CrawlerProductDao crawlerProductDao;

    @Resource
    private CrawlerProductReturnDao crawlerProductReturnDao;

    @Override
    public void store(CrawlerProduct crawlerProduct) {
        CrawlerProductPO po = CrawlerProductMapper.INSTANCE.model2PO(crawlerProduct);
        if (po.getId() == null) {
            crawlerProductDao.insert(po);
        } else {
            crawlerProductDao.update(po);
        }
    }




    public List<String> loadProductId(String categoryName) {
        BaseQuery<CrawlerProductPO> baseQuery = new BaseQuery(new CrawlerProductPO());
        baseQuery.getData().setCategoryName(categoryName);
        baseQuery.getData().setState(0);
        List<CrawlerProductPO> query = crawlerProductDao.query(baseQuery);
        return crawlerProductDao.query(baseQuery).stream().map(CrawlerProductPO::getProductId).collect(Collectors.toList());
    }

    @Override
    public Optional<CrawlerProduct> loadProduct(CrawlerProduct crawlerProduct) {
        BaseQuery<CrawlerProductPO> baseQuery = new BaseQuery(new CrawlerProductPO());
        baseQuery.getData().setProductId(crawlerProduct.getProductId());
        Optional<CrawlerProduct> crawlerProductOptional = crawlerProductDao.query(baseQuery).stream()
                .findAny()
                .map(po -> {
//                    CrawlerProductCreate create = CrawlerProductMapper.INSTANCE.model2PO(po);
                    return getCrawlerProduct(po);
                });
        return crawlerProductOptional;
    }

    private CrawlerProduct getCrawlerProduct(CrawlerProductPO po) {
        CrawlerProductCreate create = new CrawlerProductCreate()
                .setProductId(po.getProductId())
                .setState(po.getState())
                .setFlushState(po.getFlushState())
                .setFlushVersion(po.getFlushVersion())
                .setPageSize(po.getPageSize())
                .setCategoryName(po.getCategoryName())
                .setBeginPage(po.getBeginPage())
                .setCrawlerReturn(po.getCrawlerReturn())
                .setCrawlerReturnState(po.getCrawlerReturnState());
        return CrawlerProduct.loadWith(po.getId(), create);
    }

    @Override
    public void update(CrawlerProduct source) {
        CrawlerProductPO po = CrawlerProductMapper.INSTANCE.model2PO(source);
        crawlerProductReturnDao.updateByProductId(po);
    }


    @Override
    public List<CrawlerProduct> loadCrawlerProductByCategoryList(List<String> categoryList, Integer limit, Integer crawlerReturnState,Integer state,Integer flushState) {
        return crawlerProductReturnDao.queryByCategoryNameList(categoryList,limit,crawlerReturnState,state,flushState).stream().map(po -> {
            return getCrawlerProduct(po);
        }).collect(Collectors.toList());
    }

    @Override
    public void fillCrawlerReturn(CrawlerProduct source) {
        crawlerProductReturnDao.fillCrawlerProduct(source);
    }

    @Override
    public CrawlerProduct loadCrawlerDetailReturn(String productId) {
        return getCrawlerProduct(crawlerProductReturnDao.loadCrawlerDetailReturn(productId));
    }


    public static void main(String[] args) {
        DaoHelper.genXMLWithFeature(
                "E:\\data\\CrawlerProductDao.xml.bak2",
                CrawlerProductDao.class,
                CrawlerProductPO.class,
                "crawler_product",
                false);
    }

}
