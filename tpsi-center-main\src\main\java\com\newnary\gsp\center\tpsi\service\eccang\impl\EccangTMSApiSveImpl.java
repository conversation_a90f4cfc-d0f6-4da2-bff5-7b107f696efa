package com.newnary.gsp.center.tpsi.service.eccang.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.exception.BaseErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.logistics.api.delivery.dto.DeliveryOrderConsigneeInfoDTO;
import com.newnary.gsp.center.logistics.api.delivery.dto.DeliveryOrderConsignorInfoDTO;
import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderDetailInfo;
import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderDetailItemInfo;
import com.newnary.gsp.center.tpsi.infra.client.eccang.EcCangTMSApiClient;
import com.newnary.gsp.center.tpsi.infra.client.eccang.params.EcCangTMSParams;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangCreateOrderRequest;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.rpc.ShippingMethodRpc;
import com.newnary.gsp.center.tpsi.service.eccang.IEccangTMSApiSve;
import com.newnary.gsp.center.tpsi.service.impl.SystemClientSve;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2022/3/30
 */
@Slf4j
@Component
public class EccangTMSApiSveImpl extends SystemClientSve implements IEccangTMSApiSve {

    @Resource
    private ShippingMethodRpc shippingMethodRpc;

    private static Map<String, EcCangTMSApiClient> apiClientMap = new ConcurrentHashMap<>();

    private static EcCangTMSApiClient getApiClient(String params) {
        EcCangTMSParams paramsObj = JSON.parseObject(params, EcCangTMSParams.class);
        return apiClientMap.computeIfAbsent(paramsObj.getAPP_KEY(), k -> new EcCangTMSApiClient(paramsObj));
    }

    @Override
    public EcCangApiBaseResult<Object> createOrder(ThirdPartySystem thirdPartySystem, String transportOrderId, DeliveryOrderDetailInfo deliveryOrderDetailInfo, String param) {
        //获取apiClient
        EcCangTMSApiClient ecTMSApiClient = getApiClient(thirdPartySystem.getParams());

        EcCangCreateOrderRequest request = buildCreateOrderRequest(transportOrderId, deliveryOrderDetailInfo);
        String resultStr = ecTMSApiClient.sendRequest(request, "createOrder");
        return buildApiBaseResult(resultStr);
    }

    @Override
    public EcCangApiBaseResult<Object> queryLogisticsInfo(ThirdPartySystem thirdPartySystem, String transportOrderId, String systemParam) {
        //获取apiClient
        EcCangTMSApiClient ecTMSApiClient = getApiClient(thirdPartySystem.getParams());

        Map<String, Object> queryTrackNumberParam = new HashMap<>();
        queryTrackNumberParam.put("reference_no", Collections.singletonList(transportOrderId));
        String trackNumbers = ecTMSApiClient.sendRequest(queryTrackNumberParam, "getTrackNumber");
        return buildApiBaseResult(trackNumbers);
    }
    @Override
    public EcCangApiBaseResult<Object> queryLogisticsTrackInfo(ThirdPartySystem thirdPartySystem, String code, String systemParam) {
        //获取apiClient
        EcCangTMSApiClient ecTMSApiClient = getApiClient(thirdPartySystem.getParams());

        Map<String, Object> queryLogisticsTrackParam = new HashMap<>();
        queryLogisticsTrackParam.put("codes", Collections.singletonList(code));
        queryLogisticsTrackParam.put("type", 3);
        String logisticsTrack = ecTMSApiClient.sendRequest(queryLogisticsTrackParam, "getCargoTrack");
        return buildApiBaseResult(logisticsTrack);
    }
    @Override
    public EcCangApiBaseResult<Object> queryOrderInfo(ThirdPartySystem thirdPartySystem, String transportOrderId, String systemParam) {
        //获取apiClient
        EcCangTMSApiClient ecTMSApiClient = getApiClient(thirdPartySystem.getParams());

        Map<String, String> param = new HashMap<>();
        // 单号
        param.put("reference_no", transportOrderId);
        String resultStr = ecTMSApiClient.sendRequest(param, "getOrder");
        return buildApiBaseResult(resultStr);
    }

    @Override
    public EcCangApiBaseResult<Object> cancelOrder(ThirdPartySystem thirdPartySystem, String transportOrderId, String systemParam) {
        //获取apiClient
        EcCangTMSApiClient ecTMSApiClient = getApiClient(thirdPartySystem.getParams());

        Map<String, Object> cancelOrderParam = new HashMap<>();
        cancelOrderParam.put("reference_no", Collections.singletonList(transportOrderId));
        cancelOrderParam.put("type", 2);
        String resultStr = ecTMSApiClient.sendRequest(cancelOrderParam, "cancelOrder");
        return buildApiBaseResult(resultStr);
    }

    private EcCangCreateOrderRequest buildCreateOrderRequest(String transportOrderId, DeliveryOrderDetailInfo deliveryOrderDetailInfo) {
        EcCangCreateOrderRequest request = new EcCangCreateOrderRequest();
        // 订单基础信息
        request.reference_no = transportOrderId;

        String firstShippingMethod = deliveryOrderDetailInfo.getFirstShippingMethod();
        String lastShippingMethod = deliveryOrderDetailInfo.getLastShippingMethod();
        if (StringUtils.isBlank(firstShippingMethod) && StringUtils.isBlank(lastShippingMethod)) {
            log.info("构建创建wms订单请求失败：物流产品为空：{}", JSON.toJSONString(deliveryOrderDetailInfo));
            throw new ServiceException(new BaseErrorInfo("500","物流产品为空"));
        } else if (StringUtils.isBlank(firstShippingMethod)) {
            request.shipping_method = shippingMethodRpc.getLogisticsProductCodeById(lastShippingMethod);
        } else {
            request.shipping_method = shippingMethodRpc.getLogisticsProductCodeById(firstShippingMethod);
        }
        request.country_code = deliveryOrderDetailInfo.consigneeInfo.getAddrCountry();
        request.order_weight = Double.parseDouble(deliveryOrderDetailInfo.totalWeight);
        // 收货人信息
        DeliveryOrderConsigneeInfoDTO consigneeInfo = deliveryOrderDetailInfo.consigneeInfo;
        EcCangCreateOrderRequest.Consignee consignee = new EcCangCreateOrderRequest.Consignee();
        consignee.consignee_province = consigneeInfo.addrProvince;
        consignee.consignee_city = consigneeInfo.addrCity;
        consignee.consignee_district = consigneeInfo.addrDistrict;
        consignee.consignee_street = consigneeInfo.addrDetail;
        consignee.consignee_postcode = consigneeInfo.zipCode;
        consignee.consignee_name = consigneeInfo.name;
        consignee.consignee_telephone = consigneeInfo.contactNumber;
        consignee.consignee_mobile = consigneeInfo.contactNumber;
        consignee.consignee_email = consigneeInfo.contactEmail;
        request.Consignee = consignee;
        // 发件人信息
        DeliveryOrderConsignorInfoDTO consignorInfo = deliveryOrderDetailInfo.getGroupWarehouseAddrInfo();
        EcCangCreateOrderRequest.Shipper shipper = new EcCangCreateOrderRequest.Shipper();
        shipper.shipper_countrycode = consignorInfo.addrCountry;
        shipper.shipper_province = consignorInfo.addrProvince;
        shipper.shipper_city = consignorInfo.addrCity;
        shipper.shipper_street = consignorInfo.addrDistrict + consignorInfo.addrDetail;
        shipper.shipper_postcode = consignorInfo.zipCode;
        shipper.shipper_name = consignorInfo.name;
        shipper.shipper_telephone = consignorInfo.contactNumber;
        request.Shipper = shipper;
        // 商品信息
        List<DeliveryOrderDetailItemInfo> itemInfos = deliveryOrderDetailInfo.items;
        List<EcCangCreateOrderRequest.Item> items = new ArrayList<>();
        for (DeliveryOrderDetailItemInfo itemInfo : itemInfos) {
            EcCangCreateOrderRequest.Item item = new EcCangCreateOrderRequest.Item();
            item.invoice_enname = itemInfo.invoiceEnName;
            item.invoice_cnname = itemInfo.invoiceCnName;
            item.invoice_quantity = itemInfo.quantity;
            item.invoice_unitcharge = Double.parseDouble(itemInfo.supplyPrice);
            item.invoice_weight = itemInfo.invoiceUnitWeight;
            items.add(item);
        }
        request.ItemArr = items;
        return request;
    }

    private EcCangApiBaseResult<Object> buildApiBaseResult(String resultStr) {
        EcCangApiBaseResult<Object> apiBaseResult = new EcCangApiBaseResult<>();
        if (StringUtils.isBlank(resultStr)) {
            apiBaseResult.setCode("Failure");
            apiBaseResult.setMessage("ecang TMS 接口调用失败");
            return apiBaseResult;
        }

        JSONObject resultObj = JSONObject.parseObject(resultStr);
        apiBaseResult.setCode(resultObj.getString("ask"));
        apiBaseResult.setMessage(resultObj.getString("message"));
        if (resultObj.containsKey("data")) {
            apiBaseResult.setData(resultObj.getString("data"));
        } else {
            apiBaseResult.setData(resultStr);
        }
        return apiBaseResult;
    }

//    private EcCangTMSApiClient getClient(String ecTMSParams) {
//        return new EcCangTMSApiClient(ecTMSParams);
//    }

}
