package com.newnary.gsp.center.tpsi.api.open1688;


import com.newnary.api.base.common.CommonResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@RequestMapping("tpsi-center/operate1688Product")
public interface Operate1688ProductApi {

    @PostMapping("updateProductInfo")
    CommonResponse<String> updateProductInfo(List<String> customCodeList);
}
