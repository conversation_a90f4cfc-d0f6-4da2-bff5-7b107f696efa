package com.newnary.gsp.center.tpsi.api.haiying.response.shopee;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingShopeeProductDetailInfoDTO {

    /**
     * 商品状态码
     * (200:正常 100:商品未收录)
     */
    private Integer code;

    /**
     * 商品id
     */
    private String pid;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品默认价
     */
    private BigDecimal price;

    /**
     * 商品最低价
     */
    private BigDecimal min_price;

    /**
     * 商品最高价
     */
    private BigDecimal max_price;

    /**
     * 商品月销量
     */
    private Integer sold;

    /**
     * 商品总库存数
     */
    private Integer stock;

    /**
     * 商品总销量
     */
    private Integer historical_sold;

    /**
     * 商品评分
     */
    private BigDecimal rating;

    /**
     * 商品评分数
     */
    private Integer rating_count;

    /**
     * 商品浏览人数
     */
    private Integer view_count;

    /**
     * 商品喜欢人数
     */
    private Integer liked_count;

    /**
     * 商品预计到货时间
     */
    private Integer estimated_days;

    /**
     * 商品是否热销
     * 0:非热销   1:热销
     */
    private Boolean is_hot_sales;

    /**
     * 商品是否虾皮优选
     * 0:非优选   1:优选
     */
    private Boolean is_shopee_verified;

    /**
     * 商品主图
     */
    private String image;

    /**
     * 商品上架时间
     */
    private Long gen_time;

    /**
     * 商品所属店铺id
     */
    private String shop_id;

    /**
     * 商品所属店铺是否官方店铺
     * 0:否   1:是
     */
    private Boolean is_official_shop;

    /**
     * 商品类目路径
     */
    private String cid_namespace;

    /**
     * 商品类目名称路径
     */
    private String cname_namespace;

    /**
     * 商品状态
     */
    private Integer status;

    /**
     * 商品是否存在
     * （0存在，1不存在）
     */
    private Boolean not_exist;

    /**
     * 商品最近抓取时间
     */
    private Long last_modi_time;

    /**
     * 店铺所在地
     */
    private String shop_location;

    /**
     * 商品链接
     */
    private String product_url;

}
