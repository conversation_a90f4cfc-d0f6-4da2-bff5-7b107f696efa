package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.erp;

import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp.EcCangERPPurchaseOrderReceivingRequest;
import lombok.Data;

import java.util.List;

@Data
public class EcCangERPStockinOrderGetReceivingResponse {

    /** 入库单号,支持多个示例：["R11111111111","R222222222"] */
    public String receiving_code;

    /** 客户参考号 */
    public String reference_no;

    /** 采购单号 */
    public String po_code;

    public List<EcCangERPStockinOrderGetReceivingResponse.Product> product_info;

    public static class Product {

        /** 产品代码 */
        public String product_barcode;

        /** 产品名称 */
        public String product_title;

        /** 收货数量 */
        public Integer rd_received_qty;

        /** 送货数量 */
        public Integer rd_receiving_qty;

        /** 状态码：0:在途;1:处理中;2:收货完成 */
        public Integer rd_status;
    }

}
