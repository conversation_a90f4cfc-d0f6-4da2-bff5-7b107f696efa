package com.newnary.gsp.center.tpsi.infra.client.tongtu.valobj.request.erp2;

import lombok.Data;

@Data
public class TongTuOrderImportOrderBuyerInfo {

    /**
     * 买家账号
     */
    private String buyerAccount;

    /**
     * 地址1
     */
    private String buyerAddress1;

    /**
     * 地址2
     */
    private String buyerAddress2;

    /**
     * 地址3
     */
    private String buyerAddress3;

    /**
     * 城市
     */
    private String buyerCity;

    /**
     * 国家二字码
     */
    private String buyerCountryCode;

    /**
     * 买家邮箱
     */
    private String buyerEmail;

    /**
     * 手机
     */
    private String buyerMobilePhone;

    /**
     * 买家名称
     */
    private String buyerName;

    /**
     * 电话
     */
    private String buyerPhone;

    /**
     * 邮编
     */
    private String buyerPostalCode;

    /**
     * 州
     */
    private String buyerState;

}
