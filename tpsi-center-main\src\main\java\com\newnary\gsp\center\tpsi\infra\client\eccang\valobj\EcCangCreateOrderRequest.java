package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj;


import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/23
 */
public class EcCangCreateOrderRequest {
    /**
     * 是否预报，Y是(下单后为预报状态)，N否(下单后为草稿状态)，默认为Y
     */
    public String is_auto_audit;

    /**
     * 客户参考号
     */
    public String reference_no;

    /**
     * 配送方式/运输方式
     */
    public String shipping_method;

    /**
     * 收件人国家二字码
     */
    public String country_code;

    /**
     * 订单重量，单位KG，最多3位小数
     */
    public double order_weight;

    /**
     * 外包装件数
     */
    public int order_pieces;

    /**
     * 投保金额
     */
    public double insurance_value;

    /**
     * 申报类型
     * 1=Gif=礼品
     * 2=Commercial Sample=商品货样
     * 3=Document=文件
     * 4=Other=其他
     * 5=Marketplace Reseller
     * 6=Marketplace
     * 7=Sale B2B
     * 8=Sale B2C
     * 默认为4
     */
    public String mail_cargo_type;


    /**
     * 货物类型
     * B=Box=包裹
     * C=Pallet=卡板
     * D=Document=文件
     * L=Letter=信封
     * W=Package=包裹
     * 默认为W
     */
    public String cargo_type;

    /**
     * 发票运费
     */
    public double invoice_fee;


    /**
     * 收件人信息
     */
    public Consignee Consignee;

    /**
     * 发件人信息
     */
    public Shipper Shipper;

    /**
     * 商品信息
     */
    public List<Item> ItemArr;

    /**
     * 材积信息,快件材积,如果传入多个则为一票多件
     */
    public List<Volume> Volume;

    public static class Consignee {
        /**
         * 收件人公司名
         */
        public String consignee_company;

        /**
         * 收件人省
         */
        public String consignee_province;

        /**
         * 收件人城市
         */
        public String consignee_city;

        /**
         * 收件人地址1
         */
        public String consignee_street;

        /**
         * 收件人地址2
         */
        public String consignee_street2;

        /**
         * 收件人地址3
         */
        public String consignee_street3;

        /**
         * 收件人县区（一般不填）
         */
        public String consignee_district;

        /**
         * 收件人邮编
         */
        public String consignee_postcode;

        /**
         *
         */
        public String consignee_name;

        /**
         * 收件人电话
         */
        public String consignee_telephone;

        /**
         * 收件人手机
         */
        public String consignee_mobile;

        /**
         * 收件人邮箱
         */
        public String consignee_email;

        /**
         * 证件类型
         * ID:身份证
         * PP:护照
         */
        public String consignee_certificatetype;

        /**
         * 号码
         */
        public String consignee_certificatecode;

        /**
         * 有效期,
         * 格式：2014-04-15
         */
        public String consignee_credentials_period;

        /**
         * 护照签发地
         */
        public String consignee_passport_address;

        /**
         * 买家ID
         */
        public String buyer_id;

        /**
         * 收件人门牌号
         */
        public String consignee_doorplate;

        /**
         * 收件人税号（VAT&GST Number）
         */
        public String consignee_taxno;

        /**
         * 收件人税号类型 1=个人 2=公司 3=护照 4=其他
         */
        public String consignee_taxno_type;

        /**
         * 收件人EORI
         */
        public String consignee_eori;
    }

    public static class Shipper {
        /**
         * 发件人公司名
         */
        public String shipper_company;
        /**
         * 发件人国家二字码
         */
        public String shipper_countrycode;
        /**
         * 发件人省
         */
        public String shipper_province;
        /**
         * 发件人城市
         */
        public String shipper_city;
        /**
         * 发件人地址
         */
        public String shipper_street;
        /**
         * 发件人邮编
         */
        public String shipper_postcode;
        /**
         * 区域代码
         */
        public String shipper_areacode;
        /**
         * 发件人姓名
         */
        public String shipper_name;

        /**
         * 发件人电话
         */
        public String shipper_telephone;

        /**
         * 发件人手机
         */
        public String shipper_mobile;
        /**
         * 发件人邮箱
         */
        public String shipper_email;
        /**
         * 发件人传真
         */
        public String shipper_fax;
        /**
         * 发件人门牌号
         */
        public String shipper_doorplate;

        /**
         * 发件人税号
         */
        public String shipper_tax_number;
        /**
         * 发件人税号类型 1=个人 2=公司 3=护照 4=其他
         */
        public String shipper_tax_number_type;
        /**
         * 发件人EORI
         */
        public String shipper_eor;
    }

    public static class Item {
        /**
         * 英文海关申报品名
         */
        public String invoice_enname;

        /**
         * 中文海关申报品名
         */
        public String invoice_cnname;

        /**
         * 申报重量，单位KG,最多三位小数
         */
        public String invoice_weight;

        /**
         * 申报数量
         */
        public int invoice_quantity;

        /**
         * 申报单价
         */
        public double invoice_unitcharge;

        /**
         * 单位:
         * MTR=米
         * PCE=件
         * SET=套
         * 默认为PCE
         */
        public String unit_code;

        /**
         * 配货信息
         */
        public String invoice_note;

        /**
         * 海关协制编号
         */
        public String hs_code;

        /**
         * 品牌
         */
        public String invoice_brand;

        /**
         * 产品SKU编码
         */
        public String sku;

        /**
         * 箱子号，长度最多50
         * 与材积信息的箱号对应，不填默认为U001
         */
        public String box_number;

        /**
         * 申报币种，默认为USD(美元)
         */
        public String invoice_currencycode;

        /**
         * 原产国
         */
        public String country_of_origin;

        public String invoice_url;

        public String material;

        public String material_enture;

        public String use;

    }

    public static class Volume {
        /**
         * 长单位CM
         */
        public String length;
        /**
         * 宽单位CM
         */
        public String width;
        /**
         * 高单位CM
         */
        public String height;
        /**
         * 箱子重量单位KG
         */
        public String weight;
        /**
         * 箱号,由字母和数字组成,长度最多50
         * 同一订单箱号不能重复
         * 与申报信息里的箱号对应,如果没有材积信息，则默认为U001
         */
        public String box_number;
        /**
         * 子单号,由字母和数字组成,长度最多50
         * 子单号在系统不能重复
         */
        public String child_number;
    }

}
