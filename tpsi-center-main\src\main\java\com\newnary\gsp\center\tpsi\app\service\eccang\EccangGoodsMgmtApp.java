package com.newnary.gsp.center.tpsi.app.service.eccang;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.common.PageCondition;
import com.newnary.api.base.common.PageList;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.distributed.tools.concurrent.template.DConcurrentTemplate;
import com.newnary.gsp.center.purchase.api.product.response.SkuDetailInfo;
import com.newnary.gsp.center.purchase.api.product.response.SkuInfo;
import com.newnary.gsp.center.purchase.api.supplier.response.SupplierGoodsInfo;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp.*;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.erp.*;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingInfo;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingType;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.ThirdPartyMappingManager;
import com.newnary.gsp.center.tpsi.infra.rpc.PurchaseRpc;
import com.newnary.gsp.center.tpsi.service.eccang.IEccangERPApiSve;
import com.newnary.gsp.center.user.api.identity.feign.UserCoreFeignApi;
import com.newnary.gsp.center.user.api.identity.request.UserBatchQueryCommand;
import com.newnary.gsp.center.user.api.identity.response.UserBasicDTO;
import com.newnary.tenant.context.TenantCarrier;
import com.newnary.tenant.context.TenantID;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: jack
 * @CreateTime: 2025/5/22
 */
@Slf4j
@Component
public class EccangGoodsMgmtApp {

    @Value("${tpsi.bizIds}")
    private String bizIds;

    private static String MGMT_PREFIX = EccangGoodsMgmtApp.class.getName();

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;
    @Resource
    private PurchaseRpc purchaseRpc;
    @Resource
    private IEccangERPApiSve eccangERPApiSve;
    @Resource
    private ThirdPartyMappingManager thirdPartyMappingManager;
    @Resource
    private EccangPoMgmtApp eccangPoMgmtApp;

    public void syncSkus(List<SkuDetailInfo> skuList) {
        JSONObject jsonObject = JSON.parseObject(bizIds);
        String bizId = jsonObject.getString("ECCANG");
        ThirdPartySystem thirdPartySystem = loadSystem(bizId);
        if (null == thirdPartySystem) {
            log.info("采购系统SKU变更消息处理，未匹配到易仓对接信息，消息无需处理！");
            return;
        }
        Map<String, SkuDetailInfo> skuMap = new ConcurrentHashMap<>();
        skuList.forEach(skuDetailInfo -> {
            SkuInfo skuInfo = skuDetailInfo.getSkuInfo();
            skuMap.put(skuInfo.getSku(), skuDetailInfo);
        });
        if (skuMap.isEmpty()) {
            log.info("采购系统SKU变更消息处理，未匹配到SKU信息，消息无需处理！");
            return;
        }
        DConcurrentTemplate.tryLockMode(
                MGMT_PREFIX.concat(StringUtils.join(skuMap.keySet(), ",")),
                lock -> lock.tryLock(20, TimeUnit.SECONDS),
                () -> {
                    //先查一次易仓确认是否存在
                    EcCangERPGetProductBySkuRequest getProductBySkuRequest = new EcCangERPGetProductBySkuRequest();
                    getProductBySkuRequest.setProductSku(skuMap.keySet().stream().collect(Collectors.toList()));
                    EcCangApiBaseResult<String> getProductBySkuResult = eccangERPApiSve.getProductBySku(thirdPartySystem, getProductBySkuRequest);
                    if (StringUtils.equals(getProductBySkuResult.getCode(), "200")) {
                        List<EcCangERPSyncProductRequest> requestList = new ArrayList<>();
                        //如果存在则更新
                        List<EcCangERPGetProductBySkuResponse> getProductBySkuList = JSON.parseArray(getProductBySkuResult.getData(), EcCangERPGetProductBySkuResponse.class);
                        if (CollectionUtils.isNotEmpty(getProductBySkuList)) {
                            List<String> productSkuList = getProductBySkuList.stream().map(EcCangERPGetProductBySkuResponse::getProductSku).collect(Collectors.toList());
                            skuMap.forEach((k, v) -> {
                                if (productSkuList.contains(k)) {
                                    requestList.add(updateEccangSku(v));
                                } else {
                                    requestList.add(addEccangSku(v));
                                }
                            });
                        } else {
                            //如果不存在则新增
                            skuList.forEach(skuDetailInfo -> {
                                requestList.add(addEccangSku(skuDetailInfo));
                            });
                        }
                        eccangERPApiSve.syncBatchProduct(thirdPartySystem, requestList);
                    } else {
                        //请求错误处理
                        throw new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "请求易仓接口异常");
                    }
                }
        );
    }

    private EcCangERPSyncProductRequest updateEccangSku(SkuDetailInfo skuDetailInfo) {
        EcCangERPSyncProductRequest request = new EcCangERPSyncProductRequest();
        request.actionType = "EDIT";

        request.productSku = skuDetailInfo.getSkuInfo().getSku();
        request.productTitle = skuDetailInfo.getSkuInfo().getSkuName();
        //TODO 下列名称为空时待处理(因为易仓必填,但采购系统非必填,待沟通处理逻辑)
        request.productTitleEn = skuDetailInfo.getSkuInfo().getNameEn();
        request.pdOverseaTypeCn = skuDetailInfo.getSpuBaseInfo().getZhDeclaredName();
        request.pdOverseaTypeEn = skuDetailInfo.getSpuBaseInfo().getEnDeclaredName();
        request.productDeclaredValue = skuDetailInfo.getSpuBaseInfo().getDeclaredPrice().toString();
        request.pdDeclareCurrencyCode = skuDetailInfo.getSpuBaseInfo().getDeclaredCurrency();

        request.currencyCode = skuDetailInfo.getSkuInfo().getCurrency();
        request.productWeight = skuDetailInfo.getSkuInfo().getGrossWeight().doubleValue();
        request.defaultSupplierCode = null; //TODO 读取默认供应商逻辑待处理

        //TODO 类目映射待处理

        return request;
    }

    private EcCangERPSyncProductRequest addEccangSku(SkuDetailInfo skuDetailInfo) {
        EcCangERPSyncProductRequest request = new EcCangERPSyncProductRequest();
        request.actionType = "ADD";

        //TODO 类目映射待处理

        return request;
    }

    public void syncSupplierGoods(SupplierGoodsInfo supplierGoodsInfo) {
        JSONObject jsonObject = JSON.parseObject(bizIds);
        String bizId = jsonObject.getString("ECCANG");
        ThirdPartySystem thirdPartySystem = loadSystem(bizId);
        if (null == thirdPartySystem) {
            log.info("采购系统SKU变更消息处理，未匹配到易仓对接信息，消息无需处理！");
            return;
        }
        DConcurrentTemplate.tryLockMode(
                MGMT_PREFIX.concat(String.valueOf(supplierGoodsInfo.getId())),
                lock -> lock.tryLock(20, TimeUnit.SECONDS),
                () -> {
                    String thirdPartySupplierId = getSupplierId(thirdPartySystem, supplierGoodsInfo);
                    SkuDetailInfo skuInfo = purchaseRpc.getBySku(supplierGoodsInfo.getGoodsSku());
                    if (Objects.isNull(skuInfo)) {
                        log.info("采购系统SKU变更消息处理，未匹配到SKU信息，消息无需处理！");
                        return;
                    }
                    String purchaser = skuInfo.getSkuInfo().getPurchaser();
                    String thirdPartyUserId = getUserId(purchaser);
                    //查一次易仓确认是否存在
                    EcCangERPGetSupplierProductRequest getSupplierProductRequest = new EcCangERPGetSupplierProductRequest();
                    getSupplierProductRequest.setSupplierId(Integer.valueOf(thirdPartySupplierId));
                    getSupplierProductRequest.setSku(supplierGoodsInfo.getGoodsSku());
                    EcCangApiBaseResult<String> getSupplierProductResp = eccangERPApiSve.getSupplierProductList(thirdPartySystem, getSupplierProductRequest);
                    Map<String, EcCangERPGetSupplierProductResponse> supplierProductMap = new ConcurrentHashMap<>();
                    if (StringUtils.equals(getSupplierProductResp.getCode(), "200")) {
                        List<EcCangERPGetSupplierProductResponse> getSupplierProductListResponse = JSON.parseArray(getSupplierProductResp.getData(), EcCangERPGetSupplierProductResponse.class);
                        for (EcCangERPGetSupplierProductResponse getSupplierProductResponse : getSupplierProductListResponse) {
                            supplierProductMap.put(getSupplierProductResponse.getProduct_sku(), getSupplierProductResponse);
                        }
                    } else if (!StringUtils.equals(getSupplierProductResp.getCode(), "10003")) {
                        throw new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "获取易仓供应商商品列表失败-" + getSupplierProductResp.getMessage());
                    }
                    EcCangERPGetSupplierProductResponse getSupplierProductResponse = supplierProductMap.get(supplierGoodsInfo.getGoodsSku());
                    if (Objects.isNull(getSupplierProductResponse)) {
                        //如果不存在则新增
                        EcCangERPSyncSupplierProductResponse syncSupplierProductResponse = insertSupplierProduct(thirdPartySystem, thirdPartySupplierId, thirdPartyUserId, supplierGoodsInfo);
                    } else {
                        //如果存在则更新
                        String spId = "";
                        for (EcCangERPGetSupplierProductResponse.SupplierProduct supplierProduct : getSupplierProductResponse.getSupplier_product()) {
                            if (supplierProduct.getSupplier_id().equals(Integer.valueOf(thirdPartySupplierId))) {
                                spId = supplierProduct.getSp_id();
                            }
                        }
                        if (StringUtils.isBlank(spId)) {
                            throw new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "易仓供应商商品查询有误");
                        }
                        updateSupplierProduct(thirdPartySystem, Integer.valueOf(spId), thirdPartySupplierId, thirdPartyUserId, supplierGoodsInfo);
                    }
                }
        );
    }


    private String getSupplierId(ThirdPartySystem thirdPartySystem, SupplierGoodsInfo supplierGoodsInfo) {
        //先从数据库映射中查询
        ThirdPartyMappingInfo supplierMapping = thirdPartyMappingManager.getNewestInfoBySource("ECCANG", "PMS", supplierGoodsInfo.getSupplierId(), ThirdPartyMappingType.SUPPLIER_ID);
        String supplierId = null;
        if (null != supplierMapping) {  //有则直接返回
            supplierId = supplierMapping.getTargetId();
        } else {
            //没有的话再查一次易仓采购供应商
            EcCangERPGetSupplierListRequest getSupplierListRequest = new EcCangERPGetSupplierListRequest();
            EcCangERPGetSupplierListRequest.Condition condition = new EcCangERPGetSupplierListRequest.Condition();
            condition.setSupplierName(supplierGoodsInfo.getSupplierName());
            getSupplierListRequest.setCondition(condition);
            EcCangApiBaseResult<String> getSupplierListResp = eccangERPApiSve.getSupplierList(thirdPartySystem, getSupplierListRequest);
            if (StringUtils.equals(getSupplierListResp.getCode(), "200")) {
                List<EcCangERPGetSupplierListResponse> getSupplierListResponse = JSON.parseArray(getSupplierListResp.getData(), EcCangERPGetSupplierListResponse.class);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(getSupplierListResponse)) {
                    if (getSupplierListResponse.size() == 1) {
                        //易仓采购供应商查到记录
                        EcCangERPGetSupplierListResponse supplier = getSupplierListResponse.get(0);
                        supplierId = supplier.getSupplierId();
                        //补充数据库映射
                        thirdPartyMappingManager.insertOrUpdate("PMS", "ECCANG", supplierGoodsInfo.getSupplierId(), supplierId, ThirdPartyMappingType.SUPPLIER_ID.name(), JSON.toJSONString(supplier));
                    } else {
                        log.error("易仓采购供应商名称重复！");
                    }
                } else {
                    //易仓供应商没有就创建
                    EcCangERPSyncSupplierRequest syncSupplier = new EcCangERPSyncSupplierRequest();
                    syncSupplier.setActionType("ADD");
                    EcCangERPSyncSupplierRequest.Supplier supplier = new EcCangERPSyncSupplierRequest.Supplier();
                    supplier.setSupplierCode(supplierGoodsInfo.getSupplierName());
                    supplier.setSupplierName(supplierGoodsInfo.getSupplierName());
                    supplier.setLevel("A");
                    supplier.setSupplierTeamworkType(0);
                    supplier.setSupplierType(2);
                    supplier.setPcId(1);
                    supplier.setAccountType(1);
                    supplier.setPayType(2);
                    supplier.setSupplierCarrier(1);
                    supplier.setShippingMethodIdHead(3);
                    supplier.setSupplierShipPayType(1);
                    supplier.setSupplierQcException(1);
                    supplier.setBuyerId(1);
                    syncSupplier.setSupplier(supplier);
                    EcCangApiBaseResult<String> syncSupplierResp = eccangERPApiSve.syncSupplier(thirdPartySystem, syncSupplier);
                    if (StringUtils.equals(getSupplierListResp.getCode(), "200")) {
                        EcCangERPSyncSupplierResponse syncSupplierResponse = JSON.parseObject(syncSupplierResp.getData(), EcCangERPSyncSupplierResponse.class);
                        supplierId = String.valueOf(syncSupplierResponse.getSupplierId());
                        //创建完插入数据库
                        thirdPartyMappingManager.insertOrUpdate("PMS", "ECCANG", supplierGoodsInfo.getSupplierId(), supplierId, ThirdPartyMappingType.SUPPLIER_ID.name(), JSON.toJSONString(syncSupplierResponse));
                    } else {
                        log.error("创建易仓采购供应商{}失败！", supplierGoodsInfo.getSupplierName());
                    }
                }
            }
        }
        return supplierId;
    }

    private EcCangERPSyncSupplierProductResponse insertSupplierProduct(ThirdPartySystem thirdPartySystem, String thirdPartySupplierId, String thirdPartyUserId, SupplierGoodsInfo supplierGoodsInfo) {
        EcCangERPSyncSupplierProductRequest request = new EcCangERPSyncSupplierProductRequest();
        request.setActionType("ADD");

        request.setSupplierId(Integer.valueOf(thirdPartySupplierId));
        request.setSpSupplierProductCode(supplierGoodsInfo.getGoodsSku());
        request.setSpUnitPrice(supplierGoodsInfo.getPurchasePrice() != null ? supplierGoodsInfo.getPurchasePrice().floatValue() : null);
        request.setSpDefault(supplierGoodsInfo.getIsDefault());
        request.setSpMinQty(NumberUtils.max(ObjectUtils.defaultIfNull(supplierGoodsInfo.getMinPurchaseQuantity(), 0), 1)); // MOQ必填
        request.setBuyerId(Integer.valueOf(thirdPartyUserId));
        String currencyCode = supplierGoodsInfo.getCurrency();
        if (StringUtils.isBlank(currencyCode) || StringUtils.equals(currencyCode, "CNY")) {
            currencyCode = "RMB";
        }
        request.setCurrencyCode(currencyCode);
        if (StringUtils.isNotBlank(supplierGoodsInfo.getAliLink())) {
            request.setSpProductAddress(Arrays.asList(supplierGoodsInfo.getAliLink()));
        }
        EcCangApiBaseResult<String> result = eccangERPApiSve.syncSupplierProduct(thirdPartySystem, request);
        if (StringUtils.equals(result.getCode(), "200")) {
            return JSON.parseObject(result.getData(), EcCangERPSyncSupplierProductResponse.class);
        } else {
            throw new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "请求易仓接口异常");
        }
    }

    private EcCangERPSyncSupplierProductResponse updateSupplierProduct(ThirdPartySystem thirdPartySystem, Integer spId, String thirdPartySupplierId, String thirdPartyUserId, SupplierGoodsInfo supplierGoodsInfo) {
        EcCangERPSyncSupplierProductRequest request = new EcCangERPSyncSupplierProductRequest();
        request.setActionType("EDIT");
        request.setSpId(spId);

        request.setSupplierId(Integer.valueOf(thirdPartySupplierId));
        request.setSpSupplierProductCode(supplierGoodsInfo.getGoodsSku());
        request.setSpUnitPrice(supplierGoodsInfo.getPurchasePrice().floatValue());
        request.setSpDefault(supplierGoodsInfo.getIsDefault());
        request.setSpMinQty(NumberUtils.max(ObjectUtils.defaultIfNull(supplierGoodsInfo.getMinPurchaseQuantity(), 0), 1)); // MOQ必填
        request.setBuyerId(Integer.valueOf(thirdPartyUserId));
        String currencyCode = supplierGoodsInfo.getCurrency();
        if (StringUtils.isBlank(currencyCode) || StringUtils.equals(currencyCode, "CNY")) {
            currencyCode = "RMB";
        }
        request.setCurrencyCode(currencyCode);
        if (StringUtils.isNotBlank(supplierGoodsInfo.getAliLink())) {
            request.setSpProductAddress(Arrays.asList(supplierGoodsInfo.getAliLink()));
        }
        EcCangApiBaseResult<String> result = eccangERPApiSve.syncSupplierProduct(thirdPartySystem, request);
        if (StringUtils.equals(result.getCode(), "200")) {
            return JSON.parseObject(result.getData(), EcCangERPSyncSupplierProductResponse.class);
        } else {
            throw new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "请求易仓接口异常");
        }
    }

    private String getUserId(String purchaserId) {
        //TODO 本来应该用ID的，不知道为什么商品表里直接用中文名，这里只能做临时匹配映射，待以后商品表的字段一并修改再处理
//        ThirdPartyMappingInfo userMapping = thirdPartyMappingManager.getNewestInfoBySource("ECCANG", "GSP", purchaserId, ThirdPartyMappingType.USER_ID);
//        if (null == userMapping) {
//            return null;
//        }
//        return userMapping.getTargetId();
        Integer userId = UserIdMapping.get(purchaserId);
        if (null == userId) {
            throw new ServiceException(CommonErrorInfo.ERROR_101_QUERY_DATA_ERROR, "找不到对应采购用户映射信息-" + purchaserId);
        }
        return String.valueOf(userId);
    }

    //结算方式
    public static final Map<String, Integer> UserIdMapping = new HashMap<String, Integer>() {
        {
            put("何梁莉", 941);
            put("叶盈影", 942);
            put("徐嘉丽", 834);
        }
    };

    private ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }

    @Resource
    private UserCoreFeignApi userCoreFeignApi;

    @PostConstruct
    public void initUserMapping() {
        try {
            TenantCarrier.setTenantID(new TenantID("TENANT9380329708340982579200"));
            PageList<ThirdPartyMappingInfo> pageList = thirdPartyMappingManager.getNewestInfoByTargetBizId("ECCANG", "GSP", ThirdPartyMappingType.USER_ID, new PageCondition(1, 100));
            if (CollectionUtils.isNotEmpty(pageList.getItems())) {
                Map<String, ThirdPartyMappingInfo> userMapping = pageList.getItems().stream().collect(Collectors.toMap(ThirdPartyMappingInfo::getSourceId, Function.identity()));
                UserBatchQueryCommand command = new UserBatchQueryCommand();
                command.setUserIds(new ArrayList<>(userMapping.keySet()));
                List<UserBasicDTO> userBasicDTOS = userCoreFeignApi.batchQuery(command).mustSuccessOrThrowOriginal();
                for (UserBasicDTO userBasicDTO : userBasicDTOS) {
                    UserIdMapping.put(userBasicDTO.getNickName(), Integer.parseInt(userMapping.get(userBasicDTO.getUserId()).getTargetId()));
                }
            }
        } catch (Exception e) {
            log.error("易仓用户映射初始化异常", e);
        } finally {
            TenantCarrier.clearTenantID();
        }
    }

}
