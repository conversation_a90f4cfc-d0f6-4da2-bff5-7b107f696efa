package com.newnary.gsp.center.tpsi.infra.rpc;

import com.newnary.gsp.center.basicdata.api.currency.ExchangeRateApi;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;


@Component
public class ExchangeRateRpc {

    @Resource
    private ExchangeRateApi exchangeRateApi;

    public BigDecimal currencyConvert(BigDecimal sourceAmount, String sourceCurrency, String targetCurrency) {
        return exchangeRateApi.currencyConvert(sourceAmount, sourceCurrency, targetCurrency).mustSuccessOrThrowOriginal();
    }

}
