package com.newnary.gsp.center.tpsi.infra.rpc;

import com.newnary.gsp.center.stock.api.logistics.feign.BatchStockFeignApi;
import com.newnary.gsp.center.stock.api.logistics.request.BatchStockQueryCommand;
import com.newnary.gsp.center.stock.api.logistics.response.BatchStockOnlyStockInfo;
import com.newnary.gsp.center.stock.domain.model.vo.BatchStockState;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Component
public class BatchStockRpc {

    @Resource
    private BatchStockFeignApi batchStockFeignApi;

    public List<BatchStockOnlyStockInfo> getBySupplierSkuId(String supplierSkuId) {
        BatchStockQueryCommand command = new BatchStockQueryCommand();
        command.setSupplierSkuIds(Collections.singletonList(supplierSkuId));
        command.setState(BatchStockState.ENABLE);
        return batchStockFeignApi.queryOnlyStockInfo(command).mustSuccessOrThrowOriginal();
    }
}
