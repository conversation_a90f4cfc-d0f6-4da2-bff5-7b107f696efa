package com.newnary.gsp.center.tpsi.infra.repository.db.manager;

import com.alibaba.fastjson.JSON;
import com.newnary.api.base.common.PageCondition;
import com.newnary.api.base.common.PageList;
import com.newnary.api.base.common.PageMeta;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.common.utils.page.PageUtil;
import com.newnary.common.utils.page.ParamUtil;
import com.newnary.dao.base.BaseDao;
import com.newnary.dao.base.helper.DaoHelper;
import com.newnary.dao.base.manager.BaseManager;
import com.newnary.dao.base.po.BaseQuery;
import com.newnary.gsp.center.tpsi.infra.client.tk.dto.vo.TKOrderExtInfo;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingInfo;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingType;
import com.newnary.gsp.center.tpsi.infra.repository.db.dao.ThirdPartyMappingDao;
import com.newnary.gsp.center.tpsi.infra.repository.db.dao.ThirdPartyMappingExtDao;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyMappingPO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ThirdPartyMappingManager extends BaseManager<ThirdPartyMappingPO> {
    @Resource
    private ThirdPartyMappingDao thirdPartyMappingDao;

    @Resource
    private ThirdPartyMappingExtDao thirdPartyMappingExtDao;


    public static void main(String[] args) {
        DaoHelper.genXMLWithFeature(
                "D:\\dddplus\\tpsi-center\\tpsi-center-main\\src\\main\\java\\com\\newnary\\gsp\\center\\tpsi\\infra\\repository\\db\\dao\\ThirdPartyMappingDao.xml",
                ThirdPartyMappingDao.class,
                ThirdPartyMappingPO.class,
                "third_party_mapping",
                false);
    }

    public Map<String, String> batchGetIdMappingBySource(String sourceBizId, String targetBizId, List<String> sourceIds, ThirdPartyMappingType type) {
        if (StringUtils.isAnyBlank(sourceBizId, targetBizId) || CollectionUtils.isEmpty(sourceIds) || null == type) {
            throw new ServiceException(CommonErrorInfo.ERROR_101_QUERY_DATA_ERROR, "第三方映射 获取目标ID,缺失必要查询信息");
        }

        Map<String, String> mapping = new HashMap<>();
        BaseQuery<ThirdPartyMappingPO> query = new BaseQuery<>(new ThirdPartyMappingPO());
        query.getData().setSourceBizId(sourceBizId);
        query.getData().setTargetBizId(targetBizId);
        query.getData().setBizType(type.name());
        query.addIn("source_id", sourceIds);
        List<ThirdPartyMappingPO> result = thirdPartyMappingDao.query(query);
        if (CollectionUtils.isNotEmpty(result)) {
            for (ThirdPartyMappingPO mappingPO : result) {
                mapping.put(mappingPO.getSourceId(), mappingPO.getTargetId());
            }
        }
        return mapping;
    }

    public Map<String, ThirdPartyMappingInfo> batchGetIdMappingInfoBySource(String sourceBizId, String targetBizId, List<String> sourceIds, ThirdPartyMappingType type) {
        if (StringUtils.isAnyBlank(sourceBizId, targetBizId) || CollectionUtils.isEmpty(sourceIds) || null == type) {
            throw new ServiceException(CommonErrorInfo.ERROR_101_QUERY_DATA_ERROR, "第三方映射 获取目标ID,缺失必要查询信息");
        }

        Map<String, ThirdPartyMappingInfo> mapping = new HashMap<>();
        BaseQuery<ThirdPartyMappingPO> query = new BaseQuery<>(new ThirdPartyMappingPO());
        query.getData().setSourceBizId(sourceBizId);
        query.getData().setTargetBizId(targetBizId);
        query.getData().setBizType(type.name());
        query.addIn("source_id", sourceIds);
        List<ThirdPartyMappingPO> result = thirdPartyMappingDao.query(query);
        if (CollectionUtils.isNotEmpty(result)) {
            for (ThirdPartyMappingPO mappingPO : result) {
                ThirdPartyMappingInfo thirdPartyMappingInfo = new ThirdPartyMappingInfo();
                BeanUtils.copyProperties(mappingPO, thirdPartyMappingInfo);
                mapping.put(mappingPO.getSourceId(), thirdPartyMappingInfo);
            }
        }
        return mapping;
    }

    public Map<String, ThirdPartyMappingInfo> batchGetIdMappingInfoByTargetIds(String sourceBizId, String targetBizId, List<String> targetIds, ThirdPartyMappingType type) {
        if (StringUtils.isAnyBlank(sourceBizId, targetBizId) || CollectionUtils.isEmpty(targetIds) || null == type) {
            throw new ServiceException(CommonErrorInfo.ERROR_101_QUERY_DATA_ERROR, "第三方映射 获取目标ID,缺失必要查询信息");
        }

        Map<String, ThirdPartyMappingInfo> mapping = new HashMap<>();
        BaseQuery<ThirdPartyMappingPO> query = new BaseQuery<>(new ThirdPartyMappingPO());
        query.getData().setSourceBizId(sourceBizId);
        query.getData().setTargetBizId(targetBizId);
        query.getData().setBizType(type.name());
        query.addIn("target_id", targetIds);
        List<ThirdPartyMappingPO> result = thirdPartyMappingDao.query(query);
        if (CollectionUtils.isNotEmpty(result)) {
            for (ThirdPartyMappingPO mappingPO : result) {
                ThirdPartyMappingInfo thirdPartyMappingInfo = new ThirdPartyMappingInfo();
                BeanUtils.copyProperties(mappingPO, thirdPartyMappingInfo);
                mapping.put(mappingPO.getTargetId(), thirdPartyMappingInfo);
            }
        }
        return mapping;
    }

    public Map<String, ThirdPartyMappingInfo> batchGetIdMappingInfoByTargetCategoryPath(String sourceBizId, String targetBizId, List<String> targetCategoryPaths, ThirdPartyMappingType type) {
        if (StringUtils.isAnyBlank(sourceBizId, targetBizId) || CollectionUtils.isEmpty(targetCategoryPaths) || null == type) {
            throw new ServiceException(CommonErrorInfo.ERROR_101_QUERY_DATA_ERROR, "第三方映射 获取目标ID,缺失必要查询信息");
        }

        Map<String, ThirdPartyMappingInfo> mapping = new HashMap<>();
        BaseQuery<ThirdPartyMappingPO> query = new BaseQuery<>(new ThirdPartyMappingPO());
        query.getData().setSourceBizId(sourceBizId);
        query.getData().setTargetBizId(targetBizId);
        query.getData().setBizType(type.name());
        query.addIn("target_category_path", targetCategoryPaths);
        List<ThirdPartyMappingPO> result = thirdPartyMappingDao.query(query);
        if (CollectionUtils.isNotEmpty(result)) {
            for (ThirdPartyMappingPO mappingPO : result) {
                ThirdPartyMappingInfo thirdPartyMappingInfo = new ThirdPartyMappingInfo();
                BeanUtils.copyProperties(mappingPO, thirdPartyMappingInfo);
                mapping.put(mappingPO.getTargetCategoryPath(), thirdPartyMappingInfo);
            }
        }
        return mapping;
    }


    public ThirdPartyMappingInfo getInfoBySource(String sourceId, String sourceBizId, String targetBizId, ThirdPartyMappingType type) {
        if (StringUtils.isAnyBlank(sourceBizId, targetBizId) || StringUtils.isBlank(sourceId) || null == type) {
            throw new ServiceException(CommonErrorInfo.ERROR_101_QUERY_DATA_ERROR, "第三方映射 获取映射信息,缺失必要查询信息");
        }

        BaseQuery<ThirdPartyMappingPO> query = new BaseQuery<>(new ThirdPartyMappingPO());
        query.getData().setSourceBizId(sourceBizId);
        query.getData().setTargetBizId(targetBizId);
        query.getData().setBizType(type.name());
        query.getData().setSourceId(sourceId);
        List<ThirdPartyMappingPO> result = thirdPartyMappingDao.query(query);
        if (CollectionUtils.isNotEmpty(result)) {
            ThirdPartyMappingPO po = result.get(0);
            ThirdPartyMappingInfo info = new ThirdPartyMappingInfo();
            BeanUtils.copyProperties(po, info);
            return info;
        }
        return null;
    }

    public void insertOrUpdate(String sourceBizId, String targetBizId, String sourceId, String targetId, String type, Object extInfo) {
        if (StringUtils.isAnyBlank(sourceBizId, targetBizId, sourceId, targetId, type)) {
            return;
        }

        BaseQuery<ThirdPartyMappingPO> query = new BaseQuery<>(new ThirdPartyMappingPO());
        query.getData().setSourceId(sourceId);
        query.getData().setTargetId(targetId);
        query.getData().setBizType(type);
        query.getData().setSourceBizId(sourceBizId);
        query.getData().setTargetBizId(targetBizId);
        List<ThirdPartyMappingPO> result = thirdPartyMappingDao.query(query);

        if (CollectionUtils.isNotEmpty(result)) {
            ThirdPartyMappingPO existPO = result.get(0);
            if (null != extInfo) {
                existPO.setExtendBizInfo(JSON.toJSONString(extInfo));
                thirdPartyMappingDao.update(existPO);
            }
        } else {
            ThirdPartyMappingPO insertPO = new ThirdPartyMappingPO();
            insertPO.setSourceBizId(sourceBizId);
            insertPO.setTargetBizId(targetBizId);
            insertPO.setBizType(type);
            insertPO.setTargetId(targetId);
            insertPO.setSourceId(sourceId);
            insertPO.setIndexKey(type.concat(sourceId).concat(sourceBizId).concat(targetId).concat(targetBizId));
            insertPO.setExtendBizInfo(JSON.toJSONString(extInfo));
            thirdPartyMappingDao.insert(insertPO);
        }
    }

    public void insertOrUpdateCategory(String sourceBizId, String targetBizId, String sourceId, String targetId, String targetCategoryPath, String type, Object extInfo) {
        if (StringUtils.isAnyBlank(sourceBizId, targetBizId, sourceId, type)) {
            return;
        }

        BaseQuery<ThirdPartyMappingPO> query = new BaseQuery<>(new ThirdPartyMappingPO());
        query.getData().setSourceId(sourceId);
        query.getData().setTargetId(targetId);
        query.getData().setTargetCategoryPath(targetCategoryPath);
        query.getData().setBizType(type);
        query.getData().setSourceBizId(sourceBizId);
        query.getData().setTargetBizId(targetBizId);
        List<ThirdPartyMappingPO> result = thirdPartyMappingDao.query(query);

        if (CollectionUtils.isNotEmpty(result)) {
            ThirdPartyMappingPO existPO = result.get(0);
            if (null != extInfo) {
                existPO.setExtendBizInfo(JSON.toJSONString(extInfo));
                thirdPartyMappingDao.update(existPO);
            }
        } else {
            ThirdPartyMappingPO insertPO = new ThirdPartyMappingPO();
            insertPO.setSourceBizId(sourceBizId);
            insertPO.setTargetBizId(targetBizId);
            insertPO.setBizType(type);
            insertPO.setSourceId(sourceId);
            insertPO.setTargetId(targetId);
            insertPO.setTargetCategoryPath(targetCategoryPath);
            if (null != targetId) {
                insertPO.setIndexKey(type.concat(sourceId).concat(sourceBizId).concat(targetId).concat(targetBizId));
            } else {
                insertPO.setIndexKey(type.concat(sourceId).concat(sourceBizId).concat(targetBizId));
            }

            insertPO.setExtendBizInfo(JSON.toJSONString(extInfo));
            thirdPartyMappingDao.insert(insertPO);
        }
    }

    public void updateSourceId(ThirdPartyMappingInfo newestInfoByTarget, String sourceId) {

        BaseQuery<ThirdPartyMappingPO> query = new BaseQuery<>(new ThirdPartyMappingPO());
        query.getData().setSourceId(sourceId);
        query.getData().setTargetId(newestInfoByTarget.getTargetId());
        query.getData().setBizType(newestInfoByTarget.getBizType());
        query.getData().setSourceBizId(newestInfoByTarget.getSourceBizId());
        query.getData().setTargetBizId(newestInfoByTarget.getTargetBizId());
        List<ThirdPartyMappingPO> result = thirdPartyMappingDao.query(query);

        if (CollectionUtils.isNotEmpty(result)) {
            ThirdPartyMappingPO existPO = result.get(0);
            existPO.setSourceId(sourceId);
            thirdPartyMappingDao.update(existPO);
        }
    }

    public ThirdPartyMappingInfo getNewestInfoByTarget(String targetBizId, String sourceBizId, String targetId, ThirdPartyMappingType type) {
        BaseQuery<ThirdPartyMappingPO> query = new BaseQuery<>(new ThirdPartyMappingPO());
        query.getData().setSourceBizId(sourceBizId);
        query.getData().setTargetBizId(targetBizId);
        query.getData().setBizType(type.name());
        query.getData().setTargetId(targetId);
        query.addOrderBy("gmt_modified", 0);
        List<ThirdPartyMappingPO> result = thirdPartyMappingDao.query(query);
        if (CollectionUtils.isNotEmpty(result)) {
            ThirdPartyMappingInfo thirdPartyMappingInfo = new ThirdPartyMappingInfo();
            BeanUtils.copyProperties(result.get(0), thirdPartyMappingInfo);
            return thirdPartyMappingInfo;
        }
        return null;
    }

    public ThirdPartyMappingInfo getNewestInfoBySource(String targetBizId, String sourceBizId, String sourceId, ThirdPartyMappingType type) {
        BaseQuery<ThirdPartyMappingPO> query = new BaseQuery<>(new ThirdPartyMappingPO());
        query.getData().setSourceBizId(sourceBizId);
        query.getData().setTargetBizId(targetBizId);
        query.getData().setBizType(type.name());
        query.getData().setSourceId(sourceId);
        query.addOrderBy("gmt_modified", 0);
        List<ThirdPartyMappingPO> result = thirdPartyMappingDao.query(query);
        if (CollectionUtils.isNotEmpty(result)) {
            ThirdPartyMappingInfo thirdPartyMappingInfo = new ThirdPartyMappingInfo();
            BeanUtils.copyProperties(result.get(0), thirdPartyMappingInfo);
            return thirdPartyMappingInfo;
        }
        return null;
    }

    public PageList<ThirdPartyMappingInfo> getNewestInfoByTargetBizId(String targetBizId, String sourceBizId, ThirdPartyMappingType type, PageCondition pageCondition) {
        //查询商品数量
        BaseQuery<ThirdPartyMappingPO> query = new BaseQuery<>(new ThirdPartyMappingPO());
        query.getData().setSourceBizId(sourceBizId);
        query.getData().setTargetBizId(targetBizId);
        query.getData().setBizType(type.name());
/*        long count = thirdPartyMappingDao.count(query);
        PageMeta pageMeta = new PageMeta();
        pageMeta.setPageNum(pageCondition.getPageNum());
        pageMeta.setPageSize(pageCondition.getPageSize());
        pageMeta.setTotal(count);*/
        //执行分页查询
        Map<String, Object> paramMap = ParamUtil.convert2ParamMap(query.getData());
        Integer startRow = (pageCondition.getPageNum() - 1) * pageCondition.getPageSize();
        paramMap.put("startRow", startRow);
        paramMap.put("pageSize", pageCondition.getPageSize());
        List<ThirdPartyMappingPO> result = thirdPartyMappingExtDao.pageQuery(paramMap);
        if (CollectionUtils.isNotEmpty(result)) {
            List<ThirdPartyMappingInfo> list = new ArrayList<>();
            result.forEach(thirdPartyMappingPO -> {
                ThirdPartyMappingInfo thirdPartyMappingInfo = new ThirdPartyMappingInfo();
                BeanUtils.copyProperties(thirdPartyMappingPO, thirdPartyMappingInfo);
                list.add(thirdPartyMappingInfo);
            });

            return PageUtil.createPageList(new PageMeta(), list);
        }
        return null;
    }

    public PageList<ThirdPartyMappingInfo> getNewestInfoByTargetBizIdAndGmtCreate(String targetBizId, String sourceBizId, ThirdPartyMappingType type, PageCondition pageCondition, Long gmtCreateLess, Long gmtCreateGreater) {
        //查询商品数量
        BaseQuery<ThirdPartyMappingPO> query = new BaseQuery<>(new ThirdPartyMappingPO());
        query.getData().setSourceBizId(sourceBizId);
        query.getData().setTargetBizId(targetBizId);
        query.getData().setBizType(type.name());
/*        long count = thirdPartyMappingDao.count(query);
        PageMeta pageMeta = new PageMeta();
        pageMeta.setPageNum(pageCondition.getPageNum());
        pageMeta.setPageSize(pageCondition.getPageSize());
        pageMeta.setTotal(count);*/
        //执行分页查询
        Map<String, Object> paramMap = ParamUtil.convert2ParamMap(query.getData());
        Integer startRow = (pageCondition.getPageNum() - 1) * pageCondition.getPageSize();
        paramMap.put("gmtCreateLess", gmtCreateLess);
        paramMap.put("gmtCreateGreater", gmtCreateGreater);
        paramMap.put("startRow", startRow);
        paramMap.put("pageSize", pageCondition.getPageSize());
        List<ThirdPartyMappingPO> result = thirdPartyMappingExtDao.pageQuery(paramMap);
        if (CollectionUtils.isNotEmpty(result)) {
            List<ThirdPartyMappingInfo> list = new ArrayList<>();
            result.forEach(thirdPartyMappingPO -> {
                ThirdPartyMappingInfo thirdPartyMappingInfo = new ThirdPartyMappingInfo();
                BeanUtils.copyProperties(thirdPartyMappingPO, thirdPartyMappingInfo);
                list.add(thirdPartyMappingInfo);
            });

            return PageUtil.createPageList(new PageMeta(), list);
        }
        return null;
    }

    public int getNewestInfoCountByTargetBizId(String targetBizId, String sourceBizId, ThirdPartyMappingType type) {
        //查询商品数量
        BaseQuery<ThirdPartyMappingPO> query = new BaseQuery<>(new ThirdPartyMappingPO());
        query.getData().setSourceBizId(sourceBizId);
        query.getData().setTargetBizId(targetBizId);
        query.getData().setBizType(type.name());
        long count = thirdPartyMappingDao.count(query);
        return (int) count;
    }

    public void tryUpdateByTarget(String targetId, String targetBizId, String sourceBizId, ThirdPartyMappingType type, TKOrderExtInfo extInfo) {
        if (ObjectUtils.isEmpty(extInfo)) {
            return;
        }
        BaseQuery<ThirdPartyMappingPO> query = new BaseQuery<>(new ThirdPartyMappingPO());
        query.getData().setTargetId(targetId);
        query.getData().setTargetBizId(targetBizId);
        query.getData().setSourceBizId(sourceBizId);
        query.getData().setBizType(type.name());
        List<ThirdPartyMappingPO> result = thirdPartyMappingDao.query(query);
        if (CollectionUtils.isNotEmpty(result)) {
            ThirdPartyMappingPO updatePO = result.get(0);
            updatePO.setExtendBizInfo(JSON.toJSONString(extInfo));
            thirdPartyMappingDao.update(updatePO);
        }
    }

/*    public Map<String, Object> buildMap(Object po) {
        //通过getDeclaredFields()方法获取对象类中的所有属性（含私有）
        Field[] fields = po.getClass().getDeclaredFields();
        Map<String, Object> params = new HashMap<>();
        for (Field field : fields) {
            //设置允许通过反射访问私有变量
            field.setAccessible(true);
            //获取字段的值
            String value = null;
            try {
                value = null == field.get(po)? "":field.get(po).toString();
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
            //获取字段属性名称
            String name = field.getName();
            //其他自定义操作
            params.put(name, value);
        }
        return params;
    }*/

    @Override
    public BaseDao<ThirdPartyMappingPO> getDao() {
        return thirdPartyMappingDao;
    }
}
