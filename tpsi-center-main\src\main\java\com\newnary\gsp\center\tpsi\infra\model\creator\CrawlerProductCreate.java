package com.newnary.gsp.center.tpsi.infra.model.creator;

import com.newnary.gsp.center.tpsi.infra.model.id.CrawlerProductId;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;


/**
 * <AUTHOR>
 * @since Created on 2023-03-17
 **/
@Getter
@Setter
@Accessors(chain = true)
public class CrawlerProductCreate {


    private CrawlerProductId crawlerProductId;

    /**
     *  类目名称
     */
    private String categoryName;

    /**
     * 页码
     */
    private Integer beginPage;

    /**
     * 页大小
     */
    private Integer pageSize;

    /**
     * 商品id
     */
    private String productId;

    /**
     *  是否入库，0未，1已入库
     */
    private Integer state;

    /**
     *  是否需要 重置价格以及库存状态标识
     */
    private Integer flushState;

    /**
     *  重置价格以及库存版本号
     */
    private Integer flushVersion;

    /**
     * 爬虫返回的详情页数据
     */
    private String crawlerReturn;

    /**
     * 是否存储详情快照
     */
    private Integer crawlerReturnState;

    /**
     * 失败原因
     */
    private String failReason;
}
