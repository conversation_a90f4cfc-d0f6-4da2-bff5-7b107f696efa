package com.newnary.gsp.center.tpsi.api.open1688.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Data
public class SearchKeyWordsCommand {

    /**
     * 场景：all
     */
    @NotNull(message = "场景不能为空")
    private String scenario;

    @NotNull(message = "搜索参数不能为空")
    private Param param;

    @Setter
    @Getter
    public static class Param{
        /**
         * 关键词
         */
        private String keywords;

        /**
         * 类目id列表，用逗号分隔
         */
        private String categoryIds;

        /**
         * 起批量
         */
        private String quantityBegin;

        /**
         * 起始价
         */
        private String priceStart;

        /**
         * 终止价
         */
        private String priceEnd;

        /**
         * 排序字段
         */
        private String sortType;

        /**
         * 降序desc还是升序asc，默认不传算法排序
         */
        private String sortOrder;

        /**
         * 过滤参数，shipIn48Hours（48小时发货），freeExchange7days（7天包换），powerMerchant（实力商家），crossPotential(跨境潜力商品)，ttpft(批发团商品)，jxhy(精选货源商品)
         */
        private String filter;

        /**
         * 分页大小
         */
        private String pageSize;

        /**
         * 当前页
         */
        private String pageNum;
    }
}
