package com.newnary.gsp.center.tpsi.service.haiying;

import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.HaiYingDataApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.lazada.*;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
public interface IHaiYingDataLazadaApiSve {

    HaiYingDataApiBaseResult<String> getProductList(HaiYingLazadaProductListRequest productsInfoRequest);

    HaiYingDataApiBaseResult<String> getProductDetailInfo(HaiYingLazadaProductDetailInfoRequest productDetailInfoRequest);

    HaiYingDataApiBaseResult<String> getProductExtInfo(HaiYingLazadaProductExtInfoRequest productExtInfoRequest);

    HaiYingDataApiBaseResult<String> getProductHistoryInfo(HaiYingLazadaProductHistoryInfoRequest productHistoryInfoRequest);

    HaiYingDataApiBaseResult<String> getProductHistoryDailyReview(HaiYingLazadaProductHistoryDailyReviewRequest productHistoryDailyReviewRequest);

    HaiYingDataApiBaseResult<String> getCategoryTree(HaiYingLazadaCategoryTreeRequest categoryTreeRequest);

    HaiYingDataApiBaseResult<String> getTopCategoryInfo(HaiYingLazadaTopCategoryInfoRequest topCategoryInfoRequest);

    HaiYingDataApiBaseResult<String> getSubCategoryInfo(HaiYingLazadaSubCategoryInfoRequest subCategoryInfoRequest);


}
