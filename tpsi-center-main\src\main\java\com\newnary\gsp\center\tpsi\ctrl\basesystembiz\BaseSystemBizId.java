package com.newnary.gsp.center.tpsi.ctrl.basesystembiz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Data
@Component
public class BaseSystemBizId {
    @Value("${tpsi.bizIds}")
    private String bizIds;

    public String get(String bizCode) {
        JSONObject jsonObject = JSON.parseObject(bizIds);
        return jsonObject.getString(bizCode);
    }
}
