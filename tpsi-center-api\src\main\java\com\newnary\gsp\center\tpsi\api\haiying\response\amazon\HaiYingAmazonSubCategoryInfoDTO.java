package com.newnary.gsp.center.tpsi.api.haiying.response.amazon;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingAmazonSubCategoryInfoDTO {

    /**
     * 商品子级排名
     */
    private Integer rank;

    /**
     * 商品子类目id
     */
    private String cate_id;

    /**
     * 商品子类目名
     */
    private String cate_name;

    /**
     * 商品1级类目id
     */
    private String p_l1_id;

    /**
     * 商品2级类目id
     */
    private String p_l2_id;

    /**
     * 商品3级类目id
     */
    private String p_l3_id;

    /**
     * 商品4级类目id
     */
    private String p_l4_id;

    /**
     * 商品5级类目id
     */
    private String p_l5_id;

    /**
     * 商品6级类目id
     */
    private String p_l6_id;

    /**
     * 商品7级类目id
     */
    private String p_l7_id;

    /**
     * 商品8级类目id
     */
    private String p_l8_id;

    /**
     * 商品9级类目id
     */
    private String p_l9_id;

    /**
     * 商品10级类目id
     */
    private String p_l10_id;

    /**
     * 商品排名时间
     */
    private Long last_upd_date;

    /**
     * 商品1级类目名
     */
    private String p_l1_name;

    /**
     * 商品2级类目名
     */
    private String p_l2_name;

    /**
     * 商品3级类目名
     */
    private String p_l3_name;

    /**
     * 商品4级类目名
     */
    private String p_l4_name;

    /**
     * 商品5级类目名
     */
    private String p_l5_name;

    /**
     * 商品6级类目名
     */
    private String p_l6_name;

    /**
     * 商品7级类目名
     */
    private String p_l7_name;

    /**
     * 商品8级类目名
     */
    private String p_l8_name;

    /**
     * 商品9级类目名
     */
    private String p_l9_name;

    /**
     * 商品10级类目名
     */
    private String p_l10_name;

    /**
     * 商品类目排名突破前100时间
     */
    private Long break_top100_time;

    /**
     * 商品类目排名突破前200时间
     */
    private Long break_top200_time;

    /**
     * 商品首次进入新品榜的时间
     */
    private Long break_nsr_time;

    /**
     * 商品叶子类目最高排名标识
     */
    private Integer max_sub_cate_rank_flag;

    /**
     * 商品前7天排名上升均值
     */
    private BigDecimal average_ranking_in_seven_days;

    /**
     * 商品前30天天排名上升均值
     */
    private BigDecimal average_ranking_in_thirty_days;

    /**
     * 商品前90天排名中，前90-23天的最高排名是否大于前7天的均值排名
     * 0:否     1:是
     */
    private Integer asin_recommended_mark;

}
