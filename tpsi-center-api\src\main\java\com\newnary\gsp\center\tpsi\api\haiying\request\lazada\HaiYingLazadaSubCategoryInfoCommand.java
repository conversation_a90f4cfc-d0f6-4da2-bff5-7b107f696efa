package com.newnary.gsp.center.tpsi.api.haiying.request.lazada;

import com.newnary.api.base.common.PageCondition;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingOrderByType;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingShopeeCategoryListOrderBy;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingStation;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingLazadaSubCategoryInfoCommand {

    /**
     * 站点
     */
    @NotNull(message = "站点不能为空")
    private HaiYingStation station;

    /**
     * 类目名
     * (存在特殊字符,转码UTF-8)
     */
    private String cname;

    /**
     * 每一页的数据量(默认海鹰设置 全部)(int 型)
     * 数值范围[1-10000]
     */
    private PageCondition pageCondition;

}
