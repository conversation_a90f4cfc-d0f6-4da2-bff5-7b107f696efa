package com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.response;


import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class VVICGetItemListResponse {
    /**
     *商品数量
     */
    private Integer total;

    /**
     *返回结果语言en/cn
     */
    private String lang;

    /**
     * 页码
     */
    private Integer page;

    /**
     * 总页数
     */
    private Integer total_page;

    /**
     * 商品列表
     */
    private List<Item> item_list;

    @Getter
    @Setter
    public static class Item {

        /**
         * 商品VVIC ID
         */
        private String item_vid;

        /**
         * 商品 ID
         */
        private String item_id;

        /**
         * 商品标题
         */
        private String item_title;

        /**
         * 商品首图
         */
        private String item_view_image;

        /**
         * 创建时间
         */
        private String create_time;

        /**
         * 商品更新时间
         */
        private String update_time;

        /**
         * 商品上架时间
         */
        private String up_time;
    }

}
