package com.newnary.gsp.center.tpsi.infra.client.tk.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ShippingPackageReq {
    public String package_id;
    private int pick_up_type;
    public Integer pick_up_start_time;
    public Integer pick_up_end_time;
    public SelfShipment self_shipment;

    @Getter
    @Setter
    public static class SelfShipment {
        public String tracking_number;
        public String shipping_provider_id;
    }
}
