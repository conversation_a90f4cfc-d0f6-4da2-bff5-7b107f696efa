package com.newnary.gsp.center.tpsi.api.jt.vo.cn;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 2023-12-26
 * 发件信息对象
 * <AUTHOR>
 */
@Data
public class CreateOrderCnJTConsignee {

    @Size(max = 64,message = "consigneeName(最大64个字符)")
    @NotBlank(message = "consigneeName(不能为空)")
    private String consigneeName;

    @Size(max = 64,message = "consigneeCompany(最大64个字符)")
    private String consigneeCompany;

    @Size(max = 2,message = "consigneeCountry(最大2个字符)")
    @NotBlank(message = "consigneeCountry(不能为空)")
    private String consigneeCountry;

    @Size(max = 32,message = "consigneeProvince(最大32个字符)")
    @NotBlank(message = "consigneeProvince(不能为空)")
    private String consigneeProvince;

    @Size(max = 32,message = "consigneeCity(最大32个字符)")
    @NotBlank(message = "consigneeCity(不能为空)")
    private String consigneeCity;

    @Size(max = 32,message = "consigneeDistrict(最大32个字符)")
    private String consigneeDistrict;

    @Size(max = 255,message = "consigneeAddress(最大255个字符)")
    @NotBlank(message = "consigneeAddress(不能为空)")
    private String consigneeAddress;

    /**
     * 用于和地址1。进行拼接
     */
    @Size(max = 255,message = "consigneeAddress(最大255个字符)")
    private String consigneeAddress2;


    /**
     * 如渠道无特殊要求，请不要填写此字段
     */
    @Size(max = 32,message = "consigneeDoorplate(最大32个字符)")
    private String consigneeDoorplate;

    /**
     * 如渠道无特殊要求，请不要填写此字段。
     */
    @Size(max = 32,message = "consigneeStreet(最大32个字符)")
    private String consigneeStreet;

    @Size(max = 32,message = "consigneePostcode(最大32个字符)")
    private String consigneePostcode;

    @Size(max = 20,message = "consigneePostcode(最大20个字符)")
    private String consigneePhone;

    @Size(max = 20,message = "consigneeEmail(最大20个字符)")
    private String consigneeEmail;

    @Size(max = 20,message = "consigneeIdcard(最大20个字符)")
    private String consigneeIdcard;

    @Size(max = 20,message = "consigneeTaxNo(最大20个字符)")
    private String consigneeTaxNo;


}
