package com.newnary.gsp.center.tpsi.thirdpartymapping;

import com.alibaba.fastjson.JSON;
import com.newnary.api.base.common.PageCondition;
import com.newnary.common.utils.page.ParamUtil;
import com.newnary.dao.base.po.BaseQuery;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingInfo;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingType;
import com.newnary.gsp.center.tpsi.infra.repository.db.dao.ThirdPartyMappingExtDao;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.ThirdPartyMappingManager;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyMappingPO;
import com.newnary.test.starter.BaseTestInjectTenant;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ThirdPartyMappingTest extends BaseTestInjectTenant {
    @Override
    protected String tenantId() {
        return "TENANT3924117688555169382400";
    }

    @Resource
    private ThirdPartyMappingExtDao thirdPartyMappingExtDao;

    @Test
    public void testPageQuery() {
        BaseQuery<ThirdPartyMappingPO> query = new BaseQuery<>(new ThirdPartyMappingPO());
        PageCondition pageCondition = new PageCondition(1,50);
        query.getData().setSourceBizId("GSP");
        query.getData().setTargetBizId("VVIC");
        query.getData().setBizType("PRODUCT_ID");
        Map<String, Object> paramMap = ParamUtil.convert2ParamMap(query.getData());
        Integer startRow = (pageCondition.getPageNum() - 1) * pageCondition.getPageSize();
        paramMap.put("startRow", startRow);
        paramMap.put("pageSize", pageCondition.getPageSize());
        List<ThirdPartyMappingPO> thirdPartyMappingPOS = thirdPartyMappingExtDao.pageQuery(paramMap);
        System.out.println(JSON.toJSONString(thirdPartyMappingPOS));
    }

    @Resource
    private ThirdPartyMappingManager thirdPartyMappingManager;

    @Test
    public void testInsert(){
        thirdPartyMappingManager.insertOrUpdate("SHENGWEI", "ECCANG", "231106测试供应商", "11265", ThirdPartyMappingType.GSP_EC_SUP_N_ID.name(),null);
    }

    @Test
    public void testQuery(){
        ThirdPartyMappingInfo supplierMapping = thirdPartyMappingManager.getNewestInfoBySource("ECCANG", "SHENGWEI", "231106测试供应商", ThirdPartyMappingType.GSP_EC_SUP_N_ID);
        //ThirdPartyMappingInfo userMapping = thirdPartyMappingManager.getNewestInfoByTarget("ECCANG", "SHENGWEI", "USER0957311275106997506048", ThirdPartyMappingType.USER_ID);
        System.out.println(supplierMapping.getTargetId());
    }
}
