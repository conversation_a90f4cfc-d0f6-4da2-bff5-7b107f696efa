package com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.request;

import lombok.Data;

import java.util.List;

@Data
public class QueryOpen1688OrderDetailsRequest {

    /**
     * 站点信息，指定调用的API是属于国际站（alibaba）还是1688网站（1688）
     */
    private String webSite;

    /**
     * 交易的订单id
     */
    private String orderId;

    /**
     * 查询结果中包含的域，GuaranteesTerms：保障条款，NativeLogistics：物流信息，RateDetail：评价详情，OrderInvoice：发票信息。默认返回GuaranteesTerms、NativeLogistics、OrderInvoice。
     */
    private String includeFields;

    /**
     * 垂直表中的attributeKeys
     */
    private List<String> attributeKeys;
}
