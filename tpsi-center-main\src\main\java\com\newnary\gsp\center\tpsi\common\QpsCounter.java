package com.newnary.gsp.center.tpsi.common;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 模仿Sentinel滑动窗口统计QPS的方法
 *
 * <AUTHOR>
 * @since Created on 2025-06-16
 **/
public class QpsCounter {

    // 窗口长度（秒）
    private static final int WINDOW_LENGTH = 10;
    // 窗口分割的格数（如10格，每格100ms）
    private static final int SAMPLE_COUNT = 100;
    // 每格的时间跨度（毫秒）
    private static final int INTERVAL = WINDOW_LENGTH * 1000 / SAMPLE_COUNT;
    // 每个方法名对应一个滑动窗口
    private static final Map<String, Window> methodWindowMap = new ConcurrentHashMap<>();

    public static void record(String methodName) {
        Window window = methodWindowMap.computeIfAbsent(methodName, k -> new Window());
        window.record();
    }

    public static double getQps(String methodName) {
        Window window = methodWindowMap.get(methodName);
        if (window == null) {
            return 0.0;
        }
        return window.getQps();
    }

    public static double getQps(String methodName, int seconds) {
        if (seconds <= 0 || seconds > WINDOW_LENGTH) {
            throw new IllegalArgumentException("seconds must be between 1 and " + WINDOW_LENGTH);
        }
        Window window = methodWindowMap.get(methodName);
        if (window == null) {
            return 0.0;
        }
        return window.getQps(seconds);
    }

    // 滑动窗口内部类
    private static class Window {
        private final Slot[] slots = new Slot[SAMPLE_COUNT];

        public Window() {
            for (int i = 0; i < SAMPLE_COUNT; i++) {
                slots[i] = new Slot();
            }
        }

        public void record() {
            long now = System.currentTimeMillis();
            int idx = getIdx(now);
            long slotStart = getSlotStart(now);
            Slot slot = slots[idx];
            //noinspection SynchronizationOnLocalVariableOrMethodParameter
            synchronized (slot) {
                if (slot.timestamp != slotStart) {
                    slot.timestamp = slotStart;
                    slot.counter.set(0);
                }
                slot.counter.incrementAndGet();
            }
        }

        public double getQps() {
            return getQps(1);
        }

        public double getQps(int seconds) {
            long now = System.currentTimeMillis();
            int sum = 0;
            long windowStart = now - seconds * 1000L;
            for (int i = 0; i < SAMPLE_COUNT; i++) {
                Slot slot = slots[i];
                //noinspection SynchronizationOnLocalVariableOrMethodParameter
                synchronized (slot) {
                    if (slot.timestamp >= windowStart && slot.timestamp < now) {
                        sum += slot.counter.get();
                    }
                }
            }
            return sum * 1.0 / seconds;
        }

        private int getIdx(long now) {
            return (int) ((now / INTERVAL) % SAMPLE_COUNT);
        }

        private long getSlotStart(long now) {
            return now - (now % INTERVAL);
        }

    }

    private static class Slot {
        volatile long timestamp = 0;
        final AtomicInteger counter = new AtomicInteger(0);
    }

}
