package com.newnary.gsp.center.tpsi.infra.client.ejingling.valobj.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class EJingLingCreateOrderReq {
    /**
     * 外部订单id
     */
    @NotNull(message = "外部订单id不能为空")
    private String outerOrderId;

    /**
     * 外部订单编码
     */
    @NotNull(message = "外部订单编码不能为空")
    private String outerOrderNo;

    /**
     * 快递公司id，支持的公司由快递公司查询接口提供
     */
    @NotNull(message = "快递公司id不能为空")
    private String courierId;

    /**
     * 商品总金额
     */
    @NotNull(message = "商品总金额不能为空")
    private Double goodsAmount;

    /**
     * 运费
     */
    @NotNull(message = "运费不能为空")
    private Double postAmount;

    /**
     * 商品参数
     */
    @NotNull(message = "商品参数不能为空")
    private List<SkuParam> skuParams;

    /**
     *  发货信息
     */
    @NotNull(message = "发货信息不能为空")
    private ReceiveInfo receiveInfo;

    @Getter
    @Setter
    public static class SkuParam {

        /**
         * sku id
         */
        @NotNull(message = "skuId不能为空")
        private Long goodsSkuId;

        /**
         * 下单数量
         */
        @NotNull(message = "下单数量不能为空")
        private Integer skuNum;
    }

    @Getter
    @Setter
    public static class ReceiveInfo {

        /**
         * 省
         */
        @NotNull(message = "省份不能为空")
        private String province;

        /**
         * 市
         */
        @NotNull(message = "城市不能为空")
        private String city;

        /**
         * 区
         */
        @NotNull(message = "地区不能为空")
        private String district;

        /**
         * 详细收货地址
         */
        @NotNull(message = "详细收货地址不能为空")
        private String receiverAddress;

        /**
         * 收货人姓名
         */
        @NotNull(message = "收货人姓名不能为空")
        private String receiverName;

        /**
         * 收货人电话
         */
        @NotNull(message = "收货人电话不能为空")
        private String receiverMobile;
    }
}
