package com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.request;

import lombok.Data;

@Data
public class GetOpen1688OrderLogisticsInfoRequest {

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 需要返回的字段，目前有:company.name,sender,receiver,sendgood。返回的字段要用英文逗号分隔开
     */
    private String fields;

    /**
     * 是1688业务还是icbu业务
     */
    private String webSite;
}
