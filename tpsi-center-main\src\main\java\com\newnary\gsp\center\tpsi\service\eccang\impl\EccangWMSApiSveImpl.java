package com.newnary.gsp.center.tpsi.service.eccang.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.newnary.gsp.center.logistics.api.delivery.dto.TrackInfoDTO;
import com.newnary.gsp.center.logistics.api.delivery.dto.TransportItemDTO;
import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderDetailInfo;
import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderDetailItemInfo;
import com.newnary.gsp.center.logistics.api.delivery.response.TransportOrderPackageInfo;
import com.newnary.gsp.center.logistics.api.warehouse.response.ReceiptWarehouseRes;
import com.newnary.gsp.center.product.api.product.request.SupplierSpuLogisticsAttrInfo;
import com.newnary.gsp.center.product.api.product.response.ChannelSaleItemDetailInfo;
import com.newnary.gsp.center.tpsi.infra.client.eccang.EcCangWMSApiClient;
import com.newnary.gsp.center.tpsi.infra.client.eccang.params.EcCangWMSParams;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.wms.*;
import com.newnary.gsp.center.tpsi.infra.model.ApiRequestParams;
import com.newnary.gsp.center.tpsi.infra.model.DeliveryOrderEcCangApiAssociation;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.DeliveryOrderItemEcCangApiAssociation;
import com.newnary.gsp.center.tpsi.infra.rpc.SaleItemRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.ShippingMethodRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.WarehouseRpc;
import com.newnary.gsp.center.tpsi.service.eccang.IEccangWMSApiSve;
import com.newnary.gsp.center.tpsi.service.impl.SystemClientSve;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class EccangWMSApiSveImpl extends SystemClientSve implements IEccangWMSApiSve {

    @Resource
    private SaleItemRpc saleItemRpc;

    @Resource
    private ShippingMethodRpc shippingMethodRpc;

    @Resource
    private WarehouseRpc warehouseRpc;

    private static Map<String, EcCangWMSApiClient> apiClientMap = new ConcurrentHashMap<>();

    private static EcCangWMSApiClient getApiClient(String params) {
        EcCangWMSParams paramsObj = JSON.parseObject(params, EcCangWMSParams.class);
        return apiClientMap.computeIfAbsent(paramsObj.getAppkey(), k -> new EcCangWMSApiClient(paramsObj));
    }

    @Override
    public EcCangApiBaseResult<String> freightCreateOrder(TransportOrderPackageInfo transportPackageInfo, DeliveryOrderDetailInfo deliveryOrderDetailInfo, ThirdPartySystem thirdPartySystem, ApiRequestParams apiRequestParams, EcCangFreightCreateOrderRequest request) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());

        buildFreightCreateOrderRequest(transportPackageInfo, deliveryOrderDetailInfo, apiRequestParams, request);
        EcCangApiBaseResult<String> resultStr = ecCangWMSApiClient.sendFreightRequest(request, "/freight/freight/create-order", "FREIGHT");
        return resultStr;
    }

    @Override
    public EcCangApiBaseResult<String> freightCreateAbroadOrder(DeliveryOrderDetailInfo deliveryOrderDetailInfo, ThirdPartySystem thirdPartySystem, ApiRequestParams apiRequestParams, EcCangFreightCreateOrderRequest request, String trackingNum) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());

        buildFreightCreateAbroadOrderRequest(deliveryOrderDetailInfo, apiRequestParams, request, trackingNum);
        EcCangApiBaseResult<String> resultStr = ecCangWMSApiClient.sendFreightRequest(request, "/freight/freight/create-order", "FREIGHT");
        return resultStr;
    }

//    private static final Map<String, String> logisticsCodeMap = new HashMap<String, String>() {
//        //TODO 物流待确认并做对照
//        {
//            put("CN-PH-C1|PH-C1".toUpperCase(), "PH-A-STH-EX".toUpperCase()); //菲律宾空运大货特货头程+卡派  //TODO 测试代码
//            put("PH-AIR-PARCEL-BULK|PH-LOCAL-TRUCK".toUpperCase(), "PH-A-BTH-CD".toUpperCase());        //菲律宾空运大货特货头程+卡派
//            put("PH-AIR-PARCEL-BULK|PH-LOCAL-EXPRESS".toUpperCase(), "PH-A-BTH-EX".toUpperCase());      //菲律宾空运大货特货头程+快递
//            put("PH-AIR-PARCEL-BULK|PH-LOCAL-PICKUP".toUpperCase(), "PH-A-BTH-PU".toUpperCase());       //菲律宾空运大货特货头程+自提
//            put("PH-AIR-PARCEL-PACKET|PH-LOCAL-TRUCK".toUpperCase(), "PH-A-STH-CD".toUpperCase());      //菲律宾空运小包特货头程+卡派
//            put("PH-AIR-PARCEL-PACKET|PH-LOCAL-EXPRESS".toUpperCase(), "PH-A-STH-EX".toUpperCase());    //菲律宾空运小包特货头程+快递
//            put("PH-AIR-PARCEL-PACKET|PH-LOCAL-PICKUP".toUpperCase(), "PH-A-STH-PU".toUpperCase());     //菲律宾空运小包特货头程+自提
//            put("PH-SEA-PARCEL-BULK|PH-LOCAL-TRUCK".toUpperCase(), "PH-S-TH-CD".toUpperCase());         //菲律宾海运特货头程+卡派
//            put("PH-SEA-PARCEL-BULK|PH-LOCAL-EXPRESS".toUpperCase(), "PH-S-TH-EX".toUpperCase());       //菲律宾海运特货头程+快递
//            put("PH-SEA-PARCEL-BULK|PH-LOCAL-PICKUP".toUpperCase(), "PH-S-TH-PU".toUpperCase());        //菲律宾海运特货头程+自提
//        }
//    };

//    private static final Map<String, String> logisticsCodeMap = new HashMap<String, String>() {
//        //TODO 头尾程分别做对照 @jack 2022-11-30
//        {
//            put("PH-AIR-PARCEL-PACKET".toUpperCase(), "PH-A-STH-PU".toUpperCase());     //菲律宾空运小包特货头程
//            put("PH-AIR-PARCEL-BULK".toUpperCase(), "PH-A-BTH-PU".toUpperCase());       //菲律宾空运大货特货头程
//            put("PH-SEA-PARCEL-BULK".toUpperCase(), "PH-S-TH-PU".toUpperCase());        //菲律宾海运特货头程
//            put("PH-LOCAL-EXPRESS".toUpperCase(), "PH-EXPRESS-SECOND".toUpperCase());       //快递
//            put("PH-LOCAL-TRUCK".toUpperCase(), "PHLCL".toUpperCase());      //卡派
//            put("PH-LOCAL-PICKUP".toUpperCase(), "SELFPICKUP".toUpperCase());    //自提
//        }
//    };

    private volatile Map<String, String> logisticsCodeMap;

    private Map<String, String> getLogisticsCodeMap() {
        if (logisticsCodeMap == null) {
            synchronized (this) {
                if (logisticsCodeMap == null) {
                    logisticsCodeMap = new HashMap<>(shippingMethodRpc.getLogisticsChannelMappingList(""));
                }
            }
        }
        return logisticsCodeMap;
    }

    private static final Map<String, String> warehouseCodeMap = new HashMap<String, String>() {
        //TODO 仓库编码与WMS仓库编码做对照 @zhongjing 2022-12-15
        {
            put("RH-PH-ZY-TT001".toUpperCase(), "PGLPH01".toUpperCase());
            put("TIKTOK-SELF-PH".toUpperCase(), "PGLPH01".toUpperCase());
        }
    };
    private static final Map<String, String> shippingMethodCodeMap = new HashMap<String, String>() {
        //TODO 运输方式映射 @zhongjing 2022-12-15
        {
            put("J&T Express".toUpperCase(), "TIKTOK".toUpperCase());
            put("Ninja Van Philippines".toUpperCase(), "TIKTOK".toUpperCase());
            put("Flash Express Philippines".toUpperCase(), "TIKTOK".toUpperCase());
        }
    };

    private String convertLogisticsCode(String firstShippingMethodCode, String lastShippingMethodCode) {
        String key = firstShippingMethodCode.concat("|").concat(lastShippingMethodCode);
        return getLogisticsCodeMap().get(key.toUpperCase());
    }

    private String convertLogisticsCode(String shippingMethodCode) {
        String logisticsCode = getLogisticsCodeMap().get(shippingMethodCode.toUpperCase());
        if (StringUtils.isEmpty(logisticsCode)) {
            log.error("{}找不到物流产品匹配代码", shippingMethodCode.toUpperCase());
        }
        return logisticsCode;
    }

    private EcCangFreightCreateOrderRequest buildFreightCreateOrderRequest(TransportOrderPackageInfo transportPackageInfo, DeliveryOrderDetailInfo deliveryOrderDetailInfo, ApiRequestParams apiRequestParams, EcCangFreightCreateOrderRequest request) {
        EcCangFreightCreateOrderRequest params = JSON.parseObject(apiRequestParams.getParams(), EcCangFreightCreateOrderRequest.class);

//        request.freight_order_code = deliveryOrderDetailInfo.tradeOrderId.concat("-").concat(deliveryOrderDetailInfo.tradeSubOrderId);  //TODO 待定用哪个单号
        request.freight_order_code = deliveryOrderDetailInfo.tradeOrderId;

        request.warehouse_id = params.warehouse_id;

        List<TrackInfoDTO> stockoutTrackingInfos = deliveryOrderDetailInfo.getStockoutTrackingInfos();

        //物流产品
        request.head_logistics_code = deliveryOrderDetailInfo.internationalChannelId != null || deliveryOrderDetailInfo.firstShippingMethod != null ? getLogisticsProductCode(deliveryOrderDetailInfo.internationalChannelId, deliveryOrderDetailInfo.firstShippingMethod, deliveryOrderDetailInfo.goodsType) : getLogisticsProductCode(deliveryOrderDetailInfo.terminalChannelId, deliveryOrderDetailInfo.lastShippingMethod, deliveryOrderDetailInfo.goodsType);
        request.end_logistics_code = deliveryOrderDetailInfo.terminalChannelId != null || deliveryOrderDetailInfo.lastShippingMethod != null ? getLogisticsProductCode(deliveryOrderDetailInfo.terminalChannelId, deliveryOrderDetailInfo.lastShippingMethod, deliveryOrderDetailInfo.goodsType) : getLogisticsProductCode(deliveryOrderDetailInfo.internationalChannelId, deliveryOrderDetailInfo.firstShippingMethod, deliveryOrderDetailInfo.goodsType);
/*        if (StringUtils.isNotBlank(deliveryOrderDetailInfo.internationalChannelId) || StringUtils.isNotBlank(deliveryOrderDetailInfo.firstShippingMethod)) {
            request.head_logistics_code = getLogisticsProductCode(deliveryOrderDetailInfo.internationalChannelId, deliveryOrderDetailInfo.firstShippingMethod, deliveryOrderDetailInfo.goodsType);
            request.end_logistics_code = request.head_logistics_code;
        } else {
            request.head_logistics_code = getLogisticsProductCode(deliveryOrderDetailInfo.terminalChannelId,deliveryOrderDetailInfo.lastShippingMethod,deliveryOrderDetailInfo.goodsType);
            request.end_logistics_code = request.head_logistics_code;
        }*/

        request.platform = params.platform;
        request.site = transportPackageInfo.getConsignee().getAddrCountry();   //站点需填国家二字码
        request.currency_code = params.currency_code;

        request.rec_name = transportPackageInfo.getConsignee().getName();
        request.rec_phone = transportPackageInfo.getConsignee().getContactNumber();
        request.rec_country_code = transportPackageInfo.getConsignee().getAddrCountry();

//        if (deliveryOrderDetailInfo.isCod) {
//            List<ReceiptWarehouseRes> offices = warehouseRpc.getReceiptWarehouseList("office");
//            if (StringUtils.isNotBlank(deliveryOrderDetailInfo.consigneeInfo.freightWarehouse)) {
//                Optional<ReceiptWarehouseRes> first = offices.stream().filter(receiptWarehouseRes -> deliveryOrderDetailInfo.consigneeInfo.freightWarehouse.equals(receiptWarehouseRes.getName())).findFirst();
//                first.ifPresent(receiptWarehouseRes -> request.rec_address = receiptWarehouseRes.getAddrDetail());
//            } else {
//                request.rec_address = deliveryOrderDetailInfo.consigneeInfo.addrDetail;
//            }
//        } else {
//            request.rec_address = deliveryOrderDetailInfo.consigneeInfo.addrDetail;
//        }

        request.rec_address = deliveryOrderDetailInfo.consigneeInfo.addrDetail;
        List<ReceiptWarehouseRes> warehouseResList = warehouseRpc.getReceiptWarehouseList("warehouse");
        if (CollectionUtils.isNotEmpty(warehouseResList)) {
            warehouseResList.forEach(warehouseRes -> {
                if (warehouseRes.getName().equalsIgnoreCase("PH")) {
                    request.rec_address = warehouseRes.getAddrDetail();
                }
            });
        }
        request.rec_province = transportPackageInfo.getConsignee().getAddrProvince();
        request.rec_city = transportPackageInfo.getConsignee().getAddrCity();
        request.rec_district = transportPackageInfo.getConsignee().getAddrDistrict();
        request.postal_code = transportPackageInfo.getConsignee().getZipCode();

        request.is_reviewed = 1;
        request.order_remark = "";

        request.order_type = 0;
        List<TransportItemDTO> itemInfos = transportPackageInfo.getItems();
        List<EcCangFreightCreateOrderRequest.ProductData> productDatas = new ArrayList<>();
        for (TransportItemDTO itemInfo : itemInfos) {
            EcCangFreightCreateOrderRequest.ProductData productData = new EcCangFreightCreateOrderRequest.ProductData();
            try {
                ChannelSaleItemDetailInfo channelSaleItemDetailInfo = saleItemRpc.querySaleItem(deliveryOrderDetailInfo.channelId, itemInfo.getSaleItemCode());
                log.info("channelSaleItemDetailInfo: {}", JSON.toJSONString(channelSaleItemDetailInfo));
                JSONArray specsArray = new JSONArray();
                if (CollectionUtils.isNotEmpty(channelSaleItemDetailInfo.getSkuDetailInfo().getSkuInfo().getSpecs())) {
                    channelSaleItemDetailInfo.getSkuDetailInfo().getSkuInfo().getSpecs().forEach(supplierSkuSpecInfo -> {
                        JSONObject specsJson = new JSONObject();
                        specsJson.put(supplierSkuSpecInfo.getSpecName(), supplierSkuSpecInfo.getSpecValue());
                        specsArray.add(specsJson);
                    });
                }
                //采购包裹号和供应商发货快递号
                productData.package_code = null != stockoutTrackingInfos ? stockoutTrackingInfos.get(0).getTrackingNo() : null;

                productData.product_standard = specsArray.toJSONString();
                productData.product_img_url = CollectionUtils.isNotEmpty(itemInfo.getSupplierSkuPicUrl()) ? itemInfo.getSupplierSkuPicUrl().get(0) : channelSaleItemDetailInfo.skuDetailInfo.spuBaseInfo.mainImageUrls.stream().findAny().get();
                //String categoryName = channelSaleItemDetailInfo.skuDetailInfo.spuBaseInfo.customCategoryName;
                String categoryName = channelSaleItemDetailInfo.skuDetailInfo.spuBaseInfo.descInfo.customCategoryName;
                productData.product_category = StringUtils.isEmpty(categoryName) ? "未分类" : categoryName;
                SupplierSpuLogisticsAttrInfo logisticsAttrInfo = channelSaleItemDetailInfo.skuDetailInfo.spuBaseInfo.logisticsAttrInfo;
                productData.hs_code = ObjectUtils.isNotEmpty(logisticsAttrInfo) ? logisticsAttrInfo.hscode : "";

                //用customCode?
                productData.child_code = StringUtils.isEmpty(itemInfo.getCustomCode()) ? itemInfo.getSupplierSkuId() : itemInfo.getCustomCode();
                productData.product_count = itemInfo.getQuantity();

                String nameCn = StringUtils.isEmpty(itemInfo.getInvoiceCnName()) ? itemInfo.getSupplierSkuTitle() : itemInfo.getInvoiceCnName();
                String nameEn = StringUtils.isEmpty(itemInfo.getInvoiceEnName()) ? nameCn : itemInfo.getInvoiceEnName();

                productData.product_name_cn = nameCn;
                productData.product_name_en = nameEn;
                productData.product_declared = itemInfo.getSupplyPrice().doubleValue() == 0d ? 1d : itemInfo.getSupplyPrice().doubleValue();
            } catch (Exception e) {
                e.printStackTrace();
                log.error("获取detailItem详情出错{}", itemInfo.getSaleItemCode());
            }
            productDatas.add(productData);
        }
        request.product_data = productDatas;
        return request;
    }

    private EcCangFreightCreateOrderRequest buildFreightCreateAbroadOrderRequest(DeliveryOrderDetailInfo deliveryOrderDetailInfo, ApiRequestParams apiRequestParams, EcCangFreightCreateOrderRequest request, String trackingNum) {
        EcCangFreightCreateOrderRequest params = JSON.parseObject(apiRequestParams.getParams(), EcCangFreightCreateOrderRequest.class);

//        request.freight_order_code = deliveryOrderDetailInfo.tradeOrderId.concat("-").concat(deliveryOrderDetailInfo.tradeSubOrderId).concat("-PH");  //TODO 待定用哪个单号
        request.freight_order_code = deliveryOrderDetailInfo.tradeOrderId;

        List<TrackInfoDTO> stockoutTrackingInfos = deliveryOrderDetailInfo.getStockoutTrackingInfos();

        request.warehouse_id = params.ph_warehouse_id;

        //物流产品
/*        if (StringUtils.isNotBlank(deliveryOrderDetailInfo.internationalChannelId) || StringUtils.isNotBlank(deliveryOrderDetailInfo.firstShippingMethod)) {
            request.head_logistics_code = getLogisticsProductCode(deliveryOrderDetailInfo.internationalChannelId, deliveryOrderDetailInfo.firstShippingMethod, deliveryOrderDetailInfo.goodsType);
            request.end_logistics_code = request.head_logistics_code;
        } else {
            request.head_logistics_code = getLogisticsProductCode(deliveryOrderDetailInfo.terminalChannelId,deliveryOrderDetailInfo.lastShippingMethod,deliveryOrderDetailInfo.goodsType);
            request.end_logistics_code = request.head_logistics_code;
        }*/
        request.head_logistics_code = deliveryOrderDetailInfo.internationalChannelId != null || deliveryOrderDetailInfo.firstShippingMethod != null ? getLogisticsProductCode(deliveryOrderDetailInfo.internationalChannelId, deliveryOrderDetailInfo.firstShippingMethod, deliveryOrderDetailInfo.goodsType) : getLogisticsProductCode(deliveryOrderDetailInfo.terminalChannelId, deliveryOrderDetailInfo.lastShippingMethod, deliveryOrderDetailInfo.goodsType);
        request.end_logistics_code = deliveryOrderDetailInfo.terminalChannelId != null || deliveryOrderDetailInfo.lastShippingMethod != null ? getLogisticsProductCode(deliveryOrderDetailInfo.terminalChannelId, deliveryOrderDetailInfo.lastShippingMethod, deliveryOrderDetailInfo.goodsType) : getLogisticsProductCode(deliveryOrderDetailInfo.internationalChannelId, deliveryOrderDetailInfo.firstShippingMethod, deliveryOrderDetailInfo.goodsType);
        request.platform = params.platform;
        request.site = deliveryOrderDetailInfo.consigneeInfo.addrCountry;   //站点需填国家二字码
        request.currency_code = params.currency_code;

        if (deliveryOrderDetailInfo.isCod) {
            List<ReceiptWarehouseRes> offices = warehouseRpc.getReceiptWarehouseList("office");
            if (StringUtils.isNotBlank(deliveryOrderDetailInfo.consigneeInfo.freightWarehouse)) {
                Optional<ReceiptWarehouseRes> first = offices.stream().filter(receiptWarehouseRes -> deliveryOrderDetailInfo.consigneeInfo.freightWarehouse.equals(receiptWarehouseRes.getName())).findFirst();
                first.ifPresent(receiptWarehouseRes -> request.rec_address = receiptWarehouseRes.getAddrDetail());
            } else {
                request.rec_address = deliveryOrderDetailInfo.consigneeInfo.addrDetail;
            }
        } else {
            request.rec_address = deliveryOrderDetailInfo.consigneeInfo.addrDetail;
        }
        request.rec_name = deliveryOrderDetailInfo.consigneeInfo.name;
        request.rec_phone = deliveryOrderDetailInfo.consigneeInfo.contactNumber;
        request.rec_country_code = deliveryOrderDetailInfo.consigneeInfo.addrCountry;
        request.rec_province = deliveryOrderDetailInfo.consigneeInfo.addrProvince;
        request.rec_city = deliveryOrderDetailInfo.consigneeInfo.addrCity;
        request.rec_district = deliveryOrderDetailInfo.consigneeInfo.addrDistrict;
        request.postal_code = deliveryOrderDetailInfo.consigneeInfo.zipCode;

        request.is_reviewed = 1;
        request.order_remark = "";

        request.total_amount = Double.valueOf(deliveryOrderDetailInfo.amountInfo.itemTotalAmount);
        request.order_type = 0;
        List<DeliveryOrderDetailItemInfo> itemInfos = deliveryOrderDetailInfo.items;
        List<EcCangFreightCreateOrderRequest.ProductData> productDatas = new ArrayList<>();
        for (DeliveryOrderDetailItemInfo itemInfo : itemInfos) {
            EcCangFreightCreateOrderRequest.ProductData productData = new EcCangFreightCreateOrderRequest.ProductData();
            try {
                ChannelSaleItemDetailInfo channelSaleItemDetailInfo = saleItemRpc.querySaleItem(deliveryOrderDetailInfo.channelId, itemInfo.getSaleItemCode());
                JSONArray specsArray = new JSONArray();
                if (CollectionUtils.isNotEmpty(channelSaleItemDetailInfo.getSkuDetailInfo().getSkuInfo().getSpecs())) {
                    channelSaleItemDetailInfo.getSkuDetailInfo().getSkuInfo().getSpecs().forEach(supplierSkuSpecInfo -> {
                        JSONObject specsJson = new JSONObject();
                        specsJson.put(supplierSkuSpecInfo.getSpecName(), supplierSkuSpecInfo.getSpecValue());
                        specsArray.add(specsJson);
                    });
                }
                //采购包裹号和供应商发货快递号
                productData.package_code = null != stockoutTrackingInfos ? stockoutTrackingInfos.get(0).getTrackingNo() : null;

                productData.product_standard = specsArray.toJSONString();
                //商品图片
                productData.product_img_url = CollectionUtils.isNotEmpty(itemInfo.getSupplierSkuPicUrl()) ? itemInfo.getSupplierSkuPicUrl().get(0) : channelSaleItemDetailInfo.skuDetailInfo.spuBaseInfo.mainImageUrls.stream().findAny().get();

                //String categoryName = channelSaleItemDetailInfo.skuDetailInfo.spuBaseInfo.customCategoryName;
                String categoryName = channelSaleItemDetailInfo.skuDetailInfo.spuBaseInfo.descInfo.customCategoryName;
                productData.product_category = StringUtils.isEmpty(categoryName) ? "未分类" : categoryName;
                //productData.hs_code = channelSaleItemDetailInfo.skuDetailInfo.spuBaseInfo.logisticsAttrInfo.hscode;

                //用customCode?
                productData.child_code = StringUtils.isEmpty(itemInfo.customCode) ? itemInfo.supplierSkuId : itemInfo.customCode;
                productData.product_count = itemInfo.quantity;

                String nameCn = StringUtils.isEmpty(itemInfo.invoiceCnName) ? itemInfo.supplierSkuTitle : itemInfo.invoiceCnName;
                String nameEn = StringUtils.isEmpty(itemInfo.invoiceEnName) ? nameCn : itemInfo.invoiceEnName;

                productData.product_name_cn = nameCn;
                productData.product_name_en = nameEn;
                productData.product_declared = Double.valueOf(itemInfo.supplyPrice) == 0d ? 1d : Double.valueOf(itemInfo.supplyPrice);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("获取detailItem详情出错{}", itemInfo.getSaleItemCode());
            }
            productDatas.add(productData);
        }
        request.product_data = productDatas;
        return request;
    }

    //获取物流产品编码（映射）
    private String getLogisticsProductCode(String logisticId, String logisticProductId, String goodsType) {
        String code = "";
        String logisticsCode;
        if (StringUtils.isNotBlank(logisticProductId)) {
            logisticsCode = shippingMethodRpc.getLogisticsProductCodeById(logisticProductId);
            if (StringUtils.isNotBlank(logisticsCode)) {
                code = this.convertLogisticsCode(logisticsCode);
            }
            if (StringUtils.isNotBlank(logisticsCode) && StringUtils.isBlank(code)) {
                code = this.convertLogisticsCode(logisticsCode.concat("-".concat(goodsType)));
            }
        } else if (StringUtils.isNotBlank(logisticId)) {
            logisticsCode = shippingMethodRpc.getLogisticsCodeById(logisticId);
            if (StringUtils.isNotBlank(logisticsCode)) {
                code = this.convertLogisticsCode(logisticsCode);
            } else {
                logisticsCode = shippingMethodRpc.getLogisticsProductCodeById(logisticProductId);
                if (StringUtils.isNotBlank(logisticsCode)) {
                    code = this.convertLogisticsCode(logisticsCode);
                }
            }
            if (StringUtils.isNotBlank(logisticsCode) && StringUtils.isBlank(code)) {
                code = this.convertLogisticsCode(logisticsCode.concat("-".concat(goodsType)));
            }
        } else {
            log.info("构建创建wms订单请求失败：物流产品为空：{} {}", logisticId, logisticProductId);
            //throw new ServiceException(new BaseErrorInfo("500","物流产品为空"));
        }
        return code;
    }

    @Override
    public EcCangApiBaseResult<String> freightCreateProduct(DeliveryOrderEcCangApiAssociation association, ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());

        EcCangFreightCreateProductRequest request = buildFreightCreateProductRequest(association, association.getWmsOrderCode(), association.getWmsWarehouseId());
        if (null == request) {
            return null;
        }
        EcCangApiBaseResult<String> resultStr = ecCangWMSApiClient.sendFreightRequest(request, "/freight/freight/create-product", "FREIGHT");
        return resultStr;
    }

    private EcCangFreightCreateProductRequest buildFreightCreateProductRequest(DeliveryOrderEcCangApiAssociation association, String wmsOrderCode, Integer wmsWarehouseId) {
        EcCangFreightCreateProductRequest request = new EcCangFreightCreateProductRequest();
        request.order_code = wmsOrderCode;
        request.warehouse_id = wmsWarehouseId;
        List<EcCangFreightCreateProductRequest.ProductData> productDatas = new ArrayList<>();

        association.getItems().forEach(item -> {
//            if (StringUtils.isNotEmpty(item.getErpPurchaseOrderTrackingNo())) {
            EcCangFreightCreateProductRequest.ProductData productData = new EcCangFreightCreateProductRequest.ProductData();
            productData.pid = item.getWmsProductPid();
            productData.child_code = item.getWmsChildCode();
            productData.package_code = StringUtils.isNotEmpty(item.getErpPurchaseOrderTrackingNo()) ? item.getErpPurchaseOrderTrackingNo() : "";
            productData.product_count = item.getWmsProductCount();
            productData.product_category = item.getWmsProductCategory();
            productData.product_name_cn = item.getWmsProductNameCn();
            productData.product_name_en = item.getWmsProductNameEn();
            productData.product_standard = item.getWmsProductStandard();
            productData.product_img_url = item.getWmsProductImgUrl();
            productData.product_declared = item.getWmsProductDeclared() == 0d ? 1d : item.getWmsProductDeclared();
            productDatas.add(productData);
//            }
        });
        request.product_data = productDatas;
        if (CollectionUtils.isEmpty(productDatas)) {
            log.error("{}需要修改的内容为空", wmsOrderCode);
            return null;
        }
        return request;
    }

    @Override
    public EcCangApiBaseResult<String> freightCreateProduct(DeliveryOrderItemEcCangApiAssociation item, ThirdPartySystem thirdPartySystem, String wmsOrderCode, Integer wmsWarehouseId) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());

        EcCangFreightCreateProductRequest request = buildFreightCreateProductRequest(item, wmsOrderCode, wmsWarehouseId);
        EcCangApiBaseResult<String> resultStr = ecCangWMSApiClient.sendFreightRequest(request, "/freight/freight/create-product", "FREIGHT");
        return resultStr;
    }

    private EcCangFreightCreateProductRequest buildFreightCreateProductRequest(DeliveryOrderItemEcCangApiAssociation item, String wmsOrderCode, Integer wmsWarehouseId) {
        EcCangFreightCreateProductRequest request = new EcCangFreightCreateProductRequest();
        request.order_code = wmsOrderCode;
        request.warehouse_id = wmsWarehouseId;
        List<EcCangFreightCreateProductRequest.ProductData> productDatas = new ArrayList<>();
        EcCangFreightCreateProductRequest.ProductData productData = new EcCangFreightCreateProductRequest.ProductData();
        productData.pid = item.getWmsProductPid();
        productData.child_code = item.getWmsChildCode();
        productData.package_code = item.getErpPurchaseOrderTrackingNo();
        productData.product_count = item.getWmsProductCount();
        productData.product_category = item.getWmsProductCategory();
        productData.product_name_cn = item.getWmsProductNameCn();
        productData.product_name_en = item.getWmsProductNameEn();
        productData.product_standard = item.getWmsProductStandard();
        productData.product_img_url = item.getWmsProductImgUrl();
        productData.product_declared = item.getWmsProductDeclared() == 0d ? 1d : item.getWmsProductDeclared();;
        productDatas.add(productData);
        request.product_data = productDatas;
        return request;
    }

    @Override
    public EcCangApiBaseResult<String> freightGetOrderInfo(String wmsOrderId, ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());

        EcCangFreightGetOrderInfoRequest request = buildFreightGetOrderInfoRequest(wmsOrderId);
        EcCangApiBaseResult<String> resultStr = ecCangWMSApiClient.sendFreightRequest(request, "/freight/freight/get-order-info", "FREIGHT");
        return resultStr;
    }

    private EcCangFreightGetOrderInfoRequest buildFreightGetOrderInfoRequest(String wmsOrderId) {
        EcCangFreightGetOrderInfoRequest request = new EcCangFreightGetOrderInfoRequest();
        List<String> order_code = new ArrayList<>();
        order_code.add(wmsOrderId);
        request.order_code = order_code;
        return request;
    }

    @Override
    public EcCangApiBaseResult<String> freightCancelOrder(DeliveryOrderEcCangApiAssociation association, ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());

        EcCangFreightCancelOrderRequest request = buildFreightCancelOrderRequest(association);
        EcCangApiBaseResult<String> resultStr = ecCangWMSApiClient.sendFreightRequest(request, "/freight/freight/cancel-order", "FREIGHT");
        return resultStr;
    }

    private EcCangFreightCancelOrderRequest buildFreightCancelOrderRequest(DeliveryOrderEcCangApiAssociation association) {
        EcCangFreightCancelOrderRequest request = new EcCangFreightCancelOrderRequest();
        request.order_code = association.getWmsOrderCode();
        request.warehouse_id = association.getWmsWarehouseId();
        return request;
    }

    @Override
    public EcCangApiBaseResult<String> freightCreateOrderLabel(String wmsOrderCode, String orderLabelCode, String labelType, String labelUrl, ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());

        EcCangFreightCreateOrderLabelRequest request = buildFreightCreateOrderLabelRequest(wmsOrderCode, orderLabelCode, labelType, labelUrl);
        EcCangApiBaseResult<String> resultStr = ecCangWMSApiClient.sendFreightRequest(request, "/freight/freight/create-order-label", "FREIGHT");
        return resultStr;
    }

    private EcCangFreightCreateOrderLabelRequest buildFreightCreateOrderLabelRequest(String wmsOrderCode, String orderLabelCode, String labelType, String labelUrl) {
        EcCangFreightCreateOrderLabelRequest request = new EcCangFreightCreateOrderLabelRequest();
        request.order_code = wmsOrderCode;
        request.order_label_code = orderLabelCode;
        request.label_type = labelType;
        request.label_url = labelUrl;
        return request;
    }

    @Override
    public EcCangApiBaseResult<String> getClaimOrders(ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());

        EcCangFreightGetClaimOrdersRequest request = buildFreightGetClaimOrdersRequest();
        EcCangApiBaseResult<String> resultStr = ecCangWMSApiClient.sendFreightRequest(request, "/freight/freight/get-claim-orders", "sendFreightRequest");
        return resultStr;
    }

    private EcCangFreightGetClaimOrdersRequest buildFreightGetClaimOrdersRequest() {
        EcCangFreightGetClaimOrdersRequest request = new EcCangFreightGetClaimOrdersRequest();
        return request;
    }

    @Override
    public EcCangApiBaseResult<String> getWarehouse(ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());
        return ecCangWMSApiClient.sendWMSRequest(null, "getWarehouse");
    }

    @Override
    public EcCangApiBaseResult<String> createProduct(EcCangCreateProductRequest request, ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());
        return ecCangWMSApiClient.sendWMSRequest(request, "createProduct");
    }

    @Override
    public EcCangApiBaseResult<String> updateProduct(EcCangUpdateProductRequest request, ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());
        return ecCangWMSApiClient.sendWMSRequest(request, "modifyProduct");
    }

    @Override
    public EcCangApiBaseResult<String> getProductStock(EcCangGetProductStockRequest req, ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());
        if (req.getWarehouse_code() != null) {
            req.setWarehouse_code(warehouseCodeMap.get(req.getWarehouse_code().toUpperCase()));
            return ecCangWMSApiClient.sendWMSRequest(req, "getProductInventory");
        }
        EcCangApiBaseResult<String> objectEcCangApiBaseResult = new EcCangApiBaseResult<>();
        objectEcCangApiBaseResult.setData("{\"ask\":\"Failure\"}");
        return objectEcCangApiBaseResult;
    }

    @Override
    public EcCangApiBaseResult<String> createOrder(EcCangCreateOrderRequest request, ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());
        if (null != request.getWarehouse_code() && null != request.getShipping_method()) {
            request.setWarehouse_code(warehouseCodeMap.get(request.getWarehouse_code().toUpperCase()));
            request.setShipping_method(shippingMethodCodeMap.get(request.getShipping_method().toUpperCase()));
            return ecCangWMSApiClient.sendWMSRequest(request, "createOrder");
        }
        EcCangApiBaseResult<String> objectEcCangApiBaseResult = new EcCangApiBaseResult<>();
        objectEcCangApiBaseResult.setData("{\"ask\":\"Failure\"}");
        return objectEcCangApiBaseResult;
    }

    @Override
    public EcCangApiBaseResult<String> cancelOrder(EcCangCancelOrderRequest request, ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());
        return ecCangWMSApiClient.sendWMSRequest(request, "cancelOrder");
    }

    @Override
    public EcCangApiBaseResult<String> getOrderState(EcCangGetOrderStateRequest request, ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());
        return ecCangWMSApiClient.sendWMSRequest(request, "getOrderStatusByCode");
    }

    @Override
    public EcCangApiBaseResult<String> getOrderTracking(String orderNumber, ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());
        EcCangGetOrderTrackingRequest request = new EcCangGetOrderTrackingRequest();
        request.setOrder_numbers(orderNumber);
        return ecCangWMSApiClient.sendWMSRequest(request, "getOrderTracking");
    }

    @Override
    public EcCangApiBaseResult<String> getShippingMethod(EcCangGetShippingMethodRequest request, ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());
        request.setWarehouseCode(warehouseCodeMap.get("RH-PH-ZY-TT001".toUpperCase()));
        return ecCangWMSApiClient.sendWMSRequest(request, "getShippingMethod");
    }

    @Override
    public EcCangApiBaseResult<String> getOrderList(EcCangGetOrderListRequest request, ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());
        return ecCangWMSApiClient.sendWMSRequest(request, "getOrderList");
    }

    @Override
    public EcCangApiBaseResult<String> createReturnBill(EcCangCreateReturnBillRequest request, ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());
        return ecCangWMSApiClient.sendWMSRequest(request, "createReturnBill");
    }

    @Override
    public EcCangApiBaseResult<String> updateReturnBill(EcCangUpdateReturnBillRequest request, ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());
        return ecCangWMSApiClient.sendWMSRequest(request, "updateReturnBill");
    }

    @Override
    public EcCangApiBaseResult<String> getReturnBill(EcCangGetReturnBillRequest request, ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());
        return ecCangWMSApiClient.sendWMSRequest(request, "getReturnBill");
    }

    @Override
    public EcCangApiBaseResult<String> uploadFile(EcCangUploadFileRequest request, ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());
        return ecCangWMSApiClient.sendWMSRequest(request, "uploadFile");
    }

    @Override
    public EcCangApiBaseResult<String> saveOrderAttach(EcCangSaveOrderAttachRequest request, ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());
        return ecCangWMSApiClient.sendWMSRequest(request, "saveOrderAttach");
    }

    @Override
    public EcCangApiBaseResult<String> modifyOrder(EcCangModifyOrderRequest request, ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());
        return ecCangWMSApiClient.sendWMSRequest(request, "modifyOrder");
    }

    @Override
    public EcCangApiBaseResult<String> dealOrderSendRequest(EcCangFreightDealOrderSendRequest request, ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangWMSApiClient ecCangWMSApiClient = getApiClient(thirdPartySystem.getParams());
        return ecCangWMSApiClient.sendFreightRequest(request, "/freight/freight/deal-order-send-request", "sendFreightRequest");
    }

//    private EcCangWMSApiClient getClient(String ecCangParams) {
//        return new EcCangWMSApiClient(ecCangParams);
//    }

}
