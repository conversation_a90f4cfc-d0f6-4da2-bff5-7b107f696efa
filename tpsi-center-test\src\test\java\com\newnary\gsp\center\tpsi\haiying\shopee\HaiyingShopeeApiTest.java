package com.newnary.gsp.center.tpsi.haiying.shopee;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.common.PageCondition;
import com.newnary.api.base.common.PageList;
import com.newnary.api.base.common.PageMeta;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingOrderByType;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingShopeeCategoryListOrderBy;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingShopeeKeywordListOrderBy;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingStation;
import com.newnary.gsp.center.tpsi.api.haiying.request.shopee.*;
import com.newnary.gsp.center.tpsi.api.haiying.response.shopee.*;
import com.newnary.gsp.center.tpsi.ctrl.haiying.HaiYingShopeeCommand2RequestTranslator;
import com.newnary.gsp.center.tpsi.ctrl.haiying.HaiYingShopeeResponse2DTOTranslator;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.HaiYingDataApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.shopee.*;
import com.newnary.gsp.center.tpsi.service.haiying.IHaiYingDataShopeeApiSve;
import com.newnary.test.starter.BaseTestInjectTenant;
import org.junit.Test;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
public class HaiyingShopeeApiTest extends BaseTestInjectTenant {

    private static final SimpleDateFormat SIMPLE_TIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    protected String tenantId() {
        return "TENANT3924117688555169382400";
    }

    @Resource
    private IHaiYingDataShopeeApiSve haiYingShopeeDataApiSve;

    @Test
    public void testShopeeKeywordList() {
        HaiYingShopeeKeywordListCommand command = new HaiYingShopeeKeywordListCommand();
        command.setStation(HaiYingStation.SHOPEE_PH);
//        command.setOrder_by_type(HaiYingOrderByType.ASC);
//        command.setOrder_by(HaiYingShopeeKeywordListOrderBy.recommend_price);
//        command.setSearch_volume_start(10);
//        command.setSearch_volume_end(30);
        command.setPageCondition(new PageCondition(4999, 20));
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingShopeeDataApiSve.getKeywordList(HaiYingShopeeCommand2RequestTranslator.transShopeeKeywordList(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingShopeeKeywordInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingShopeeKeywordInfoResponse.class);
            PageList<HaiYingShopeeKeywordInfoDTO> ret = HaiYingShopeeResponse2DTOTranslator.transShopeeKeywordInfoList(command.getStation(), responseList, getResultPageMeta(command.getPageCondition(), apiBaseResult));
            System.out.println(JSON.toJSONString(ret));
        }
    }

    @Test
    public void testShopeeKeywordInfo() {
        HaiYingShopeeKeywordInfoCommand command = new HaiYingShopeeKeywordInfoCommand();
        command.setStation(HaiYingStation.SHOPEE_MY);
        command.setKeyword("mask");
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingShopeeDataApiSve.getKeywordInfo(HaiYingShopeeCommand2RequestTranslator.transShopeeKeywordInfo(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingShopeeKeywordInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingShopeeKeywordInfoResponse.class);
            PageList<HaiYingShopeeKeywordInfoDTO> ret = HaiYingShopeeResponse2DTOTranslator.transShopeeKeywordInfoList(command.getStation(), responseList, getResultPageMeta(command.getPageCondition(), apiBaseResult));
            System.out.println(JSON.toJSONString(ret));
        }
    }

    @Test
    public void testShopeeProductList() throws ParseException {
        HaiYingShopeeProductListCommand command = new HaiYingShopeeProductListCommand();
        command.setStation(HaiYingStation.SHOPEE_PH);
        command.setPageCondition(new PageCondition(1, 50));
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingShopeeDataApiSve.getProductList(HaiYingShopeeCommand2RequestTranslator.transShopeeProductList(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingShopeeProductListResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingShopeeProductListResponse.class);
            PageList<HaiYingShopeeProductListDTO> ret = HaiYingShopeeResponse2DTOTranslator.transShopeeProductListList(command.getStation(), responseList, getResultPageMeta(command.getPageCondition(), apiBaseResult));
            System.out.println(JSON.toJSONString(ret));
        }
    }

    @Test
    public void testShopeeProductDetailInfo() {
        HaiYingShopeeProductDetailInfoCommand command = new HaiYingShopeeProductDetailInfoCommand();
        command.setStation(HaiYingStation.SHOPEE_MY);
        HaiYingShopeeShopProductID id = new HaiYingShopeeShopProductID();
        id.setPid("7528192896");
        id.setShop_id("13033184");
        List<HaiYingShopeeShopProductID> ids = new ArrayList<>();
        ids.add(id);
        command.setPid_and_shop_ids(ids);
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingShopeeDataApiSve.getProductDetailInfo(HaiYingShopeeCommand2RequestTranslator.transShopeeProductDetailInfo(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingShopeeProductDetailInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingShopeeProductDetailInfoResponse.class);
            List<HaiYingShopeeProductDetailInfoDTO> ret = HaiYingShopeeResponse2DTOTranslator.transShopeeProductDetailInfoList(responseList);
            System.out.println(JSON.toJSONString(ret));
        }
    }

    @Test
    public void testShopeeProductExtInfo() {
        HaiYingShopeeProductExtInfoCommand command = new HaiYingShopeeProductExtInfoCommand();
        command.setStation(HaiYingStation.SHOPEE_MY);
        command.setPids(new ArrayList<String>(){
            {
                add("7528192896");
                add("7319093014");
            }
        });
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingShopeeDataApiSve.getProductExtInfo(HaiYingShopeeCommand2RequestTranslator.transShopeeProductExtInfo(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingShopeeProductExtInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingShopeeProductExtInfoResponse.class);
            List<HaiYingShopeeProductExtInfoDTO> ret = HaiYingShopeeResponse2DTOTranslator.transShopeeProductExtInfoList(responseList);
            System.out.println(JSON.toJSONString(ret));
        }
    }

    @Test
    public void testShopeeProductHistoryInfo() {
        HaiYingShopeeProductHistoryInfoCommand command = new HaiYingShopeeProductHistoryInfoCommand();
        command.setStation(HaiYingStation.SHOPEE_MY);
        command.setPids(new ArrayList<String>(){
            {
                add("7528192896");
                add("7319093014");
            }
        });
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingShopeeDataApiSve.getProductHistoryInfo(HaiYingShopeeCommand2RequestTranslator.transShopeeProductHistoryInfo(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingShopeeProductHistoryInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingShopeeProductHistoryInfoResponse.class);
            List<HaiYingShopeeProductHistoryInfoDTO> ret = HaiYingShopeeResponse2DTOTranslator.transShopeeProductHistoryInfoList(responseList);
            System.out.println(JSON.toJSONString(ret));
        }
    }

    @Test
    public void testShopeeCategoryTree() {
        HaiYingShopeeCategoryTreeCommand command = new HaiYingShopeeCategoryTreeCommand();
        command.setStation(HaiYingStation.SHOPEE_MY);
        //command.setCid("11000979");
        //command.setLevel(1);
        //command.setP_l1_id("11020924");
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingShopeeDataApiSve.getCategoryTree(HaiYingShopeeCommand2RequestTranslator.transShopeeCategoryTree(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingShopeeCategoryTreeResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingShopeeCategoryTreeResponse.class);
            List<HaiYingShopeeCategoryTreeDTO> ret = HaiYingShopeeResponse2DTOTranslator.transShopeeCategoryTreeList(responseList);
            System.out.println(JSON.toJSONString(ret));
        }
    }

    @Test
    public void testShopeeTopCategoryInfo() {
        HaiYingShopeeTopCategoryInfoCommand command = new HaiYingShopeeTopCategoryInfoCommand();
        command.setStation(HaiYingStation.SHOPEE_MY);
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingShopeeDataApiSve.getTopCategoryInfo(HaiYingShopeeCommand2RequestTranslator.transShopeeTopCategoryInfo(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingShopeeTopCategoryInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingShopeeTopCategoryInfoResponse.class);
            List<HaiYingShopeeTopCategoryInfoDTO> ret = HaiYingShopeeResponse2DTOTranslator.transShopeeTopCategoryInfoList(responseList);
            System.out.println(JSON.toJSONString(ret));
        }
    }

    @Test
    public void testShopeeSubCategoryInfo() throws ParseException {
        HaiYingShopeeSubCategoryInfoCommand command = new HaiYingShopeeSubCategoryInfoCommand();
        command.setStation(HaiYingStation.SHOPEE_MY);
//        command.setCid("11000538");

        command.setStat_time_start(SIMPLE_TIME_FORMAT.parse("2022-07-01 00:00:00").getTime());
        command.setStat_time_end(SIMPLE_TIME_FORMAT.parse("2022-07-10 23:59:59").getTime());
        command.setPageCondition(new PageCondition());
        command.setOrder_by(HaiYingShopeeCategoryListOrderBy.sold_sub_top_percent);
        command.setOrder_by_type(HaiYingOrderByType.ASC);
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingShopeeDataApiSve.getSubCategoryInfo(HaiYingShopeeCommand2RequestTranslator.transShopeeSubCategoryInfo(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingShopeeSubCategoryInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingShopeeSubCategoryInfoResponse.class);
            PageList<HaiYingShopeeSubCategoryInfoDTO> ret = HaiYingShopeeResponse2DTOTranslator.transShopeeSubCategoryInfoList(responseList, getResultPageMeta(command.getPageCondition(), apiBaseResult));
            System.out.println(JSON.toJSONString(ret));
        }
    }

    private PageMeta getResultPageMeta(PageCondition pageCondition, HaiYingDataApiBaseResult<String> apiBaseResult) {
        if (apiBaseResult.getTotalSize() > 0) {
            PageMeta pageMeta = new PageMeta();
            if (null != pageCondition) {
                pageMeta.pageNum = pageCondition.pageNum;
            } else {
                pageMeta.pageNum = 1;
            }
            pageMeta.pageSize = apiBaseResult.getSize();
            if (apiBaseResult.getTotalSize() % apiBaseResult.getSize() == 0) {
                pageMeta.pages = apiBaseResult.getTotalSize() / apiBaseResult.getSize();
            } else {
                pageMeta.pages = (apiBaseResult.getTotalSize() / apiBaseResult.getSize()) + 1;
            }
            pageMeta.total = apiBaseResult.getTotalSize();
            return pageMeta;
        }
        return null;
    }

}
