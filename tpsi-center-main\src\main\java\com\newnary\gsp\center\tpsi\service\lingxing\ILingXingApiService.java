package com.newnary.gsp.center.tpsi.service.lingxing;

import com.newnary.gsp.center.tpsi.infra.client.lingxing.params.LingXingDataParam;
import com.newnary.gsp.center.tpsi.service.lingxing.request.QueryLingXingProductInfoListRequest;
import com.newnary.gsp.center.tpsi.service.lingxing.request.QueryLingXingProductInfoRequest;
import com.newnary.gsp.center.tpsi.service.lingxing.request.QueryLingXingProductListRequest;
import com.newnary.gsp.center.tpsi.service.lingxing.response.QueryLingXingProductInfoListResponse;
import com.newnary.gsp.center.tpsi.service.lingxing.response.QueryLingXingProductInfoResponse;
import com.newnary.gsp.center.tpsi.service.lingxing.response.QueryLingXingProductListResponse;

public interface ILingXingApiService {
    QueryLingXingProductListResponse queryProductList(LingXingDataParam lingXingDataParam,QueryLingXingProductListRequest request);
    QueryLingXingProductInfoListResponse queryProductInfoList(LingXingDataParam lingXingDataParam, QueryLingXingProductInfoListRequest request);
    QueryLingXingProductInfoResponse queryProductInfo(LingXingDataParam lingXingDataParam, QueryLingXingProductInfoRequest request);
}
