package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.erp;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class EcCangERPGetSupplierProductResponse {

    /**
     * 产品开发id
     */
    private String pd_id;

    /**
     * sku
     */
    private String product_sku;

    /**
     * 产品名称
     */
    private String product_title;

    /**
     * 产品状态:0 删除,1正式产品,2:开发产品
     */
    private Integer product_status;

    /**
     * 销售状态, 请参考业务接口>产品>获取产品销售状态
     */
    private Integer sale_status;

    /**
     * 默认供应商代码
     */
    private String default_supplier_code;

    /**
     * 建议零售价
     */
    private Float suggest_price;

    /**
     * 默认采购员用户id
     */
    private Integer buyer_id;

    /**
     * (产品开发) 申报币种 如"USD"
     */
    private String pd_declare_currency_code;

    /**
     * 供应商产品
     */
    private List<SupplierProduct> supplier_product;

    @Setter
    @Getter
    public static class SupplierProduct {

        /**
         * 供应商产品id
         */
        private String sp_id;

        /**
         * 供应商代码
         */
        private String supplier_code;

        /**
         * 供应商名称
         */
        private String supplier_name;

        /**
         * 供应商id
         */
        private Integer supplier_id;

        /**
         * 供应商产品代码
         */
        private String sp_supplier_product_code;

        /**
         * 开户供应商产品名称
         */
        private String sp_supplier_product_name;

        /**
         * 供应商单价【不含税单价】
         */
        private Float sp_unit_price;

        /**
         * 供应商最近单价
         */
        private Float sp_last_price;

        /**
         * 报价状态,1、开发中，2、开发采用，3、正式产品
         */
        private Integer sp_status;

        /**
         * 采购单位 0:散件 1:整件
         */
        private Integer sp_purchase_unit;

        /**
         * 供应商产品，当选择整件时，增加1：补货取整，0：补货不取整
         */
        private Integer sp_purchase_rounding;

        /**
         * 币种
         */
        private String currency_code;

        /**
         * 采购交期
         */
        private Integer sp_eta_time;

        /**
         * 最低采购量
         */
        private Integer sp_min_qty;

        /**
         * 创建人
         */
        private Integer creater_id;

        /**
         * 采购员
         */
        private Integer buyer_id;

        /**
         * 跟单员
         */
        private Integer track_id;

        /**
         * 修改人
         */
        private String updater_id;

        /**
         * 询价状态,0：未询价，1：询价中，2：询价完成
         */
        private Integer sp_price_status;

        /**
         * 保留供应商对应的SKU或货号
         */
        private String sp_supplier_sku;

        /**
         * 默认供应商，0:否，1：是
         */
        private Integer sp_default;

        /**
         * 供应商产品地址
         */
        private String sp_product_address;

        /**
         * 税率
         */
        private Float sp_tax_rate;

        /**
         * 不含税采购价
         */
        private Float sp_unit_price_not_tax;

        /**
         * 组织机构id，user_organization表主键
         */
        private Integer user_organization_id;

        /**
         * 采购参考价
         */
        private Float sp_reference_unit_price;

        /**
         * 是否启用产品有效期
         */
        private Integer is_validate;

        /**
         * 是否启用价格区间的有效期
         */
        private Integer is_validate_map;

        /**
         * 产品地址
         */
        private List<ProductAddress> product_address;

        /**
         * 价格范围
         */
        private List<PriceRange> price_range;

        /**
         * 仓库产品:1688产品比例，例如1/12
         */
        private String sp_proportion1688;

        /**
         * 含税单价
         */
        private Float sp_contract_price;

        @Getter
        @Setter
        private static class ProductAddress {

            /**
             * 供应商产品地址
             */
            private String sp_product_address;

        }

        @Getter
        @Setter
        private static class PriceRange {

            /**
             * 最小采购量
             */
            private Integer min_quantity;

            /**
             * 最大采购量
             */
            private Integer max_quantity;

            /**
             * 产品区间价格
             */
            private Float unit_price;

        }

    }

}
