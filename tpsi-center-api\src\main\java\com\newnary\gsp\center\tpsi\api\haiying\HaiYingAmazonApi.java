package com.newnary.gsp.center.tpsi.api.haiying;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.api.base.common.PageList;
import com.newnary.gsp.center.tpsi.api.haiying.request.amazon.HaiYingAmazonCategoryTreeCommand;
import com.newnary.gsp.center.tpsi.api.haiying.request.amazon.HaiYingAmazonProductDetailInfoCommand;
import com.newnary.gsp.center.tpsi.api.haiying.request.amazon.HaiYingAmazonProductHistoryInfoCommand;
import com.newnary.gsp.center.tpsi.api.haiying.request.amazon.HaiYingAmazonProductListCommand;
import com.newnary.gsp.center.tpsi.api.haiying.response.amazon.HaiYingAmazonCategoryTreeDTO;
import com.newnary.gsp.center.tpsi.api.haiying.response.amazon.HaiYingAmazonProductDetailInfoDTO;
import com.newnary.gsp.center.tpsi.api.haiying.response.amazon.HaiYingAmazonProductHistoryInfoDTO;
import com.newnary.gsp.center.tpsi.api.haiying.response.amazon.HaiYingAmazonProductListDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-13
 */
@RequestMapping("tpsi-center/haiying/amazon")
public interface HaiYingAmazonApi {

    @PostMapping("getAmazonProductList")
    CommonResponse<PageList<HaiYingAmazonProductListDTO>> getAmazonProductList(@RequestBody HaiYingAmazonProductListCommand command);

    @PostMapping("getAmazonProductDetailInfo")
    CommonResponse<List<HaiYingAmazonProductDetailInfoDTO>> getAmazonProductDetailInfo(@RequestBody HaiYingAmazonProductDetailInfoCommand command);

    @PostMapping("getAmazonProductHistoryInfo")
    CommonResponse<List<HaiYingAmazonProductHistoryInfoDTO>> getAmazonProductHistoryInfo(@RequestBody HaiYingAmazonProductHistoryInfoCommand command);

    @PostMapping("getAmazonCategoryTree")
    CommonResponse<List<HaiYingAmazonCategoryTreeDTO>> getAmazonCategoryTree(@RequestBody HaiYingAmazonCategoryTreeCommand command);

}
