package com.newnary.gsp.center.tpsi.infra.client.alibaba;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ocean.rawsdk.ApiExecutor;
import com.alibaba.ocean.rawsdk.client.entity.AuthorizationToken;
import com.alibaba.ocean.rawsdk.common.AbstractAPIRequest;
import com.alibaba.ocean.rawsdk.common.SDKResult;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.distributed.tools.cache.DPojoHolder;
import com.newnary.distributed.tools.cache.DistributedCache;
import com.newnary.distributed.tools.concurrent.template.DConcurrentTemplate;
import com.newnary.gsp.center.tpsi.infra.client.alibaba.dto.Open1688AccessTokenDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.Base64;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since Created on 2022-05-12
 **/
@Slf4j
public class Open1688OceanClient {

    @Resource
    private DistributedCache distributedCache;

    @Value(value = "${tpsi-center.config.open1688OceanApiInfo}")
    private String open1688OceanApiInfo;

    private JSONObject get1688ApiInfo() {
        // FIXME: 2022/5/12 临时性代码, 近期务必优化 @yangzc
        if (StringUtils.isNotBlank(open1688OceanApiInfo)) {
            String decodeJsonStr = new String(Base64.getDecoder().decode(open1688OceanApiInfo.getBytes()));
            return JSON.parseObject(decodeJsonStr);
        }
        return new JSONObject();
    }

    public ApiExecutor getApiExecutor(String appId) {
        // FIXME: 2022/5/12 获取对接参数 @yangzc
        JSONObject json = get1688ApiInfo();
        return new ApiExecutor(json.getString("appKey"), json.getString("secKey"));
    }

    public String getAccessToken(String appId) {
        JSONObject json = get1688ApiInfo();

        // authModel约定, SINGLE_USER 单用户授权模式, MULTI_USER 多用户授权模式, OAuth授权模式
        String authModel = json.getString("authModel");
        switch (authModel) {
            case "SINGLE_USER":
                return json.getString("accessToken");
            case "MULTI_USER":
                return refreshToken(appId, json);
            case "OAuth":
                System.out.println(">>>>>>>>>>>>>>>>>>>>>>>>>OAuth<<<<<<<<<<<<<<<<<<<<<<<<<<<");
            default:
                throw new ServiceException(CommonErrorInfo.ERROR_100_SYSTEM_ERROR, "1688开放平台配置异常");
        }
    }

    private String refreshToken(String appId, JSONObject open1688InfoJson) {
        DPojoHolder<Open1688AccessTokenDTO> accessTokenHolder = distributedCache
                .pojoHolder("OPEN1688_ACCESS_TOKEN:".concat(open1688InfoJson.getString("appKey")), Open1688AccessTokenDTO.class);
        Open1688AccessTokenDTO accessTokenDTO = accessTokenHolder.get();

        if (accessTokenDTO == null || accessTokenDTO.hasExpired()) {
            return DConcurrentTemplate.tryLockMode(
                    "OPEN1688_ACCESS_TOKEN_GENERATE_LOCK".concat(open1688InfoJson.getString("appKey")),
                    t -> t.tryLock(1, TimeUnit.SECONDS),
                    () -> {
                        // 双重检查
                        Open1688AccessTokenDTO reAccessTokenDTO = accessTokenHolder.get();
                        if (reAccessTokenDTO != null && !reAccessTokenDTO.hasExpired()) {
                            return reAccessTokenDTO.getAccessToken();
                        }

                        // 重新获取
                        AuthorizationToken token = getApiExecutor(appId).refreshToken(open1688InfoJson.getString("refreshToken"));

                        long timeToLive = Math.max(open1688InfoJson.getLong("apiExpiresTime") - System.currentTimeMillis(), 60 * 1000);
                        accessTokenHolder.set(new Open1688AccessTokenDTO(token), timeToLive, TimeUnit.MILLISECONDS);

                        return token.getAccess_token();
                    }
            );
        }
        return accessTokenDTO.getAccessToken();
    }

    public final <TResponse> TResponse execute(AbstractAPIRequest<TResponse> apiRequest, String appId) {
        ApiExecutor apiExecutor = getApiExecutor(appId);
        SDKResult<TResponse> execute = apiExecutor.execute(apiRequest, getAccessToken(appId));
        if (StringUtils.isNotEmpty(execute.getErrorCode())) {
            log.error("Open1688Ocean>> execute >> AppId=[{}], ErrorCode=[{}], ErrorMessage=[{}]",
                    appId,
                    execute.getErrorCode(),
                    execute.getErrorMessage());
            throw new ServiceException(CommonErrorInfo.ERROR_100_SYSTEM_ERROR);
        } else {
            return execute.getResult();
        }
    }

}
