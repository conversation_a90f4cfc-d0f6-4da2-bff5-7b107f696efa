package com.newnary.gsp.center.tpsi.api.haiying.response.lazada;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingLazadaProductSkuInfoDTO {

    /**
     * 商品sku id
     */
    private String sku_id;

    /**
     * 卖家sku
     */
    private String seller_sku;

    /**
     * 商品sku价格
     */
    private BigDecimal price;

    /**
     * 商品sku原价
     */
    private BigDecimal origin_price;

    /**
     * 商品sku库存
     */
    private Integer stock;

    /**
     * 商品sku主图
     */
    private String main_image;

    /**
     * 商品sku颜色
     */
    private String color;

    /**
     * 商品sku size
     */
    private String size;

    /**
     * 商品sku全部图片
     */
    private String sku_img;

    /**
     * 商品包裹包含产品
     */
    private String box_content;

    /**
     * sku规格属性
     */
    private String properties;

}
