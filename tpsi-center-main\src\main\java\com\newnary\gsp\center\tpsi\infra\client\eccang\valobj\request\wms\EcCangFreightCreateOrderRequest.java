package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.wms;

import java.util.List;

public class EcCangFreightCreateOrderRequest {

    public String freight_order_code;

    public Integer warehouse_id;
    public Integer ph_warehouse_id;

    public String head_logistics_code;
    public String end_logistics_code;
    public String platform;
    public String site;
    public String currency_code;
    public String rec_name;
    public String rec_phone;
    public String rec_country_code;
    public String rec_province;

    public String rec_city;
    public String rec_district;
    public String rec_address;
    public String order_remark;
    public String postal_code;
    public Double total_amount;
    public Integer order_type;
    public Integer is_reviewed;
    public String platform_shop;
    public String customs_code;
    public Integer create_type;

    public List<ProductData> product_data;

    public static class ProductData {
        public String child_code;
        public String package_code;
        public String product_standard;
        public String product_img_url;
        public Integer product_count;
        public String product_category;
        public String product_name_cn;
        public String product_name_en;
        public Double product_declared;
        public String hs_code;
    }
}
