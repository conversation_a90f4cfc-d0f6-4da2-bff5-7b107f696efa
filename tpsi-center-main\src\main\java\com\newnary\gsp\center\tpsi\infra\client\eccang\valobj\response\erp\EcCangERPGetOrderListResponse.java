package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.erp;

public class EcCangERPGetOrderListResponse {

    public String CustomTagEn;
    public String orderType;
    public String platformUserName;
    public String CustomTagZh;
    public String abnormalReason;
    public String buyerId;
    public String productCount;
    public String systemNote;
    public String paypalTransactionId;
    public String SystemTagEn;
    public String processAgain;
    public String dateCreateSys;
    public String SystemTagZh;
    public String platformShipStatus;
    public String billNo;
    public String trackDeliveredTime;
    public String companyCode;
    public String createrUserName;
    public String abnormalType;
    public String shippingMethod;
    public String platformShipTime;
    public String finalvaluefeeTotal;
    public String isMark;
    public String warehouseId;
    public String costShipFee;
    public String subtotal;
    public String userAccount;
    public String buyerMail;
    public String amountpaid;

    public String order_id;
    public Integer status;
    public String refNo;
    public String datePaidPlatform;
    public String updateDate;
    public String customerServiceNote;
    public String createType;
    public String shipFee;
    public String orderWeight;
    public String createrUserCode;
    public String platform;
    public String warehouseOrderCode;
    public String warehouseCode;
    public String shippingMethodNo;
    public String shippingMethodPlatform;
    public String dateLatestShip;
    public String countryCode;
    public String trackStatus;
    public String dateCreatePlatform;
    public String platformFeeTotal;
    public String currency;
    public String orderDesc;
    public String outboundBatchTrackingNo;
    public String saleOrderCode;
    public String dateWarehouseShipping;
    public String discount_val;
    public String otherFee;
    public String buyerName;
    public String paypalAccount;
    public String outboundBatchCarrier;
    public String site;
    public String createdDate;
    public String sysOrderCode;
    public String createrUserNameEn;

}
