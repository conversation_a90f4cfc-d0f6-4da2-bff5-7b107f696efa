package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.shopee;

import lombok.Data;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingShopeeProductListResponse {

    /**
     * 商品唯一ID
     */
    private String pid;

    /**
     * 商品前30天销售件数
     */
    private String sold;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 店铺名称
     */
    private String shop_name;

    /**
     * 商品评分
     */
    private String rating;

    /**
     * 商品默认价
     */
    private String price;

    /**
     * 商品最低价
     */
    private String min_price;

    /**
     * 商品最高价
     */
    private String max_price;

    /**
     * 商品上架时间
     */
    private String gen_time;

    /**
     * 商品主图
     */
    private String image;

    /**
     * 商品收藏人数
     */
    private String favorite;

    /**
     * 商品评分数
     */
    private String ratings;

    /**
     * 商品所属店铺id
     */
    private String shop_id;

    /**
     * 店铺开张时间
     */
    private String approved_date;

    /**
     * 商品状态
     */
    private String status;

    /**
     * 商品是否存在
     */
    private String not_exist;

    /**
     * 商品最新抓取时间
     */
    private String last_modi_time;

    /**
     * 商品归属的类目ID(由一级至子类，多个以逗号分隔)
     */
    private String cid;

    /**
     * 商品所属的类目结构
     */
    private String category_structure;

    /**
     * 商品统计时间
     */
    private String stat_time;

    /**
     * 商品预计到货时间
     */
    private String estimated_days;

    /**
     * 商品是否热销
     * (预留字段)
     */
    private String is_hot_sales;

    /**
     * 商品是否虾皮优选
     */
    private String is_shopee_verified;

    /**
     * 商品所属店铺是否官方店铺
     */
    private String is_official_shop;

    /**
     * 店铺所在地
     */
    private String shop_location;

    /**
     * 商品总销售件数
     */
    private String historical_sold;

    /**
     * 店主名称
     */
    private String user_name;

    /**
     * 商品前30天销售额
     */
    private String payment;

    /**
     * 店铺所在
     * 0: 本地   1:海外   null:未知
     */
    private String shipping_icon_type;

    /**
     * shopee商品链接
     */
    private String product_url;

    /**
     * shopee店铺链接
     */
    private String shop_url;

    /**
     * 商品浏览数
     */
    private String view_count;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 店铺商品总数
     */
    private String shop_products_count;

}
