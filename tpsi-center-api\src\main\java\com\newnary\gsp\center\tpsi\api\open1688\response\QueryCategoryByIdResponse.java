package com.newnary.gsp.center.tpsi.api.open1688.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class QueryCategoryByIdResponse {
    private List<Category> categoryInfo;
    private Boolean succes;

    @Getter
    @Setter
    public static class Category{
        private String categoryID;
        private String name;
        private Boolean isLeaf;
        private List<Long> parentIDs;
        private Integer minOrderQuantity;
        private List<FeatureInfo> featureInfos;
        private String categoryType;
        private Boolean isSupportProcessing;
        private List<ChildCategory> childCategorys;

    }

    @Getter
    @Setter
    public static class ChildCategory {
        private String id;
        private String name;
        private Boolean isLeaf;
        private String categoryType;
    }


    @Getter
    @Setter
    public static class FeatureInfo{
        private String key;
        private String value;
        private Integer status;
        private Boolean hierarchy;
    }
}
