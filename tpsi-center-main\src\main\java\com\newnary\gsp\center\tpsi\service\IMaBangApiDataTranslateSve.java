package com.newnary.gsp.center.tpsi.service;

import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderDetailInfo;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierAdjustSupplyPriceReq;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierUpdateStockReq;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.MaBangStockQuantityWarehouse;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.MaBangStockSkuWarehouseInfo;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.order.MaBangDoCreateOrder;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.order.MaBangDoDeliverOrder;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.stock.MaBangDoSearchSkuList;
import com.newnary.gsp.center.tpsi.infra.model.ApiRequestParams;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;

public interface IMaBangApiDataTranslateSve {

    MaBangDoCreateOrder buildMaBangDoCreateOrder(String stockoutOrderId, DeliveryOrderDetailInfo deliveryOrderLiteInfo, ApiRequestParams apiRequestParams);

    MaBangDoDeliverOrder buildMaBangDoDeliveryOrder();

    MaBangDoSearchSkuList buildMaBangDoSearchSkuList();

    OpenSupplierUpdateStockReq buildOpenSupplierUpdateStockReq(ThirdPartySystem system, MaBangStockQuantityWarehouse maBangStockQuantityWarehousem, String sku);

    OpenSupplierAdjustSupplyPriceReq buildOpenSupplierAdjustSupplyPriceReq(String supplierId, MaBangStockSkuWarehouseInfo warehouseInfo, String sku, String whCountry);

}
