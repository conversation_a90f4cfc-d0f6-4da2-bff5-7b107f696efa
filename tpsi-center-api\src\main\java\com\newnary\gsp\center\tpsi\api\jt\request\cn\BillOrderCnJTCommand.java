package com.newnary.gsp.center.tpsi.api.jt.request.cn;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 查询订单账单
 *
 * <AUTHOR>
 * @since Created on 2023-08-11
 **/
@Data
@Valid
public class BillOrderCnJTCommand {

    @Size(max = 32,message = "customerOrderNo 最大字符(32)")
    private String customerOrderNo;

    @Size(max = 32,message = "deliveryNo 最大字符(32)")
    private String deliveryNo;


}
