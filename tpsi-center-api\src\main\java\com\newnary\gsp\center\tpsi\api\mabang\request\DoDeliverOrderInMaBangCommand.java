package com.newnary.gsp.center.tpsi.api.mabang.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class DoDeliverOrderInMaBangCommand {

    @NotBlank(message = "第三方系统ID（必填）")
    public String thirdPartySystemId;

    @NotBlank(message = "马帮平台订单号（必填）")
    public String maBangPlatformOrderId;

    @NotBlank(message = "马帮物流渠道编号（必填）")
    public String maBangLogisticsChannelId;

    @NotBlank(message = "货运单号（必填）")
    public String trackNumber;

    /**
     * 内部单号
     */
    public String trackNumber1;
}
