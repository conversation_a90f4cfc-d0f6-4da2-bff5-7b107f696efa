package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp;

import java.util.List;

public class EcCangERPStockinOrderSyncReceivingRequest {

    public String actionType;

    public Integer receivingStatus;

    public Integer receivingType;
    public Integer warehouseId;

    /** 预计到货日期，格式(2017-03-20)） */
    public String expectedDate;

    public List<Product> productList;

    public static class Product {

        /** 产品代码 */
        public String productSku;

        /** 产品数量 */
        public Integer qty;

        /** 是否质检: 0 否， 1 是 */
        public Integer isQc;
    }
}
