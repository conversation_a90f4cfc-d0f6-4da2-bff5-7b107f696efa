package com.newnary.gsp.center.tpsi.eccang;

import com.newnary.api.base.constants.HeaderNameConstants;
import com.newnary.gsp.center.tpsi.app.listener.mq.GSPPurchaseOrderSuccessOrderProcessor;
import com.newnary.gsp.center.tpsi.app.listener.mq.GSPPurchaseOrderSyncTrackingNumberProcessor;
import com.newnary.gsp.center.tpsi.app.listener.mq.GSPPurchaseOrderTakeStockProcessor;
import com.newnary.gsp.center.tpsi.app.listener.mq.StockoutOrderFinishSyncGSPPurchaseOrderProcessor;
import com.newnary.messagebody.gsp.logistics.mo.StockoutOrderStateChangeMO;
import com.newnary.messagebody.gsp.purchase.mo.GspPurchaseOrderTakeStockMO;
import com.newnary.mq.starter.consumer.MQMessage;
import com.newnary.spring.cloud.context.RequestContext;
import com.newnary.test.starter.AbstractInjectRequestContext;
import com.newnary.test.starter.BaseTestInjectTenant;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

public class ProcessorTest extends AbstractInjectRequestContext {
    @Override
    protected String tenantId() {
        return "TENANT3924117688555169382400";
    }

    @Resource
    private GSPPurchaseOrderTakeStockProcessor gspPurchaseOrderTakeStockProcessor;

    @Resource
    private GSPPurchaseOrderSuccessOrderProcessor gspPurchaseOrderCloseOrderProcessor;

    @Resource
    private GSPPurchaseOrderSyncTrackingNumberProcessor gspPurchaseOrderSyncTrackingNumberProcessor;

    @Resource
    private StockoutOrderFinishSyncGSPPurchaseOrderProcessor stockoutOrderFinishSyncGSPPurchaseOrderProcessor;

    @Override
    protected Map<String, String> addContext() {
        Map<String, String> map = new HashMap<>();
        map.put(HeaderNameConstants.USE_CURRENCY, "PHP");
        map.put(HeaderNameConstants.USE_SITE, "PH");
        map.put(HeaderNameConstants.USER_ID, "U177541269749817");
        map.put(HeaderNameConstants.USE_LANGUAGE, "en_US");
        map.put(HeaderNameConstants.TENANT_ID,"abc");
        return map;
    }

    @Test
    public void purchaseOrderTakeStockProcessorTest(){
        MQMessage<GspPurchaseOrderTakeStockMO> mqMessage = new MQMessage<>();
        GspPurchaseOrderTakeStockMO mo = new GspPurchaseOrderTakeStockMO();
        mo.setPurchaseOrderId("405104443638550528");
        mo.setCurrOptUserId("USER0957311275106997506048");
        mqMessage.setContent(mo);
        gspPurchaseOrderTakeStockProcessor.doProcess(mqMessage);
    }

    @Test
    public void puuchaseOrderSuccessProcessorTest(){
        MQMessage<GspPurchaseOrderTakeStockMO> mqMessage = new MQMessage<>();
        GspPurchaseOrderTakeStockMO mo = new GspPurchaseOrderTakeStockMO();
        mo.setPurchaseOrderId("404726040095756288");
        mo.setCurrOptUserId("USER0957311275106997506048");
        mqMessage.setContent(mo);
        gspPurchaseOrderCloseOrderProcessor.doProcess(mqMessage);
    }

    @Test
    public void testSyncTrackingNumber2(){
        MQMessage<GspPurchaseOrderTakeStockMO> mqMessage = new MQMessage<>();
        GspPurchaseOrderTakeStockMO mo = new GspPurchaseOrderTakeStockMO();
        mo.setPurchaseOrderId("404726040095756288");
        mo.setCurrOptUserId("USER0957311275106997506048");
        mqMessage.setContent(mo);
        boolean b = gspPurchaseOrderSyncTrackingNumberProcessor.doProcess(mqMessage);
    }

    @Test
    public void testCheckStockoutFinishSyncPurchaseOrder(){
        MQMessage<StockoutOrderStateChangeMO> mqMessage = new MQMessage<>();
        StockoutOrderStateChangeMO mo = new StockoutOrderStateChangeMO();
        mo.setStockoutOrderId("SO180366307557427");
        mqMessage.setContent(mo);
        boolean b = stockoutOrderFinishSyncGSPPurchaseOrderProcessor.doProcess(mqMessage);
    }
}
