package com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.response;


import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CreateOpen1688OrderResponse {
    /**
     * 订单总金额（单位分），一次创建多个订单时，该字段为空
     */
    private Long totalSuccessAmount;

    /**
     * 订单ID，一次创建多个订单时，该字段为空
     */
    private String orderId;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误码
     */
    private String code;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 账期信息，非账期支付订单返回空
     */
    private AccountPeriod accountPeriod;

    /**
     * 失败商品信息
     */
    private List<FailedOffer> failedOfferList;

    /**
     * 运费，单位：分，一次创建多个订单时，该字段为空
     */
    private Long postFee;

    /**
     * 一次创建多个订单
     */
    private List<OrderInfo> orderList;

    @Setter
    @Getter
    public static class AccountPeriod{

        /**
         * 账期的类型,1：一个月指定日期结算一次，3：两个月指定日期结算一次，6：三个月指定日期结算一次，5：按收货时间和账期日期结算
         */
        private Integer tapType;

        /**
         * 根据账期类型不同而不同，按月结算类型此值代表具体某日，按收货时间结算时此值代表结算时间周期
         */
        private Integer tapDate;

        /**
         * 逾期次数
         */
        private Integer tapOverdue;
    }

    @Setter
    @Getter
    public static class FailedOffer{

        /**
         * 下单失败的商品
         */
        private String offerId;

        /**
         * 下单失败商品的规格ID
         */
        private String specId;

        /**
         * 下单失败的错误编码
         */
        private String errorCode;

        /**
         * 下单失败的错误描述
         */
        private String errorMessage;

    }

    @Setter
    @Getter
    public static class OrderInfo{

        /**
         *
         * 运费
         */
        private Long postFee;

        /**
         * 订单实付款金额，单位为分
         */
        private Long orderAmmount;

        /**
         * 描述信息
         */
        private String message;

        /**
         * 返回码
         */
        private String resultCode;

        /**
         * 是否成功
         */
        private Boolean success;

        /**
         * 订单号
         */
        private String orderId;
    }
}
