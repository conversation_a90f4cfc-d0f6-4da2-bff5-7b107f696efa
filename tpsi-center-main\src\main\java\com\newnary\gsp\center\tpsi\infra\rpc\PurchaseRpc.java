package com.newnary.gsp.center.tpsi.infra.rpc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.common.PageList;
import com.newnary.gsp.center.purchase.api.category.feign.PurchaseCategoryFeignApi;
import com.newnary.gsp.center.purchase.api.category.response.CategoryInfo;
import com.newnary.gsp.center.purchase.api.order.PurchaseOrderGoodsApi;
import com.newnary.gsp.center.purchase.api.order.feign.PurchaseOrderFeignApi;
import com.newnary.gsp.center.purchase.api.order.request.*;
import com.newnary.gsp.center.purchase.api.order.response.PurchaseOrderInfo;
import com.newnary.gsp.center.purchase.api.plan.feign.PlanFeignApi;
import com.newnary.gsp.center.purchase.api.plan.feign.PlanGoodsFeignApi;
import com.newnary.gsp.center.purchase.api.plan.request.PlanGoodsUpdateStatusCommand;
import com.newnary.gsp.center.purchase.api.plan.request.PlanUpdateCommand;
import com.newnary.gsp.center.purchase.api.plan.request.PurchasePlanPushCommand;
import com.newnary.gsp.center.purchase.api.plan.response.PlanInfo;
import com.newnary.gsp.center.purchase.api.product.feign.PurchaseSkuFeignApi;
import com.newnary.gsp.center.purchase.api.product.feign.PurchaseSpuFeignApi;
import com.newnary.gsp.center.purchase.api.product.request.*;
import com.newnary.gsp.center.purchase.api.product.response.SkuDetailInfo;
import com.newnary.gsp.center.purchase.api.product.response.SkuInfo;
import com.newnary.gsp.center.purchase.api.product.response.SpuInfo;
import com.newnary.gsp.center.purchase.api.supplier.feign.SupplierFeignApi;
import com.newnary.gsp.center.purchase.api.supplier.feign.SupplierGoodsFeignApi;
import com.newnary.gsp.center.purchase.api.supplier.response.SupplierFullDTO;
import com.newnary.gsp.center.purchase.api.supplier.response.SupplierGoodsInfo;
import com.newnary.gsp.center.purchase.api.tags.feign.TagsFeignApi;
import com.newnary.gsp.center.purchase.api.tags.request.TagsCreateCommand;
import com.newnary.gsp.center.purchase.api.tags.response.TagsInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class PurchaseRpc {

    @Resource
    private PlanFeignApi planFeignApi;

    @Resource
    private PlanGoodsFeignApi planGoodsFeignApi;

    @Resource
    private PurchaseOrderFeignApi purchaseOrderFeignApi;

    @Resource
    private PurchaseOrderGoodsApi purchaseOrderGoodsApi;

    @Resource
    private PurchaseSkuFeignApi purchaseSkuFeignApi;

    @Resource
    private PurchaseSpuFeignApi purchaseSpuFeignApi;

    @Resource
    private SupplierFeignApi supplierFeignApi;

    @Resource
    private SupplierGoodsFeignApi supplierGoodsFeignApi;

    @Resource
    private TagsFeignApi tagsFeignApi;

    @Resource
    private PurchaseCategoryFeignApi purchaseCategoryFeignApi;

    public String updateOrderState(String orderId) {
        PurchaseOrderUpdateCommand command = new PurchaseOrderUpdateCommand();
        command.setId(Long.valueOf(orderId));
        command.setOrderStatus(50);
        return purchaseOrderFeignApi.autoUpdateStatus(command).mustSuccessOrThrowOriginal();
    }

    public String updateThirdOrderCode(Long orderId, String thirdOrderCode) {
        PurchaseOrderUpdateCommand command = new PurchaseOrderUpdateCommand();
        command.setId(orderId);
        command.setThirdOrderCode(thirdOrderCode);
        return purchaseOrderFeignApi.updateThirdOrderCode(command).mustSuccessOrThrowOriginal();
    }

    public String syncProductAndSupplier(SpuSkuSupplierCommand command) {
        return purchaseSpuFeignApi.createSpuSkuSupplerPlan(command).mustSuccessOrThrowOriginal();
    }

    public String createPurchasePlan(PurchasePlanPushCommand command) {
        return planFeignApi.pushPurchasePlan(command).mustSuccessOrThrowOriginal();
    }

    public String createSpu(SpuCreateCommand spuCreateCommand) {
        return purchaseSpuFeignApi.create(spuCreateCommand).mustSuccessOrThrowOriginal();
    }

    public void closePurchasePlan(String purchasePlanId) {
        //采购计划商品取消
        PlanGoodsUpdateStatusCommand planGoodsShareUpdateCommand = new PlanGoodsUpdateStatusCommand();
        planGoodsShareUpdateCommand.setPurchasePlanId(Long.valueOf(purchasePlanId));
        planGoodsShareUpdateCommand.setStatus(20);
        planGoodsFeignApi.updateStatus(planGoodsShareUpdateCommand);

        //采购计划取消
        PlanUpdateCommand planUpdateCommand = new PlanUpdateCommand();
        planUpdateCommand.setPurchasePlanId(Long.valueOf(purchasePlanId));
        planUpdateCommand.setStatus(30);
        planFeignApi.updateStatus(planUpdateCommand);
    }

    public PlanInfo getPurchasePlan(String erpOrderSaleOrderCode) {
        return planFeignApi.getPurchasePlan(Long.valueOf(erpOrderSaleOrderCode)).mustSuccessOrThrowOriginal();
    }

    public List<PurchaseOrderInfo> getPurchaseOrderByPlanId(Long purchasePlanId) {
        return purchaseOrderFeignApi.getPurchaseOrderByPlanId(purchasePlanId).mustSuccessOrThrowOriginal();
    }

    //获取采购单详情
    public PurchaseOrderInfo queryOrderDetail(Long orderId) {
        return purchaseOrderFeignApi.queryOrderDetail(orderId).mustSuccessOrThrowOriginal();
    }

    //获取采购单详情
    public PurchaseOrderInfo queryOrderDetailByOrderCode(String orderCode) {
        return purchaseOrderFeignApi.queryOrderDetailByOrderCode(orderCode).mustSuccessOrThrowOriginal();
    }

    public SpuInfo getSpuBySpuId(String spuId) {
        return purchaseSpuFeignApi.getBySpuId(spuId).mustSuccessOrThrowOriginal();
    }

    public List<SkuInfo> querySkuListBySpuId(String spuId) {
        return purchaseSkuFeignApi.querySkuListBySpuId(spuId).mustSuccessOrThrowOriginal();
    }

    public SkuDetailInfo getBySkuId(String skuId) {
        return purchaseSkuFeignApi.getBySkuId(skuId).mustSuccessOrThrowOriginal();
    }

    //获取商品详情
    public SkuDetailInfo getBySku(String sku) {
        return purchaseSkuFeignApi.getBySku(sku).mustSuccessOrThrowOriginal();
    }

    public List<SkuDetailInfo> getBySkuList(List<String> skuList) {
        SkuDetailQueryCommand queryCommand = new SkuDetailQueryCommand();
        queryCommand.customCodes = skuList;
        return purchaseSkuFeignApi.queryDetails(queryCommand).mustSuccessOrThrowOriginal();
    }

    public SupplierGoodsInfo getSupplierGoodsById(Long id) {
        return supplierGoodsFeignApi.getById(id).mustSuccessOrThrowOriginal();
    }

    //获取供应商商品信息
    public List<SupplierGoodsInfo> getBySupplierId(String supplierId) {
        return supplierGoodsFeignApi.getBySupplierId(supplierId).mustSuccessOrThrowOriginal();
    }

    //获取供应商信息
    public SupplierFullDTO getSupplier(String supplierId) {
        return supplierFeignApi.get(supplierId).mustSuccessOrThrowOriginal();
    }

    //获取类目
    public CategoryInfo getCategoryById(String categoryId) {
        return purchaseCategoryFeignApi.get(categoryId).mustSuccessOrThrowOriginal();
    }

    //创建标签
    public TagsInfo createTag(TagsCreateCommand command) {
        return tagsFeignApi.create(command).mustSuccessOrThrowOriginal();
    }

    public TagsInfo getTag(String tagsId) {
        return tagsFeignApi.get(tagsId).mustSuccessOrThrowOriginal();
    }

    //打标签
    public void saveSkuTags(SkuTagsCommand command) {
        purchaseSkuFeignApi.saveSkuTags(command);
    }

    public void importSkuTages(String link, String addOrDel) {
        purchaseSkuFeignApi.importSkuTags(link, addOrDel);
    }

    //采购单查询
    public PageList<PurchaseOrderInfo> queryPurchaseOrderList(PurchaseOrderPageQueryCommand command) {
        return purchaseOrderFeignApi.PurchaseDetailPageQuery(command).mustSuccessOrThrowOriginal();
    }

    public void scanSignList4ThirdParty(EntryScanningCommand command) {
        purchaseOrderFeignApi.scanSignList4ThirdParty(command).mustSuccessOrThrowOriginal();
    }

    public void batchTakeStock4ThirdParty(BatchTakeStockCommand command) {
        purchaseOrderGoodsApi.batchTakeStock4ThirdParty(command).mustSuccessOrThrowOriginal();
    }

    public void updateGoodsSize(UpdateGoodsSizeCommand updateCommand) {
        purchaseSkuFeignApi.updateGoodsSize(updateCommand).mustSuccessOrThrowOriginal();
    }

    public List<PurchaseOrderInfo> getPurchaseOrderByRefOrderNumber(String stockoutOrderId) {
        return purchaseOrderFeignApi.getPurchaseOrderByRefOrderNumber(stockoutOrderId).mustSuccessOrThrowOriginal();
    }

    // 查询已付款、部分收货的采购单据
    public List<PurchaseOrderInfo> queryPurchaseOrderListDetailByCreateTime(JSONObject jsonObject) {
        PurchaseOrderQueryCommand command = new PurchaseOrderQueryCommand();
        long currentTimeMillis = System.currentTimeMillis();
        command.setGmtCreateStart(currentTimeMillis - TimeUnit.DAYS.toMillis((Integer) jsonObject.get("startDay")));
        command.setGmtCreateEnd(currentTimeMillis - TimeUnit.DAYS.toMillis((Integer) jsonObject.get("endDay")));
        command.setOrderStatus(Arrays.asList(40, 45));
        log.info("查询已付款、部分收货的采购单据:{}", JSON.toJSONString(command));
        return purchaseOrderFeignApi.queryOrderListDetailByCreateTime(command).mustSuccessOrThrowOriginal();
    }

    public List<PurchaseOrderInfo> queryCancelStockOrder(JSONObject jsonObject) {
        PurchaseOrderQueryCommand command = new PurchaseOrderQueryCommand();
        long currentTimeMillis = System.currentTimeMillis();
        command.setGmtCreateStart(currentTimeMillis - TimeUnit.DAYS.toMillis((Integer) jsonObject.get("startDay")));
        command.setGmtCreateEnd(currentTimeMillis - TimeUnit.DAYS.toMillis((Integer) jsonObject.get("endDay")));
        command.setOrderStatus(Collections.singletonList(45));
        command.setCustomType("cancelStock");
        return purchaseOrderFeignApi.queryCancelStockOrder(command).mustSuccessOrThrowOriginal();
    }

//    修改供应商链接
//    public void updateSkuAlilink(UpdateSkuAlilinkCommand command){
//        purchaseSkuFeignApi.updateSkuAlilink(command);
//    }

}
