package com.newnary.gsp.center.tpsi.service.eccang.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.logistics.api.delivery.dto.DeliveryOrderConsignorInfoDTO;
import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderDetailInfo;
import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderDetailItemInfo;
import com.newnary.gsp.center.product.api.product.response.ChannelSaleItemDetailInfo;
import com.newnary.gsp.center.tpsi.infra.client.eccang.EcCangERPApiClient;
import com.newnary.gsp.center.tpsi.infra.client.eccang.params.EcCangERPParams;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp.*;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.erp.EcCangERPGetPurchaseOrdersResponse;
import com.newnary.gsp.center.tpsi.infra.model.ApiRequestParams;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.DeliveryOrderItemEcCangApiAssociation;
import com.newnary.gsp.center.tpsi.infra.rpc.SaleItemRpc;
import com.newnary.gsp.center.tpsi.service.eccang.IEccangERPApiSve;
import com.newnary.gsp.center.tpsi.service.impl.SystemClientSve;
import com.newnary.spring.cloud.domain.Asserts;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class EccangERPApiSveImpl extends SystemClientSve implements IEccangERPApiSve {

    private static Map<String, EcCangERPApiClient> apiClientMap = new ConcurrentHashMap<>();

    private static EcCangERPApiClient getApiClient(String params) {
        EcCangERPParams paramsObj = JSON.parseObject(params, EcCangERPParams.class);
        return apiClientMap.computeIfAbsent(paramsObj.getUserName(), k -> new EcCangERPApiClient(paramsObj));
    }

    @Resource
    private SaleItemRpc saleItemRpc;

    @Override
    public EcCangApiBaseResult<String> createOrder(DeliveryOrderDetailInfo deliveryOrderDetailInfo, ThirdPartySystem thirdPartySystem, ApiRequestParams apiRequestParams) {
        //获取apiClient
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());

        EcCangERPSyncOrderRequest request = buildCreateOrderRequest(deliveryOrderDetailInfo, apiRequestParams);
        String resultStr = ecCangERPApiClient.sendRequest(request, "syncOrder", "EB");
        return buildErpApiBaseResult(resultStr);
    }

    private EcCangERPSyncOrderRequest buildCreateOrderRequest(DeliveryOrderDetailInfo deliveryOrderDetailInfo, ApiRequestParams apiRequestParams) {
        EcCangERPSyncOrderRequest params = JSON.parseObject(apiRequestParams.getParams(), EcCangERPSyncOrderRequest.class);

        EcCangERPSyncOrderRequest request = new EcCangERPSyncOrderRequest();
        // 订单基础信息
        request.actionType = "ADD";
        //是否创建后自动审核
        request.orderVerify = params.orderVerify;
        request.refNoIsUnique = "0";

        EcCangERPSyncOrderRequest.Order order = new EcCangERPSyncOrderRequest.Order();

        //平台
        order.platform = params.order.platform;
        order.orderType = "sale";
//        order.refNo = IDGenerator.generateBizNidWithPrefix("", LengthType.SIXTEEN);
        //TODO 待定用哪个单号
        order.refNo = deliveryOrderDetailInfo.tradeOrderId.concat(deliveryOrderDetailInfo.tradeSubOrderId);

        order.userAccount = params.order.userAccount;
        order.currency = deliveryOrderDetailInfo.amountInfo.getTradeCurrency();
        //易仓的币种不是CNY是RMB
        order.currency = "RMB";

        order.buyerId = params.order.buyerId;
        order.buyerName = params.order.buyerName;
        order.warehouseId = params.order.warehouseId;
        order.shippingMethod = params.order.shippingMethod;
        //order下其余属性暂不录入,需要时再补充
        request.order = order;

        List<DeliveryOrderDetailItemInfo> itemInfos = deliveryOrderDetailInfo.items;
        List<EcCangERPSyncOrderRequest.OrderDetail> orderDetails = new ArrayList<>();
        for (DeliveryOrderDetailItemInfo itemInfo : itemInfos) {
            EcCangERPSyncOrderRequest.OrderDetail orderDetail = new EcCangERPSyncOrderRequest.OrderDetail();
            orderDetail.productSku = StringUtils.isEmpty(itemInfo.customCode) ? itemInfo.supplierSkuId : itemInfo.customCode;
            orderDetail.unitPrice = Double.parseDouble(itemInfo.supplyPrice);
            orderDetail.qty = itemInfo.quantity;
            orderDetail.productTitle = itemInfo.getSupplierSkuTitle();

            //设置不发货
            orderDetail.action = 0;
            //orderDetail下其余属性暂不录入,需要时再补充
            orderDetails.add(orderDetail);
        }
        request.orderDetails = orderDetails;

        EcCangERPSyncOrderRequest.OrderAddress orderAddress = new EcCangERPSyncOrderRequest.OrderAddress();
        DeliveryOrderConsignorInfoDTO groupWarehouseAddrInfo = deliveryOrderDetailInfo.getGroupWarehouseAddrInfo();
        orderAddress.name = groupWarehouseAddrInfo.name;
        orderAddress.countryCode = groupWarehouseAddrInfo.addrCountry;
        orderAddress.cityName = groupWarehouseAddrInfo.addrCity;
        orderAddress.postalCode = groupWarehouseAddrInfo.zipCode;
        orderAddress.line1 = groupWarehouseAddrInfo.addrDetail;
        orderAddress.district = groupWarehouseAddrInfo.addrDistrict;
        orderAddress.state = groupWarehouseAddrInfo.addrProvince;
        orderAddress.phone = groupWarehouseAddrInfo.contactNumber;
        //orderAddress下其余属性暂不录入,需要时再补充
        request.orderAddress = orderAddress;
        return request;
    }

    @Override
    public EcCangApiBaseResult<String> getOrderListBySaleOrderCode(ThirdPartySystem thirdPartySystem, String saleOrderCode) {
        //获取apiClient
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());

        EcCangERPGetOrderListRequest request = buildGetOrderListRequest(saleOrderCode);
        String resultStr = ecCangERPApiClient.sendRequest(request, "getOrderList", "EB");
        return buildErpApiBaseResult(resultStr);
    }

    private EcCangERPGetOrderListRequest buildGetOrderListRequest(String saleOrderCode) {
        EcCangERPGetOrderListRequest request = new EcCangERPGetOrderListRequest();
        EcCangERPGetOrderListRequest.Condition condition = new EcCangERPGetOrderListRequest.Condition();

        List<String> saleOrderCodes = new ArrayList<>();
        saleOrderCodes.add(saleOrderCode);
        condition.saleOrderCodes = saleOrderCodes;
        request.condition = condition;
        return request;
    }

    @Override
    public EcCangApiBaseResult<String> syncProduct(DeliveryOrderDetailItemInfo deliveryOrderDetailItemInfo, ThirdPartySystem thirdPartySystem, ApiRequestParams apiRequestParams, String channelId) {
        //获取apiClient
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());

        JSONObject productJson = new JSONObject();
        productJson.put("productSku", StringUtils.isEmpty(deliveryOrderDetailItemInfo.customCode) ? deliveryOrderDetailItemInfo.supplierSkuId : deliveryOrderDetailItemInfo.customCode);
        String productRet = ecCangERPApiClient.sendRequest(productJson, "getProductBySku", "WMS");
        EcCangApiBaseResult<String> productExist = buildErpApiBaseResult(productRet);
        JSONArray productArray = JSON.parseArray(productExist.getData());
        String opertion = "EDIT";
        if (productArray.isEmpty()) {
            opertion = "ADD";
        }

        EcCangERPSyncProductRequest request = buildSyncProductRequest(opertion, deliveryOrderDetailItemInfo, apiRequestParams, channelId);
        String resultStr = ecCangERPApiClient.sendRequest(request, "syncProduct", "WMS");
        return buildErpApiBaseResult(resultStr);
    }

    private EcCangERPSyncProductRequest buildSyncProductRequest(String operation, DeliveryOrderDetailItemInfo deliveryOrderDetailItemInfo, ApiRequestParams apiRequestParams, String channelId) {
        EcCangERPSyncProductRequest params = JSON.parseObject(apiRequestParams.getParams(), EcCangERPSyncProductRequest.class);

        EcCangERPSyncProductRequest request = new EcCangERPSyncProductRequest();

        //当出库单详情里的信息不足以支撑业务上的流程，则通过saleItemCode获取更详细的产品详情去补充信息
        try {
            ChannelSaleItemDetailInfo channelSaleItemDetailInfo = saleItemRpc.querySaleItem(channelId, deliveryOrderDetailItemInfo.getSaleItemCode());
            request.actionType = operation;
            request.productSku = StringUtils.isEmpty(deliveryOrderDetailItemInfo.customCode) ? deliveryOrderDetailItemInfo.supplierSkuId : deliveryOrderDetailItemInfo.customCode;
            request.productTitle = deliveryOrderDetailItemInfo.supplierSkuTitle;
/*            String enTitle = channelSaleItemDetailInfo.skuDetailInfo.spuBaseInfo.enTitle;
            enTitle = StringUtils.isEmpty(enTitle) ? channelSaleItemDetailInfo.skuDetailInfo.spuBaseInfo.zhTitle : enTitle;
            enTitle = StringUtils.isEmpty(enTitle) ? deliveryOrderDetailItemInfo.supplierSkuTitle : enTitle;*/

            //TODO
            String enTitle = channelSaleItemDetailInfo.skuDetailInfo.spuBaseInfo.descInfo.getTitle();
            enTitle = StringUtils.isEmpty(enTitle) ? channelSaleItemDetailInfo.skuDetailInfo.spuBaseInfo.descInfo.getTitle() : enTitle;
            enTitle = StringUtils.isEmpty(enTitle) ? deliveryOrderDetailItemInfo.supplierSkuTitle : enTitle;

            request.productTitleEn = enTitle;
            request.pdOverseaTypeCn = deliveryOrderDetailItemInfo.invoiceCnName;
            request.pdOverseaTypeEn = deliveryOrderDetailItemInfo.invoiceEnName;
            request.productDeclaredValue = deliveryOrderDetailItemInfo.supplyPrice;
            request.pdDeclareCurrencyCode = deliveryOrderDetailItemInfo.supplyPriceCurrency;
            request.defaultSupplierCode = params.defaultSupplierCode;
            request.userOrganizationId = params.userOrganizationId;
            JSONArray specsArray = new JSONArray();
            if (CollectionUtils.isNotEmpty(channelSaleItemDetailInfo.getSkuDetailInfo().getSkuInfo().getSpecs())) {
                channelSaleItemDetailInfo.getSkuDetailInfo().getSkuInfo().getSpecs().forEach(supplierSkuSpecInfo -> {
                    JSONObject specsJson = new JSONObject();
                    specsJson.put(supplierSkuSpecInfo.getSpecName(), supplierSkuSpecInfo.getSpecValue());
                    specsArray.add(specsJson);
                });
            }
            request.productSpecs = specsArray.toJSONString();
            request.changeProductProperty(channelSaleItemDetailInfo.skuDetailInfo.skuInfo.specs);
            String productAddress = channelSaleItemDetailInfo.skuDetailInfo.skuInfo.refProductLink;
            if (StringUtils.isNotEmpty(productAddress)) {
                List<String> spProductAddress = Arrays.asList(productAddress);
                request.refUrl = spProductAddress;
                request.spProductAddress = spProductAddress;
            }
            if ("ADD".equals(operation)) {
                BigDecimal grossWeight = channelSaleItemDetailInfo.skuDetailInfo.skuInfo.grossWeight;
                BigDecimal netWeight = channelSaleItemDetailInfo.skuDetailInfo.skuInfo.netWeight;
                BigDecimal sizeHeight = channelSaleItemDetailInfo.skuDetailInfo.skuInfo.sizeHeight;
                BigDecimal sizeLength = channelSaleItemDetailInfo.skuDetailInfo.skuInfo.sizeLength;
                BigDecimal sizeWidth = channelSaleItemDetailInfo.skuDetailInfo.skuInfo.sizeWidth;
                BigDecimal packingHeight = channelSaleItemDetailInfo.skuDetailInfo.skuInfo.packingHeight;
                BigDecimal packingLength = channelSaleItemDetailInfo.skuDetailInfo.skuInfo.packingLength;
                BigDecimal packingWidth = channelSaleItemDetailInfo.skuDetailInfo.skuInfo.packingWidth;
                switch (judgeSizeInfo(grossWeight, netWeight)) {
                    case 1:
                        request.productWeight = grossWeight.doubleValue();
                        request.pdNetWeight = netWeight.doubleValue();
                        break;
                    case 2:
                        request.productWeight = netWeight.doubleValue();
                        request.pdNetWeight = netWeight.doubleValue();
                        break;
                    case 3:
                        request.productWeight = grossWeight.doubleValue();
                        request.pdNetWeight = grossWeight.doubleValue();
                        break;
                    default:
                        request.productWeight = 1.00;
                        request.pdNetWeight = 1.00;
                }

                switch (judgeSizeInfo(sizeHeight, packingHeight)) {
                    case 1:
                        request.productHeight = packingHeight.multiply(new BigDecimal("100")).doubleValue();
                        request.pdNetHeight = sizeHeight.multiply(new BigDecimal("100")).doubleValue();
                        break;
                    case 2:
                        request.productHeight = packingHeight.multiply(new BigDecimal("100")).doubleValue();
                        request.pdNetHeight = packingHeight.multiply(new BigDecimal("100")).doubleValue();
                        break;
                    case 3:
                        request.productHeight = sizeHeight.multiply(new BigDecimal("100")).doubleValue();
                        request.pdNetHeight = sizeHeight.multiply(new BigDecimal("100")).doubleValue();
                        break;
                    default:
                        request.productHeight = 1.00;
                        request.pdNetHeight = 1.00;

                }
                switch (judgeSizeInfo(sizeLength, packingLength)) {
                    case 1:
                        request.productLength = packingLength.multiply(new BigDecimal("100")).doubleValue();
                        request.pdNetLength = sizeLength.multiply(new BigDecimal("100")).doubleValue();
                        break;
                    case 2:
                        request.productLength = packingHeight.multiply(new BigDecimal("100")).doubleValue();
                        request.pdNetLength = packingHeight.multiply(new BigDecimal("100")).doubleValue();
                        break;
                    case 3:
                        request.productLength = sizeHeight.multiply(new BigDecimal("100")).doubleValue();
                        request.pdNetLength = sizeHeight.multiply(new BigDecimal("100")).doubleValue();
                        break;
                    default:
                        request.productLength = 1.00;
                        request.pdNetLength = 1.00;

                }
                switch (judgeSizeInfo(sizeWidth, packingWidth)) {
                    case 1:
                        request.productWidth = packingWidth.multiply(new BigDecimal("100")).doubleValue();
                        request.pdNetWidth = sizeWidth.multiply(new BigDecimal("100")).doubleValue();
                        break;
                    case 2:
                        request.productWidth = packingWidth.multiply(new BigDecimal("100")).doubleValue();
                        request.pdNetWidth = packingWidth.multiply(new BigDecimal("100")).doubleValue();
                        break;
                    case 3:
                        request.productWidth = sizeWidth.multiply(new BigDecimal("100")).doubleValue();
                        request.pdNetWidth = sizeWidth.multiply(new BigDecimal("100")).doubleValue();
                        break;
                    default:
                        request.productWidth = 1.00;
                        request.pdNetWidth = 1.00;

                }

            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取detailItem详情出错{}", deliveryOrderDetailItemInfo.getSaleItemCode());
        }

        return request;
    }

    //判断尺寸信息
    public int judgeSizeInfo(BigDecimal size1, BigDecimal size2) {
        int i = 0;
        if ((ObjectUtils.isNotEmpty(size1) && size1.doubleValue() > 0.00) && (ObjectUtils.isNotEmpty(size2) && size2.doubleValue() > 0.00)) {
            i = 1;
        } else if ((ObjectUtils.isEmpty(size1) || size1.doubleValue() <= 0.00) && (ObjectUtils.isNotEmpty(size2) && size2.doubleValue() > 0.00)) {
            i = 2;
        } else if ((ObjectUtils.isNotEmpty(size1) && size1.doubleValue() > 0.00) && (ObjectUtils.isEmpty(size2) || size2.doubleValue() <= 0.00)) {
            i = 3;
        }
        return i;
    }

    @Override
    public EcCangApiBaseResult<String> cancelOrder(ThirdPartySystem thirdPartySystem, String saleOrderCode) {
        //获取apiClient
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());

        EcCangERPCancelOrderRequest request = buildCancelOrderRequest(saleOrderCode);
        String resultStr = ecCangERPApiClient.sendRequest(request, "cancelOrder", "EB");
        return buildErpApiBaseResult(resultStr);
    }

    private EcCangERPCancelOrderRequest buildCancelOrderRequest(String saleOrderCode) {
        EcCangERPCancelOrderRequest request = new EcCangERPCancelOrderRequest();
        request.saleOrderCode = saleOrderCode;
        return request;
    }

    @Override
    public EcCangApiBaseResult<String> syncPurchaseOrders(String orderRefNo, DeliveryOrderItemEcCangApiAssociation item, ThirdPartySystem thirdPartySystem, ApiRequestParams apiRequestParams) {
        EcCangApiBaseResult<String> result = getPurchaseOrders(orderRefNo, thirdPartySystem);
        if (result.getCode().equals("200")) {
            //更新关联信息
            List<EcCangERPGetPurchaseOrdersResponse> responsePurchaseOrders = JSONObject.parseArray(result.getData(), EcCangERPGetPurchaseOrdersResponse.class);
            if (CollectionUtils.isEmpty(responsePurchaseOrders)) {
                //获取apiClient
                EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());

                EcCangERPSyncPurchaseOrdersRequest request = buildSyncPurchaseOrdersRequest(orderRefNo, item, apiRequestParams);

                String resultStr = ecCangERPApiClient.sendRequest(request, "syncPurchaseOrders", "WMS");
                return buildErpApiBaseResult(resultStr);
            } else {
                return result;
            }
        }
        return null;
    }

    private EcCangERPSyncPurchaseOrdersRequest buildSyncPurchaseOrdersRequest(String orderRefNo, DeliveryOrderItemEcCangApiAssociation item, ApiRequestParams apiRequestParams) {
        EcCangERPSyncPurchaseOrdersRequest params = JSON.parseObject(apiRequestParams.getParams(), EcCangERPSyncPurchaseOrdersRequest.class);

        EcCangERPSyncPurchaseOrdersRequest request = new EcCangERPSyncPurchaseOrdersRequest();
        request.warehouse_id = params.warehouse_id;
        request.po_type = params.po_type;
        request.suppiler_id = params.suppiler_id;
        request.operator_purchase = params.operator_purchase;
        request.operation_type = params.operation_type;
        request.account_type = params.account_type;
        request.currency_code = params.currency_code;
        request.pay_type = params.pay_type;
        request.supplier_pay_type = params.supplier_pay_type;
        request.shipping_method_id_head = params.shipping_method_id_head;
        request.pts_oprater = params.pts_oprater;
        request.po_is_net = 0;
        request.ref_no = orderRefNo;
//        request.po_status = 3;    //需要设置为待确认的状态交由采购人员进行编辑为1688采购单进行人工采购
        List<EcCangERPSyncPurchaseOrdersRequest.ProductList> productList = new ArrayList<>();

        EcCangERPSyncPurchaseOrdersRequest.ProductList product = new EcCangERPSyncPurchaseOrdersRequest.ProductList();
        //当出库单详情里的信息不足以支撑业务上的流程，则通过saleItemCode获取更详细的产品详情去补充信息
        try {
            ChannelSaleItemDetailInfo channelSaleItemDetailInfo = saleItemRpc.querySaleItem("eccang", item.getSaleItemCode());
            product.product_sku = StringUtils.isEmpty(item.getCustomCode()) ? item.getSupplierSkuId() : item.getCustomCode();
            product.qty_expected = item.getQuantity();
            product.currency_code = params.currency_code;
            product.unit_price = Double.valueOf(item.getSupplyPrice());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取detailItem详情出错{}", item.getSaleItemCode());
        }

        productList.add(product);

        request.productList = productList;
        return request;
    }

    @Override
    public EcCangApiBaseResult<String> getPurchaseOrders(String orderRefNo, ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());

        EcCangERPGetPurchaseOrdersRequest request = new EcCangERPGetPurchaseOrdersRequest();
        request.code_like = orderRefNo;
        request.po_staus_type = 1;
        request.pageSize = 100;
        String resultStr = ecCangERPApiClient.sendRequest(request, "getPurchaseOrders", "WMS");
        //由于反斜杠存在，此版本的含有转义字符("\")导致转换失败，所以先将其替换
        return buildErpApiBaseResult(StringEscapeUtils.unescapeJavaScript(resultStr));
    }

    @Override
    public EcCangApiBaseResult<String> getPurchaseOrdersNoDecode(String orderRefNo, ThirdPartySystem thirdPartySystem, Integer poStaus, Integer page) {
        //获取apiClient
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());

        EcCangERPGetPurchaseOrdersRequest request = new EcCangERPGetPurchaseOrdersRequest();
        if (StringUtils.isNotEmpty(orderRefNo)) {
            request.code_like = orderRefNo;
        }
        request.po_staus_type = 1;
        if (Objects.nonNull(poStaus)) {
            request.po_staus = poStaus;
        }
        if (Objects.nonNull(page)) {
            request.page = page;
        }
        request.pageSize = 1000;
        request.orderBy = "date_create desc";
        String resultStr = ecCangERPApiClient.sendRequestNoDecode(request, "getPurchaseOrders", "WMS");
        return buildErpApiBaseResult(resultStr);
    }

    @Override
    public EcCangApiBaseResult<String> cancelPurchaseOrder(ThirdPartySystem thirdPartySystem, Set<String> poCodes) {
        Asserts.assertCollectionNotEmpty(poCodes, "易仓ERP采购单号集合不能为空");
        //获取apiClient
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());

        JSONObject request = new JSONObject();
        JSONArray poCodeArray = new JSONArray();
        poCodes.forEach(poCode -> {
            poCodeArray.add(poCode);
        });
        request.put("poCode", poCodeArray);
        String resultStr = ecCangERPApiClient.sendRequest(request, "revocationPurchase", "WMS");
        return buildErpApiBaseResult(resultStr);
    }

    @Override
    public EcCangApiBaseResult<String> purchaseOrderInbound(String referenceNo, DeliveryOrderItemEcCangApiAssociation item, ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());

        EcCangERPPurchaseOrderInboundRequest request = buildPurchaseOrderInboundRequest(referenceNo, item);
        String resultStr = ecCangERPApiClient.sendRequest(request, "purchaseOrderInbound", "WMS");
        return buildErpApiBaseResult(resultStr);
    }

    private EcCangERPPurchaseOrderInboundRequest buildPurchaseOrderInboundRequest(String referenceNo, DeliveryOrderItemEcCangApiAssociation item) {
        EcCangERPPurchaseOrderInboundRequest request = new EcCangERPPurchaseOrderInboundRequest();
        request.poCode = item.getErpPurchaseOrderCode();
        //参考号，可以修改
        request.refrenceNo = referenceNo;
        List<EcCangERPPurchaseOrderInboundRequest.Product> productList = new ArrayList<>();
        EcCangERPPurchaseOrderInboundRequest.Product product = new EcCangERPPurchaseOrderInboundRequest.Product();
        product.productSku = StringUtils.isEmpty(item.getCustomCode()) ? item.getSupplierSkuId() : item.getCustomCode();
        product.receivedQty = item.getQuantity();

        productList.add(product);
        request.productList = productList;
        return request;
    }

    @Override
    public EcCangApiBaseResult<String> getWarehouseList(ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());

        String resultStr = ecCangERPApiClient.sendRequest(null, "getWarehouses", "WMS");
        //System.out.println(resultStr);
        return buildErpApiBaseResult(resultStr);
    }

    @Override
    public EcCangApiBaseResult<String> getUserList(ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());

        String resultStr = ecCangERPApiClient.sendRequest(null, "getUser", "WMS");
        //System.out.println(resultStr);
        return buildErpApiBaseResult(resultStr);
    }

    @Override
    public EcCangApiBaseResult<String> getProductList(ThirdPartySystem thirdPartySystem, EcCangERPGetProductListRequest request) {
        //获取apiClient
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());

        String resultStr = ecCangERPApiClient.sendRequest(request, "getProductList", "WMS");
        //System.out.println(resultStr);
        return buildErpApiBaseResult(resultStr);
    }

    @Override
    public EcCangApiBaseResult<String> getProductBySku(ThirdPartySystem thirdPartySystem, EcCangERPGetProductBySkuRequest request) {
        //获取apiClient
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());

        String resultStr = ecCangERPApiClient.sendRequest(request, "getProductBySku", "WMS");
        return buildErpApiBaseResult(resultStr);
    }

    @Override
    public EcCangApiBaseResult<String> syncSupplier(ThirdPartySystem thirdPartySystem, EcCangERPSyncSupplierRequest request) {
        //获取apiClient
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());

        String resultStr = ecCangERPApiClient.sendRequest(request, "syncSupplierInfo", "WMS");
        return buildErpApiBaseResult(resultStr);
    }

    @Override
    public EcCangApiBaseResult<String> getSupplierProductList(ThirdPartySystem thirdPartySystem, EcCangERPGetSupplierProductRequest request) {
        //获取apiClient
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());

        String resultStr = ecCangERPApiClient.sendRequest(request, "getSupplierProductList", "WMS");
        return buildErpApiBaseResult(resultStr);
    }

    @Override
    public EcCangApiBaseResult<String> getSupplierList(ThirdPartySystem thirdPartySystem, EcCangERPGetSupplierListRequest request) {
        //获取apiClient
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());

        String resultStr = ecCangERPApiClient.sendRequest(request, "getSupplierList", "WMS");
        return buildErpApiBaseResult(resultStr);
    }

    @Override
    public EcCangApiBaseResult<String> syncProduct(ThirdPartySystem thirdPartySystem, EcCangERPSyncProductRequest request) {
        //获取apiClient
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());

        JSONObject productJson = new JSONObject();
        productJson.put("productSku", request.productSku);
        String productRet = ecCangERPApiClient.sendRequest(productJson, "getProductBySku", "WMS");
        EcCangApiBaseResult<String> productExist = buildErpApiBaseResult(productRet);
        JSONArray productArray = JSON.parseArray(productExist.getData());
        if (!productArray.isEmpty()) {
/*            EcCangApiBaseResult<String> apiBaseResult = new EcCangApiBaseResult<>();
            JSONObject jsonObject = productArray.getJSONObject(0);
            apiBaseResult.setData(jsonObject.getString("productSku"));
            return apiBaseResult;*/
            request.actionType = "EDIT";
            String resultStr = ecCangERPApiClient.sendRequest(request, "syncProduct", "WMS");
            return buildErpApiBaseResult(resultStr);
        }
        String resultStr = ecCangERPApiClient.sendRequest(request, "syncProduct", "WMS");
        return buildErpApiBaseResult(resultStr);
    }

    @Override
    public EcCangApiBaseResult<String> syncBatchProduct(ThirdPartySystem thirdPartySystem, List<EcCangERPSyncProductRequest> requestList) {
        //获取apiClient
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());
        if (CollectionUtils.isEmpty(requestList) || requestList.size() > 100) {
            throw new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "批量同步商品数量不能为空或超过100");
        }
        String resultStr = ecCangERPApiClient.sendRequest(requestList.toArray(), "syncBatchProduct", "WMS");
        return buildErpApiBaseResult(resultStr);
    }

    @Override
    public EcCangApiBaseResult<String> syncPurchaseOrders(ThirdPartySystem thirdPartySystem, EcCangERPSyncPurchaseOrdersRequest request) {
        // 固定操作类型为新增
        request.action_type = request.action_type == null ? "ADD" : request.action_type;
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());
        String resultStr = ecCangERPApiClient.sendRequest(request, "syncPurchaseOrders", "WMS");
        return buildErpApiBaseResult(resultStr);
    }

    @Override
    public EcCangApiBaseResult<String> purchaseOrderInbound(ThirdPartySystem thirdPartySystem, EcCangERPPurchaseOrderInboundRequest request) {
        //获取apiClient
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());
        String resultStr = ecCangERPApiClient.sendRequest(request, "purchaseOrderInbound", "WMS");
        return buildErpApiBaseResult(resultStr);
    }

    @Override
    public EcCangApiBaseResult<String> getSupplier(ThirdPartySystem thirdPartySystem) {
        //获取apiClient
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());
        String resultStr = ecCangERPApiClient.sendRequest(null, "getSupplier", "WMS");
        return buildErpApiBaseResult(resultStr);
    }

    @Override
    public EcCangApiBaseResult<String> syncReceiving(ThirdPartySystem thirdPartySystem, EcCangERPStockinOrderSyncReceivingRequest request) {
        //获取apiClient
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());
        String resultStr = ecCangERPApiClient.sendRequest(request, "syncReceiving", "WMS");
        return buildErpApiBaseResult(resultStr);
    }

    @Override
    public EcCangApiBaseResult<String> getReceiving(ThirdPartySystem thirdPartySystem, EcCangERPStockinOrderGetReceivingRequest request) {
        //获取apiClient
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());
        String resultStr = ecCangERPApiClient.sendRequest(request, "getReceiving", "WMS");
        return buildErpApiBaseResult(resultStr);
    }

    @Override
    public EcCangApiBaseResult<String> purchaseOrderReceiving(ThirdPartySystem thirdPartySystem, EcCangERPPurchaseOrderReceivingRequest request) {
        //获取apiClient
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());
        String resultStr = ecCangERPApiClient.sendRequest(request, "purchaseOrderReceiving", "WMS");
        return buildErpApiBaseResult(resultStr);
    }

    @Override
    public EcCangApiBaseResult<String> syncSupplierProduct(ThirdPartySystem thirdPartySystem, EcCangERPSyncSupplierProductRequest request) {
        //获取apiClient
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());
        String resultStr = ecCangERPApiClient.sendRequest(request, "syncSupplierProduct", "WMS");
        return buildErpApiBaseResult(resultStr);
    }

    @Override
    public EcCangApiBaseResult<String> getWarehouseLocation(ThirdPartySystem thirdPartySystem, EcCangERPGetWarehouseLocationRequest request) {
        //获取apiClient
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());
        String resultStr = ecCangERPApiClient.sendRequest(request, "getWarehouseLocation", "WMS");
        return buildErpApiBaseResult(resultStr);
    }

    @Override
    public EcCangApiBaseResult<String> verifyPurchase(ThirdPartySystem thirdPartySystem, EcCangERPPurchaseOrderVerifyPurchaseRequest request) {
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());
        String resultStr = ecCangERPApiClient.sendRequest(request, "verifyPurchase", "WMS");
        return buildErpApiBaseResult(resultStr);
    }

    @Override
    public EcCangApiBaseResult<String> syncConfirmReceiving(ThirdPartySystem thirdPartySystem, EcCangERPPurchaseOrderSyncConfirmReceivingRequest request) {
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());
        String resultStr = ecCangERPApiClient.sendRequest(request, "syncConfirmReceiving", "WMS");
        return buildErpApiBaseResult(resultStr);
    }

    @Override
    public EcCangApiBaseResult<String> syncPurchaseTrackingNote(ThirdPartySystem thirdPartySystem, EcCangERPPurchaseOrderSyncPurchaseTrackingNoteRequest request) {
        EcCangERPApiClient ecCangERPApiClient = getApiClient(thirdPartySystem.getParams());
        String resultStr = ecCangERPApiClient.sendRequest(request, "syncPurchaseTrackingNote", "WMS");
        return buildErpApiBaseResult(resultStr);
    }

    private EcCangApiBaseResult<String> buildErpApiBaseResult(String resultStr) {
        EcCangApiBaseResult<String> apiBaseResult = new EcCangApiBaseResult<>();
        JSONObject resultObj = JSONObject.parseObject(resultStr);

        if (StringUtils.isBlank(resultStr) || Objects.isNull(resultObj)) {
            apiBaseResult.setCode("Failure");
            apiBaseResult.setMessage("ECCANG ERP 接口调用失败");
            return apiBaseResult;
        }

        apiBaseResult.setCode(resultObj.getString("code"));
        apiBaseResult.setMessage(resultObj.getString("message"));
        if (resultObj.containsKey("data")) {
            apiBaseResult.setData(resultObj.getString("data"));
        } else {
            apiBaseResult.setData(resultStr);
        }
        apiBaseResult.setError(resultObj.getString("error"));
        return apiBaseResult;
    }

//    private EcCangERPApiClient getClient(String ecCangParams) {
//        return new EcCangERPApiClient(ecCangParams);
//    }

}
