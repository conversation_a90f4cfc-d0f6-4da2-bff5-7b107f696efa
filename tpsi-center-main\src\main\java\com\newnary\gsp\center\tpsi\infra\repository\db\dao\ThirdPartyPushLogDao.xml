<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.newnary.gsp.center.tpsi.infra.repository.db.dao.ThirdPartyPushLogDao">

<resultMap id="thirdPartyPushLogPOResult" type="com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyPushLogPO">
    <result column="resp_data" property="respData"/>
    <result column="event_data" property="eventData"/>
    <result column="event_biz_id" property="eventBizId"/>
    <result column="event_biz_state" property="eventBizState"/>
    <result column="req_data" property="reqData"/>
    <result column="event_type" property="eventType"/>
    <result column="event_state" property="eventState"/>
    <result column="sys_remark" property="sysRemark"/>
    <result column="manual_remark" property="manualRemark"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="id" property="id"/>
    <result column="gmt_create" property="gmtCreate"/>
    <result column="gmt_modified" property="gmtModified"/>
</resultMap>

<sql id="thirdPartyPushLogPO_columns">
    resp_data,
    event_data,
    event_biz_id,
    event_biz_state,
    req_data,
    event_type,
    event_state,
    sys_remark,
    manual_remark,
    tenant_id,
    id,
    gmt_create,
    gmt_modified
</sql>

<sql id="thirdPartyPushLogPO_sqlForInsert">
    resp_data,
    event_data,
    event_biz_id,
    event_biz_state,
    req_data,
    event_type,
    event_state,
    sys_remark,
    manual_remark,
    tenant_id,
    id,
    gmt_create,
    gmt_modified
</sql>

<sql id="thirdPartyPushLogPO_columnsForInsert">
    #{respData},
    #{eventData},
    #{eventBizId},
    #{eventBizState},
    #{reqData},
    #{eventType},
    #{eventState},
    #{sysRemark},
    #{manualRemark},
    #{tenantId},
    #{id},
    REPLACE(unix_timestamp(NOW(3)),'.',''),
    REPLACE(unix_timestamp(NOW(3)),'.','')
</sql>

<sql id="thirdPartyPushLogPO_setterForUpdate">
    <set>
        gmt_modified = REPLACE(unix_timestamp(NOW(3)),'.',''),
        resp_data = #{respData},
        event_data = #{eventData},
        event_biz_id = #{eventBizId},
        event_biz_state = #{eventBizState},
        req_data = #{reqData},
        event_type = #{eventType},
        event_state = #{eventState},
        sys_remark = #{sysRemark},
        manual_remark = #{manualRemark},
    </set>
</sql>

<sql id="thirdPartyPushLogPO_selector">
    select
    <include refid="thirdPartyPushLogPO_columns"/>
    from third_party_push_log
</sql>

<sql id="thirdPartyPushLogPO_query_segment">
    <trim prefix="WHERE" prefixOverrides="AND|OR">
        <if test="data.respData != null">
            AND resp_data = #{data.respData}
        </if>
        <if test="data.eventData != null">
            AND event_data = #{data.eventData}
        </if>
        <if test="data.eventBizId != null">
            AND event_biz_id = #{data.eventBizId}
        </if>
        <if test="data.eventBizState != null">
            AND event_biz_state = #{data.eventBizState}
        </if>
        <if test="data.reqData != null">
            AND req_data = #{data.reqData}
        </if>
        <if test="data.eventType != null">
            AND event_type = #{data.eventType}
        </if>
        <if test="data.eventState != null">
            AND event_state = #{data.eventState}
        </if>
        <if test="data.sysRemark != null">
            AND sys_remark = #{data.sysRemark}
        </if>
        <if test="data.manualRemark != null">
            AND manual_remark = #{data.manualRemark}
        </if>
        <if test="data.id != null">
            AND id = #{data.id}
        </if>
        <if test="data.gmtCreate != null">
            AND gmt_create = #{data.gmtCreate}
        </if>
        <if test="data.gmtModified != null">
            AND gmt_modified = #{data.gmtModified}
        </if>
        <if test="ins != null and ins.size() > 0">
            AND
            <foreach collection="ins" item="item" open="(" separator="and" close=")">
                ${item.column} in
                <foreach collection="item.values" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </foreach>
        </if>
        <if test="notIns != null and notIns.size() > 0">
            AND
            <foreach collection="notIns" item="item" open="(" separator="and" close=")">
                ${item.column} not in
                <foreach collection="item.values" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </foreach>
        </if>
        <if test="equals != null and equals.size() > 0">
            AND
            <foreach collection="equals" item="item" open="(" separator="and" close=")">
                ${item.column} = #{item.value}
            </foreach>
        </if>
        <if test="notEquals != null and notEquals.size() > 0">
            AND
            <foreach collection="notEquals" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ <> ]]> #{item.value}
            </foreach>
        </if>
        <if test="lts != null and lts.size() > 0">
            AND
            <foreach collection="lts" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ < ]]> #{item.value}
            </foreach>
        </if>
        <if test="gts != null and gts.size() > 0">
            AND
            <foreach collection="gts" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ > ]]> #{item.value}
            </foreach>
        </if>
        <if test="likes != null and likes.size() > 0">
            AND
            <foreach collection="likes" item="item" open="(" separator="and" close=")">
                ${item.column} like concat('%', #{item.value}, '%')
            </foreach>
        </if>
        <if test="ltes != null and ltes.size() > 0">
            AND
            <foreach collection="ltes" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ <= ]]> #{item.value}
            </foreach>
        </if>
        <if test="gtes != null and gtes.size() > 0">
            AND
            <foreach collection="gtes" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ >= ]]> #{item.value}
            </foreach>
        </if>
        <if test="ranges != null and ranges.size() > 0">
            AND
            <foreach collection="ranges" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ >= ]]> #{item.from} and ${item.column} <![CDATA[ < ]]> #{item.to}
            </foreach>
        </if>
        AND tenant_id = #{tenantId}
    </trim>
</sql>


<insert id="insert" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyPushLogPO">
    insert into third_party_push_log
    (
        <include refid="thirdPartyPushLogPO_sqlForInsert"/>
    )
    values
    (
        <include refid="thirdPartyPushLogPO_columnsForInsert"/>
    )
</insert>

<update id="update" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyPushLogPO">
    update third_party_push_log
    <include refid="thirdPartyPushLogPO_setterForUpdate" />
    where id = #{id}
    and tenant_id = #{tenantId}
</update>


<update id="updateByQuery" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyPushLogPO">
    update third_party_push_log
    <set>
        gmt_modified = REPLACE(unix_timestamp(NOW(3)),'.',''),
        resp_data = #{update.respData},
        event_data = #{update.eventData},
        event_biz_id = #{update.eventBizId},
        event_biz_state = #{update.eventBizState},
        req_data = #{update.reqData},
        event_type = #{update.eventType},
        event_state = #{update.eventState},
        sys_remark = #{update.sysRemark},
        manual_remark = #{update.manualRemark},
    </set>
    <include refid="thirdPartyPushLogPO_query_segment"/>
</update>

<!-- Delete -->
<delete id="delete">
    delete from third_party_push_log
    <include refid="thirdPartyPushLogPO_query_segment"/>
</delete>
<delete id="deleteById">
    delete from third_party_push_log
    where id = #{id}
    and tenant_id = #{tenantId}
</delete>





<!-- query class -->
<sql id="thirdPartyPushLogPO_groupBy">
    <if test="groupBys != null and groupBys.size() > 0">
        group by
        <foreach collection="groupBys" item="item" open="" separator="," close="">
            #{item}
        </foreach>
    </if>
</sql>

<sql id="thirdPartyPushLogPO_orderby">
    <if test="orderBys != null and orderBys.size() > 0">
        order by
        <foreach collection="orderBys" item="item" open="" separator="," close="">
            ${item.column} ${item.direction}
        </foreach>
    </if>
</sql>


<select id="query" resultMap="thirdPartyPushLogPOResult" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyPushLogPO">
    <include refid="thirdPartyPushLogPO_selector"/>
    <include refid="thirdPartyPushLogPO_query_segment"/>
    <include refid="thirdPartyPushLogPO_groupBy"/>
    <include refid="thirdPartyPushLogPO_orderby"/>
</select>



<select id="count" resultType="java.lang.Long">
    SELECT COUNT(1) FROM third_party_push_log
    <include refid="thirdPartyPushLogPO_query_segment"/>
</select>

<select id="getById" resultMap="thirdPartyPushLogPOResult">
    <include refid="thirdPartyPushLogPO_selector"/>
    where id = #{id}
    and tenant_id = #{tenantId}
</select>

<select id="getByIdForUpdate" resultMap="thirdPartyPushLogPOResult">
    <include refid="thirdPartyPushLogPO_selector"/>
    where id = #{id}
    and tenant_id = #{tenantId}
    for update
</select>

</mapper>
