package com.newnary.gsp.center.tpsi.api.haiying.enums;

/**
 * @Author: jack
 * @CreateTime: 2022-7-13
 */
public enum HaiYingEbayProductListOrderBy {

    price("商品价格"),
    sold("商品总销售件数"),
    sold_the_previous_day("商品前1天销售件数"),
    payment_the_previous_day("商品前1天销售金额"),
    sales_three_day1("商品前3天销售件数"),
    payment_three_day1("商品前3天销售金额"),
    sales_three_day_growth("商品前3天销售增幅"),
    gen_time("商品上架时间"),
    watchers("收藏数"),
    sold_the_previous_growth("商品前1天销售增幅"),
    visit("商品总浏览数"),
    sales_week1("商品前7天销售件数"),
    sales_week_growth("商品前7天销售增幅"),
    ;

    private String description;

    HaiYingEbayProductListOrderBy(String description) {
        this.description = description;
    }

}
