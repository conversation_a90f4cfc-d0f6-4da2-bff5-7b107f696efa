package com.newnary.gsp.center.tpsi.infra.client.tongtu.valobj;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/12/22 12:01
 */
@Data
public class TongTuGoods {
    private String enablePackageNum;
    private String packageMaterialName;
    private String packageWeight;
    private String packageWidth;
    private String updatedDate;
    private String categoryName;
    private String productName;
    private String productWidth;
    private String packageCost;
    private List<TongTuGoodsImage> productImgList;
    private List<TongTuGoodsDetail> goodsDetail;

    private String product_id;
    private String packageLength;
    private String sku;
    private String labelName;
    private String productLength;
    private String developerName;
    private String supplierName;
    private String brandName;
    private String declareCnName;
    private String productHeight;
    private String productPackingName;
    private String productFeature;
    private String labelList;
    private String packageHeight;
    private String declareEnName;
    private String skuLabel;
    private String productCode;
    private String createdDate;
    private String inquirerName;
    private String purchaserId;
    private String hsCode;
    private String productPackingEnName;
    private String purchaseName;
    private String status;
}
