package com.newnary.gsp.center.tpsi.infra.repository;

import com.newnary.dao.base.converter.POConverterFactory;
import com.newnary.dao.base.helper.DaoHelper;
import com.newnary.dao.base.po.BaseQuery;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.repository.db.converter.ThirdPartySystemPOConverter;
import com.newnary.gsp.center.tpsi.infra.repository.db.dao.ThirdPartySystemDao;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartySystemPO;
import com.newnary.gsp.center.tpsi.infra.translator.PO2ModelCreatorTranslator;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/12/14 14:20
 */
@Component
public class ThirdPartySystemRepository implements IThirdPartySystemRepository {

    @Resource
    private ThirdPartySystemDao thirdPartySystemDao;

    @Override
    public void store(ThirdPartySystem thirdPartySystem) {
        ThirdPartySystemPO po = POConverterFactory.find(ThirdPartySystemPOConverter.class)
                .convert2PO(thirdPartySystem);

        if (po.id != null) {
            thirdPartySystemDao.update(po);
        } else {
            thirdPartySystemDao.insert(po);
        }
    }

    @Override
    public Optional<ThirdPartySystem> loadByBizId(String bizId) {
        if (StringUtils.isEmpty(bizId)) {
            return Optional.empty();
        }

        BaseQuery<ThirdPartySystemPO> query = new BaseQuery<>(new ThirdPartySystemPO());
        query.getData().setBizId(bizId);

        return loadByQuery(query);
    }

    @Override
    public Optional<ThirdPartySystem> loadSystemByBizIdAndName(String provider, String name) {
        if (StringUtils.isEmpty(provider) || StringUtils.isEmpty(name)) {
            return Optional.empty();
        }

        BaseQuery<ThirdPartySystemPO> query = new BaseQuery<>(new ThirdPartySystemPO());
        query.getData().setSystemProvider(provider);
        query.getData().setName(name);
        return loadByQuery(query);
    }

    @Override
    public Optional<ThirdPartySystem> loadBySystemId(String systemId) {
        if (StringUtils.isEmpty(systemId)) {
            return Optional.empty();
        }

        BaseQuery<ThirdPartySystemPO> query = new BaseQuery<>(new ThirdPartySystemPO());
        query.getData().setSystemId(systemId);

        return loadByQuery(query);
    }


    @Override
    public List<ThirdPartySystem> getSystemsByProvider(String provider) {
        BaseQuery<ThirdPartySystemPO> query = new BaseQuery<>(new ThirdPartySystemPO());
        query.getData().setSystemProvider(provider);
        List<ThirdPartySystemPO> result = thirdPartySystemDao.query(query);
        return result.stream()
                .map(po -> ThirdPartySystem.loadWith(PO2ModelCreatorTranslator.transThirdPartySystemCreator(po)))
                .collect(Collectors.toList());
    }

    private Optional<ThirdPartySystem> loadByQuery(BaseQuery<ThirdPartySystemPO> query) {
        List<ThirdPartySystemPO> pos = thirdPartySystemDao.query(query);
        if (CollectionUtils.isNotEmpty(pos)) {
            return pos.stream()
                    .map(po -> ThirdPartySystem.loadWith(PO2ModelCreatorTranslator.transThirdPartySystemCreator(po)))
                    .findAny();
        }
        return Optional.empty();
    }

    public static void main(String[] args) {
        DaoHelper.genXMLWithFeature(
                "/Users/<USER>/Documents/workspace/newnary/project/platform-service/tpsi-center/tpsi-center-infrastructure/src/main/java/com/newnary/gsp/center/tpsi/infra/repository/db/dao/ThirdPartySystemDao.xml",
                ThirdPartySystemDao.class,
                ThirdPartySystemPO.class,
                "third_party_system",
                false);
    }
}
