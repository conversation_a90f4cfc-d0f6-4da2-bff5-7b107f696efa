package com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.stock;

import lombok.Data;

@Data
public class MaBangDoSearchSkuList {

    /**
     * 库存SKU
     */
    private String stockSku;

    /**
     * 创建起始时间 (参数要有timeCreatedStar或者updateTimeStart,否则提示签名不正确)
     */
    private String timeCreatedStart;

    /**
     * 创建结束时间
     */
    private String timeCreatedEnd;

    /**
     * 更新起始时间 (参数要有timeCreatedStar或者updateTimeStart,否则提示签名不正确)
     */
    private String updateTimeStart;

    /**
     * 更新结束时间
     */
    private String updateTimeEnd;

    /**
     * 页数
     */
    private Integer page;

    /**
     * 当前每页条数，默认20，最大值为100
     */
    private Integer rowsPerPage;

    /**
     * 1是0否返回虚拟SKU,默认0不返回
     */
    private Integer showVirtualSku;

    /**
     * 1是0否返回默认供应商,默认0不返回
     */
    private Integer showProvider;

    /**
     * 1是0否返回仓库信息,默认0不返回
     */
    private Integer showWarehouse;

    /**
     * 1是0否返回自定义分类,默认0不返回
     */
    private Integer showLabel;

    /**
     * 1是0否返回多属性,默认0不返回
     */
    private Integer showattributes;
}
