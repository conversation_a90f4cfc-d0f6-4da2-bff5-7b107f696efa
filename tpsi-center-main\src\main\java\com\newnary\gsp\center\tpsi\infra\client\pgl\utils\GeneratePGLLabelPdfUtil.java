package com.newnary.gsp.center.tpsi.infra.client.pgl.utils;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.newnary.gsp.center.tpsi.infra.client.common.CreatePdfUtil;
import com.newnary.gsp.center.tpsi.infra.client.common.PDFDirection;
import com.newnary.gsp.center.tpsi.infra.client.pgl.dto.GeneratePGLPdfParam;
import com.newnary.gsp.center.tpsi.infra.client.pgl.dto.GoodsParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;

/**
 * @Author: jack
 * @CreateTime: 2024/1/18
 */
@Slf4j
public class GeneratePGLLabelPdfUtil {

    public static ByteArrayOutputStream generatePdf(GeneratePGLPdfParam pdfParam, String filePath) {
        Document document = null;
        PdfWriter writer = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            float mmPx = 2.83f;
            float partCmPx = 7.08f;
            float halfCmPx = 14.16f;

            document = new Document(new Rectangle(283f, 283f), partCmPx, partCmPx, partCmPx, partCmPx);

            BaseFont simsunFont = BaseFont.createFont(filePath + "msyh.ttc,0", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            BaseFont msyhFont = BaseFont.createFont(filePath + "simsun.ttc,0", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);

            //写pdf---start
            writer = PdfWriter.getInstance(document, byteArrayOutputStream);  // Do this BEFORE document.open()
            document.open();

            PdfPTable table = new PdfPTable(7);
            table.setSplitLate(false);
            table.setWidthPercentage(100);

            table.setWidths(new float[] {7.5f * mmPx, 7.5f * mmPx, 37.5f * mmPx, 12.5f * mmPx, 12.5f * mmPx, partCmPx, 15 * mmPx});

            Font msyh8Chine = new Font(msyhFont, 8);
            Font msyh10Chine = new Font(msyhFont, 10, Font.BOLD);
            Font simsun9Chine = new Font(simsunFont, 9);
            Font simsun10Chine = new Font(simsunFont, 10);
            Font simsun12Chine = new Font(simsunFont, 12, Font.BOLD, BaseColor.WHITE);
            Font simsum14Chine = new Font(simsunFont,14, Font.BOLD);

            String logoPath = filePath + "pgl_logo.jpg";

            String[] fixStr = {"FROM:", "Ship To:", "product code", "No of PCS", "COD", "No", "Qty", "Description of Contents", "Kg", "Val", "Total Gross Weight(Kg)"};

            CreatePdfUtil.addCell(table, fixStr[0] + pdfParam.getSenderName(), Element.ALIGN_LEFT, msyh8Chine, 3, 0, halfCmPx, PDFDirection.UP, null, PDFDirection.LEFT, PDFDirection.RIGHT);
            CreatePdfUtil.addImageCell(table, logoPath, 0, 4, 3, 20f * mmPx, null, Element.ALIGN_TOP);
            CreatePdfUtil.addCell(table, pdfParam.getSenderPhone(), Element.ALIGN_LEFT, msyh8Chine, 3, 0, halfCmPx, null, null, PDFDirection.LEFT, PDFDirection.RIGHT);
            CreatePdfUtil.addCell(table, pdfParam.getSenderAddress(), Element.ALIGN_LEFT, msyh8Chine, 3, 0, 15f * mmPx, null, PDFDirection.DOWN, PDFDirection.LEFT, PDFDirection.RIGHT);

            CreatePdfUtil.addCell(table, fixStr[1] + pdfParam.getReceiverName(), Element.ALIGN_LEFT, msyh8Chine, 3, 0, halfCmPx, null, null, PDFDirection.LEFT, PDFDirection.RIGHT);
            CreatePdfUtil.addCell(table, fixStr[2], Element.ALIGN_CENTER, simsun9Chine, 1, 2, 0, halfCmPx);
            CreatePdfUtil.addCell(table, fixStr[3], Element.ALIGN_CENTER, simsun9Chine, 1, 2, 0, halfCmPx);

            CreatePdfUtil.addCell(table, StringUtils.isNotEmpty(pdfParam.getReceiverPhone()) ? pdfParam.getReceiverPhone() : "/", Element.ALIGN_LEFT, msyh8Chine, 3, 0, halfCmPx, null, null, PDFDirection.LEFT, PDFDirection.RIGHT);
            CreatePdfUtil.addCell(table, pdfParam.getLogisticsCode(), Element.ALIGN_CENTER, Element.ALIGN_MIDDLE, simsum14Chine, 1, 2, 2, 2f * halfCmPx);
            CreatePdfUtil.addCell(table, pdfParam.getPcs(), Element.ALIGN_CENTER, Element.ALIGN_MIDDLE, simsum14Chine, 1, 2, 2, 2f * halfCmPx);

            CreatePdfUtil.addCell(table, pdfParam.getReceiverAddress() + "\n" + pdfParam.getReceiverPostcode(), Element.ALIGN_LEFT, msyh8Chine, 3, 2, 17.5f * mmPx, null, PDFDirection.DOWN, PDFDirection.LEFT, PDFDirection.RIGHT);
            CreatePdfUtil.addBlackCell(table, fixStr[4], Element.ALIGN_CENTER, Element.ALIGN_MIDDLE, simsun12Chine, 1, 1, 0, 12.5f * mmPx);
            if (pdfParam.getIsCod()) {
                CreatePdfUtil.addCell(table, pdfParam.getTransportPrice().setScale(2) + "PHP", Element.ALIGN_CENTER, Element.ALIGN_MIDDLE, msyh8Chine, 1, 3, 0, 12.5f * mmPx);
            } else {
                CreatePdfUtil.addCell(table, "0PHP", Element.ALIGN_CENTER, Element.ALIGN_MIDDLE, msyh8Chine, 1, 3, 0, 12.5f * mmPx);
            }

            CreatePdfUtil.addCell(table, fixStr[5], Element.ALIGN_LEFT, simsun10Chine, 1, 1, 0, halfCmPx);
            CreatePdfUtil.addCell(table, fixStr[6], Element.ALIGN_LEFT, simsun10Chine, 1, 1, 0, halfCmPx);
            CreatePdfUtil.addCell(table, fixStr[7], Element.ALIGN_LEFT, simsun10Chine, 1, 2, 0, halfCmPx);
            CreatePdfUtil.addCell(table, fixStr[8], Element.ALIGN_LEFT, simsun10Chine, 1, 2, 0, halfCmPx);
            CreatePdfUtil.addCell(table, fixStr[9], Element.ALIGN_LEFT, simsun10Chine, 1, 1, 0, halfCmPx);

            Integer totalQuantity = 0;
            BigDecimal totalWeight = BigDecimal.ZERO;
//            BigDecimal totalPrice = BigDecimal.ZERO;
            if (CollectionUtils.isEmpty(pdfParam.getGoodsList())) {
                for (int i = 1; i < 4; i++) {
                    CreatePdfUtil.addCell(table, String.valueOf(i), Element.ALIGN_LEFT, simsun10Chine, 1, 0, halfCmPx, PDFDirection.LEFT);
                    CreatePdfUtil.addCell(table, "", Element.ALIGN_LEFT, simsun10Chine, 1, 0, halfCmPx, PDFDirection.LEFT);
                    CreatePdfUtil.addCell(table, "", Element.ALIGN_LEFT, simsun10Chine, 2, 0, halfCmPx, PDFDirection.LEFT);
                    CreatePdfUtil.addCell(table, "", Element.ALIGN_LEFT, simsun10Chine, 2, 0, halfCmPx, PDFDirection.LEFT);
                    CreatePdfUtil.addCell(table, "", Element.ALIGN_LEFT, simsun10Chine, 1, 0, halfCmPx, null, null, PDFDirection.LEFT, PDFDirection.RIGHT);
                }
            } else {
                for (GoodsParam goodsParam : pdfParam.getGoodsList()) {
                    totalQuantity += goodsParam.getGoodsQuantity();
                    totalWeight = totalWeight.add(goodsParam.getWeightValue());
//                    totalPrice = totalPrice.add(goodsParam.getPriceValue());
                }
                for (int i = 0; i < 3; i++) {
                    if (i < pdfParam.getGoodsList().size()) {
                        GoodsParam goodsParam = pdfParam.getGoodsList().get(i);
                        CreatePdfUtil.addCell(table, String.valueOf(i + 1), Element.ALIGN_LEFT, simsun10Chine, 1, 0, halfCmPx, PDFDirection.LEFT);
                        CreatePdfUtil.addCell(table, String.valueOf(goodsParam.getGoodsQuantity()), Element.ALIGN_LEFT, simsun10Chine, 1, 0, halfCmPx, PDFDirection.LEFT);
                        CreatePdfUtil.addCell(table, goodsParam.getGoodsName(), Element.ALIGN_LEFT, simsun10Chine, 2, 0, halfCmPx, PDFDirection.LEFT);
                        CreatePdfUtil.addCell(table, goodsParam.getWeightValue().setScale(2).toString(), Element.ALIGN_LEFT, simsun10Chine, 2, 0, halfCmPx, PDFDirection.LEFT);
                        CreatePdfUtil.addCell(table, /*goodsParam.getPriceValue().setScale(2).toString()*/"", Element.ALIGN_LEFT, simsun10Chine, 1, 0, halfCmPx, null, null, PDFDirection.LEFT, PDFDirection.RIGHT);
                    } else {
                        CreatePdfUtil.addCell(table, String.valueOf(i + 1), Element.ALIGN_LEFT, simsun10Chine, 1, 0, halfCmPx, PDFDirection.LEFT);
                        CreatePdfUtil.addCell(table, "", Element.ALIGN_LEFT, simsun10Chine, 1, 0, halfCmPx, PDFDirection.LEFT);
                        CreatePdfUtil.addCell(table, "", Element.ALIGN_LEFT, simsun10Chine, 2, 0, halfCmPx, PDFDirection.LEFT);
                        CreatePdfUtil.addCell(table, "", Element.ALIGN_LEFT, simsun10Chine, 2, 0, halfCmPx, PDFDirection.LEFT);
                        CreatePdfUtil.addCell(table, "", Element.ALIGN_LEFT, simsun10Chine, 1, 0, halfCmPx, null, null, PDFDirection.LEFT, PDFDirection.RIGHT);
                    }
                }
            }
            CreatePdfUtil.addCell(table, "", Element.ALIGN_CENTER, simsun10Chine, 1, 0, halfCmPx, PDFDirection.UP, PDFDirection.DOWN, PDFDirection.LEFT, null);
            CreatePdfUtil.addCell(table, String.valueOf(totalQuantity), Element.ALIGN_LEFT, simsun10Chine, 1, 0, halfCmPx, PDFDirection.UP, PDFDirection.DOWN, null, null);
            CreatePdfUtil.addCell(table, fixStr[10], Element.ALIGN_LEFT, simsun10Chine, 1, 2, 0, halfCmPx);
            CreatePdfUtil.addCell(table, totalWeight.setScale(2).toString(), Element.ALIGN_LEFT, simsun10Chine, 1, 2, 0, halfCmPx);
            CreatePdfUtil.addCell(table, /*totalPrice.setScale(2).toString()*/"", Element.ALIGN_LEFT, simsun10Chine, 1, 1, 0, halfCmPx);

            BitMatrix bitMatrix = new MultiFormatWriter().encode(pdfParam.getOrderNum(), BarcodeFormat.CODE_128, 250, 28);
            CreatePdfUtil.addImageCell(table, MatrixToImageWriter.toBufferedImage(bitMatrix), 0, 7, 0, 12 * mmPx);
            CreatePdfUtil.addCell(table, pdfParam.getOrderNum(), Element.ALIGN_CENTER, Element.ALIGN_TOP, msyh10Chine, 0, 7, 0, halfCmPx);

            document.add(table);
            byteArrayOutputStream.close();

            document.close();

            return byteArrayOutputStream;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (document != null) {
                document.close();
            }
            if (writer != null) {
                writer.close();
            }
        }
        return new ByteArrayOutputStream();

    }

    public static void main(String[] args) {
        String resourcePath = Thread.currentThread().getContextClassLoader().getResource("").getPath();
        GeneratePGLPdfParam pdfParam = new GeneratePGLPdfParam();
        pdfParam.setIsCod(false);
        pdfParam.setOrderNum("TEST0001");
        ByteArrayOutputStream outputStream = generatePdf(pdfParam, resourcePath + "PGL" + File.separator);
        byte[] bytes = outputStream.toByteArray();
        String folderName = "D://Test";
        String fileName = "test" + System.currentTimeMillis() + ".pdf";
        File file = CreatePdfUtil.createFile(folderName, fileName);
        try {
            CreatePdfUtil.byteArrayToPDF(bytes, file);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
