package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp;

import java.util.List;

/**
 * 只记录常用参数
 */
public class EcCangERPGetOrderListRequest {

    public Integer page;

    public Integer pageSize;
    /**
     * 是否返回订单明细数据，1:返回，0：不返回 默认0
     */
    public Integer getDetail;
    /**
     * 是否返回订单地址数据，1:返回，0：不返回 默认0
     */
    public Integer getAddress;
    /**
     * 是否返回自定义订单类型，1:返回，0：不返回 默认0
     */
    public Integer getCustomOrderType;

    public Condition condition;

    public static class Condition {

        public List<String> refNos;

        public List<String> saleOrderCodes;

        public List<String> warehouseOrderCodes;
        public List<String> productSku;

        /**
         * 订单状态，0:已废弃,1:付款未完成,2:待发货审核,3:待发货,4:已发货,5:冻结中,6:缺货,7:问题件,8:未付款
         */
        private String status;



    }
}
