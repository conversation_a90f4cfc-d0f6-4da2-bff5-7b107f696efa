package com.newnary.gsp.center.tpsi.infra.client.ejingling.valobj.request;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class EJingLingIncreGoodsListReq {
    /**
     * 数量，最大500
     */
    @NotNull(message = "每页数量不能为空")
    private Integer size;

    /**
     * 页码，默认为1
     */
    @NotNull(message = "当前页数不能为空")
    private Integer page;

    /**
     * “2022-11-22 00:00:00”，最后更新时间
     */
    @NotNull(message = "最后更新时间不能为空")
    private String startLastUpdatedTime;

    /**
     * 截至最后更新时间，与开始时间跨度不超过一天
     */
    @NotNull(message = "截止到最后更新时间不能为空")
    private String endLastUpdatedTime;

    /**
     * 上一批中最大的一个id
     */
    private Long maxId;
}
