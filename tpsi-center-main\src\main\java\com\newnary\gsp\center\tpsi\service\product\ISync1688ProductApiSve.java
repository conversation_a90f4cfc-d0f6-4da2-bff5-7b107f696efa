package com.newnary.gsp.center.tpsi.service.product;

import com.newnary.gsp.center.tpsi.infra.model.CrawlerProduct;

import java.util.List;

/**
 * @Author: czh
 * @CreateTime: 2023-3-25
 */
public interface ISync1688ProductApiSve {
    void storeProductIds(List<String> productList, String keywords, int i, Integer pageSize);

    void updateProductState(String productId,Integer state,Integer flushState,Integer flushVersion);

    List<CrawlerProduct> loadCrawlerProductByCategoryList(List<String> categoryList, Integer limit, Integer crawlerReturnState,Integer state,Integer flushState);

    CrawlerProduct loadCrawlerDetailReturn(String productId);

}
