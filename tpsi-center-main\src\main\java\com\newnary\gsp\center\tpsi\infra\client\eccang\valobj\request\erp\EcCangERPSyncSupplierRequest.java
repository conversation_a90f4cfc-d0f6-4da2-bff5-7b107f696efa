package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Setter
@Getter
public class EcCangERPSyncSupplierRequest {

    /**
     * 操作类型：
     * ADD 新增、
     * EDIT 编辑
     */
    @NotBlank(message = "操作类型不能为空")
    private String actionType;

    /**
     * 供应商：
     * Request.supplier 可更新
     * 注意：actionType为EDIT时,本对象内的字段为非必传，supplierCode必传
     */
    @NotNull(message = "供应商信息必填")
    private Supplier supplier;

    private String supplierCode;

    /**
     * 支付账户
     * Request.paymentAccountList
     * （默认支付方式为‘在线’时为必需，不需要更新可不传，更新方式为覆盖）
     * 可更新
     */
    private List<PaymentAccount> paymentAccountList;

    /**
     * 联系方式：
     * Request.contactList
     *  （不需要更新可不传，更新方式为覆盖）
     * 可更新
     */
    private List<SupplierContact> contactList;

    /**
     * 附属图片URL
     * Request.supplierImagesList
     *  （不需要更新可不传，更新方式为覆盖）
     * 可更新
     */
    private List<SupplierImage> supplierImagesList;

    @Setter
    @Getter
    public static class Supplier {

        /**
         * 供应商代码：数字字母下划线和中横线最佳
         */
        @NotBlank(message = "供应商编码必填")
        private String supplierCode;

        /**
         * 名称CN
         * 可更新
         */
        @NotBlank(message = "供应商名称必填")
        private String supplierName;

        /**
         * 等级：
         * A、B、C、D
         * 可更新
         */
        @NotBlank(message = "供应商等级必填")
        private String level;

        /**
         * 组织机构ID
         */
        private String supplierOrganizationId;

        /**
         * 合作类型：
         * 0 正常、
         * 1 临时、
         * 2 备用
         * 可更新
         */
        @NotNull(message = "合作类型必填")
        private Integer supplierTeamworkType;

        /**
         * 供应商类型：
         * 1 零售、
         * 2 批发、
         * 3 生产商、
         * 4 通用虚拟、
         * 5 显示、
         * 6 市场
         * 可更新
         */
        @NotNull(message = "供应商类型必填")
        private Integer supplierType;

        /**
         * 支付周期类型：
         * 1 月结、
         * 2 隔月结、
         * 3 日结、
         * 4 周结、
         * 5 半月结
         * 可更新
         */
        @NotNull(message = "支付周期必填")
        private Integer pcId;

        /**
         * 结算方式：
         * 1 货到付款、
         * 2 款到发货、
         * 3 帐期
         * 可更新
         */
        @NotNull(message = "结算方式必填")
        private Integer accountType;

        /**
         * 默认支付方式：
         * 1 现金、
         * 2 在线、
         * 3 银行卡
         * 可更新
         */
        @NotNull(message = "支付方式必填")
        private Integer payType;

        /**
         * 运输承担方：
         * 1 供应商、
         * 2 采购方
         * 可更新
         */
        @NotNull(message = "运输承担方必填")
        private Integer supplierCarrier;

        /**
         * 运输支付方式：
         * 当运输承担方为 采购方 时，此项必填（1、到付。2、预付）。其他方式时此项不用填
         * 可更新
         */
        @NotNull(message = "运输支付方式必填")
        private Integer supplierShipPayType;

        /**
         * 默认运输方式：
         * 1 自提、
         * 2 快递、
         * 3 物流、
         * 4 送货
         * 可更新
         */
        @NotNull(message = "运输方式必填")
        private Integer shippingMethodIdHead;

        /**
         * QC不良品处理：
         * 1 退货、
         * 2 换货、
         * 3 采购方承担
         * 可更新
         */
        @NotNull(message = "QC不良品处理必填")
        private Integer supplierQcException;

        /**
         * 合同注意事项
         * 可更新
         */
        private String supplierTreaty;

        /**
         * 比例值：当 结算方式 为款到发货时，此项必填。其他方式时此项不用填
         * 可更新
         */
        private Float accountProportion;

        /**
         * 默认运输方式承运商Id：当‘默认运输方式’不为“自提”时，此项必填
         * 从接口 相关接口-&getAllShipper 获取
         * 可更新
         */
        private Integer psId;

        /**
         * 名称EN
         * 可更新
         */
        private String supplierNameEn;

        /**
         * 默认采购员：用户ID
         * 可更新
         */
        private Integer buyerId;

        /**
         * 默认跟单员：用户ID
         * 可更新
         */
        private Integer trackId;

        /**
         * 默认供应商开发者：用户ID
         * 可更新
         */
        private Integer supplierDevelopId;

        /**
         * 主营品类代码，一级品类代码
         * 可更新
         */
        private String supplierMainCategoryCode;

        /**
         * 状态：
         * 1 可用、
         * 0 作废
         * 可更新
         */
        private Integer supplierStatus;

        /**
         * 供应商佣金比例：例如0.03
         * 可更新
         */
        private Float supplierCommissionRatio;

        /**
         * 税率：例如0.15
         * 可更新
         */
        private Float supplierTaxRate;

        /**
         * 合同采购金额
         * 可更新
         */
        private Float purchaseContractAmount;

        /**
         * 店铺网址
         */
        private String supplierWebsite;

        /**
         * 是否网采供应商：
         * 0 否、
         * 1 是
         */
        private Integer supplierIsNet;

        /**
         * 备注
         */
        private String supplierNote;

        /**
         * 是否备注随供应商带出至采购备注
         * 0-不带出，1-带出
         */
        private Integer syncSupplierNote;

    }

    @Setter
    @Getter
    public static class PaymentAccount {

        /**
         * 支付方式 1:现金; 2:在线; 3:银行卡
         */
        @NotNull(message = "支付方式必填")
        private Integer pmId;

        /**
         * 支付平台（仅针对"在线"支付方式） 1:paypal; 2:财付通; 3:支付宝; 4:块钱; 5:网银; 6:微信; 7:诚e赊
         */
        @NotNull(message = "支付平台必填")
        private Integer platformType;

        /**
         * 银行名称 （仅针对"银行卡"支付方式）
         */
        @NotBlank(message = "银行名称必填")
        private String platformName;

        /**
         * 账户
         */
        @NotBlank(message = "账户必填")
        private String pmAccount;

        /**
         * 开户人
         */
        @NotBlank(message = "开户人必填")
        private String pmName;

        /**
         * 收款人
         */
        private String pmLead;

        /**
         * 收款公司
         */
        private String pmCompany;

        /**
         * 状态：0:停用、1:可用 ，默认：可用
         */
        private Integer pmStatus;

    }

    @Setter
    @Getter
    public static class SupplierContact {

        /**
         * 联系人名称
         */
        @NotBlank(message = "联系人姓名必填")
        private String contactName;

        /**
         * 联系人电话
         */
        @NotBlank(message = "联系人电话必填")
        private String contactTel;

        /**
         * 联系人ID，主键ID
         */
        private Integer contactId;

        /**
         * Fax
         */
        private String contactFax;

        /**
         * 中文联系地址
         */
        private String contactAddress;

        /**
         * 英文联系地址
         */
        private String contactAddressEn;

        /**
         * 联系邮编
         */
        private String contactPostCode;

        /**
         * QQ
         */
        private String contactQQ;

        /**
         * 微信号
         */
        private String contactWechat;

        /**
         * 旺旺号
         */
        private String contactWangwang;

        /**
         * Skype
         */
        private String contactSkype;

    }

    @Setter
    @Getter
    public static class SupplierImage {

        /**
         * 图片URL
         */
        @NotBlank(message = "图片URL必填")
        private String url;
        
    }

}
