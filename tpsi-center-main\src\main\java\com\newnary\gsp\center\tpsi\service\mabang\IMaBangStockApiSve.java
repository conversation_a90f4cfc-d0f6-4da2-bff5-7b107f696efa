package com.newnary.gsp.center.tpsi.service.mabang;

import com.newnary.gsp.center.tpsi.api.mabang.request.SyncProductFromMaBangCommand;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.MaBangStockQauntityList;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.MaBangStockSku;

public interface IMaBangStockApiSve {

    /**
     * 马帮查询库存商品列表-用于从通途同步库存过去马帮
     *
     * @param thirdPartySystemId
     * @param messageTag
     * @param context
     * @param tongTuThirdPartySystemId
     */
    void stockDoSearchSkuListForSyncStock(String thirdPartySystemId, String messageTag, String context, String tongTuThirdPartySystemId);

    /**
     * 获取马帮库存sku在warehouse下的库存数量
     *
     * @param thirdPartySystemId
     * @param stockSku
     * @param warehouse
     * @return
     */
    Integer stockGetStockQuantity(String thirdPartySystemId, String stockSku, String warehouse);

    /**
     * 获取马帮库存sku的仓库库存列表
     *
     * @param thirdPartySystemId
     * @param stockSku
     * @return
     */
    MaBangStockQauntityList stockGetStockQuantity(String thirdPartySystemId, String stockSku);

    /**
     * 商品同步-根据sku获取马帮库存sku
     * 返回马帮库存sku
     *
     * @param thirdPartySystemId
     * @param stockSku
     * @return
     */
    MaBangStockSku stockDoSearchSkuList(String thirdPartySystemId, String stockSku);

    /**
     * 商品同步-同步马帮主sku
     *
     * @param req
     */
    void syncSalesSku(SyncProductFromMaBangCommand req);

    /**
     * 商品同步-同步马帮库存sku
     *
     * @param req
     */
    void syncStockSku(SyncProductFromMaBangCommand req);

}
