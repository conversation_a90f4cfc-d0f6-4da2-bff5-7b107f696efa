package com.newnary.gsp.center.tpsi.ctrl.haiying;

import com.newnary.api.base.common.PageList;
import com.newnary.api.base.common.PageMeta;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingStation;
import com.newnary.gsp.center.tpsi.api.haiying.response.shopee.*;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.shopee.*;
import com.newnary.gsp.center.tpsi.infra.mapper.HaiYingShopeeDataMapper;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/07/14 13:45
 */
public class HaiYingShopeeResponse2DTOTranslator {

    public static PageList<HaiYingShopeeKeywordInfoDTO> transShopeeKeywordInfoList(HaiYingStation station, List<HaiYingShopeeKeywordInfoResponse> response, PageMeta pageMeta) {
        PageList<HaiYingShopeeKeywordInfoDTO> ret = new PageList<>();
        List<HaiYingShopeeKeywordInfoDTO> list = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingShopeeKeywordInfoDTO dto = HaiYingShopeeDataMapper.INSTANCE.transShopeeKeywordInfoDTO(resp);
            switch (station) {
                case SHOPEE_MY:
                    dto.setCurrency("MYR");
                    break;
                case SHOPEE_ID:
                    dto.setCurrency("IDR");
                    break;
                case SHOPEE_PH:
                    dto.setCurrency("PHP");
                    break;
                case SHOPEE_TH:
                    dto.setCurrency("THB");
                    break;
                case SHOPEE_BR:
                    dto.setCurrency("BRL");
                    break;
                case SHOPEE_CL:
                    dto.setCurrency("CLP");
                    break;
                case SHOPEE_CO:
                    dto.setCurrency("COP");
                    break;
                case SHOPEE_MX:
                    dto.setCurrency("MXN");
                    break;
                case SHOPEE_PL:
                    dto.setCurrency("PLN");
                    break;
                case SHOPEE_SG:
                    dto.setCurrency("SGD");
                    break;
                case SHOPEE_VN:
                    dto.setCurrency("VND");
                    break;
                case SHOPEE_Taiwan_CHN:
                    dto.setCurrency("TWD");
                    break;
                default:
                    break;
            }
            list.add(dto);
        });
        ret.setItems(list);
        ret.setPageMeta(pageMeta);
        return ret;
    }

    public static PageList<HaiYingShopeeProductListDTO> transShopeeProductListList(HaiYingStation station, List<HaiYingShopeeProductListResponse> response, PageMeta pageMeta) {
        PageList<HaiYingShopeeProductListDTO> ret = new PageList<>();
        List<HaiYingShopeeProductListDTO> list = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingShopeeProductListDTO dto = HaiYingShopeeDataMapper.INSTANCE.transShopeeProductListDTO(resp);
            switch (station) {
                case SHOPEE_MY:
                    dto.setCurrency("MYR");
                    break;
                case SHOPEE_ID:
                    dto.setCurrency("IDR");
                    break;
                case SHOPEE_PH:
                    dto.setCurrency("PHP");
                    break;
                case SHOPEE_TH:
                    dto.setCurrency("THB");
                    break;
                case SHOPEE_BR:
                    dto.setCurrency("BRL");
                    break;
                case SHOPEE_CL:
                    dto.setCurrency("CLP");
                    break;
                case SHOPEE_CO:
                    dto.setCurrency("COP");
                    break;
                case SHOPEE_MX:
                    dto.setCurrency("MXN");
                    break;
                case SHOPEE_PL:
                    dto.setCurrency("PLN");
                    break;
                case SHOPEE_SG:
                    dto.setCurrency("SGD");
                    break;
                case SHOPEE_VN:
                    dto.setCurrency("VND");
                    break;
                case SHOPEE_Taiwan_CHN:
                    dto.setCurrency("TWD");
                    break;
                default:
                    break;
            }
            list.add(dto);
        });
        ret.setItems(list);
        ret.setPageMeta(pageMeta);
        return ret;
    }

    public static List<HaiYingShopeeProductDetailInfoDTO> transShopeeProductDetailInfoList(List<HaiYingShopeeProductDetailInfoResponse> response) {
        List<HaiYingShopeeProductDetailInfoDTO> ret = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingShopeeProductDetailInfoDTO dto = HaiYingShopeeDataMapper.INSTANCE.transShopeeProductDetailInfoDTO(resp);

            ret.add(dto);
        });
        return ret;
    }

    public static List<HaiYingShopeeProductExtInfoDTO> transShopeeProductExtInfoList(List<HaiYingShopeeProductExtInfoResponse> response) {
        List<HaiYingShopeeProductExtInfoDTO> ret = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingShopeeProductExtInfoDTO dto = HaiYingShopeeDataMapper.INSTANCE.transShopeeProductExtInfoDTO(resp);

            ret.add(dto);
        });
        return ret;
    }

    public static List<HaiYingShopeeProductHistoryInfoDTO> transShopeeProductHistoryInfoList(List<HaiYingShopeeProductHistoryInfoResponse> response) {
        List<HaiYingShopeeProductHistoryInfoDTO> ret = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingShopeeProductHistoryInfoDTO dto = HaiYingShopeeDataMapper.INSTANCE.transShopeeProductHistoryInfoDTO(resp);

            ret.add(dto);
        });
        return ret;
    }

    public static List<HaiYingShopeeCategoryTreeDTO> transShopeeCategoryTreeList(List<HaiYingShopeeCategoryTreeResponse> response) {
        List<HaiYingShopeeCategoryTreeDTO> ret = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingShopeeCategoryTreeDTO dto = HaiYingShopeeDataMapper.INSTANCE.transShopeeCategoryTreeDTO(resp);

            ret.add(dto);
        });
        return ret;
    }

    public static List<HaiYingShopeeTopCategoryInfoDTO> transShopeeTopCategoryInfoList(List<HaiYingShopeeTopCategoryInfoResponse> response) {
        List<HaiYingShopeeTopCategoryInfoDTO> ret = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingShopeeTopCategoryInfoDTO dto = HaiYingShopeeDataMapper.INSTANCE.transShopeeTopCategoryInfoDTO(resp);

            ret.add(dto);
        });
        return ret;
    }

    public static PageList<HaiYingShopeeSubCategoryInfoDTO> transShopeeSubCategoryInfoList(List<HaiYingShopeeSubCategoryInfoResponse> response, PageMeta pageMeta) {
        PageList<HaiYingShopeeSubCategoryInfoDTO> ret = new PageList<>();
        List<HaiYingShopeeSubCategoryInfoDTO> list = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingShopeeSubCategoryInfoDTO dto = HaiYingShopeeDataMapper.INSTANCE.transShopeeSubCategoryInfoDTO(resp);

            list.add(dto);
        });
        ret.setItems(list);
        ret.setPageMeta(pageMeta);
        return ret;
    }

}
