package com.newnary.gsp.center.tpsi.infra.mapper;

import com.newnary.gsp.center.tpsi.infra.model.creator.ThirdPartySystemCreator;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartySystemPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @Date 2021/12/14 14:55
 */
@Mapper
public interface ThirdPartySystemMapper {

    ThirdPartySystemMapper INSTANCE = Mappers.getMapper(ThirdPartySystemMapper.class);

    @Mappings({
            @Mapping(source = "systemStatus", target = "status"),
            @Mapping(source = "systemProvider", target = "provider")
    })
    ThirdPartySystemCreator po2ModelCreator(ThirdPartySystemPO po);
}
