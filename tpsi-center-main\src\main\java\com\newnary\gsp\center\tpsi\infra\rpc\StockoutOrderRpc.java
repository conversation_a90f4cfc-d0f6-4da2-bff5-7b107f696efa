package com.newnary.gsp.center.tpsi.infra.rpc;

import com.newnary.gsp.center.logistics.api.delivery.feign.StockoutOrderFeignApi;
import com.newnary.gsp.center.logistics.api.delivery.request.*;
import com.newnary.gsp.center.logistics.api.delivery.response.StockoutOrderInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/1
 */
@Component
public class StockoutOrderRpc {

    @Resource
    private StockoutOrderFeignApi stockoutOrderFeignApi;

    public List<StockoutOrderInfo> getListWithDelivery(StockoutOrderWithDeliveryQueryCommand command) {
        return stockoutOrderFeignApi.getListWithDelivery(command).mustSuccessOrThrowOriginal();
    }

    public StockoutOrderInfo getStockoutOrder(String stockoutOrderId) {
        StockoutOrderIdCommand command = new StockoutOrderIdCommand();
        command.setStockoutOrderId(stockoutOrderId);
        return stockoutOrderFeignApi.getInfo(command).mustSuccessOrThrowOriginal();
    }

    /**
     * 出库单受理
     * @param stockoutOrderId
     */
    public void accept(String stockoutOrderId) {
        StockoutOrderOperateCommand stockoutOrderAcceptCommand = new StockoutOrderOperateCommand();
        stockoutOrderAcceptCommand.setStockoutOrderId(stockoutOrderId);
        stockoutOrderFeignApi.accept(stockoutOrderAcceptCommand).mustSuccessOrThrowOriginal();
    }

    /**
     * 出库单已推送
     * @param command
     */
    public void thirdPushed(StockoutOrderThirdPushedCommand command) {
        stockoutOrderFeignApi.thirdPushed(command).mustSuccessOrThrowOriginal();
    }

    /**
     * 更新物流跟踪信息(首次调用会变更出库单为已发货状态)
     * @param command
     */
    public void updateTrack(StockoutOrderUpdateTrackCommand command) {
        stockoutOrderFeignApi.updateTrack(command).mustSuccessOrThrowOriginal();
    }

    /**
     * 追加物流跟踪信息
     * @param command
     */
    public void addTrack(StockoutOrderUpdateTrackCommand command) {
        stockoutOrderFeignApi.addTrack(command).mustSuccessOrThrowOriginal();
    }

    /**
     * 完成出库单
     * @param stockoutOrderId
     */
    public void deliver(String stockoutOrderId) {
        StockoutOrderOperateCommand stockoutOrderDeliverCommand = new StockoutOrderOperateCommand();
        stockoutOrderDeliverCommand.setStockoutOrderId(stockoutOrderId);
        stockoutOrderFeignApi.deliver(stockoutOrderDeliverCommand).mustSuccessOrThrowOriginal();
    }

    public void doCancel(StockoutOrderDoCancelCommand command) {
        stockoutOrderFeignApi.doCancel(command).mustSuccessOrThrowOriginal();
    }

    public void confirmCancel(StockoutOrderConfirmCancelCommand command) {
        stockoutOrderFeignApi.confirmCancel(command).mustSuccessOrThrowOriginal();
    }

    public void rejectCancel(StockoutOrderRejectCancelCommand command) {
        stockoutOrderFeignApi.rejectCancel(command).mustSuccessOrThrowOriginal();
    }

}
