package com.newnary.gsp.center.tpsi.api.haiying.request.shopee;

import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingStation;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingShopeeCategoryTreeCommand {

    /**
     * 站点(默认:马来西亚站)
     */
    @NotNull(message = "站点不能为空")
    private HaiYingStation station;

    /**
     * 类目等级
     */
    private Integer level;

    /**
     * 类目id
     */
    private String cid;

    /**
     * 父类目id
     */
    private String p_l1_id;
    private String p_l2_id;
    private String p_l3_id;


}
