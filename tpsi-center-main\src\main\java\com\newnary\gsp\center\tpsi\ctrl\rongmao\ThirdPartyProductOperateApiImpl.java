package com.newnary.gsp.center.tpsi.ctrl.rongmao;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.tpsi.api.rongmao.ThirdPartyProductOperateApi;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.service.rongmao.IProductOperateService;
import com.newnary.gsp.center.tpsi.service.rongmao.ThirdPartyServiceSelector;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@Slf4j
public class ThirdPartyProductOperateApiImpl implements ThirdPartyProductOperateApi {

    @Resource
    private ThirdPartyServiceSelector thirdPartyServiceSelector;

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;


    @Override
    public CommonResponse<String> create(String thirdPartySystemId, String productId) {
        ThirdPartySystem loadSystem = loadSystem(thirdPartySystemId);
        IProductOperateService service = thirdPartyServiceSelector.select(loadSystem.getBizId());
        return service.create(loadSystem, productId);
    }

    @Override
    public CommonResponse<String> delete(String thirdPartySystemId, String productId) {
        ThirdPartySystem loadSystem = loadSystem(thirdPartySystemId);
        IProductOperateService service = thirdPartyServiceSelector.select(loadSystem.getBizId());
        return service.delete(loadSystem,productId);
    }

    @Override
    public CommonResponse<String> update(String thirdPartySystemId, String productId) {
        ThirdPartySystem loadSystem = loadSystem(thirdPartySystemId);
        IProductOperateService service = thirdPartyServiceSelector.select(loadSystem.getBizId());
        return service.update(loadSystem,productId);
    }

    public ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }
}
