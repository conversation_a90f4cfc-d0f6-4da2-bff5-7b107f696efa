package com.newnary.gsp.center.tpsi.vvic;

import com.alibaba.fastjson.JSON;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierSpuOptSupplyStateReq;
import com.newnary.gsp.center.product.api.product.request.SupplierSpuCreateV2Command;
import com.newnary.gsp.center.tpsi.app.job.VVICJobManager;
import com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.request.*;
import com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.response.*;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingInfo;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.infra.rpc.OpenSupplierProductRpc;
import com.newnary.gsp.center.tpsi.service.vvic.VVICApiSev;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.test.starter.BaseTestInjectTenant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class VVICApiSevTest extends BaseTestInjectTenant {
    @Override
    protected String tenantId() {
        return "TENANT3924117688555169382400";
    }

    @Resource
    private VVICApiSev vvicApiSevImpl;

    @Resource
    private VVICJobManager vvicJobManager;

    //获取商品列表
    @Test
    public void testGetItemList() {
        VVICGetItemListReq vvicGetItemListReq = new VVICGetItemListReq();
        vvicGetItemListReq.setPage(1);
        vvicGetItemListReq.setLang("en");
        vvicGetItemListReq.setCity_market_code("gz,pn");
        vvicGetItemListReq.setUp_time_end("2023-03-01 12:15:00");
        vvicGetItemListReq.setUp_time_start("2023-03-01 12:00:00");
        VVICGetItemListResponse list = vvicApiSevImpl.getItemList("TESTVVIC0001", vvicGetItemListReq);
/*        long starTimeStamp = DateUtils.DateString2TimeStamp("2023-03-01 12:00:00");
        long a = 30*60*1000;
        int h = 23*24*2;
        int count = 0;
        vvicGetItemListReq.setPage(1);
        vvicGetItemListReq.setLang("cn");
        vvicGetItemListReq.setCity_market_code("gz,pn");
        VVICGetItemListResponse itemList = new VVICGetItemListResponse();
        while (count < h) {
            count++;
            vvicGetItemListReq.setUp_time_start(DateUtils.timeStamp2DateString("yyyy-MM-dd HH:mm:ss",starTimeStamp));
            vvicGetItemListReq.setUp_time_end(DateUtils.timeStamp2DateString("yyyy-MM-dd HH:mm:ss",starTimeStamp+=a));
            VVICGetItemListResponse list = vvicApiSevImpl.getItemList("TESTVVIC0001", vvicGetItemListReq);
            //System.out.println(list.toString());
            if (ObjectUtils.isNotEmpty(list) && list.getTotal_page() > 0) {
                itemList=list;
                for (int i = list.getPage()+1;i < list.getTotal_page();i++){
                    vvicGetItemListReq.setPage(i);
                    itemList.getItem_list().addAll(vvicApiSevImpl.getItemList("TESTVVIC0001", vvicGetItemListReq).getItem_list());
                }
            }
        }
         System.out.println(itemList);*/

    }

    //获取商品详情
    @Test
    public void testGetItemDetail() {
        VVICGetItemDetialReq vvicGetItemDetialReq = new VVICGetItemDetialReq();
        vvicGetItemDetialReq.setItem_vid("6442594541fa9a000813d29d,6442595e41fa9a000813d2a3,6442597841fa9a000813d2ac,644259782f9995000878a799,6442598641fa9a000813d2b2,644259912f9995000878a7ad,644259252f9995000878a788,644259a42f9995000878a7b7,644259b4c6dec700088596dc,644259b82f9995000878a7d3,644259bb2f9995000878a7d8,644259ca41fa9a000813d2c3,644259d741fa9a000813d2ca,644259e1c6dec700088596e1,64425a0341fa9a000813d2d8,64425a142f9995000878a819,64425a2241fa9a000813d30d,64425a2f41fa9a000813d312,64425a3241fa9a000813d318,64425a3c41fa9a000813d31f");
        vvicGetItemDetialReq.setLang("en");
        VVICGetItemDetialResponse itemList = vvicApiSevImpl.getItemDetial("TESTVVIC0001", vvicGetItemDetialReq);
        System.out.println(JSON.toJSONString(itemList.getItem_list()));
    }

    //获取商品id
    @Test
    public void testGetItemStatus() {
        VVICGetItemStatusReq vvicGetItemStatusReq = new VVICGetItemStatusReq();
        vvicGetItemStatusReq.setItem_vid("63f23971e308260008ede6fd");
        vvicGetItemStatusReq.setLang("en");
        VVICGetItemStatusResponse itemList = vvicApiSevImpl.getItemStatus("TESTVVIC0001", vvicGetItemStatusReq);
        System.out.println();
    }

    //创建订单
    @Test
    public void testCreateOrder() {
        VVICCreateOrderReq req = new VVICCreateOrderReq();
        req.setUser_name("newnary");
        req.setUser_mobile("13222222222");
        req.setShipper_name("张三");
        req.setShipper_mobile("15219796900");
        req.setConsignee("李四");
        req.setCountry("中国");
        req.setProvince("广东省");
        req.setCity("广州市");
        req.setArea("白云区");
        req.setCountry_id(1);
        req.setProvince_id(16210);
        req.setCity_id(16573);
        req.setAddress("菜鸟驿站 涌南大街店");
        req.setMobile("19924341694");
        req.setExpress_id(6L);
        req.setExpress_name("圆通快递-淘宝");
        req.setExpress_type(1);
        req.setDelivery_time("2023-03-31");
        req.setOut_order_no("20230330135855");

        List<VVICCreateOrderReq.OrderDetail> list = new ArrayList<>();
        VVICCreateOrderReq.OrderDetail orderDetail = new VVICCreateOrderReq.OrderDetail();
        list.add(orderDetail);
        req.setOrder_details(list);
        orderDetail.setItem_num(10);
        orderDetail.setSku_price("40.0");
        orderDetail.setSku_vid("63fed13dc718c6000880a907");
        buildVas(req);
        VVICCreateOrderResponse response = vvicApiSevImpl.createOrder("TESTVVIC0001", req);
        System.out.println();

    }

    public void buildVas(VVICCreateOrderReq req) {
        VVICGetVasReq vvicGetVasReq = new VVICGetVasReq();
        vvicGetVasReq.setMarket_code("gz");
        VVICGetVasReq.Item item = new VVICGetVasReq.Item();
        List<VVICGetVasReq.Item> list = new ArrayList<>();
        item.setItem_vid("63fed13dc718c6000880a905");
        item.setQuantity(10);
        list.add(item);
        vvicGetVasReq.setItems(list);
        VVICGetVasResponse response = vvicApiSevImpl.getVas("TESTVVIC0001", vvicGetVasReq);

        List<VVICCreateOrderReq.VasEntry> vasEntryList = new ArrayList<>();

        response.getVas_list().forEach(vas -> {
            VVICCreateOrderReq.VasEntry vasEntry = new VVICCreateOrderReq.VasEntry();
            vasEntry.setQuantity(1);
            vasEntry.setVas_id(Long.valueOf(vas.getVas_entry_list().get(0).getVas_id()));
            vasEntry.setVas_name(vas.getVas_entry_list().get(0).getVas_name());
            vasEntry.setType(vas.getType());
            vasEntryList.add(vasEntry);
        });
        req.setVas_entries(vasEntryList);
    }

    //取消订单
    @Test
    public void testCancelOrder() {
        VVICCancelOrderReq req = new VVICCancelOrderReq();
        req.setOrder_no("810035524996466027");
        req.setCancel_reason("不想要了");
        req.setRemark("不想要了");
        VVICCancelOrderResponse response = vvicApiSevImpl.cancelOrder("TESTVVIC0001", req);
        System.out.println();
    }

    //获取订单状态
    @Test
    public void testGetOrderStatus() {
        VVICGetOrderStatusReq req = new VVICGetOrderStatusReq();
        req.setOrder_no("810019493933964927");
        VVICGetOrderStatusResponse response = vvicApiSevImpl.getOrderStatus("TESTVVIC0001", req);
        System.out.println();
    }

    //获取订单列表
    @Test
    public void testGetOrderList() {
        VVICGetOrderListReq req = new VVICGetOrderListReq();
        req.setOrder_no("810019493933964927");
        VVICGetOrderListResponse response = vvicApiSevImpl.getOrderList("TESTVVIC0001", req);
        System.out.println();
    }

    //获取代发服务
    @Test
    public void testGetVas() {
        VVICGetVasReq req = new VVICGetVasReq();
        req.setMarket_code("gz");
        VVICGetVasReq.Item item = new VVICGetVasReq.Item();
        List<VVICGetVasReq.Item> list = new ArrayList<>();
        item.setItem_vid("63fed13dc718c6000880a905");
        item.setQuantity(10);
        list.add(item);
        req.setItems(list);
        VVICGetVasResponse response = vvicApiSevImpl.getVas("TESTVVIC0001", req);
        System.out.println();
    }

    //快递清单查询
    @Test
    public void testGetExpressList() {
        VVICGetExpressReq vvicGetExpressReq = new VVICGetExpressReq();
        vvicGetExpressReq.setCountry("中国");
        vvicGetExpressReq.setProvince("广东省");
        vvicGetExpressReq.setCity("广州市");
        vvicGetExpressReq.setAddress("菜鸟驿站 涌南大街店");
        vvicGetExpressReq.setArea("白云区");
        vvicGetExpressReq.setMarket_code("gz");
        VVICGetExpressReq.Good good = new VVICGetExpressReq.Good();
        List<VVICGetExpressReq.Good> list = new ArrayList<>();
        vvicGetExpressReq.setGoods(list);
        good.setItem_vid("63fed13dc718c6000880a905");
        good.setQuantity(10);
        good.setSku_vid("63fed13dc718c6000880a907");
        good.setPrice("40.0");
        list.add(good);
        VVICGetExpressResponse result = vvicApiSevImpl.getExpressList("TESTVVIC0001", vvicGetExpressReq);
        System.out.println(result);
    }


    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;
    @Resource
    private OpenSupplierProductRpc openSupplierProductRpc;

    @Test
    public void testCreateProductJob() {
        ReturnT<String> result = vvicJobManager.createVVICProduct("{\"thirdPartySystemId\":\"TESTVVIC0001\",\"time\":\"20\"}");
/*        List<VVICGetItemDetialResponse.Item> items = JSON.parseArray("[{\"art_no\":\"现货6075\",\"attr_str\":\"颜色:黑色短款 黑色长款,尺码:S M L XL,材质成分:聚对苯二甲酸乙二酯(涤纶)100%,风格:甜美,甜美:瑞丽,裙长:中裙,廓形:X型,袖长:长袖,领型:荷叶领,袖型:喇叭袖,面料:雪纺,裙型:荷叶边裙,图案:字母/数字/文字,流行元素/工艺:印花,衣门襟:套头,货号:现货6075,年份季节:2023年春季\",\"category_name_one\":\"女装\",\"category_name_sub\":\"裙装\",\"category_name_two\":\"连衣裙\",\"create_time\":\"2023-03-01 12:14:53\",\"desc\":\"…..\",\"expired_time\":\"\",\"item_id\":36888671,\"item_title\":\"敏敏仲夏夜黑色长袖连衣裙女春季新款显瘦收腰遮肚裙子\",\"item_vid\":\"63fed13dc718c6000880a905\",\"item_view_image\":\"//img1.vvic.com/upload/1677643840929_835974.jpeg\",\"list_grid_image\":\"//img1.vvic.com/upload/1677643840929_835974.jpeg,//img1.vvic.com/upload/1677643851668_625595.jpeg,//img1.vvic.com/upload/1677643851753_499915.jpeg,//img1.vvic.com/upload/1677643851783_437862.jpeg\",\"market_code\":\"gz\",\"price\":40.0,\"shop\":{\"ratio_of_delivery_on_time\":0,\"ratio_of_return_success\":0},\"shop_name\":\"KERER珂瑞尔\",\"shop_vid\":\"5bff54c63acc5a72644141ea\",\"skuList\":[{\"arrive_time\":0,\"color\":\"黑色短款\",\"color_id\":\"1627207:9999836\",\"color_img\":\"\",\"is_lack\":0,\"price\":40.0,\"size\":\"S\",\"size_id\":\"20509:224\",\"sku_id\":\";20509:224;1627207:9999836;\",\"sku_vid\":\"63fed13dc718c6000880a906\",\"status\":1},{\"arrive_time\":0,\"color\":\"黑色短款\",\"color_id\":\"1627207:9999836\",\"color_img\":\"\",\"is_lack\":0,\"price\":40.0,\"size\":\"M\",\"size_id\":\"20509:225\",\"sku_id\":\";20509:225;1627207:9999836;\",\"sku_vid\":\"63fed13dc718c6000880a907\",\"status\":1},{\"arrive_time\":0,\"color\":\"黑色短款\",\"color_id\":\"1627207:9999836\",\"color_img\":\"\",\"is_lack\":0,\"price\":40.0,\"size\":\"L\",\"size_id\":\"20509:226\",\"sku_id\":\";20509:226;1627207:9999836;\",\"sku_vid\":\"63fed13dc718c6000880a908\",\"status\":1},{\"arrive_time\":0,\"color\":\"黑色短款\",\"color_id\":\"1627207:9999836\",\"color_img\":\"\",\"is_lack\":0,\"price\":40.0,\"size\":\"XL\",\"size_id\":\"20509:156\",\"sku_id\":\";20509:156;1627207:9999836;\",\"sku_vid\":\"63fed13dc718c6000880a909\",\"status\":1},{\"arrive_time\":0,\"color\":\"黑色长款\",\"color_id\":\"1627207:9999888\",\"color_img\":\"\",\"is_lack\":0,\"price\":40.0,\"size\":\"S\",\"size_id\":\"20509:224\",\"sku_id\":\";20509:224;1627207:9999888;\",\"sku_vid\":\"63fed13dc718c6000880a90a\",\"status\":1},{\"arrive_time\":0,\"color\":\"黑色长款\",\"color_id\":\"1627207:9999888\",\"color_img\":\"\",\"is_lack\":0,\"price\":40.0,\"size\":\"M\",\"size_id\":\"20509:225\",\"sku_id\":\";20509:225;1627207:9999888;\",\"sku_vid\":\"63fed13dc718c6000880a90b\",\"status\":1},{\"arrive_time\":0,\"color\":\"黑色长款\",\"color_id\":\"1627207:9999888\",\"color_img\":\"\",\"is_lack\":0,\"price\":40.0,\"size\":\"L\",\"size_id\":\"20509:226\",\"sku_id\":\";20509:226;1627207:9999888;\",\"sku_vid\":\"63fed13dc718c6000880a90c\",\"status\":1},{\"arrive_time\":0,\"color\":\"黑色长款\",\"color_id\":\"1627207:9999888\",\"color_img\":\"\",\"is_lack\":0,\"price\":40.0,\"size\":\"XL\",\"size_id\":\"20509:156\",\"sku_id\":\";20509:156;1627207:9999888;\",\"sku_vid\":\"63fed13dc718c6000880a90d\",\"status\":1}],\"status\":1,\"supply_level\":5,\"up_time\":\"2023-03-01 12:14:53\",\"update_time\":\"2023-03-22 01:10:34\",\"weight_type\":2}]", VVICGetItemDetialResponse.Item.class);
        //Map<String, ThirdPartyMappingInfo> categoryMapping = thirdPartyMappingManager.batchGetIdMappingInfoByTargetCategoryPath("GSP", "VVIC", items.stream().map(item -> item.getCategory_name_one().concat("/").concat(item.getCategory_name_sub().concat("/").concat(item.getCategory_name_two()))).collect(Collectors.toList()), ThirdPartyMappingType.CATEGORY);
        ThirdPartySystem thirdPartySystem = loadSystem("TESTVVIC0001");
        SupplierSpuCreateV2Command supplierSpuCreateV2Command = vvicJobManager.buildCreateSpuReq(thirdPartySystem, null, items.get(0));
        String spu = openSupplierProductRpc.createSpu(supplierSpuCreateV2Command);
        System.out.println(spu);*/
    }

    @Test
    public void testUpdateProductStock() {
        ReturnT<String> stringReturnT = vvicJobManager.updateVVICProductStock("{\"thirdPartySystemId\":\"TESTVVIC0001\",\"stock\":\"30\"}");
    }

    @Test
    public void testUpdateProductPrice() {
        ReturnT<String> stringReturnT = vvicJobManager.updateVVICProductPrice("{\"thirdPartySystemId\":\"TESTVVIC0001\"}");
    }

    @Test
    public void testUpdateProductInfo() {
        ReturnT<String> stringReturnT = vvicJobManager.updateVVICProductInfo("{\"thirdPartySystemId\":\"TESTVVIC0001\",\"gmtCreateLess\":\"2023-04-23 10:32:45\"}");
    }

    public ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }

    //测试商品上架
    @Test
    public void testProductStartSupply() {

        //spu
        OpenSupplierSpuOptSupplyStateReq openSupplierSpuOptSupplyStateReq = new OpenSupplierSpuOptSupplyStateReq();
        openSupplierSpuOptSupplyStateReq.setCustomCode("63f23971e308260008ede6fd");
        openSupplierSpuOptSupplyStateReq.setSupplierId("VD3137318810500941090816");
        openSupplierProductRpc.startSpuSupply(openSupplierSpuOptSupplyStateReq);

        //sku
/*        OpenSupplierSkuStartSupplyReq openSupplierSkuStartSupplyReq = new OpenSupplierSkuStartSupplyReq();
        openSupplierSkuStartSupplyReq.setCustomSkuCode("6437c76fc6dec700087db11e");
        openSupplierSkuStartSupplyReq.setSupplierId("VD3137318810500941090816");
        openSupplierProductRpc.startSupply(openSupplierSkuStartSupplyReq);*/
    }

    //测试商品下架
    @Test
    public void testProduceStopSupply() {
        //spu
        OpenSupplierSpuOptSupplyStateReq openSupplierSpuOptSupplyStateReq = new OpenSupplierSpuOptSupplyStateReq();
        openSupplierSpuOptSupplyStateReq.setCustomCode("63f23971e308260008ede6fd");
        openSupplierSpuOptSupplyStateReq.setSupplierId("VD3137318810500941090816");
        openSupplierProductRpc.stopSpuSupply(openSupplierSpuOptSupplyStateReq);

        //sku
/*        OpenSupplierSkuStopSupplyReq req = new OpenSupplierSkuStopSupplyReq();
        req.setCustomSkuCode("6437c76fc6dec700087db11e");
        req.setSupplierId("VD3137318810500941090816");
        openSupplierProductRpc.stopSupply(req);*/
    }

    @Test
    public void testCreateProduct() {
        ThirdPartySystem thirdPartySystem = loadSystem("TESTVVIC0001");
        Map<String, ThirdPartyMappingInfo> categoryMap = new HashMap<>();
        ThirdPartyMappingInfo thirdPartyMappingInfo = new ThirdPartyMappingInfo();
        thirdPartyMappingInfo.setSourceId("PMC357988525732640194560");
        categoryMap.put("Dress/Dress/Dress", thirdPartyMappingInfo);
/*        VVICGetItemDetialResponse.Item item = newItem();
        SupplierSpuCreateV2Command supplierSpuCreateV2Command = vvicJobManager.buildCreateSpuReq(thirdPartySystem, categoryMap, item);
        String spuId = openSupplierProductRpc.createSpu(supplierSpuCreateV2Command);*/

    }

    @Test
    public void newItem() {
        VVICGetItemDetialResponse.Item item = JSON.parseObject("{\"shop\":{\"ratio_of_delivery_on_time\":0.91,\"credit_level\":3,\"ratio_of_return_success\":0.98},\"color\":\"Black\",\"category_id_one\":1,\"supply_level\":5,\"expired_time\":\"\",\"list_grid_image\":\"http://img1.vvic.com/upload/1676817798981_885699.jpg,//img1.vvic.com/upload/1676818311227_453754.jpg,//img1.vvic.com/upload/1676818318007_906112.jpg,//img1.vvic.com/upload/1676818327086_497852.jpg,//img1.vvic.com/upload/1676818305365_220747.jpg\",\"item_vid\":\"63f23971e308260008ed11df\",\"update_time\":\"2023-02-22 08:51:26\",\"video_url\":\"https://video.vvic.com/ae7dd120b06571edb6af6723b78e0102/e731907e74e04156b16a348e1997e4f0-0fd54712ddaf5a53e861268e68a5b89b-ld.mp4\",\"price\":36.0,\"size_id\":\"20509:224,20509:225,20509:226\",\"category_id_sub\":101,\"item_title\":\"High-Grade Dress for Women Summer New Small Polo Collar Slimming Commuting Black Dress Tide\",\"category_name_one\":\"Dress\",\"art_no\":\"实拍2539#\",\"category_name_sub\":\"Dress\",\"create_time\":\"2023-02-19 23:00:01\",\"item_id\":36631542,\"attr_str\":\"Color: Black, Size: S|M|L, Material Composition: Other Materials 100%, Style: Commute, Commute: Korean-style, Total Length: Skirt, Sleeve length: Short sleeve, Collar style: POLO collar, Clothing fly: , SKU: Real Shot 2539#, Waist details: High waist, Year season: Summer of 2023, Applicable season: Summer\",\"shop_name\":\"Clothing Edge Real Shot Shop ◆ Daily Update\",\"market_code\":\"gz\",\"category_id_two\":20000106,\"color_id\":\"1627207:9999860\",\"size\":\"S,M,L\",\"sku_list\":[{\"color_img\":\"\",\"color_id\":\"1627207:9999860\",\"color\":\"Black\",\"size\":\"S\",\"item_id\":36631542,\"price\":36.0,\"size_id\":\"20509:224\",\"sku_id\":\";20509:224;1627207:9999860;\",\"is_lack\":0,\"status\":1,\"sku_vid\":\"63f23971e308260008ed11ef\",\"arrive_time\":0},{\"color_img\":\"\",\"color_id\":\"1627207:9999860\",\"color\":\"Black\",\"size\":\"M\",\"item_id\":36631542,\"price\":36.0,\"size_id\":\"20509:225\",\"sku_id\":\";20509:225;1627207:9999860;\",\"is_lack\":0,\"status\":1,\"sku_vid\":\"63f23971e308260008ed11eg\",\"arrive_time\":0},{\"color_img\":\"\",\"color_id\":\"1627207:9999860\",\"color\":\"Black\",\"size\":\"L\",\"item_id\":36631542,\"price\":36.0,\"size_id\":\"20509:226\",\"sku_id\":\";20509:226;1627207:9999860;\",\"is_lack\":0,\"status\":1,\"sku_vid\":\"63f23971e308260008ed11eh\",\"arrive_time\":0}],\"color_imgs\":\"\",\"category_name_two\":\"Dress\",\"shop_vid\":\"5bff544d3acc5a726440fcc6\",\"up_time\":\"2023-03-01 12:00:00\",\"item_view_image\":\"//img1.vvic.com/upload/1676817798981_885699.jpg\",\"desc\":\"<div align=\\\"center\\\">\\n\\t<img src=\\\"http://img1.vvic.com/upload/1676818745377_230737.jpg\\\" alt=\\\"\\\" /><img src=\\\"https://img1.vvic.com/upload/1676818745789_352809.jpg\\\" alt=\\\"\\\" /><img src=\\\"//img1.vvic.com/upload/1676818745943_269779.jpg\\\" alt=\\\"\\\" /><img src=\\\"http://img1.vvic.com/upload/1676818746095_391554.jpg\\\" alt=\\\"\\\" /><img src=\\\"//img1.vvic.com/upload/1676818746249_693255.jpg\\\" alt=\\\"\\\" /><img src=\\\"https://img1.vvic.com/upload/1676818746473_190399.jpg\\\" alt=\\\"\\\" /><img src=\\\"//img1.vvic.com/upload/1676818746794_180430.jpg\\\" alt=\\\"\\\" /><img src=\\\"//img1.vvic.com/upload/1676818747008_202331.jpg\\\" alt=\\\"\\\" /><img src=\\\"//img1.vvic.com/upload/1676818747346_83885.jpg\\\" alt=\\\"\\\" /><img src=\\\"//img1.vvic.com/upload/1676818747565_530526.jpg\\\" alt=\\\"\\\" /><img src=\\\"//img1.vvic.com/upload/1676818747733_428024.jpg\\\" alt=\\\"\\\" /><img src=\\\"//img1.vvic.com/upload/1676818747940_874654.jpg\\\" alt=\\\"\\\" /><img src=\\\"//img1.vvic.com/upload/1676818748128_696839.jpg\\\" alt=\\\"\\\" /><img src=\\\"//img1.vvic.com/upload/1676818748316_923123.jpg\\\" alt=\\\"\\\" /><img src=\\\"//img1.vvic.com/upload/1676818748505_323809.jpg\\\" alt=\\\"\\\" /><img src=\\\"//img1.vvic.com/upload/1676818748672_374610.jpg\\\" alt=\\\"\\\" /><img src=\\\"//img1.vvic.com/upload/1676818748837_648446.jpg\\\" alt=\\\"\\\" /><img src=\\\"//img1.vvic.com/upload/1676818749025_422501.jpg\\\" alt=\\\"\\\" /><img src=\\\"//img1.vvic.com/upload/1676818749222_945810.jpg\\\" alt=\\\"\\\" /><img src=\\\"//img1.vvic.com/upload/1676818749403_691247.jpg\\\" alt=\\\"\\\" /><img src=\\\"//img1.vvic.com/upload/1676818749567_156699.jpg\\\" alt=\\\"\\\" /><img src=\\\"//img1.vvic.com/upload/1676818749755_484044.jpg\\\" alt=\\\"\\\" /><img src=\\\"//img1.vvic.com/upload/1676818749926_186786.jpg\\\" alt=\\\"\\\" /><img src=\\\"//img1.vvic.com/upload/1676818750088_741343.jpg\\\" alt=\\\"\\\" /><img src=\\\"//img1.vvic.com/upload/1676818750253_234649.jpg\\\" alt=\\\"\\\" /> \\n</div>\",\"status\":1,\"weight_type\":2}", VVICGetItemDetialResponse.Item.class);
/*        String desc = item.getDesc();
        String replace = desc.replace("src=\"", "src=\"http:").replace("\n","<br>").replace("\t","");
        item.setDesc(replace);*/

        String desc = item.getDesc();
        if (StringUtils.isNotBlank(desc)) {
            if (!(desc.contains("https") || desc.contains("http"))) {
                if (desc.contains("<img") || desc.contains("<div") || desc.contains("<p>")) {
                    String replace = desc.replace("src=\"", "src=\"http:");
                    item.setDesc(replace);
                } else {
                    //iterator.remove();
                }
            } else if (desc.contains("链接") || desc.contains("提取码") || desc.contains("网盘")) {
                Document parse = Jsoup.parse(desc);//html为内容
                Elements imgs = parse.getElementsByTag("img");
                if (CollectionUtils.isNotEmpty(imgs)) {
                    StringBuilder stringBuilder = new StringBuilder();
                    imgs.forEach(img -> {
                        String src = img.attr("src");
                        Pattern pattern = Pattern.compile("\\w+.(jpg|png|jpeg|bmp|webp|JPG|PNG|JPEG|BMP|WEBP)$");
                        Matcher matcher = pattern.matcher(src);
                        if (matcher.find()) {
                            String outerHtml = img.outerHtml();
                            if (!(outerHtml.contains("http") || outerHtml.contains("https"))) {
                                stringBuilder.append(outerHtml.replace("src=\"", "src=\"http:"));
                            } else {
                                stringBuilder.append(outerHtml);
                            }
                        }
                    });
                    item.setDesc(stringBuilder.toString());
                } else {
                    //iterator.remove();
                }
            } else {
                if (!(desc.contains("src=\"https") || desc.contains("src=\"http"))) {
                    desc = desc.replace("src=\"", "src=\"http:");
                }
                item.setDesc(desc);
            }
        }


        //return item;
    }
}
