package com.newnary.gsp.center.tpsi.infra.repository;

import com.newnary.api.base.common.PageCondition;
import com.newnary.api.base.common.PageList;
import com.newnary.gsp.center.tpsi.infra.model.ApiDockingResult;

import java.util.List;
import java.util.Optional;

public interface IApiDockingResultRepository {

    void store(ApiDockingResult thirdPartySystem);

    Optional<ApiDockingResult> loadByKeyAndType(String key, String type);

    List<ApiDockingResult> loadByTypeAndStatus(String type, String status);

    PageList<ApiDockingResult> pageQueryByTypeAndStatus(String type, String status, PageCondition condition);
}
