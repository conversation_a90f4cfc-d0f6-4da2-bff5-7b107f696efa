package com.newnary.gsp.center.tpsi.service.haiying;

import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.HaiYingDataApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.ebay.*;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
public interface IHaiYingDataEbayApiSve {

    HaiYingDataApiBaseResult<String> getProductList(HaiYingEbayProductListRequest productListRequest);

    HaiYingDataApiBaseResult<String> getProductDetailInfo(HaiYingEbayProductDetailInfoRequest productDetailInfoRequest);

    HaiYingDataApiBaseResult<String> getCategoryTree(HaiYingEbayCategoryTreeRequest categoryTreeRequest);

    HaiYingDataApiBaseResult<String> getCategoryDetail(HaiYingEbayCategoryDetailRequest categoryDetailRequest);

    HaiYingDataApiBaseResult<String> getTopCategoryInfo(HaiYingEbayTopCategoryInfoRequest topCategoryInfoRequest);

}
