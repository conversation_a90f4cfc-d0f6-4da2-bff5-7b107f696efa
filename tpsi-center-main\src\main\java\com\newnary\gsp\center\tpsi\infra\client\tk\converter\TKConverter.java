package com.newnary.gsp.center.tpsi.infra.client.tk.converter;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.product.api.product.request.SupplierSkuSpecInfo;
import com.newnary.gsp.center.product.api.product.response.ChannelSaleItemDetailInfo;
import com.newnary.gsp.center.product.api.product.response.SupplierSkuInfo;
import com.newnary.gsp.center.product.api.product.response.SupplierSpuBaseInfo;
import com.newnary.gsp.center.product.api.product.vo.SupplierSpuDescInfo;
import com.newnary.gsp.center.product.api.product.vo.SupplierSpuParamInfo;
import com.newnary.gsp.center.tpsi.infra.client.tk.dto.TKApiParam;
import com.newnary.gsp.center.tpsi.infra.client.tk.dto.TKProductCreateReq;
import com.newnary.gsp.center.tpsi.infra.client.tk.dto.TKProductUpdateReq;
import com.newnary.gsp.center.tpsi.infra.client.tk.dto.vo.SaleAttribute;
import com.newnary.gsp.center.tpsi.infra.client.tk.dto.vo.TKOrderExtInfo;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyCategoryMappingExtInfo;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingInfo;
import com.newnary.gsp.center.trade.api.order.dto.OrderConsigneeDTO;
import com.newnary.gsp.center.trade.api.order.dto.OrderGoodsDTO;
import com.newnary.gsp.center.trade.api.order.dto.OrderSaleItemDTO;
import com.newnary.gsp.center.trade.api.order.dto.PaymentDTO;
import com.newnary.gsp.center.trade.api.order.enums.OrderPayMethod;
import com.newnary.gsp.center.trade.api.order.enums.OrderPayState;
import com.newnary.gsp.center.trade.api.order.enums.OrderPayType;
import com.newnary.gsp.center.trade.api.order.request.command.OrderCreateReq;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 1. tk 海外版商品数据不能有中文信息
 */
public class TKConverter {

    public static TKProductUpdateReq buildProduct(List<ChannelSaleItemDetailInfo> items,
                                                  Map<String, ThirdPartyMappingInfo> categoryIdMapping,
                                                  Map<String, String> brandIdMapping,
                                                  Map<String, String> imageIdMapping,
                                                  String apiParam
    ) {
        TKApiParam tkApiParam = JSONObject.parseObject(apiParam, TKApiParam.class);
        TKProductUpdateReq req = new TKProductUpdateReq();
        SupplierSpuBaseInfo spuBaseInfo = items.get(0).getSkuDetailInfo().getSpuBaseInfo();
        SupplierSpuDescInfo descInfo = spuBaseInfo.getDescInfo();
        req.setProduct_name(descInfo.getTitle());
        if (descInfo.getTitle().length() <= 25) {
            throw new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "标题长度少于25字");
        }
        String brandId = spuBaseInfo.getBrandInfo().getBrandId();
        //TODO 暂时不用品牌，后面可能用
        //String tkBrandId = getTkBrandId(brandIdMapping, tkApiParam, brandId);
        // req.setBrand_id(tkBrandId);
        String tkCategoryId = getTkCategoryId(categoryIdMapping, tkApiParam, spuBaseInfo);
        req.setCategory_id(tkCategoryId);

        req.setDescription(descInfo.getTextDesc());
        if (StringUtils.isBlank(descInfo.getTextDesc())) {
            throw new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "创建商品缺失必要信息-商品详情");
        }
        // todo 英国区域单位换算：公制转英制
        SupplierSkuInfo skuInfo = items.get(0).getSkuDetailInfo().getSkuInfo();
        req.setPackage_weight(convertBigDecimal2Str(skuInfo.getGrossWeight()));
        req.setPackage_height(covertM2CM(skuInfo.getPackingHeight()));
        req.setPackage_length(covertM2CM(skuInfo.getPackingLength()));
        req.setPackage_width(covertM2CM(skuInfo.getPackingWidth()));


        req.setImages(buildImages(imageIdMapping));
        req.setProduct_attributes(buildProductAttributes(categoryIdMapping.get(spuBaseInfo.getCategoryId()), spuBaseInfo));
        req.setOuter_product_id(spuBaseInfo.getSupplierSpuId());
        req.setSkus(buildSkus(items, tkApiParam, categoryIdMapping.get(spuBaseInfo.getCategoryId())));
        return req;
    }


    private static String convertBigDecimal2Str(BigDecimal value) {
        if (value == null) {
            return "0";
        }
        return value.toString();
    }

    private static Integer covertM2CM(BigDecimal value) {
        if (value == null) {
            return 0;
        }
        return value.multiply(new BigDecimal(100)).intValue();
    }

    private static List<TKProductCreateReq.ProductAttribute> buildProductAttributes(ThirdPartyMappingInfo thirdPartyMappingInfo, SupplierSpuBaseInfo spuBaseInfo) {
        List<TKProductCreateReq.ProductAttribute> attributes = new ArrayList<>();

        List<SupplierSpuParamInfo> customParams = spuBaseInfo.getParamsInfo().getCustomParams();
        if (CollectionUtils.isEmpty(customParams)) {
            return attributes;
        }
        if (ObjectUtils.isEmpty(thirdPartyMappingInfo)) {
            return attributes;
        }
        String extendBizInfo = thirdPartyMappingInfo.getExtendBizInfo();
        if (StringUtils.isBlank(extendBizInfo)) {
            return attributes;
        }
        ThirdPartyCategoryMappingExtInfo categoryMappingExtInfo = JSONObject.parseObject(extendBizInfo, ThirdPartyCategoryMappingExtInfo.class);
        Map<String, String> attrMap = categoryMappingExtInfo.getSourceProductAttributeToTargetAttribute();
        if (null == attrMap) {
            return attributes;
        }
        for (SupplierSpuParamInfo customParam : customParams) {
            String attrId = attrMap.get(customParam.getParamName());
            if (StringUtils.isBlank(attrId) && CollectionUtils.isEmpty(customParam.getValues())) {
                continue;
            }

            TKProductCreateReq.ProductAttribute attribute = new TKProductCreateReq.ProductAttribute();
            attribute.setAttribute_id(attrId);
            List<TKProductCreateReq.ProductAttributeValue> values = new ArrayList<>();
            customParam.getValues().forEach(item -> {
                TKProductCreateReq.ProductAttributeValue value = new TKProductCreateReq.ProductAttributeValue();
                value.setValue_name(item.getParamValue());
                values.add(value);
            });
            attribute.setAttribute_values(values);

        }
        return attributes;
    }


    private static String getTkCategoryId(Map<String, ThirdPartyMappingInfo> categoryIdMapping, TKApiParam tkApiParam, SupplierSpuBaseInfo spuBaseInfo) {
        ThirdPartyMappingInfo categoryMappingInfo = categoryIdMapping.get(spuBaseInfo.getCategoryId());
        if (null == categoryMappingInfo) {
            throw new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "tiktok 类目映射缺失, categoryId=".concat(spuBaseInfo.getCategoryId()));
        }
        return categoryMappingInfo.getTargetId();
    }

    private static String getTkBrandId(Map<String, String> brandIdMapping, TKApiParam tkApiParam, String brandId) {
        String tkBrandId = brandIdMapping.get(brandId);
        if (StringUtils.isBlank(tkBrandId) && null != tkApiParam.getBrandMapping()) {
            tkBrandId = tkApiParam.getBrandMapping().getString(brandId);
        }
        if (StringUtils.isBlank(tkBrandId)) {
            throw new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "tiktok 品牌映射缺失, brandId=".concat(brandId));
        }
        return tkBrandId;
    }

    private static List<TKProductCreateReq.Sku> buildSkus(List<ChannelSaleItemDetailInfo> items, TKApiParam tkApiParam, ThirdPartyMappingInfo categoryMappingInfo) {
        List<TKProductCreateReq.Sku> skus = new ArrayList<>();

        items.forEach(item -> {
            TKProductCreateReq.Sku sku = new TKProductCreateReq.Sku();
            sku.setOriginal_price(item.getChannelSaleItemInfo().getSupplyInfo().getSupplyPrice().toString());
            sku.setSeller_sku(item.getChannelSaleItemInfo().getSaleItemId());
            sku.setOuter_sku_id(item.getChannelSaleItemInfo().getSaleItemId());
            SupplierSkuInfo skuInfo = item.getSkuDetailInfo().getSkuInfo();

            // todo 各国区域条码校验,商品系统提供的的接口数据返回该字段缺少值，暂时填写
            if (StringUtils.isAllBlank(skuInfo.getCodeEAN(), skuInfo.getCodeUPC())) {
                String barType = StringUtils.isNotBlank(skuInfo.getCodeEAN()) ? "EAN" : "UPC";
                String barcode = StringUtils.isNotBlank(skuInfo.getCodeEAN()) ? skuInfo.getCodeEAN() : skuInfo.getCodeUPC();
                sku.setProduct_identifier_code(new TKProductCreateReq.ProductIdentifierCode("12345678901234", 1));
            } else {
                String barType = StringUtils.isNotBlank(skuInfo.getCodeEAN()) ? "EAN" : "UPC";
                String barcode = StringUtils.isNotBlank(skuInfo.getCodeEAN()) ? skuInfo.getCodeEAN() : skuInfo.getCodeUPC();
                sku.setProduct_identifier_code(new TKProductCreateReq.ProductIdentifierCode(barcode, convertCodeType(barType)));
            }


            List<SupplierSkuSpecInfo> specs = skuInfo.getSpecs();

            String categoryExtendBizInfo = categoryMappingInfo.getExtendBizInfo();
            if (CollectionUtils.isEmpty(specs) || StringUtils.isBlank(categoryExtendBizInfo)) {
                throw new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "tiktok gsp商品缺失规格信息或规格映射信息, itemId=".concat(item.getSaleItemInfo().getSaleItemId()));
            }
            ThirdPartyCategoryMappingExtInfo categoryMappingExtInfo = JSONObject.parseObject(categoryExtendBizInfo, ThirdPartyCategoryMappingExtInfo.class);
            Map<String, String> saleAttributeMap = categoryMappingExtInfo.getSourceSaleAttributeToTargetAttribute();

            List<SaleAttribute> saleAttributes = new ArrayList<>();
            specs.forEach(spec -> {
                SaleAttribute saleAttribute = new SaleAttribute();
                String attrId = saleAttributeMap.get(spec.getSpecName());
                if (StringUtils.isBlank(attrId)) {
                    throw new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "tiktok gsp商品部分缺失规格信息或规格映射信息, itemId=".concat(item.getSaleItemInfo().getSaleItemId()));
                }
                saleAttribute.setAttribute_id(attrId);
                saleAttribute.setCustom_value(spec.getSpecValue());
                saleAttributes.add(saleAttribute);

            });

            sku.setSales_attributes(saleAttributes);

            // todo 后续平台类目id映射以及类目规格属性数据维护了再维护该值, 出问题再说
            if (StringUtils.isNotBlank(tkApiParam.getDefaultWarehouseId())) {
                TKProductCreateReq.StockInfo stockInfo = new TKProductCreateReq.StockInfo();
                stockInfo.setWarehouse_id(tkApiParam.getDefaultWarehouseId());
                stockInfo.setAvailable_stock(item.getChannelSaleItemInfo().getStockInfo().getTotalCount());
                sku.setStock_infos(Collections.singletonList(stockInfo));
            }
            skus.add(sku);

        });
        return skus;
    }


    private static List<TKProductCreateReq.Image> buildImages(Map<String, String> imageIdMapping) {
        List<TKProductCreateReq.Image> images = new ArrayList<>();
        for (String key : imageIdMapping.keySet()) {
            TKProductCreateReq.Image image = new TKProductCreateReq.Image();
            image.setId(imageIdMapping.get(key));
            images.add(image);
        }
        return images;
    }

    public static OrderCreateReq convert2ErpOrderCreateReq(JSONObject item, String channelId) {
        OrderCreateReq req = new OrderCreateReq();
        req.setChannelId(channelId);
        req.setChannelOrderNo(item.getString("order_id"));
        req.setOperator("system");
        req.setRemark(item.getString("buyer_message"));
        // 普通销售订单
       // req.setOrderType("SO");
        //req.setFirstLogisticsProductId(item.getString("shipping_provider"));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        //req.setGmtOrderCreate(sdf.format(new Date(item.getString("create_time"))));
        //req.setGmtOrderCreate(sdf.format(new Date().getTime()));
        req.setPaymentPrice(item.getJSONObject("payment_info").getString("total_amount"));
        req.setShippingPrice(item.getJSONObject("payment_info").getString("shipping_fee"));
        req.setTotalTax(item.getJSONObject("payment_info").getString("taxes"));
        req.setTotalGoodsPrice(item.getJSONObject("payment_info").getString("original_total_product_price"));
        req.setTradeCurrency(item.getJSONObject("payment_info").getString("currency"));
        req.setDiscountPrice(getDiscountPrice(item));

        req.setPayment(buildPayment(item));
        req.setOrderGoodsDTOList(buildOrderGoodsList(item));
        req.setOrderConsignee(buildOrderConsignee(item));
        return req;
    }

    private static OrderConsigneeDTO buildOrderConsignee(JSONObject item) {
        JSONObject receiptAddress = item.getJSONObject("recipient_address");
        OrderConsigneeDTO dto = new OrderConsigneeDTO();
        dto.setBackupDetailInfo(receiptAddress.getString("full_address"));
        dto.setDistrict(receiptAddress.getString("district"));
        dto.setProvince(receiptAddress.getString("state"));
        dto.setCity(receiptAddress.getString("city"));
        dto.setCountry(receiptAddress.getString("region_code"));
        StringBuilder detailInfo = new StringBuilder();
        if (StringUtils.isNotBlank(receiptAddress.getString("town"))) {
            detailInfo.append(receiptAddress.getString("town"));
        }
        detailInfo.append(receiptAddress.getString("address_detail"));
        dto.setDetailInfo(detailInfo.toString());
        dto.setZip(receiptAddress.getString("zipcode"));
        dto.setPhone(receiptAddress.getString("phone"));
        dto.setName(receiptAddress.getString("name"));
        return dto;
    }

    private static List<OrderSaleItemDTO> buildOrderGoodsList(JSONObject item) {
        List<OrderSaleItemDTO> orderGoodsDTOS = new ArrayList<>();
        JSONArray itemList = item.getJSONArray("item_list");
        if (CollectionUtils.isEmpty(itemList)) {
            return orderGoodsDTOS;
        }
        for (int i = 0; i < itemList.size(); i++) {
            JSONObject goodObj = itemList.getJSONObject(i);
            OrderSaleItemDTO dto = new OrderSaleItemDTO();
            dto.setOuterGoodsId(goodObj.getString("seller_sku"));
            dto.setQuantity(goodObj.getInteger("quantity"));
            dto.setOuterGoodsName(goodObj.getString("product_name"));
            dto.setUnitPrice(goodObj.getBigDecimal("sku_sale_price"));
            dto.setUnitPriceCurrency(item.getJSONObject("payment_info").getString("currency"));
            dto.setTax(new BigDecimal("0"));

            orderGoodsDTOS.add(dto);
        }
        return orderGoodsDTOS;
    }

    private static String getDiscountPrice(JSONObject item) {
        Integer goodsTotalPrice = ObjectUtils.defaultIfNull(item.getJSONObject("payment_info").getInteger("original_total_product_price"), 0);
        Integer shippingFee = ObjectUtils.defaultIfNull(item.getJSONObject("payment_info").getInteger("shipping_fee"), 0);
        Integer totalAmount = ObjectUtils.defaultIfNull(item.getJSONObject("payment_info").getInteger("total_amount"), 0);
        return String.valueOf(goodsTotalPrice + shippingFee - totalAmount);
    }


    private static PaymentDTO buildPayment(JSONObject item) {
        PaymentDTO paymentDTO = new PaymentDTO();
        paymentDTO.setPayMethod(OrderPayMethod.ONLINE_PAYMENT);
        paymentDTO.setPayType(OrderPayType.EXTERNAL_PAY);
        paymentDTO.setPayProductCode(item.getString("payment_method"));
        paymentDTO.setPayState(OrderPayState.PAID);
        paymentDTO.setGmtPay(item.getLong("paid_time"));
        return paymentDTO;
    }

    public static TKOrderExtInfo buildOrderExtInfo(JSONObject item) {

        return JSONObject.parseObject(item.toJSONString(), TKOrderExtInfo.class);
    }

    /**
     * 1-GTIN、2-EAN、3-UPC、4-ISBN (input one of them to this field)
     *
     * @param value
     * @return
     */
    public static Integer convertCodeType(String value) {
        switch (value) {
            case "EAN":
                return 2;
            case "UPC":
                return 3;
        }
        throw new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "未知sku 条码类型");
    }
}
