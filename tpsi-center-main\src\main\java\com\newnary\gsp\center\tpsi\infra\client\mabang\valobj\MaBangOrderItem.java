package com.newnary.gsp.center.tpsi.infra.client.mabang.valobj;

import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/1/5
 */
@Data
public class MaBangOrderItem {

    private String orderItemId;
    private String originOrderId;
    private String stockSku;
    private String title;
    private String pictureUrl;
    private String costPrice;
    private String sellPrice;
    private String sellPriceOrigin;
    private Integer quantity;
    private String specifics;
    private String status;
    private String isCombo;
    private String itemId;
    private String platformQuantity;
    private String platformSku;
    private String productUnit;
    private String stockGrid;
    private String stockWarehouseId;
    private String stockWarehouseName;
    private String transactionId;
    private String unitWeight;
    private String itemRemark;
    private String noLiquidCosmetic;
    private String stockStatus;
    private String hasGoods;
}
