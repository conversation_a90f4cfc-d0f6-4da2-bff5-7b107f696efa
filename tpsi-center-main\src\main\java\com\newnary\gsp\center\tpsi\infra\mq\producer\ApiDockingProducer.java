package com.newnary.gsp.center.tpsi.infra.mq.producer;

import com.newnary.mq.starter.producer.AbstractMQProducer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2021/12/16 15:54
 */
@Component
public class ApiDockingProducer extends AbstractMQProducer {

    @Value("${gsp.tpsi.api-docking.mq.producer-id}")
    private String group;

    @Override
    public String producerGroup() {
        return group;
    }
}
