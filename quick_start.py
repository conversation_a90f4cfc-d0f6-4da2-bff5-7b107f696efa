#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动脚本
帮助用户快速开始使用发票OCR工具
"""

import os
import sys
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import paddleocr
        import pandas
        import numpy
        import PIL
        import cv2
        import fitz
        import openpyxl
        return True
    except ImportError:
        return False

def create_sample_folder():
    """创建示例文件夹"""
    sample_folder = Path("sample_invoices")
    if not sample_folder.exists():
        sample_folder.mkdir()
        print(f"✓ 创建示例文件夹: {sample_folder}")
        return sample_folder
    else:
        print(f"✓ 示例文件夹已存在: {sample_folder}")
        return sample_folder

def main():
    """主函数"""
    print("=" * 60)
    print("发票OCR工具快速启动")
    print("=" * 60)
    
    # 检查依赖
    print("1. 检查依赖包...")
    if not check_dependencies():
        print("❌ 依赖包未完全安装")
        print("\n请选择安装方式:")
        print("a) 自动安装: python install_dependencies.py")
        print("b) 手动安装: pip install -r requirements.txt")
        
        choice = input("\n是否现在自动安装依赖? (y/n): ").lower()
        if choice == 'y':
            print("\n正在安装依赖...")
            os.system("python install_dependencies.py")
        else:
            print("请先安装依赖包后再运行此脚本")
            return
    else:
        print("✓ 所有依赖包已安装")
    
    # 测试PaddleOCR
    print("\n2. 测试PaddleOCR...")
    try:
        from paddleocr import PaddleOCR
        ocr = PaddleOCR(use_textline_orientation=True, lang='ch', show_log=False)
        print("✓ PaddleOCR初始化成功")
    except Exception as e:
        print(f"❌ PaddleOCR初始化失败: {e}")
        print("建议运行: python simple_test.py 进行详细诊断")
        return
    
    # 创建示例文件夹
    print("\n3. 准备工作环境...")
    sample_folder = create_sample_folder()
    
    # 检查是否有文件
    files = list(sample_folder.glob("*"))
    supported_files = []
    for file in files:
        if file.suffix.lower() in ['.pdf', '.png', '.jpg', '.jpeg', '.bmp', '.tiff']:
            supported_files.append(file)
    
    if not supported_files:
        print(f"\n📁 请将发票文件放入 '{sample_folder}' 文件夹中")
        print("支持的格式: PDF, PNG, JPG, JPEG, BMP, TIFF")
        
        input("\n放入文件后按回车键继续...")
        
        # 重新检查文件
        files = list(sample_folder.glob("*"))
        supported_files = []
        for file in files:
            if file.suffix.lower() in ['.pdf', '.png', '.jpg', '.jpeg', '.bmp', '.tiff']:
                supported_files.append(file)
        
        if not supported_files:
            print("❌ 未找到支持的发票文件")
            return
    
    print(f"✓ 找到 {len(supported_files)} 个发票文件")
    for file in supported_files:
        print(f"  - {file.name}")
    
    # 开始处理
    print(f"\n4. 开始处理发票...")
    output_file = "发票识别结果.xlsx"
    
    try:
        from invoice_ocr_processor import InvoiceOCRProcessor
        
        processor = InvoiceOCRProcessor()
        processor.process_folder(str(sample_folder), output_file)
        
        if os.path.exists(output_file):
            print(f"\n🎉 处理完成！")
            print(f"结果已保存到: {output_file}")
            
            # 显示结果预览
            try:
                import pandas as pd
                df = pd.read_excel(output_file)
                print(f"\n📊 处理统计:")
                print(f"  总发票数: {len(df)}")
                
                if 'invoice_type' in df.columns:
                    type_counts = df['invoice_type'].value_counts()
                    for invoice_type, count in type_counts.items():
                        print(f"  {invoice_type}: {count} 张")
                
                print(f"\n📋 前3行数据预览:")
                preview_cols = ['file_name', 'invoice_type', 'invoice_number', 'total_amount']
                available_cols = [col for col in preview_cols if col in df.columns]
                if available_cols:
                    print(df[available_cols].head(3).to_string(index=False))
                
            except Exception as e:
                print(f"预览结果时出错: {e}")
            
            # 询问是否打开文件
            choice = input(f"\n是否打开结果文件? (y/n): ").lower()
            if choice == 'y':
                try:
                    os.startfile(output_file)  # Windows
                except:
                    try:
                        os.system(f"open {output_file}")  # macOS
                    except:
                        os.system(f"xdg-open {output_file}")  # Linux
        else:
            print("❌ 处理失败，未生成结果文件")
            
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")
        print("建议查看日志文件: invoice_ocr.log")
    
    print(f"\n" + "=" * 60)
    print("快速启动完成")
    print("=" * 60)
    
    print(f"\n💡 下次使用可以直接运行:")
    print(f"python invoice_ocr_processor.py {sample_folder}")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
