package com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class VVICGetExpressReq {
    /**
     * 国家
     */
    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 地区
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 市场编码
     */
    private String market_code;

    /**
     * 商品信息
     */
    private List<Good> goods;

    @Getter
    @Setter
    public static class Good {
        /**
         * itemId
         */
        private String item_vid;
        /**
         * 商品数量
         */
        private Integer quantity;

        /**
         * 商品skuId
         */
        private String sku_vid;

        /**
         * 商品价格
         */
        private String price;
    }

}
