package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.wms;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
public class EcCangCreateOrderRequest {
    private String platform;
    private String aliexpress_order_no;
    private String lp_order_number;
    private String sw_order_number;
    @NotNull(message = "订单参考号不能为空")
    private String reference_no;
    private String allocated_auto;
    @NotNull(message = "配送方式不能为空")
    private String shipping_method;
    @NotNull(message = "配送仓库不能为空")
    private String warehouse_code;
    @NotNull(message = "收件人国家不能为空")
    private String country_code;
    private String province;
    private String city;
    private String district;
    @NotNull(message = "地址1不能为空")
    private String address1;
    private String address2;
    private String address3;
    @NotNull(message = "邮编不能为空")
    private String zipcode;
    private String license;
    private String doorplate;
    private String company;
    @NotNull(message = "收件人姓名不能为空")
    private String name;
    @NotNull(message = "收件人联系方式不能为空")
    private String phone;
    private String cell_phone;
    private String email;
    private String platform_shop;
    private Integer is_order_cod;
    private Float order_cod_price;
    private String order_cod_currency;
    private Integer order_age_limit;
    private Integer is_signature;
    private Integer insurance_value;
    private Integer is_insurance;
    private String  channel_code;
    private String  packageCenterCode;
    private String  packageCenterName;
    private String  QrCode;
    private String  shortAddress;
    private String  order_desc;
    private String  remark;
    private String  order_business_type;
    private String  receiver_id_type_code;
    private String  receiver_id_number;
    private String  pay_no;
    private String  payer_name;
    private String  id_type_code;
    private String  id_number;
    private String  payer_phone;
    private String  tax;
    private String  other_payment;
    private String  pro_amount;
    private String  transport_fee;
    private String  valuation_fee;
    private String  trans_type_id;
    private String  trans_tool_id;
    private String  voyages;
    private String  pack_type_id;
    private String  trans_sum_no;
    private String  lading_bill_no;
    private Integer  verify;
    private Integer  forceVerify;
    @NotNull(message = "订单明细不能为空")
    private List<Item> items;
    private List<Report> report;
    private String tracking_no;
    private Label label;
    private List<Attach> attach;
    private Integer is_pack_box;
    private String seller_id;
    private String buyer_id;
    private Integer only_logistics;
    private Integer is_release_cargo;
    private Integer is_vip;
    private String order_kind;
    private String order_payer_name;
    private String order_id_number;
    private String order_payer_phone;
    private String order_country_code_origin;
    private String order_sale_amount;
    private String order_sale_currency;
    private String is_platform_ebay;
    private String ebay_item_id;
    private String ebay_transaction_id;
    private String tax_payment_method;
    private String customs_company_name;
    private String customs_address;
    private String customs_contact_name;
    private String customs_email;
    private String customs_tax_code;
    private String customs_phone;
    private String customs_city;
    private String customs_state;
    private String customs_country_code;
    private String customs_postcode;
    private String customs_doorplate;
    private String consignee_tax_number;
    private String order_battery_type;
    private String vat_tax_code;
    private String distribution_information;
    private Integer consignee_tax_type;
    private String consignee_eori;
    private String api_source;
    private String assign_date;
    private String assign_time;
    private String lp_code;
    private Integer is_merge;
    private String IOSS;
    private Integer merge_order_count;
    private Integer insurance_type;
    private String insurance_type_goods_value;
    private Integer is_ju_order;
    public static class Item{
        public String product_sku;
        public String reference_no;
        public String product_name;
        public String product_name_en;
        public BigDecimal product_declared_value;
        public Integer quantity;
        public String ref_tnx;
        public String ref_item_id;
        public String ref_buyer_id;
        public String already_taxed;
        public String child_order_id;
        public List<BatchInfo> batch_info;

    }
    public static class Report{
        public String product_sku;
        public String product_title;
        public String product_title_en;
        public Integer product_quantity;
        public BigDecimal product_declared_value;
        public BigDecimal product_weight;

    }
    public static class Attach{
        public String file_type;
        public Integer attach_id;

    }
    public static class BatchInfo{
        public String inventory_code;
        public Integer sku_quantity;
    }
    public static class Label{
        public String file_type;
        public String file_size;
        public String[] file_data;
    }
}
