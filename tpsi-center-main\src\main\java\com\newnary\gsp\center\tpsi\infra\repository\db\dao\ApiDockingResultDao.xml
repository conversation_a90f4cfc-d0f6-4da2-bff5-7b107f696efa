<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.newnary.gsp.center.tpsi.infra.repository.db.dao.ApiDockingResultDao">

<resultMap id="apiDockingResultPOResult" type="com.newnary.gsp.center.tpsi.infra.repository.db.po.ApiDockingResultPO">
    <result column="value_key" property="valueKey"/>
    <result column="value_type" property="valueType"/>
    <result column="value_json" property="valueJson"/>
    <result column="data_status" property="dataStatus"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="id" property="id"/>
    <result column="gmt_modified" property="gmtModified"/>
    <result column="gmt_create" property="gmtCreate"/>
</resultMap>

<sql id="apiDockingResultPO_columns">
    value_key,
    value_type,
    value_json,
    data_status,
    tenant_id,
    id,
    gmt_modified,
    gmt_create
</sql>

<sql id="apiDockingResultPO_sqlForInsert">
    value_key,
    value_type,
    value_json,
    data_status,
    tenant_id,
    id,
    gmt_modified,
    gmt_create
</sql>

<sql id="apiDockingResultPO_columnsForInsert">
    #{valueKey},
    #{valueType},
    #{valueJson},
    #{dataStatus},
    #{tenantId},
    #{id},
    REPLACE(unix_timestamp(NOW(3)),'.',''),
    REPLACE(unix_timestamp(NOW(3)),'.','')
</sql>

<sql id="apiDockingResultPO_setterForUpdate">
    <set>
        gmt_modified = REPLACE(unix_timestamp(NOW(3)),'.',''),
        value_key = #{valueKey},
        value_type = #{valueType},
        value_json = #{valueJson},
        data_status = #{dataStatus},
    </set>
</sql>

<sql id="apiDockingResultPO_selector">
    select
    <include refid="apiDockingResultPO_columns"/>
    from api_docking_result
</sql>

<sql id="apiDockingResultPO_query_segment">
    <trim prefix="WHERE" prefixOverrides="AND|OR">
        <if test="data.valueKey != null">
            AND value_key = #{data.valueKey}
        </if>
        <if test="data.valueType != null">
            AND value_type = #{data.valueType}
        </if>
        <if test="data.valueJson != null">
            AND value_json = #{data.valueJson}
        </if>
        <if test="data.dataStatus != null">
            AND data_status = #{data.dataStatus}
        </if>
        <if test="data.id != null">
            AND id = #{data.id}
        </if>
        <if test="data.gmtModified != null">
            AND gmt_modified = #{data.gmtModified}
        </if>
        <if test="data.gmtCreate != null">
            AND gmt_create = #{data.gmtCreate}
        </if>
        <if test="ins != null and ins.size() > 0">
            AND
            <foreach collection="ins" item="item" open="(" separator="and" close=")">
                ${item.column} in
                <foreach collection="item.values" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </foreach>
        </if>
        <if test="notIns != null and notIns.size() > 0">
            AND
            <foreach collection="notIns" item="item" open="(" separator="and" close=")">
                ${item.column} not in
                <foreach collection="item.values" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </foreach>
        </if>
        <if test="equals != null and equals.size() > 0">
            AND
            <foreach collection="equals" item="item" open="(" separator="and" close=")">
                ${item.column} = #{item.value}
            </foreach>
        </if>
        <if test="notEquals != null and notEquals.size() > 0">
            AND
            <foreach collection="notEquals" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ <> ]]> #{item.value}
            </foreach>
        </if>
        <if test="lts != null and lts.size() > 0">
            AND
            <foreach collection="lts" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ < ]]> #{item.value}
            </foreach>
        </if>
        <if test="gts != null and gts.size() > 0">
            AND
            <foreach collection="gts" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ > ]]> #{item.value}
            </foreach>
        </if>
        <if test="likes != null and likes.size() > 0">
            AND
            <foreach collection="likes" item="item" open="(" separator="and" close=")">
                ${item.column} like concat('%', #{item.value}, '%')
            </foreach>
        </if>
        <if test="ltes != null and ltes.size() > 0">
            AND
            <foreach collection="ltes" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ <= ]]> #{item.value}
            </foreach>
        </if>
        <if test="gtes != null and gtes.size() > 0">
            AND
            <foreach collection="gtes" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ >= ]]> #{item.value}
            </foreach>
        </if>
        <if test="ranges != null and ranges.size() > 0">
            AND
            <foreach collection="ranges" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ >= ]]> #{item.from} and ${item.column} <![CDATA[ < ]]> #{item.to}
            </foreach>
        </if>
        AND tenant_id = #{tenantId}
    </trim>
</sql>


<insert id="insert" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.ApiDockingResultPO">
    insert into api_docking_result
    (
        <include refid="apiDockingResultPO_sqlForInsert"/>
    )
    values
    (
        <include refid="apiDockingResultPO_columnsForInsert"/>
    )
</insert>

<update id="update" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.ApiDockingResultPO">
    update api_docking_result
    <include refid="apiDockingResultPO_setterForUpdate" />
    where id = #{id}
    and tenant_id = #{tenantId}
</update>


<update id="updateByQuery" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.ApiDockingResultPO">
    update api_docking_result
    <set>
        gmt_modified = REPLACE(unix_timestamp(NOW(3)),'.',''),
        value_key = #{update.valueKey},
        value_type = #{update.valueType},
        value_json = #{update.valueJson},
        data_status = #{update.dataStatus},
    </set>
    <include refid="apiDockingResultPO_query_segment"/>
</update>

<!-- Delete -->
<delete id="delete">
    delete from api_docking_result
    <include refid="apiDockingResultPO_query_segment"/>
</delete>
<delete id="deleteById">
    delete from api_docking_result
    where id = #{id}
    and tenant_id = #{tenantId}
</delete>





<!-- query class -->
<sql id="apiDockingResultPO_groupBy">
    <if test="groupBys != null and groupBys.size() > 0">
        group by
        <foreach collection="groupBys" item="item" open="" separator="," close="">
            #{item}
        </foreach>
    </if>
</sql>

<sql id="apiDockingResultPO_orderby">
    <if test="orderBys != null and orderBys.size() > 0">
        order by
        <foreach collection="orderBys" item="item" open="" separator="," close="">
            ${item.column} ${item.direction}
        </foreach>
    </if>
</sql>


<select id="query" resultMap="apiDockingResultPOResult" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.ApiDockingResultPO">
    <include refid="apiDockingResultPO_selector"/>
    <include refid="apiDockingResultPO_query_segment"/>
    <include refid="apiDockingResultPO_groupBy"/>
    <include refid="apiDockingResultPO_orderby"/>
</select>



<select id="count" resultType="java.lang.Long">
    SELECT COUNT(1) FROM api_docking_result
    <include refid="apiDockingResultPO_query_segment"/>
</select>

<select id="getById" resultMap="apiDockingResultPOResult">
    <include refid="apiDockingResultPO_selector"/>
    where id = #{id}
    and tenant_id = #{tenantId}
</select>

<select id="getByIdForUpdate" resultMap="apiDockingResultPOResult">
    <include refid="apiDockingResultPO_selector"/>
    where id = #{id}
    and tenant_id = #{tenantId}
    for update
</select>

</mapper>
