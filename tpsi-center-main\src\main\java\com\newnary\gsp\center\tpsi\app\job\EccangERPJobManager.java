package com.newnary.gsp.center.tpsi.app.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.exception.BaseErrorInfo;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.common.utils.locale.CountryCode;
import com.newnary.common.utils.locale.LanguageLocaleType;
import com.newnary.gsp.center.product.api.category.common.CategoryLevel;
import com.newnary.gsp.center.product.api.category.response.CategoryInfo;
import com.newnary.gsp.center.product.api.common.dto.MultimediaInfo;
import com.newnary.gsp.center.product.api.common.dto.MultimediaType;
import com.newnary.gsp.center.product.api.open.request.*;
import com.newnary.gsp.center.product.api.product.enums.DPSOuterCodeType;
import com.newnary.gsp.center.product.api.product.enums.SupplierItemSaleMode;
import com.newnary.gsp.center.product.api.product.request.*;
import com.newnary.gsp.center.product.api.product.vo.SupplierSpuDescInfo;
import com.newnary.gsp.center.tpsi.infra.client.eccang.params.EcCangERPParams;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp.EcCangERPGetProductListRequest;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.erp.EcCangERPGetProductListResponse;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingInfo;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingType;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.ThirdPartyMappingManager;
import com.newnary.gsp.center.tpsi.infra.rpc.CategoryRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.OpenSupplierProductRpc;
import com.newnary.gsp.center.tpsi.service.eccang.IEccangERPApiSve;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.job.core.handler.annotation.Job;
import com.newnary.tenant.context.TenantCarrier;
import com.newnary.tenant.context.TenantID;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class EccangERPJobManager {

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Resource
    private OpenSupplierProductRpc openSupplierProductRpc;
    @Resource
    private CategoryRpc categoryRpc;

    @Resource
    private IEccangERPApiSve eccangERPApiSveImpl;

    @Resource
    private ThirdPartyMappingManager thirdPartyMappingManager;

    private final ThreadPoolExecutor processExecutor = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors() * 20,
            Runtime.getRuntime().availableProcessors() * 20,
            20L, TimeUnit.SECONDS,
            new LinkedBlockingDeque<>(100),
            new ThreadPoolExecutor.CallerRunsPolicy());

    private String categoryId = null;
    private String thirdPartySystemId = null;

    //同步ecCangERP 商品
    @Job("autoSyncECangERPProduct")
    public ReturnT<String> syncECangERPProduct(String param) {
        log.info("同步易仓商品定时任务开始, param={}", param);
        try {
            //根据参数获取需要执行的第三方系统id
            JSONObject paramObject = JSONObject.parseObject(param);
            //TenantCarrier.setTenantID(new TenantID("GZRM"));
            thirdPartySystemId = paramObject.getString("thirdPartySystemId");
            Integer perGroupCount = paramObject.getInteger("perGroupCount");
            String supplierId = paramObject.getString("supplierId");
            String warehouse = paramObject.getString("warehouse");
            categoryId = paramObject.getString("categoryId");
            String products = paramObject.getString("products");
            //根据thirdPartySystemId获取第三方系统参数
            ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
            if (StringUtils.isNotBlank(products)) {
                List<String> list = JSON.parseArray(products, String.class);
                for (String s : list) {
                    syncEcCangProduct(thirdPartySystem, warehouse, supplierId, s);
                }
                return ReturnT.SUCCESS;
            }

            //指定插入页数
            Integer insertPage = paramObject.getInteger("insertPage");
            //指定从第几页开始插入
            Integer indexPage = paramObject.getInteger("indexPage");
            //指定每页条数
            Integer pageSize = paramObject.getInteger("pageSize");

            EcCangERPParams params = JSON.parseObject(thirdPartySystem.getParams(), EcCangERPParams.class);
            params.setSupplierId(supplierId);
            params.setWarehouse(warehouse);
            thirdPartySystem.setParams(JSON.toJSONString(params));
            EcCangERPGetProductListRequest ecCangERPGetProductListRequest = new EcCangERPGetProductListRequest();
            //ecCangERPGetProductListRequest.setProductStatus(1);
            ecCangERPGetProductListRequest.setPageSize(pageSize);
            List<EcCangERPGetProductListResponse> productList = null;
            //当前页数
            int currentPage = 0;
            if (indexPage != null) {
                currentPage = indexPage - 1;
            }
            Map<String, ThirdPartyMappingInfo> categoryMapping = null;
            do {
                ecCangERPGetProductListRequest.setPage(currentPage += 1);
                try {
                    EcCangApiBaseResult<String> productListStr = eccangERPApiSveImpl.getProductList(thirdPartySystem, ecCangERPGetProductListRequest);
                    assert productListStr != null;
                    if (StringUtils.isBlank(productListStr.getData())) continue;
                    productList = JSON.parseArray(productListStr.getData(), EcCangERPGetProductListResponse.class);
                } catch (Exception e) {
                    log.error("[{}] 易仓商品同步任务异常！e={}", "易仓商品同步任务", e);
                    log.info("当前页数为：{}", currentPage);
                    continue;
                }

                if (CollectionUtils.isEmpty(productList)) continue;

                //获取类目信息
                if (StringUtils.isBlank(categoryId)) {
                    categoryMapping = thirdPartyMappingManager.batchGetIdMappingInfoByTargetIds("GSP", "ECCANG", productList.stream().map(item -> {
                        if (StringUtils.isNotBlank(item.getProcutCategoryCode3())) {
                            return item.getProcutCategoryCode3();
                        } else if (StringUtils.isNotBlank(item.getProcutCategoryCode2())) {
                            return item.getProcutCategoryCode2();
                        } else if (StringUtils.isNotBlank(item.getProcutCategoryCode1())) {
                            return item.getProcutCategoryCode1();
                        } else {
                            return item.getProcutCategoryCode3();
                        }
                    }).distinct().collect(Collectors.toList()), ThirdPartyMappingType.CATEGORY);
                }
                //syncProduct(productList,params,categoryMapping);
                processBatch(productList, perGroupCount, params, categoryMapping);
                productList = ((indexPage != null) ? ((currentPage - indexPage) >= insertPage) : (currentPage >= insertPage)) ? null : productList;
                log.info("当前页数为：{}", currentPage);
            } while (CollectionUtils.isNotEmpty(productList));

        } catch (Exception e) {
            log.error("[{}] 易仓商品同步任务异常！e={}", "易仓商品同步任务", e);
            return new ReturnT<>(500, e.getMessage());
        }
        return ReturnT.SUCCESS;
    }

    //根据products同步商品
    public void syncEcCangProduct(ThirdPartySystem thirdPartySystem, String warehouse, String supplierId, String products) {
        EcCangERPParams params = JSON.parseObject(thirdPartySystem.getParams(), EcCangERPParams.class);
        params.setSupplierId(supplierId);
        params.setWarehouse(warehouse);
        thirdPartySystem.setParams(JSON.toJSONString(params));
        EcCangERPGetProductListRequest ecCangERPGetProductListRequest = new EcCangERPGetProductListRequest();
        ecCangERPGetProductListRequest.setProductSku(products);
        EcCangApiBaseResult<String> productListStr = eccangERPApiSveImpl.getProductList(thirdPartySystem, ecCangERPGetProductListRequest);
        assert productListStr != null;
        if (StringUtils.isBlank(productListStr.getData())) return;
        List<EcCangERPGetProductListResponse> productList = JSON.parseArray(productListStr.getData(), EcCangERPGetProductListResponse.class);
        try {
            processBatch(productList, 2, params, null);
        } catch (Exception e) {
            //e.printStackTrace();
            log.error("[{}] 易仓商品同步任务异常！e={}", "易仓商品同步任务", e);
        }
    }

    //使用多线程进行同步
    private void processBatch(List<EcCangERPGetProductListResponse> productList, Integer perGroupCount, EcCangERPParams params, Map<String, ThirdPartyMappingInfo> categoryMapping) throws Exception {
        final Optional<TenantID> tenantIdKept = TenantCarrier.getTenantID();
        // 1. 批量任务分组
        //List<String> skuIdList = productList.stream().map(EcCangERPGetProductListResponse::getProductSku).collect(Collectors.toList());
        List<List<EcCangERPGetProductListResponse>> groupSkuIds = ListUtils.partition(productList, perGroupCount);
        CountDownLatch processCountDownLatch = new CountDownLatch(groupSkuIds.size());

        // 2. 线程池执行
        groupSkuIds.forEach(groupItem -> {

            CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> {
                boolean tenantIdMiss = false;
                try {
                    if (!TenantCarrier.getTenantID().isPresent()) {
                        TenantID tenantID = tenantIdKept.orElse(null);
                        TenantCarrier.setTenantID(tenantID);
                        tenantIdMiss = tenantID != null;
                    }
                    syncProduct(groupItem, params, categoryMapping);
                } catch (Exception e) {
                    log.error("[{}] 易仓商品同步任务异常！e={}", "易仓商品同步任务", e);
                } finally {
                    processCountDownLatch.countDown();
                    if (tenantIdMiss) {
                        TenantCarrier.clearTenantID();
                    }
                }
            }, processExecutor);

        });

        // 3. 等待批次商品同步处理完成
        processCountDownLatch.await();
    }

    public void syncProduct(List<EcCangERPGetProductListResponse> productList, EcCangERPParams ecCangERPParams, Map<String, ThirdPartyMappingInfo> categoryMapping) {
        productList.forEach(sku -> {
            ThirdPartyMappingInfo newestInfoByTarget = thirdPartyMappingManager.getNewestInfoByTarget("ECCANG", "GSP", sku.getProductSku(), ThirdPartyMappingType.PRODUCT_ID);
            if (ObjectUtils.isNotEmpty(newestInfoByTarget)) return;
            try {
/*                EcCangERPGetProductBySkuRequest ecCangERPGetProductBySkuRequest = new EcCangERPGetProductBySkuRequest();
                ecCangERPGetProductBySkuRequest.setProductSku(sku);
                String data = eccangERPApiSveImpl.getProductBySku(thirdPartySystem.getBizId(), ecCangERPGetProductBySkuRequest).getData();
                EcCangERPGetProductBySkuResponse response = (data != null) ? (JSON.parseArray(data, EcCangERPGetProductBySkuResponse.class).get(0)) : null;
                if (response == null) {
                    log.info("ec商品信息为空：{}", response);
                    return;
                }*/
                String spuId = openSupplierProductRpc.createSpu4StockAsync(buildSupplierSpuCreateV2Command(ecCangERPParams, sku, categoryMapping));
                if (spuId != null) {
                    thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", spuId, sku.getProductSku(), ThirdPartyMappingType.PRODUCT_ID.name(), sku);
                    if ("0".equals(sku.getProductStatus())) {
                        OpenSupplierSpuOptSupplyStateReq openSupplierSpuOptSupplyStateReq = new OpenSupplierSpuOptSupplyStateReq();
                        openSupplierSpuOptSupplyStateReq.setSupplierId(ecCangERPParams.getSupplierId());
                        openSupplierSpuOptSupplyStateReq.setCustomCode(sku.getProductSku());
                        openSupplierProductRpc.stopSpuSupply(openSupplierSpuOptSupplyStateReq);

                        OpenSupplierSkuStopSupplyReq req = new OpenSupplierSkuStopSupplyReq();
                        req.setSupplierId(ecCangERPParams.getSupplierId());
                        req.setCustomSkuCode(sku.getProductSku());
                        openSupplierProductRpc.stopSupply(req);
                    }
                    //updatePrice(thirdPartySystem,sku);
                    //updateStock(thirdPartySystem,sku);
                }
            } catch (Exception e) {
                log.error("ecang创建商品失败：{}，skuid:{}", e.getMessage(), sku.getProductSku());
            }

        });
    }

    public SupplierSpuCreateV2Command buildSupplierSpuCreateV2Command(EcCangERPParams ecCangERPParams, EcCangERPGetProductListResponse response, Map<String, ThirdPartyMappingInfo> categoryMapping) {
        SupplierSpuCreateV2Command spuCreateV2Command = new SupplierSpuCreateV2Command();

        //供应商
        spuCreateV2Command.setSupplierId(ecCangERPParams.getSupplierId());

        //spu描述
        spuCreateV2Command.setDescInfos(buildDescInfos(response));

        //默认语言
        spuCreateV2Command.setDefaultLocale(LanguageLocaleType.zh_CN);

        //商家自定义编码
        spuCreateV2Command.setCustomCode(response.getProductSku());

        //商家品牌id
        spuCreateV2Command.setCustomBrandId(response.getBrandCode());

        //类目id
        if (StringUtils.isBlank(categoryId)) {
            setCategoryInfo(spuCreateV2Command, categoryMapping, response);
        } else {
            spuCreateV2Command.setCategoryId(categoryId);
        }

        //商家自定义类目id
        if (StringUtils.isNotBlank(response.getProcutCategoryCode3())) {
            spuCreateV2Command.setCustomCategoryId(response.getProcutCategoryCode3());
        } else if (StringUtils.isNotBlank(response.getProcutCategoryCode2())) {
            spuCreateV2Command.setCustomCategoryId(response.getProcutCategoryCode2());
        } else if (StringUtils.isNotBlank(response.getProcutCategoryCode1())) {
            spuCreateV2Command.setCustomCategoryId(response.getProcutCategoryCode1());
        }

        //商品主图
        String productImages = response.getProductImages();
        List<MultimediaInfo> mainImages = new ArrayList<>();
        if (StringUtils.isNotBlank(productImages)) {
            String[] split = productImages.split(",");
            for (String image : split) {
                MultimediaInfo multimediaInfo = new MultimediaInfo();
                multimediaInfo.setType(MultimediaType.IMAGE);
                multimediaInfo.setFileUrl(image);
                mainImages.add(multimediaInfo);
            }
        }
        spuCreateV2Command.setMainImages(mainImages);

        //skuList
        spuCreateV2Command.setSkuList(buildSkuList(ecCangERPParams, response));

        //原产国
        spuCreateV2Command.setCountryOfOriginCode(CountryCode.CN);

        //供应商信息
        spuCreateV2Command.setDpsOuterCode(response.getDefaultSupplierCode());
        spuCreateV2Command.setDpsOuterCodeType(DPSOuterCodeType.ERP_ECCANG);
/*        if (StringUtils.isNotBlank(response.getDefaultSupplierCode())) {
            EcCangERPGetSupplierListRequest ecCangERPGetSupplierListRequest = new EcCangERPGetSupplierListRequest();
            ecCangERPGetSupplierListRequest.setPage(1);
            ecCangERPGetSupplierListRequest.setPageSize(1);
            EcCangERPGetSupplierListRequest.Condition condition = new EcCangERPGetSupplierListRequest.Condition();
            condition.setSupplierCode(response.getDefaultSupplierCode());
            ecCangERPGetSupplierListRequest.setCondition(condition);
            EcCangApiBaseResult<String> supplierList = eccangERPApiSveImpl.getSupplierList(thirdPartySystemId, ecCangERPGetSupplierListRequest);
            if (StringUtils.isNotBlank(supplierList.getData())) {
                List<EcCangERPGetSupplierListResponse> responses = JSON.parseArray(supplierList.getData(), EcCangERPGetSupplierListResponse.class);
                EcCangERPGetSupplierListResponse ecresponse = responses.get(0);
                spuCreateV2Command.setDpsName(ecresponse.getSupplierName());
                spuCreateV2Command.setDpsOuterCode(ecresponse.getSupplierCode());
                spuCreateV2Command.setDpsOuterCodeType(DPSOuterCodeType.ERP_ECCANG);
            }
        }*/
        //是否尝试创建
        spuCreateV2Command.setIsTryCreate(true);

        //是否自动审核通过
        spuCreateV2Command.setIsAutoAuditPass(true);

        //操作人
        spuCreateV2Command.setOperator("tpsi");

        return spuCreateV2Command;
    }

    //构建商品类目信息
    private void setCategoryInfo(SupplierSpuCreateV2Command supplierSpuCreateV2Command, Map<String, ThirdPartyMappingInfo> categoryMapping, EcCangERPGetProductListResponse response) {
        //String path = item.getCategory_name_one().concat("/".concat(item.getCategory_name_sub()).concat("/").concat(item.getCategory_name_two()));
        String platformCategoryId = null;
        if (StringUtils.isNotBlank(response.getProcutCategoryCode3())) {
            platformCategoryId = response.getProcutCategoryCode3();
        } else if (StringUtils.isNotBlank(response.getProcutCategoryCode2())) {
            platformCategoryId = response.getProcutCategoryCode2();
        } else if (StringUtils.isNotBlank(response.getProcutCategoryCode1())) {
            platformCategoryId = response.getProcutCategoryCode1();
        }
        try {
            if (StringUtils.isNotBlank(platformCategoryId)) {
                ThirdPartyMappingInfo thirdPartyMappingInfo = categoryMapping.get(platformCategoryId);
                CategoryInfo categoryInfo = categoryRpc.getCategoryById(thirdPartyMappingInfo.getSourceId());
                if (ObjectUtils.isNotEmpty(categoryInfo)) {
                    supplierSpuCreateV2Command.setCategoryId(categoryInfo.getCategoryId());
                    supplierSpuCreateV2Command.setMgmtCategoryLevel(CategoryLevel.getByValue(categoryInfo.getCategoryLevel()));
                }
            } else {
                throw new RuntimeException();
            }
        } catch (Exception e) {
            log.error("ECCANG商品设置类目失败，goodId: {} categorlyMapping: {} targetId: {}", response.getProductSku(), JSON.toJSONString(categoryMapping), platformCategoryId);
        }
    }

    //spu描述信息
    public List<SupplierSpuDescInfo> buildDescInfos(EcCangERPGetProductListResponse response) {
        List<SupplierSpuDescInfo> supplierSpuDescInfos = new ArrayList<>();
        SupplierSpuDescInfo supplierSpuDescInfoZh = new SupplierSpuDescInfo();
        SupplierSpuDescInfo supplierSpuDescInfoEn = new SupplierSpuDescInfo();

        //语言
        supplierSpuDescInfoEn.setLocale(LanguageLocaleType.en_US);
        supplierSpuDescInfoZh.setLocale(LanguageLocaleType.zh_CN);

        //标题
        supplierSpuDescInfoEn.setTitle(response.getProductTitleEn());
        supplierSpuDescInfoZh.setTitle(response.getProductTitle());

        //商家品牌名称
        supplierSpuDescInfoZh.setCustomBrandName(response.getBrandName());
        supplierSpuDescInfoEn.setCustomBrandName(response.getBrandName());

        //商家类目名称
        if (StringUtils.isNotBlank(response.getProcutCategoryName3())) {
            supplierSpuDescInfoZh.setCustomCategoryName(response.getProcutCategoryName3());
        } else if (StringUtils.isNotBlank(response.getProcutCategoryName2())) {
            supplierSpuDescInfoZh.setCustomCategoryName(response.getProcutCategoryName2());
        } else if (StringUtils.isNotBlank(response.getProcutCategoryName1())) {
            supplierSpuDescInfoZh.setCustomCategoryName(response.getProcutCategoryName1());
        }

        //文本描述
        supplierSpuDescInfoEn.setTextDesc(response.getPdDesc());
        supplierSpuDescInfoZh.setTextDesc(response.getPdDesc());

        supplierSpuDescInfoEn.setTransSourceType("tpsi");
        supplierSpuDescInfoZh.setTransSourceType("tpsi");
        supplierSpuDescInfoEn.setTransProvider("ECCANG");
        supplierSpuDescInfoZh.setTransProvider("ECCANG");
        supplierSpuDescInfoEn.setTransBaseLocate(LanguageLocaleType.en_US);
        supplierSpuDescInfoZh.setTransBaseLocate(LanguageLocaleType.zh_CN);

        supplierSpuDescInfos.add(supplierSpuDescInfoEn);
        supplierSpuDescInfos.add(supplierSpuDescInfoZh);
        return supplierSpuDescInfos;
    }

    public List<SupplierSkuCreateInfo> buildSkuList(EcCangERPParams ecCangERPParams, EcCangERPGetProductListResponse response) {
        List<SupplierSkuCreateInfo> supplierSkuCreateInfos = new ArrayList<>();
        SupplierSkuCreateInfo skuCreateInfo = new SupplierSkuCreateInfo();

        skuCreateInfo.setCustomCode(response.getProductSku());

        skuCreateInfo.setRefProductLink(response.getRefUrl());

        skuCreateInfo.setMoq(1);

        BigDecimal bigDecimal = new BigDecimal("100");
        if (ObjectUtils.isNotEmpty(response.getProductNetWeight())) {
            skuCreateInfo.setNetWeight(response.getProductNetWeight());
        }

        if (ObjectUtils.isNotEmpty(response.getProductWeight())) {
            skuCreateInfo.setGrossWeight(response.getProductWeight());
        }

        if (ObjectUtils.isNotEmpty(response.getProductLength())) {
            skuCreateInfo.setPackingLength(response.getProductLength().divide(bigDecimal, 3, RoundingMode.HALF_UP));
        }

        if (ObjectUtils.isNotEmpty(response.getProductWidth())) {
            skuCreateInfo.setPackingWidth(response.getProductWidth().divide(bigDecimal, 3, RoundingMode.HALF_UP));
        }

        if (ObjectUtils.isNotEmpty(response.getProductHeight())) {
            skuCreateInfo.setPackingHeight(response.getProductHeight().divide(bigDecimal, 3, RoundingMode.HALF_UP));
        }

        //主规格
        SupplierSkuMainSpecInfo mainSpecInfo = new SupplierSkuMainSpecInfo();
        if (StringUtils.isNotBlank(response.getProductImages())) {
            String[] split = response.getProductImages().split(",");
            MultimediaInfo image = new MultimediaInfo();
            image.setType(MultimediaType.IMAGE);
            image.setFileUrl(split[0]);
            mainSpecInfo.setImage(image);
            skuCreateInfo.setMainSpecInfo(mainSpecInfo);
        }

        //规格
        String productSpecs = response.getProductSpecs();
        if (StringUtils.isNotBlank(productSpecs)) {
            productSpecs = productSpecs.replace("'", "");
            productSpecs = productSpecs.replace("]", "");
            productSpecs = productSpecs.replace("[", "");
            productSpecs = productSpecs.replace("}", "");
            productSpecs = productSpecs.replace("{", "");
            String[] split = productSpecs.split(",");
            List<SupplierSkuSpecInfo> specs = new ArrayList<>();
            for (String specStr : split) {
                String[] spec = specStr.split(":");
                SupplierSkuSpecInfo supplierSkuSpecInfo = new SupplierSkuSpecInfo();
                supplierSkuSpecInfo.setSpecName(spec[0]);
                supplierSkuSpecInfo.setSpecValue(spec[1]);
                if (mainSpecInfo.getSpec() == null) {
                    mainSpecInfo.setSpec(supplierSkuSpecInfo);
                }
                specs.add(supplierSkuSpecInfo);
            }
            skuCreateInfo.setSpecs(specs);
        }

        //扩展信息
        SupplierSkuCreateExtendInfo extendInfo = new SupplierSkuCreateExtendInfo();
        //库存
        extendInfo.setReferenceStockNum(0);

        //供货信息
        List<SupplierItemCreateOrUpdate4SpuCommand> supplierItems = new ArrayList<>();
        SupplierItemCreateOrUpdate4SpuCommand supplierItemCreateOrUpdate4SpuCommand = new SupplierItemCreateOrUpdate4SpuCommand();
        supplierItemCreateOrUpdate4SpuCommand.setSaleMode(SupplierItemSaleMode.COUNTRY);
        supplierItemCreateOrUpdate4SpuCommand.setCountry("CN");
        supplierItemCreateOrUpdate4SpuCommand.setWarehouseId(ecCangERPParams.getWarehouse());
        supplierItemCreateOrUpdate4SpuCommand.setSupplyPriceCurrency(response.getCurrency_code());
        BigDecimal lin = BigDecimal.valueOf(0);
        BigDecimal sp_unit_price = response.getSp_unit_price();

        if (sp_unit_price == null || (sp_unit_price.compareTo(lin) == 0 || sp_unit_price.compareTo(lin) < 0)) {
            throw new ServiceException(new BaseErrorInfo("PRICE_CODE", "商品价格不合法"));
        }
        supplierItemCreateOrUpdate4SpuCommand.setSupplyPrice(response.getSp_unit_price());
        supplierItems.add(supplierItemCreateOrUpdate4SpuCommand);
        extendInfo.setSupplierItems(supplierItems);
        skuCreateInfo.setExtendInfo(extendInfo);
        supplierSkuCreateInfos.add(skuCreateInfo);
        return supplierSkuCreateInfos;
    }

    //更新商品价格
    public void updatePrice(ThirdPartySystem thirdPartySystem, EcCangERPGetProductListResponse response) {
        OpenSupplierAdjustSupplyPriceReq req = new OpenSupplierAdjustSupplyPriceReq();
        JSONObject jsonObject = JSON.parseObject(thirdPartySystem.getParams());
        String supplierId = jsonObject.get("supplierId").toString();
        req.setSupplierId(supplierId);
        req.setCountry("CN");
        req.setSupplyPriceCurrency("CNY");
        try {
            req.setCustomSkuCode(String.valueOf(response.getProductSku()));
            req.setSupplyPrice(response.getSp_unit_price());
            //供货明细表
            List<OpenSupplierItemCreateCombineDetailInfo> detailInfos = new ArrayList<>();
            OpenSupplierItemCreateCombineDetailInfo info = new OpenSupplierItemCreateCombineDetailInfo();
            info.setCombineNum(1);
            info.setSupplyPriceCurrency(response.getCurrency_code());
            detailInfos.add(info);
            info.setSupplyPrice(response.getSp_unit_price());
            req.setCombineDetails(detailInfos);

            openSupplierProductRpc.adjustSupplyPrice(req);
        } catch (ServiceException e) {
            log.error("更新商品价格：supplier: {}  supplierSkuId: {} message: {}", supplierId, response.getProductSku(), e.getMessage());
        }
    }

    //更新库存
    private void updateStock(ThirdPartySystem thirdPartySystem, EcCangERPGetProductListResponse response) {
        EcCangERPParams ecCangERPParams = JSON.parseObject(thirdPartySystem.getParams(), EcCangERPParams.class);
        String supplierId = ecCangERPParams.getSupplierId();

        OpenSupplierUpdateStock4BatchReq openSupplierUpdateStock4BatchReq = new OpenSupplierUpdateStock4BatchReq();

        try {
            List<OpenSupplierUpdateStockReq> list = new ArrayList<>();
            OpenSupplierUpdateStockReq req = new OpenSupplierUpdateStockReq();
            req.setBizSerialNumber(UUID.randomUUID().toString().replace("-", "").substring(0, 18));
            req.setSupplierId(supplierId);
            req.setCustomWarehouseCode(ecCangERPParams.getWarehouse());
            req.setCustomSkuCode(response.getProductSku());
            req.setStockNum(0);
            list.add(req);
            openSupplierUpdateStock4BatchReq.setReqs(list);

            openSupplierProductRpc.updateStockBatch(openSupplierUpdateStock4BatchReq);
        } catch (Exception e) {
            log.error("更新库存失败：supplier: {}  supplierSkuIds: {} message: {}", supplierId, openSupplierUpdateStock4BatchReq.getReqs().stream().map(OpenSupplierUpdateStockReq::getCustomSkuCode).collect(Collectors.toList()), e.getMessage());
        }
    }

    private ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }
}
