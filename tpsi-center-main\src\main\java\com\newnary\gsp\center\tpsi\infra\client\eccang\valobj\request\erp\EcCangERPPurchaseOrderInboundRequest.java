package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp;

import java.util.List;

public class EcCangERPPurchaseOrderInboundRequest {

    public String poCode;
    public String refrenceNo;
    public List<Product> productList;
    public Integer supplierId;
    public String supplierName;
    public Integer warehouseId;
    public String company;

    public static class Product {

        public String productSku;

        public Integer receivedQty;
    }
}
