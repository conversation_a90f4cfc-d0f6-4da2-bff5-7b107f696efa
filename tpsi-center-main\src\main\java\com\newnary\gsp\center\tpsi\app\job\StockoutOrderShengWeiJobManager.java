package com.newnary.gsp.center.tpsi.app.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.distributed.tools.concurrent.template.DConcurrentTemplate;
import com.newnary.gsp.center.logistics.api.delivery.dto.TrackInfoDTO;
import com.newnary.gsp.center.logistics.api.delivery.dto.TransportOrderPackageDTO;
import com.newnary.gsp.center.logistics.api.delivery.enums.StockoutOrderState;
import com.newnary.gsp.center.logistics.api.delivery.request.StockoutOrderThirdPushedCommand;
import com.newnary.gsp.center.logistics.api.delivery.request.StockoutOrderUpdateTrackCommand;
import com.newnary.gsp.center.logistics.api.delivery.request.StockoutOrderWithDeliveryQueryCommand;
import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderDetailInfo;
import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderDetailItemInfo;
import com.newnary.gsp.center.logistics.api.delivery.response.StockoutOrderInfo;
import com.newnary.gsp.center.logistics.api.delivery.response.TransportOrderDetailInfo;
import com.newnary.gsp.center.product.api.product.response.ChannelSaleItemDetailInfo;
import com.newnary.gsp.center.product.api.product.response.SupplierSkuDetailInfo;
import com.newnary.gsp.center.purchase.api.order.request.BatchTakeStockCommand;
import com.newnary.gsp.center.purchase.api.order.request.EntryScanningCommand;
import com.newnary.gsp.center.purchase.api.order.response.PurchaseOrderGoodsInfo;
import com.newnary.gsp.center.purchase.api.order.response.PurchaseOrderInfo;
import com.newnary.gsp.center.purchase.api.product.request.SpuSkuSupplierCommand;
import com.newnary.gsp.center.tpsi.app.service.eccang.StockoutPoMgmtApp;
import com.newnary.gsp.center.tpsi.infra.client.eccang.params.EcCangERPParams;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.wms.EcCangFreightGetOrderInfoResponse;
import com.newnary.gsp.center.tpsi.infra.model.DeliveryOrderEcCangApiAssociation;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.creator.DeliveryOrderEcCangApiAssociationCreator;
import com.newnary.gsp.center.tpsi.infra.model.vo.DeliveryOrderItemEcCangApiAssociation;
import com.newnary.gsp.center.tpsi.infra.repository.IDeliveryOrderEcCangApiAssociationRepository;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.infra.rpc.*;
import com.newnary.gsp.center.tpsi.infra.translator.DeliveryOrderEcCangApiAssociationTranslator;
import com.newnary.gsp.center.tpsi.infra.translator.SWDataPushCommandTranslator;
import com.newnary.gsp.center.tpsi.service.eccang.IEccangWMSApiSve;
import com.newnary.gsp.center.trade.api.order.response.OrderDTO;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.job.core.handler.annotation.Job;
import com.newnary.spring.cloud.domain.Asserts;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Component
@Slf4j
public class StockoutOrderShengWeiJobManager {

    private static final String AUTO_PUSH_STOCKOUT_WAITING_ORDER_2_ECCANG_PREFIX = "AUTO_PUSH_STOCKOUT_WAITING_ORDER_2_ECCANG";

    private static final String AUTO_CHECK_STOCKOUTING_ORDER_BASE_ECCANG_PREFIX = "AUTO_CHECK_STOCKOUTING_ORDER_BASE_ECCANG";

    private static final String AUTO_CHECK_STOCKOUTFINISH_ORDER_BASE_ECCANG_PREFIX = "AUTO_CHECK_STOCKOUTFINISH_ORDER_BASE_ECCANG_PREFIX";

    private static final String LOCK_DELIVERY_ORDER_ECCANG_API_ASSOCIATION_TABLE_PREFIX = "LOCK_DELIVERY_ORDER_ECCANG_API_ASSOCIATION_TABLE";

    @Resource
    private DeliveryOrderRpc deliveryOrderRpc;
    @Resource
    private StockoutOrderRpc stockoutOrderRpc;
    @Resource
    private TransportOrderRpc transportOrderRpc;
    @Resource
    private TradeOrderRpc tradeOrderRpc;
    @Resource
    private SaleItemRpc saleItemRpc;
    @Resource
    private PurchaseRpc purchaseRpc;
    @Resource
    private IEccangWMSApiSve eccangWMSApiSve;
    @Resource
    private IDeliveryOrderEcCangApiAssociationRepository deliveryOrderEcCangApiAssociationRepository;
    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;
    @Resource
    private StockoutPoMgmtApp stockoutPoMgmtApp;


    @Job("autoPushStockoutWaittingOrder2ShengWeiPurchase")
    public ReturnT<String> autoPushStockoutWaittingOrder2ShengWeiPurchase(String param) {
        log.info("定时任务推送系统待发货出库单到采购系统创建采购商品及供应商 - 开始 --- {}", new Date());
        //根据参数获取需要执行的第三方系统id
        JSONObject paramObject = JSONObject.parseObject(param);
        //易仓WMS系统id
        String wmsTpsId = paramObject.getString("wmsTpsId");
        Asserts.assertNotBlank(wmsTpsId, "wms系统id不为空");

        //易仓ERP系统id (目前只是用于获取参数，并不在易仓下订单)
        String erpTpsId = paramObject.getString("erpTpsId");

        ThirdPartySystem thirdPartySystem = loadSystem(erpTpsId);
        EcCangERPParams params = JSON.parseObject(thirdPartySystem.getParams(), EcCangERPParams.class);
        String stockoutOrderId = paramObject.getString("stockoutOrderId");
        //查询租户下的易仓店铺，国内仓收货地址
        log.info("开始获取api订单、商品创建请求参数");
        DConcurrentTemplate.tryLockMode(
                AUTO_PUSH_STOCKOUT_WAITING_ORDER_2_ECCANG_PREFIX.concat(param),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    log.info("执行 核心逻辑--- {}", new Date());
                    StockoutOrderWithDeliveryQueryCommand queryCommand = new StockoutOrderWithDeliveryQueryCommand();
                    queryCommand.setStockoutOrderState(StockoutOrderState.STOCKOUTING.getState());
                    queryCommand.setThirdPushed(false);
                    if (StringUtils.isNotEmpty(stockoutOrderId)){
                        queryCommand.setStockoutOrderId(stockoutOrderId);
                    }

                    queryCommand.setDeliveryOrderSupplierId(params.getSupplierId());
                    queryCommand.setDeliveryOrderChannelId(params.getChannelId());
                    List<StockoutOrderInfo> stockoutOrderInfoList = stockoutOrderRpc.getListWithDelivery(queryCommand);
                    if (CollectionUtils.isNotEmpty(stockoutOrderInfoList)) {
                        int resultSize = stockoutOrderInfoList.size();
                        log.info("获取的待出库状态出库单数量为{}", resultSize);
                        stockoutOrderInfoList.forEach(stockoutOrderInfo -> {
                            try {
                                pushPurchaseProcessPreconditions(wmsTpsId, stockoutOrderInfo);
                            } catch (Exception e) {
                                log.error("{}处理已出库单出错{}", stockoutOrderInfo.getStockoutOrderId(), e.getMessage());
                            }
                        });
                    }
                }
        );

        log.info("定时任务推送系统待发货出库单到采购系统创建采购商品及供应商 - 结束 --- {}", new Date());
        return ReturnT.SUCCESS;
    }

    @Job("autoCheckStockoutingOrderBaseShengWei")
    public ReturnT<String> autoCheckStockoutingOrderBaseShengWei(String param) {
        log.info("基于采购系统API定时任务检查出库中出库单 - 开始 --- {}", new Date());
        //从参数中获取需要执行的第三方系统bizId
        JSONObject paramObject = JSONObject.parseObject(param);
        //易仓ERP系统id
        String erpTpsId = paramObject.getString("erpTpsId");
        Asserts.assertNotBlank(erpTpsId, "erp系统id不为空");
        //易仓WMS系统id
        String wmsTpsId = paramObject.getString("wmsTpsId");
        Asserts.assertNotBlank(wmsTpsId, "wms系统id不为空");

        String stockoutOrderId = paramObject.getString("stockoutOrderId");
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(erpTpsId);
        EcCangERPParams params = JSON.parseObject(thirdPartySystem.getParams(), EcCangERPParams.class);

        DConcurrentTemplate.tryLockMode(
                AUTO_CHECK_STOCKOUTING_ORDER_BASE_ECCANG_PREFIX.concat(param),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    log.info("执行 核心逻辑--- {}", new Date());
                    StockoutOrderWithDeliveryQueryCommand queryCommand = new StockoutOrderWithDeliveryQueryCommand();
                    queryCommand.setStockoutOrderState(StockoutOrderState.STOCKOUTING.getState());
                    if (StringUtils.isNotEmpty(stockoutOrderId)) {
                        queryCommand.setStockoutOrderId(stockoutOrderId);
                    }
                    queryCommand.setDeliveryOrderSupplierId(params.getSupplierId());
                    queryCommand.setDeliveryOrderChannelId(params.getChannelId());
                    List<StockoutOrderInfo> stockoutOrderInfoList = stockoutOrderRpc.getListWithDelivery(queryCommand);
                    if (CollectionUtils.isNotEmpty(stockoutOrderInfoList)) {
                        int resultSize = stockoutOrderInfoList.size();
                        log.info("获取的待出库状态出库单数量为{}", resultSize);
                        stockoutOrderInfoList.forEach(stockoutOrderInfo -> {
                            try {
                                if (stockoutOrderInfo.getThirdPushed()) {
                                    //调用升威创建采购计划，采购计划接口
                                    coreHandleStockoutingOrder(stockoutOrderInfo, wmsTpsId, erpTpsId);
                                } else {
                                    //未在升威采购推送商品以及供应商创建
                                    pushPurchaseProcessPreconditions(wmsTpsId, stockoutOrderInfo);
                                }
                            } catch (Exception e) {
                                log.error("{}处理已出库单出错{}", stockoutOrderInfo.getStockoutOrderId(), e.getMessage());
                            }
                        });
                    }
                });

        log.info("基于采购系统API定时任务检查出库中出库单 - 结束 --- {}", new Date());
        return ReturnT.SUCCESS;
    }

    @Job("autoCheckStockoutFinishOrderBaseShengWei")
    public ReturnT<String> autoCheckStockoutFinishSyncPurchaseOrder(String param) {
        log.info("基于易仓API定时任务检查出库中出库单 - 开始 --- {}", new Date());
        //从参数中获取需要执行的第三方系统bizId
        JSONObject paramObject = JSONObject.parseObject(param);
        //易仓ERP系统id
        String erpTpsId = paramObject.getString("erpTpsId");
        Asserts.assertNotBlank(erpTpsId, "erp系统id不为空");

        String stockoutOrderId = paramObject.getString("stockoutOrderId");
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(erpTpsId);
        EcCangERPParams params = JSON.parseObject(thirdPartySystem.getParams(), EcCangERPParams.class);

        DConcurrentTemplate.tryLockMode(
                AUTO_CHECK_STOCKOUTFINISH_ORDER_BASE_ECCANG_PREFIX.concat(param),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    log.info("执行 核心逻辑--- {}", new Date());
                    StockoutOrderWithDeliveryQueryCommand queryCommand = new StockoutOrderWithDeliveryQueryCommand();
                    queryCommand.setStockoutOrderState(StockoutOrderState.STOCKOUT_FINISH.getState());
                    if (StringUtils.isNotEmpty(stockoutOrderId)) {
                        queryCommand.setStockoutOrderId(stockoutOrderId);
                    }
                    queryCommand.setDeliveryOrderSupplierId(params.getSupplierId());
                    queryCommand.setDeliveryOrderChannelId(params.getChannelId());
                    List<StockoutOrderInfo> stockoutOrderInfoList = stockoutOrderRpc.getListWithDelivery(queryCommand);
                    if (CollectionUtils.isNotEmpty(stockoutOrderInfoList)) {
                        int resultSize = stockoutOrderInfoList.size();
                        log.info("获取的已完成状态出库单数量为{}", resultSize);
                        stockoutOrderInfoList.forEach(stockoutOrderInfo -> {
                            try {
                                //用于完结采购系统的采购单
//                                completePurchaseOrder(stockoutOrderInfo);
                                stockoutPoMgmtApp.checkStockoutFinishSyncPurchaseOrder(stockoutOrderInfo.getStockoutOrderId());
                            } catch (Exception e) {
                                log.error("{}处理已出库单出错{}", stockoutOrderInfo.getStockoutOrderId(), e.getMessage());
                            }
                        });
                    }
                });

        log.info("基于升威采购API定时任务检查出库中出库单 - 结束 --- {}", new Date());
        return ReturnT.SUCCESS;
    }

    private void completePurchaseOrder(StockoutOrderInfo stockoutOrderInfo) {
        log.info("现在获取出库单{} 关联的采购单",stockoutOrderInfo.getStockoutOrderId());
        //获取采购单详细信息
        List<PurchaseOrderInfo> purchaseOrderInfo = purchaseRpc.getPurchaseOrderByRefOrderNumber(stockoutOrderInfo.getStockoutOrderId());
        if (CollectionUtils.isNotEmpty(purchaseOrderInfo)){
            purchaseOrderInfo.forEach(purchaseOrder ->{
                if (purchaseOrder.getOrderStatus()==50 || purchaseOrder.getOrderStatus()==55){
                    //已经完结的采购单
                    return;
                }
                //签收
                EntryScanningCommand scanningCommand = new EntryScanningCommand();
                scanningCommand.setScanArrivalType("trackingNumber");
                scanningCommand.setCode(purchaseOrder.getTrackingNumber());
                scanningCommand.setPurchaseOrderId(purchaseOrder.getId());
                purchaseRpc.scanSignList4ThirdParty(scanningCommand);

                BatchTakeStockCommand takeStockCommand = new BatchTakeStockCommand();
                List<PurchaseOrderGoodsInfo> goodsInfos = new ArrayList<>();
                purchaseOrder.getPurchaseOrderGoodsList().forEach(info->{
                    //收货
                    info.setReceivingQuantity(info.getPurchaseQuantity());
                    goodsInfos.add(info);
                    takeStockCommand.setOrderGoodsList(goodsInfos);
                });
                purchaseRpc.batchTakeStock4ThirdParty(takeStockCommand);
            });
        }

    }

    private void pushPurchaseProcessPreconditions(String wmsTpsId, StockoutOrderInfo stockoutOrderInfo) {
        log.info("现在获取出库单{}关联的发货单{}", stockoutOrderInfo.getStockoutOrderId(), stockoutOrderInfo.getReferenceId());
        //获取发货单详细信息
        DeliveryOrderDetailInfo deliveryOrderDetailInfo = deliveryOrderRpc.getDetailInfo(stockoutOrderInfo.getReferenceId());
        if (null != deliveryOrderDetailInfo) {
            log.info("发货单{}详情已获取", deliveryOrderDetailInfo.getDeliveryOrderId());
            //1.创建升威系统的商品，1688供应商
            boolean isCreateSucess = createPurchaseProductAndSupplier(deliveryOrderDetailInfo);

            //2.修改发货单状态
            updateStockoutOrderState(stockoutOrderInfo.getStockoutOrderId(), isCreateSucess);

            //3.记录出库单与ERP和WMS的关联
            if (isCreateSucess) {
                recordAssociationInformation(wmsTpsId, stockoutOrderInfo.getStockoutOrderId(), deliveryOrderDetailInfo);
            }
        } else {
            log.warn("出库单{}获取不到关联发货单{}", stockoutOrderInfo.getStockoutOrderId(), stockoutOrderInfo.getReferenceId());
        }
    }

    private void updateStockoutOrderState(String stockoutOrderId, boolean isCreateSucess) {
        if (isCreateSucess) {
            try {
                StockoutOrderThirdPushedCommand command = new StockoutOrderThirdPushedCommand();
                command.setStockoutOrderId(stockoutOrderId);
                command.setThirdOrderId("FAIL-UNCONFIRMED CREATION");
                stockoutOrderRpc.thirdPushed(command);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("{}调用受理方法出错", stockoutOrderId);
            }
        }
    }

    /**
     * 记录关联关系表
     *
     * @param wmsTpsId
     * @param stockoutOrderId
     * @param deliveryOrderDetailInfo
     * @param
     */
    private void recordAssociationInformation(String wmsTpsId, String stockoutOrderId, DeliveryOrderDetailInfo deliveryOrderDetailInfo) {
        //todo czh  目前关联关系已经没有易仓订单，后续要改表字段名，暂时先用
        createAssociation(deliveryOrderDetailInfo.getTradeOrderId(), null, wmsTpsId);
    }

    private boolean createPurchaseProductAndSupplier(DeliveryOrderDetailInfo deliveryOrderDetailInfo) {
        AtomicBoolean isCreateSuccess = new AtomicBoolean(false);
        deliveryOrderDetailInfo.items.forEach(item -> {
            //1. 升威采购系统构建商品以及供应商等信息
            SupplierSkuDetailInfo skuDetailInfo = saleItemRpc.querySaleItem(deliveryOrderDetailInfo.getChannelId(), item.getSaleItemCode()).skuDetailInfo;
            isCreateSuccess.set(createSWBasicInfo(skuDetailInfo, item));
        });

        return isCreateSuccess.get();
    }


    /**
     * 升威系统创建基础信息 （商品以及供应商）
     *
     * @param supplierSku
     * @param item
     * @return
     */
    private boolean createSWBasicInfo(SupplierSkuDetailInfo supplierSku, DeliveryOrderDetailItemInfo item) {
        SpuSkuSupplierCommand spuSkuSupplierCommand = SWDataPushCommandTranslator.constructSpuSkuSupplierCommand(supplierSku, item);
        String isSuccess = purchaseRpc.syncProductAndSupplier(spuSkuSupplierCommand);
        log.info("创建升威基础信息是否成功---{}", isSuccess);
        return StringUtils.equals("SUCCESS", isSuccess);
    }

    /**
     * 对出库单详情的处理逻辑---升威版
     *
     * @param stockoutOrderInfo
     */
    private void coreHandleStockoutingOrder(StockoutOrderInfo stockoutOrderInfo, String wmsTpsId, String erpTpsId) {
        log.info("现在获取出库单{}关联的发货单{}", stockoutOrderInfo.getStockoutOrderId(), stockoutOrderInfo.getReferenceId());
        try {
            //第三步 获取发货单详细信息
            DeliveryOrderDetailInfo deliveryOrderDetailInfo = deliveryOrderRpc.getDetailInfo(stockoutOrderInfo.getReferenceId());
            if (null != deliveryOrderDetailInfo) {
                log.info("发货单{}详情已获取", deliveryOrderDetailInfo.getDeliveryOrderId());
                DConcurrentTemplate.tryLockMode(
                        LOCK_DELIVERY_ORDER_ECCANG_API_ASSOCIATION_TABLE_PREFIX.concat(deliveryOrderDetailInfo.getDeliveryOrderId()),
                        lock -> lock.tryLock(3, TimeUnit.SECONDS),
                        () -> {
                            Optional<DeliveryOrderEcCangApiAssociation> ecCangApiAssociation = deliveryOrderEcCangApiAssociationRepository.loadByDeliveryOrderId(deliveryOrderDetailInfo.getDeliveryOrderId());
                            if (ecCangApiAssociation.isPresent()) {
                                DeliveryOrderEcCangApiAssociation association = ecCangApiAssociation.get();
                                //获取采购计划信息、获取升威采购单信息
                                checkPurchasePlan(association, stockoutOrderInfo, deliveryOrderDetailInfo, wmsTpsId);
                                deliveryOrderEcCangApiAssociationRepository.store(association);
                            } else {
                                //提示出错
                                log.error("{}找不到已有关联信息", stockoutOrderInfo.getStockoutOrderId());
                                createAssociation(deliveryOrderDetailInfo.getTradeOrderId(), erpTpsId, wmsTpsId);
                            }
                        });
            } else {
                log.warn("出库单{}获取不到关联发货单{}", stockoutOrderInfo.getStockoutOrderId(), stockoutOrderInfo.getReferenceId());
            }
        } catch (Exception e) {
            log.info("此出库中的出库单有异常{}", e.getMessage());
        }
    }

    private void createAssociation(String tradeOrderId, String erpTpsId, String wmsTpsId) {
        OrderDTO tradeOrder = tradeOrderRpc.getTradeOrder(tradeOrderId);
        tradeOrder.getOrderSubItemList().forEach(subItem -> {
            DeliveryOrderDetailInfo deliveryOrderDetailInfo = deliveryOrderRpc.getDetailInfo(subItem.getDeliveryOrderId());
            StockoutOrderInfo stockoutOrderInfo = stockoutOrderRpc.getStockoutOrder(deliveryOrderDetailInfo.getStockoutOrderId());
            if (ObjectUtils.isNotEmpty(stockoutOrderInfo)) {
                DeliveryOrderEcCangApiAssociationCreator associationCreator = DeliveryOrderEcCangApiAssociationTranslator.buildCreator(tradeOrder, deliveryOrderDetailInfo, stockoutOrderInfo.getStockoutOrderId(), null, stockoutOrderInfo.getThirdOrderId(), erpTpsId, wmsTpsId);
                associationCreator.getItems().forEach(item -> {
                    ChannelSaleItemDetailInfo channelSaleItemDetailInfo = saleItemRpc.querySaleItem(deliveryOrderDetailInfo.getChannelId(), item.getSaleItemCode());
                    JSONArray specsArray = new JSONArray();
                    if (CollectionUtils.isNotEmpty(channelSaleItemDetailInfo.getSkuDetailInfo().getSkuInfo().getSpecs())) {
                        channelSaleItemDetailInfo.getSkuDetailInfo().getSkuInfo().getSpecs().forEach(supplierSkuSpecInfo -> {
                            JSONObject specsJson = new JSONObject();
                            specsJson.put(supplierSkuSpecInfo.getSpecName(), supplierSkuSpecInfo.getSpecValue());
                            specsArray.add(specsJson);
                        });
                    }
                    item.setWmsProductStandard(specsArray.toJSONString());
                });

                //记录出库单与ERP和WMS的关联
                try {
                    deliveryOrderEcCangApiAssociationRepository.store(DeliveryOrderEcCangApiAssociation.createWith(associationCreator));
                    log.info("对接系统保存发货单关联成功 {}", subItem.getDeliveryOrderId());
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("{}保存发货单关联出错", subItem.getDeliveryOrderId());
                }
            } else {
                log.error("发货单{}缺失关联出库单或运输单", subItem.getDeliveryOrderId());
            }
        });
    }

    /**
     * 升威采购计划
     *
     * @param association
     * @param stockoutOrderInfo
     * @param deliveryOrderDetailInfo
     */
    private void checkPurchasePlan(DeliveryOrderEcCangApiAssociation association, StockoutOrderInfo stockoutOrderInfo, DeliveryOrderDetailInfo deliveryOrderDetailInfo, String wmsTpsId) {
        log.info("判断出库单号为{}的采购计划在升威上的状态", stockoutOrderInfo.getStockoutOrderId());
        String erpOrderSaleOrderCode = association.getErpOrderSaleOrderCode();
        //检索是否已经创建过采购计划，并且生成采购单
        if (StringUtils.isNotBlank(erpOrderSaleOrderCode) && erpOrderSaleOrderCode.startsWith("SUCCESS")) {
            //            PlanInfo purchasePlan = purchaseRpc.getPurchasePlan(erpOrderSaleOrderCode.split("-")[1]);
//            int status = Math.toIntExact(purchasePlan.status);
//            switch (status) {
//                case 0:
//                    //未处理
//                case 10:
//                    //部分生成，目前采购计划的初始化状态，跟0无差别，待完善
//                    log.info("状态：{},暂无需处理", status);
//                    association.setErpOrderStatus(status);
//                    break;
//                case 20:
//                    //全部生成，代表采购订已生成，读取信息
//                    //更新采购单信息,如果有货运单号,则更新集运订单产品包裹号
//                    association.setErpOrderStatus(status);
//                    checkPurchaseOrderTrackingNoSW(association, stockoutOrderInfo, purchasePlan.getId(), wmsTpsId);
//                    break;
//                case 30:
//                    //作废
//                    association.setErpOrderStatus(status);
//                    StockoutOrderConfirmCancelCommand stockoutOrderConfirmCancelCommand = new StockoutOrderConfirmCancelCommand();
//                    stockoutOrderConfirmCancelCommand.setStockoutOrderId(stockoutOrderInfo.getStockoutOrderId());
//                    stockoutOrderConfirmCancelCommand.setCancelType(CancelType.SUPPLIER_CANCEL.name());
//                    stockoutOrderRpc.confirmCancel(stockoutOrderConfirmCancelCommand);
//                    break;
//                default:
//                    throw new IllegalStateException("Unexpected value: " + purchasePlan.status);
//            }
        } else {
            //创建采购计划
            try {
                String purchasePlan = purchaseRpc.createPurchasePlan(SWDataPushCommandTranslator.constructPurchasePlanCommand(deliveryOrderDetailInfo,stockoutOrderInfo));
                association.setErpOrderSaleOrderCode(purchasePlan);
                //保存三方采购计划id
                StockoutOrderThirdPushedCommand command = new StockoutOrderThirdPushedCommand();
                command.setStockoutOrderId(stockoutOrderInfo.getStockoutOrderId());
                command.setThirdOrderId(purchasePlan);
                stockoutOrderRpc.thirdPushed(command);
            } catch (Exception e) {
                log.info("创建采购计划失败，原因{}", e.getMessage());
            }
        }
    }

    /**
     * 检查采购订单物流等信息---升威版
     *
     * @param association
     * @param purchasePlanId
     * @param thirdPartySystem
     */
    private void checkPurchaseOrderTrackingNoSW(DeliveryOrderEcCangApiAssociation association, StockoutOrderInfo stockoutOrderInfo, Long purchasePlanId, ThirdPartySystem thirdPartySystem) {
        //更新采购订单信息
        updatePurchaseOrderInfo(association, stockoutOrderInfo, purchasePlanId);

        //更新升威采购单本地运费
        BigDecimal purchaseShippingFee = new BigDecimal(association.getItems().get(0).getErpPurchaseOrderShippingFee());
        tradeOrderRpc.updatePurchaseShippingAmount(association.getDeliveryOrderId(), "CNY", purchaseShippingFee);

        //再更新易仓WMS集运订单包裹信息
        editFreightProduct(association, thirdPartySystem);
    }

    private void updatePurchaseOrderInfo(DeliveryOrderEcCangApiAssociation association, StockoutOrderInfo stockoutOrderInfo, Long purchasePlanId) {
        //查询出采购订单
        List<PurchaseOrderInfo> purchaseOrderInfos = purchaseRpc.getPurchaseOrderByPlanId(purchasePlanId);
        if (CollectionUtils.isNotEmpty(purchaseOrderInfos)) {
            //升威采购单是由供应商分组
            //供应商---采购单
            Map<String, Long> purchaseSupplierOrderId = purchaseOrderInfos.stream().collect(Collectors.toMap(PurchaseOrderInfo::getSupplierName, PurchaseOrderInfo::getId));
            //计算出所有运费
            BigDecimal allShippingFee = purchaseOrderInfos.stream().map(PurchaseOrderInfo::getShippingFee).reduce(BigDecimal.ZERO, BigDecimal::add);
            //计算出所有采购费
            BigDecimal allAmountPayable = purchaseOrderInfos.stream().map(PurchaseOrderInfo::getAmountPayable).reduce(BigDecimal.ZERO, BigDecimal::add);
            //该出库单下所有的采购单承运商集合
            List<TrackInfoDTO> trackInfoDTOS = new ArrayList<>();
            log.info("{}获取易仓ERP采购单信息成功", purchaseSupplierOrderId);
            purchaseOrderInfos.stream().forEach(purchaseOrderInfo -> {
                /**
                 * 订单状态
                 *  0,"草稿",
                 *  10 "待下单",     20 "已下单",   30"待付款"，
                 *  40"已付款",      45"部分到货",  50 "全部到货"，
                 *  55"不等待剩余" ,  60"已上架",   70 "作废",
                 *  80"下单进行中" ,  90"驳回"
                 */
                Integer orderStatus = purchaseOrderInfo.getOrderStatus();

                if (orderStatus != 70 || orderStatus != 90) {
                    association.getItems().forEach(item -> {
                        //该采购单下的商品列表
                        List<String> orderGoodsIds = purchaseOrderInfo.getPurchaseOrderGoodsList().stream().map(PurchaseOrderGoodsInfo::getSku).collect(Collectors.toList());
                        if (orderGoodsIds.contains(item.getCustomCode())) {
                            //设置采购单
                            item.setErpPurchaseOrderCode(String.valueOf(purchaseOrderInfo.getId()));
                            //设置物流单号
                            item.setErpPurchaseOrderTrackingNo(purchaseOrderInfo.getTrackingNumber());
                            //设置采购单状态
                            item.setErpPurchaseOrderStatus(purchaseOrderInfo.getOrderStatus());
                        }
                        //运费统一计算
                        item.setErpPurchaseOrderShippingFee(allShippingFee.doubleValue());
                        //总采购金额统一计算
                        item.setErpPurchaseOrderPayableAmount(allAmountPayable.doubleValue());
                    });
                }

                if (StringUtils.isNotEmpty(purchaseOrderInfo.getTrackingNumber())) {
                    JSONArray trackingNumberArray = JSON.parseArray(purchaseOrderInfo.getTrackingNumberRecord());
                    JSONObject trackingNumberObject = (JSONObject) trackingNumberArray.get(trackingNumberArray.size() - 1);
                    //添加采购订单物流承运商
                    trackInfoDTOS.add(new TrackInfoDTO(trackingNumberObject.getString("trackingCompany"), trackingNumberObject.getString("trackingNumber")));
                }
            });
            if (CollectionUtils.isNotEmpty(trackInfoDTOS)) {
                StockoutOrderUpdateTrackCommand command = new StockoutOrderUpdateTrackCommand();
                command.setStockoutOrderId(stockoutOrderInfo.getStockoutOrderId());
                command.setTrackInfos(trackInfoDTOS);
                stockoutOrderRpc.updateTrack(command);
            }
        }
    }

    private void editFreightProduct(DeliveryOrderEcCangApiAssociation association, ThirdPartySystem thirdPartySystem) {
        if (StringUtils.isEmpty(association.getTransportOrderId())) {
            return;
        }
        TransportOrderDetailInfo transportOrderDetailInfo = transportOrderRpc.getDetail(association.getTransportOrderId());
        if (CollectionUtils.isEmpty(transportOrderDetailInfo.getOrderPackages()) || StringUtils.isEmpty(transportOrderDetailInfo.getOrderPackages().get(0).getThirdOrderId())) {
            return;
        }
        TransportOrderPackageDTO transportOrderPackage = transportOrderDetailInfo.getOrderPackages().get(0);
        association.setWmsOrderCode(transportOrderPackage.getThirdOrderId());
        EcCangApiBaseResult<String> getFreightOrderApiBaseResult = eccangWMSApiSve.freightGetOrderInfo(transportOrderPackage.getThirdOrderId(), thirdPartySystem);
        if (getFreightOrderApiBaseResult.getCode().equals("200") && getFreightOrderApiBaseResult.getMessage().equalsIgnoreCase("Success")) {
            List<EcCangFreightGetOrderInfoResponse> responseOrders = JSONObject.parseArray(getFreightOrderApiBaseResult.getData(), EcCangFreightGetOrderInfoResponse.class);
            if (CollectionUtils.isNotEmpty(responseOrders)) {
                responseOrders.forEach(respOrder -> {
                    if (respOrder.order_status == 1) {  //只有集运订单还是待签收状态下才能修改

                        Map<String, String> productMap = respOrder.product_data.stream().filter(Objects::nonNull).collect(Collectors.toMap(
                                object -> {
                                    return object.child_code;
                                },
                                object -> {
                                    return object.package_code;
                                }
                        ));
                        boolean needEdit = false;
                        for (DeliveryOrderItemEcCangApiAssociation item : association.getItems()) {
                            String packageCode = productMap.get(item.getWmsChildCode());
                            if ((StringUtils.isEmpty(packageCode) && StringUtils.isNotEmpty(item.getErpPurchaseOrderTrackingNo()))
                                    || (StringUtils.isNotEmpty(packageCode) && StringUtils.isNotEmpty(item.getErpPurchaseOrderTrackingNo()) && !packageCode.equals(item.getErpPurchaseOrderTrackingNo()))) {
                                needEdit = true;
                                break;
                            }
                        }

                        if (needEdit) {
                            //TODO 重新按运输单维度加载集运订单商品,暂时看不太合理,后续需调整改进
                            Optional<DeliveryOrderEcCangApiAssociation> transportAssociation = deliveryOrderEcCangApiAssociationRepository.loadByTransportOrderId(association.getTransportOrderId());
                            if (transportAssociation.isPresent()) {
                                EcCangApiBaseResult<String> freightCreateProductApiBaseResult = eccangWMSApiSve.freightCreateProduct(transportAssociation.get(), thirdPartySystem);
                                if (null != freightCreateProductApiBaseResult && freightCreateProductApiBaseResult.getCode().equals("200") && freightCreateProductApiBaseResult.getMessage().equalsIgnoreCase("Success")) {
                                    JSONArray productResult = JSONArray.parseArray(freightCreateProductApiBaseResult.getData());
                                    productResult.forEach(productObj -> {
                                        JSONObject productJson = (JSONObject) productObj;
                                        association.getItems().forEach(item -> {
                                            if (item.getWmsProductPid().equals(productJson.getInteger("pid"))) {
                                                item.setWmsProductPid(productJson.getInteger("pid"));
                                                item.setWmsChildCode(productJson.getString("child_code"));
                                                item.setWmsProductPackageCode(productJson.getString("package_code"));
                                            }
                                        });
                                    });
                                } else {
                                    //出错不处理，等待下次重新创建
                                    log.error("{}更新集运订单产品信息失败{}，数据为空", association.getWmsOrderCode(), freightCreateProductApiBaseResult == null ? "" : freightCreateProductApiBaseResult.getMessage());
                                }
                            } else {
                                log.error("{}运输单关联信息不存在,暂不处理");
                            }
                        }
                    }
                });
            }
        }
    }

    private ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }

}
