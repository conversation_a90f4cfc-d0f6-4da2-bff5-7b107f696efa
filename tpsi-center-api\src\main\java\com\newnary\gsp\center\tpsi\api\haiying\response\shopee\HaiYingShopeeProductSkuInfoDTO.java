package com.newnary.gsp.center.tpsi.api.haiying.response.shopee;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingShopeeProductSkuInfoDTO {

    /**
     * 商品变体id
     */
    private String sid;

    /**
     * 商品变体名称
     */
    private String sname;

    /**
     * 商品变体价格
     */
    private BigDecimal price;

    /**
     * 商品变体月销量
     * (已取消)
     */
    @Deprecated
    private Integer sold;

    /**
     * 商品变体库存数
     */
    private Integer stock;

}
