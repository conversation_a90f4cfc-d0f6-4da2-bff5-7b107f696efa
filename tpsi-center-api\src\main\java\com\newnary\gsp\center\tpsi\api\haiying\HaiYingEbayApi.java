package com.newnary.gsp.center.tpsi.api.haiying;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.api.base.common.PageList;
import com.newnary.gsp.center.tpsi.api.haiying.request.ebay.*;
import com.newnary.gsp.center.tpsi.api.haiying.response.ebay.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-13
 */
@RequestMapping("tpsi-center/haiying/ebay")
public interface HaiYingEbayApi {

    @PostMapping("getEbayProductList")
    CommonResponse<PageList<HaiYingEbayProductListDTO>> getEbayProductList(@RequestBody HaiYingEbayProductListCommand command);

    @PostMapping("getEbayProductDetailInfo")
    CommonResponse<List<HaiYingEbayProductDetailInfoDTO>> getEbayProductDetailInfo(@RequestBody HaiYingEbayProductDetailInfoCommand command);

    @PostMapping("getEbayCategoryTree")
    CommonResponse<List<HaiYingEbayCategoryTreeDTO>> getEbayCategoryTree(@RequestBody HaiYingEbayCategoryTreeCommand command);

    @PostMapping("getEbayTopCategoryInfo")
    CommonResponse<List<HaiYingEbayTopCategoryInfoDTO>> getEbayTopCategoryInfo(@RequestBody HaiYingEbayTopCategoryInfoCommand command);

    @PostMapping("getEbayCategoryDetail")
    CommonResponse<PageList<HaiYingEbayCategoryDetailDTO>> getEbayCategoryDetail(@RequestBody HaiYingEbayCategoryDetailCommand command);

}
