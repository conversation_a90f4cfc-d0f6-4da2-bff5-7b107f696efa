package com.newnary.gsp.center.tpsi.app.service.wms;

import com.newnary.api.base.common.PageList;
import com.newnary.gsp.center.logistics.api.warehouse.response.WarehouseLiteRes;
import com.newnary.gsp.center.stock.api.logistics.response.BatchStockOnlyStockInfo;
import com.newnary.gsp.center.tpsi.infra.rpc.BatchStockRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.LogisticsRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.SupplierStockApiRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.WmsBatchStockRpc;
import com.newnary.gsp.center.wms.api.stock.request.BatchBinLocationStockPageQueryReq;
import com.newnary.gsp.center.wms.api.stock.response.BatchStockRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class StockSyncMgmtApp {

    @Resource
    private LogisticsRpc logisticsRpc;

    @Resource
    private WmsBatchStockRpc wmsBatchStockRpc;

    @Resource
    private SupplierStockApiRpc supplierStockApiRpc;

    @Resource
    private BatchStockRpc batchStockRpc;

    public void syncSupplierSkuStock(String supplierSkuId, String warehouseId, String shipperId) {
        // 1.根据货主id跟三方仓库id查询出供应商id
        List<WarehouseLiteRes> warehouseByThirdInfoList = logisticsRpc.findWarehouseByThirdInfo(warehouseId,shipperId);
        if (CollectionUtils.isNotEmpty(warehouseByThirdInfoList)){
            // 2.查询批次库存找到对应供应商
            List<BatchStockOnlyStockInfo> batchStockOnlyStockInfos = batchStockRpc.getBySupplierSkuId(supplierSkuId);
            if (CollectionUtils.isNotEmpty(batchStockOnlyStockInfos)){
                BatchStockOnlyStockInfo batchStockOnlyStockInfo = batchStockOnlyStockInfos.get(0);
                Map<String, WarehouseLiteRes> warehouseLiteResMap = warehouseByThirdInfoList.stream().collect(Collectors.toMap(WarehouseLiteRes::getWarehouseId, item -> item));
                WarehouseLiteRes warehouseByThirdInfo = warehouseLiteResMap.get(batchStockOnlyStockInfo.getWarehouseId());

                // 3.根据supplierSkuId查询一遍库存
                List<BatchStockRes> stockBySupplierSkuId = wmsBatchStockRpc.findStockBySupplierSkuId(supplierSkuId).items;
                if (CollectionUtils.isNotEmpty(stockBySupplierSkuId)){
                    BatchStockRes batchStockRes = stockBySupplierSkuId.get(0);
                    //可用库存
                    Integer usableCount = batchStockRes.getUsableCount();
                    log.info("可用库存相关信息，supplierSkuId=[{}], supplierId=[{}], warehouseId=[{}], usableCount=[{}]", supplierSkuId, warehouseByThirdInfo.getBizContext(), warehouseByThirdInfo.getWarehouseId(), usableCount);
                    // 增加判断、防止校验错误
                    if (usableCount != null && usableCount >= 0){
                        // 4.根据供应商id跟skuId，同时更新该供应商的sku库存信息
                        supplierStockApiRpc.syncStock(warehouseByThirdInfo,supplierSkuId,usableCount);
                    }
                }
            }
        }
    }

    public PageList<BatchStockRes> queryPageByBatchBLStock(BatchBinLocationStockPageQueryReq req) {
        return wmsBatchStockRpc.queryPageByBatchBLStock(req);
    }
}
