package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj;

import lombok.Getter;

public enum EcCangOrderStatusEnum {
    /**
     * 草稿
     */
    D("D", "CREATED"),
    /**
     * 已预报
     */
    P("P", "CREATED"),
    /**
     * 已签入
     */
    V("V", "RECEIVED"),
    /**
     * 已签出
     */
    C("C", "PACKED"),
    /**
     * 暂存件
     */
    Q("Q", "HOLD"),
    /**
     * 已废弃
     */
    E("E", "CLOSED"),
    /**
     * 妥投
     */
    O("O", "FINISHED"),
    /**
     * 仓库未知状态
     */
    UNKNOW("UNKNOW", "未知状态");


    public static EcCangOrderStatusEnum getByValue(String value) {
        for (EcCangOrderStatusEnum ecCangOrderStatusEnum : EcCangOrderStatusEnum.values()) {
            if (ecCangOrderStatusEnum.getEcangTMSOrderStatus().equals(value)) {
                return ecCangOrderStatusEnum;
            }
        }
        return UNKNOW;
    }

    EcCangOrderStatusEnum(String ecangTMSOrderStatus, String erpOrderStatus) {
        this.ecangTMSOrderStatus = ecangTMSOrderStatus;
        this.erpOrderStatus = erpOrderStatus;
    }

    @Getter
    private String erpOrderStatus;
    @Getter
    private String ecangTMSOrderStatus;

}
