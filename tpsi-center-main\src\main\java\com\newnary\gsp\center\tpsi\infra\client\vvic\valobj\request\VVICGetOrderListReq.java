package com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.request;

import lombok.Data;

@Data
public class VVICGetOrderListReq {

    /**
     * 	订单业务类型，例：1-普通，2-拼团（新字段，暂未支持）
     */
    private String type;

    /**
     *订单号，多个用英文逗号分隔
     */
    private String order_no;

    /**
     *外部订单号，多个用英文逗号分隔
     */
    private String out_order_no;

    /**
     *	创建订单的开始时间（时间格式:yyyy-MM-dd）
     */
    private String begin_time;

    /**
     *创建订单的结束时间（时间格式:yyyy-MM-dd）
     */
    private String end_time;

    /**
     *订单状态，支持：待付款“1”；未付款交易关闭“2”；拿货中“3”；配送中“5”；交易成功“6”;已付款交易关闭“8”;部分发货“9”
     */
    private String order_status;

    /**
     *订单收货人的姓名
     */
    private String consignee;

    /**
     *默认1
     */
    private Integer page;

    /**
     *	默认20
     */
    private Integer page_size;
}
