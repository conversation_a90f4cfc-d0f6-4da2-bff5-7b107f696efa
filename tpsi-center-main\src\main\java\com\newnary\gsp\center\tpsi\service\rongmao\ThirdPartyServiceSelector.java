package com.newnary.gsp.center.tpsi.service.rongmao;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class ThirdPartyServiceSelector {

    @Resource
    private IProductOperateService ecCangProductOperateServiceImpl;

    public IProductOperateService select(String bizId){
        switch (bizId) {
            case "ECCANG":
                return ecCangProductOperateServiceImpl;
        }

        return null;
    }
}
