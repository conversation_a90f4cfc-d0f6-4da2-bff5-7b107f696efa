package com.newnary.gsp.center.tpsi.infra.client.mabang.valobj;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class MaBangSalesSku {

    private String classType;

    private String salesSku;
    @JSONField(name ="package")
    private String packingType;
    private String length;
    private String stockPicture;
    private int powder;
    private String sales;
    private int isTort;
    private List<String> stockSku;
    private int noLiquidCosmetic;
    private String salesName;
    private String salesNameEn;
    private int hasBattery;
    private String artDesigner;
    private String width;
    private int magnetic;
    private MaBangSalesSkuCategory parentCategory;
    private String developer;
    private MaBangSalesSkuCategory category;
    private String height;
}
