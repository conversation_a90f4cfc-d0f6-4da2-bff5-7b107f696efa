package com.newnary.gsp.center.tpsi.service.ninjavan.impl;

import com.alibaba.fastjson.JSONObject;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.logistics.api.LogisticsCenterErrorInfo;
import com.newnary.gsp.center.logistics.api.delivery.dto.TransportConsigneeDTO;
import com.newnary.gsp.center.logistics.api.delivery.dto.TransportItemDTO;
import com.newnary.gsp.center.logistics.api.delivery.response.TransportOrderPackageInfo;
import com.newnary.gsp.center.logistics.api.warehouse.response.ReceiptWarehouseRes;
import com.newnary.gsp.center.product.api.category.response.CategoryInfo;
import com.newnary.gsp.center.tpsi.api.logisticsService.request.LogisticsCancelOrderCommand;
import com.newnary.gsp.center.tpsi.api.logisticsService.request.LogisticsPrintOrderCommand;
import com.newnary.gsp.center.tpsi.api.logisticsService.response.LogisticsOrderResp;
import com.newnary.gsp.center.tpsi.api.logisticsService.response.LogisticsPrintSheetResp;
import com.newnary.gsp.center.tpsi.api.ninjavan.request.CancelOrderNinJavanCommand;
import com.newnary.gsp.center.tpsi.api.ninjavan.request.CreateOrderNinJavanCommand;
import com.newnary.gsp.center.tpsi.api.ninjavan.request.SheetOrderNinJavanCommand;
import com.newnary.gsp.center.tpsi.api.ninjavan.response.NinJavanDataApiBaseResult;
import com.newnary.gsp.center.tpsi.api.ninjavan.response.NinJavanOrderResp;
import com.newnary.gsp.center.tpsi.api.ninjavan.response.NinJavanPrintSheetResp;
import com.newnary.gsp.center.tpsi.api.ninjavan.util.Obj2map;
import com.newnary.gsp.center.tpsi.api.ninjavan.vo.*;
import com.newnary.gsp.center.tpsi.infra.client.ninjavan.NinJavanApiClient;
import com.newnary.gsp.center.tpsi.infra.client.ninjavan.dto.NinJavanBase64RetDTO;
import com.newnary.gsp.center.tpsi.infra.model.ApiDockingResult;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.creator.ApiDockingResultCreator;
import com.newnary.gsp.center.tpsi.infra.model.vo.ApiDockingResultType;
import com.newnary.gsp.center.tpsi.infra.model.vo.LogisticsServiceConstants;
import com.newnary.gsp.center.tpsi.infra.model.vo.LogisticsServiceContext;
import com.newnary.gsp.center.tpsi.infra.repository.ApiDockingResultRepository;
import com.newnary.gsp.center.tpsi.infra.rpc.ExchangeRateRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.SpaceFileRpc;
import com.newnary.gsp.center.tpsi.service.ILogisticsApiSve;
import com.newnary.gsp.center.tpsi.service.impl.SystemClientSve;
import com.newnary.gsp.center.trade.api.order.enums.OrderPayState;
import com.newnary.gsp.center.trade.api.order.response.OrderDTO;
import com.newnary.spring.cloud.extend.Extend;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

@Extend("NINJAVAN")
@Component
@Slf4j
public class NinjavanLogisticsApiSvelmpl extends SystemClientSve implements ILogisticsApiSve {

    private static final String thirdPartySystemId = "TEST_NINJAVAN";

    private static final String ninjavanVersion = "1.4.1";

    private static final Integer pageLimit = 100000;

    //最小保价金额
    private static final BigDecimal minInsuredValue = new BigDecimal(2500);
    //最大保价金额
    private static final BigDecimal maxInsuredValue = new BigDecimal(50000);

    @Resource
    private ApiDockingResultRepository apiDockingResultRepository;
    @Resource
    private SpaceFileRpc spaceFileRpc;
    @Resource
    private ExchangeRateRpc exchangeRateRpc;

    private NinJavanApiClient client;

    private void init() {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        //获取apiClient
        client = getClient(thirdPartySystem.getParams());
    }

    private NinJavanApiClient getClient(String NinJavanParams) {
        return new NinJavanApiClient(NinJavanParams);
    }

    public String loadAccessToken() {
        init();
        return client.loadAccessToken();
    }

    private void saveDockingResult(NinJavanDataApiBaseResult<String> result, String valueKey, String valueType) {
        ApiDockingResultCreator apiDockingResultCreator = new ApiDockingResultCreator();
        apiDockingResultCreator.setValueKey(valueKey);
        apiDockingResultCreator.setValueType(valueType);
        apiDockingResultCreator.setValueJson(result.getResult());
        apiDockingResultCreator.setDataStatus("saved");
        ApiDockingResult apiDockingResult = ApiDockingResult.createWith(apiDockingResultCreator);
        apiDockingResultRepository.store(apiDockingResult);
    }

    @Override
    public void createOrder() {
        init();
        CreateOrderNinJavanCommand command = buildCreateCommand();
        if (ObjectUtils.isEmpty(command)) {
            throw new ServiceException(LogisticsCenterErrorInfo.ERROR_5010_LOGISTICS_SERVICE_API_ERROR);
        }
        NinJavanDataApiBaseResult<String> baseResult = client.createOrder(JSONObject.toJSONString(Obj2map.getUnderToHump(command)));

        String result = baseResult.getResult();
        JSONObject jsonObject = JSONObject.parseObject(result);
        String data = jsonObject.get("data").toString();
        saveDockingResult(baseResult, command.getSourceOrderId(), ApiDockingResultType.NinJavaNCreate.name());
        buildOrderResponse(JSONObject.parseObject(Obj2map.lineToHump(data), NinJavanOrderResp.class),command.getDelivery().getInsuredValue());
    }

    private void buildOrderResponse(NinJavanOrderResp ninJavanOrderResp,Float insuredValue) {
        LogisticsOrderResp ret = new LogisticsOrderResp();
        ret.setTrackingId(ninJavanOrderResp.getTrackingId());
        ret.setRequestedTrackingId(ninJavanOrderResp.getRequestedTrackingId());
        if (ObjectUtils.isNotEmpty(ninJavanOrderResp.getDelivery())) {
            ret.setDeliveryStartDate(ninJavanOrderResp.getDelivery().getDeliveryStartDate());
            ret.setAllowSelfCollection(ninJavanOrderResp.getDelivery().getAllowSelfCollection());
        }
        ret.setInsuredValue(insuredValue.toString());
        LogisticsServiceContext.getCurrentContext().put(LogisticsServiceConstants.CALL_CREATE_RESPONSE, ret);
    }

    private CreateOrderNinJavanCommand buildCreateCommand() {
        OrderDTO tradeOrderInfo = (OrderDTO) LogisticsServiceContext.getCurrentContext().get(LogisticsServiceConstants.LOGISTICS_TRADE_DOMAIN);
        TransportOrderPackageInfo transportOrderPackageInfo = (TransportOrderPackageInfo) LogisticsServiceContext.getCurrentContext().get(LogisticsServiceConstants.LOGISTICS_TRANSPORT_DOMAIN);
        ReceiptWarehouseRes warehouseInfo = (ReceiptWarehouseRes) LogisticsServiceContext.getCurrentContext().get(LogisticsServiceConstants.LOGISTICS_WAREHOUSE_DOMAIN);
        Map<String, CategoryInfo> categoryMap = (Map<String, CategoryInfo>) LogisticsServiceContext.getCurrentContext().get(LogisticsServiceConstants.LOGISTICS_CATEGORY_DOMAIN);
        if (ObjectUtils.isEmpty(tradeOrderInfo) || ObjectUtils.isEmpty(transportOrderPackageInfo)
                || ObjectUtils.isEmpty(warehouseInfo) || ObjectUtils.isEmpty(categoryMap)
                || CollectionUtils.isEmpty(transportOrderPackageInfo.getItems())) {
            return null;
        }
        CreateOrderNinJavanCommand ret = new CreateOrderNinJavanCommand();
        //1。 基础数据
        ret.setSourceOrderId(transportOrderPackageInfo.getTransportOrderPackageId());
        ret.setSourceReferenceId(transportOrderPackageInfo.getTransportOrderId());
        //2。 发件地
        buildFrom(ret, warehouseInfo);
        //3。 收件人
        buildTo(ret, transportOrderPackageInfo.getConsignee());
        //4。 包裹详情 TODO 「测试环境必传-生产可不传递--大多是海关信息」
        buildParcelDetails(ret, tradeOrderInfo);
        //5。 商品item
        buildItem(ret, transportOrderPackageInfo.getItems(), categoryMap);
        //6。 cod 信息
        buildDelivery(ret, transportOrderPackageInfo.getItemReferenceIds(), transportOrderPackageInfo, tradeOrderInfo);
        //7。 退件地址
        buildReturn(ret, warehouseInfo);
        return ret;
    }

    private void buildFrom(CreateOrderNinJavanCommand command, ReceiptWarehouseRes receiptWarehouseRes) {

        CreateNinJavanOrderFrom from = new CreateNinJavanOrderFrom();
        from.setName(receiptWarehouseRes.getName());
        from.setAddressLine1(receiptWarehouseRes.getAddrDetail());
        if (StringUtils.isNotEmpty(receiptWarehouseRes.getArea())) {
            from.setAddressLine1(from.getAddressLine1().concat(", ").concat(receiptWarehouseRes.getArea()));
        }
        from.setCity(receiptWarehouseRes.getCity());
        from.setStateProvince(receiptWarehouseRes.getStateProvince());
        from.setPostCode(receiptWarehouseRes.getPostCode());
        from.setCountryCode(receiptWarehouseRes.getCountryCode());
        from.setContactNumber(receiptWarehouseRes.getContactNumber());
        from.setContactEmail(receiptWarehouseRes.getContactEmail());

        command.setFrom(from);
    }

    private void buildTo(CreateOrderNinJavanCommand command, TransportConsigneeDTO consigneeVO) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(StringUtils.isNotEmpty(consigneeVO.getAddrDistrict()) ? consigneeVO.getAddrDistrict() : "")
                .append(" ")
                .append(StringUtils.isNotEmpty(consigneeVO.getAddrStreet()) ? consigneeVO.getAddrStreet() : "")
                .append(" ")
                .append(StringUtils.isNotEmpty(consigneeVO.getAddrDetail()) ? consigneeVO.getAddrDetail() : "");
        CreateNinJavanOrderTo to = new CreateNinJavanOrderTo();
        to.setName(consigneeVO.getName());
        to.setAddressLine1(stringBuilder.toString());
        to.setCountryCode(consigneeVO.getAddrCountry());
        to.setStateProvince(consigneeVO.getAddrProvince());
        to.setCity(consigneeVO.getAddrCity());
        to.setPostCode(consigneeVO.getZipCode());
        to.setContactNumber(consigneeVO.getContactNumber());
        to.setContactEmail(consigneeVO.getContactEmail());
        command.setTo(to);
    }

    private void buildParcelDetails(CreateOrderNinJavanCommand command, OrderDTO tradeOrder) {
        CreateNinJavanOrderParcelDetail parcelDetail = new CreateNinJavanOrderParcelDetail();
        parcelDetail.setCustomsCurrency(tradeOrder.getCurrency());
        command.setParcelDetails(parcelDetail);
    }

    private void buildItem(CreateOrderNinJavanCommand command, List<TransportItemDTO> itemVOList, Map<String, CategoryInfo> categoryInfoMap) {
        List<CreateNinJavanOrderItem> itemList = new ArrayList<>();
        for (TransportItemDTO item : itemVOList) {
            CategoryInfo categoryInfo = categoryInfoMap.get(item.getCategoryId());
            CreateNinJavanOrderItem orderItem = new CreateNinJavanOrderItem();
            //单独设置英文名
            if (ObjectUtils.isNotEmpty(categoryInfo)) {
                orderItem.setDescription(CollectionUtils.isNotEmpty(categoryInfo.getDescList()) ? categoryInfo.getDescList().get(0).getCategoryName() : categoryInfo.getCategoryName());
            }
            //币种兑换
//            BigDecimal phpAmount = exchangeRateRpc.currencyConvert(item.getSalePrice(), transportOrderItem.getSalePriceCurrency(), "PHP");
//            orderItem.setUnitValue(phpAmount.floatValue());
            orderItem.setQuantity(item.getQuantity());
            orderItem.setUnitWeight(item.getInvoiceUnitWeight() == null ? 0.00F : item.getInvoiceUnitWeight().floatValue());
            itemList.add(orderItem);
        }
        command.setItems(itemList);
    }

    private void buildDelivery(CreateOrderNinJavanCommand command, List<String> itemReferenceIds, TransportOrderPackageInfo transportOrderPackage, OrderDTO tradeOrder) {
        log.info("发货单id{},tradePhpAmount{}", itemReferenceIds, transportOrderPackage.getTransportAmount());

        BigDecimal transportPhpAmount = transportOrderPackage.getTransportAmount();

        CreateNinJavanOrderDelivery delivery = new CreateNinJavanOrderDelivery();
        if (tradeOrder.getIsCod()
                && "ONLINE_SELF_COD".equals(tradeOrder.getPayProductCode())
                && OrderPayState.WAIT_PAY.equals(tradeOrder.getPayState())) {
            //只有cod 需要填写货到付款金额
            delivery.setCashOnDelivery(transportPhpAmount.floatValue());
        }

        //订单支付金额--TODO cod 货到付款-更改为取应收
        BigDecimal decimalTradeAmount = transportPhpAmount;

        if (decimalTradeAmount.compareTo(minInsuredValue) < 1) {
            //Todo 不做任何处理
            delivery.setInsuredValue((float) 0);
        } else if (decimalTradeAmount.compareTo(minInsuredValue) == 1 && decimalTradeAmount.compareTo(maxInsuredValue) == -1) {
            // 2500＜包裹价值＜50000PHP 时，保价金额写采购价
            BigDecimal totalSupplyPrice = BigDecimal.ZERO;
            for (TransportItemDTO item : transportOrderPackage.getItems()) {
                //币种兑换
                BigDecimal phpAmount = exchangeRateRpc.currencyConvert(item.getSupplyPrice(), item.getSupplyPriceCurrency(), "PHP");

                totalSupplyPrice = totalSupplyPrice.add(phpAmount.multiply(new BigDecimal(item.getQuantity())));
            }
            delivery.setInsuredValue(totalSupplyPrice.floatValue());
        } else if (decimalTradeAmount.compareTo(maxInsuredValue) > -1) {
            delivery.setInsuredValue(maxInsuredValue.floatValue());
        }
        command.setDelivery(delivery);
    }

    private void buildReturn(CreateOrderNinJavanCommand command, ReceiptWarehouseRes receiptWarehouseRes) {

        CreateNinJavanOrderReturn orderReturn = new CreateNinJavanOrderReturn();
        orderReturn.setName(receiptWarehouseRes.getName());
        orderReturn.setAddressLine1(receiptWarehouseRes.getAddrDetail());
        orderReturn.setCountryCode(receiptWarehouseRes.getCountryCode());
        orderReturn.setStateProvince(receiptWarehouseRes.getStateProvince());
        orderReturn.setCity(receiptWarehouseRes.getCity());
        orderReturn.setPostCode(receiptWarehouseRes.getPostCode());
        orderReturn.setContactNumber(receiptWarehouseRes.getContactNumber());
        orderReturn.setContactEmail(receiptWarehouseRes.getContactEmail());
        command.setOrderReturn(orderReturn);
    }

    @Override
    public void doCancel() {
        init();
        CancelOrderNinJavanCommand command = buildCancelCommand();
        if (ObjectUtils.isEmpty(command)) {
            throw new ServiceException(LogisticsCenterErrorInfo.ERROR_5010_LOGISTICS_SERVICE_API_ERROR);
        }
        NinJavanDataApiBaseResult<String> baseResult = client.doCancel(JSONObject.toJSONString(Obj2map.getUnderToHump(command)));
        saveDockingResult(baseResult, command.getTrackingId(), ApiDockingResultType.NinJavaNCancel.name());
    }

    private CancelOrderNinJavanCommand buildCancelCommand() {
        LogisticsCancelOrderCommand command = (LogisticsCancelOrderCommand) LogisticsServiceContext.getCurrentContext().get(LogisticsServiceConstants.LOGISTICS_CANCEL_DOMAIN);
        if (ObjectUtils.isEmpty(command)) {
            return null;
        }
        CancelOrderNinJavanCommand ret = new CancelOrderNinJavanCommand();
        ret.setTrackingId(command.getTrackingId());
        ret.setCancelReason(command.getCancelReason());
        return ret;
    }

    @Override
    public void printSheet() {
        init();
        SheetOrderNinJavanCommand command = buildPrintCommand();
        if (ObjectUtils.isEmpty(command)) {
            throw new ServiceException(LogisticsCenterErrorInfo.ERROR_5010_LOGISTICS_SERVICE_API_ERROR);
        }
        NinJavanDataApiBaseResult<String> baseResult = client.printSheet(JSONObject.toJSONString(Obj2map.getUnderToHump(command)));

        //生成面单pdf 地址
        String pdfFileUrl = generateSheetFile(command.getTrackingId(), baseResult.getResult());

        String result = baseResult.getResult();

        //封装数据
        String substring = result.substring(0, result.length() - 2);
        StringBuilder stringBuilder = new StringBuilder(substring);
        stringBuilder.append(",");
        stringBuilder.append("\"pdf_file_url\":" + "\"" + pdfFileUrl + "\"}}");
        baseResult.setResult(stringBuilder.toString());

        System.out.println(baseResult.getResult());
        log.info("[NinJavaN 生成面单]封装结果集, result={}", baseResult.getResult());

        //结果集
        String resultPdf = baseResult.getResult();
        JSONObject jsonObject = JSONObject.parseObject(resultPdf);
        String data = (String) jsonObject.get("data").toString();
        saveDockingResult(baseResult, command.getTrackingId(), ApiDockingResultType.NinJavaNPrintSheet.name());

        buildPrintResponse(JSONObject.parseObject(Obj2map.lineToHump(data), NinJavanPrintSheetResp.class));
    }

    @Override
    public void queryTrack() {

    }

    private void buildPrintResponse(NinJavanPrintSheetResp resp) {
        LogisticsPrintSheetResp ret = new LogisticsPrintSheetResp();
        ret.setLabelBase64(resp.getLabelBase64());
        ret.setPdfFileUrl(resp.getPdfFileUrl());
        LogisticsServiceContext.getCurrentContext().put(LogisticsServiceConstants.CALL_PRINT_RESPONSE, ret);
    }

    private SheetOrderNinJavanCommand buildPrintCommand() {
        LogisticsPrintOrderCommand command = (LogisticsPrintOrderCommand) LogisticsServiceContext.getCurrentContext().get(LogisticsServiceConstants.LOGISTICS_PRINT_DOMAIN);
        if (ObjectUtils.isEmpty(command)) {
            return null;
        }
        SheetOrderNinJavanCommand ret = new SheetOrderNinJavanCommand();
        ret.setTrackingId(command.getTrackingId());
        ret.setTemplateId(command.getTemplateId());
        return ret;
    }


    /**
     * base64 转成 pdf
     *
     * @param trackingId
     * @param ret
     * @return
     */
    private String generateSheetFile(String trackingId, String ret) {
        NinJavanBase64RetDTO ninJavanBase64RetDTO = JSONObject.parseObject(ret, NinJavanBase64RetDTO.class);

        try {
            String base64Con = ninJavanBase64RetDTO.getData().getLabel_base64();
//            ByteArrayOutputStream outputStream = Base64Util.base64ToOutStream(base64Con);

            ByteArrayOutputStream outputStream = base64ToPdf(base64Con);

            String fileId = spaceFileRpc.createSpaceFile(trackingId, outputStream.toByteArray(), ".pdf");
            Map<String, String> fileIdMap = spaceFileRpc.queryFileId4FileUrlMapByFileId(Collections.singletonList(fileId));
            String sheetPdfFileUrl = fileIdMap.get(fileId);
            return sheetPdfFileUrl;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    private ByteArrayOutputStream base64ToPdf(String base64Data) {
        int width = 15 * 72; // 设置PDF宽度为15cm，转换为点的单位（1英寸=72点）
        int height = 10 * 72; // 设置PDF高度为10cm，转换为点的单位（1英寸=72点）

        // 解码Base64数据
        byte[] decodedData = Base64.getDecoder().decode(base64Data);

        try {
            // 创建一个临时PDF文件
            Path tempFilePath = Paths.get("temp.pdf");
            Files.write(tempFilePath, decodedData);

            // 读取临时PDF文件
            PdfReader reader = new PdfReader(tempFilePath.toString());


            // 将临时PDF文件的内容复制到新的PDF文件中
            PdfStamper stamper = new PdfStamper(reader, new FileOutputStream("output.pdf"));
            PdfContentByte content = stamper.getUnderContent(1);
            PdfContentByte canvas = stamper.getOverContent(1);
            content.setRGBColorFill(255, 255, 255);
            content.rectangle(0, 0, width, height);
            content.fill();
            canvas.addTemplate(stamper.getImportedPage(reader, 1), 0, 0);
            stamper.close();
            reader.close();

            // 删除临时PDF文件
            Files.delete(tempFilePath);

            // 将修改后的PDF读取并写入ByteArrayOutputStream
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            PdfReader modifiedReader = new PdfReader("output.pdf");
            PdfStamper modifiedStamper = new PdfStamper(modifiedReader, outputStream);
            modifiedStamper.close();
            modifiedReader.close();

            Files.delete(Paths.get("output.pdf"));
            System.out.println("PDF转换完成！");
            log.info("PDF转换完成！");

            return outputStream;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new ByteArrayOutputStream();
    }

}
