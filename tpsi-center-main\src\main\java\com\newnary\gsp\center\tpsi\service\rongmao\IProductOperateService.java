package com.newnary.gsp.center.tpsi.service.rongmao;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;

public interface IProductOperateService {

    CommonResponse<String> create(ThirdPartySystem thirdPartySystem, String productId);
    CommonResponse<String> delete(ThirdPartySystem thirdPartySystem,String productId);
    CommonResponse<String> update(ThirdPartySystem thirdPartySystem,String productId);
}
