package com.newnary.gsp.center.tpsi.infra.repository;



import com.newnary.gsp.center.tpsi.infra.model.CrawlerProduct;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since Created on 2023-03-17
 **/
public interface ICrawProductRepository {

    void store(CrawlerProduct crawlerProduct);

    List<String> loadProductId(String categoryName);

    Optional<CrawlerProduct> loadProduct(CrawlerProduct crawlerProduct);

    void update(CrawlerProduct source);


    List<CrawlerProduct> loadCrawlerProductByCategoryList(List<String> categoryList, Integer limit, Integer crawlerReturnState,Integer state,Integer flushState);

    void fillCrawlerReturn(CrawlerProduct source);

    CrawlerProduct loadCrawlerDetailReturn(String productId);
}
