package com.newnary.gsp.center.tpsi.infra.client.tk.dto;

import com.newnary.gsp.center.tpsi.infra.client.tk.dto.vo.PackageOrderInfo;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


/**
 * @see <a href="https://partner.tiktokshop.com/doc/page/262784?onlySelectedDir=13589">tiktok开放平台-/api/fulfillment/detail</a>
 */
@Getter
@Setter
public class PackageDetail {
    private String package_id;
    private List<PackageOrderInfo> order_info_list;
    private Integer package_status;
    private Integer package_freeze_status;
    private Integer sc_tag;
    private Integer prInteger_tag;
    private Integer sku_tag;
    private Integer note_tag;
    private String delivery_option;
    private String shipping_provider;
    private String shipping_provider_id;
    private String tracking_number;
    private Integer pick_up_type;
    private Integer pick_up_start_time;
    private Integer pick_up_end_time;
    private Integer create_time;
    private Integer update_time;
    private List<String> order_line_id_list;
    private Integer cancel_because_logistic_issue;

}
