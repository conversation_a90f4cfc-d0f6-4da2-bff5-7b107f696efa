<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.newnary.gsp.center.tpsi.infra.repository.db.dao.DeliveryOrderItemEcCangApiAssociationDao">

<resultMap id="deliveryOrderItemEcCangApiAssociationPOResult" type="com.newnary.gsp.center.tpsi.infra.repository.db.po.DeliveryOrderItemEcCangApiAssociationPO">
    <result column="wms_product_category" property="wmsProductCategory"/>
    <result column="erp_purchase_order_status" property="erpPurchaseOrderStatus"/>
    <result column="wms_product_standard" property="wmsProductStandard"/>
    <result column="wms_product_name_cn" property="wmsProductNameCn"/>
    <result column="wms_product_name_en" property="wmsProductNameEn"/>
    <result column="erp_purchase_order_shipping_fee" property="erpPurchaseOrderShippingFee"/>
    <result column="wms_product_count" property="wmsProductCount"/>
    <result column="wms_product_declared" property="wmsProductDeclared"/>
    <result column="erp_purchase_order_tracking_no" property="erpPurchaseOrderTrackingNo"/>
    <result column="wms_product_img_url" property="wmsProductImgUrl"/>
    <result column="erp_purchase_order_code" property="erpPurchaseOrderCode"/>
    <result column="wms_product_package_code" property="wmsProductPackageCode"/>
    <result column="erp_purchase_order_payable_amount" property="erpPurchaseOrderPayableAmount"/>
    <result column="wms_product_pid" property="wmsProductPid"/>
    <result column="custom_code" property="customCode"/>
    <result column="quantity" property="quantity"/>
    <result column="sale_item_code" property="saleItemCode"/>
    <result column="wms_child_code" property="wmsChildCode"/>
    <result column="association_id" property="associationId"/>
    <result column="supply_price" property="supplyPrice"/>
    <result column="supplier_sku_id" property="supplierSkuId"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="id" property="id"/>
    <result column="gmt_create" property="gmtCreate"/>
    <result column="gmt_modified" property="gmtModified"/>
</resultMap>

<sql id="deliveryOrderItemEcCangApiAssociationPO_columns">
    wms_product_category,
    erp_purchase_order_status,
    wms_product_standard,
    wms_product_name_cn,
    wms_product_name_en,
    erp_purchase_order_shipping_fee,
    wms_product_count,
    wms_product_declared,
    erp_purchase_order_tracking_no,
    wms_product_img_url,
    erp_purchase_order_code,
    wms_product_package_code,
    erp_purchase_order_payable_amount,
    wms_product_pid,
    custom_code,
    quantity,
    sale_item_code,
    wms_child_code,
    association_id,
    supply_price,
    supplier_sku_id,
    tenant_id,
    id,
    gmt_create,
    gmt_modified
</sql>

<sql id="deliveryOrderItemEcCangApiAssociationPO_sqlForInsert">
    wms_product_category,
    erp_purchase_order_status,
    wms_product_standard,
    wms_product_name_cn,
    wms_product_name_en,
    erp_purchase_order_shipping_fee,
    wms_product_count,
    wms_product_declared,
    erp_purchase_order_tracking_no,
    wms_product_img_url,
    erp_purchase_order_code,
    wms_product_package_code,
    erp_purchase_order_payable_amount,
    wms_product_pid,
    custom_code,
    quantity,
    sale_item_code,
    wms_child_code,
    association_id,
    supply_price,
    supplier_sku_id,
    tenant_id,
    id,
    gmt_create,
    gmt_modified
</sql>

<sql id="deliveryOrderItemEcCangApiAssociationPO_columnsForInsert">
    #{wmsProductCategory},
    #{erpPurchaseOrderStatus},
    #{wmsProductStandard},
    #{wmsProductNameCn},
    #{wmsProductNameEn},
    #{erpPurchaseOrderShippingFee},
    #{wmsProductCount},
    #{wmsProductDeclared},
    #{erpPurchaseOrderTrackingNo},
    #{wmsProductImgUrl},
    #{erpPurchaseOrderCode},
    #{wmsProductPackageCode},
    #{erpPurchaseOrderPayableAmount},
    #{wmsProductPid},
    #{customCode},
    #{quantity},
    #{saleItemCode},
    #{wmsChildCode},
    #{associationId},
    #{supplyPrice},
    #{supplierSkuId},
    #{tenantId},
    #{id},
    REPLACE(unix_timestamp(NOW(3)),'.',''),
    REPLACE(unix_timestamp(NOW(3)),'.','')
</sql>

<sql id="deliveryOrderItemEcCangApiAssociationPO_setterForUpdate">
    <set>
        gmt_modified = REPLACE(unix_timestamp(NOW(3)),'.',''),
        wms_product_category = #{wmsProductCategory},
        erp_purchase_order_status = #{erpPurchaseOrderStatus},
        wms_product_standard = #{wmsProductStandard},
        wms_product_name_cn = #{wmsProductNameCn},
        wms_product_name_en = #{wmsProductNameEn},
        erp_purchase_order_shipping_fee = #{erpPurchaseOrderShippingFee},
        wms_product_count = #{wmsProductCount},
        wms_product_declared = #{wmsProductDeclared},
        erp_purchase_order_tracking_no = #{erpPurchaseOrderTrackingNo},
        wms_product_img_url = #{wmsProductImgUrl},
        erp_purchase_order_code = #{erpPurchaseOrderCode},
        wms_product_package_code = #{wmsProductPackageCode},
        erp_purchase_order_payable_amount = #{erpPurchaseOrderPayableAmount},
        wms_product_pid = #{wmsProductPid},
        custom_code = #{customCode},
        quantity = #{quantity},
        sale_item_code = #{saleItemCode},
        wms_child_code = #{wmsChildCode},
        association_id = #{associationId},
        supply_price = #{supplyPrice},
        supplier_sku_id = #{supplierSkuId},
    </set>
</sql>

<sql id="deliveryOrderItemEcCangApiAssociationPO_selector">
    select
    <include refid="deliveryOrderItemEcCangApiAssociationPO_columns"/>
    from delivery_order_item_eccang_api_association
</sql>

<sql id="deliveryOrderItemEcCangApiAssociationPO_query_segment">
    <trim prefix="WHERE" prefixOverrides="AND|OR">
        <if test="data.wmsProductCategory != null">
            AND wms_product_category = #{data.wmsProductCategory}
        </if>
        <if test="data.erpPurchaseOrderStatus != null">
            AND erp_purchase_order_status = #{data.erpPurchaseOrderStatus}
        </if>
        <if test="data.wmsProductStandard != null">
            AND wms_product_standard = #{data.wmsProductStandard}
        </if>
        <if test="data.wmsProductNameCn != null">
            AND wms_product_name_cn = #{data.wmsProductNameCn}
        </if>
        <if test="data.wmsProductNameEn != null">
            AND wms_product_name_en = #{data.wmsProductNameEn}
        </if>
        <if test="data.erpPurchaseOrderShippingFee != null">
            AND erp_purchase_order_shipping_fee = #{data.erpPurchaseOrderShippingFee}
        </if>
        <if test="data.wmsProductCount != null">
            AND wms_product_count = #{data.wmsProductCount}
        </if>
        <if test="data.wmsProductDeclared != null">
            AND wms_product_declared = #{data.wmsProductDeclared}
        </if>
        <if test="data.erpPurchaseOrderTrackingNo != null">
            AND erp_purchase_order_tracking_no = #{data.erpPurchaseOrderTrackingNo}
        </if>
        <if test="data.wmsProductImgUrl != null">
            AND wms_product_img_url = #{data.wmsProductImgUrl}
        </if>
        <if test="data.erpPurchaseOrderCode != null">
            AND erp_purchase_order_code = #{data.erpPurchaseOrderCode}
        </if>
        <if test="data.wmsProductPackageCode != null">
            AND wms_product_package_code = #{data.wmsProductPackageCode}
        </if>
        <if test="data.erpPurchaseOrderPayableAmount != null">
            AND erp_purchase_order_payable_amount = #{data.erpPurchaseOrderPayableAmount}
        </if>
        <if test="data.wmsProductPid != null">
            AND wms_product_pid = #{data.wmsProductPid}
        </if>
        <if test="data.customCode != null">
            AND custom_code = #{data.customCode}
        </if>
        <if test="data.quantity != null">
            AND quantity = #{data.quantity}
        </if>
        <if test="data.saleItemCode != null">
            AND sale_item_code = #{data.saleItemCode}
        </if>
        <if test="data.wmsChildCode != null">
            AND wms_child_code = #{data.wmsChildCode}
        </if>
        <if test="data.associationId != null">
            AND association_id = #{data.associationId}
        </if>
        <if test="data.supplyPrice != null">
            AND supply_price = #{data.supplyPrice}
        </if>
        <if test="data.supplierSkuId != null">
            AND supplier_sku_id = #{data.supplierSkuId}
        </if>
        <if test="data.id != null">
            AND id = #{data.id}
        </if>
        <if test="data.gmtCreate != null">
            AND gmt_create = #{data.gmtCreate}
        </if>
        <if test="data.gmtModified != null">
            AND gmt_modified = #{data.gmtModified}
        </if>
        <if test="ins != null and ins.size() > 0">
            AND
            <foreach collection="ins" item="item" open="(" separator="and" close=")">
                ${item.column} in
                <foreach collection="item.values" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </foreach>
        </if>
        <if test="notIns != null and notIns.size() > 0">
            AND
            <foreach collection="notIns" item="item" open="(" separator="and" close=")">
                ${item.column} not in
                <foreach collection="item.values" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </foreach>
        </if>
        <if test="equals != null and equals.size() > 0">
            AND
            <foreach collection="equals" item="item" open="(" separator="and" close=")">
                ${item.column} = #{item.value}
            </foreach>
        </if>
        <if test="notEquals != null and notEquals.size() > 0">
            AND
            <foreach collection="notEquals" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ <> ]]> #{item.value}
            </foreach>
        </if>
        <if test="lts != null and lts.size() > 0">
            AND
            <foreach collection="lts" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ < ]]> #{item.value}
            </foreach>
        </if>
        <if test="gts != null and gts.size() > 0">
            AND
            <foreach collection="gts" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ > ]]> #{item.value}
            </foreach>
        </if>
        <if test="likes != null and likes.size() > 0">
            AND
            <foreach collection="likes" item="item" open="(" separator="and" close=")">
                ${item.column} like concat('%', #{item.value}, '%')
            </foreach>
        </if>
        <if test="ltes != null and ltes.size() > 0">
            AND
            <foreach collection="ltes" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ <= ]]> #{item.value}
            </foreach>
        </if>
        <if test="gtes != null and gtes.size() > 0">
            AND
            <foreach collection="gtes" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ >= ]]> #{item.value}
            </foreach>
        </if>
        <if test="ranges != null and ranges.size() > 0">
            AND
            <foreach collection="ranges" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ >= ]]> #{item.from} and ${item.column} <![CDATA[ < ]]> #{item.to}
            </foreach>
        </if>
        AND tenant_id = #{tenantId}
    </trim>
</sql>


<insert id="insert" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.DeliveryOrderItemEcCangApiAssociationPO">
    insert into delivery_order_item_eccang_api_association
    (
        <include refid="deliveryOrderItemEcCangApiAssociationPO_sqlForInsert"/>
    )
    values
    (
        <include refid="deliveryOrderItemEcCangApiAssociationPO_columnsForInsert"/>
    )
</insert>

<update id="update" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.DeliveryOrderItemEcCangApiAssociationPO">
    update delivery_order_item_eccang_api_association
    <include refid="deliveryOrderItemEcCangApiAssociationPO_setterForUpdate" />
    where id = #{id}
    and tenant_id = #{tenantId}
</update>


<update id="updateByQuery" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.DeliveryOrderItemEcCangApiAssociationPO">
    update delivery_order_item_eccang_api_association
    <set>
        gmt_modified = REPLACE(unix_timestamp(NOW(3)),'.',''),
        wms_product_category = #{update.wmsProductCategory},
        erp_purchase_order_status = #{update.erpPurchaseOrderStatus},
        wms_product_standard = #{update.wmsProductStandard},
        wms_product_name_cn = #{update.wmsProductNameCn},
        wms_product_name_en = #{update.wmsProductNameEn},
        erp_purchase_order_shipping_fee = #{update.erpPurchaseOrderShippingFee},
        wms_product_count = #{update.wmsProductCount},
        wms_product_declared = #{update.wmsProductDeclared},
        erp_purchase_order_tracking_no = #{update.erpPurchaseOrderTrackingNo},
        wms_product_img_url = #{update.wmsProductImgUrl},
        erp_purchase_order_code = #{update.erpPurchaseOrderCode},
        wms_product_package_code = #{update.wmsProductPackageCode},
        erp_purchase_order_payable_amount = #{update.erpPurchaseOrderPayableAmount},
        wms_product_pid = #{update.wmsProductPid},
        custom_code = #{update.customCode},
        quantity = #{update.quantity},
        sale_item_code = #{update.saleItemCode},
        wms_child_code = #{update.wmsChildCode},
        association_id = #{update.associationId},
        supply_price = #{update.supplyPrice},
        supplier_sku_id = #{update.supplierSkuId},
    </set>
    <include refid="deliveryOrderItemEcCangApiAssociationPO_query_segment"/>
</update>

<!-- Delete -->
<delete id="delete">
    delete from delivery_order_item_eccang_api_association
    <include refid="deliveryOrderItemEcCangApiAssociationPO_query_segment"/>
</delete>
<delete id="deleteById">
    delete from delivery_order_item_eccang_api_association
    where id = #{id}
    and tenant_id = #{tenantId}
</delete>





<!-- query class -->
<sql id="deliveryOrderItemEcCangApiAssociationPO_groupBy">
    <if test="groupBys != null and groupBys.size() > 0">
        group by
        <foreach collection="groupBys" item="item" open="" separator="," close="">
            #{item}
        </foreach>
    </if>
</sql>

<sql id="deliveryOrderItemEcCangApiAssociationPO_orderby">
    <if test="orderBys != null and orderBys.size() > 0">
        order by
        <foreach collection="orderBys" item="item" open="" separator="," close="">
            ${item.column} ${item.direction}
        </foreach>
    </if>
</sql>


<select id="query" resultMap="deliveryOrderItemEcCangApiAssociationPOResult" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.DeliveryOrderItemEcCangApiAssociationPO">
    <include refid="deliveryOrderItemEcCangApiAssociationPO_selector"/>
    <include refid="deliveryOrderItemEcCangApiAssociationPO_query_segment"/>
    <include refid="deliveryOrderItemEcCangApiAssociationPO_groupBy"/>
    <include refid="deliveryOrderItemEcCangApiAssociationPO_orderby"/>
</select>



<select id="count" resultType="java.lang.Long">
    SELECT COUNT(1) FROM delivery_order_item_eccang_api_association
    <include refid="deliveryOrderItemEcCangApiAssociationPO_query_segment"/>
</select>

<select id="getById" resultMap="deliveryOrderItemEcCangApiAssociationPOResult">
    <include refid="deliveryOrderItemEcCangApiAssociationPO_selector"/>
    where id = #{id}
    and tenant_id = #{tenantId}
</select>

<select id="getByIdForUpdate" resultMap="deliveryOrderItemEcCangApiAssociationPOResult">
    <include refid="deliveryOrderItemEcCangApiAssociationPO_selector"/>
    where id = #{id}
    and tenant_id = #{tenantId}
    for update
</select>

</mapper>
