package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.wms;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
public class EcCangUploadFileRequest {
    @NotNull(message = "文件类型不能为空")
    private String file_type;
    @NotNull(message = "文件base64数据不能为空")
    private String file_data;
    @NotNull(message = "订单附件不能为空")
    private String module;
    private String file_note;
}
