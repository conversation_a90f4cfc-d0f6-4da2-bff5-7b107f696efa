package com.newnary.gsp.center.tpsi.service.mabang.impl;

import com.newnary.gsp.center.tpsi.infra.client.mabang.MaBangGWApiClient;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.MaBangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.mq.producer.ApiDockingProducer;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.service.impl.SystemClientSve;
import com.newnary.gsp.center.tpsi.service.mabang.IMaBangWarehouseApiSve;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class MaBangWarehouseApiSveImpl extends SystemClientSve implements IMaBangWarehouseApiSve {

    public static final Logger LOGGER = LoggerFactory.getLogger(MaBangWarehouseApiSveImpl.class);

    @Resource
    private ApiDockingProducer apiDockingProducer;

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Override
    public String warehouseDoAddStorage(String thirdPartySystemId, String context) {
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        //获取apiClient
        MaBangGWApiClient maBangGWApiClient = getClient(thirdPartySystem.getParams());
        //执行盘点
        MaBangApiBaseResult<String> ret = maBangGWApiClient.warehouseDoAddStorage(context);
        //处理结果
        if (ret.getCode().equals("200")) {
            LOGGER.info("库存盘点成功");
            return "SUCCESS";
        } else {
            String errorMessage = ret.getMessage();
            return errorMessage;
        }
    }

    private MaBangGWApiClient getClient(String maBangParams) {
        return new MaBangGWApiClient(maBangParams);
    }

}
