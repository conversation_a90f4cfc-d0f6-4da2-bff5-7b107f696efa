<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.newnary.gsp.center.tpsi.infra.repository.db.dao.ThirdPartyMappingExtDao">

<resultMap id="thirdPartyMappingPOResult" type="com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyMappingPO">
    <result column="source_related_id" property="sourceRelatedId"/>
    <result column="target_category_path" property="targetCategoryPath"/>
    <result column="source_id" property="sourceId"/>
    <result column="target_id" property="targetId"/>
    <result column="source_biz_id" property="sourceBizId"/>
    <result column="extend_biz_info" property="extendBizInfo"/>
    <result column="biz_type" property="bizType"/>
    <result column="target_biz_id" property="targetBizId"/>
    <result column="index_key" property="indexKey"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="id" property="id"/>
    <result column="gmt_create" property="gmtCreate"/>
    <result column="gmt_modified" property="gmtModified"/>
</resultMap>

<sql id="thirdPartyMappingPO_columns">
    source_related_id,
    target_category_path,
    source_id,
    target_id,
    source_biz_id,
    extend_biz_info,
    biz_type,
    target_biz_id,
    index_key,
    tenant_id,
    id,
    gmt_create,
    gmt_modified
</sql>
<sql id="thirdPartyMappingPO_selector">
    select
    <include refid="thirdPartyMappingPO_columns"/>
    from third_party_mapping
</sql>
<sql id="pageQuerySQL">
    <include refid="thirdPartyMappingPO_selector"/>
    <where>
        <if test="sourceRelatedId != null">
            AND source_related_id = #{sourceRelatedId}
        </if>
        <if test="targetCategoryPath != null">
            AND target_category_path = #{targetCategoryPath}
        </if>
        <if test="sourceId != null">
            AND source_id = #{sourceId}
        </if>
        <if test="targetId != null">
            AND target_id = #{targetId}
        </if>
        <if test="sourceBizId != null">
            AND source_biz_id = #{sourceBizId}
        </if>
        <if test="extendBizInfo != null">
            AND extend_biz_info = #{extendBizInfo}
        </if>
        <if test="bizType != null">
            AND biz_type = #{bizType}
        </if>
        <if test="targetBizId != null">
            AND target_biz_id = #{targetBizId}
        </if>
        <if test="indexKey != null">
            AND index_key = #{indexKey}
        </if>
        <if test="id != null">
            AND id = #{id}
        </if>
        <if test="gmtCreate != null">
            AND gmt_create = #{gmtCreate}
        </if>
        <if test="gmtModified != null">
            AND gmt_modified = #{gmtModified}
        </if>
        <if test="gmtCreateLess != null">
            AND gmt_create &lt;= #{gmtCreateLess}
        </if>
        <if test="gmtCreateGreater != null">
            AND gmt_create &gt;= #{gmtCreateGreater}
        </if>
    </where>
</sql>
<select id="pageQuery" resultMap="thirdPartyMappingPOResult">
    <include refid="pageQuerySQL"/>
    limit ${startRow}, ${pageSize}
</select>
</mapper>
