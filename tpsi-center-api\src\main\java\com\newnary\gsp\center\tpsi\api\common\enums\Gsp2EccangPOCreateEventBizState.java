package com.newnary.gsp.center.tpsi.api.common.enums;

public enum Gsp2EccangPOCreateEventBizState {

    SYNC_PO_INIT(10, "同步采购单tpsi初始化"),
    SYNC_EC_PO_FAILED(11, "易仓采购单创建失败"),
    SYNC_EC_PO_CREATED(20, "易仓采购单已创建"),
    SYNC_EC_PO_GET_RECEIVING_FAILED(21, "获取易仓采购单关联入库单失败"),
    SYNC_EC_PO_RECEIVING_WAIT_GSP(23, "GSP暂无任何收货记录"),
    SYNC_GSP_PO_RECEIVING_WAIT_EC(24, "EC暂无任何收货记录"),
    SYNC_EC_PO_RECEIVING_PART(30, "同步部分收货"),
    SYNC_EC_PO_RECEIVING_NO_WAIT_PART(35, "同步部分收货且不等待剩余"),
    SYNC_EC_PO_RECEIVING_ALL(40, "同步全部收货"),
    ;

    private Integer value;
    private String desc;

    Gsp2EccangPOCreateEventBizState(Integer value, String desc){
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue(){
        return this.value;
    }

    public String getDesc() {
        return this.desc;
    }
}