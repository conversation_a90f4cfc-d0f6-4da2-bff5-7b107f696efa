package com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Data
public class VVICGetItemDetialResponse {
    /**
     * 返回结果语言
     */
    private String lang;

    /**
     * 商品列表
     */
    private List<Item> item_list;

    @Getter
    @Setter
    public static class Item {
        /**
         *商品详情
         */
        private String desc;

        /**
         *价格
         */
        private BigDecimal price;

        /**
         *状态1：在架，0：下架
         */
        private Integer status;

        /**
         *商品VVIC ID
         */
        private String item_vid;

        /**
         *商品ID
         */
        private Integer item_id;

        /**
         *商品标题
         */
        private String item_title;

        /**
         *档口VVIC ID
         */
        private String shop_vid;

        /**
         *档口名称
         */
        private String shop_name;

        /**
         *市场
         */
        private String market_code;

        /**
         * 颜色
         */
        private String color;

        /**
         * 颜色图片
         */
        private String color_imgs;

        /**
         *商品一级类目
         */
        private String category_name_one;

        /**
         *商品一级类目id
         */
        private String category_id_one;

        /**
         *商品1.5级类目
         */
        private String category_name_sub;

        /**
         *商品1.5级类目id
         */
        private String category_id_sub;

        /**
         *	商品二级类目
         */
        private String category_name_two;

        /**
         *	商品二级类目id
         */
        private String category_id_two;

        /**
         *供货能力等级
         */
        private Integer supply_level;

        /**
         *商品重量分类,详细划分见: 重量分类与重量对应表
         */
        private Integer weight_type;

        /**
         *首图
         */
        private String item_view_image;

        /**
         *橱窗图
         */
        private String list_grid_image;

        /**
         * 视频
         */
        private String video_url;
        /**
         *创建时间
         */
        private String create_time;

        /**
         *更新时间
         */
        private String update_time;

        /**
         * 上架时间
         */
        private String up_time;

        /**
         * 下架时间
         */
        private String expired_time;

        /**
         * 货号
         */
        private String art_no;

        /**
         * 属性列表
         */
        private String attr_str;

        /**
         * SKU列表
         */
        private List<Sku> skuList;

        /**
         * 档口数据
         */
        private Shop shop;
    }

    @Getter
    @Setter
    public static class Sku {
        /**
         * 颜色
         */
        private String color;

        /**
         * 尺码
         */
        private String size;

        /**
         *价格
         */
        private BigDecimal price;

        /**
         * 状态
         */
        private Integer status;

        /**
         * SKU VID
         */
        private String sku_vid;

        /**
         * SKU ID
         */
        private String sku_id;

        /**
         * 颜色图片
         */
        private String color_img;

        /**
         * 颜色ID
         */
        private String color_id;

        /**
         * 尺码ID
         */
        private String size_id;

        /**
         * 是否缺货，0、2-不缺货，1-缺货
         */
        private Integer is_lack;

        /**
         * 库存信息
         */
        private Integer stock;
        /**
         * 预计到货时间
         */
        private Long arrive_time;

    }

    @Getter
    @Setter
    public static class Shop {
        /**
         * 档口信用等级,3-信用极好 2-信用良好
         */
        private Integer credit_level;

        /**
         * 准时发货率,0-1,1为100%
         */
        private Integer ratio_of_delivery_on_time;

        /**
         * 退货成功率,0-1,1为100%
         */
        private Integer ratio_of_return_success;
    }
}
