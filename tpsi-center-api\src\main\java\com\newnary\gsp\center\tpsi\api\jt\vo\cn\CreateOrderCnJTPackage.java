package com.newnary.gsp.center.tpsi.api.jt.vo.cn;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * 2023-12-26
 *
 * <AUTHOR>
 */
@Data
public class CreateOrderCnJTPackage {

    @NotNull(message = "packageLengthD(不能为空)")
    private BigDecimal packageLengthD;


    @NotNull(message = "packageLengthD(不能为空)")
    private BigDecimal packageWidthD;


    @NotNull(message = "packageLengthD(不能为空)")
    private BigDecimal packageHeightD;


    @NotNull(message = "packageLengthD(不能为空)")
    private BigDecimal packageWeightD;


}
