package com.newnary.gsp.center.tpsi.infra.rpc;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.api.base.common.PageList;
import com.newnary.gsp.center.product.api.category.CategoryApi;
import com.newnary.gsp.center.product.api.category.request.CategoryPageQueryCommand;
import com.newnary.gsp.center.product.api.category.request.CategoryQueryCommand;
import com.newnary.gsp.center.product.api.category.response.CategoryInfo;
import com.newnary.gsp.center.product.api.category.response.CategoryNode;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class CategoryRpc {

    @Resource
    private CategoryApi categoryApi;

    public CategoryInfo getCategoryById(String categoryId) {
        return categoryApi.get(categoryId).mustSuccessOrThrowOriginal();
    }

    public PageList<CategoryNode> pageQuerySubCategory(CategoryPageQueryCommand categoryPageQueryCommand) {
        return categoryApi.pageQuerySubCategory(categoryPageQueryCommand).mustSuccessOrThrowOriginal();
    }
}
