package com.newnary.gsp.center.tpsi.ctrl.open1688;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.common.CommonResponse;
import com.newnary.api.base.constants.HeaderNameConstants;
import com.newnary.api.base.exception.BaseErrorInfo;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierSkuStartSupplyReq;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierSkuStopSupplyReq;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierSpuOptSupplyStateReq;
import com.newnary.gsp.center.product.api.product.request.SupplierSpuCreateV2Command;
import com.newnary.gsp.center.product.api.product.request.SupplierSpuUpdateV2Command;
import com.newnary.gsp.center.tpsi.api.open1688.Open1688Api;
import com.newnary.gsp.center.tpsi.api.open1688.request.*;
import com.newnary.gsp.center.tpsi.api.open1688.response.*;
import com.newnary.gsp.center.tpsi.app.job.Open1688JobManager;
import com.newnary.gsp.center.tpsi.ctrl.basesystembiz.BaseSystemBizId;
import com.newnary.gsp.center.tpsi.ctrl.open1688.entry.Open1688Message;
import com.newnary.gsp.center.tpsi.ctrl.open1688.entry.ProductMessageContent;
import com.newnary.gsp.center.tpsi.ctrl.open1688.entry.UpdateProductStockMessageContent;
import com.newnary.gsp.center.tpsi.infra.client.open1688.util.SecurityUtil;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.bijia.request.QueryOpen1688MultiLanguageKeywordRequest;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.bijia.request.QueryOpen1688MultiLanguageProductDetailRequest;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.bijia.response.QueryOpen1688MultiLanguageKeywordResponse;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.bijia.response.QueryOpen1688MultiLanguageProductDetailResponse;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.comon.response.Category1688;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.request.QueryOpen1688ProductByImageUrlRequest;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.request.QueryProductRequest;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.request.SearchByKeywordsRequest;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.response.QueryOpen1688ProductByImageUrlResponse;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.response.QueryProductResponse;
import com.newnary.gsp.center.tpsi.infra.client.vvic.params.VVICBaseParam;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingInfo;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingType;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.ThirdPartyMappingManager;
import com.newnary.gsp.center.tpsi.infra.rpc.OpenSupplierProductRpc;
import com.newnary.gsp.center.tpsi.service.open1688.Open1688Service;
import com.newnary.spring.cloud.context.RequestContext;
import com.newnary.tenant.context.TenantCarrier;
import com.newnary.tenant.context.TenantID;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static com.newnary.gsp.center.tpsi.infra.client.open1688.util.StringUtil.encodeHexStr;
import static com.newnary.gsp.center.tpsi.infra.client.open1688.util.StringUtil.toBytes;

@RestController
@Slf4j
public class Open1688ApiImpl implements Open1688Api {

    @Resource
    private Open1688Service open1688ServiceImpl;

    @Resource
    private OpenSupplierProductRpc openSupplierProductRpc;

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Autowired
    private Open1688JobManager open1688JobManager;

    @Resource
    private ThirdPartyMappingManager thirdPartyMappingManager;

    @Resource
    private BaseSystemBizId baseSystemBizId;

    //----------  1688消息通知 --------------------------------------------------------
    @Override
    public CommonResponse<String> open1688Webhooks(String tenantID, String message) {

        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        String referer = request.getHeader("Referer");
        String host = request.getHeader("Host");
        String origin = request.getHeader("Origin");
        log.info("1688消息请求来源，host={}，origin={},referer={}", host, origin, referer);
/*        String tenantID = RequestContext.assertHeader(HeaderNameConstants.TENANT_ID);*/

        if (StringUtils.isBlank(tenantID)) {
            return CommonResponse.success("租户id必填");
        }else {
            TenantCarrier.setTenantID(new TenantID(tenantID));
        }
        String bizId = baseSystemBizId.get("OPEN1688");
        log.info("1688推送商品信息请求开始，tenantID={}，bizId={}", tenantID, bizId);
        if (null == RequestContextHolder.getRequestAttributes()) {
            return CommonResponse.success("request is null");
        }
        try {
            String messageDecode = URLDecoder.decode(message, "UTF-8");
            log.info("1688推送消息内容={} ", messageDecode);
            String result = "{\"".concat(messageDecode.replace("&", ",").replace("=", ":")).concat("\"}").replace("message", "message\"").replace("_aop_signature:", "\"aop_signature\":\"");
            JSONObject messageObj = JSON.parseObject(result);
            String aop_signature = messageObj.getString("aop_signature");

            Open1688Message open1688Message = JSON.parseObject(messageObj.getString("message"), Open1688Message.class);

            //间隔时间太长的消息不做处理
            if (new Date().getTime() - Long.parseLong(open1688Message.getGmtBorn()) > 6000) {
                throw new ServiceException(new BaseErrorInfo("OPEN1688_TIMEINTERVAL", "时间间隔太长"));
            }
            //校验签名
            if (!checkSign(aop_signature, messageDecode)) {
                throw new ServiceException(new BaseErrorInfo("OPEN1688_SIGNERROR", "签名错误"));
            }

            ThirdPartySystem thirdPartySystem = loadSystem(bizId);
            //消息处理
            return dealMessage(thirdPartySystem, open1688Message);
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            Optional<TenantID> optionalTenantID = TenantCarrier.getTenantID();
            if (optionalTenantID.isPresent()) {
                TenantCarrier.clearTenantID();
            }
        }
        return null;
    }

    //消息处理
    private synchronized CommonResponse<String> dealMessage(ThirdPartySystem thirdPartySystem, Open1688Message open1688Message) {
        //判断消息类型
        ProductMessageContent productMessageContent;
        switch (open1688Message.getType()) {
            //产品下架
            case "PRODUCT_RELATION_VIEW_PRODUCT_EXPIRE":
                productMessageContent = JSON.parseObject(open1688Message.getData(), ProductMessageContent.class);
                if (ObjectUtils.isNotEmpty(productMessageContent)) {
                    String[] split = productMessageContent.getProductIds().split(",");
                    for (String id : split) {
                        QueryProductRequest queryProductRequest = new QueryProductRequest();
                        queryProductRequest.setOfferId(id);
                        QueryProductResponse queryProductResponse = open1688ServiceImpl.queryProductById(thirdPartySystem, queryProductRequest);
                        if (ObjectUtils.isEmpty(queryProductResponse)) return CommonResponse.success("商品不存在");
                        String status = queryProductResponse.getStatus();
                        ThirdPartyMappingInfo newestInfoByTarget = thirdPartyMappingManager.getNewestInfoByTarget("OPEN1688", "GSP", id, ThirdPartyMappingType.PRODUCT_ID);
                        if (ObjectUtils.isNotEmpty(newestInfoByTarget) && ("member expired".equals(status) || "auto expired".equals(status) || "expired".equals(status) || "member deleted".equals(status) || "deleted".equals(status))) {
                            //spu下架
                            stopSpuSupply(thirdPartySystem, queryProductResponse.getProductID().toString());

                            //sku下架
                            stopSupply(thirdPartySystem,queryProductResponse);
                        }
                    }
                }
                return CommonResponse.success("success to expire");
            //产品删除
            case "PRODUCT_RELATION_VIEW_PRODUCT_DELETE":
                productMessageContent = JSON.parseObject(open1688Message.getData(), ProductMessageContent.class);
                if (ObjectUtils.isNotEmpty(productMessageContent)) {
                    String[] split = productMessageContent.getProductIds().split(",");
                    for (String id : split) {
                        QueryProductRequest queryProductRequest = new QueryProductRequest();
                        queryProductRequest.setOfferId(id);
                        //查询商品明细
                        QueryProductResponse queryProductResponse = open1688ServiceImpl.queryProductById(thirdPartySystem, queryProductRequest);
                        if (ObjectUtils.isEmpty(queryProductResponse)) return CommonResponse.success("商品不存在");
                        String status = queryProductResponse.getStatus();
                        ThirdPartyMappingInfo newestInfoByTarget = thirdPartyMappingManager.getNewestInfoByTarget("OPEN1688", "GSP", id, ThirdPartyMappingType.PRODUCT_ID);
                        if (ObjectUtils.isNotEmpty(newestInfoByTarget) && ("member expired".equals(status) || "auto expired".equals(status) || "expired".equals(status) || "member deleted".equals(status) || "deleted".equals(status))) {
                            //spu下架
                            stopSpuSupply(thirdPartySystem, queryProductResponse.getProductID().toString());

                            //sku下架
                            stopSupply(thirdPartySystem,queryProductResponse);
                        }
                    }
                }
                return CommonResponse.success("success to delete");
            //产品新增或者修改
            case "PRODUCT_RELATION_VIEW_PRODUCT_NEW_OR_MODIFY":
                productMessageContent = JSON.parseObject(open1688Message.getData(), ProductMessageContent.class);
                if (ObjectUtils.isNotEmpty(productMessageContent)) {
                    String[] split = productMessageContent.getProductIds().split(",");
                    for (String id : split) {
                        QueryProductRequest queryProductRequest = new QueryProductRequest();
                        queryProductRequest.setOfferId(id);
                        //查询商品明细
                        QueryProductResponse queryProductResponse = open1688ServiceImpl.queryProductById(thirdPartySystem, queryProductRequest);
                        if (ObjectUtils.isEmpty(queryProductResponse)) return CommonResponse.success("商品不存在");
                        String status = queryProductResponse.getStatus();
                        ThirdPartyMappingInfo newestInfoByTarget = thirdPartyMappingManager.getNewestInfoByTarget("OPEN1688", "GSP", id, ThirdPartyMappingType.PRODUCT_ID);
                        if (ObjectUtils.isNotEmpty(newestInfoByTarget) && "modified".equals(status)) {
                            SupplierSpuCreateV2Command spuCreateV2Command = open1688JobManager.buildSupplierSpuCreateV2Command(thirdPartySystem, queryProductResponse);
                            //构建更新商品参数
                            SupplierSpuUpdateV2Command supplierSpuUpdateV2Command = buildSupplierSpuUpdateV2Command(newestInfoByTarget.getSourceId(), spuCreateV2Command);
                            //TODO 执行更新
                            //更新商品库存
                            open1688JobManager.updateStock(thirdPartySystem,queryProductResponse);
                            //更新商品价格
                            open1688JobManager.updatePrice(thirdPartySystem,queryProductResponse);
                        } else if ("new".equals(status)) {
                            SupplierSpuCreateV2Command spuCreateV2Command = open1688JobManager.buildSupplierSpuCreateV2Command(thirdPartySystem, queryProductResponse);
                            //创建商品
                            String spuId = openSupplierProductRpc.createSpu(spuCreateV2Command);
                            if (StringUtils.isNotBlank(spuId)) {
                                //保存商品映射关系
                                thirdPartyMappingManager.insertOrUpdate("GSP", "OPEN1688", spuId, queryProductResponse.getProductID().toString(), ThirdPartyMappingType.PRODUCT_ID.name(), queryProductResponse);

                                //更新商品价格
                                open1688JobManager.updatePrice(thirdPartySystem, queryProductResponse);

                                //更新商品库存
                                open1688JobManager.updateStock(thirdPartySystem, queryProductResponse);
                            }
                        }
                    }
                }
                return CommonResponse.success("success to insertOrUpdate");
            //产品上架
            case "PRODUCT_RELATION_VIEW_PRODUCT_REPOST":
                productMessageContent = JSON.parseObject(open1688Message.getData(), ProductMessageContent.class);
                if (ObjectUtils.isNotEmpty(productMessageContent)) {
                    String[] split = productMessageContent.getProductIds().split(",");
                    for (String id : split) {
                        QueryProductRequest queryProductRequest = new QueryProductRequest();
                        queryProductRequest.setOfferId(id);
                        QueryProductResponse queryProductResponse = open1688ServiceImpl.queryProductById(thirdPartySystem, queryProductRequest);
                        if (ObjectUtils.isEmpty(queryProductResponse)) return CommonResponse.success("商品不存在");
                        ThirdPartyMappingInfo newestInfoByTarget = thirdPartyMappingManager.getNewestInfoByTarget("OPEN1688", "GSP", queryProductResponse.getProductID().toString(), ThirdPartyMappingType.PRODUCT_ID);
                        String status = queryProductResponse.getStatus();
                        if (ObjectUtils.isNotEmpty(newestInfoByTarget) && "published".equals(status)) {
                            //spu上架
                            startSpuSupply(thirdPartySystem, queryProductResponse.getProductID().toString());

                            //sku上架
                            startSupply(thirdPartySystem,queryProductResponse);
                        }
                    }
                }
                return CommonResponse.success("success to repost");
            //产品库存变更
            case "PRODUCT_PRODUCT_INVENTORY_CHANGE":
                UpdateProductStockMessageContent updateProductStockMessageContent = JSON.parseObject(open1688Message.getData(), UpdateProductStockMessageContent.class);
                if (ObjectUtils.isNotEmpty(updateProductStockMessageContent)) {
                    List<UpdateProductStockMessageContent.OfferInventoryChange> offerInventoryChangeList = updateProductStockMessageContent.getOfferInventoryChangeList();
                    if (CollectionUtils.isNotEmpty(offerInventoryChangeList)) {
                        offerInventoryChangeList.forEach(offerInventoryChange -> {
                            QueryProductRequest queryProductRequest = new QueryProductRequest();
                            queryProductRequest.setOfferId(offerInventoryChange.getOfferId());
                            QueryProductResponse queryProductResponse = open1688ServiceImpl.queryProductById(thirdPartySystem, queryProductRequest);
                            if (ObjectUtils.isEmpty(queryProductResponse)) return;
                            ThirdPartyMappingInfo newestInfoByTarget = thirdPartyMappingManager.getNewestInfoByTarget("OPEN1688", "GSP", queryProductResponse.getProductID().toString(), ThirdPartyMappingType.PRODUCT_ID);
                            if (ObjectUtils.isNotEmpty(queryProductResponse) && ObjectUtils.isNotEmpty(newestInfoByTarget)) {
                                //更新商品库存
                                open1688JobManager.updateStock(thirdPartySystem, queryProductResponse);
                            }
                        });
                    }
                }
                return CommonResponse.success("success to change stock");

            default:
                throw new ServiceException(new BaseErrorInfo("OPEN1688_TYPEERROR", "不处理此消息类型: ".concat(open1688Message.getType())));
        }
    }

    //签名检验
    public Boolean checkSign(String sign, String messageStr) {
        String[] datas = new String[1];
        String[] split = messageStr.split("&");
        datas[0] = split[0].replace("=", "");
        byte[] signature = SecurityUtil.hmacSha1(datas, toBytes("iFKINd1hsNw"));
        String signStr = encodeHexStr(signature);
        boolean result = sign.equals(signStr);
        log.info("1688消息签名校验：传送值：{} 生成值：{}", sign, signStr);
        return result;
    }

    private SupplierSpuUpdateV2Command buildSupplierSpuUpdateV2Command(String supplierSpuId, SupplierSpuCreateV2Command spuCreateV2Command) {
        SupplierSpuUpdateV2Command supplierSpuUpdateV2Command = new SupplierSpuUpdateV2Command();
        supplierSpuUpdateV2Command.setSupplierSpuId(supplierSpuId);
        supplierSpuUpdateV2Command.setSupplierId(spuCreateV2Command.getSupplierId());
        supplierSpuUpdateV2Command.setDescInfos(spuCreateV2Command.getDescInfos());
        supplierSpuUpdateV2Command.setDefaultLocale(spuCreateV2Command.getDefaultLocale());
        supplierSpuUpdateV2Command.setCustomCode(spuCreateV2Command.getCustomCode());
        supplierSpuUpdateV2Command.setCustomBrandId(spuCreateV2Command.getCustomBrandId());
        supplierSpuUpdateV2Command.setCategoryId(spuCreateV2Command.getCategoryId());
        supplierSpuUpdateV2Command.setCustomCategoryId(spuCreateV2Command.getCustomCategoryId());
        supplierSpuUpdateV2Command.setMgmtCategoryLevel(spuCreateV2Command.getMgmtCategoryLevel());
        supplierSpuUpdateV2Command.setMgmtCategoryId(spuCreateV2Command.getMgmtCategoryId());
        supplierSpuUpdateV2Command.setMainImages(spuCreateV2Command.getMainImages());
        supplierSpuUpdateV2Command.setDetailImages(spuCreateV2Command.getDetailImages());
        supplierSpuUpdateV2Command.setVideos(spuCreateV2Command.getVideos());
        supplierSpuUpdateV2Command.setCountryOfOriginCode(spuCreateV2Command.getCountryOfOriginCode());
        supplierSpuUpdateV2Command.setLogisticsAttrInfo(spuCreateV2Command.getLogisticsAttrInfo());
        supplierSpuUpdateV2Command.setSkuList(spuCreateV2Command.getSkuList());
        supplierSpuUpdateV2Command.setParamsInfo(spuCreateV2Command.getParamsInfo());
        supplierSpuUpdateV2Command.setIsAutoAuditPass(spuCreateV2Command.getIsAutoAuditPass());
        supplierSpuUpdateV2Command.setIsCheckAttribute(spuCreateV2Command.getIsCheckAttribute());
        supplierSpuUpdateV2Command.setOperator(spuCreateV2Command.getOperator());

        return supplierSpuUpdateV2Command;
    }

    //sku商品停止供应（下架）
    private void stopSupply(ThirdPartySystem thirdPartySystem, QueryProductResponse queryProductResponse) {
        OpenSupplierSkuStopSupplyReq req = new OpenSupplierSkuStopSupplyReq();
        VVICBaseParam vvicBaseParam = JSON.parseObject(thirdPartySystem.getParams(), VVICBaseParam.class);
        req.setSupplierId(vvicBaseParam.getSupplierId());
        if (null != queryProductResponse && CollectionUtils.isNotEmpty(queryProductResponse.getProductSkuInfos())) {
            queryProductResponse.getProductSkuInfos().forEach(sku -> {
                req.setCustomSkuCode(sku.getSkuId().toString());
                openSupplierProductRpc.stopSupply(req);
            });
        }
    }

    //sku商品开始供应（上架）
    private void startSupply(ThirdPartySystem thirdPartySystem, QueryProductResponse queryProductResponse) {
        OpenSupplierSkuStartSupplyReq req = new OpenSupplierSkuStartSupplyReq();
        VVICBaseParam vvicBaseParam = JSON.parseObject(thirdPartySystem.getParams(), VVICBaseParam.class);
        req.setSupplierId(vvicBaseParam.getSupplierId());
        if (null != queryProductResponse && CollectionUtils.isNotEmpty(queryProductResponse.getProductSkuInfos())) {
            queryProductResponse.getProductSkuInfos().forEach(sku -> {
                req.setCustomSkuCode(sku.getSkuId().toString());
                openSupplierProductRpc.startSupply(req);
            });
        }
    }

    //spu商品停止供应
    private void stopSpuSupply(ThirdPartySystem thirdPartySystem, String itemId) {
        OpenSupplierSpuOptSupplyStateReq req = new OpenSupplierSpuOptSupplyStateReq();
        VVICBaseParam vvicBaseParam = JSON.parseObject(thirdPartySystem.getParams(), VVICBaseParam.class);
        req.setSupplierId(vvicBaseParam.getSupplierId());
        req.setCustomCode(itemId);
        openSupplierProductRpc.stopSpuSupply(req);
    }

    //spu商品开始供应
    private void startSpuSupply(ThirdPartySystem thirdPartySystem, String itemId) {
        OpenSupplierSpuOptSupplyStateReq req = new OpenSupplierSpuOptSupplyStateReq();
        VVICBaseParam vvicBaseParam = JSON.parseObject(thirdPartySystem.getParams(), VVICBaseParam.class);
        req.setSupplierId(vvicBaseParam.getSupplierId());
        req.setCustomCode(itemId);
        openSupplierProductRpc.startSpuSupply(req);
    }

    private String readRequestBody(InputStream inputStream) throws IOException {
        char[] buffer = new char[1024];
        final StringBuilder out = new StringBuilder();
        try (Reader in = new InputStreamReader(inputStream, StandardCharsets.UTF_8)) {
            for (; ; ) {
                int rsz = in.read(buffer, 0, buffer.length);
                if (rsz < 0)
                    break;
                out.append(buffer, 0, rsz);
            }
        }
        return out.toString();
    }

    public ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }

    public ThirdPartySystem loadSystemByBizIdAndName(String systemProvider, String name) {
        return thirdPartySystemRepository.loadSystemByBizIdAndName(systemProvider, name)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }

    //--------------------------------------------------------------------------------------------------------------

    @Override
    public CommonResponse<QueryCategoryByIdResponse> getCategoryById(String categoryId) {
        QueryCategoryByIdResponse queryCategoryByIdResponse = new QueryCategoryByIdResponse();
        try {
            TenantCarrier.setTenantID(new TenantID(RequestContext.assertHeader(HeaderNameConstants.TENANT_ID)));
            Category1688 open1688 = open1688ServiceImpl.getCategoryById(loadSystem(baseSystemBizId.get("OPEN1688")), categoryId);
            if (null != open1688) PropertyUtils.copyProperties(queryCategoryByIdResponse, open1688);
        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            e.printStackTrace();
        } finally {
            Optional<TenantID> optionalTenantID = TenantCarrier.getTenantID();
            if (optionalTenantID.isPresent()) {
                TenantCarrier.clearTenantID();
            }
        }
        return CommonResponse.success(queryCategoryByIdResponse);
    }

    @Override
    public CommonResponse<SearchByKeywordsResponse> searchByKeywords(@RequestBody SearchKeyWordsCommand command) {
        if (null == command) return CommonResponse.success(new SearchByKeywordsResponse());
        SearchByKeywordsResponse searchByKeywordsResponse = null;
        try {
            TenantCarrier.setTenantID(new TenantID(RequestContext.assertHeader(HeaderNameConstants.TENANT_ID)));
            SearchByKeywordsRequest searchByKeywordsRequest = JSON.parseObject(JSON.toJSONString(command), SearchByKeywordsRequest.class);
            searchByKeywordsResponse = JSON.parseObject(JSON.toJSONString(open1688ServiceImpl.searchByKeywords(loadSystem(baseSystemBizId.get("OPEN1688")), searchByKeywordsRequest)), SearchByKeywordsResponse.class);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            Optional<TenantID> optionalTenantID = TenantCarrier.getTenantID();
            if (optionalTenantID.isPresent()) {
                TenantCarrier.clearTenantID();
            }
        }
        return CommonResponse.success(searchByKeywordsResponse);
    }

    @Override
    public CommonResponse<QueryProductByIdResponse> queryProductByCustomCode(@RequestBody Query1688ProductCommand command) {
        QueryProductByIdResponse queryProductByIdResponse = null;
        try {
            TenantCarrier.setTenantID(new TenantID(RequestContext.assertHeader(HeaderNameConstants.TENANT_ID)));
            QueryProductRequest queryProductRequest = new QueryProductRequest();
            queryProductRequest.setOfferId(command.getCustomCode());
            QueryProductResponse queryProductResponse = open1688ServiceImpl.queryProductById(loadSystem(baseSystemBizId.get("OPEN1688")), queryProductRequest);
            if (null != queryProductResponse) {
                queryProductByIdResponse = JSON.parseObject(JSON.toJSONString(queryProductResponse), QueryProductByIdResponse.class);
            }
            ;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            Optional<TenantID> optionalTenantID = TenantCarrier.getTenantID();
            if (optionalTenantID.isPresent()) {
                TenantCarrier.clearTenantID();
            }
        }
        return CommonResponse.success(queryProductByIdResponse);
    }


    //图搜
    @Override
    public CommonResponse<com.newnary.gsp.center.tpsi.api.open1688.response.QueryOpen1688ProductByImageUrlResponse> queryProductImageUrl(@RequestBody Query1688ProductImageUrlCommand command) {
        if (ObjectUtils.isEmpty(command)) return CommonResponse.success(null);
        QueryOpen1688ProductByImageUrlRequest request = new QueryOpen1688ProductByImageUrlRequest();
        buildQueryOpen1688ProductByImageUrlRequest(request, command);
        try {
            QueryOpen1688ProductByImageUrlResponse urlResponse = open1688ServiceImpl.queryOpen1688ProductByImageUrl(loadSystem(baseSystemBizId.get("OPEN1688")), request);
            if (ObjectUtils.isNotEmpty(urlResponse)) {
                /*List<QueryOpen1688ProductByImageUrlResult> result = urlResponse.getResult();
               if (CollectionUtils.isNotEmpty(result)) {
                    return CommonResponse.success(result.stream().map(QueryOpen1688ProductByImageUrlResult::getOfferId).collect(Collectors.toList()));
                }*/
                return CommonResponse.success(dealWithPrice(JSON.parseObject(JSON.toJSONString(urlResponse), com.newnary.gsp.center.tpsi.api.open1688.response.QueryOpen1688ProductByImageUrlResponse.class)));
            }
        } catch (Exception e) {
            log.info("查询失败：{}",e.getMessage());
        }
        return CommonResponse.success(null);
    }

    // 多语言关键词搜索
    @Override
    public CommonResponse<Query1688MultiLanguageKeywordResponse> queryMultiLanguageKeyWords(@RequestBody Query1688MultiLanguageKeywordCommand command) {
        if (null == command) return CommonResponse.success(new Query1688MultiLanguageKeywordResponse());
        Query1688MultiLanguageKeywordResponse response = new Query1688MultiLanguageKeywordResponse();
        try {
            TenantCarrier.setTenantID(new TenantID(RequestContext.assertHeader(HeaderNameConstants.TENANT_ID)));
            QueryOpen1688MultiLanguageKeywordRequest request = buildQueryOpen1688MultiLanguageKeywordRequest(command);
            QueryOpen1688MultiLanguageKeywordResponse clientResponse = open1688ServiceImpl.queryMultiLanguageKeyWords(loadSystemByBizIdAndName("OPEN1688", "1688跨境代采寻源比价"), request);
            response = convertKeyWordsData(clientResponse, response);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            Optional<TenantID> optionalTenantID = TenantCarrier.getTenantID();
            if (optionalTenantID.isPresent()) {
                TenantCarrier.clearTenantID();
            }
        }
        return CommonResponse.success(response);
    }

    private QueryOpen1688MultiLanguageKeywordRequest buildQueryOpen1688MultiLanguageKeywordRequest(Query1688MultiLanguageKeywordCommand command) {
        QueryOpen1688MultiLanguageKeywordRequest request = new QueryOpen1688MultiLanguageKeywordRequest();
        QueryOpen1688MultiLanguageKeywordRequest.OfferQueryParam offerQueryParam = new QueryOpen1688MultiLanguageKeywordRequest.OfferQueryParam();
        request.setOfferQueryParam(offerQueryParam);
        offerQueryParam.setBeginPage(command.getBeginPage());
        offerQueryParam.setPageSize(command.getPageSize());
        offerQueryParam.setKeyword(command.getKeyword());
        offerQueryParam.setCountry(command.getCountry());
        return request;
    }

    private Query1688MultiLanguageKeywordResponse convertKeyWordsData(QueryOpen1688MultiLanguageKeywordResponse source, Query1688MultiLanguageKeywordResponse target){
        if (ObjectUtils.isNotEmpty(source)){
            if (ObjectUtils.isNotEmpty(source.getResult())){
                Query1688MultiLanguageKeywordResponse.PageInfo pageInfo = new Query1688MultiLanguageKeywordResponse.PageInfo();
                pageInfo.setCurrentPage(source.getResult().getCurrentPage());
                pageInfo.setPageSize(source.getResult().getPageSize());
                pageInfo.setTotalPage(source.getResult().getTotalPage());
                pageInfo.setTotalRecords(source.getResult().getTotalRecords());
                target.setPageInfo(pageInfo);
                if (ObjectUtils.isNotEmpty(source.getResult().getData())){
                    Query1688MultiLanguageKeywordResponse response = JSON.parseObject(JSON.toJSONString(source.getResult()), Query1688MultiLanguageKeywordResponse.class);
                    target.setData(response.getData());
                }
            }
        }
        return target;
    }

    // 多语言商详
    @Override
    public CommonResponse<Query1688MultiLanguageProductDetailResponse> queryMultiLanguageProductDetail(@RequestBody Query1688MultiLanguageProductDetailCommand command) {
        if (null == command) return CommonResponse.success(new Query1688MultiLanguageProductDetailResponse());
        Query1688MultiLanguageProductDetailResponse response = new Query1688MultiLanguageProductDetailResponse();
        try {
            TenantCarrier.setTenantID(new TenantID(RequestContext.assertHeader(HeaderNameConstants.TENANT_ID)));
            QueryOpen1688MultiLanguageProductDetailRequest request = buildQueryOpen1688MultiLanguageProductDetailRequest(command);
            QueryOpen1688MultiLanguageProductDetailResponse clientResp = open1688ServiceImpl.queryMultiLanguageProductDetail(loadSystemByBizIdAndName("OPEN1688", "1688跨境代采寻源比价"), request);
            if (ObjectUtils.isNotEmpty(clientResp)){
                if (ObjectUtils.isNotEmpty(clientResp.getResult())){
                    response = JSON.parseObject(JSON.toJSONString(clientResp.getResult()), Query1688MultiLanguageProductDetailResponse.class);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            Optional<TenantID> optionalTenantID = TenantCarrier.getTenantID();
            if (optionalTenantID.isPresent()) {
                TenantCarrier.clearTenantID();
            }
        }
        return CommonResponse.success(response);
    }

    private QueryOpen1688MultiLanguageProductDetailRequest buildQueryOpen1688MultiLanguageProductDetailRequest(Query1688MultiLanguageProductDetailCommand command) {
        QueryOpen1688MultiLanguageProductDetailRequest request = new QueryOpen1688MultiLanguageProductDetailRequest();
        QueryOpen1688MultiLanguageProductDetailRequest.OfferDetailParam offerDetailParam = new QueryOpen1688MultiLanguageProductDetailRequest.OfferDetailParam();
        request.setOfferDetailParam(offerDetailParam);
        offerDetailParam.setOfferId(command.getOfferId());
        offerDetailParam.setCountry(command.getCountry());
        return request;
    }


    private com.newnary.gsp.center.tpsi.api.open1688.response.QueryOpen1688ProductByImageUrlResponse dealWithPrice(com.newnary.gsp.center.tpsi.api.open1688.response.QueryOpen1688ProductByImageUrlResponse response){
        List<QueryOpen1688ProductByImageUrlResult> resultList = response.getResult();
        if (CollectionUtils.isNotEmpty(resultList)) {
            for (QueryOpen1688ProductByImageUrlResult result : resultList) {
                QueryOpen1688ProductByImageUrlResult.OfferPrice offerPrice = result.getOfferPrice();
                if (null != offerPrice) {
                    List<QueryOpen1688ProductByImageUrlResult.QuantityPrice> quantityPriceList = offerPrice.getQuantityPrice();

                    if (CollectionUtils.isNotEmpty(quantityPriceList)) {
                        List<QueryOpen1688ProductByImageUrlResult.PriceRange> startQuantityPriceList = new ArrayList<>();
                        for (QueryOpen1688ProductByImageUrlResult.QuantityPrice quantityPrice : quantityPriceList) {
                            QueryOpen1688ProductByImageUrlResult.PriceRange priceRange = new QueryOpen1688ProductByImageUrlResult.PriceRange();
                            String quantity = quantityPrice.getQuantity();
                            if (quantity.contains("~")){
                                String[] split = quantity.split("~");
                                priceRange.setStartQuantity(Integer.valueOf(split[0]));
                            }else if (quantity.contains("≥")){
                                String[] split = quantity.split("≥");
                                priceRange.setStartQuantity(Integer.valueOf(split[1]));
                            }else {
                                priceRange.setStartQuantity(Integer.valueOf(quantity));
                            }

                            priceRange.setPrice(new BigDecimal(quantityPrice.getValue()));
                            startQuantityPriceList.add(priceRange);
                        }
                        offerPrice.setStartQuantityPrice(startQuantityPriceList);
                    }
                }
            }
        }
        return response;
    }


    private void buildQueryOpen1688ProductByImageUrlRequest(QueryOpen1688ProductByImageUrlRequest request,Query1688ProductImageUrlCommand command){
        QueryOpen1688ProductByImageUrlRequest.Param param = new QueryOpen1688ProductByImageUrlRequest.Param();
        Query1688ProductImageUrlCommand.Param param1 = command.getParam();
        request.setScenario(command.getScenario());
        param.setImageUrl(param1.getImageUrl());
        param.setFiller(param1.getFiller());
        param.setPageNum(param1.getPageNum());
        param.setPageSize(param1.getPageSize());
        param.setPriceEnd(param1.getPriceEnd());
        param.setPriceStart(param1.getPriceStart());
        param.setQuantityBegin(param1.getQuantityBegin());
        request.setParam(param);
    }

/*    private QueryProductByImageUrlResponse buildQueryProductByImageUrlResponse(QueryOpen1688ProductByImageUrlResponse urlResponse){
        QueryProductByImageUrlResponse queryProductByImageUrlResponse = new QueryProductByImageUrlResponse();
        QueryProductByImageUrlResponse.PageInfo pageInfo = queryProductByImageUrlResponse.getPageInfo();
        List<QueryProductByImageUrlResult> result = queryProductByImageUrlResponse.getResult();
        QueryOpen1688ProductByImageUrlResponse.PageInfo urlPageInfo = urlResponse.getPageInfo();
        List<QueryOpen1688ProductByImageUrlResult> urlResult = urlResponse.getResult();
        if (ObjectUtils.isNotEmpty(urlPageInfo)) {
            pageInfo.setCurrentPage(urlPageInfo.getCurrentPage());
            pageInfo.setPageSize(urlPageInfo.getPageSize());
            pageInfo.setTotalPage(urlPageInfo.getTotalPage());
            pageInfo.setTotalRecords(urlPageInfo.getTotalRecords());
        }
        if (ObjectUtils.isNotEmpty(urlResult)) {
            result = new ArrayList<>();
            List<QueryProductByImageUrlResult> finalResult = result;
            urlResult.forEach(r->{
                QueryProductByImageUrlResult queryProductByImageUrlResult = new QueryProductByImageUrlResult();
                queryProductByImageUrlResult.setOfferId(r.getOfferId());
                queryProductByImageUrlResult.setSubject(r.getSubject());
                finalResult.add(queryProductByImageUrlResult);
            });
        }
        return queryProductByImageUrlResponse;
    }*/
}
