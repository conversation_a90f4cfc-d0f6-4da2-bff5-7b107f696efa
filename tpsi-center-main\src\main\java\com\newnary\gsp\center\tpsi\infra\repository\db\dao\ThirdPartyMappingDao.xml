<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.newnary.gsp.center.tpsi.infra.repository.db.dao.ThirdPartyMappingDao">

<resultMap id="thirdPartyMappingPOResult" type="com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyMappingPO">
    <result column="source_id" property="sourceId"/>
    <result column="target_id" property="targetId"/>
    <result column="index_key" property="indexKey"/>
    <result column="biz_type" property="bizType"/>
    <result column="target_biz_id" property="targetBizId"/>
    <result column="source_biz_id" property="sourceBizId"/>
    <result column="extend_biz_info" property="extendBizInfo"/>
    <result column="target_category_path" property="targetCategoryPath"/>
    <result column="source_related_id" property="sourceRelatedId"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="gmt_modified" property="gmtModified"/>
    <result column="gmt_create" property="gmtCreate"/>
    <result column="id" property="id"/>
</resultMap>

<sql id="thirdPartyMappingPO_columns">
    source_id,
    target_id,
    index_key,
    biz_type,
    target_biz_id,
    source_biz_id,
    extend_biz_info,
    target_category_path,
    source_related_id,
    tenant_id,
    gmt_modified,
    gmt_create,
    id
</sql>

<sql id="thirdPartyMappingPO_sqlForInsert">
    source_id,
    target_id,
    index_key,
    biz_type,
    target_biz_id,
    source_biz_id,
    extend_biz_info,
    target_category_path,
    source_related_id,
    tenant_id,
    gmt_modified,
    gmt_create,
    id
</sql>

<sql id="thirdPartyMappingPO_columnsForInsert">
    #{sourceId},
    #{targetId},
    #{indexKey},
    #{bizType},
    #{targetBizId},
    #{sourceBizId},
    #{extendBizInfo},
    #{targetCategoryPath},
    #{sourceRelatedId},
    #{tenantId},
    REPLACE(unix_timestamp(NOW(3)),'.',''),
    REPLACE(unix_timestamp(NOW(3)),'.',''),
    #{id}
</sql>

<sql id="thirdPartyMappingPO_setterForUpdate">
    <set>
        gmt_modified = REPLACE(unix_timestamp(NOW(3)),'.',''),
        source_id = #{sourceId},
        target_id = #{targetId},
        index_key = #{indexKey},
        biz_type = #{bizType},
        target_biz_id = #{targetBizId},
        source_biz_id = #{sourceBizId},
        extend_biz_info = #{extendBizInfo},
        target_category_path = #{targetCategoryPath},
        source_related_id = #{sourceRelatedId},
    </set>
</sql>

<sql id="thirdPartyMappingPO_selector">
    select
    <include refid="thirdPartyMappingPO_columns"/>
    from third_party_mapping
</sql>

<sql id="thirdPartyMappingPO_query_segment">
    <trim prefix="WHERE" prefixOverrides="AND|OR">
        <if test="data.sourceId != null">
            AND source_id = #{data.sourceId}
        </if>
        <if test="data.targetId != null">
            AND target_id = #{data.targetId}
        </if>
        <if test="data.indexKey != null">
            AND index_key = #{data.indexKey}
        </if>
        <if test="data.bizType != null">
            AND biz_type = #{data.bizType}
        </if>
        <if test="data.targetBizId != null">
            AND target_biz_id = #{data.targetBizId}
        </if>
        <if test="data.sourceBizId != null">
            AND source_biz_id = #{data.sourceBizId}
        </if>
        <if test="data.extendBizInfo != null">
            AND extend_biz_info = #{data.extendBizInfo}
        </if>
        <if test="data.targetCategoryPath != null">
            AND target_category_path = #{data.targetCategoryPath}
        </if>
        <if test="data.sourceRelatedId != null">
            AND source_related_id = #{data.sourceRelatedId}
        </if>
        <if test="data.gmtModified != null">
            AND gmt_modified = #{data.gmtModified}
        </if>
        <if test="data.gmtCreate != null">
            AND gmt_create = #{data.gmtCreate}
        </if>
        <if test="data.id != null">
            AND id = #{data.id}
        </if>
        <if test="ins != null and ins.size() > 0">
            AND
            <foreach collection="ins" item="item" open="(" separator="and" close=")">
                ${item.column} in
                <foreach collection="item.values" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </foreach>
        </if>
        <if test="notIns != null and notIns.size() > 0">
            AND
            <foreach collection="notIns" item="item" open="(" separator="and" close=")">
                ${item.column} not in
                <foreach collection="item.values" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </foreach>
        </if>
        <if test="equals != null and equals.size() > 0">
            AND
            <foreach collection="equals" item="item" open="(" separator="and" close=")">
                ${item.column} = #{item.value}
            </foreach>
        </if>
        <if test="notEquals != null and notEquals.size() > 0">
            AND
            <foreach collection="notEquals" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ <> ]]> #{item.value}
            </foreach>
        </if>
        <if test="lts != null and lts.size() > 0">
            AND
            <foreach collection="lts" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ < ]]> #{item.value}
            </foreach>
        </if>
        <if test="gts != null and gts.size() > 0">
            AND
            <foreach collection="gts" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ > ]]> #{item.value}
            </foreach>
        </if>
        <if test="likes != null and likes.size() > 0">
            AND
            <foreach collection="likes" item="item" open="(" separator="and" close=")">
                ${item.column} like concat('%', #{item.value}, '%')
            </foreach>
        </if>
        <if test="ltes != null and ltes.size() > 0">
            AND
            <foreach collection="ltes" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ <= ]]> #{item.value}
            </foreach>
        </if>
        <if test="gtes != null and gtes.size() > 0">
            AND
            <foreach collection="gtes" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ >= ]]> #{item.value}
            </foreach>
        </if>
        <if test="ranges != null and ranges.size() > 0">
            AND
            <foreach collection="ranges" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ >= ]]> #{item.from} and ${item.column} <![CDATA[ < ]]> #{item.to}
            </foreach>
        </if>
        AND tenant_id = #{tenantId}
    </trim>
</sql>


<insert id="insert" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyMappingPO">
    insert into third_party_mapping
    (
        <include refid="thirdPartyMappingPO_sqlForInsert"/>
    )
    values
    (
        <include refid="thirdPartyMappingPO_columnsForInsert"/>
    )
</insert>

<update id="update" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyMappingPO">
    update third_party_mapping
    <include refid="thirdPartyMappingPO_setterForUpdate" />
    where id = #{id}
    and tenant_id = #{tenantId}
</update>


<update id="updateByQuery" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyMappingPO">
    update third_party_mapping
    <set>
        gmt_modified = REPLACE(unix_timestamp(NOW(3)),'.',''),
        source_id = #{update.sourceId},
        target_id = #{update.targetId},
        index_key = #{update.indexKey},
        biz_type = #{update.bizType},
        target_biz_id = #{update.targetBizId},
        source_biz_id = #{update.sourceBizId},
        extend_biz_info = #{update.extendBizInfo},
        target_category_path = #{update.targetCategoryPath},
        source_related_id = #{update.sourceRelatedId},
    </set>
    <include refid="thirdPartyMappingPO_query_segment"/>
</update>

<!-- Delete -->
<delete id="delete">
    delete from third_party_mapping
    <include refid="thirdPartyMappingPO_query_segment"/>
</delete>
<delete id="deleteById">
    delete from third_party_mapping
    where id = #{id}
    and tenant_id = #{tenantId}
</delete>





<!-- query class -->
<sql id="thirdPartyMappingPO_groupBy">
    <if test="groupBys != null and groupBys.size() > 0">
        group by
        <foreach collection="groupBys" item="item" open="" separator="," close="">
            #{item}
        </foreach>
    </if>
</sql>

<sql id="thirdPartyMappingPO_orderby">
    <if test="orderBys != null and orderBys.size() > 0">
        order by
        <foreach collection="orderBys" item="item" open="" separator="," close="">
            ${item.column} ${item.direction}
        </foreach>
    </if>
</sql>


<select id="query" resultMap="thirdPartyMappingPOResult" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyMappingPO">
    <include refid="thirdPartyMappingPO_selector"/>
    <include refid="thirdPartyMappingPO_query_segment"/>
    <include refid="thirdPartyMappingPO_groupBy"/>
    <include refid="thirdPartyMappingPO_orderby"/>
</select>



<select id="count" resultType="java.lang.Long">
    SELECT COUNT(1) FROM third_party_mapping
    <include refid="thirdPartyMappingPO_query_segment"/>
</select>

<select id="getById" resultMap="thirdPartyMappingPOResult">
    <include refid="thirdPartyMappingPO_selector"/>
    where id = #{id}
    and tenant_id = #{tenantId}
</select>

<select id="getByIdForUpdate" resultMap="thirdPartyMappingPOResult">
    <include refid="thirdPartyMappingPO_selector"/>
    where id = #{id}
    and tenant_id = #{tenantId}
    for update
</select>

</mapper>
