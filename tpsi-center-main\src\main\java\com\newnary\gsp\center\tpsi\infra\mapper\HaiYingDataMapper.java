package com.newnary.gsp.center.tpsi.infra.mapper;


import com.newnary.api.base.common.PageCondition;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingLazadaProductListOrderBy;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingOrderByType;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingStation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.mapstruct.Mapper;
import org.mapstruct.Named;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: jack
 * @CreateTime: 2022-9-5
 */
@Mapper
public interface HaiYingDataMapper {

    FastDateFormat SIMPLE_TIME_FORMAT = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");
    FastDateFormat SIMPLE_DATE_FORMAT = FastDateFormat.getInstance("yyyy-MM-dd");
    FastDateFormat MILL_TIME_FORMAT = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss.s");

    @Named("simpleTimeStr2Long")
    default Long simpleTimeStr2Long(String time) {
        if (StringUtils.isNotEmpty(time)) {
            try {
                return SIMPLE_TIME_FORMAT.parse(time).getTime();
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    @Named("simpleTimeLong2Str")
    default String simpleTimeLong2Str(Long time) {
        if (null != time) {
            return SIMPLE_TIME_FORMAT.format(new Date(time));
        }
        return null;
    }

    @Named("simpleDateStr2Long")
    default Long simpleDateStr2Long(String date) {
        if (StringUtils.isNotEmpty(date)) {
            try {
                return SIMPLE_DATE_FORMAT.parse(date).getTime();
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    @Named("simpleDateLong2Str")
    default String simpleDateLong2Str(Long date) {
        if (null != date) {
            return SIMPLE_DATE_FORMAT.format(new Date(date));
        }
        return null;
    }

    @Named("millTimeStr2Long")
    default Long millTimeStr2Long(String time) {
        if (StringUtils.isNotEmpty(time)) {
            try {
                return MILL_TIME_FORMAT.parse(time).getTime();
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    @Named("haiyingStation2Str")
    default String haiyingStation2Str(HaiYingStation station) {
        if (null != station) {
            return station.getSite();
        }
        return null;
    }

    @Named("haiyingStations2Strs")
    default List<String> haiyingStations2Strs(List<HaiYingStation> stations) {
        ArrayList<String> list = new ArrayList<>();
        if (null != stations && stations.size() > 0) {
            for (HaiYingStation haiYingStation : stations) {
                list.add(haiYingStation.getSite());
            }
            return list;
        }


        return list;
    }

    @Named("str2ListWithComma")
    default List<String> str2ListWithComma(String str) {
        if (StringUtils.isNotEmpty(str)) {
            return Stream.of(str.split(",")).collect(Collectors.toList());
        }
        return null;
    }

    @Named("str2ListWithSemicolon")
    default List<String> str2ListWithSemicolon(String str) {
        if (StringUtils.isNotEmpty(str)) {
            return Stream.of(str.split(";")).collect(Collectors.toList());
        }
        return null;
    }

    @Named("str2ListWithBr")
    default List<String> str2ListWithBr(String str) {
        if (StringUtils.isNotEmpty(str)) {
            return Stream.of(str.split("<br/>")).collect(Collectors.toList());
        }
        return null;
    }

    @Named("list2StrWithComma")
    default String list2StrWithComma(List<String> lists) {
        if (CollectionUtils.isNotEmpty(lists)) {
            StringBuffer ret = new StringBuffer();
            for (String list : lists) {
                ret.append(list.concat(","));
            }
            ret.setLength(ret.length() - 1);
            return ret.toString();
        }
        return null;
    }

    @Named("list2StrWithSemicolon")
    default String list2StrWithSemicolon(List<String> lists) {
        if (CollectionUtils.isNotEmpty(lists)) {
            StringBuffer ret = new StringBuffer();
            for (String list : lists) {
                ret.append(list.concat(";"));
            }
            ret.setLength(ret.length() - 1);
            return ret.toString();
        }
        return null;
    }

    @Named("str2Boolean")
    default Boolean str2Boolean(String str) {
        if (StringUtils.isNotEmpty(str)) {
            return str.equals("1");
        }
        return null;
    }

    @Named("haiYingLazadaProductListOrderBy2description")
    default String haiYingLazadaProductListOrderBy2description(HaiYingLazadaProductListOrderBy haiYingLazadaProductListOrderBy) {
        if (null != haiYingLazadaProductListOrderBy && StringUtils.isNotBlank(haiYingLazadaProductListOrderBy.name())){
            return haiYingLazadaProductListOrderBy.name();
        }
        return null;
    }

    @Named("haiYingOrderByType2description")
    default String haiYingOrderByType2description(HaiYingOrderByType haiYingOrderByType) {
        if (null != haiYingOrderByType && StringUtils.isNotBlank(haiYingOrderByType.name())){
            return haiYingOrderByType.name();
        }
        return null;
    }

    @Named("boolean2Str")
    default String boolean2Str(Boolean bol) {
        if (null != bol) {
            return bol ? "1" : "0";
        }
        return null;
    }

    @Named("pageCondition2CurrentPage")
    default String pageCondition2CurrentPage(PageCondition page) {
        if (null != page) {
            return String.valueOf(page.pageNum);
        }
        return null;
    }

    @Named("pageCondition2PageSize")
    default String pageCondition2PageSize(PageCondition page) {
        if (null != page) {
            return String.valueOf(page.pageSize);
        }
        return null;
    }

/*    @Named("alist2List")
    default List alist2List(List<Object> list) {
        List list2 = new ArrayList();
        list2.addAll(list);
        return list2;
    }*/


}
