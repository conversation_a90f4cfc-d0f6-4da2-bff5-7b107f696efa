package com.newnary.gsp.center.tpsi.infra.client.open1688;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.request.*;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.response.*;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 跨境代采ERP解决方案
 */
@Slf4j
@Getter
@Setter
public class Open1688CrossProcurementAgencyClient extends Open1688Client{

    /**
     * 跨境分销关键词搜索
     * com.alibaba.fenxiao/cross.keywords.search
     */
    private String searchByKeywords;

    /**
     * 跨境分销图搜
     * com.alibaba.fenxiao/cross.image.search
     */
    private String getProductByImageUrl;

    /**
     * 获取跨境商品详情接口
     * com.alibaba.fenxiao/cross.productInfo.get
     */
    private String queryProductById;

    /**
     * 跨境订单创建
     * com.alibaba.fenxiao/alibaba.trade.createCrossOrder
     */
    private String createOrder;

    /**
     * 订单详情查看(买家视角)
     * com.alibaba.fenxiao/alibaba.trade.get.buyerView
     */
    private String getOrderDetail;

    /**
     * 取消交易
     * com.alibaba.fenxiao/alibaba.trade.cancel
     */
    private String closeOrder;

    /**
     * 获取交易订单的物流信息(买家视角)
     * com.alibaba.fenxiao/alibaba.trade.getLogisticsInfos.buyerView
     */
    private String getLogisticsInfo;

    /**
     * 获取交易订单的物流跟踪信息(买家视角)
     * com.alibaba.fenxiao/alibaba.trade.getLogisticsTraceInfo.buyerView
     */
    private String getLogisticsTraceInfo;

    /**
     * 精选货源 分销严选商品列表查询
     * com.alibaba.fenxiao/alibaba.pifatuan.product.list
     */
    private String getJXHYProduct;

    /**
     * 精选货源 分销严选商品详情批量查询
     * com.alibaba.fenxiao/alibaba.pifatuan.product.detail.list
     */
    private String getJXHYProductDetail;

    public Open1688CrossProcurementAgencyClient(String params) {
        super(params);
        setGetCategoryById("param2/1/com.alibaba.product/alibaba.category.get/".concat(getAppKey()));
        setSearchByKeywords("param2/1/com.alibaba.fenxiao/cross.keywords.search/".concat(getAppKey()));
        setGetProductByImageUrl("param2/1/com.alibaba.fenxiao/cross.image.search/".concat(getAppKey()));
        setQueryProductById("param2/1/com.alibaba.fenxiao/cross.productInfo.get/".concat(getAppKey()));
        setCreateOrder("param2/1/com.alibaba.fenxiao/alibaba.trade.createCrossOrder/".concat(getAppKey()));
        setGetOrderDetail("param2/1/com.alibaba.fenxiao/alibaba.trade.get.buyerView/".concat(getAppKey()));
        setCloseOrder("param2/1/com.alibaba.fenxiao/alibaba.trade.cancel/".concat(getAppKey()));
        setGetLogisticsInfo("param2/1/com.alibaba.fenxiao/alibaba.trade.getLogisticsInfos.buyerView/".concat(getAppKey()));
        setGetLogisticsTraceInfo("param2/1/com.alibaba.fenxiao/alibaba.trade.getLogisticsTraceInfo.buyerView/".concat(getAppKey()));
        setGetJXHYProduct("param2/1/com.alibaba.fenxiao/alibaba.pifatuan.product.list/".concat(getAppKey()));
        setGetJXHYProductDetail("param2/2/com.alibaba.fenxiao/alibaba.pifatuan.product.detail.list/".concat(getAppKey()));
    }

    //关键词搜索
    public SearchByKeywordsResponse searchByKeywords(SearchByKeywordsRequest searchByKeywordsRequest) {
        try {

            Map<String, String> params = BeanUtils.describe(searchByKeywordsRequest);
            params.putAll(BeanUtils.describe(searchByKeywordsRequest.getParam()));
            params.entrySet().removeIf(entry -> StringUtils.isBlank(entry.getValue()));
            String responseStr = callApi(searchByKeywords, params);
            if (!"null".equals(responseStr) && StringUtils.isNotEmpty(responseStr)) {
                JSONObject responseObj = JSON.parseObject(responseStr);
                String resultStr = JSON.toJSONString(responseObj.get("result"));
                if (!"null".equals(resultStr) && StringUtils.isNotEmpty(resultStr)) {
                    JSONObject resultObj = JSON.parseObject(resultStr);
                    if (resultObj.getBoolean("success")) {
                        return JSON.parseObject(resultObj.toJSONString(), SearchByKeywordsResponse.class);
                    } else {
                        log.info("1688商品关键词搜索：{}",responseStr);
                    }
                }

            }else {
                log.info("关键词搜索结果：{}",responseStr);
            }
        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            e.printStackTrace();
        }
        return null;
    }

    //获取商品详情
    public QueryProductResponse queryProductById(QueryProductRequest queryProductRequest) {
        try {
            Map<String, String> params = BeanUtils.describe(queryProductRequest);
            params.entrySet().removeIf(entry -> StringUtils.isBlank(entry.getValue()));
            String result = callApi(queryProductById, params);
            if (!"null".equals(result) && StringUtils.isNotEmpty(result)) {
                JSONObject jsonObject = JSON.parseObject(result);
                if (jsonObject.getBoolean("success")) {
                    return JSON.parseObject(jsonObject.getString("productInfo"), QueryProductResponse.class);
                } else {
                    log.info("获取1688商品详情：{}",result);
                }
            }
        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            e.printStackTrace();
        }
        return null;
    }

    //图搜接口
    public QueryOpen1688ProductByImageUrlResponse queryOpen1688ProductByImageUrl(QueryOpen1688ProductByImageUrlRequest request) {
        try {
            log.info("1688商品图片搜索开始：{}", JSON.toJSONString(request));
            Map<String, String> params = BeanUtils.describe(request);
            params.putAll(BeanUtils.describe(request.getParam()));
            params.entrySet().removeIf(entry -> StringUtils.isBlank(entry.getValue()));
            String responseStr = callApi(getProductByImageUrl, params);
            if (!"null".equals(responseStr) && StringUtils.isNotEmpty(responseStr)) {
                JSONObject responseObj = JSON.parseObject(responseStr);
                String resultStr = JSON.toJSONString(responseObj.get("result"));
                if (!"null".equals(resultStr) && StringUtils.isNotEmpty(resultStr)) {
                    JSONObject resultObj = JSON.parseObject(resultStr);
                    if (resultObj.getBoolean("success")) {
                        log.info("1688商品图片搜索成功");
                        return JSON.parseObject(resultObj.toJSONString(), QueryOpen1688ProductByImageUrlResponse.class);
                    } else {
                        log.info("1688商品图片搜索：{}",responseStr);
                    }
                }

            }
        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            e.printStackTrace();
        }
        return null;
    }

    //创建1688订单
    public CreateOpen1688OrderResponse createOpen1688Order(CreateOpen1688OrderRequest createOpen1688OrderRequest) {
        CreateOpen1688OrderResponse createOpen1688OrderResponse = new CreateOpen1688OrderResponse();
        Map<String, String> params = null;
        try {
            params = BeanUtils.describe(createOpen1688OrderRequest);
            params.entrySet().removeIf(entry -> StringUtils.isBlank(entry.getValue()));
            String result = callApi(createOrder, params);
            if (!"null".equals(result) && StringUtils.isNotEmpty(result)) {
                JSONObject jsonObject = JSON.parseObject(result);
                if (jsonObject.getBoolean("success")) {
                    createOpen1688OrderResponse = JSON.parseObject(jsonObject.getString("result"), CreateOpen1688OrderResponse.class);
                } else {
                    log.info("创建1688订单：{}",result);
                }
            }
        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            e.printStackTrace();
        }
        return createOpen1688OrderResponse;
    }

    //获取订单详情
    public QueryOpen1688OrderDetailsResponse getOpen1688OrderDetail(QueryOpen1688OrderDetailsRequest queryOpen1688OrderDetailsRequest) {
        QueryOpen1688OrderDetailsResponse createOpen1688OrderResponse = new QueryOpen1688OrderDetailsResponse();
        Map<String, String> params = null;
        try {
            params = BeanUtils.describe(queryOpen1688OrderDetailsRequest);
            params.entrySet().removeIf(entry -> StringUtils.isBlank(entry.getValue()));
            String result = callApi(getOrderDetail, params);
            if (!"null".equals(result) && StringUtils.isNotEmpty(result)) {
                JSONObject jsonObject = JSON.parseObject(result);
                if (jsonObject.getBoolean("success")){
                    createOpen1688OrderResponse = JSON.parseObject(jsonObject.getString("result"), QueryOpen1688OrderDetailsResponse.class);
                }else {
                    log.info("获取1688订单详情：{}",result);
                }
            }
        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            e.printStackTrace();
        }
        return createOpen1688OrderResponse;
    }


    //取消订单
    public Boolean closeOpen1688Order(CloseOpen1688OrderRequest closeOpen1688OrderRequest) {
        Map<String, String> params = null;
        try {
            params = BeanUtils.describe(closeOpen1688OrderRequest);
            params.entrySet().removeIf(entry -> StringUtils.isBlank(entry.getValue()));
            String result = callApi(closeOrder, params);
            if (!"null".equals(result) && StringUtils.isNotEmpty(result)) {
                JSONObject jsonObject = JSON.parseObject(result);
                log.info("关闭1688订单：{}",result);
                return jsonObject.getBoolean("success");
            }
        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            e.printStackTrace();
        }
        return false;
    }

    //获取订单物流信息
    public GetOpen1688OrderLogisticsInfoResponse getOpen1688OrderLogisticsInfo(GetOpen1688OrderLogisticsInfoRequest request) {
        GetOpen1688OrderLogisticsInfoResponse getOpen1688OrderLogisticsInfoResponse = new GetOpen1688OrderLogisticsInfoResponse();
        Map<String, String> params = null;
        try {
            params = BeanUtils.describe(request);
            params.entrySet().removeIf(entry -> StringUtils.isBlank(entry.getValue()));
            String result = callApi(getLogisticsInfo, params);
            if (!"null".equals(result) && StringUtils.isNotEmpty(result)) {
                JSONObject jsonObject = JSON.parseObject(result);
                if (jsonObject.getBoolean("success")){
                    getOpen1688OrderLogisticsInfoResponse = JSON.parseObject(jsonObject.getString("result"), GetOpen1688OrderLogisticsInfoResponse.class);
                }else {
                    log.info("获取1688订单物流信息：{}",result);
                }
            }
        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            e.printStackTrace();
        }
        return getOpen1688OrderLogisticsInfoResponse;
    }

    //获取物流跟踪信息
    public GetOpen1688OrderLogisticsTraceInfoResponse getOpen1688OrderLogisticsTraceInfo(GetOpen1688OrderLogisticsTraceInfoRequest request) {
        GetOpen1688OrderLogisticsTraceInfoResponse getOpen1688OrderLogisticsTraceInfoResponse = new GetOpen1688OrderLogisticsTraceInfoResponse();
        Map<String, String> params = null;
        try {
            params = BeanUtils.describe(request);
            params.entrySet().removeIf(entry -> StringUtils.isBlank(entry.getValue()));
            String result = callApi(getLogisticsInfo, params);
            if (!"null".equals(result) && StringUtils.isNotEmpty(result)) {
                JSONObject jsonObject = JSON.parseObject(result);
                if (jsonObject.getBoolean("success")){
                    getOpen1688OrderLogisticsTraceInfoResponse = JSON.parseObject(jsonObject.getString("logisticsTrace"), GetOpen1688OrderLogisticsTraceInfoResponse.class);
                }else {
                    log.info("获取1688订单物流跟踪信息：{}",result);
                }
            }
        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            e.printStackTrace();
        }
        return getOpen1688OrderLogisticsTraceInfoResponse;
    }

    //获取精品货源商品列表
    public QueryOpen1688JXHYProductResponse getJXHYProduct(QueryOpen1688JXHYProductRequest request){
        QueryOpen1688JXHYProductResponse queryOpen1688JXHYProductResponse = new QueryOpen1688JXHYProductResponse();
        Map<String, String> params = null;
        try {
            params = BeanUtils.describe(request);
            params.entrySet().removeIf(entry -> StringUtils.isBlank(entry.getValue()));
            String responseStr = callApi(getJXHYProduct, params);
            if (!"null".equals(responseStr) && StringUtils.isNotEmpty(responseStr)) {
                JSONObject response = JSON.parseObject(responseStr);
                JSONObject result = response.getJSONObject("result");
                if (result.getBoolean("success")){
                    queryOpen1688JXHYProductResponse = JSON.parseObject(result.toJSONString(), QueryOpen1688JXHYProductResponse.class);
                }else {
                    log.info("获取1688精品货源商品列表：{}",responseStr);
                }
            }
        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            e.printStackTrace();
        }

        return queryOpen1688JXHYProductResponse;
    }

    //获取精品货源商品详情列表
    public QueryJXHYProductDetailResponse getJXHYProductDetail(List<Long> offerIds){
        QueryJXHYProductDetailResponse queryOpen1688JXHYProductDetailResponse = new QueryJXHYProductDetailResponse();
        Map<String, String> params = null;
        try {
            params = new HashMap<>();
            params.put("offerIds", JSON.toJSONString(offerIds));
            String responseStr = callApi(getJXHYProductDetail, params);
            if (!"null".equals(responseStr) && StringUtils.isNotEmpty(responseStr)) {
                JSONObject response = JSON.parseObject(responseStr);
                JSONObject result = response.getJSONObject("result");
                if (result.getBoolean("success")){
                    queryOpen1688JXHYProductDetailResponse = JSON.parseObject(result.toJSONString(), QueryJXHYProductDetailResponse.class);
                }else {
                    log.info("获取1688精品货源商品详情列表：{}",responseStr);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return queryOpen1688JXHYProductDetailResponse;
    }

}
