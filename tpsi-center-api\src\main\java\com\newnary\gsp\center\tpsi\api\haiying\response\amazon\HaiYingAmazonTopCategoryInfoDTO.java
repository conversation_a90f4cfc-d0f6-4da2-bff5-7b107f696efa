package com.newnary.gsp.center.tpsi.api.haiying.response.amazon;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingAmazonTopCategoryInfoDTO {

    /**
     * 商品1级类目排名
     */
    private Integer top_sellers_rank;

    /**
     * 商品类目排名
     */
    private Integer rank;

    /**
     * 类目id
     */
    private String cate_id;

    /**
     * 类目名称
     */
    private String cate_name;

    /**
     * 1级类目id
     */
    private String p_l1_id;

    /**
     * 1级类目名称
     */
    private String p_l1_name;

    /**
     * 商品排名时间
     */
    private Long last_upd_date;

    /**
     * 商品前7天排名上升均值
     */
    private BigDecimal average_ranking_in_seven_days;

    /**
     * 商品前30天天排名上升均值
     */
    private BigDecimal average_ranking_in_thirty_days;

    /**
     * 商品前90天排名中，前90-23天的最高排名是否大于前7天的均值排名
     * 0:否     1:是
     */
    private Boolean asin_recommended_mark;

    /**
     * 商品前90天的最早排名
     */
    private Integer first_rank_in_90_days;

    /**
     * 商品前90天的排名差
     */
    private Integer ranking_difference_in_90_days;

    /**
     * 商品前90天的最早排名时间
     */
    private Long first_rank_date_in_90_days;

    /**
     * 商品前7天排名变动率
     */
    private BigDecimal ranking_change_rate_in_the_first_7_days;

    /**
     * 商品前30天排名变动率
     */
    private BigDecimal ranking_change_rate_in_the_first_30_days;

    /**
     * 商品日销量
     */
    private Integer day_sales;

    /**
     * 商品月销量
     */
    private Integer month_sales;

    /**
     * 商品月销额
     */
    private BigDecimal month_payments;

}
