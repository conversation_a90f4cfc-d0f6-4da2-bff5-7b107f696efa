package com.newnary.gsp.center.tpsi.api.haiying.response.ebay;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingEbayProductSkuInfoDTO {

    /**
     * 商品变体id
     */
    private String variation_id;

    /**
     * 商品变体名称
     */
    private String variation_name;

    /**
     * 商品变体价格
     */
    private BigDecimal price;

    /**
     * 商品变体销量
     */
    private Integer sold;

    /**
     * 商品变体收藏
     */
    private Integer watchers;

    /**
     * 商品变体主图
     */
    private String main_image;

    /**
     * 商品变体最近抓取时间
     */
    private Long last_modi_time;

}
