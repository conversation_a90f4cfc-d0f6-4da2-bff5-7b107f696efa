package com.newnary.gsp.center.tpsi.infra.rpc;

import com.newnary.gsp.center.purchase.api.product.feign.PurchaseSpuFeignApi;
import com.newnary.gsp.center.purchase.api.product.request.SpuPageQueryCommand;
import com.newnary.gsp.center.purchase.api.product.response.SpuInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class PurchaseSpuRpc {

    @Resource
    private PurchaseSpuFeignApi purchaseSpuFeignAp;

    public List<SpuInfo> getSpuInfoList(SpuPageQueryCommand command){
        return purchaseSpuFeignAp.pageQuery(command).mustSuccessOrThrowOriginal().getItems();
    }
}
