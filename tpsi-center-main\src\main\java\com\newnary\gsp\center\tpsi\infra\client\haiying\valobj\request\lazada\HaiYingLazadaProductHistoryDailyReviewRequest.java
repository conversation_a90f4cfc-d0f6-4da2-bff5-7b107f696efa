package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.lazada;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: jack
 * @CreateTime: 2022-8-31
 */
@Data
public class HaiYingLazadaProductHistoryDailyReviewRequest {

    /**
     * 站点
     */
    @NotNull(message = "站点不能为空")
    private String station;

    /**
     * 商品id(string型)
     * (多个商品id用逗号分隔，最多100个商品id)
     */
    @NotNull(message = "商品id不能为空")
    private String item_ids;

    /**
     * 商品统计日期起始值(string型,格式:年-月-日)
     * (默认查询近一个月的数据)
     */
    private String stat_date_start;

    /**
     * 商品抓取时间结束值(string型,格式:年-月-日)
     * (默认查询近一个月的数据)
     */
    private String stat_date_end;

}
