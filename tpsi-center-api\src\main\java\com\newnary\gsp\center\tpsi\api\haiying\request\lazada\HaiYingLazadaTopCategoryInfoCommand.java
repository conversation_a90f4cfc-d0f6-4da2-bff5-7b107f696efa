package com.newnary.gsp.center.tpsi.api.haiying.request.lazada;

import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingStation;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingLazadaTopCategoryInfoCommand {

    /**
     * 站点
     */
    @NotNull(message = "站点不能为空")
    private HaiYingStation station;

    /**
     * 类目名
     * (存在特殊字符,转码UTF-8)
     */
    private String cname;

}
