package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp;

import lombok.Data;

import java.util.List;

@Data
public class EcCangERPGetProductBySkuRequest {

    /**
     * 产品SKU代码  支持多个["sku1","sku1"] 上限1000个
     */
    private List<String> productSku;

    /**
     * 获取组合产品明细 0
     * 否 1
     * 是
     */
    private Integer getProductCombination;

    /**
     *获取产品自定义分类 0
     * 否 1
     * 是
     */
    private Integer getProductCustomCategory;

}
