package com.newnary.gsp.center.tpsi.app.job;

import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.common.PageCondition;
import com.newnary.api.base.common.PageList;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.tms.api.smallparcel.request.TransportOrderPageQueryReq;
import com.newnary.gsp.center.tms.api.smallparcel.response.TransportOrderPageQueryResp;
import com.newnary.gsp.center.tpsi.api.jt.request.cn.TrackOrderCnJTCommand;
import com.newnary.gsp.center.tpsi.api.jt.response.cn.JTCnTrackResultResp;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.infra.rpc.RouteInfoRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.tms.TmsTransportOrderRpc;
import com.newnary.gsp.center.tpsi.service.JT.impl.JTCnLogisticsApiSvelmpl;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.job.core.handler.annotation.Job;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


@Component
@Slf4j
public class JTCnTMSJobManager {


    @Resource
    private JTCnLogisticsApiSvelmpl jtCnLogisticsApiSvelmpl;

    @Resource
    private RouteInfoRpc routeInfoRpc;

    @Resource
    private TmsTransportOrderRpc tmsTransportOrderRpc;

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Job("autoJT-CN-QueryTrack")
    public ReturnT<String> queryTrack(String param) {
        log.info("[拉取JT-CN运单轨迹任务] 定时任务开始, param={}", param);
        try {

            // 1. 获取第三方系统ID
            JSONObject paramObject = JSONObject.parseObject(param);
            String thirdPartySystemId = paramObject.getString("thirdPartySystemId");

            // 1.1 校验第三方系统ID
            if (StringUtils.isBlank(thirdPartySystemId)) {
                log.warn("[拉取JT-CN运单轨迹任务] thirdPartySystemId为空");
                return ReturnT.FAIL;
            }

            // 1.3 获取第三方系统参数
            ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);

            // 2. 分页查询未完成的物流单
            int pageNum = 1;
            int pageSize = 50;
            while (true) {
                List<TransportOrderPageQueryResp> transportOrders = fetchTransportOrders(pageNum, pageSize, thirdPartySystem);
                if (CollectionUtils.isEmpty(transportOrders)) {
                    break;
                }

                // 批量获取轨迹并处理
                processTracks(transportOrders);

                pageNum++;
            }
        } catch (ServiceException e) {
            log.error("[拉取JT-CN运单轨迹任务] 第三方系统配置错误, param={}, error={}", param, e.getMessage(), e);
            return ReturnT.FAIL;
        } catch (Exception e) {
            log.error("[拉取JT-CN运单轨迹任务] 定时任务异常, param={}", param, e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }


    private List<TransportOrderPageQueryResp> fetchTransportOrders(int pageNum, int pageSize, ThirdPartySystem thirdPartySystem) {
        TransportOrderPageQueryReq tranQueryCom = new TransportOrderPageQueryReq();
        tranQueryCom.setPageCondition(new PageCondition(pageNum, pageSize));
        tranQueryCom.setPushState("PUSHED");
        tranQueryCom.setStates(Arrays.asList("PRE_REPORTED", "IN_TRANSIT"));
        PageList<TransportOrderPageQueryResp> pageList = tmsTransportOrderRpc.pageQuery(tranQueryCom);
        return pageList.getItems();
    }

    private void processTracks(List<TransportOrderPageQueryResp> transportOrders) {
        List<String> trackList = transportOrders.stream()
                .map(TransportOrderPageQueryResp::getOrderId)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(trackList)) {
            TrackOrderCnJTCommand command = new TrackOrderCnJTCommand();
            command.setNos(trackList.stream().map(String::valueOf).collect(Collectors.joining(",")));
            command.setNoType(4);
            List<JTCnTrackResultResp> trackResultRespList = jtCnLogisticsApiSvelmpl.queryTrack(command);

            if (CollectionUtils.isNotEmpty(trackResultRespList)) {
                for (JTCnTrackResultResp responseItem : trackResultRespList) {
                    // 处理轨迹数据并写入
                    handleTrackData(responseItem);
                }
            }
        }
    }

    private void handleTrackData(JTCnTrackResultResp trackResult) {
        // 实现轨迹数据处理逻辑
        // 例如：更新数据库、发送通知等
    }

    public ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }

}
