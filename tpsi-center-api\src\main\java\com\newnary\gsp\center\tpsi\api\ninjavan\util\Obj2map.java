package com.newnary.gsp.center.tpsi.api.ninjavan.util;

import cn.hutool.core.bean.BeanUtil;
import com.newnary.gsp.center.tpsi.api.ninjavan.vo.CreateNinJavanOrderItem;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 2023-08-14
 *
 * <AUTHOR>
 */
public class Obj2map {

    private static final String COMPILE = "[A-Z]";
    private static Pattern linePattern = Pattern.compile("_(\\w)");
    private static Pattern humpPattern = Pattern.compile("[A-Z]");

    /**
     * 驼峰转下划线,首字母小写
     *
     * @param str
     * @return
     */
    public static String humpToLine(String str) {
        Matcher matcher = humpPattern.matcher(str);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, "_" + matcher.group(0).toLowerCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }


    /**
     * 下划线转驼峰,首字母大写
     *
     * @param str
     * @return
     */
    public static String lineToHump(String str) {
        Matcher matcher = linePattern.matcher(str);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, matcher.group(1).toUpperCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }


    /**
     * map 递归 key 驼峰转下划线
     *
     * @param object
     * @return
     */
    public static <T> Map getUnderToHump(T object) {
        if (object == null) {
            return null;
        }

        Map<String, Object> map = new HashMap<>();
        List<Map<String,Object>> list = new ArrayList<>();

        // 获取实体类所有属性，返回Field数组
        Field[] fields = object.getClass().getDeclaredFields();

        try {
            for (int i = 0; i < fields.length; i++) {
                // 属性名称
                String fieldName = fields[i].getName();

                if ("serialVersionUID".equalsIgnoreCase(fieldName)) {
                    continue;
                }

                // 转换驼峰形式属性名称成下划线风格，获取map的key 例：fieldName 》 field_name
                String transformFieldName = getTransformFieldName(fieldName);
                // map 的 value ，属性的值
                Object fieldValue = null;
                List<CreateNinJavanOrderItem> fieldItemValue = null;

                // 将属性的首字符大写，方便构造get，set方法
                String name = fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);

                // 获取属性的类型
                String type = fields[i].getGenericType().toString();
                Method m = object.getClass().getMethod("get" + name);

                switch (type) {
                    // 如果有需要,可以仿照下面继续进行扩充,再增加对其它类型的判断
                    case "class java.lang.String":
                    case "class java.lang.Boolean":
                    case "class java.util.Date":
                    case "class java.lang.Integer":
                    case "class java.lang.Long":
                    case "class java.lang.Float":
                        // 调用getter方法获取属性值
                        fieldValue = m.invoke(object);
                        break;
                    case "java.util.List<com.newnary.gsp.center.tpsi.api.ninjavan.vo.CreateNinJavanOrderItem>":
                        fieldItemValue = (List<CreateNinJavanOrderItem>)m.invoke(object);
                        for (CreateNinJavanOrderItem orderItem : fieldItemValue) {
                            Map<String, Object> stringObjectMap = BeanUtil.beanToMap(orderItem, true, false);
                            list.add(stringObjectMap);
                        }
                        fieldValue = list;
                        break;
                    default:
                        // 属性类型为bean,则递归
                        Object obj = m.invoke(object);
                        fieldValue = getUnderToHump(obj);
                }
                map.put(transformFieldName, fieldValue);
            }
        } catch (Exception e) {
            // 系统异常
            e.printStackTrace();
        }
        return map;
    }

    /**
     * 转换风格 驼峰转下划线
     *
     * @param fieldName 属性名称
     * @return
     */
    private static String getTransformFieldName(String fieldName) {
        Pattern humpPattern = Pattern.compile(COMPILE);
        Matcher matcher = humpPattern.matcher(fieldName);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, "_" + matcher.group(0).toLowerCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }



}
