package com.newnary.gsp.center.tpsi.app.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.common.PageCondition;
import com.newnary.api.base.common.PageList;
import com.newnary.api.base.common.Status;
import com.newnary.api.base.exception.BaseErrorInfo;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.common.utils.locale.LanguageLocaleType;
import com.newnary.distributed.tools.concurrent.template.DConcurrentTemplate;
import com.newnary.gsp.center.product.api.category.common.CategoryLevel;
import com.newnary.gsp.center.product.api.category.response.CategoryInfo;
import com.newnary.gsp.center.product.api.common.dto.MultimediaInfo;
import com.newnary.gsp.center.product.api.common.dto.MultimediaType;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierAdjustSupplyPriceReq;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierItemCreateCombineDetailInfo;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierUpdateStock4BatchReq;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierUpdateStockReq;
import com.newnary.gsp.center.product.api.product.request.*;
import com.newnary.gsp.center.product.api.product.response.SupplierSkuQueryLiteInfo;
import com.newnary.gsp.center.product.api.product.vo.SupplierSpuDescInfo;
import com.newnary.gsp.center.product.api.product.vo.SupplierSpuParamInfo;
import com.newnary.gsp.center.product.api.product.vo.SupplierSpuParamValueInfo;
import com.newnary.gsp.center.product.api.product.vo.SupplierSpuParamsInfo;
import com.newnary.gsp.center.tpsi.infra.client.ejingling.utils.DateUtils;
import com.newnary.gsp.center.tpsi.infra.client.vvic.params.VVICBaseParam;
import com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.request.VVICGetItemDetialReq;
import com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.request.VVICGetItemListReq;
import com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.response.VVICGetItemDetialResponse;
import com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.response.VVICGetItemListResponse;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingInfo;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingType;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.ThirdPartyMappingManager;
import com.newnary.gsp.center.tpsi.infra.rpc.CategoryRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.OpenSupplierProductRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.SupplierSkuRpc;
import com.newnary.gsp.center.tpsi.service.vvic.VVICApiSev;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.job.core.handler.annotation.Job;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
@Slf4j
public class VVICJobManager {

    @Resource
    private VVICApiSev vvicApiSevImpl;

    @Resource
    private OpenSupplierProductRpc openSupplierProductRpc;

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Resource
    private ThirdPartyMappingManager thirdPartyMappingManager;

    @Resource
    private CategoryRpc categoryRpc;

    @Resource
    private SupplierSkuRpc supplierSkuRpc;

/*    @Resource
    private SupplierSpuRpc supplierSkuRpc;*/

    /**
     * 将集合按指定数量分组
     *
     * @param list     数据集合
     * @param quantity 分组数量
     * @return 分组结果
     */
    public <T> List<List<T>> groupListByQuantity(List<T> list, int quantity) {

        if (list == null || list.size() == 0) {
            return null;
        }
        if (quantity <= 0) {
            throw new IllegalArgumentException("Wrong quantity.");
        }
        List<List<T>> wrapList = new ArrayList<>();
        int count = 0;
        while (count < list.size()) {
            wrapList.add(list.subList(count, Math.min((count + quantity), list.size())));
            count += quantity;
        }

        return wrapList;
    }

    private static final Map<Integer, BigDecimal> weightTypeMapping = new HashMap<Integer, BigDecimal>() {
        //weightType对照
        {
            put(1, new BigDecimal("0.2"));
            put(2, new BigDecimal("0.3"));
            put(3, new BigDecimal("0.4"));
            put(4, new BigDecimal("0.5"));
            put(5, new BigDecimal("0.6"));
            put(6, new BigDecimal("0.8"));
            put(7, new BigDecimal("0.9"));
            put(8, new BigDecimal("0.95"));
        }
    };

    @Job("autoCreateVVICProduct")
    public ReturnT<String> createVVICProduct(String param) {

        DConcurrentTemplate.tryLockMode(
                "VVIC:FETCH_PRODUCT",
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    log.info("vvic-获取搜款网商品同步到供应商后台 定时任务开始, param={}", param);
                    JSONObject paramObject = JSON.parseObject(param);
                    String thirdPartySystemId = paramObject.getString("thirdPartySystemId");
                    String time = paramObject.getString("time");
                    long timeStamp = Long.parseLong(time) * 60000;
                    long endTimeStamp = new Date().getTime();
                    long starTimeStamp = endTimeStamp - timeStamp;
                    //根据thirdPartySystemId获取第三方系统参数
                    ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
                    VVICBaseParam vvicBaseParam = JSON.parseObject(thirdPartySystem.getParams(), VVICBaseParam.class);
                    if (starTimeStamp >= endTimeStamp) {
                        throw new ServiceException(Status.error(new BaseErrorInfo("500", "时间输入错误"), "starTimeStamp >= endTimeStamp", "createVVICProduct"));
                    }
                    long space = 1800000L;
                    //查询商品销售单元
                    VVICGetItemListReq vvicGetItemListReq = new VVICGetItemListReq();
                    vvicGetItemListReq.setLang(vvicBaseParam.getLang());
                    vvicGetItemListReq.setCity_market_code(vvicBaseParam.getMarketCode());
                    do {
                        vvicGetItemListReq.setPage(1);
                        //判断时间
                        if (endTimeStamp - starTimeStamp < space) {
                            vvicGetItemListReq.setUp_time_start(DateUtils.timeStamp2DateString("yyyy-MM-dd HH:mm:ss", starTimeStamp));
                            vvicGetItemListReq.setUp_time_end(DateUtils.timeStamp2DateString("yyyy-MM-dd HH:mm:ss", endTimeStamp));
                            starTimeStamp += space;
                        } else {
                            vvicGetItemListReq.setUp_time_start(DateUtils.timeStamp2DateString("yyyy-MM-dd HH:mm:ss", starTimeStamp));
                            vvicGetItemListReq.setUp_time_end(DateUtils.timeStamp2DateString("yyyy-MM-dd HH:mm:ss", starTimeStamp += space));
                        }
                        VVICGetItemListResponse items = vvicApiSevImpl.getItemList(thirdPartySystemId, vvicGetItemListReq);

                        if (ObjectUtils.isNotEmpty(items) && CollectionUtils.isNotEmpty(items.getItem_list())) {
                            //页数大于1就先全部查出来
                            if (items.getTotal_page() > 1) {
                                for (int i = 2; i <= items.getTotal_page(); i++) {
                                    vvicGetItemListReq.setPage(i);
                                    items.getItem_list().addAll(vvicApiSevImpl.getItemList(thirdPartySystemId, vvicGetItemListReq).getItem_list());
                                }
                            }
                            try {
                                //给商品item分组，20个一组，因为查询明细时一次只能查询20个
                                List<List<VVICGetItemListResponse.Item>> lists = groupListByQuantity(items.getItem_list(), 20);
                                lists.forEach(list -> {
                                    //拼接item_vid
                                    StringBuilder stringBuilder = new StringBuilder();
                                    list.forEach(item -> stringBuilder.append(item.getItem_vid()).append(","));
                                    String vids = stringBuilder.toString();
                                    //查询商品明细
                                    VVICGetItemDetialReq vvicGetItemDetialReq = new VVICGetItemDetialReq();
                                    vvicGetItemDetialReq.setLang(vvicBaseParam.getLang());
                                    vvicGetItemDetialReq.setItem_vid(vids);
                                    VVICGetItemDetialResponse itemDetial = vvicApiSevImpl.getItemDetial(thirdPartySystemId, vvicGetItemDetialReq);
                                    //构建商品参数，创建供应商商品
                                    if (ObjectUtils.isNotEmpty(itemDetial) && CollectionUtils.isNotEmpty(itemDetial.getItem_list())) {
                                        List<String> categoryList = itemDetial.getItem_list().stream().map(item -> item.getCategory_name_one().concat("/").concat(item.getCategory_name_sub().concat("/").concat(item.getCategory_name_two()))).distinct().collect(Collectors.toList());
                                        Map<String, ThirdPartyMappingInfo> categoryMapping = thirdPartyMappingManager.batchGetIdMappingInfoByTargetCategoryPath("GSP", "VVIC", categoryList, ThirdPartyMappingType.CATEGORY);
                                        if (null == categoryMapping || categoryMapping.size() == 0) {
                                            log.error("类目映射map为空，请求参数：{}  categoryMapping：{}", JSON.toJSONString(categoryList), categoryMapping);
                                            return;
                                        }
                                        itemDetial.getItem_list().forEach(item -> {
                                            ThirdPartyMappingInfo newestInfoByTarget = thirdPartyMappingManager.getNewestInfoByTarget("VVIC", "GSP", item.getItem_vid(), ThirdPartyMappingType.PRODUCT_ID);
                                            if (ObjectUtils.isEmpty(newestInfoByTarget)) {
                                                try {
                                                    String supplierProductSpuId = openSupplierProductRpc.createSpu(buildCreateSpuReq(thirdPartySystem, categoryMapping, item));
                                                    //创建完，初始化商品库存
                                                    updateStock(thirdPartySystem, item);
                                                    //更新完库存更新价格
                                                    updatePrice(thirdPartySystem, item);
                                                    //存一个商品映射信息
                                                    thirdPartyMappingManager.insertOrUpdate("GSP", "VVIC", supplierProductSpuId, item.getItem_vid(), ThirdPartyMappingType.PRODUCT_ID.name(), item);
                                                } catch (Exception e) {
                                                    log.error("autoCreateVVICProduct：{}", e.getMessage());
                                                }
                                            }
                                        });
                                    }
                                });
                            } catch (Exception e) {
                                log.error("autoCreateVVICProduct: {}", e.getMessage());
                            }
                        }
                    } while ((starTimeStamp < endTimeStamp));
                }
        );
        return ReturnT.SUCCESS;
    }

    //构建创建供应商SPU请求参数
    public SupplierSpuCreateV2Command buildCreateSpuReq(ThirdPartySystem thirdPartySystem, Map<String, ThirdPartyMappingInfo> categoryMapping, VVICGetItemDetialResponse.Item item) {
        SupplierSpuCreateV2Command supplierSpuCreateV2Command = new SupplierSpuCreateV2Command();
        VVICBaseParam param = JSON.parseObject(thirdPartySystem.getParams(), VVICBaseParam.class);
        //供应商id
        supplierSpuCreateV2Command.setSupplierId(param.getSupplierId());
        //spu描述信息
        supplierSpuCreateV2Command.setDescInfos(buildSpuDescInfo(item));
        //默认语言
        if ("cn".equals(param.getLang())) {
            supplierSpuCreateV2Command.setDefaultLocale(LanguageLocaleType.zh_CN);
        } else {
            supplierSpuCreateV2Command.setDefaultLocale(LanguageLocaleType.en_US);
        }

        //supplierSpuCreateV2Command.setDefaultLocale(LanguageLocaleType.zh_CN);
        //商家自定义spu编码
        supplierSpuCreateV2Command.setCustomCode(item.getItem_vid());
        //品牌
        supplierSpuCreateV2Command.setBrandId(param.getBrandId());
        //设置类目id和类目等级
        setCategoryInfo(supplierSpuCreateV2Command,
                categoryMapping,
                item);
        //商品主图列表
        MultimediaInfo multimediaInfo = new MultimediaInfo();
        multimediaInfo.setFileUrl("http:".concat(item.getItem_view_image()));
        multimediaInfo.setType(MultimediaType.IMAGE);
        List<MultimediaInfo> multimediaInfos = new ArrayList<>();
        multimediaInfos.add(multimediaInfo);
        supplierSpuCreateV2Command.setMainImages(multimediaInfos);
        //明细图片
        String[] detailImages = item.getList_grid_image().split(",");
        //List<MultimediaInfo> detailImageList = new ArrayList<>();
        for (String image : detailImages) {
            if (!(image.contains("https") || image.contains("http"))) {
                MultimediaInfo detailImage = new MultimediaInfo();
                detailImage.setFileUrl("http:".concat(image));
                detailImage.setType(MultimediaType.IMAGE);
                multimediaInfos.add(detailImage);
            }
        }
        //supplierSpuCreateV2Command.setDetailImages(detailImageList);
        //视频
        MultimediaInfo videosInfo = new MultimediaInfo();
        videosInfo.setFileUrl(item.getVideo_url());
        videosInfo.setType(MultimediaType.VIDEO);
        List<MultimediaInfo> videos = new ArrayList<>();
        videos.add(videosInfo);
        supplierSpuCreateV2Command.setVideos(videos);
        //skuList
        supplierSpuCreateV2Command.setSkuList(buildSkuList(item));

        //产品参数信息
        buildParamsInfo(supplierSpuCreateV2Command, item);

        //自动审核通过
        supplierSpuCreateV2Command.setIsAutoAuditPass(true);

        //操作人
        supplierSpuCreateV2Command.setOperator("tpsi");

        return supplierSpuCreateV2Command;
    }

    //构建spu规格信息
    private List<SupplierSpuDescInfo> buildSpuDescInfo(VVICGetItemDetialResponse.Item item) {
        SupplierSpuDescInfo supplierSpuDescInfo = new SupplierSpuDescInfo();
        //语言
        supplierSpuDescInfo.setLocale(LanguageLocaleType.en_US);
        //标题
        supplierSpuDescInfo.setTitle(item.getItem_title());
        //描述
        supplierSpuDescInfo.setTextDesc(item.getDesc());
        //添加自定义类目名称
        String catetoryPath = item.getCategory_name_one().concat("/".concat(item.getCategory_name_sub()).concat("/").concat(item.getCategory_name_two()));
        if (StringUtils.isNotBlank(catetoryPath)) {
            supplierSpuDescInfo.setCustomCategoryName(catetoryPath);
        }
        //翻译来源类型
        supplierSpuDescInfo.setTransSourceType("英文");
        //翻译提供者
        supplierSpuDescInfo.setTransProvider("vvic");
        //翻译基准语言
        supplierSpuDescInfo.setTransBaseLocate(LanguageLocaleType.en_US);
        List<SupplierSpuDescInfo> descInfos = new ArrayList<>();
        descInfos.add(supplierSpuDescInfo);
        return descInfos;
    }

    //构建类目信息
    private void setCategoryInfo(SupplierSpuCreateV2Command supplierSpuCreateV2Command, Map<String, ThirdPartyMappingInfo> categoryMapping, VVICGetItemDetialResponse.Item item) {
        String path = item.getCategory_name_one().concat("/".concat(item.getCategory_name_sub()).concat("/").concat(item.getCategory_name_two()));
        try {
            ThirdPartyMappingInfo thirdPartyMappingInfo = categoryMapping.get(path);
            CategoryInfo categoryInfo = categoryRpc.getCategoryById(thirdPartyMappingInfo.getSourceId());
            if (ObjectUtils.isNotEmpty(categoryInfo)) {
                supplierSpuCreateV2Command.setCategoryId(categoryInfo.getCategoryId());
                supplierSpuCreateV2Command.setMgmtCategoryLevel(CategoryLevel.getByValue(categoryInfo.getCategoryLevel()));
            }
        } catch (Exception e) {
            log.error("VVIC商品设置类目失败，item_vid: {} categorlyMapping: {} targetId: {}", item.getItem_vid(), JSON.toJSONString(categoryMapping), item.getCategory_name_one().concat("/".concat(item.getCategory_name_sub().concat("/").concat(item.getCategory_name_two()))));
        }
    }

    //构建sku列表
    private List<SupplierSkuCreateInfo> buildSkuList(VVICGetItemDetialResponse.Item item) {
        List<SupplierSkuCreateInfo> skuList = new ArrayList<>();
        item.getSkuList().forEach(sku -> {
            SupplierSkuCreateInfo supplierSkuCreateInfo = new SupplierSkuCreateInfo();
            supplierSkuCreateInfo.setCustomCode(sku.getSku_vid());
            //设置主规格
            SupplierSkuMainSpecInfo supplierSkuMainSpecInfo = new SupplierSkuMainSpecInfo();
            String colorImg = sku.getColor_img();
            if (StringUtils.isNotBlank(colorImg)) {
                MultimediaInfo multimediaInfo = new MultimediaInfo();
                multimediaInfo.setType(MultimediaType.IMAGE);
                if (!(colorImg.contains("http") || colorImg.contains("https"))) {
                    colorImg = "http:".concat(colorImg);
                }
                multimediaInfo.setFileUrl(colorImg);
                supplierSkuMainSpecInfo.setImage(multimediaInfo);
            }

            supplierSkuCreateInfo.setMainSpecInfo(supplierSkuMainSpecInfo);
            //设置规格参数
            List<SupplierSkuSpecInfo> specs = new ArrayList<>();
            //颜色
            SupplierSkuSpecInfo colorSpec = new SupplierSkuSpecInfo();
            colorSpec.setSpecName("color");
            colorSpec.setSpecValue(sku.getColor());
            supplierSkuMainSpecInfo.setSpec(colorSpec);
            specs.add(colorSpec);
            //尺寸
            SupplierSkuSpecInfo sizeSpec = new SupplierSkuSpecInfo();
            sizeSpec.setSpecName("size");
            sizeSpec.setSpecValue(sku.getSize());
            specs.add(sizeSpec);
            supplierSkuCreateInfo.setSpecs(specs);

            //净重
            supplierSkuCreateInfo.setNetWeight(weightTypeMapping.get(item.getWeight_type()));

            //毛重
            supplierSkuCreateInfo.setGrossWeight(weightTypeMapping.get(item.getWeight_type()));

            //最小起订量
            supplierSkuCreateInfo.setMoq(1);

            //售价
            supplierSkuCreateInfo.setLowestRetailPrice(new BigDecimal(String.valueOf(sku.getPrice())));
            skuList.add(supplierSkuCreateInfo);
        });
        return skuList;

    }

    //构建产品参数信息
    private void buildParamsInfo(SupplierSpuCreateV2Command supplierSpuCreateV2Command, VVICGetItemDetialResponse.Item item) {
        //设置供应商自定义规格信息
        SupplierSpuParamsInfo supplierSpuParamsInfo = new SupplierSpuParamsInfo();
        String attr_str = item.getAttr_str();
        if (StringUtils.isNotBlank(attr_str)) {
            String[] attrs = attr_str.split(",");
            List<SupplierSpuParamInfo> customParams = new ArrayList<>();
            for (String attr : attrs) {
                String[] split = attr.split(":");
                List<SupplierSpuParamValueInfo> valueInfoList = new ArrayList<>();
                SupplierSpuParamInfo supplierSpuParamInfo = new SupplierSpuParamInfo();
                if (split.length > 1 && StringUtils.isNotBlank(split[1])) {
                    supplierSpuParamInfo.setParamName(split[0].trim());
                    String trim = split[1].trim();
                    if (trim.contains("|")) {
                        String[] values = trim.split("\\|");
                        for (String value : values) {
                            if (StringUtils.isNotBlank(value)) {
                                SupplierSpuParamValueInfo supplierSpuParamValueInfo = new SupplierSpuParamValueInfo();
                                supplierSpuParamValueInfo.setParamValue(value);
                                valueInfoList.add(supplierSpuParamValueInfo);
                            }
                        }
                    } else {
                        SupplierSpuParamValueInfo supplierSpuParamValueInfo = new SupplierSpuParamValueInfo();
                        supplierSpuParamValueInfo.setParamValue(split[1]);
                        valueInfoList.add(supplierSpuParamValueInfo);
                    }
                }
                if (CollectionUtils.isNotEmpty(valueInfoList)) {
                    supplierSpuParamInfo.setValues(valueInfoList);
                    customParams.add(supplierSpuParamInfo);
                }
            }
            supplierSpuParamsInfo.setCustomParams(customParams);
            supplierSpuCreateV2Command.setParamsInfo(supplierSpuParamsInfo);
        }
    }

    @Job("autoUpdateVVICProductStock")
    public ReturnT<String> updateVVICProductStock(String param) {
        DConcurrentTemplate.tryLockMode(
                "VVIC:FETCH_PRODUCT_STOCK",
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    log.info("vvic-获取搜款网商品库存同步到供应商后台 定时任务开始, param={}", param);
                    JSONObject paramObject = JSON.parseObject(param);
                    String thirdPartySystemId = paramObject.getString("thirdPartySystemId");

                    //根据thirdPartySystemId获取第三方系统参数
                    ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
                    VVICBaseParam vvicBaseParam = JSON.parseObject(thirdPartySystem.getParams(), VVICBaseParam.class);
                    String supplierId = vvicBaseParam.getSupplierId();
                    //使用供应商id查询商品
                    SupplierSkuPageQueryCommand supplierSkuPageQueryCommand = new SupplierSkuPageQueryCommand();
                    supplierSkuPageQueryCommand.setSupplierId(supplierId);
                    List<SupplierSkuQueryLiteInfo> skuList;
                    int page = 0;
                    do {
                        PageCondition pageCondition = new PageCondition(page += 1, 50);
                        supplierSkuPageQueryCommand.setPageCondition(pageCondition);
                        skuList = supplierSkuRpc.pageQuery(supplierSkuPageQueryCommand);
                        if (null == skuList || skuList.size() == 0) {
                            continue;
                        }
                        //批量更新商品库存
                        batchUpdateStock(thirdPartySystem, skuList, Integer.parseInt(paramObject.get("stock").toString()));
                    } while (null != skuList && skuList.size() > 0);
                }
        );

        return ReturnT.SUCCESS;
    }

    @Job("autoUpdateVVICProductPrice")
    public ReturnT<String> updateVVICProductPrice(String param) {
        DConcurrentTemplate.tryLockMode(
                "VVIC:FETCH_PRODUCT_PRICE",
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    log.info("vvic-获取搜款网商品价格同步到供应商后台 定时任务开始, param={}", param);
                    JSONObject paramObject = JSON.parseObject(param);
                    String thirdPartySystemId = paramObject.getString("thirdPartySystemId");

                    //根据thirdPartySystemId获取第三方系统参数
                    ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
                    VVICBaseParam vvicBaseParam = JSON.parseObject(thirdPartySystem.getParams(), VVICBaseParam.class);
                    String supplierId = vvicBaseParam.getSupplierId();

                    //根据供应商id查询供应商商品
                    SupplierSkuSearchDetailsCommand supplierSkuSearchDetailsCommand = new SupplierSkuSearchDetailsCommand();
                    supplierSkuSearchDetailsCommand.setSupplierId(supplierId);
                    //通过映射表查询供应商商品
                    PageList<ThirdPartyMappingInfo> pageResult;
                    int page = 0;
                    do {
                        pageResult = thirdPartyMappingManager.getNewestInfoByTargetBizId("VVIC", "GSP", ThirdPartyMappingType.PRODUCT_ID, new PageCondition(page += 1, 200));
                        if (null != pageResult && pageResult.getItems() != null) {
                            updatePrice(thirdPartySystem, pageResult);
                        }
                    } while (null != pageResult && CollectionUtils.isNotEmpty(pageResult.getItems()));
                }
        );

        return ReturnT.SUCCESS;
    }

    //更新库存1
    public void updateStock(ThirdPartySystem thirdPartySystem, VVICGetItemDetialResponse.Item item) {
        OpenSupplierUpdateStockReq req = new OpenSupplierUpdateStockReq();
        req.setBizSerialNumber(UUID.randomUUID().toString().replace("-", "").substring(0, 18));
        VVICBaseParam vvicBaseParam = JSON.parseObject(thirdPartySystem.getParams(), VVICBaseParam.class);
        String supplierId = vvicBaseParam.getSupplierId();
        req.setSupplierId(supplierId);
        req.setCustomWarehouseCode(vvicBaseParam.getWarehouse());
        req.setStockNum(10);
        item.getSkuList().forEach(sku -> {
            req.setCustomSkuCode(sku.getSku_vid());
            try {
                openSupplierProductRpc.updateStock(req);
            } catch (ServiceException e) {
                log.error("更新库存失败：supplier: {}  supplierSkuId: {} message: {}", supplierId, sku.getSku_vid(), e.getMessage());
            }
        });
    }

    //更新库存2
    private void updateStock(ThirdPartySystem thirdPartySystem, String skuId, int stock) {
        OpenSupplierUpdateStockReq req = new OpenSupplierUpdateStockReq();
        req.setBizSerialNumber(UUID.randomUUID().toString().replace("-", "").substring(0, 18));
        VVICBaseParam vvicBaseParam = JSON.parseObject(thirdPartySystem.getParams(), VVICBaseParam.class);
        String supplierId = vvicBaseParam.getSupplierId();
        req.setSupplierId(supplierId);
        req.setCustomWarehouseCode(vvicBaseParam.getWarehouse());
        req.setStockNum(stock);
        req.setCustomSkuCode(skuId);
        try {
            openSupplierProductRpc.updateStock(req);
        } catch (ServiceException e) {
            log.error("更新库存失败：supplier: {}  supplierSkuId: {} message: {}", supplierId, skuId, e.getMessage());
        }
    }

    //更新库存2
    private void batchUpdateStock(ThirdPartySystem thirdPartySystem, List<SupplierSkuQueryLiteInfo> list, int stock) {
        OpenSupplierUpdateStock4BatchReq openSupplierUpdateStock4BatchReq = new OpenSupplierUpdateStock4BatchReq();
        VVICBaseParam vvicBaseParam = JSON.parseObject(thirdPartySystem.getParams(), VVICBaseParam.class);
        String supplierId = vvicBaseParam.getSupplierId();
        List<OpenSupplierUpdateStockReq> reqList = new ArrayList<>();
        list.forEach(skuQueryLiteInfo -> {
            OpenSupplierUpdateStockReq req = new OpenSupplierUpdateStockReq();
            req.setBizSerialNumber(UUID.randomUUID().toString().replace("-", "").substring(0, 18));
            req.setSupplierId(supplierId);
            req.setCustomWarehouseCode(vvicBaseParam.getWarehouse());
            req.setStockNum(stock);
            req.setCustomSkuCode(skuQueryLiteInfo.getCustomCode());
            reqList.add(req);
        });
        openSupplierUpdateStock4BatchReq.setReqs(reqList);
        try {
            openSupplierProductRpc.updateStockBatch(openSupplierUpdateStock4BatchReq);
        } catch (ServiceException e) {
            log.error("更新库存失败：supplier: {}  supplierSkuId: {} message: {}", supplierId, reqList.stream().map(OpenSupplierUpdateStockReq::getCustomSkuCode).collect(Collectors.toList()), e.getMessage());
        }
    }

    //更新商品价格1
    public void updatePrice(ThirdPartySystem thirdPartySystem, PageList<ThirdPartyMappingInfo> pageResult) {
        List<List<ThirdPartyMappingInfo>> lists = groupListByQuantity(pageResult.getItems(), 20);
        VVICBaseParam vvicBaseParam = JSON.parseObject(thirdPartySystem.getParams(), VVICBaseParam.class);
        //根据商品skuid查询衫海精商品明细
        lists.forEach(list -> {
            StringBuilder stringBuilder = new StringBuilder();
            list.forEach(item -> stringBuilder.append(item.getTargetId()).append(","));
            String vids = stringBuilder.toString();
            VVICGetItemDetialReq req = new VVICGetItemDetialReq();
            req.setLang(vvicBaseParam.getLang());
            req.setItem_vid(vids);
            VVICGetItemDetialResponse itemDetial = vvicApiSevImpl.getItemDetial(thirdPartySystem.getBizId(), req);
            if (null != itemDetial && CollectionUtils.isNotEmpty(itemDetial.getItem_list())) {
                //根据商品明细库存更新供应商sku库存
                itemDetial.getItem_list().forEach(item -> {
                    updatePrice(thirdPartySystem, item);
                    //updatePrice(thirdPartySystem,item);
                });
            }
        });
    }

    //更新商品价格2
    public void updatePrice(ThirdPartySystem thirdPartySystem, VVICGetItemDetialResponse.Item item) {
        OpenSupplierAdjustSupplyPriceReq req = new OpenSupplierAdjustSupplyPriceReq();
        VVICBaseParam vvicBaseParam = JSON.parseObject(thirdPartySystem.getParams(), VVICBaseParam.class);
        String supplierId = vvicBaseParam.getSupplierId();
        req.setSupplierId(supplierId);
        req.setCountry("CN");
        req.setSupplyPriceCurrency("CNY");

        item.getSkuList().forEach(sku -> {
            req.setCustomSkuCode(sku.getSku_vid());
            req.setSupplyPrice(sku.getPrice());
            //供货明细表
            List<OpenSupplierItemCreateCombineDetailInfo> detailInfos = new ArrayList<>();
            OpenSupplierItemCreateCombineDetailInfo info = new OpenSupplierItemCreateCombineDetailInfo();
            info.setCombineNum(1);
            info.setSupplyPriceCurrency("CNY");
            detailInfos.add(info);
            info.setSupplyPrice(sku.getPrice());
            req.setCombineDetails(detailInfos);
            try {
                openSupplierProductRpc.adjustSupplyPrice(req);
            } catch (ServiceException e) {
                log.error("更新商品价格：supplier: {}  supplierSkuId: {} message: {}", supplierId, sku.getSku_vid(), e.getMessage());
            }
        });
    }

    //更新商品信息
    @Job("autoUpdateVVICProductInfo")
    public ReturnT<String> updateVVICProductInfo(String param) {
        return DConcurrentTemplate.tryLockMode(
                "VVIC:UPDATE_PRODUCTINFO",
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    log.info("vvic-根据商品映射数据更新商品信息 定时任务开始, param={}", param);
                    JSONObject paramObject = JSON.parseObject(param);
                    String thirdPartySystemId = paramObject.getString("thirdPartySystemId");
                    long gmtCreateLess = DateUtils.DateString2TimeStamp(paramObject.getString("gmtCreateLess"));
                    long gmtCreateGreater = DateUtils.DateString2TimeStamp(paramObject.getString("gmtCreateGreater"));

                    //根据thirdPartySystemId获取第三方系统参数
                    ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
                    VVICBaseParam vvicBaseParam = JSON.parseObject(thirdPartySystem.getParams(), VVICBaseParam.class);
                    String supplierId = vvicBaseParam.getSupplierId();

                    //根据供应商id查询供应商商品
                    SupplierSkuSearchDetailsCommand supplierSkuSearchDetailsCommand = new SupplierSkuSearchDetailsCommand();
                    supplierSkuSearchDetailsCommand.setSupplierId(supplierId);
                    //使用供应商id查询商品
                    //List<SupplierSkuInfo> skuInfos = supplierSkuRpc.getSkuInfo(supplierSkuSearchDetailsCommand);
                    PageList<ThirdPartyMappingInfo> pageResult;
                    int page = 0;
                    do {
                        pageResult = thirdPartyMappingManager.getNewestInfoByTargetBizIdAndGmtCreate("VVIC", "GSP", ThirdPartyMappingType.PRODUCT_ID, new PageCondition(page += 1, 200), gmtCreateLess, gmtCreateGreater);
                        if (null != pageResult && CollectionUtils.isNotEmpty(pageResult.getItems())) {
                            //List<String> vidList = pageResult.getItems().stream().map(ThirdPartyMappingInfo::getSourceId).collect(Collectors.toList());
                            List<VVICGetItemDetialResponse.Item> collect = pageResult.getItems().stream().map(thirdPartyMappingInfoJSON->{ return JSON.parseObject(thirdPartyMappingInfoJSON.getExtendBizInfo(), VVICGetItemDetialResponse.Item.class);}).collect(Collectors.toList());
                            Map<String, String> idMap = pageResult.getItems().stream().collect(Collectors.toMap(ThirdPartyMappingInfo::getTargetId, ThirdPartyMappingInfo::getSourceId));
                            List<String> categoryList = collect.stream().map(item -> item.getCategory_name_one().concat("/").concat(item.getCategory_name_sub().concat("/").concat(item.getCategory_name_two()))).distinct().collect(Collectors.toList());
                            Map<String, ThirdPartyMappingInfo> categoryMapping = thirdPartyMappingManager.batchGetIdMappingInfoByTargetCategoryPath("GSP", "VVIC", categoryList, ThirdPartyMappingType.CATEGORY);
                            collect.forEach(item -> {
                                String desc = item.getDesc();
                                if (StringUtils.isNotBlank(desc)) {
                                    if (!(desc.contains("https") || desc.contains("http"))) {
                                        if (desc.contains("<img") || desc.contains("<div") || desc.contains("<p>")) {
                                            String replace = desc.replace("src=\"", "src=\"http:");
                                            item.setDesc(replace);
                                        }
                                    } else if (desc.contains("链接") || desc.contains("提取码") || desc.contains("网盘")) {
                                        Document parse = Jsoup.parse(desc);//html为内容
                                        Elements imgs = parse.getElementsByTag("img");
                                        if (CollectionUtils.isNotEmpty(imgs)) {
                                            StringBuilder stringBuilder = new StringBuilder();
                                            imgs.forEach(img -> {
                                                String src = img.attr("src");
                                                Pattern pattern = Pattern.compile("\\w+.(jpg|png|jpeg|bmp|webp|JPG|PNG|JPEG|BMP|WEBP)$");
                                                Matcher matcher = pattern.matcher(src);
                                                if (matcher.find()) {
                                                    String outerHtml = img.outerHtml();
                                                    if (!(outerHtml.contains("http") || outerHtml.contains("https"))) {
                                                        stringBuilder.append(outerHtml.replace("src=\"", "src=\"http:"));
                                                    } else {
                                                        stringBuilder.append(outerHtml);
                                                    }
                                                }
                                            });
                                            item.setDesc(stringBuilder.toString());
                                        }
                                    } else {
                                        if (!(desc.contains("src=\"https") || desc.contains("src=\"http"))) {
                                            desc = desc.replace("src=\"", "src=\"http:");
                                        }
                                        item.setDesc(desc);
                                    }
                                }
                                SupplierSpuCreateV2Command spuCreateV2Command = buildCreateSpuReq(thirdPartySystem, categoryMapping, item);
                                SupplierSpuUpdateV2Command supplierSpuUpdateV2Command = buildSupplierSpuUpdateV2Command(idMap.get(item.getItem_vid()), spuCreateV2Command);
                                //TODO 等商品更新接口写好进行商品更新 时间点为	2023-04-26 17:49:15开始
                            });
                        }
                    } while (null != pageResult && CollectionUtils.isNotEmpty(pageResult.getItems()));

                    return ReturnT.SUCCESS;
                });
    }

    private SupplierSpuUpdateV2Command buildSupplierSpuUpdateV2Command(String supplierSpuId,SupplierSpuCreateV2Command spuCreateV2Command) {
        SupplierSpuUpdateV2Command supplierSpuUpdateV2Command = new SupplierSpuUpdateV2Command();
        supplierSpuUpdateV2Command.setSupplierSpuId(supplierSpuId);
        supplierSpuUpdateV2Command.setSupplierId(spuCreateV2Command.getSupplierId());
        supplierSpuUpdateV2Command.setDescInfos(spuCreateV2Command.getDescInfos());
        supplierSpuUpdateV2Command.setDefaultLocale(spuCreateV2Command.getDefaultLocale());
        supplierSpuUpdateV2Command.setCustomCode(spuCreateV2Command.getCustomCode());
        supplierSpuUpdateV2Command.setCustomBrandId(spuCreateV2Command.getCustomBrandId());
        supplierSpuUpdateV2Command.setCategoryId(spuCreateV2Command.getCategoryId());
        supplierSpuUpdateV2Command.setCustomCategoryId(spuCreateV2Command.getCustomCategoryId());
        supplierSpuUpdateV2Command.setMgmtCategoryLevel(spuCreateV2Command.getMgmtCategoryLevel());
        supplierSpuUpdateV2Command.setMgmtCategoryId(spuCreateV2Command.getMgmtCategoryId());
        supplierSpuUpdateV2Command.setMainImages(spuCreateV2Command.getMainImages());
        supplierSpuUpdateV2Command.setDetailImages(spuCreateV2Command.getDetailImages());
        supplierSpuUpdateV2Command.setVideos(spuCreateV2Command.getVideos());
        supplierSpuUpdateV2Command.setCountryOfOriginCode(spuCreateV2Command.getCountryOfOriginCode());
        supplierSpuUpdateV2Command.setLogisticsAttrInfo(spuCreateV2Command.getLogisticsAttrInfo());
        supplierSpuUpdateV2Command.setSkuList(spuCreateV2Command.getSkuList());
        supplierSpuUpdateV2Command.setParamsInfo(spuCreateV2Command.getParamsInfo());
        supplierSpuUpdateV2Command.setIsAutoAuditPass(spuCreateV2Command.getIsAutoAuditPass());
        supplierSpuUpdateV2Command.setIsCheckAttribute(spuCreateV2Command.getIsCheckAttribute());
        supplierSpuUpdateV2Command.setOperator(spuCreateV2Command.getOperator());

        return supplierSpuUpdateV2Command;
    }

    //更新商品信息
    @Job("autoUpdateVVICProductInfoV2")
    public ReturnT<String> updateVVICProductInfoV2(String param) {
        return DConcurrentTemplate.tryLockMode(
                "VVIC:FETCH_PRODUCT_PRICE",
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    log.info("vvic-获取搜款网商品价格同步到供应商后台 定时任务开始, param={}", param);
                    JSONObject paramObject = JSON.parseObject(param);
                    String thirdPartySystemId = paramObject.getString("thirdPartySystemId");
                    long gmtCreateLess = DateUtils.DateString2TimeStamp(paramObject.getString("gmtCreateLess"));
                    long gmtCreateGreater = DateUtils.DateString2TimeStamp(paramObject.getString("gmtCreateGreater"));

                    //根据thirdPartySystemId获取第三方系统参数
                    ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
                    VVICBaseParam vvicBaseParam = JSON.parseObject(thirdPartySystem.getParams(), VVICBaseParam.class);
                    String supplierId = vvicBaseParam.getSupplierId();

                    //根据供应商id查询供应商商品
                    SupplierSkuSearchDetailsCommand supplierSkuSearchDetailsCommand = new SupplierSkuSearchDetailsCommand();
                    supplierSkuSearchDetailsCommand.setSupplierId(supplierId);
                    //使用供应商id查询商品
                    //List<SupplierSkuInfo> skuInfos = supplierSkuRpc.getSkuInfo(supplierSkuSearchDetailsCommand);
                    PageList<ThirdPartyMappingInfo> pageResult;
                    int page = 0;
                    do {
                        pageResult = thirdPartyMappingManager.getNewestInfoByTargetBizIdAndGmtCreate("VVIC", "GSP", ThirdPartyMappingType.PRODUCT_ID, new PageCondition(page += 1, 200), gmtCreateLess, gmtCreateGreater);
                        if (null != pageResult && CollectionUtils.isNotEmpty(pageResult.getItems())) {
                            List<String> vidList = pageResult.getItems().stream().map(ThirdPartyMappingInfo::getTargetId).collect(Collectors.toList());
                            List<List<String>> lists = groupListByQuantity(vidList, 20);
                            lists.forEach(list->{
                                StringBuilder stringBuilder = new StringBuilder();
                                
                            });
                        }

                    } while (null != pageResult && CollectionUtils.isNotEmpty(pageResult.getItems()));

                    return ReturnT.SUCCESS;
                });
    }

    public ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }

/*    public static void main(String[] args) {
        String attr_str = "颜色:黑色,尺码:S M L,材质成分:其他材质100%,风格:通勤,通勤:韩版,裙长:短裙,袖长:短袖,领型:POLO领,衣门襟:,货号:实拍2539#,腰型:高腰,年份季节:2023年夏季,适用季节:夏季";
        SupplierSpuParamsInfo supplierSpuParamsInfo = new SupplierSpuParamsInfo();
        if (StringUtils.isNotBlank(attr_str)) {
            String[] attrs = attr_str.split(",");
            List<SupplierSpuParamInfo> customParams = new ArrayList<>();
            for (String attr : attrs) {
                String[] split = attr.split(":");
                SupplierSpuParamInfo supplierSpuParamInfo = new SupplierSpuParamInfo();
                supplierSpuParamInfo.setParamName(split[0]);
                List<SupplierSpuParamValueInfo> valueInfoList = new ArrayList<>();
                if (split.length > 1) {
                    String[] values = split[1].split(" ");
                    for (String value : values) {
                        SupplierSpuParamValueInfo supplierSpuParamValueInfo = new SupplierSpuParamValueInfo();
                        supplierSpuParamValueInfo.setParamValue(value);
                        valueInfoList.add(supplierSpuParamValueInfo);
                    }
                } else {
                    SupplierSpuParamValueInfo supplierSpuParamValueInfo = new SupplierSpuParamValueInfo();
                    supplierSpuParamValueInfo.setParamValue("");
                    valueInfoList.add(supplierSpuParamValueInfo);
                }
                supplierSpuParamInfo.setValues(valueInfoList);
                customParams.add(supplierSpuParamInfo);
            }
            supplierSpuParamsInfo.setCustomParams(customParams);
            System.out.println(JSON.toJSONString(supplierSpuParamsInfo));
        }
    }*/
}
