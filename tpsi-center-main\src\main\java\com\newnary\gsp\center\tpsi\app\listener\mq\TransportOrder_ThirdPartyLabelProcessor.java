package com.newnary.gsp.center.tpsi.app.listener.mq;

import com.newnary.gsp.center.tpsi.app.service.transport.TransportOrderCommandApp;
import com.newnary.gsp.center.tpsi.infra.mq.consumer.TransportOrderThirdPartyConsumer;
import com.newnary.messagebody.gsp.logistics.GSPTransportOrderThirdPartyTopic;
import com.newnary.messagebody.gsp.logistics.mo.TransportOrderThirdPartyMO;
import com.newnary.mq.starter.consumer.AbstractMQProcessor;
import com.newnary.mq.starter.consumer.MQMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class TransportOrder_ThirdPartyLabelProcessor extends AbstractMQProcessor<TransportOrderThirdPartyMO> {

    @Resource
    private TransportOrderCommandApp transportOrderCommandApp;

    @Override
    public boolean doProcess(MQMessage<TransportOrderThirdPartyMO> message) {
        log.info("获取面单{}", message.getContent());
        TransportOrderThirdPartyMO mo = message.getContent();
        transportOrderCommandApp.thirdPrintLabel(mo.transportOrderPackageId, mo.thirdOrderId, mo.thirdPartyCode);
        return true;
    }


    @Override
    public Class<?> consumerClz() {
        return TransportOrderThirdPartyConsumer.class;
    }

    @Override
    public String tag() {
        return GSPTransportOrderThirdPartyTopic.Tag.PULL_LABEL;
    }
}
