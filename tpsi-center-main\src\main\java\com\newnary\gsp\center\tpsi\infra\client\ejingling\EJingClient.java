package com.newnary.gsp.center.tpsi.infra.client.ejingling;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.common.utils.httpmethod.ApiBaseResult;
import com.newnary.common.utils.httpmethod.HttpMethodUtil;
import com.newnary.gsp.center.tpsi.infra.client.ejingling.params.EJingLingDataParam;
import com.newnary.gsp.center.tpsi.infra.client.ejingling.utils.EJingLingSignatureUtil;
import com.newnary.gsp.center.tpsi.infra.client.ejingling.valobj.EJingLingDataApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.ejingling.valobj.request.*;
import com.newnary.gsp.center.tpsi.infra.client.ejingling.valobj.response.*;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

@Slf4j
@Setter
@Getter
public class EJingClient {

    private String sellerNick;
    private String thirdPartyNick;
    private String baseUrl;
    private String key;

    public EJingClient(String eJingLingDataParam) {
        EJingLingDataParam param = JSON.parseObject(eJingLingDataParam, EJingLingDataParam.class);
        this.sellerNick = param.getSellerNick();
        //this.sellerNick = "融合科技";
        this.thirdPartyNick = param.getThirdPartyNick();
        //this.thirdPartyNick = "衫数平台";
        this.baseUrl = param.getBaseUrl();
        //this.baseUrl = "https://www.ejingling.cn";
        this.key = param.getKey();
        //this.key = "3F4F4165FD73A77BBF5E7A16E5D7A6A4";
    }

    //全量商品列表
    private String FULLGOODSLIST = "/api/mall/inter/goods/fullGoodsList";

    //店铺全量商品列表
    private String SHOPFULLGOODSLIST = "/api/mall/inter/goods/shop/fullGoodsList";

    //增量商品列表
    private String INCREGOODSLIST = "/api/mall/inter/goods/increGoodsList";

    //店铺增量商品列表
    private String SHOPINCREGOODSLIST = "/api/mall/inter/goods/shop/increGoodsList";

    //创建订单
    private String CREATEORDER = "/api/mall/inter/order/outerOrder/create";

    //订单发货
    private String ORDERDELIVER = "/api/mall/inter/order/outerOrderDeliver";

    //查询支持的快递公司
    private String GETCOURIER = "/api/mall/inter/waybill/Courier";

    //物流公司列表查询
    private String GETLOGISTICFIRM = "/api/mall/inter/courier/outerCourierList";

    //物流公司列表查询
    private String GETORDERBYORDERNO = "/api/mall/inter/order/saleOrderByOrderNo";

    //通过商品id获取商品信息
    private String GETGOODSBYGOODSID = "/api/mall/inter/goods/goodsListByIds";

    /**
     * 衫海精接口请求调用
     */
    private EJingLingDataApiBaseResult<String> sendRequest(String serviceUrl, String service, String contentType, JSONObject paramJson) {
        Map<String, Object> param = new HashMap<>();
        String url = baseUrl.concat(serviceUrl);
        ApiBaseResult apiBaseResult = null;
        Date date = new Date();
        try {
            System.out.println(url);
            String signature = EJingLingSignatureUtil.sign(paramJson.toString(), key, service, date.getTime());
            param.put("params", paramJson.toString());
            param.put("signature", signature);
            param.put("sellerNick", sellerNick);
            param.put("thirdPartyNick", thirdPartyNick);
            param.put("requestTime", date.getTime());
            apiBaseResult = HttpMethodUtil.syncPostMethod(url, 3, null, contentType, null, null, param);
            EJingLingDataApiBaseResult<String> eJingLingDataApiBaseResult = buildEJingLingDataApiBaseResult(apiBaseResult.getRet());
            log.info("[EJingLingData] 请求结束, service={}, code={}, resultSize={}, totalSize={}, message={}",service,apiBaseResult.getCode(),apiBaseResult.getRet().length(),apiBaseResult.getRet().length(),apiBaseResult.getMessage());
            return eJingLingDataApiBaseResult;
        } catch (Exception e) {
            e.printStackTrace();
            EJingLingDataApiBaseResult<String> ret = new EJingLingDataApiBaseResult<>();
            ret.setCode(0);
            ret.setMsg("异常:".concat(e.getMessage()));
            return ret;
        }
    }

    //全量列表
    public EJingLingGoodsListResponse getFullGoodsList(EJingLingFullGoodsListReq req) {
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(req));
        EJingLingDataApiBaseResult<String> dataApiBaseResult = sendRequest(FULLGOODSLIST, "fullGoodsList", "application/json", jsonObject);
        if (ObjectUtils.isEmpty(dataApiBaseResult)) {
            return new EJingLingGoodsListResponse();
        }
        return JSON.parseObject(dataApiBaseResult.getResult(), EJingLingGoodsListResponse.class);
    }

    //店铺全量列表
    public EJingLingGoodsListResponse getShopFullGoodsList(EJingLingShopFullGoodsListReq req) {
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(req));
        EJingLingDataApiBaseResult<String> dataApiBaseResult = sendRequest(SHOPFULLGOODSLIST, "shopFullGoodsList", "application/json", jsonObject);
        if (ObjectUtils.isEmpty(dataApiBaseResult)) {
            return new EJingLingGoodsListResponse();
        }
        return JSON.parseObject(dataApiBaseResult.getResult(), EJingLingGoodsListResponse.class);
    }

    //增量列表
    public EJingLingGoodsListResponse getIncreGoodsList(EJingLingIncreGoodsListReq req) {
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(req));
        EJingLingDataApiBaseResult<String> dataApiBaseResult = sendRequest(INCREGOODSLIST, "increGoodsList", "application/json", jsonObject);
        if (ObjectUtils.isEmpty(dataApiBaseResult)) {
            return new EJingLingGoodsListResponse();
        }
        return JSON.parseObject(dataApiBaseResult.getResult(), EJingLingGoodsListResponse.class);
    }

    //店铺增量列表
    public EJingLingGoodsListResponse getShopIncreGoodsList(EJingLingShopIncreGoodsListReq req) {
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(req));
        EJingLingDataApiBaseResult<String> dataApiBaseResult = sendRequest(SHOPINCREGOODSLIST, "shopIncreGoodsList", "application/json", jsonObject);
        if (ObjectUtils.isEmpty(dataApiBaseResult)) {
            return new EJingLingGoodsListResponse();
        }
        return JSON.parseObject(dataApiBaseResult.getResult(), EJingLingGoodsListResponse.class);
    }

    //创建订单
    public List<EJingLingCreateOrderResponse> createOrder(EJingLingCreateOrderReq req) {
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(req));
        EJingLingDataApiBaseResult<String> dataApiBaseResult = sendRequest(CREATEORDER, "outerOrderCreate", "application/json", jsonObject);
        if (ObjectUtils.isEmpty(dataApiBaseResult)) {
            return new ArrayList<>();
        }
        log.info("订单创建：".concat(req.getOuterOrderId()).concat(":").concat(dataApiBaseResult.getMsg()));
        return JSON.parseArray(dataApiBaseResult.getResult(), EJingLingCreateOrderResponse.class);
    }

    //订单发货
    public String orderDeliver(EJingLingOrderDeliverReq req) {
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(req));
        EJingLingDataApiBaseResult<String> dataApiBaseResult = sendRequest(ORDERDELIVER, "outerOrderDeliver", "application/json", jsonObject);
        if (ObjectUtils.isEmpty(dataApiBaseResult)) {
            return "";
        }
        log.info("订单发货：".concat(req.getOrderNo()).concat(":").concat(dataApiBaseResult.getMsg()));
        return dataApiBaseResult.getMsg();
    }

    //查询支持的快递公司
    public List<EJingLingGetCourierResponse> getCourier(EJingLingGetCourierReq req) {
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(req));
        EJingLingDataApiBaseResult<String> dataApiBaseResult = sendRequest(GETCOURIER, "waybillCourier", "application/json", jsonObject);
        if (ObjectUtils.isEmpty(dataApiBaseResult)) {
            return new ArrayList<>();
        }
        return JSON.parseArray(dataApiBaseResult.getResult(),EJingLingGetCourierResponse.class);
    }

    //查询物流公司列表
    public List<EJingLingLogisticsFirmResponse> getLogisticsFrirm() {
        EJingLingDataApiBaseResult<String> dataApiBaseResult = sendRequest(GETLOGISTICFIRM, "outerCourierList", "application/json", new JSONObject());
        if (ObjectUtils.isEmpty(dataApiBaseResult)) {
            return new ArrayList<>();
        }
        return JSON.parseArray(dataApiBaseResult.getResult(),EJingLingLogisticsFirmResponse.class);
    }

    //通过订单编码查询订单
    public List<EJingLingGetOrderResponse> getOrderByOrderNo(String orderNo) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderNo",orderNo);
        EJingLingDataApiBaseResult<String> dataApiBaseResult = sendRequest(GETORDERBYORDERNO, "saleOrderByOrderNo", "application/json", jsonObject);
        if (ObjectUtils.isEmpty(dataApiBaseResult)) {
            return new ArrayList<>();
        }
        return JSON.parseArray(dataApiBaseResult.getResult(),EJingLingGetOrderResponse.class);
    }

    //查询商品信息
    public List<EJingLingGoodsListResponse.OuterGoodsVo> getGoodsByGoodsIds(String ids) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("goodsIds",ids);
        EJingLingDataApiBaseResult<String> dataApiBaseResult = sendRequest(GETGOODSBYGOODSID, "goodsListByIds", "application/json", jsonObject);
        if (ObjectUtils.isEmpty(dataApiBaseResult)) {
            return new ArrayList<>();
        }
        return JSON.parseArray(dataApiBaseResult.getResult(),EJingLingGoodsListResponse.OuterGoodsVo.class);
    }

    private EJingLingDataApiBaseResult<String> buildEJingLingDataApiBaseResult(String result) {
        if (StringUtils.isBlank(result)) {
            EJingLingDataApiBaseResult<String> baseResult = new EJingLingDataApiBaseResult<>();
            baseResult.setCode(0);
            baseResult.setMsg("没有返回数据");
            return baseResult;
        }
        EJingLingDataApiBaseResult<String> dataApiBaseResult = new EJingLingDataApiBaseResult<>();
        JSONObject parse = JSON.parseObject(result);
        dataApiBaseResult.setCode(Integer.valueOf(parse.getString("code")));
        dataApiBaseResult.setMsg(parse.getString("msg"));
        dataApiBaseResult.setResult(parse.getString("result"));
        return dataApiBaseResult;
    }
}
