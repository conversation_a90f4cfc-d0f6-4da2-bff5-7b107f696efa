package com.newnary.gsp.center.tpsi.infra.client.ejingling.valobj.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class EJingLingGoodsListResponse {
    /**
     * 是否为最后一页
     */
    private String lastPage;
    /**
     * 外部商品列表
     */
    private List<OuterGoodsVo> outerGoodsVoList;

    @Setter
    @Getter
    public static class OuterGoodsVo{
        /**
         * 商品id
         */
        private Long goodsId;

        /**
         * 商品淘宝类目id
         */
        private Long platformCategoryId;

        /**
         * 类目名称
         */
        private String categoryName;

        /**
         * 商品编码
         */
        private String goodsNo;

        /**
         * 商品名称
         */
        private String goodsName;

        /**
         * 商品状态，2.下架，3.上架
         */
        private Integer status;

        /**
         * 主图url
         */
        private String mainPicUrl;

        /**
         * 市场id
         */
        private Long marketId;

        /**
         * 淘宝行业属性，格式："k1:v1:nk1:nv1;k2:v2:nk2:nv2"
         */
        private String businessProps;

        /**
         * 上架时间
         */
        private String putAwayTime;

        /**
         * 创建时间
         */
        private String createTime;

        /**
         * 修改时间
         */
        private String lastUpdateTime;


        private GoodsPic goodsPicList;


        private List<Sku> skuList;

        private List<SaleProp> salePropList;

        private List<PropPic> propPicList;
    }

    @Setter
    @Getter
    public static class GoodsPic{
        /**
         * 主图列表
         */
        private String mainPics;

        /**
         * 主图视频
         */
        private String videoUrl;

        /**
         * 富文本
         */
        private String htmlUrl;
    }

    @Setter
    @Getter
    public static class Sku{

        /**
         * 商品明细id
         */
        private Long skuId;

        /**
         * 商品明细编码
         */
        private String skuNo;

        /**
         * 规格
         */
        private String goodsSpec;

        /**
         * 规格编码
         */
        private String goodsSpecCode;

        /**
         * 图片url
         */
        private String picUrl;

        /**
         * sku成本价格
         */
        private Double skuCostPrice;

        /**
         * sku销售价格
         */
        private Double skuSalePrice;

        /**
         * 库存
         */
        private Integer stockNum;

        /**
         * 创建时间
         */
        private String createTime;

        /**
         * 修改时间
         */
        private String lastUpdateTime;
    }

    @Setter
    @Getter
    public static class SaleProp{

        /**
         * 销售属性code kv，格式 1627207:28320
         */
        private String propCode;

        /**
         * 销售属性name kv，格式 颜色:白色
         */
        private String propName;

        /**
         * 1-是颜色属性  0或者无返回则不是
         */
        private Integer isColor;
    }
    @Setter
    @Getter
    public static class PropPic{
        /**
         * 颜色code，格式 1627207:28320
         */
        private String colorCode;

        /**
         * 颜色名称，格式 颜色:白色
         */
        private String colorName;

        /**
         * 属性图url
         */
        private String propPicUrl;
    }
}
