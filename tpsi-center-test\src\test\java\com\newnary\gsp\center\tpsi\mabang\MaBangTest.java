package com.newnary.gsp.center.tpsi.mabang;

import com.newnary.gsp.center.tpsi.app.job.MaBangDataJobManager;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.test.starter.BaseTestInjectTenant;
import org.junit.Test;

import javax.annotation.Resource;

public class MaBangTest extends BaseTestInjectTenant {

    @Override
    protected String tenantId() {
        return "GZRM";
    }

    @Resource
    private MaBangDataJobManager maBangDataJobManager;

    @Test
    public void testSyncMaBangProduct(){
        ReturnT<String> stringReturnT = maBangDataJobManager.autoDoSyncMaBangProduct("{\"thirdPartySystemId\":\"TESTMABANG0001\",\"spuStartPage\":40,\"spuEndPage\":1,\"pageSize\":100}");
    }
}
