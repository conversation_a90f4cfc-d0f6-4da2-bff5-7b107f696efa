package com.newnary.gsp.center.tpsi.infra.client.tongtu.valobj.request.erp2;

import lombok.Data;

import java.util.List;

@Data
public class TongTuOrderImportOrder {

    /**
     * 买家信息
     */
    private TongTuOrderImportOrderBuyerInfo buyerInfo;

    /**
     * 币种
     */
    private String currency;

    /**
     * 买家支付的保险
     */
    private String insuranceIncome;

    /**
     * 买家支付的保险币种
     */
    private String insuranceIncomeCurrency;

    /**
     * 是否需要返回通途订单ID,0-不需要1-需要 默认0不需要;
     * 如果需要返回订单ID那么返回结果集是一个Object:{"orderId":"","saleRecordNum":""},否则返回一个字符串，内容是saleRecordNum
     */
    private String needReturnOrderId;

    /**
     * 买家留言
     */
    private String notes;

    /**
     * 订单币种
     */
    private String ordercurrency;

    private List<TongTuOrderImportOrderPaymentInfo> paymentInfos;

    /**
     * 订单平台代码
     */
    private String platformCode;

    /**
     * 订单备注,只能新增
     */
    private List<String> remarks;

    /**
     * 订单号
     */
    private String saleRecordNum;

    /**
     * 卖家账号代码
     */
    private String sellerAccountCode;

    /**
     * 渠道ID
     */
    private String shippingMethodId;

    /**
     * 买家支付的税金
     */
    private String taxIncome;

    /**
     * 买家支付的税金币种
     */
    private String taxIncomeCurrency;

    /**
     * 订单总额
     */
    private String totalPrice;

    /**
     * 订单总额币种
     */
    private String totalPriceCurrency;

    /**
     * 交易信息
     */
    private List<TongTuOrderImportOrderTransaction> transactions;

    /**
     * 仓库ID
     */
    private String warehouseId;

}
