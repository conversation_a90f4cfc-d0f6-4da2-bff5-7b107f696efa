package com.newnary.gsp.center.tpsi.infra.client.ejingling.valobj.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Data
public class EJingLingGetOrderResponse {

    private String saleOrderNo;
    private Integer orderStatus;
    private Integer totalNum;
    private Double actualAmount;
    private Double postAmount;
    private String message;
    private String businessNo;
    private Date payTime;
    private Date deliverTime;
    private Date receiveTime;
    private Date createTime;
    private Date lastUpdateTime;
    private String receiverName;
    private String receiverMobile;
    private String receiverAddress;
    private List<OrderDetail> orderDetailList;
    private List<CourierInfo> courierInfoList;

    @Setter
    @Getter
    public static class OrderDetail {
        private Long saleOrderDetailId;
        private Long outerGoodsId;
        private String sellerOuterNo;
        private String goodsName;
        private String mainPic;
        private Long outerSkuId;
        private String skuNo;
        private Integer num;
        private Double price;
        private Double totalAmount;
    }

    @Getter
    @Setter
    public static class CourierInfo {
        private String courierName;
        private String courierOrderNo;
    }


}
