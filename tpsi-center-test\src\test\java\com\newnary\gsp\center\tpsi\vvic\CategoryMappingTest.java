package com.newnary.gsp.center.tpsi.vvic;

import com.newnary.gsp.center.product.api.thirdpartymapping.vo.ThirdPartyMappingType;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.ThirdPartyMappingManager;
import com.newnary.test.starter.BaseTestInjectTenant;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.junit.Test;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;

public class CategoryMappingTest extends BaseTestInjectTenant {

    private static final String SOURCE_BIZ_ID = "GSP";
    private static final String TARGET_BIZ_ID = "VVIC";

    @Resource
    private ThirdPartyMappingManager thirdPartyMappingManager;

    @Override
    protected String tenantId() {
        return "TENANT3924117688555169382400";
    }

    @Test
    public void initCategory() {
        File file = new File("D:\\备份资料\\open\\项目资料\\类目\\6.12\\搜款网类目对应阿里类目.xlsx");
        int titleRow = 1;
        try {
            Workbook wb = WorkbookFactory.create(file);
            Sheet sheet = wb.getSheet("英文类目");
            if (sheet == null) {
                throw new RuntimeException("Sheet1无数据");
            }
            int totalRows = sheet.getLastRowNum();

            for (int i = titleRow; i <= totalRows; i++) {
                Row row = sheet.getRow(i);
                if (null == row) {
                    return;
                }
                String categoryPath = row.getCell(0).getStringCellValue().concat("/").concat(row.getCell(1).getStringCellValue().concat("/").concat(row.getCell(2).getStringCellValue()));
                System.out.println(row.getRowNum());
                if (null != row.getCell(6) && !"#N/A".equals(row.getCell(6).toString())) {
                    thirdPartyMappingManager.insertOrUpdateCategory(SOURCE_BIZ_ID,TARGET_BIZ_ID,row.getCell(6).getStringCellValue(),null,categoryPath, ThirdPartyMappingType.CATEGORY.name(),null);
                    //thirdPartyMappingManager.insertOrUpdateCategory(SOURCE_BIZ_ID,TARGET_BIZ_ID,"PMC357988525732640194560",null,categoryPath, ThirdPartyMappingType.CATEGORY.name(),null);
                }
                titleRow++;/*else if (null != row.getCell(13) && !"#N/A".equals(row.getCell(13).toString())) {
                    thirdPartyMappingManager.insertOrUpdateCategory(SOURCE_BIZ_ID,TARGET_BIZ_ID,row.getCell(13).getStringCellValue(),null,categoryPath, ThirdPartyMappingType.CATEGORY.name(),null);
                }else if (null != row.getCell(12) && !"#N/A".equals(row.getCell(12).toString())) {
                    thirdPartyMappingManager.insertOrUpdateCategory(SOURCE_BIZ_ID,TARGET_BIZ_ID,row.getCell(12).getStringCellValue(),null,categoryPath, ThirdPartyMappingType.CATEGORY.name(),null);
                }else if (null != row.getCell(11) && !"#N/A".equals(row.getCell(11).toString())) {
                    thirdPartyMappingManager.insertOrUpdateCategory(SOURCE_BIZ_ID,TARGET_BIZ_ID,row.getCell(11).getStringCellValue(),null,categoryPath, ThirdPartyMappingType.CATEGORY.name(),null);
                }*/
            }
        } catch (InvalidFormatException | IOException e) {
            e.printStackTrace();
        }
    }
}
