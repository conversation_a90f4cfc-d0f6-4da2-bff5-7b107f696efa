package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Data
public class EcCangERPGetSupplierListRequest {

    private Integer page;
    private Integer pageSize;

    private Condition condition;

    @Setter
    @Getter
    public static class Condition {

        /**
         * 启用状态，0正式供应商，1草稿，2待审核，3暂停供应商
         */
        private Integer supplierStatus;

        /**
         * 结算类型，1:货到付款，2:款到发货，3:帐期
         */
        private Integer accountType;

        /**
         * 供应商代码
         */
        private String supplierCode;

        /**
         * 供应商名称
         */
        private String supplierName;

        /**
         * 采购员
         */
        private String buyerId;

        /**
         * 跟单员
         */
        private String trackId;

        /**
         * 主营品类代码，一级品类代码
         */
        private String supplierMainCategoryCode;

        private String dateFor;
        private String dateTo;
    }

}
