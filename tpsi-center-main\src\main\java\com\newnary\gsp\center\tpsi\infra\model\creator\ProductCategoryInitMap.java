package com.newnary.gsp.center.tpsi.infra.model.creator;

import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

@Data
public class ProductCategoryInitMap {

    Map<String, String> map = new LinkedHashMap<>(3000);

    {
        categoryInit();
    }

    private void categoryInit() {
        {
            map.put("暖贴、暖宝宝、发热贴","PMC6993307310578727124992-100");
            map.put("充电打火机、电子点烟器","PMC7074307310641629102080-100");
            map.put("打火机","PMC7074307310641629102080-100");
            map.put("火柴","PMC7074307310641629102080-100");
            map.put("创意伞具","PMC7983307311198494261248-100");
            map.put("垂钓伞","PMC7983307311198494261248-100");
            map.put("挡风衣、挡风被","PMC7983307311198494261248-100");
            map.put("儿童伞具","PMC7983307311198494261248-100");
            map.put("高尔夫伞","PMC7983307311198494261248-100");
            map.put("工艺伞","PMC7983307311198494261248-100");
            map.put("广告伞","PMC7983307311198494261248-100");
            map.put("其他挡风遮阳防雨工具","PMC7983307311198494261248-100");
            map.put("其他伞","PMC7983307311198494261248-100");
            map.put("伞架/伞配件","PMC7983307311198494261248-100");
            map.put("沙滩伞","PMC7983307311198494261248-100");
            map.put("油纸伞","PMC7983307311198494261248-100");
            map.put("装饰伞","PMC7983307311198494261248-100");
            map.put("冰垫","PMC7751307311058161238016-100");
            map.put("防打鼾用品","PMC6640307310338628386816-100");
            map.put("迷你手持扇","PMC6751307310407259783168-100");
            map.put("日用冰贴","PMC6993307310578727124992-100");
            map.put("扇子","PMC6751307310407259783168-100");
            map.put("炭盒/竹炭包","PMC7011307310590911578112-100");
            map.put("针线盒","PMC6743307310402553774080-100");
            map.put("冰箱除味剂","PMC7327307310802442911744-100");
            map.put("除菌卡","PMC7327307310802442911744-100");
            map.put("家用除湿剂、干燥剂","PMC7327307310802442911744-100");
            map.put("洁烟沙","PMC7327307310802442911744-100");
            map.put("空气清新/净化剂","PMC7327307310802442911744-100");
            map.put("其他空气净化用品","PMC7327307310802442911744-100");
            map.put("无火香薰","PMC6633307310334752849920-100");
            map.put("香薰","PMC6633307310334752849920-100");
            map.put("香薰补充液","PMC6633307310334752849920-100");
            map.put("香薰膏","PMC6633307310334752849920-100");
            map.put("香薰石","PMC6633307310334752849920-100");
            map.put("熏香、盘香、塔香","PMC6633307310334752849920-100");
            map.put("樟脑丸","PMC7011307310590911578112-100");
            map.put("竹炭包","PMC7011307310590911578112-100");
            map.put("管道疏通剂","PMC7007307310588353052672-100");
            map.put("洁瓷剂","PMC7007307310588353052672-100");
            map.put("亮碟（碗）剂","PMC7007307310588353052672-100");
            map.put("尿池除臭用品","PMC7007307310588353052672-100");
            map.put("洗洁精","PMC7007307310588353052672-100");
            map.put("洗碗粉","PMC7007307310588353052672-100");
            map.put("洗碗洗涤剂","PMC7007307310588353052672-100");
            map.put("皂粉清洁球","PMC7007307310588353052672-100");
            map.put("地毯干洗剂","PMC7007307310588353052672-100");
            map.put("其他地板清洁护理","PMC7007307310588353052672-100");
            map.put("皮革家具护理品","PMC7007307310588353052672-100");
            map.put("其他家居清洁护理","PMC7007307310588353052672-100");
            map.put("墙体除霉剂","PMC7007307310588353052672-100");
            map.put("鞋油","PMC7007307310588353052672-100");
            map.put("假牙清洁","PMC6679307310362871463936-100");
            map.put("口喷","PMC6679307310362871463936-100");
            map.put("口腔凝胶","PMC6679307310362871463936-100");
            map.put("旅行（便携）手动牙刷/套装","PMC6679307310362871463936-100");
            map.put("舌苔清洁器","PMC6679307310362871463936-100");
            map.put("牙粉","PMC6679307310362871463936-100");
            map.put("牙贴","PMC6679307310362871463936-100");
            map.put("月子牙刷","PMC6679307310362871463936-100");
            map.put("花露水、防蚊液","PMC6667307310355581763584-100");
            map.put("灭蚁用品","PMC6667307310355581763584-100");
            map.put("灭蟑用品","PMC6667307310355581763584-100");
            map.put("清凉油","PMC6667307310355581763584-100");
            map.put("驱虫用品","PMC6667307310355581763584-100");
            map.put("驱蚊手环","PMC6667307310355581763584-100");
            map.put("蚊香液/蚊香片","PMC6667307310355581763584-100");
            map.put("婴幼儿驱蚊防蚊","PMC6667307310355581763584-100");
            map.put("擦手纸","PMC7021307310600386510848-100");
            map.put("抽纸","PMC7021307310600386510848-100");
            map.put("大盘纸","PMC7021307310600386510848-100");
            map.put("拉拉裤","PMC7021307310600386510848-100");
            map.put("面巾纸","PMC7021307310600386510848-100");
            map.put("其他生活用纸","PMC7021307310600386510848-100");
            map.put("柔巾卷","PMC7021307310600386510848-100");
            map.put("湿厕纸","PMC7022307310601154068480-100");
            map.put("原轴纸/杠纸","PMC7021307310600386510848-100");
            map.put("纸尿片","PMC6646307310342365511680-100");
            map.put("坐厕纸","PMC7022307310601154068480-100");
            map.put("大码风衣","PMC6162307310030741307392-100");
            map.put("大码毛衣","PMC7474307310892020662272-100");
            map.put("大码棉服","PMC6162307310030741307392-100");
            map.put("大码皮草","PMC6162307310030741307392-100");
            map.put("大码皮衣","PMC8249307311359832358912-100");
            map.put("大码其他女式上装","PMC6162307310030741307392-100");
            map.put("大码卫衣、绒衣","PMC6162307310030741307392-100");
            map.put("大码羽绒服","PMC6162307310030741307392-100");
            map.put("动漫角色扮演服","PMC6162307310030741307392-100");
            map.put("防辐射服","PMC6162307310030741307392-100");
            map.put("汉服/禅茶服/禅舞服","PMC6162307310030741307392-100");
            map.put("婚纱","PMC8303307311392602456064-100");
            map.put("礼服/晚装","PMC8303307311392602456064-100");
            map.put("洛丽塔Lolita服/配件","PMC6162307310030741307392-100");
            map.put("毛衣","PMC7474307310892020662272-100");
            map.put("棉衣","PMC6162307310030741307392-100");
            map.put("女式貂绒衫","PMC7474307310892020662272-100");
            map.put("女式唐装","PMC6162307310030741307392-100");
            map.put("女式羊毛衫","PMC7474307310892020662272-100");
            map.put("女式羊绒衫","PMC7474307310892020662272-100");
            map.put("其他民族女装","PMC6162307310030741307392-100");
            map.put("旗袍","PMC8302307311391797149696-100");
            map.put("舞台服、表演服","PMC6162307310030741307392-100");
            map.put("秀禾服","PMC8303307311392602456064-100");
            map.put("羽绒服","PMC6162307310030741307392-100");
            map.put("孕妇裤","PMC8256307311364555145216-100");
            map.put("孕妇套装","PMC8275307311375779102720-100");
            map.put("裙装","PMC8277307311377062559744-100");
            map.put("连衣裙","PMC8299307311390056513536-100");
            map.put("POLO衫","PMC8291307311385409224704-100");
            map.put("短裤","PMC7460307310883590111232-100");
            map.put("其他服饰","PMC6162307310030741307392-100");
            map.put("外套","PMC8249307311359832358912-100");
            map.put("其他裤子","PMC8256307311364555145216-100");
            map.put("T恤","PMC8297307311388735307776-100");
            map.put("衬衫","PMC8293307311386038370304-100");
            map.put("外套","PMC8249307311359832358912-100");
            map.put("外套","PMC8249307311359832358912-100");
            map.put("外套","PMC8249307311359832358912-100");
            map.put("其他服饰","PMC6162307310030741307392-100");
            map.put("其他服饰","PMC6162307310030741307392-100");
            map.put("牛仔裤","PMC7454307310879597133824-100");
            map.put("外套","PMC8249307311359832358912-100");
            map.put("卫衣","PMC8246307311357512908800-100");
            map.put("毛衣&针织","PMC7474307310892020662272-100");
            map.put("其他服饰","PMC6162307310030741307392-100");
            map.put("外套","PMC8249307311359832358912-100");
            map.put("其他套装","PMC8275307311375779102720-100");
            map.put("背心、背心和抹胸上衣","PMC8295307311387242135552-100");
            map.put("西装","PMC8248307311359027052544-100");
            map.put("其他服饰","PMC6162307310030741307392-100");
            map.put("唇蜜","PMC6253307310087028867072-100");
            map.put("唇釉","PMC6253307310087028867072-100");
            map.put("宝宝护唇/润唇","PMC6289307310111380996096-100");
            map.put("唇部磨砂","PMC6289307310111380996096-100");
            map.put("唇膏、唇油","PMC6289307310111380996096-100");
            map.put("唇膜、唇霜","PMC6289307310111380996096-100");
            map.put("甲片","PMC6228307310072277499904-100");
            map.put("甲油胶","PMC6229307310073019891712-100");
            map.put("卸甲用品","PMC6232307310074664058880-100");
            map.put("指甲锉","PMC6228307310072277499904-100");
            map.put("指甲刷","PMC6228307310072277499904-100");
            map.put("痘痘贴","PMC6276307310101310472192-100");
            map.put("粉刺针、暗疮针、黑头夹","PMC6276307310101310472192-100");
            map.put("粉扑、美妆蛋","PMC6200307310055043104768-100");
            map.put("粉扑清洁液","PMC6200307310055043104768-100");
            map.put("画眉、修眉套装","PMC6200307310055043104768-100");
            map.put("睫毛夹、烫睫毛器","PMC6200307310055043104768-100");
            map.put("睫毛刷","PMC6200307310055043104768-100");
            map.put("卷发球、发卷","PMC6200307310055043104768-100");
            map.put("眉刀、眉剪、眉夹","PMC6200307310055043104768-100");
            map.put("眉卡","PMC6200307310055043104768-100");
            map.put("眉刷","PMC6200307310055043104768-100");
            map.put("眉贴","PMC6200307310055043104768-100");
            map.put("美发剪","PMC6200307310055043104768-100");
            map.put("美发梳","PMC6200307310055043104768-100");
            map.put("美睫胶","PMC6200307310055043104768-100");
            map.put("面膜纸、棒、碗","PMC6200307310055043104768-100");
            map.put("其他美妆工具","PMC6200307310055043104768-100");
            map.put("双眼皮贴、胶水","PMC6200307310055043104768-100");
            map.put("吸油面纸","PMC6200307310055043104768-100");
            map.put("洗脸扑","PMC6200307310055043104768-100");
            map.put("BB霜","PMC6248307310084281597952-100");
            map.put("粉饼","PMC6248307310084281597952-100");
            map.put("隔离/妆前","PMC6248307310084281597952-100");
            map.put("蜜粉、散粉、定妆粉","PMC6250307310085695078400-100");
            map.put("修容","PMC6243307310081345585152-100");
            map.put("遮瑕","PMC6243307310081345585152-100");
            map.put("T区护理套装","PMC6191307310048885866496-100");
            map.put("鼻贴、鼻膜","PMC6266307310095442640896-100");
            map.put("黑头导出液","PMC6266307310095442640896-100");
            map.put("护肤旅行装","PMC6266307310095442640896-100");
            map.put("洁面产品","PMC6282307310106624655360-100");
            map.put("面部护理套装","PMC6292307310113734000640-100");
            map.put("其他面部护肤品","PMC6266307310095442640896-100");
            map.put("手工皂","PMC6178307310040774082560-100");
            map.put("安瓶、原液","PMC6287307310110646992896-100");
            map.put("面膜粉","PMC6279307310102958833664-100");
            map.put("面膜套装","PMC6279307310102958833664-100");
            map.put("贴片面膜","PMC6279307310102958833664-100");
            map.put("涂抹面膜","PMC6279307310102958833664-100");
            map.put("男士洁面","PMC6273307310099410452480-100");
            map.put("男士面部护理套装","PMC6273307310099410452480-100");
            map.put("男士面膜","PMC6273307310099410452480-100");
            map.put("男士润唇膏","PMC6273307310099410452480-100");
            map.put("男士眼霜","PMC6273307310099410452480-100");
            map.put("固体香膏","PMC6274307310099997655040-100");
            map.put("眼线","PMC6238307310078124359680-100");
            map.put("眼膜","PMC6277307310102233219072-100");
            map.put("眼霜","PMC6277307310102233219072-100");
            map.put("保温餐桌、保温板","PMC4179321519378468311040-100");
            map.put("保温餐桌、保温板","PMC4179321519378468311040-100");
            map.put("冰箱净化器","PMC4179321519378468311040-100");
            map.put("冰箱净化器","PMC4179321519378468311040-100");
            map.put("茶吧机","PMC4179321519378468311040-100");
            map.put("茶吧机","PMC4179321519378468311040-100");
            map.put("刀具除菌机","PMC4179321519378468311040-100");
            map.put("刀具除菌机","PMC4179321519378468311040-100");
            map.put("电饼铛、煎烤炉","PMC4179321519378468311040-100");
            map.put("电饼铛、煎烤炉","PMC4179321519378468311040-100");
            map.put("电动便携速冷器","PMC4179321519378468311040-100");
            map.put("电动便携速冷器","PMC4179321519378468311040-100");
            map.put("电动打蛋器","PMC7278307310771518308352-100");
            map.put("电动打蛋器","PMC7278307310771518308352-100");
            map.put("电动煮茶器","PMC4179321519378468311040-100");
            map.put("电动煮茶器","PMC4179321519378468311040-100");
            map.put("电炖锅","PMC4179321519378468311040-100");
            map.put("电炖锅","PMC4179321519378468311040-100");
            map.put("电饭煲、电饭锅","PMC4179321519378468311040-100");
            map.put("电饭煲、电饭锅","PMC4179321519378468311040-100");
            map.put("电热饭盒","PMC4179321519378468311040-100");
            map.put("电热饭盒","PMC4179321519378468311040-100");
            map.put("电热炉","PMC4179321519378468311040-100");
            map.put("电热炉","PMC4179321519378468311040-100");
            map.put("电陶炉","PMC7287307310777621020672-100");
            map.put("电陶炉","PMC7287307310777621020672-100");
            map.put("电煮锅","PMC4179321519378468311040-100");
            map.put("电煮锅","PMC4179321519378468311040-100");
            map.put("豆浆机","PMC7274307310769416962048-100");
            map.put("豆浆机","PMC7274307310769416962048-100");
            map.put("豆芽机","PMC4179321519378468311040-100");
            map.put("豆芽机","PMC4179321519378468311040-100");
            map.put("多士炉、三明治机","PMC4179321519378468311040-100");
            map.put("多士炉、三明治机","PMC4179321519378468311040-100");
            map.put("果蔬解毒机、净化机","PMC4179321519378468311040-100");
            map.put("果蔬解毒机、净化机","PMC4179321519378468311040-100");
            map.put("恒温雪茄柜","PMC4179321519378468311040-100");
            map.put("恒温雪茄柜","PMC4179321519378468311040-100");
            map.put("家用菜馅机","PMC4179321519378468311040-100");
            map.put("家用菜馅机","PMC4179321519378468311040-100");
            map.put("家用炒菜机","PMC4179321519378468311040-100");
            map.put("家用炒菜机","PMC4179321519378468311040-100");
            map.put("家用电动涮烤锅","PMC4179321519378468311040-100");
            map.put("家用电动涮烤锅","PMC4179321519378468311040-100");
            map.put("家用电炸锅","PMC4179321519378468311040-100");
            map.put("家用电炸锅","PMC4179321519378468311040-100");
            map.put("家用干果机","PMC4179321519378468311040-100");
            map.put("家用干果机","PMC4179321519378468311040-100");
            map.put("家用空气炸锅","PMC4179321519378468311040-100");
            map.put("家用空气炸锅","PMC4179321519378468311040-100");
            map.put("家用面条机","PMC4179321519378468311040-100");
            map.put("家用面条机","PMC4179321519378468311040-100");
            map.put("家用酿酒机","PMC4179321519378468311040-100");
            map.put("家用酿酒机","PMC4179321519378468311040-100");
            map.put("家用破壁机","PMC4179321519378468311040-100");
            map.put("家用破壁机","PMC4179321519378468311040-100");
            map.put("家用早餐机","PMC4179321519378468311040-100");
            map.put("家用早餐机","PMC4179321519378468311040-100");
            map.put("家用榨油机","PMC4179321519378468311040-100");
            map.put("家用榨油机","PMC4179321519378468311040-100");
            map.put("净水器、净水设备","PMC4179321519378468311040-100");
            map.put("净水器配件、附件","PMC4179321519378468311040-100");
            map.put("净水器配件、附件","PMC4179321519378468311040-100");
            map.put("冷饮机","PMC4179321519378468311040-100");
            map.put("冷饮机","PMC4179321519378468311040-100");
            map.put("迷你电饭煲","PMC4179321519378468311040-100");
            map.put("迷你电饭煲","PMC4179321519378468311040-100");
            map.put("奶泡机","PMC4179321519378468311040-100");
            map.put("奶泡机","PMC4179321519378468311040-100");
            map.put("刨冰机","PMC4179321519378468311040-100");
            map.put("刨冰机","PMC4179321519378468311040-100");
            map.put("热得快","PMC4179321519378468311040-100");
            map.put("热得快","PMC4179321519378468311040-100");
            map.put("酸奶机","PMC4179321519378468311040-100");
            map.put("酸奶机","PMC4179321519378468311040-100");
            map.put("碎冰机","PMC4179321519378468311040-100");
            map.put("碎冰机","PMC4179321519378468311040-100");
            map.put("消毒刀架","PMC4179321519378468311040-100");
            map.put("消毒刀架","PMC4179321519378468311040-100");
            map.put("养生壶、煎药壶","PMC4179321519378468311040-100");
            map.put("养生壶、煎药壶","PMC4179321519378468311040-100");
            map.put("原汁机","PMC4179321519378468311040-100");
            map.put("原汁机","PMC4179321519378468311040-100");
            map.put("蒸烤一体机","PMC4179321519378468311040-100");
            map.put("蒸烤一体机","PMC4179321519378468311040-100");
            map.put("制冰机","PMC4179321519378468311040-100");
            map.put("制冰机","PMC4179321519378468311040-100");
            map.put("智能杀菌筷筒","PMC4179321519378468311040-100");
            map.put("智能杀菌筷筒","PMC4179321519378468311040-100");
            map.put("煮蛋器","PMC4179321519378468311040-100");
            map.put("煮蛋器","PMC4179321519378468311040-100");
            map.put("紫砂煲","PMC4179321519378468311040-100");
            map.put("紫砂煲","PMC4179321519378468311040-100");
            map.put("自动上水器","PMC4179321519378468311040-100");
            map.put("自动上水器","PMC4179321519378468311040-100");
            map.put("恒温酒柜","PMC6720321682709955092480-100");
            map.put("家用冷柜","PMC7297307310783333662720-100");
            map.put("空调","PMC6720321682709955092480-100");
            map.put("空调配件、附件","PMC6720321682709955092480-100");
            map.put("排气、换气扇","PMC6720321682709955092480-100");
            map.put("燃气灶具","PMC6720321682709955092480-100");
            map.put("洗烘套装","PMC7319307310797564936192-100");
            map.put("消毒柜","PMC6720321682709955092480-100");
            map.put("烟机灶具配件","PMC6720321682709955092480-100");
            map.put("烟灶消套装","PMC6720321682709955092480-100");
            map.put("浴霸","PMC6720321682709955092480-100");
            map.put("电暖手宝","PMC7311307310791923597312-100");
            map.put("吊扇","PMC7309307310790610780160-100");
            map.put("对流扇、循环扇","PMC7309307310790610780160-100");
            map.put("发热垫","PMC6720321682709955092480-100");
            map.put("烘鞋器、干鞋器","PMC7320307310798248607744-100");
            map.put("户外灭蚊灯/灭蚊器","PMC7338307310809900384256-100");
            map.put("空调扇","PMC7309307310790610780160-100");
            map.put("冷暖仪","PMC7311307310791923597312-100");
            map.put("灭蚊灯","PMC7338307310809900384256-100");
            map.put("暖被机","PMC6720321682709955092480-100");
            map.put("暖脚器","PMC7311307310791923597312-100");
            map.put("驱虫驱鼠器","PMC6720321682709955092480-100");
            map.put("水暖毯","PMC6720321682709955092480-100");
            map.put("无叶扇","PMC7309307310790610780160-100");
            map.put("电动调奶器","PMC6720321682709955092480-100");
            map.put("吸鼻器","PMC6720321682709955092480-100");
            map.put("便携饮水机","PMC6720321682709955092480-100");
            map.put("超声波清洁机、蒸汽清洁机","PMC6720321682709955092480-100");
            map.put("除醛机","PMC6720321682709955092480-100");
            map.put("电子产品降温器","PMC6720321682709955092480-100");
            map.put("干手机","PMC6720321682709955092480-100");
            map.put("干衣机","PMC7319307310797564936192-100");
            map.put("挂烫机","PMC6720321682709955092480-100");
            map.put("家用除螨机、除螨仪","PMC6720321682709955092480-100");
            map.put("家用垃圾处理机","PMC6720321682709955092480-100");
            map.put("家用扫地机","PMC6720321682709955092480-100");
            map.put("家用消毒棒","PMC6720321682709955092480-100");
            map.put("家用消毒液制造机","PMC6720321682709955092480-100");
            map.put("柔巾机","PMC6720321682709955092480-100");
            map.put("台式净饮机","PMC6720321682709955092480-100");
            map.put("提醒器","PMC6720321682709955092480-100");
            map.put("洗鞋机","PMC6720321682709955092480-100");
            map.put("香薰机","PMC6720321682709955092480-100");
            map.put("衣物护理机","PMC6720321682709955092480-100");
            map.put("饮水机、直饮机","PMC6720321682709955092480-100");
            map.put("蒸汽刷","PMC6720321682709955092480-100");
            map.put("智能空调伴侣","PMC6720321682709955092480-100");
            map.put("智能垃圾桶","PMC6720321682709955092480-100");
            map.put("K歌宝","PMC7592321686363588141056-100");
            map.put("唱戏机","PMC7592321686363588141056-100");
            map.put("电视机顶盒","PMC7592321686363588141056-100");
            map.put("碟机","PMC7592321686363588141056-100");
            map.put("功放机","PMC7592321686363588141056-100");
            map.put("黑胶唱片机","PMC7592321686363588141056-100");
            map.put("扩音器","PMC7592321686363588141056-100");
            map.put("其他音响/设备","PMC7592321686363588141056-100");
            map.put("视频机","PMC7592321686363588141056-100");
            map.put("智能翻译机","PMC7592321686363588141056-100");
            map.put("组合音响","PMC7592321686363588141056-100");
            map.put("智能家居系统","PMC7592321686363588141056-100");
            map.put("蛋托","PMC7448321727707274678272-100");
            map.put("封口夹","PMC7448321727707274678272-100");
            map.put("锅盖架","PMC7448321727707274678272-100");
            map.put("筷笼","PMC7448321727707274678272-100");
            map.put("沥水篮、盆","PMC7448321727707274678272-100");
            map.put("沥水碗架","PMC7448321727707274678272-100");
            map.put("密封盒、储物罐","PMC7448321727707274678272-100");
            map.put("调味架","PMC7448321727707274678272-100");
            map.put("微波炉层架","PMC7448321727707274678272-100");
            map.put("油壶","PMC7448321727707274678272-100");
            map.put("玻璃清洁器","PMC4507321715357725368320-100");
            map.put("除尘掸、桌面扫","PMC4507321715357725368320-100");
            map.put("除尘刷、扫床刷","PMC4507321715357725368320-100");
            map.put("地刮","PMC4507321715357725368320-100");
            map.put("分类垃圾桶","PMC4507321715357725368320-100");
            map.put("家务/地板清洁用具","PMC4507321715357725368320-100");
            map.put("家用盆","PMC4507321715357725368320-100");
            map.put("静电除尘刷","PMC4507321715357725368320-100");
            map.put("垃圾夹","PMC4507321715357725368320-100");
            map.put("垃圾架","PMC4507321715357725368320-100");
            map.put("垃圾桶","PMC4507321715357725368320-100");
            map.put("懒人抹布","PMC4507321715357725368320-100");
            map.put("奶瓶刷/奶嘴刷","PMC4507321715357725368320-100");
            map.put("其他家务清洁用具","PMC4507321715357725368320-100");
            map.put("扫把、簸箕","PMC4507321715357725368320-100");
            map.put("纱窗刷","PMC4507321715357725368320-100");
            map.put("痰盂","PMC4507321715357725368320-100");
            map.put("拖把配件","PMC4507321715357725368320-100");
            map.put("储物挂袋","PMC7039307310613820866560-100");
            map.put("饭盒袋","PMC7039307310613820866560-100");
            map.put("防水袋","PMC7039307310613820866560-100");
            map.put("家用购物车","PMC7039307310613820866560-100");
            map.put("简易衣橱","PMC7039307310613820866560-100");
            map.put("鞋靴罩、袋","PMC7039307310613820866560-100");
            map.put("牙刷套","PMC7039307310613820866560-100");
            map.put("脏衣篮","PMC7039307310613820866560-100");
            map.put("纸巾盒、纸巾抽、卷纸器","PMC7039307310613820866560-100");
            map.put("吹风机架","PMC6788307310428487155712-100");
            map.put("儿童座便器","PMC6788307310428487155712-100");
            map.put("耳勺、洁耳器","PMC6788307310428487155712-100");
            map.put("肥皂盒、架、网","PMC6788307310428487155712-100");
            map.put("挤牙膏器","PMC6808307310440671608832-100");
            map.put("拖把架","PMC6788307310428487155712-100");
            map.put("吸盘肥皂盒","PMC6803307310437551046656-100");
            map.put("吸盘毛巾架","PMC6788307310428487155712-100");
            map.put("吸盘牙刷筒","PMC6788307310428487155712-100");
            map.put("洗发杯/水勺","PMC6788307310428487155712-100");
            map.put("洗漱包、袋","PMC6788307310428487155712-100");
            map.put("浴帘、浴帐","PMC6788307310428487155712-100");
            map.put("浴室凳","PMC6788307310428487155712-100");
            map.put("浴网、浴兜","PMC6788307310428487155712-100");
            map.put("浴罩","PMC6788307310428487155712-100");
            map.put("搓衣板","PMC7016307310594225078272-100");
            map.put("裤架","PMC7004307310585966493696-100");
            map.put("晾晒篮、晾晒绳、晾晒网","PMC7004307310585966493696-100");
            map.put("晾衣杆、衣叉","PMC7004307310585966493696-100");
            map.put("领带架","PMC7004307310585966493696-100");
            map.put("其他衣物晾晒、洗护","PMC7004307310585966493696-100");
            map.put("熨烫板","PMC7009307310589225467904-100");
            map.put("粘毛滚、除毛器","PMC7010307310590118854656-100");
            map.put("叠衣板、架","PMC7039307310613820866560-100");
            map.put("多层置物架","PMC7039307310613820866560-100");
            map.put("墙壁挂架","PMC7039307310613820866560-100");
            map.put("双面贴物器、魔力吸盘","PMC7039307310613820866560-100");
            map.put("吸盘置物架","PMC7039307310613820866560-100");
            map.put("洗衣机架","PMC7039307310613820866560-100");
            map.put("浴室架","PMC7039307310613820866560-100");
            map.put("纸巾架","PMC7039307310613820866560-100");
            map.put("保温桶/保温提锅","PMC7094307310653968744448-100");
            map.put("保鲜盖","PMC7094307310653968744448-100");
            map.put("保鲜盒、饭盒","PMC7094307310653968744448-100");
            map.put("刀叉勺套装","PMC6877307310486548905984-100");
            map.put("定制餐具","PMC7618307310976070320128-100");
            map.put("果盘、果篮","PMC7618307310976070320128-100");
            map.put("酒店摆台","PMC7618307310976070320128-100");
            map.put("酒店餐具","PMC7618307310976070320128-100");
            map.put("可降解餐具/环保餐具","PMC7618307310976070320128-100");
            map.put("可折叠餐具/便携餐具","PMC7618307310976070320128-100");
            map.put("扭曲勺","PMC7618307310976070320128-100");
            map.put("盘","PMC7618307310976070320128-100");
            map.put("其他餐饮用品","PMC7618307310976070320128-100");
            map.put("勺、调羹","PMC7618307310976070320128-100");
            map.put("喂养餐具","PMC7618307310976070320128-100");
            map.put("西餐刀、叉、勺","PMC7618307310976070320128-100");
            map.put("一次性餐盒","PMC7618307310976070320128-100");
            map.put("一次性刀、叉、勺","PMC7618307310976070320128-100");
            map.put("茶宠","PMC6899307310502072025088-100");
            map.put("茶夹","PMC6899307310502072025088-100");
            map.put("茶滤","PMC6899307310502072025088-100");
            map.put("茶盘","PMC6899307310502072025088-100");
            map.put("茶匙","PMC6899307310502072025088-100");
            map.put("茶托","PMC6899307310502072025088-100");
            map.put("茶叶罐","PMC6899307310502072025088-100");
            map.put("冲茶器","PMC6899307310502072025088-100");
            map.put("建盏","PMC6899307310502072025088-100");
            map.put("包饺子用具","PMC7052307310624025608192-100");
            map.put("保鲜袋","PMC7092307310652643344384-100");
            map.put("保鲜膜","PMC7092307310652643344384-100");
            map.put("杯架、酒杯架","PMC7092307310652643344384-100");
            map.put("冰格","PMC7092307310652643344384-100");
            map.put("冰块桶","PMC7092307310652643344384-100");
            map.put("剥壳去核器","PMC7092307310652643344384-100");
            map.put("剥蒜器、蒜泥器","PMC7092307310652643344384-100");
            map.put("防油挡板","PMC7092307310652643344384-100");
            map.put("擀面杖","PMC7092307310652643344384-100");
            map.put("核桃夹","PMC7092307310652643344384-100");
            map.put("黄油盒","PMC7092307310652643344384-100");
            map.put("解冻板","PMC7092307310652643344384-100");
            map.put("酒漏、油漏","PMC7059307310629209767936-100");
            map.put("开橙器剥橙器","PMC7059307310629209767936-100");
            map.put("开瓶器、开罐器","PMC7059307310629209767936-100");
            map.put("烹饪勺铲","PMC6978307310567750631424-100");
            map.put("敲肉锤","PMC7092307310652643344384-100");
            map.put("切蛋器","PMC7082307310647161389056-100");
            map.put("切果器","PMC7059307310629209767936-100");
            map.put("揉面袋","PMC7092307310652643344384-100");
            map.put("揉面垫","PMC7092307310652643344384-100");
            map.put("肉丸制作器","PMC7089307310651301167104-100");
            map.put("食物夹","PMC7092307310652643344384-100");
            map.put("食物研磨器","PMC7092307310652643344384-100");
            map.put("食物罩","PMC7092307310652643344384-100");
            map.put("手动扯蛋器","PMC7092307310652643344384-100");
            map.put("手动灌肠器","PMC7092307310652643344384-100");
            map.put("手动搅拌棒","PMC7092307310652643344384-100");
            map.put("手动开椰器","PMC7092307310652643344384-100");
            map.put("手动榨汁器","PMC7092307310652643344384-100");
            map.put("寿司套件","PMC7092307310652643344384-100");
            map.put("撕肉器","PMC7092307310652643344384-100");
            map.put("松肉针","PMC7092307310652643344384-100");
            map.put("网漏、油格","PMC7092307310652643344384-100");
            map.put("洗米筛","PMC7092307310652643344384-100");
            map.put("虾蟹工具","PMC7092307310652643344384-100");
            map.put("牙签筒","PMC7092307310652643344384-100");
            map.put("一次性家用铝箔","PMC7092307310652643344384-100");
            map.put("猪毛夹","PMC7092307310652643344384-100");
            map.put("蔬果刨丝器","PMC7068307310637464158208-100");
            map.put("锅盖","PMC7101307310658137882624-100");
            map.put("锅盖","PMC7101307310658137882624-100");
            map.put("锅具套装","PMC7101307310658137882624-100");
            map.put("锅具套装","PMC7101307310658137882624-100");
            map.put("火锅","PMC7283307310774198468608-100");
            map.put("火锅","PMC7283307310774198468608-100");
            map.put("煎蛋锅、迷你锅","PMC7101307310658137882624-100");
            map.put("煎蛋锅、迷你锅","PMC7101307310658137882624-100");
            map.put("酒精炉、酒精锅","PMC7101307310658137882624-100");
            map.put("酒精炉、酒精锅","PMC7101307310658137882624-100");
            map.put("卡式炉","PMC7293307310781244899328-100");
            map.put("卡式炉","PMC7293307310781244899328-100");
            map.put("焖烧锅、焖烧壶","PMC7101307310658137882624-100");
            map.put("焖烧锅、焖烧壶","PMC7101307310658137882624-100");
            map.put("奶锅","PMC7101307310658137882624-100");
            map.put("奶锅","PMC7101307310658137882624-100");
            map.put("其他锅具","PMC7101307310658137882624-100");
            map.put("其他锅具","PMC7101307310658137882624-100");
            map.put("汽锅","PMC7101307310658137882624-100");
            map.put("汽锅","PMC7101307310658137882624-100");
            map.put("砂锅/石锅","PMC7101307310658137882624-100");
            map.put("砂锅/石锅","PMC7101307310658137882624-100");
            map.put("汤锅","PMC7101307310658137882624-100");
            map.put("汤锅","PMC7101307310658137882624-100");
            map.put("陶瓷煲","PMC7101307310658137882624-100");
            map.put("陶瓷煲","PMC7101307310658137882624-100");
            map.put("压力锅","PMC7294307310781945348096-100");
            map.put("压力锅","PMC7294307310781945348096-100");
            map.put("野营锅具","PMC7101307310658137882624-100");
            map.put("野营锅具","PMC7101307310658137882624-100");
            map.put("蒸锅","PMC7101307310658137882624-100");
            map.put("蒸锅","PMC7101307310658137882624-100");
            map.put("裱花嘴、裱花袋、裱花枪","PMC7054307310626026291200-100");
            map.put("打蛋盆","PMC7054307310626026291200-100");
            map.put("蛋糕模","PMC7054307310626026291200-100");
            map.put("蛋糕上色喷笔","PMC7054307310626026291200-100");
            map.put("蛋清分离器","PMC7054307310626026291200-100");
            map.put("花嘴转换器","PMC7054307310626026291200-100");
            map.put("手动打蛋器","PMC7054307310626026291200-100");
            map.put("冰酒石","PMC7107307310661971476480-100");
            map.put("冰桶、冰夹","PMC7107307310661971476480-100");
            map.put("酒具套装","PMC7107307310661971476480-100");
            map.put("酒瓶塞","PMC7107307310661971476480-100");
            map.put("酒签","PMC7107307310661971476480-100");
            map.put("酒提","PMC7107307310661971476480-100");
            map.put("滤酒器","PMC7107307310661971476480-100");
            map.put("其他酒具","PMC7107307310661971476480-100");
            map.put("调酒器","PMC7107307310661971476480-100");
            map.put("醒酒器","PMC7107307310661971476480-100");
            map.put("煮酒器","PMC7107307310661971476480-100");
            map.put("奶罐","PMC7110307310663917633536-100");
            map.put("配套器具","PMC7110307310663917633536-100");
            map.put("糖缸","PMC7110307310663917633536-100");
            map.put("焚火台","PMC7062307310632573599744-100");
            map.put("固态酒精","PMC7062307310632573599744-100");
            map.put("烟熏盒、烟熏篮、烟熏管","PMC7062307310632573599744-100");
            map.put("烟熏木屑","PMC7062307310632573599744-100");
            map.put("一次性签子","PMC7062307310632573599744-100");
            map.put("竹签子","PMC7062307310632573599744-100");
            map.put("组合烤具","PMC7062307310632573599744-100");
            map.put("保温壶/瓶","PMC6871307310482539151360-100");
            map.put("旅行壶","PMC6871307310482539151360-100");
            map.put("奶嘴","PMC7617307310975285985280-100");
            map.put("水具套装","PMC6871307310482539151360-100");
            map.put("吸管","PMC6871307310482539151360-100");
            map.put("折叠水袋","PMC7977307311195411447808-100");
            map.put("保暖裤","PMC8253307311361921122304-100");
            map.put("保暖内胆","PMC8253307311361921122304-100");
            map.put("保暖套装","PMC8253307311361921122304-100");
            map.put("保暖一体裤","PMC8253307311361921122304-100");
            map.put("冰丝袖套","PMC8253307311361921122304-100");
            map.put("裹胸、抹胸","PMC8253307311361921122304-100");
            map.put("男士阿罗裤","PMC7449307310876824698880-100");
            map.put("男士丁字裤","PMC7449307310876824698880-100");
            map.put("男士平角裤/平脚裤","PMC7449307310876824698880-100");
            map.put("男士三角裤","PMC7449307310876824698880-100");
            map.put("女士丁字裤","PMC8258307311365289148416-100");
            map.put("女士平角裤/平脚裤","PMC8258307311365289148416-100");
            map.put("女士三角裤","PMC8258307311365289148416-100");
            map.put("女士生理裤","PMC8258307311365289148416-100");
            map.put("其他内衣","PMC8253307311361921122304-100");
            map.put("情趣丝袜","PMC8260307311366509690880-100");
            map.put("情趣套装","PMC8260307311366509690880-100");
            map.put("晨袍","PMC8280307311378329239552-100");
            map.put("家居裤、睡裤","PMC8284307311381084897280-100");
            map.put("男士家居服","PMC7465307310886907805696-100");
            map.put("女士家居服","PMC8284307311381084897280-100");
            map.put("情侣家居服","PMC7465307310886907805696-100");
            map.put("桑拿服、汗蒸服","PMC6162307310030741307392-100");
            map.put("月子服/哺乳装","PMC8284307311381084897280-100");
            map.put("丝袜","PMC8287307311382397714432-100");
            map.put("束缚带/塑体衣/矫正带","PMC8261307311367147225088-100");
            map.put("塑身分体套装","PMC8261307311367147225088-100");
            map.put("塑身裤","PMC8261307311367147225088-100");
            map.put("JK袜","PMC8287307311382397714432-100");
            map.put("半掌袜","PMC8287307311382397714432-100");
            map.put("船袜、隐形袜","PMC8287307311382397714432-100");
            map.put("地板袜","PMC8287307311382397714432-100");
            map.put("堆堆袜","PMC8287307311382397714432-100");
            map.put("分趾袜","PMC8287307311382397714432-100");
            map.put("洛丽塔袜","PMC8287307311382397714432-100");
            map.put("美腿袜、压力袜","PMC8287307311382397714432-100");
            map.put("商务袜","PMC8287307311382397714432-100");
            map.put("袜套","PMC8287307311382397714432-100");
            map.put("鱼嘴袜、露趾袜","PMC8287307311382397714432-100");
            map.put("运动、休闲棉袜","PMC8287307311382397714432-100");
            map.put("吊袜带","PMC8253307311361921122304-100");
            map.put("乳头保护罩","PMC8253307311361921122304-100");
            map.put("胸垫、插片","PMC8253307311361921122304-100");
            map.put("胸贴、乳贴","PMC8253307311361921122304-100");
            map.put("大码上装","PMC6162307310030741307392-100");
            map.put("动漫二次元卫衣","PMC6162307310030741307392-100");
            map.put("广告衫棒球服","PMC7451307310878053629952-100");
            map.put("广告衫风衣","PMC7451307310878053629952-100");
            map.put("广告衫卫衣","PMC7451307310878053629952-100");
            map.put("男式cosplay表演服","PMC6162307310030741307392-100");
            map.put("男式PU皮衣","PMC7451307310878053629952-100");
            map.put("男式汉服","PMC6162307310030741307392-100");
            map.put("男式毛衫假两件","PMC7474307310892020662272-100");
            map.put("男式皮草","PMC6162307310030741307392-100");
            map.put("男式商务西服","PMC7471307310890766565376-100");
            map.put("男式唐装","PMC6162307310030741307392-100");
            map.put("男式羊毛衫","PMC7474307310892020662272-100");
            map.put("男式羊绒衫","PMC7474307310892020662272-100");
            map.put("男式真皮皮衣","PMC7451307310878053629952-100");
            map.put("男式中老年上装","PMC7451307310878053629952-100");
            map.put("其它广告衫","PMC6162307310030741307392-100");
            map.put("中山装","PMC7482307310896613425152-100");
            map.put("防晒服、皮肤衣","PMC6162307310030741307392-100");
            map.put("工装、制服","PMC7455307310880247250944-100");
            map.put("男式Polo衫","PMC7476307310893434142720-100");
            map.put("男式T恤","PMC7480307310895384494080-100");
            map.put("男式棒球服","PMC7451307310878053629952-100");
            map.put("男式背心","PMC7478307310894793097216-100");
            map.put("男式衬衫","PMC7477307310894201700352-100");
            map.put("男式大衣","PMC7451307310878053629952-100");
            map.put("男式风衣","PMC7451307310878053629952-100");
            map.put("男式夹克","PMC7451307310878053629952-100");
            map.put("男式棉服","PMC7474307310892020662272-100");
            map.put("男式棉裤","PMC7474307310892020662272-100");
            map.put("男式牛仔裤","PMC7454307310879597133824-100");
            map.put("男式皮裤","PMC7457307310882143076352-100");
            map.put("男式沙滩裤","PMC7460307310883590111232-100");
            map.put("男式卫裤","PMC7457307310882143076352-100");
            map.put("男式卫衣","PMC7451307310878053629952-100");
            map.put("男式西裤","PMC7471307310890766565376-100");
            map.put("男式休闲裤","PMC7457307310882143076352-100");
            map.put("男式羽绒服","PMC6162307310030741307392-100");
            map.put("男式羽绒裤","PMC6162307310030741307392-100");
            map.put("中山装、唐装、民族服装","PMC7482307310896613425152-100");
            map.put("圣诞装饰品","PMC7135307310680451579904-200");
            map.put("节日用品礼盒","PMC7135307310680451579904-200");
            map.put("气球","PMC7128307310674520834048-200");
            map.put("喷雪、喷彩带","PMC7139307310683890909184-200");
            map.put("拉花、彩带、彩条","PMC7139307310683890909184-200");
            map.put("消毒喷雾器","PMC7139307310683890909184-200");
            map.put("高压喷雾桩","PMC7139307310683890909184-200");
            map.put("恒温杯垫","PMC6895307310499475750912-40");
            map.put("餐垫、餐巾","PMC6895307310499475750912-40");
            map.put("骑行眼镜、风镜","PMC6887307310493066854400-40");
            map.put("驾驶眼镜","PMC6887307310493066854400-40");
            map.put("军迷、防弹眼镜","PMC6887307310493066854400-40");
            map.put("钓鱼眼镜","PMC6887307310493066854400-40");
            map.put("框架眼镜","PMC6887307310493066854400-40");
            map.put("光学眼镜","PMC6887307310493066854400-40");
            map.put("派对眼镜","PMC6887307310493066854400-40");
            map.put("3D眼镜","PMC6887307310493066854400-40");
            map.put("防辐射眼镜","PMC6887307310493066854400-40");
            map.put("其他眼镜","PMC6887307310493066854400-40");
            map.put("数码眼镜","PMC6887307310493066854400-40");
            map.put("3D数码眼镜","PMC6887307310493066854400-40");
            map.put("防化学眼镜","PMC6887307310493066854400-40");
            map.put("防光辐射眼镜","PMC6887307310493066854400-40");
            map.put("防雾眼镜","PMC6887307310493066854400-40");
            map.put("防紫外线眼镜","PMC6887307310493066854400-40");
            map.put("防尘眼镜","PMC6887307310493066854400-40");
            map.put("防冲击眼镜","PMC6887307310493066854400-40");
            map.put("马克杯","PMC6872307310483457703936-40");
            map.put("水果玉米","PMC7085307310648545509376-40");
            map.put("其他水果","PMC7085307310648545509376-40");
            map.put("水果果干制品","PMC7085307310648545509376-40");
            map.put("水果叉","PMC7085307310648545509376-40");
            map.put("筷子","PMC6876307310485856845824-40");
            map.put("一次性筷子","PMC6876307310485856845824-40");
            map.put("碗","PMC6875307310484980236288-40");
            map.put("烤碗","PMC6875307310484980236288-40");
            map.put("盖碗","PMC6875307310484980236288-40");
            map.put("纸碗","PMC6875307310484980236288-40");
            map.put("西餐餐具套装","PMC6893307310497294712832-40");
            map.put("玻璃餐具套装","PMC6893307310497294712832-40");
            map.put("陶瓷餐具套装","PMC6893307310497294712832-40");
            map.put("刀具套装","PMC6878307310487253549056-40");
            map.put("酒吧台","PMC7107307310661971476480-40");
            map.put("酒吧椅","PMC7107307310661971476480-40");
            map.put("酒吧桌","PMC7107307310661971476480-40");
            map.put("酒吧装饰摆件","PMC7107307310661971476480-40");
            map.put("酒架","PMC7064307310634385539072-40");
            map.put("碗碟盘套装","PMC6871307310482539151360-40");
            map.put("餐垫、杯垫","PMC6871307310482539151360-40");
            map.put("碟","PMC6871307310482539151360-40");
            map.put("一次性碗、碟、盘","PMC6871307310482539151360-40");
            map.put("烘焙量勺量杯","PMC6871307310482539151360-40");
            map.put("其他杯子","PMC6871307310482539151360-40");
            map.put("保温杯","PMC6871307310482539151360-40");
            map.put("玻璃杯","PMC6871307310482539151360-40");
            map.put("陶瓷杯","PMC6871307310482539151360-40");
            map.put("塑料杯","PMC6871307310482539151360-40");
            map.put("杯盖","PMC6871307310482539151360-40");
            map.put("杯套","PMC6871307310482539151360-40");
            map.put("吸管杯","PMC6871307310482539151360-40");
            map.put("水素杯","PMC6871307310482539151360-40");
            map.put("手动榨汁杯","PMC6871307310482539151360-40");
            map.put("太空杯","PMC6871307310482539151360-40");
            map.put("隔热杯","PMC6871307310482539151360-40");
            map.put("随手杯","PMC6871307310482539151360-40");
            map.put("杯子套装/情侣对杯","PMC6871307310482539151360-40");
            map.put("防漏杯/密封杯","PMC6871307310482539151360-40");
            map.put("焖烧杯","PMC6871307310482539151360-40");
            map.put("喷雾水杯","PMC6871307310482539151360-40");
            map.put("摇摇杯","PMC6871307310482539151360-40");
            map.put("漱口杯","PMC6871307310482539151360-40");
            map.put("一次性塑杯","PMC6871307310482539151360-40");
            map.put("一次性杯垫、盖","PMC6871307310482539151360-40");
            map.put("一次性纸杯","PMC6871307310482539151360-40");
            map.put("婴幼儿学饮杯","PMC6871307310482539151360-40");
            map.put("茶杯","PMC6871307310482539151360-40");
            map.put("飘逸杯","PMC6871307310482539151360-40");
            map.put("茶海/公道杯","PMC6871307310482539151360-40");
            map.put("闻香杯","PMC6871307310482539151360-40");
            map.put("防烫碗碟夹","PMC6871307310482539151360-40");
            map.put("酒杯","PMC6871307310482539151360-40");
            map.put("拉花杯","PMC6871307310482539151360-40");
            map.put("咖啡杯","PMC6871307310482539151360-40");
            map.put("智能降温保暖杯","PMC6871307310482539151360-40");
            map.put("电热杯","PMC6871307310482539151360-40");
            map.put("恒温杯垫","PMC6871307310482539151360-40");
            map.put("电热杯","PMC6871307310482539151360-40");
            map.put("咖啡壶","PMC7109307310663233961984-40");
            map.put("咖啡勺","PMC7110307310663917633536-40");
            map.put("咖啡具套装","PMC7110307310663917633536-40");
            map.put("咖啡滤纸","PMC7110307310663917633536-40");
            map.put("模具钢","PMC7052307310624025608192-40");
            map.put("蛋挞模具","PMC7052307310624025608192-40");
            map.put("甜甜圈模具","PMC7052307310624025608192-40");
            map.put("月饼模具","PMC7052307310624025608192-40");
            map.put("饼干模具","PMC7052307310624025608192-40");
            map.put("香肠模具","PMC7052307310624025608192-40");
            map.put("雪花酥模具","PMC7052307310624025608192-40");
            map.put("巧克力模具","PMC7052307310624025608192-40");
            map.put("面包模具","PMC7052307310624025608192-40");
            map.put("雪糕模具","PMC7052307310624025608192-40");
            map.put("阿胶糕模具","PMC7052307310624025608192-40");
            map.put("煎蛋模具","PMC7052307310624025608192-40");
            map.put("饭团模具","PMC7052307310624025608192-40");
            map.put("模具制造","PMC7052307310624025608192-40");
            map.put("快速经济模具","PMC7052307310624025608192-40");
            map.put("模具标准件","PMC7052307310624025608192-40");
            map.put("模具设备","PMC7052307310624025608192-40");
            map.put("其他模具","PMC7052307310624025608192-40");
            map.put("铸造模具","PMC7052307310624025608192-40");
            map.put("测量量具辅件","PMC7075307310642413436928-40");
            map.put("厨房定时器","PMC7088307310650596524032-40");
            map.put("猪肉类","PMC7089307310651301167104-40");
            map.put("羊肉类","PMC7089307310651301167104-40");
            map.put("牛肉类","PMC7089307310651301167104-40");
            map.put("鸡肉类","PMC7089307310651301167104-40");
            map.put("鸭肉类","PMC7089307310651301167104-40");
            map.put("鹅肉类","PMC7089307310651301167104-40");
            map.put("肉类罐头","PMC7089307310651301167104-40");
            map.put("速冻肉类丸饺类","PMC7089307310651301167104-40");
            map.put("其他肉类零食","PMC7089307310651301167104-40");
            map.put("屠宰及肉类初加工设备","PMC7089307310651301167104-40");
            map.put("其他厨卫清洁剂","PMC7092307310652643344384-40");
            map.put("其他厨房小工具","PMC7092307310652643344384-40");
            map.put("其他哺育喂养用品","PMC7092307310652643344384-40");
            map.put("其他刀剪具","PMC7092307310652643344384-40");
            map.put("其他厨房整理用品","PMC7092307310652643344384-40");
            map.put("不锈钢厨用刀","PMC7068307310637464158208-40");
            map.put("陶瓷厨用刀","PMC7068307310637464158208-40");
            map.put("家用菜刀","PMC7068307310637464158208-40");
            map.put("砍骨刀","PMC7068307310637464158208-40");
            map.put("屠刀","PMC7068307310637464158208-40");
            map.put("刺身刀","PMC7068307310637464158208-40");
            map.put("面包刀","PMC7068307310637464158208-40");
            map.put("奶酪刀","PMC7068307310637464158208-40");
            map.put("水果刀","PMC7068307310637464158208-40");
            map.put("西餐刀","PMC7068307310637464158208-40");
            map.put("厨师刀","PMC7068307310637464158208-40");
            map.put("酒店用刀","PMC7068307310637464158208-40");
            map.put("刀架","PMC7068307310637464158208-40");
            map.put("冷冻料理刀","PMC7068307310637464158208-40");
            map.put("牛扒刀","PMC7068307310637464158208-40");
            map.put("砧板、菜板","PMC7067307310636503662592-40");
            map.put("烘焙转台","PMC7054307310626026291200-40");
            map.put("烘焙套装","PMC7054307310626026291200-40");
            map.put("其他烘焙用具","PMC7054307310626026291200-40");
            map.put("烘焙用纸","PMC7054307310626026291200-40");
            map.put("烘焙烤盘","PMC7054307310626026291200-40");
            map.put("烘焙刮刀","PMC7054307310626026291200-40");
            map.put("烘焙铲","PMC7054307310626026291200-40");
            map.put("烤垫/烘焙垫","PMC7054307310626026291200-40");
            map.put("婴童抱毯、毛毯","PMC6814307310444136103936-40");
            map.put("婚庆毛毯","PMC6814307310444136103936-40");
            map.put("羊毛毯、羊绒毯","PMC6814307310444136103936-40");
            map.put("其他毛毯、绒毯","PMC6814307310444136103936-40");
            map.put("枕套","PMC6812307310442747789312-40");
            map.put("抱枕套","PMC6812307310442747789312-40");
            map.put("酒店椅套","PMC6849307310467854893056-40");
            map.put("餐桌布艺/桌布/椅套","PMC6849307310467854893056-40");
            map.put("一次性桌布","PMC6863307310476939755520-40");
            map.put("酒店桌布、台布、桌裙","PMC6863307310476939755520-40");
            map.put("汽车地毯","PMC6842307310463522177024-40");
            map.put("家用地毯","PMC6842307310463522177024-40");
            map.put("商用地毯","PMC6842307310463522177024-40");
            map.put("缝纫DIY工具套装","PMC7350307310817387216896-40");
            map.put("教学时钟","PMC6844307310464902103040-200");
            map.put("数码相框","PMC6862307310475945705472-200");
            map.put("相框、画框","PMC6862307310475945705472-200");
            map.put("门窗挂钩","PMC7035307310610461229056-200");
            map.put("口罩挂钩","PMC7035307310610461229056-200");
            map.put("门后挂钩","PMC7035307310610461229056-200");
            map.put("衣钩、挂钩、粘钩、钩子","PMC7035307310610461229056-200");
            map.put("羊眼挂钩","PMC7035307310610461229056-200");
            map.put("垃圾袋","PMC7019307310598822035456-200");
            map.put("大型垃圾箱","PMC7024307310601967763456-200");
            map.put("马桶刷架、杯","PMC6792307310430395564032-200");
            map.put("马桶刷","PMC6792307310430395564032-200");
            map.put("一次性马桶刷","PMC6792307310430395564032-200");
            map.put("智能马桶盖","PMC6806307310438775783424-200");
            map.put("普通马桶盖","PMC6806307310438775783424-200");
            map.put("皂液器、给皂器","PMC6803307310437551046656-200");
            map.put("电动皂液器","PMC6803307310437551046656-200");
            map.put("烘焙刮板","PMC7013307310592505413632-200");
            map.put("拖把、拖布桶","PMC7000307310583676403712-200");
            map.put("平板拖把","PMC7000307310583676403712-200");
            map.put("刮刮乐拖把","PMC7000307310583676403712-200");
            map.put("静电除尘拖把","PMC7000307310583676403712-200");
            map.put("汽车拖把","PMC7000307310583676403712-200");
            map.put("蒸汽拖把","PMC7000307310583676403712-200");
            map.put("家务围裙、袖套","PMC7048307310619894218752-200");
            map.put("一次性围裙、袖套","PMC7048307310619894218752-200");
            map.put("劳保围裙","PMC7048307310619894218752-200");
            map.put("牙刷架、牙具座","PMC6808307310440671608832-200");
            map.put("足浴盆","PMC6793307310431280562176-200");
            map.put("防水、防潮材料","PMC7011307310590911578112-200");
            map.put("防蚊、防蛀、灭鼠杀虫用品","PMC7011307310590911578112-200");
            map.put("防潮盒","PMC7011307310590911578112-200");
            map.put("防油、防潮纸","PMC7011307310590911578112-200");
            map.put("摄影防潮箱/包","PMC7011307310590911578112-200");
            map.put("防霉漆","PMC7011307310590911578112-200");
            map.put("防霉剂、抗菌剂","PMC7011307310590911578112-200");
            map.put("甲醛清除剂","PMC7006307310587564523520-200");
            map.put("香氛喷雾","PMC7026307310603574181888-200");
            map.put("私处香氛","PMC7026307310603574181888-200");
            map.put("防雨布","PMC7175307310708503085056-200");
            map.put("雨伞","PMC7948307311177673736192-200");
            map.put("晴雨伞","PMC7948307311177673736192-200");
            map.put("洗衣袋、护洗袋","PMC7037307310612134756352-200");
            map.put("组合衣架","PMC7030307310606615052288-200");
            map.put("衣架","PMC7030307310606615052288-200");
            map.put("展示衣架","PMC7030307310606615052288-200");
            map.put("整熨洗涤设备配件","PMC7009307310589225467904-200");
            map.put("升降晾衣架","PMC7004307310585966493696-200");
            map.put("晾衣架、晾晒架","PMC7004307310585966493696-200");
            map.put("多头晾衣架","PMC7004307310585966493696-200");
            map.put("水垢清洁剂","PMC7007307310588353052672-200");
            map.put("油污清洁剂","PMC7007307310588353052672-200");
            map.put("玻璃清洁剂","PMC7007307310588353052672-200");
            map.put("多用途清洁剂","PMC7007307310588353052672-200");
            map.put("洗衣机槽清洁剂","PMC7007307310588353052672-200");
            map.put("卫浴清洁剂","PMC7007307310588353052672-200");
            map.put("坐便清洁剂","PMC7007307310588353052672-200");
            map.put("布艺用品清洁剂","PMC7007307310588353052672-200");
            map.put("空调清洁剂","PMC7007307310588353052672-200");
            map.put("墙面清洁剂","PMC7007307310588353052672-200");
            map.put("地板清洁剂","PMC7007307310588353052672-200");
            map.put("地毯清洁剂","PMC7007307310588353052672-200");
            map.put("卫生纸/卷纸","PMC7022307310601154068480-200");
            map.put("电视","PMC7360307310823326351360-40");
            map.put("电视机配件、附件","PMC7360307310823326351360-40");
            map.put("3D电视","PMC7360307310823326351360-40");
            map.put("AR电视","PMC7360307310823326351360-40");
            map.put("液晶电视","PMC7360307310823326351360-40");
            map.put("激光电视","PMC7360307310823326351360-40");
            map.put("等离子电视","PMC7360307310823326351360-40");
            map.put("触控电视","PMC7360307310823326351360-40");
            map.put("车用氧吧/空气净化器","PMC7327307310802442911744-40");
            map.put("空气净化器","PMC7327307310802442911744-40");
            map.put("空气净化器配件","PMC7327307310802442911744-40");
            map.put("工业空气净化器","PMC7327307310802442911744-40");
            map.put("加湿器","PMC7027307310604937330688-40");
            map.put("超声波式加湿器","PMC7027307310604937330688-40");
            map.put("蒸发式加湿器","PMC7027307310604937330688-40");
            map.put("电热式加湿器","PMC7027307310604937330688-40");
            map.put("蒸汽型加湿器","PMC7027307310604937330688-40");
            map.put("复合式加湿器","PMC7027307310604937330688-40");
            map.put("高压微雾加湿器","PMC7027307310604937330688-40");
            map.put("其他式加湿器","PMC7027307310604937330688-40");
            map.put("加湿器配件","PMC7027307310604937330688-40");
            map.put("加热器","PMC7311307310791923597312-40");
            map.put("电熨斗","PMC7331307310805345370112-40");
            map.put("家用吸尘器","PMC7358307310821938036736-40");
            map.put("便携、手持式吸尘器","PMC7358307310821938036736-40");
            map.put("商用吸尘器","PMC7358307310821938036736-40");
            map.put("方便粉丝类","PMC7309307310790610780160-40");
            map.put("粉丝、粉皮","PMC7309307310790610780160-40");
            map.put("小家电玩具","PMC7342307310811909455872-40");
            map.put("商用除湿机","PMC7335307310807627071488-40");
            map.put("家用抽湿机、除湿机","PMC7335307310807627071488-40");
            map.put("除湿机","PMC7335307310807627071488-40");
            map.put("儿童牛仔衬衫","PMC7477307310894201700352-200");
            map.put("保暖衬衫","PMC7477307310894201700352-200");
            map.put("男式西服套装","PMC7470307310889957064704-200");
            map.put("广告衫外套","PMC7470307310889957064704-200");
            map.put("男式大码套装","PMC7470307310889957064704-200");
            map.put("男式中老年套装","PMC7470307310889957064704-200");
            map.put("男式休闲西装","PMC7471307310890766565376-200");
            map.put("男式加绒休闲裤","PMC7457307310882143076352-200");
            map.put("男式加绒卫裤","PMC7457307310882143076352-200");
            map.put("男式加绒牛仔裤","PMC7457307310882143076352-200");
            map.put("男式针织衫","PMC7474307310892020662272-20");
            map.put("广告衫T恤","PMC7480307310895384494080-200");
            map.put("男式动漫二次元T恤","PMC7480307310895384494080-200");
            map.put("速干t恤","PMC7480307310895384494080-200");
            map.put("内穿t恤","PMC7480307310895384494080-200");
            map.put("男式休闲短裤","PMC8258307311365289148416-200");
            map.put("男式牛仔短裤","PMC8258307311365289148416-200");
            map.put("情趣文胸","PMC8254307311362705457152-200");
            map.put("鱼骨胸衣、马甲式文胸","PMC8254307311362705457152-200");
            map.put("运动文胸","PMC8254307311362705457152-200");
            map.put("法式文胸","PMC8254307311362705457152-200");
            map.put("软支撑文胸","PMC8254307311362705457152-200");
            map.put("背心式文胸","PMC8254307311362705457152-200");
            map.put("美背文胸","PMC8254307311362705457152-200");
            map.put("大码文胸","PMC8254307311362705457152-200");
            map.put("少女文胸","PMC8254307311362705457152-200");
            map.put("隐形文胸","PMC8254307311362705457152-200");
            map.put("哺乳文胸","PMC8254307311362705457152-200");
            map.put("调整型文胸","PMC8254307311362705457152-200");
            map.put("中老年文胸","PMC8254307311362705457152-200");
            map.put("无痕文胸","PMC8254307311362705457152-200");
            map.put("文胸套装","PMC8254307311362705457152-200");
            map.put("无钢圈文胸","PMC8254307311362705457152-200");
            map.put("孕妇裙","PMC8277307311377062559744-200");
            map.put("大码半身裙","PMC8277307311377062559744-200");
            map.put("大码衬衫","PMC8293307311386038370304-200");
            map.put("大码外套","PMC8249307311359832358912-200");
            map.put("大码毛呢外套","PMC8249307311359832358912-200");
            map.put("大码夹克/棒球服","PMC8249307311359832358912-200");
            map.put("大码牛仔裤","PMC8250307311360516030464-20");
            map.put("羽绒裤/棉裤","PMC8267307311370775298048-200");
            map.put("休闲裤","PMC8267307311370775298048-200");
            map.put("羽绒裤/棉裤","PMC8267307311370775298048-200");
            map.put("休闲裤","PMC8267307311370775298048-200");
            map.put("大码打底裤","PMC8267307311370775298048-200");
            map.put("大码休闲裤","PMC8267307311370775298048-200");
            map.put("大码其他女式裤装","PMC8267307311370775298048-200");
            map.put("打底裤","PMC8267307311370775298048-200");
            map.put("一体裤","PMC8267307311370775298048-200");
            map.put("职业女裤套装","PMC8267307311370775298048-200");
            map.put("女士背心","PMC8296307311388034859008-200");
            map.put("大码针织衫","PMC8288307311383123329024-20");
            map.put("大码时尚休闲套装","PMC8275307311375779102720-200");
            map.put("职业女裙套装","PMC8275307311375779102720-200");
            map.put("女士打底套装","PMC8275307311375779102720-200");
            map.put("西装裤/正装裤","PMC8248307311359027052544-200");
            map.put("大码西装","PMC8248307311359027052544-200");
            map.put("大码T恤","PMC8297307311388735307776-200");
            map.put("围巾","PMC7380307310834659360768-40");
            map.put("围巾/丝巾/披肩 配件","PMC7380307310834659360768-40");
            map.put("运动紧身裤","PMC7393307310842515292160-40");
            map.put("运动紧身衣","PMC7393307310842515292160-40");
            map.put("孕妇上衣","PMC7417307310857245687808-40");
            map.put("塑身上衣","PMC7417307310857245687808-40");
            map.put("保暖上衣","PMC7417307310857245687808-40");
            map.put("男式羽绒马甲","PMC7389307310840493637632-40");
            map.put("男式棉服马甲","PMC7389307310840493637632-40");
            map.put("男式休闲马甲","PMC7389307310840493637632-40");
            map.put("男式西装马甲","PMC7389307310840493637632-40");
            map.put("广告衫马甲","PMC7389307310840493637632-40");
            map.put("大码马甲","PMC7389307310840493637632-40");
            map.put("运动马甲","PMC7389307310840493637632-40");
            map.put("羽绒马甲","PMC7389307310840493637632-40");
            map.put("保暖马甲、保暖背心","PMC7389307310840493637632-40");
            map.put("工装马甲","PMC7389307310840493637632-40");
            map.put("夹克/皮衣","PMC7385307310837486321664-40");
            map.put("大衣、西服罩","PMC7384307310836819427328-40");
            map.put("呢大衣","PMC7384307310836819427328-40");
            map.put("鞋垫/增高垫","PMC8323307311405118259200-200");
            map.put("鞋跟","PMC8323307311405118259200-200");
            map.put("减震、充气鞋垫","PMC8323307311405118259200-200");
            map.put("平底拖鞋","PMC8311307311398105382912-200");
            map.put("高跟拖鞋","PMC8311307311398105382912-200");
            map.put("厚底拖鞋","PMC8311307311398105382912-200");
            map.put("坡跟拖鞋","PMC8311307311398105382912-200");
            map.put("男士钱包","PMC7442307310872361959424-200");
            map.put("零钱包","PMC7442307310872361959424-200");
            map.put("儿童零钱包","PMC7442307310872361959424-200");
            map.put("智能钱包","PMC7442307310872361959424-200");
            map.put("女士单肩包","PMC8213307311338298802176-20");
            map.put("女士斜挎包","PMC8213307311338298802176-20");
            map.put("女士单肩包","PMC8213307311338298802176-20");
            map.put("女士斜挎包","PMC8213307311338298802176-20");
            map.put("化妆包","PMC8217307311340601475072-200");
            map.put("女士钱包","PMC8226307311345533976576-200");
            map.put("战术双肩背包","PMC7432307310865965645824-20");
            map.put("运动背包","PMC7432307310865965645824-20");
            map.put("防走失背包","PMC7432307310865965645824-20");
            map.put("骑行背包","PMC7432307310865965645824-20");
            map.put("吉他背包/琴盒","PMC7432307310865965645824-20");
            map.put("箱包锁","PMC8187307311322876346368-200");
            map.put("手膜","PMC6222307310068699758592-200");
            map.put("足膜","PMC6222307310068699758592-200");
            map.put("男士洗发水","PMC6218307310065902157824-200");
            map.put("其他女性护理","PMC6661307310352142434304-200");
            map.put("卫生巾","PMC6665307310354294112256-200");
            map.put("裤型卫生巾","PMC6665307310354294112256-200");
            map.put("液体卫生巾","PMC6665307310354294112256-200");
            map.put("产妇卫生巾","PMC6665307310354294112256-200");
            map.put("护手霜","PMC6224307310069488287744-200");
            map.put("面霜","PMC6224307310069488287744-200");
            map.put("儿童乳液/面霜","PMC6224307310069488287744-200");
            map.put("身体护理套装","PMC6184307310044850946048-200");
            map.put("润肤乳液","PMC6167307310034159665152-200");
            map.put("男士乳液","PMC6167307310034159665152-200");
            map.put("香皂、皂花","PMC6178307310040774082560-200");
            map.put("香皂花","PMC6178307310040774082560-200");
            map.put("滑石粉","PMC6177307310040098799616-200");
            map.put("滑石粉","PMC6177307310040098799616-200");
            map.put("摩丝、啫喱水、发蜡、弹力素","PMC6181307310043169030144-200");
            map.put("香片蜡片","PMC6181307310043169030144-200");
            map.put("地板蜡","PMC6181307310043169030144-200");
            map.put("脱毛蜡纸/脱毛膏","PMC6181307310043169030144-200");
            map.put("身体磨砂、去角质","PMC6175307310039301881856-200");
            map.put("手部磨砂、去角质","PMC6175307310039301881856-200");
            map.put("足部磨砂、去角质","PMC6175307310039301881856-200");
            map.put("面部磨砂、去角质","PMC6175307310039301881856-200");
            map.put("染发剂","PMC6211307310061628162048-200");
            map.put("化妆工具套装","PMC6254307310087825784832-200");
            map.put("口红收纳架","PMC6253307310087028867072-200");
            map.put("口红收纳盒","PMC6253307310087028867072-200");
            map.put("口红收纳包","PMC6253307310087028867072-200");
            map.put("口红","PMC6253307310087028867072-200");
            map.put("唇彩","PMC6253307310087028867072-200");
            map.put("化妆箱","PMC6200307310055043104768-200");
            map.put("化妆镜","PMC6200307310055043104768-200");
            map.put("化妆品收纳盒","PMC6200307310055043104768-200");
            map.put("化妆镜灯泡","PMC6200307310055043104768-200");
            map.put("化妆品冰箱","PMC6200307310055043104768-200");
            map.put("化妆服饰道具","PMC6200307310055043104768-200");
            map.put("化妆棉、棉签","PMC6200307310055043104768-200");
            map.put("化妆刷、刷包","PMC6200307310055043104768-200");
            map.put("定妆喷雾","PMC6249307310085007212544-200");
            map.put("粉底","PMC6247307310083539206144-200");
            map.put("荧光笔","PMC6246307310082796814336-200");
            map.put("底漆","PMC6248307310084281597952-200");
            map.put("粉末冶金","PMC6250307310085695078400-200");
            map.put("粉末冶金模","PMC6250307310085695078400-200");
            map.put("粉末涂料","PMC6250307310085695078400-200");
            map.put("其他金属粉末","PMC6250307310085695078400-200");
            map.put("非金属粉末","PMC6250307310085695078400-200");
            map.put("腮红、胭脂","PMC6245307310082100559872-200");
            map.put("儿童彩妆","PMC6260307310091361583104-200");
            map.put("彩妆套装、彩妆盘","PMC6260307310091361583104-200");
            map.put("其他彩妆","PMC6260307310091361583104-200");
            map.put("男士彩妆","PMC6260307310091361583104-200");
            map.put("睫毛膏","PMC6241307310080032768000-200");
            map.put("眉笔、眉粉、眉膏","PMC6236307310077390356480-200");
            map.put("唇笔、唇线笔","PMC6238307310078124359680-200");
            map.put("眼影","PMC6239307310078740922368-200");
            map.put("指甲油、护甲油","PMC6229307310073019891712-200");
            map.put("其他唇部护理产品","PMC6289307310111380996096-200");
            map.put("唇部护理套装","PMC6289307310111380996096-200");
            map.put("唇部护理加工定制","PMC6289307310111380996096-200");
            map.put("沙滩防晒服","PMC6281307310105815154688-200");
            map.put("防晒袖套","PMC6281307310105815154688-200");
            map.put("宝宝防晒乳/露","PMC6281307310105815154688-200");
            map.put("化妆水、爽肤水","PMC6295307310115378167808-200");
            map.put("男士爽肤水","PMC6295307310115378167808-200");
            map.put("儿童洗面奶","PMC6282307310106624655360-200");
            map.put("面部按摩霜、乳","PMC6285307310109166403584-200");
            map.put("宝宝洗浴护肤品","PMC6292307310113734000640-200");
            map.put("儿童洗浴护肤礼盒","PMC6292307310113734000640-200");
            map.put("头发免洗喷雾","PMC6284307310107534819328-200");
            map.put("脱毛喷雾/泡沫","PMC6284307310107534819328-200");
            map.put("驱蚊喷雾/走珠","PMC6284307310107534819328-200");
            map.put("一次性口罩垫","PMC6279307310102958833664-200");
            map.put("口罩机","PMC6279307310102958833664-200");
            map.put("口罩垫片","PMC6279307310102958833664-200");
            map.put("口罩鼻梁条","PMC6279307310102958833664-200");
            map.put("口罩呼吸阀","PMC6279307310102958833664-200");
            map.put("卸妆水/乳/油","PMC6256307310089151184896-200");
            map.put("卸妆巾、卸妆湿巾","PMC6256307310089151184896-200");
            map.put("唇部精华、啫喱","PMC6287307310110646992896-200");
            map.put("男士面部精华","PMC6287307310110646992896-200");
            map.put("面部精华加工定制","PMC6287307310110646992896-200");
            map.put("眼部精华","PMC6287307310110646992896-200");
            map.put("液态精华","PMC6287307310110646992896-200");
            map.put("精华油","PMC6287307310110646992896-200");
            map.put("粉状精华","PMC6287307310110646992896-200");
            map.put("剃须膏、须后水","PMC6270307310098177327104-200");
            map.put("眼部护理加工定制","PMC6277307310102233219072-200");
            map.put("眼部护理套装","PMC6277307310102233219072-200");
            map.put("车用按摩器材","PMC6206307310058562125824-200");
            map.put("按摩床","PMC6206307310058562125824-200");
            map.put("按摩床","PMC6206307310058562125824-200");
            map.put("前列腺按摩器","PMC6206307310058562125824-200");
            map.put("USB按摩器","PMC6206307310058562125824-200");
            map.put("其他按摩器材","PMC6206307310058562125824-200");
            map.put("按摩腰带","PMC6206307310058562125824-200");
            map.put("头部按摩机","PMC6206307310058562125824-200");
            map.put("MINI按摩器","PMC6206307310058562125824-200");
            map.put("按摩椅垫","PMC6206307310058562125824-200");
            map.put("按摩披肩","PMC6206307310058562125824-200");
            map.put("按摩椅","PMC6206307310058562125824-200");
            map.put("按摩棒/锤","PMC6206307310058562125824-200");
            map.put("按摩足疗机","PMC6206307310058562125824-200");
            map.put("EMS按摩器","PMC6206307310058562125824-200");
            map.put("护颈仪/颈椎按摩器","PMC6206307310058562125824-200");
            map.put("电动手部按摩仪","PMC6206307310058562125824-200");
            map.put("电动按摩贴","PMC6206307310058562125824-200");
            map.put("电动膝盖按摩仪","PMC6206307310058562125824-200");
            map.put("脸部按摩仪","PMC6206307310058562125824-200");
            map.put("智能按摩贴","PMC6206307310058562125824-200");
            map.put("电吹风","PMC6202307310056293007360-200");
            map.put("理发器","PMC6202307310056293007360-200");
            map.put("生发仪、护发仪","PMC6202307310056293007360-200");
            map.put("电动睫毛卷翘器","PMC6202307310056293007360-200");
            map.put("电动美发梳","PMC6202307310056293007360-200");
            map.put("电动编发器","PMC6202307310056293007360-200");
            map.put("脱毛/剃毛器","PMC6202307310056293007360-200");
            map.put("护肤美妆镜","PMC6202307310056293007360-200");
            map.put("电动修眉刀","PMC6202307310056293007360-200");
            map.put("电子美容仪","PMC6202307310056293007360-200");
            map.put("光子嫩肤仪","PMC6202307310056293007360-200");
            map.put("面膜机","PMC6202307310056293007360-200");
            map.put("商用美容仪（非医疗器械）","PMC6202307310056293007360-200");
            map.put("其他美体瘦身","PMC6202307310056293007360-200");
            map.put("其他美容仪器","PMC6202307310056293007360-200");
            map.put("洁面仪","PMC6202307310056293007360-200");
            map.put("冲牙器","PMC6202307310056293007360-200");
            map.put("磨脚器","PMC6202307310056293007360-200");
            map.put("喷雾仪、蒸脸仪、补水仪","PMC6202307310056293007360-200");
            map.put("毛孔清洁器/黑头仪","PMC6202307310056293007360-200");
            map.put("袪痘仪、袪斑仪","PMC6202307310056293007360-200");
            map.put("形体矫正器/矫姿用品","PMC6202307310056293007360-200");
            map.put("瘦脸机/瘦脸工具","PMC6202307310056293007360-200");
            map.put("电动搓澡仪","PMC6202307310056293007360-200");
            map.put("电动丰胸仪","PMC6202307310056293007360-200");
            map.put("洁面仪、洁面刷","PMC6202307310056293007360-200");
            map.put("美容导入仪","PMC6202307310056293007360-200");
            map.put("美眼仪","PMC6202307310056293007360-200");
            map.put("家用点痣仪","PMC6202307310056293007360-200");
            map.put("面罩美容仪","PMC6202307310056293007360-200");
            map.put("注氧仪","PMC6202307310056293007360-200");
            map.put("电动磨甲器","PMC6202307310056293007360-200");
            map.put("铲皮机","PMC6202307310056293007360-200");
            map.put("美颈仪","PMC6202307310056293007360-200");
            map.put("润眼仪","PMC6202307310056293007360-200");
            map.put("冰敷仪","PMC6202307310056293007360-200");
            map.put("洁牙仪","PMC6202307310056293007360-200");
            map.put("蜡疗机/手蜡机","PMC6202307310056293007360-200");
            map.put("头皮上药器","PMC6202307310056293007360-200");
            map.put("射频仪","PMC6202307310056293007360-200");
            map.put("电动止痒仪","PMC6202307310056293007360-200");
            map.put("牙齿美白笔","PMC6677307310362057768960-200");
            map.put("牙齿美白仪/美牙仪","PMC6677307310362057768960-200");
            map.put("口腔护理套装","PMC6674307310359960616960-200");
            map.put("口腔清新剂","PMC6675307310360640094208-200");
            map.put("电子口腔喷雾器","PMC6675307310360640094208-200");
            map.put("漱口水","PMC6673307310359297916928-200");
            map.put("牙膏","PMC6681307310364364636160-200");
            map.put("牙刷","PMC6680307310363760656384-200");
            map.put("牙签、牙线","PMC6670307310357897019392-200");
            map.put("笔记本电脑","PMC6344307310146743173120-40");
            map.put("MIDI键盘","PMC6342307310144847347712-40");
            map.put("MIDI键盘控制器","PMC6342307310144847347712-40");
            map.put("编曲键盘","PMC6342307310144847347712-40");
            map.put("键盘手托","PMC6342307310144847347712-40");
            map.put("光电鼠标","PMC6342307310144847347712-40");
            map.put("机械鼠标","PMC6342307310144847347712-40");
            map.put("键盘","PMC6342307310144847347712-40");
            map.put("键盘配件","PMC6342307310144847347712-40");
            map.put("其他鼠标、键盘","PMC6342307310144847347712-40");
            map.put("鼠标脚垫、脚贴","PMC6342307310144847347712-40");
            map.put("笔记本键盘","PMC6342307310144847347712-40");
            map.put("笔记本键盘保护膜","PMC6342307310144847347712-40");
            map.put("笔记本键盘","PMC6342307310144847347712-40");
            map.put("笔记本键盘保护膜","PMC6342307310144847347712-40");
            map.put("电脑监视器","PMC6347307310148781604864-20");
            map.put("相机自拍杆","PMC6306307310122898554880-40");
            map.put("相机防水壳、套","PMC6306307310122898554880-40");
            map.put("相机眼罩","PMC6306307310122898554880-40");
            map.put("相机背带、腕带","PMC6306307310122898554880-40");
            map.put("相机三脚架","PMC6306307310122898554880-40");
            map.put("相机清洁用品","PMC6306307310122898554880-40");
            map.put("相机充电器","PMC6306307310122898554880-40");
            map.put("相机取景器","PMC6306307310122898554880-40");
            map.put("相机屏幕贴膜","PMC6306307310122898554880-40");
            map.put("相机电源线","PMC6306307310122898554880-40");
            map.put("其他相机配件","PMC6306307310122898554880-40");
            map.put("相机手柄","PMC6306307310122898554880-40");
            map.put("相机滤镜","PMC6306307310122898554880-40");
            map.put("相机镜头盖","PMC6306307310122898554880-40");
            map.put("贴纸相机","PMC7505307310910064558080-40");
            map.put("手账贴纸/胶带","PMC7505307310910064558080-40");
            map.put("创意贴纸","PMC7505307310910064558080-40");
            map.put("文具贴纸","PMC7505307310910064558080-40");
            map.put("相机贴纸","PMC7505307310910064558080-40");
            map.put("笔记本屏幕保护膜","PMC7505307310910064558080-40");
            map.put("笔记本屏幕保护膜","PMC7505307310910064558080-40");
            map.put("手机电池","PMC7527307310922177708032-40");
            map.put("电源适配器","PMC7501307310907870937088-40");
            map.put("数码充电器","PMC7501307310907870937088-40");
            map.put("数码无线充电器","PMC7501307310907870937088-40");
            map.put("红外线适配器","PMC7501307310907870937088-40");
            map.put("蓝牙适配器","PMC7501307310907870937088-40");
            map.put("头戴式耳机","PMC5980307309911543382016-20");
            map.put("耳机","PMC5980307309911543382016-20");
            map.put("智能耳机","PMC5980307309911543382016-20");
            map.put("蓝牙耳机","PMC5980307309911543382016-20");
            map.put("汽车音箱/低音炮","PMC5986307309917931307008-40");
            map.put("蓝牙音箱","PMC5986307309917931307008-40");
            map.put("插卡音箱","PMC5986307309917931307008-40");
            map.put("其他音箱","PMC5986307309917931307008-40");
            map.put("回音壁音箱","PMC5986307309917931307008-40");
            map.put("智能音箱","PMC5986307309917931307008-40");
            map.put("智能手表","PMC7550307310936471896064-40");
            map.put("智能手机","PMC7539307310929530322944-20");
            map.put("非智能手机","PMC7539307310929530322944-20");
            map.put("二手手机","PMC7539307310929530322944-20");
            map.put("其他婴幼儿寝具/服饰","PMC6084307309983358255104-40");
            map.put("护角","PMC7562307310943321194496-40");
            map.put("纸护角","PMC7562307310943321194496-40");
            map.put("婴儿理发器","PMC7589307310959246966784-40");
            map.put("洗衣液","PMC7590307310959863529472-40");
            map.put("婴幼儿洗衣液/皂","PMC7590307310959863529472-40");
            map.put("内衣洗衣液","PMC7590307310959863529472-40");
            map.put("抑菌洗衣液、洗衣粉","PMC7590307310959863529472-40");
            map.put("男士洗衣液","PMC7590307310959863529472-40");
            map.put("其他口腔护理","PMC7555307310939227553792-40");
            map.put("消毒器、暖奶器、小家电","PMC7656307310998971219968-40");
            map.put("实木婴儿床","PMC7647307310993283743744-40");
            map.put("婴儿床","PMC7647307310993283743744-40");
            map.put("学步鞋","PMC6162307310030741307392-20");
            map.put("童鞋礼盒装","PMC6162307310030741307392-20");
            map.put("童皮鞋","PMC6162307310030741307392-20");
            map.put("学生皮鞋","PMC6162307310030741307392-20");
            map.put("水晶/水钻鞋","PMC6162307310030741307392-20");
            map.put("童板鞋","PMC6162307310030741307392-20");
            map.put("童帆布鞋","PMC6162307310030741307392-20");
            map.put("童洞洞鞋","PMC6162307310030741307392-20");
            map.put("亲子鞋","PMC6162307310030741307392-20");
            map.put("汉服鞋","PMC6162307310030741307392-20");
            map.put("童特色鞋","PMC6162307310030741307392-20");
            map.put("童棉鞋","PMC6162307310030741307392-20");
            map.put("童鞋量脚器","PMC6162307310030741307392-20");
            map.put("足球袜","PMC8111307311275866587136-40");
            map.put("足球门、足球网","PMC8111307311275866587136-40");
            map.put("足球球迷用品","PMC8111307311275866587136-40");
            map.put("足球训练辅助用具","PMC8111307311275866587136-40");
            map.put("足球鞋钉","PMC8111307311275866587136-40");
            map.put("足球护腿板","PMC8111307311275866587136-40");
            map.put("足球战术板","PMC8111307311275866587136-40");
            map.put("足球袋","PMC8111307311275866587136-40");
            map.put("足球服","PMC8111307311275866587136-40");
            map.put("桌上足球、桌上曲棍球","PMC8111307311275866587136-40");
            map.put("羽毛球","PMC8024307311224704466944-40");
            map.put("羽毛球、网球拍线","PMC8024307311224704466944-40");
            map.put("瑜伽垫","PMC8125307311284188086272-40");
            map.put("瑜伽砖","PMC8125307311284188086272-40");
            map.put("普拉提用品","PMC8125307311284188086272-40");
            map.put("瑜伽铺巾","PMC8125307311284188086272-40");
            map.put("瑜伽球","PMC8125307311284188086272-40");
            map.put("瑜伽辅助用品","PMC8125307311284188086272-40");
            map.put("瑜伽环/瑜伽轮","PMC8125307311284188086272-40");
            map.put("瑜伽拉力带","PMC8125307311284188086272-40");
            map.put("瑜伽洗鼻器","PMC8125307311284188086272-40");
            map.put("瑜伽服","PMC8125307311284188086272-40");
            map.put("冲浪、滑水、帆板","PMC8116307311278718713856-40");
            map.put("童舞蹈鞋","PMC8136307311291398094848-40");
            map.put("舞蹈鞋","PMC8136307311291398094848-40");
            map.put("羽毛球拍、网球拍","PMC8123307311282887852032-40");
            map.put("网球包","PMC8123307311282887852032-40");
            map.put("网球网、球柱","PMC8123307311282887852032-40");
            map.put("网球赛事纪念","PMC8123307311282887852032-40");
            map.put("网球服","PMC8123307311282887852032-40");
            map.put("武术服","PMC8041307311235089563648-40");
            map.put("拳击用品","PMC8041307311235089563648-40");
            map.put("跆拳道、柔道服","PMC8044307311237107023872-40");
            map.put("台球","PMC8030307311228710027264-40");
            map.put("沙包/呼啦圈/投掷套圈","PMC7970307311191187783680-40");
            map.put("计步器","PMC7955307311181876428800-40");
            map.put("智能计步器","PMC7955307311181876428800-40");
            map.put("跳绳","PMC7975307311193855361024-40");
            map.put("乒乓球","PMC8122307311282279677952-40");
            map.put("乒乓球拍","PMC8122307311282279677952-40");
            map.put("乒乓球台","PMC8122307311282279677952-40");
            map.put("乒乓球网、网架","PMC8122307311282279677952-40");
            map.put("乒乓球发球机","PMC8122307311282279677952-40");
            map.put("乒乓球挡板、底板","PMC8122307311282279677952-40");
            map.put("乒乓球保养、清洁装备","PMC8122307311282279677952-40");
            map.put("乒乓球拍套、包","PMC8122307311282279677952-40");
            map.put("乒乓球捡球器","PMC8122307311282279677952-40");
            map.put("其他乒乓球配件","PMC8122307311282279677952-40");
            map.put("乒乓球鞋","PMC8122307311282279677952-40");
            map.put("乒乓球服","PMC8122307311282279677952-40");
            map.put("排球服","PMC8124307311283558940672-40");
            map.put("排球","PMC8124307311283558940672-40");
            map.put("活力板、滑板","PMC8031307311229385310208-40");
            map.put("篮球","PMC8029307311228043132928-40");
            map.put("高尔夫球","PMC8067307311250595905536-40");
            map.put("高尔夫球杆","PMC8067307311250595905536-40");
            map.put("高尔夫球钉、球TEE","PMC8067307311250595905536-40");
            map.put("高尔夫球车","PMC8067307311250595905536-40");
            map.put("高尔夫球袋","PMC8067307311250595905536-40");
            map.put("高尔夫服","PMC8138307311292694134784-40");
            map.put("飞镖","PMC8065307311249304059904-40");
            map.put("壁球","PMC8112307311276550258688-40");
            map.put("保龄球","PMC8086307311261215883264-40");
            map.put("棒球","PMC8026307311226327662592-40");
            map.put("运动帽、头巾","PMC7950307311179330486272-40");
            map.put("运动外套","PMC7992307311204970266624-40");
            map.put("棒球鞋","PMC8133307311289204473856-40");
            map.put("篮球鞋","PMC8129307311286700474368-40");
            map.put("排球鞋","PMC8151307311299673456640-40");
            map.put("越野跑鞋","PMC8147307311297865711616-40");
            map.put("网球鞋","PMC8144307311295894388736-40");
            map.put("羽毛球鞋","PMC8128307311285907750912-40");
            map.put("足球鞋","PMC8149307311298482274304-40");
            map.put("钓鱼伞","PMC8066307311249941594112-40");
            map.put("钓鱼船","PMC8066307311249941594112-40");
            map.put("钓鱼包","PMC8066307311249941594112-40");
            map.put("钓鱼服配","PMC8066307311249941594112-40");
            map.put("钓鱼帽","PMC8066307311249941594112-40");
            map.put("钓鱼鞋","PMC8066307311249941594112-40");
            map.put("钓鱼服","PMC8066307311249941594112-40");
            map.put("钓鱼类玩具","PMC8066307311249941594112-40");
            map.put("钓鱼灯、落地式电筒","PMC8066307311249941594112-40");
            map.put("登山鞋","PMC8130307311287308648448-40");
            map.put("防护金具","PMC7934307311169641644032-40");
            map.put("其他防护眼镜","PMC7934307311169641644032-40");
            map.put("防护眼罩","PMC7934307311169641644032-40");
            map.put("其他眼部防护","PMC7934307311169641644032-40");
            map.put("综合防护眼镜","PMC7934307311169641644032-40");
            map.put("其他头部防护","PMC7934307311169641644032-40");
            map.put("其他坠落防护","PMC7934307311169641644032-40");
            map.put("其他听力防护","PMC7934307311169641644032-40");
            map.put("防护耳罩","PMC7934307311169641644032-40");
            map.put("其他自然灾害防护","PMC7934307311169641644032-40");
            map.put("防护栏","PMC7934307311169641644032-40");
            map.put("其他手部防护","PMC7934307311169641644032-40");
            map.put("电动送风过滤式呼吸防护系统","PMC7934307311169641644032-40");
            map.put("呼吸防护滤材","PMC7934307311169641644032-40");
            map.put("其他呼吸防护配件","PMC7934307311169641644032-40");
            map.put("其他呼吸防护","PMC7934307311169641644032-40");
            map.put("防护服","PMC7934307311169641644032-40");
            map.put("医用防护服","PMC7934307311169641644032-40");
            map.put("其他身体防护","PMC7934307311169641644032-40");
            map.put("露营零配件","PMC8051307311241213247488-40");
            map.put("吊床","PMC8056307311244036014080-40");
            map.put("野营灯、露营灯","PMC8052307311241850781696-40");
            map.put("野营信号灯、救生灯","PMC8052307311241850781696-40");
            map.put("野营睡袋","PMC8060307311246573568000-40");
            map.put("圆规","PMC8055307311243453005824-40");
            map.put("望远镜","PMC8047307311239069958144-40");
            map.put("户外望远镜","PMC8047307311239069958144-40");
            map.put("娃娃公仔","PMC7670307311007330467840-40");
            map.put("娃娃玩具配件","PMC7670307311007330467840-40");
            map.put("娃娃车","PMC7670307311007330467840-40");
            map.put("娃娃家具","PMC7670307311007330467840-40");
            map.put("皮艺沙发","PMC6958307310550688202752-40");
            map.put("布艺沙发","PMC6958307310550688202752-40");
            map.put("实木沙发","PMC6958307310550688202752-40");
            map.put("皮布沙发","PMC6958307310550688202752-40");
            map.put("藤/竹沙发","PMC6958307310550688202752-40");
            map.put("铁艺沙发","PMC6958307310550688202752-40");
            map.put("功能沙发","PMC6958307310550688202752-40");
            map.put("沙发类","PMC6958307310550688202752-40");
            map.put("懒人沙发","PMC6958307310550688202752-40");
            map.put("沙发+茶几","PMC6958307310550688202752-40");
            map.put("沙发+角几","PMC6958307310550688202752-40");
            map.put("沙发+茶几","PMC6958307310550688202752-40");
            map.put("沙发+角几","PMC6958307310550688202752-40");
            map.put("沙发/布艺/地毯清洗机","PMC6958307310550688202752-40");
            map.put("商超货架","PMC6926307310525958586368-40");
            map.put("婚车装饰用品","PMC6001307309930887512064-10");
            map.put("其他集装整理设备","PMC6030307309950143561728-10");
            map.put("冰箱收纳盒","PMC6030307309950143561728-10");
            map.put("锅具收纳架","PMC6030307309950143561728-10");
            map.put("衣夹、裤夹、收纳夹子","PMC6030307309950143561728-10");
            map.put("整理隔板","PMC6030307309950143561728-10");
            map.put("收纳整理架、置物架","PMC6030307309950143561728-10");
            map.put("其他整理用具","PMC6030307309950143561728-10");
            map.put("收纳层架","PMC6030307309950143561728-10");
            map.put("玩具收纳","PMC6030307309950143561728-10");
            map.put("收纳洞洞板","PMC6030307309950143561728-10");
            map.put("钥匙收纳","PMC6030307309950143561728-10");
            map.put("胶囊咖啡收纳盒/架","PMC6030307309950143561728-10");
            map.put("内衣收纳","PMC6030307309950143561728-10");
            map.put("收纳套装","PMC6030307309950143561728-10");
            map.put("收纳盒","PMC6030307309950143561728-10");
            map.put("食物收纳盒","PMC6030307309950143561728-10");
            map.put("收纳柜","PMC6030307309950143561728-10");
            map.put("收纳瓶","PMC6030307309950143561728-10");
            map.put("收纳凳、储物凳、收纳桌","PMC6030307309950143561728-10");
            map.put("收纳罐、玻璃密封罐","PMC6030307309950143561728-10");
            map.put("收纳篮、置物篮、收纳筐","PMC6030307309950143561728-10");
            map.put("收纳桶","PMC6030307309950143561728-10");
            map.put("其他收纳防尘","PMC6030307309950143561728-10");
            map.put("鞋子收纳架/盒","PMC6030307309950143561728-10");
            map.put("壁式收纳盒","PMC6030307309950143561728-10");
            map.put("水管收纳架","PMC6030307309950143561728-10");
            map.put("其他收纳用品","PMC6030307309950143561728-10");
            map.put("整理剂","PMC6030307309950143561728-10");
            map.put("车载手机支架","PMC6036307309953499004928-10");
            map.put("口罩支架","PMC6036307309953499004928-10");
            map.put("鱼竿支架","PMC6036307309953499004928-10");
            map.put("支架","PMC6036307309953499004928-10");
            map.put("LED支架","PMC6036307309953499004928-10");
            map.put("架灯、支架式电筒","PMC6036307309953499004928-10");
            map.put("LED一体化支架灯","PMC6036307309953499004928-10");
            map.put("电视机支架","PMC6036307309953499004928-10");
            map.put("投影机支架/三脚架","PMC6036307309953499004928-10");
            map.put("耳机支架","PMC6036307309953499004928-10");
            map.put("键盘支架","PMC6036307309953499004928-10");
            map.put("摄影支架","PMC6036307309953499004928-10");
            map.put("直播专用支架","PMC6036307309953499004928-10");
            map.put("摆件支架/底座","PMC6036307309953499004928-10");
            map.put("防滑垫","PMC6019307309943113908224-10");
            map.put("遮阳伞、太阳伞","PMC6013307309939037044736-10");
            map.put("智能戒指","PMC6501307310249914662912-20");
            map.put("手镯","PMC6417307310198672850944-20");
            map.put("智能手镯","PMC6417307310198672850944-20");
            map.put("胸针","PMC6406307310191181824000-40");
            map.put("镜框","PMC6423307310202527416320-40");
            map.put("太阳镜","PMC6425307310203341111296-40");
            map.put("饼干","PMC6580307310299818491904-40");
            map.put("消化饼干","PMC6580307310299818491904-40");
            map.put("夹心饼干","PMC6580307310299818491904-40");
            map.put("手指饼干","PMC6580307310299818491904-40");
            map.put("膨化","PMC6581307310300749627392-40");
            map.put("咖啡伴侣辅料","PMC6524307310264280154112-40");
            map.put("咖啡豆","PMC6524307310264280154112-40");
            map.put("咖啡粉","PMC6524307310264280154112-40");
            map.put("咖啡生豆","PMC6524307310264280154112-40");
            map.put("挂耳咖啡","PMC6524307310264280154112-40");
            map.put("速溶咖啡","PMC6524307310264280154112-40");
            map.put("胶囊咖啡","PMC6524307310264280154112-40");
            map.put("即饮咖啡","PMC6524307310264280154112-40");
            map.put("方便面类","PMC6544307310276678516736-40");
            map.put("参类滋补品","PMC6611307310320764846080-10");
            map.put("其他传统滋补品","PMC6611307310320764846080-10");
            map.put("被子固定器","PMC6817307310446304559104-40");
            map.put("藤本植物","PMC6846307310466563047424-200");
            map.put("棕榈类植物","PMC6846307310466563047424-200");
            map.put("竹类植物","PMC6846307310466563047424-200");
            map.put("水生植物","PMC6846307310466563047424-200");
            map.put("仙人掌及多肉植物","PMC6846307310466563047424-200");
            map.put("蕨类植物","PMC6846307310466563047424-200");
            map.put("其他园林植物","PMC6846307310466563047424-200");
            map.put("兰科植物","PMC6846307310466563047424-200");
            map.put("月季蔷薇类植物","PMC6846307310466563047424-200");
            map.put("水果罐头","PMC6846307310466563047424-200");
            map.put("菜粉/水果粉","PMC6846307310466563047424-200");
            map.put("装饰花瓶","PMC6864307310477665370112-200");
            map.put("填料","PMC6864307310477665370112-200");
            map.put("填料塔","PMC6864307310477665370112-200");
            map.put("其他颜料、填料","PMC6864307310477665370112-200");
            map.put("其他颜料、填料","PMC6864307310477665370112-200");
            map.put("按摩枕/颈腰靠垫/坐垫","PMC6919307310520044617728-40");
            map.put("靠垫/抱枕/抱枕被","PMC6919307310520044617728-40");
            map.put("一次性防尘罩","PMC7005307310586725662720-200");
            map.put("衣物防尘罩、袋、套","PMC7005307310586725662720-200");
            map.put("家用防尘罩","PMC7005307310586725662720-200");
            map.put("奶瓶/果蔬清洗","PMC7603307310967568465920-40");
            map.put("奶瓶","PMC7603307310967568465920-40");
            map.put("奶瓶相关","PMC7603307310967568465920-40");
            map.put("儿童桌椅","PMC7611307310971888599040-40");
            map.put("儿童学习桌椅","PMC7611307310971888599040-40");
            map.put("儿童用餐椅","PMC7611307310971888599040-40");
            map.put("儿童摇椅","PMC7611307310971888599040-40");
            map.put("儿童餐椅","PMC7611307310971888599040-40");
            map.put("电动车儿童座椅","PMC7611307310971888599040-40");
            map.put("婴儿步前鞋","PMC7618307310976070320128-40");
            map.put("婴儿摇篮/摇椅","PMC7618307310976070320128-40");
            map.put("婴儿牙刷/牙膏","PMC7618307310976070320128-40");
            map.put("婴儿棉柔巾","PMC7618307310976070320128-40");
            map.put("婴儿推车","PMC7618307310976070320128-40");
            map.put("婴儿浴盆","PMC7618307310976070320128-40");
            map.put("婴儿尿布桶","PMC7618307310976070320128-40");
            map.put("婴儿监护器","PMC7618307310976070320128-40");
            map.put("孕妇内裤","PMC7634307310985629138944-40");
            map.put("娱乐/酒吧/KTV家具","PMC8098307311268522360832-40");
            map.put("娱乐/酒吧/KTV家具","PMC8098307311268522360832-40");
            map.put("滑雪镜、登山镜","PMC8074307311254622437376-40");
            map.put("滑雪板","PMC8074307311254622437376-40");
            map.put("滑雪圈","PMC8074307311254622437376-40");
            map.put("滑雪靴","PMC8074307311254622437376-40");
            map.put("滑雪手套","PMC8074307311254622437376-40");
            map.put("其他滑雪用品","PMC8074307311254622437376-40");
            map.put("滑雪服","PMC8074307311254622437376-40");
            map.put("登山杖、滑雪杖","PMC8074307311254622437376-40");
            map.put("儿童冲锋衣/滑雪服","PMC8074307311254622437376-40");
            map.put("潜水袜","PMC8119307311280916529152-40");
            map.put("潜水用品","PMC8119307311280916529152-40");
            map.put("潜水照明装备","PMC8119307311280916529152-40");
            map.put("潜水服","PMC8119307311280916529152-40");
            map.put("潜水电脑","PMC8119307311280916529152-40");
            map.put("潜水摄影配件","PMC8119307311280916529152-40");
            map.put("展览帐篷","PMC8061307311247290793984-40");
            map.put("广告帐篷","PMC8061307311247290793984-40");
            map.put("美甲台","PMC6228307310072277499904-200");
            map.put("美甲台","PMC6228307310072277499904-200");
            map.put("美甲贴","PMC6228307310072277499904-200");
            map.put("美甲饰品","PMC6228307310072277499904-200");
            map.put("美甲笔","PMC6228307310072277499904-200");
            map.put("美甲机、光疗灯","PMC6228307310072277499904-200");
            map.put("其他美甲产品","PMC6228307310072277499904-200");
            map.put("美甲产品加工定制","PMC6228307310072277499904-200");
            map.put("剃须刀、除毛器","PMC6269307310097502044160-200");
            map.put("手表充电器","PMC8210307311336394588160-40");
            map.put("智能手表保护壳/套","PMC8210307311336394588160-40");
            map.put("智能手表表带","PMC8210307311336394588160-40");
            map.put("智能手表贴膜","PMC8210307311336394588160-40");
            map.put("园艺工具","PMC6970307310560288964608-40");
            map.put("园艺工具套组","PMC6970307310560288964608-40");
            map.put("多功能垫","PMC7215307310732989431808-40");
            map.put("儿童多功能玩具台/游戏台","PMC7215307310732989431808-40");
            map.put("多功能野营刀具","PMC7215307310732989431808-40");
            map.put("多功能切菜器","PMC7215307310732989431808-40");
            map.put("多功能蔬菜脱水器","PMC7215307310732989431808-40");
            map.put("多功能锅","PMC7215307310732989431808-40");
            map.put("多功能锅","PMC7215307310732989431808-40");
            map.put("多功能电话机","PMC7215307310732989431808-40");
            map.put("多功能料理机","PMC7215307310732989431808-40");
            map.put("多功能料理机","PMC7215307310732989431808-40");
            map.put("多功能包装机","PMC7215307310732989431808-40");
            map.put("多功能一体机办公设备","PMC7215307310732989431808-40");
            map.put("多功能刀","PMC7215307310732989431808-40");
            map.put("保鲜膜切割器","PMC7191307310718393253888-40");
            map.put("气动切割机","PMC7191307310718393253888-40");
            map.put("等离子切割机","PMC7191307310718393253888-40");
            map.put("激光切割机","PMC7191307310718393253888-40");
            map.put("其他电焊、切割设备","PMC7191307310718393253888-40");
            map.put("水切割机","PMC7191307310718393253888-40");
            map.put("磨片、切割片","PMC7191307310718393253888-40");
            map.put("玻璃瓷砖吸盘","PMC7212307310731215241216-40");
            map.put("其他民族服装","PMC6118307310004325580800-40");
            map.put("暖风机/取暖器","PMC6939307310535085391872-200");
            map.put("游泳池","PMC6942307310538117873664-200");
            map.put("温室、大棚","PMC6987307310573610074112-200");
            map.put("其他袜子","PMC6120307310005684535296-40");
            map.put("学生校服","PMC6144307310020008083456-40");
            map.put("卡通人偶服装/配件","PMC6143307310019236331520-40");
            map.put("智能眼镜","PMC7547307310934626402304-40");
            map.put("防晒面纱、防晒面罩","PMC7377307310832759341056-40");
            map.put("鸟类","PMC7912307311155511033856-40");
            map.put("童凉鞋","PMC6133307310013720821760-40");
            map.put("男式人字拖","PMC6133307310013720821760-40");
            map.put("男式凉鞋","PMC6133307310013720821760-40");
            map.put("塑料拖鞋","PMC6130307310011770470400-40");
            map.put("EVA拖鞋","PMC6130307310011770470400-40");
            map.put("毛毛拖鞋","PMC6130307310011770470400-40");
            map.put("布艺拖鞋","PMC6130307310011770470400-40");
            map.put("无纺布拖鞋","PMC6130307310011770470400-40");
            map.put("皮质拖鞋","PMC6130307310011770470400-40");
            map.put("羽绒拖鞋","PMC6130307310011770470400-40");
            map.put("亚麻拖鞋","PMC6130307310011770470400-40");
            map.put("童拖鞋","PMC6130307310011770470400-40");
            map.put("一次性拖鞋","PMC6130307310011770470400-40");
            map.put("USB拖鞋、暖脚宝","PMC6130307310011770470400-40");
            map.put("擦地拖鞋","PMC6130307310011770470400-40");
            map.put("睡眠袜","PMC6640307310338628386816-10");
            map.put("智能睡眠仪","PMC6640307310338628386816-10");
            map.put("自给式空气呼吸器","PMC6639307310337999241216-10");
            map.put("逃生呼吸器","PMC6639307310337999241216-10");
            map.put("长管供气呼吸器","PMC6639307310337999241216-10");
            map.put("戒烟灵、戒烟贴","PMC6641307310339299475456-10");
            map.put("剪纸","PMC6853307310470572802048-200");
            map.put("箱包包装","PMC6155307310026375036928-40");
            map.put("儿童帽","PMC6082307309982104158208-40");
            map.put("衣帽架","PMC6952307310545113972736-40");
            map.put("儿童衣帽架","PMC6952307310545113972736-40");
            map.put("玄关台+门厅柜+衣帽架","PMC6952307310545113972736-40");
            map.put("玄关台+门厅柜+衣帽架","PMC6952307310545113972736-40");
            map.put("莫桑钻、仿真钻发饰","PMC6077307309979310751744-40");
            map.put("天然玉石发饰","PMC6077307309979310751744-40");
            map.put("其他发饰","PMC6077307309979310751744-40");
            map.put("儿童发饰","PMC6077307309979310751744-40");
            map.put("发饰、头饰配件","PMC6077307309979310751744-40");
            map.put("收腹带、收腰带","PMC6069307309974210478080-40");
            map.put("儿童围巾","PMC6095307309989762957312-40");
            map.put("娃娃配饰","PMC6085307309984121618432-40");
            map.put("其他太阳能设备","PMC7164307310701322436608-200");
            map.put("一次性床单","PMC6831307310455624302592-40");
            map.put("婚房压床布置","PMC6831307310455624302592-40");
            map.put("床单、被单","PMC6831307310455624302592-40");
            map.put("床旗、床尾巾","PMC6831307310455624302592-40");
            map.put("酒店床品套件","PMC6831307310455624302592-40");
            map.put("床围","PMC6831307310455624302592-40");
            map.put("婚庆床品套件","PMC6831307310455624302592-40");
            map.put("太阳能发电机组","PMC7165307310702240989184-200");
            map.put("太阳能控制器","PMC7165307310702240989184-200");
            map.put("羽绒被","PMC6816307310445646053376-40");
            map.put("风力发电机组","PMC7168307310703839019008-200");
            map.put("床罩、床笠、床裙","PMC6832307310456425414656-40");
            map.put("床垫、席梦思","PMC6825307310450926682112-40");
            map.put("乳胶弹簧床垫","PMC6825307310450926682112-40");
            map.put("棕榈弹簧床垫","PMC6825307310450926682112-40");
            map.put("圆床垫","PMC6825307310450926682112-40");
            map.put("3D床垫","PMC6825307310450926682112-40");
            map.put("卷包/折叠床垫","PMC6825307310450926682112-40");
            map.put("记忆棉弹簧床垫","PMC6825307310450926682112-40");
            map.put("酒店床垫、保护垫","PMC6825307310450926682112-40");
            map.put("婴童床垫","PMC6825307310450926682112-40");
            map.put("羽绒/羽毛床垫","PMC6825307310450926682112-40");
            map.put("羊毛/羊羔绒床垫","PMC6825307310450926682112-40");
            map.put("乳胶床垫","PMC6825307310450926682112-40");
            map.put("记忆床垫","PMC6825307310450926682112-40");
            map.put("化纤床垫","PMC6825307310450926682112-40");
            map.put("海绵床垫","PMC6825307310450926682112-40");
            map.put("功能床垫","PMC6825307310450926682112-40");
            map.put("其他床垫","PMC6825307310450926682112-40");
            map.put("榻榻米床垫","PMC6825307310450926682112-40");
            map.put("婴童蚊帐","PMC6826307310451849428992-40");
            map.put("蚊帐、床幔","PMC6826307310451849428992-40");
            map.put("摇铃床铃","PMC6833307310457310412800-40");
            map.put("车挂/床挂玩具","PMC6833307310457310412800-40");
            map.put("婴童床品套件","PMC6833307310457310412800-40");
            map.put("其他婴童床品","PMC6833307310457310412800-40");
            map.put("其他水上运动用品","PMC7961307311185374478336-40");
            map.put("沙发垫/沙发套/沙发巾","PMC6850307310468656005120-40");
            map.put("车用窗帘","PMC6845307310465900347392-40");
            map.put("窗帘配件","PMC6845307310465900347392-40");
            map.put("窗帘加工","PMC6845307310465900347392-40");
            map.put("成品窗帘","PMC6845307310465900347392-40");
            map.put("布艺窗帘","PMC6845307310465900347392-40");
            map.put("门帘、小窗帘","PMC6845307310465900347392-40");
            map.put("百叶窗帘","PMC6845307310465900347392-40");
            map.put("竹窗帘","PMC6845307310465900347392-40");
            map.put("全屋窗帘套餐","PMC6845307310465900347392-40");
            map.put("组合运动户外护具","PMC7935307311170371452928-40");
            map.put("其他运动护具","PMC7935307311170371452928-40");
            map.put("教练车","PMC7963307311186901204992-40");
            map.put("奖牌","PMC7981307311197307273216-40");
            map.put("毛巾、巾类加工","PMC6809307310441359474688-200");
            map.put("酒店毛巾、浴巾","PMC6809307310441359474688-200");
            map.put("情侣、婚庆毛巾","PMC6809307310441359474688-200");
            map.put("礼品、广告毛巾","PMC6809307310441359474688-200");
            map.put("蛋糕毛巾、造型毛巾","PMC6809307310441359474688-200");
            map.put("变色毛巾、创意毛巾","PMC6809307310441359474688-200");
            map.put("真丝毛巾、蚕丝毛巾","PMC6809307310441359474688-200");
            map.put("胸套毛巾","PMC6809307310441359474688-200");
            map.put("抗菌毛巾","PMC6809307310441359474688-200");
            map.put("毛巾","PMC6809307310441359474688-200");
            map.put("婴童毛巾、浴巾、毛巾被","PMC6809307310441359474688-200");
            map.put("毛巾套装","PMC6809307310441359474688-200");
            map.put("毛巾礼盒装","PMC6809307310441359474688-200");
            map.put("压缩毛巾","PMC6809307310441359474688-200");
            map.put("粉笔","PMC7968307311189602336768-40");
            map.put("其他毛纺面料","PMC7351307310818049916928-40");
            map.put("大麻面料","PMC7351307310818049916928-40");
            map.put("亚麻面料","PMC7351307310818049916928-40");
            map.put("苎麻面料","PMC7351307310818049916928-40");
            map.put("黄麻面料","PMC7351307310818049916928-40");
            map.put("剑麻面料","PMC7351307310818049916928-40");
            map.put("其他麻类面料","PMC7351307310818049916928-40");
            map.put("其他混纺、交织类面料","PMC7351307310818049916928-40");
            map.put("腈纶面料","PMC7351307310818049916928-40");
            map.put("丙纶面料","PMC7351307310818049916928-40");
            map.put("维纶面料","PMC7351307310818049916928-40");
            map.put("芳纶面料","PMC7351307310818049916928-40");
            map.put("粘胶(人棉)面料","PMC7351307310818049916928-40");
            map.put("其他化纤面料","PMC7351307310818049916928-40");
            map.put("牛仔面料","PMC7351307310818049916928-40");
            map.put("其他棉类面料","PMC7351307310818049916928-40");
            map.put("蕾丝面料","PMC7351307310818049916928-40");
            map.put("其他针织面料","PMC7351307310818049916928-40");
            map.put("防水鞋","PMC8146307311297203011584-40");
            map.put("针线、别针、缝纫","PMC7346307310814728028160-40");
            map.put("缝纫机","PMC7349307310816082788352-40");
            map.put("线类","PMC7352307310818712616960-40");
            map.put("龙猫零食","PMC7882307311137873985536-40");
            map.put("猫猫零食","PMC7882307311137873985536-40");
            map.put("猫猫零食罐头","PMC7882307311137873985536-40");
            map.put("摇床、振荡器","PMC7839307311112150319104-40");
            map.put("车载充气床","PMC7839307311112150319104-40");
            map.put("沙发床","PMC7839307311112150319104-40");
            map.put("罗汉床","PMC7839307311112150319104-40");
            map.put("洗头床","PMC7839307311112150319104-40");
            map.put("护理床","PMC7839307311112150319104-40");
            map.put("铁艺/钢木床","PMC7839307311112150319104-40");
            map.put("沙发卡座","PMC7839307311112150319104-40");
            map.put("洗头床","PMC7839307311112150319104-40");
            map.put("护理床","PMC7839307311112150319104-40");
            map.put("铁艺/钢木床","PMC7839307311112150319104-40");
            map.put("折叠床","PMC7839307311112150319104-40");
            map.put("儿童床","PMC7839307311112150319104-40");
            map.put("子母床","PMC7839307311112150319104-40");
            map.put("充气沙发","PMC7839307311112150319104-40");
            map.put("充气床","PMC7839307311112150319104-40");
            map.put("实木床","PMC7839307311112150319104-40");
            map.put("布艺床","PMC7839307311112150319104-40");
            map.put("皮艺床","PMC7839307311112150319104-40");
            map.put("藤艺床","PMC7839307311112150319104-40");
            map.put("板式床","PMC7839307311112150319104-40");
            map.put("拔步床/架子床","PMC7839307311112150319104-40");
            map.put("其他床类","PMC7839307311112150319104-40");
            map.put("其他床榻","PMC7839307311112150319104-40");
            map.put("子母床+写字桌","PMC7839307311112150319104-40");
            map.put("子母床+写字桌","PMC7839307311112150319104-40");
            map.put("水上充气床、充气浮排","PMC7839307311112150319104-40");
            map.put("跳床","PMC7839307311112150319104-40");
            map.put("户外充气床","PMC7839307311112150319104-40");
            map.put("户外折叠床","PMC7839307311112150319104-40");
            map.put("液压机床","PMC7839307311112150319104-40");
            map.put("医用车、床、台","PMC7839307311112150319104-40");
            map.put("地垫","PMC7839307311112150319104-40");
            map.put("组合地垫","PMC7839307311112150319104-40");
            map.put("商用地垫","PMC7839307311112150319104-40");
            map.put("保险杠","PMC7767307311067631976448-10");
            map.put("其他车身及附件","PMC7767307311067631976448-10");
            map.put("车身贴","PMC7767307311067631976448-10");
            map.put("车载自行车架","PMC7767307311067631976448-10");
            map.put("车架","PMC7767307311067631976448-10");
            map.put("车架总成","PMC7767307311067631976448-10");
            map.put("楼梯及配件","PMC7849307311117812629504-40");
            map.put("非工业轮胎","PMC7763307311065572573184-10");
            map.put("车用轮胎","PMC7763307311065572573184-10");
            map.put("轮胎防滑链","PMC7763307311065572573184-10");
            map.put("自行车轮胎","PMC7763307311065572573184-10");
            map.put("汽车轮胎","PMC7763307311065572573184-10");
            map.put("工程机械轮胎","PMC7763307311065572573184-10");
            map.put("工具车轮胎","PMC7763307311065572573184-10");
            map.put("农用车轮胎","PMC7763307311065572573184-10");
            map.put("其他轮胎","PMC7763307311065572573184-10");
            map.put("散热器","PMC7794307311084472107008-10");
            map.put("汽车散热器","PMC7794307311084472107008-10");
            map.put("机械离合器","PMC7760307311063186014208-10");
            map.put("电磁离合器","PMC7760307311063186014208-10");
            map.put("磁粉离合器","PMC7760307311063186014208-10");
            map.put("离心离合器","PMC7760307311063186014208-10");
            map.put("超越离合器","PMC7760307311063186014208-10");
            map.put("安全离合器","PMC7760307311063186014208-10");
            map.put("液压离合器","PMC7760307311063186014208-10");
            map.put("气动离合器","PMC7760307311063186014208-10");
            map.put("轴承","PMC7789307311081691283456-10");
            map.put("轴承钢","PMC7789307311081691283456-10");
            map.put("机械密封件","PMC7789307311081691283456-10");
            map.put("其他密封件","PMC7789307311081691283456-10");
            map.put("轴承和离合器油","PMC7789307311081691283456-10");
            map.put("救生衣","PMC7873307311132006154240-40");
            map.put("刹车卡钳罩","PMC7758307311062535897088-10");
            map.put("刹车及离合系统用油","PMC7758307311062535897088-10");
            map.put("花洒软管","PMC7790307311082551115776-10");
            map.put("增强软管","PMC7790307311082551115776-10");
            map.put("送丝软管","PMC7790307311082551115776-10");
            map.put("其他工业皮带","PMC7790307311082551115776-10");
            map.put("皮带","PMC7790307311082551115776-10");
            map.put("燃油喷射装置","PMC7770307311070073061376-10");
            map.put("点火器","PMC7775307311073130708992-10");
            map.put("点火线圈","PMC7775307311073130708992-10");
            map.put("电源、点火系统配件","PMC7775307311073130708992-10");
            map.put("其他电源、点火系统","PMC7775307311073130708992-10");
            map.put("点火开关","PMC7775307311073130708992-10");
            map.put("点火模块","PMC7775307311073130708992-10");
            map.put("汽车电瓶/蓄电池","PMC7757307311061780922368-10");
            map.put("纽扣电池","PMC7757307311061780922368-10");
            map.put("锂原电池","PMC7757307311061780922368-10");
            map.put("电池修复机","PMC7757307311061780922368-10");
            map.put("电池测试仪","PMC7757307311061780922368-10");
            map.put("相机电池","PMC7757307311061780922368-10");
            map.put("电池盒/电池座","PMC7757307311061780922368-10");
            map.put("座椅及附件","PMC7751307311058161238016-10");
            map.put("其他车辆座椅","PMC7751307311058161238016-10");
            map.put("童车/座椅配件","PMC7751307311058161238016-10");
            map.put("海关锁","PMC7742307311052314378240-10");
            map.put("固定锁","PMC7742307311052314378240-10");
            map.put("扣锁","PMC7742307311052314378240-10");
            map.put("海关锁","PMC7742307311052314378240-10");
            map.put("固定锁","PMC7742307311052314378240-10");
            map.put("扣锁","PMC7742307311052314378240-10");
            map.put("摩托车车锁","PMC7742307311052314378240-10");
            map.put("中控锁","PMC7742307311052314378240-10");
            map.put("汽车锁","PMC7742307311052314378240-10");
            map.put("密码锁","PMC7742307311052314378240-10");
            map.put("自行车锁","PMC7742307311052314378240-10");
            map.put("机械门锁","PMC7742307311052314378240-10");
            map.put("窗锁","PMC7742307311052314378240-10");
            map.put("家具锁、办公锁","PMC7742307311052314378240-10");
            map.put("指纹锁","PMC7742307311052314378240-10");
            map.put("IC卡锁","PMC7742307311052314378240-10");
            map.put("磁卡锁","PMC7742307311052314378240-10");
            map.put("磁力锁","PMC7742307311052314378240-10");
            map.put("扫码支付盒子","PMC7738307311049801990144-10");
            map.put("数码视频盒子","PMC7738307311049801990144-10");
            map.put("狗狗服装","PMC7870307311130194214912-40");
            map.put("车用头盔","PMC7754307311059524386816-20");
            map.put("骑行头盔","PMC7754307311059524386816-20");
            map.put("防爆头盔","PMC7754307311059524386816-20");
            map.put("火花塞","PMC7776307311073982152704-10");
            map.put("宠物尿片/尿垫/护垫","PMC7797307311086313406464-40");
            map.put("汽摩离合器","PMC7782307311077283069952-10");
            map.put("减震器","PMC7784307311078876905472-10");
            map.put("摩托车轮胎","PMC7785307311079531216896-10");
            map.put("电动车轮胎","PMC7785307311079531216896-10");
            map.put("洗发水","PMC7896307311146778492928-40");
            map.put("护发素","PMC7896307311146778492928-40");
            map.put("刹车片","PMC7781307311076641341440-10");
            map.put("刹车盘/刹车鼓","PMC7781307311076641341440-10");
            map.put("刹车调整臂","PMC7781307311076641341440-10");
            map.put("宠物梳子","PMC7893307311144723283968-40");
            map.put("一次性梳子","PMC7893307311144723283968-40");
            map.put("汽车喇叭/高音头","PMC7771307311070727372800-10");
            map.put("汽车喇叭总成","PMC7771307311070727372800-10");
            map.put("儿童太阳镜","PMC6074307309977121325056-40");
            map.put("跳线架","PMC6106307309996859719680-40");
            map.put("光纤跳线","PMC6106307309996859719680-40");
            map.put("跳线机","PMC6106307309996859719680-40");
            map.put("网络光纤跳线","PMC6106307309996859719680-40");
            map.put("球类","PMC7709307311032198496256-40");
            map.put("跳跳马/跳跳球","PMC7709307311032198496256-40");
            map.put("悠悠(溜溜)球","PMC7709307311032198496256-40");
            map.put("海洋球","PMC7709307311032198496256-40");
            map.put("室内攀爬/游戏屋/球池","PMC7709307311032198496256-40");
            map.put("婴儿手抓球","PMC7709307311032198496256-40");
            map.put("巴克球","PMC7709307311032198496256-40");
            map.put("其他婴儿玩具","PMC7582307310955027496960-40");
            map.put("驱虫药物","PMC7910307311154777030656-40");
            map.put("大型户外陆地游乐设备","PMC7714307311035142897664-40");
            map.put("狗狗玩具","PMC7856307311121952407552-40");
            map.put("体重秤","PMC7661307311001630408704-40");
            map.put("珠宝秤","PMC7661307311001630408704-40");
            map.put("手提秤","PMC7661307311001630408704-40");
            map.put("其他秤","PMC7661307311001630408704-40");
            map.put("健康秤","PMC7661307311001630408704-40");
            map.put("智能体重秤","PMC7661307311001630408704-40");
            map.put("猫猫玩具","PMC7854307311121138712576-40");
            map.put("功能饮料","PMC7643307310991136260096-40");
            map.put("含乳饮料","PMC7643307310991136260096-40");
            map.put("其他软饮料","PMC7643307310991136260096-40");
            map.put("吊裤带、背带","PMC7858307311123441385472-40");
            map.put("高度计、高度尺","PMC7659307311000355340288-40");
            map.put("高度计","PMC7659307311000355340288-40");
            map.put("测量工具套装","PMC7659307311000355340288-40");
            map.put("工业相机","PMC7826307311104332136448-40");
            map.put("数码相机包","PMC7826307311104332136448-40");
            map.put("单反相机包","PMC7826307311104332136448-40");
            map.put("光学相机","PMC7826307311104332136448-40");
            map.put("单反相机","PMC7826307311104332136448-40");
            map.put("迷你相机","PMC7826307311104332136448-40");
            map.put("水下相机","PMC7826307311104332136448-40");
            map.put("传统糕点","PMC6509307310254893301760-40");
            map.put("西式糕点","PMC6509307310254893301760-40");
            map.put("原水处理耗材与配件","PMC7819307311099840036864-40");
            map.put("其他原水处理设备","PMC7819307311099840036864-40");
            map.put("污水处理成套设备","PMC7819307311099840036864-40");
            map.put("其他污水处理设备","PMC7819307311099840036864-40");
            map.put("污水处理设备配件","PMC7819307311099840036864-40");
            map.put("污水处理","PMC7819307311099840036864-40");
            map.put("其他水处理化学品","PMC7819307311099840036864-40");
            map.put("生活饮用水处理设备","PMC7819307311099840036864-40");
            map.put("牛奶","PMC6568307310291681542144-40");
            map.put("水族增氧泵","PMC7817307311098514636800-40");
            map.put("潜水泵","PMC7817307311098514636800-40");
            map.put("宠物奶粉","PMC6566307310290842681344-40");
            map.put("牛奶粉","PMC6566307310290842681344-40");
            map.put("羊奶粉","PMC6566307310290842681344-40");
            map.put("骆驼奶粉","PMC6566307310290842681344-40");
            map.put("其他成人奶粉","PMC6566307310290842681344-40");
            map.put("原料奶粉","PMC6566307310290842681344-40");
            map.put("酸奶粉","PMC6566307310290842681344-40");
            map.put("配方牛奶粉","PMC6566307310290842681344-40");
            map.put("配方羊奶粉","PMC6566307310290842681344-40");
            map.put("其他婴幼儿配方奶粉","PMC6566307310290842681344-40");
            map.put("豆浆/豆奶粉","PMC6566307310290842681344-40");
            map.put("奶粉储存盒/奶粉盒","PMC6566307310290842681344-40");
            map.put("炼乳类","PMC6519307310261163786240-40");
            map.put("汽车清洁工具","PMC7828307311105175191552-40");
            map.put("医用测温计/仪","PMC7833307311108438360064-40");
            map.put("红外测温仪","PMC7833307311108438360064-40");
            map.put("车载对讲机","PMC6772307310419305824256-40");
            map.put("灯光检测仪","PMC7831307311107108765696-40");
            map.put("电子宠物","PMC7693307311021612072960-40");
            map.put("木偶","PMC7697307311024413868032-40");
            map.put("气泡信封","PMC7711307311033532284928-40");
            map.put("气泡袋","PMC7711307311033532284928-40");
            map.put("气垫膜、气泡膜","PMC7711307311033532284928-40");
            map.put("集成房屋","PMC7845307311115824529408-40");
            map.put("集成房屋","PMC7845307311115824529408-40");
            map.put("仓鼠玩具","PMC7852307311119679094784-40");
            map.put("书法","PMC6686307310367116099584-10");
            map.put("国画","PMC6687307310368215007232-10");
            map.put("石雕","PMC6708307310380793724928-10");
            map.put("石雕","PMC6708307310380793724928-10");
            map.put("金石篆刻","PMC6693307310371922771968-10");
            map.put("托玛琳/托玛琳按摩、保健用品","PMC7917307311159378182144-40");
            map.put("香炉","PMC6688307310368928038912-10");
            map.put("祭祀香炉/鼎炉","PMC6688307310368928038912-10");
            map.put("动物香料","PMC6688307310368928038912-10");
            map.put("植物香料","PMC6688307310368928038912-10");
            map.put("合成香料","PMC6688307310368928038912-10");
            map.put("其他香料、香精","PMC6688307310368928038912-10");
            map.put("除臭剂","PMC7906307311151996207104-40");
            map.put("鞋袜除臭剂","PMC7906307311151996207104-40");
            map.put("美甲工具套装","PMC6231307310073619677184-200");
            map.put("假睫毛","PMC6240307310079382650880-200");
            map.put("平板","PMC7541307310931040272384-40");
            map.put("交互式白板/智能会议平板","PMC7541307310931040272384-40");
            map.put("平板电脑","PMC7541307310931040272384-40");
            map.put("暖气片、散热器","PMC6325307310134009266176-40");
            map.put("风扇灯","PMC6325307310134009266176-40");
            map.put("LED灯具散热器","PMC6325307310134009266176-40");
            map.put("笔记本散热器","PMC6325307310134009266176-40");
            map.put("笔记本散热器","PMC6325307310134009266176-40");
            map.put("商用垃圾处理器","PMC6332307310138727858176-40");
            map.put("可编程控制器中央处理器","PMC6332307310138727858176-40");
            map.put("主板","PMC6327307310135867342848-40");
            map.put("工业主板","PMC6327307310135867342848-40");
            map.put("显卡","PMC6326307310134810378240-40");
            map.put("电源插头(转换插头)","PMC6331307310137859637248-40");
            map.put("内存","PMC6333307310139491221504-40");
            map.put("机箱","PMC6330307310137272434688-40");
            map.put("光驱盒","PMC6329307310136538431488-40");
            map.put("光驱、刻录机","PMC6329307310136538431488-40");
            map.put("声卡转换器","PMC6334307310140191670272-40");
            map.put("直播声卡套装","PMC6334307310140191670272-40");
            map.put("声卡","PMC6334307310140191670272-40");
            map.put("其他USB产品","PMC6380307310170050920448-40");
            map.put("读卡器","PMC6380307310170050920448-40");
            map.put("网络集线器","PMC6380307310170050920448-40");
            map.put("门禁读卡器","PMC6380307310170050920448-40");
            map.put("Macbook保护套","PMC6376307310167899242496-40");
            map.put("电脑支架","PMC6377307310168603885568-40");
            map.put("笔记本电池","PMC6374307310166607396864-40");
            map.put("笔记本电池","PMC6374307310166607396864-40");
            map.put("游戏机充电器","PMC6375307310167278485504-40");
            map.put("智能设备充电器","PMC6375307310167278485504-40");
            map.put("鼠标垫","PMC6378307310169321111552-40");
            map.put("固态硬盘","PMC6323307310132738392064-40");
            map.put("移动硬盘盒","PMC6319307310130834178048-40");
            map.put("中继器","PMC6356307310154808819712-40");
            map.put("网卡","PMC6357307310155467325440-40");
            map.put("无线网卡","PMC6357307310155467325440-40");
            map.put("3G/4G上网卡读卡器","PMC6357307310155467325440-40");
            map.put("笔记本电源适配器","PMC6354307310153412116480-40");
            map.put("笔记本电源适配器","PMC6354307310153412116480-40");
            map.put("POE","PMC6352307310152791359488-40");
            map.put("网络交换机","PMC6352307310152791359488-40");
            map.put("数码网络交换机","PMC6352307310152791359488-40");
            map.put("打字机、刻字机","PMC6369307310163830767616-40");
            map.put("碎纸机","PMC6369307310163830767616-40");
            map.put("点钞机","PMC6361307310158780825600-40");
            map.put("商用点钞机","PMC6361307310158780825600-40");
            map.put("点钞机配件","PMC6361307310158780825600-40");
            map.put("工业扫描仪","PMC6397307310182881296384-40");
            map.put("热敏打印机","PMC6397307310182881296384-40");
            map.put("条码打印机","PMC6397307310182881296384-40");
            map.put("扫描仪","PMC6397307310182881296384-40");
            map.put("喷墨打印机","PMC6397307310182881296384-40");
            map.put("墨仓式打印机","PMC6397307310182881296384-40");
            map.put("激光打印机","PMC6397307310182881296384-40");
            map.put("宽幅打印机","PMC6397307310182881296384-40");
            map.put("热升华照片打印机","PMC6397307310182881296384-40");
            map.put("热敏/热转印打印机","PMC6397307310182881296384-40");
            map.put("针式打印机","PMC6397307310182881296384-40");
            map.put("打印机配件","PMC6397307310182881296384-40");
            map.put("其他打印机","PMC6397307310182881296384-40");
            map.put("证卡打印机","PMC6397307310182881296384-40");
            map.put("扫描仪配件","PMC6397307310182881296384-40");
            map.put("美术颜料","PMC6387307310174744346624-40");
            map.put("其他美术用品","PMC6387307310174744346624-40");
            map.put("相纸","PMC8160307311305499344896-40");
            map.put("复印纸","PMC8160307311305499344896-40");
            map.put("打印纸","PMC8160307311305499344896-40");
            map.put("电脑连打纸","PMC8160307311305499344896-40");
            map.put("彩喷纸","PMC8160307311305499344896-40");
            map.put("传真纸","PMC8160307311305499344896-40");
            map.put("书写纸","PMC8160307311305499344896-40");
            map.put("描图、绘图纸","PMC8160307311305499344896-40");
            map.put("收银纸","PMC8160307311305499344896-40");
            map.put("其他办公用纸","PMC8160307311305499344896-40");
            map.put("信纸、信封","PMC8157307311303402192896-40");
            map.put("封杯、包装膜","PMC8154307311301938380800-40");
            map.put("其他塑料包装材料","PMC8154307311301938380800-40");
            map.put("其他包装用纸","PMC8154307311301938380800-40");
            map.put("其他纸类包装容器","PMC8154307311301938380800-40");
            map.put("塑料软管包装","PMC8154307311301938380800-40");
            map.put("塑料包装杯","PMC8154307311301938380800-40");
            map.put("其他塑料包装容器","PMC8154307311301938380800-40");
            map.put("金属软管包装","PMC8154307311301938380800-40");
            map.put("其他金属包装容器","PMC8154307311301938380800-40");
            map.put("其他金属包装材料","PMC8154307311301938380800-40");
            map.put("竹制包装","PMC8154307311301938380800-40");
            map.put("其他木质包装容器","PMC8154307311301938380800-40");
            map.put("辅助包装材料","PMC8154307311301938380800-40");
            map.put("包装制品配附件","PMC8154307311301938380800-40");
            map.put("其他布类包装袋","PMC8154307311301938380800-40");
            map.put("布类包装材料","PMC8154307311301938380800-40");
            map.put("冷链物流包装","PMC8154307311301938380800-40");
            map.put("其他玻璃包装容器","PMC8154307311301938380800-40");
            map.put("玻璃包装材料","PMC8154307311301938380800-40");
            map.put("电子元器件包装","PMC8154307311301938380800-40");
            map.put("五金配件包装","PMC8154307311301938380800-40");
            map.put("化工包装","PMC8154307311301938380800-40");
            map.put("烘焙包装","PMC8154307311301938380800-40");
            map.put("冲调保健包装","PMC8154307311301938380800-40");
            map.put("茶叶包装","PMC8154307311301938380800-40");
            map.put("其他食品包装","PMC8154307311301938380800-40");
            map.put("生鲜水果包装","PMC8154307311301938380800-40");
            map.put("坚果炒货包装","PMC8154307311301938380800-40");
            map.put("外卖餐饮包装","PMC8154307311301938380800-40");
            map.put("休闲食品包装","PMC8154307311301938380800-40");
            map.put("粮油米面包装","PMC8154307311301938380800-40");
            map.put("蜜饯果干包装","PMC8154307311301938380800-40");
            map.put("医药包装","PMC8154307311301938380800-40");
            map.put("服装、服饰包装","PMC8154307311301938380800-40");
            map.put("鞋靴包装","PMC8154307311301938380800-40");
            map.put("内衣裤包装","PMC8154307311301938380800-40");
            map.put("袜子包装","PMC8154307311301938380800-40");
            map.put("婴童装包装","PMC8154307311301938380800-40");
            map.put("服饰配件包装","PMC8154307311301938380800-40");
            map.put("礼品包装","PMC8154307311301938380800-40");
            map.put("香烟包装","PMC8154307311301938380800-40");
            map.put("其他日用包装","PMC8154307311301938380800-40");
            map.put("玩具包装","PMC8154307311301938380800-40");
            map.put("图书包装","PMC8154307311301938380800-40");
            map.put("家纺家饰包装","PMC8154307311301938380800-40");
            map.put("鲜花包装","PMC8154307311301938380800-40");
            map.put("汽车用品包装","PMC8154307311301938380800-40");
            map.put("化妆品包装","PMC8154307311301938380800-40");
            map.put("日化包装","PMC8154307311301938380800-40");
            map.put("酒包装","PMC8154307311301938380800-40");
            map.put("软饮料包装","PMC8154307311301938380800-40");
            map.put("其他酒水饮料包装","PMC8154307311301938380800-40");
            map.put("首饰包装","PMC8154307311301938380800-40");
            map.put("围巾包装","PMC8154307311301938380800-40");
            map.put("手表包装","PMC8154307311301938380800-40");
            map.put("数码3C包装","PMC8154307311301938380800-40");
            map.put("家用电器包装","PMC8154307311301938380800-40");
            map.put("手机防水包装","PMC8154307311301938380800-40");
            map.put("平板电脑防水包装","PMC8154307311301938380800-40");
            map.put("相机防水包装","PMC8154307311301938380800-40");
            map.put("耳机包装","PMC8154307311301938380800-40");
            map.put("数据线包装","PMC8154307311301938380800-40");
            map.put("数码3C包装","PMC8154307311301938380800-40");
            map.put("家用电器包装","PMC8154307311301938380800-40");
            map.put("手机防水包装","PMC8154307311301938380800-40");
            map.put("平板电脑防水包装","PMC8154307311301938380800-40");
            map.put("相机防水包装","PMC8154307311301938380800-40");
            map.put("耳机包装","PMC8154307311301938380800-40");
            map.put("数据线包装","PMC8154307311301938380800-40");
            map.put("养殖包装","PMC8154307311301938380800-40");
            map.put("种植包装","PMC8154307311301938380800-40");
            map.put("农产品包装","PMC8154307311301938380800-40");
            map.put("其它农业包装","PMC8154307311301938380800-40");
            map.put("互联包装","PMC8154307311301938380800-40");
            map.put("活性包装","PMC8154307311301938380800-40");
            map.put("其他会计用品","PMC8167307311310289240064-40");
            map.put("运动T恤、POLO衫","PMC8019307311221856534528-40");
            map.put("运动裤","PMC7988307311201509965824-40");
            map.put("其它运动鞋","PMC8150307311299069476864-40");
            map.put("综合训练鞋、室内健身鞋","PMC8150307311299069476864-40");
            map.put("童运动鞋","PMC8131307311287912628224-40");
            map.put("运动眼镜","PMC7942307311174263767040-40");
            map.put("秒表、码表、计时器","PMC7956307311182664957952-40");
            map.put("橄榄球服","PMC8110307311275233247232-40");
            map.put("游泳圈","PMC8121307311281700864000-40");
            map.put("游泳辅助用品","PMC8121307311281700864000-40");
            map.put("其他游泳装备","PMC8121307311281700864000-40");
            map.put("婴儿游泳池","PMC8121307311281700864000-40");
            map.put("其他健身器材","PMC7965307311188306296832-40");
            map.put("拉杆","PMC7971307311191900815360-40");
            map.put("拉杆","PMC7971307311191900815360-40");
            map.put("转向拉杆","PMC7971307311191900815360-40");
            map.put("防潮垫、地席、野餐垫","PMC8059307311245944422400-40");
            map.put("野餐垫、防潮垫","PMC8059307311245944422400-40");
            map.put("口袋巾","PMC7456307310881446821888-20");
            map.put("袖章","PMC7456307310881446821888-20");
            map.put("裙撑","PMC7456307310881446821888-20");
            map.put("运动颈环/手环/指环","PMC7456307310881446821888-20");
            map.put("护膝","PMC7456307310881446821888-20");
            map.put("矫正带","PMC7456307310881446821888-20");
            map.put("节庆服饰配件","PMC7456307310881446821888-20");
            map.put("婚庆服饰配件","PMC7456307310881446821888-20");
            map.put("DIY服饰配件","PMC7456307310881446821888-20");
            map.put("夜店服饰配件","PMC7456307310881446821888-20");
            map.put("cosplay服饰配件","PMC7456307310881446821888-20");
            map.put("工作制服","PMC7455307310880247250944-200");
            map.put("女式制服皮鞋","PMC7455307310880247250944-200");
            map.put("劳保制服","PMC7455307310880247250944-200");
            map.put("睡袍、浴袍","PMC7462307310884823236608-200");
            map.put("酒店浴袍","PMC7462307310884823236608-200");
            map.put("浴袍","PMC7462307310884823236608-200");
            map.put("大码小背心/吊带/抹胸","PMC8295307311387242135552-200");
            map.put("情侣装","PMC8269307311372058755072-200");
            map.put("连裤袜、打底袜、踩脚袜","PMC8256307311364555145216-200");
            map.put("抹胸/儿童文胸","PMC8255307311363674341376-200");
            map.put("情趣睡衣","PMC8284307311381084897280-200");
            map.put("性感透视睡衣","PMC8284307311381084897280-200");
            map.put("童家居裙/睡裙","PMC8283307311380342505472-200");
            map.put("睡裙","PMC8283307311380342505472-200");
            map.put("大码蕾丝衫、雪纺","PMC8239307311353092112384-40");
            map.put("泳裤","PMC8016307311219608387584-40");
            map.put("一次性内裤","PMC8009307311215841902592-40");
            map.put("童内裤","PMC8009307311215841902592-40");
            map.put("女士大码内裤","PMC8009307311215841902592-40");
            map.put("其它女士内裤","PMC8009307311215841902592-40");
            map.put("情趣内裤","PMC8009307311215841902592-40");
            map.put("男士大码内裤","PMC8009307311215841902592-40");
            map.put("男士U型/囊袋内裤","PMC8009307311215841902592-40");
            map.put("其它男士内裤","PMC8009307311215841902592-40");
            map.put("羊毛球","PMC8243307311356149760000-40");
            map.put("其他丝绸面料","PMC8242307311355482865664-40");
            map.put("其他皮革废料","PMC8236307311351863181312-40");
            map.put("猪皮革","PMC8236307311351863181312-40");
            map.put("羊皮革","PMC8236307311351863181312-40");
            map.put("马皮革","PMC8236307311351863181312-40");
            map.put("牛皮革","PMC8236307311351863181312-40");
            map.put("其他皮革","PMC8236307311351863181312-40");
            map.put("比基尼、分体泳衣","PMC8008307311215246311424-40");
            map.put("亲子泳装","PMC8015307311219050545152-40");
            map.put("三件套泳装","PMC8015307311219050545152-40");
            map.put("帆布","PMC8232307311349317238784-40");
            map.put("羽绒连体衣","PMC8011307311217272160256-40");
            map.put("童家居服连体衣","PMC8011307311217272160256-40");
            map.put("塑身连体衣","PMC8011307311217272160256-40");
            map.put("电蒸锅","PMC7284307310775247044608-40");
            map.put("电蒸锅","PMC7284307310775247044608-40");
            map.put("真空压缩袋","PMC7102307310658821554176-40");
            map.put("嵌入式微波炉","PMC7277307310770905939968-40");
            map.put("微波炉","PMC7277307310770905939968-40");
            map.put("微波炉","PMC7277307310770905939968-40");
            map.put("工业手持气动搅拌机","PMC7274307310769416962048-40");
            map.put("榨汁机","PMC7274307310769416962048-40");
            map.put("榨汁机","PMC7274307310769416962048-40");
            map.put("搅拌机","PMC7274307310769416962048-40");
            map.put("其他热水器","PMC7323307310799611756544-40");
            map.put("电热水器","PMC7323307310799611756544-40");
            map.put("即热式热水器","PMC7323307310799611756544-40");
            map.put("太阳能热水器","PMC7323307310799611756544-40");
            map.put("燃气热水器","PMC7323307310799611756544-40");
            map.put("空气能热水器","PMC7323307310799611756544-40");
            map.put("洗衣机","PMC7319307310797564936192-40");
            map.put("家用烘干机","PMC7319307310797564936192-40");
            map.put("冰箱配件、附件","PMC7297307310783333662720-40");
            map.put("抽油烟机","PMC7273307310768712318976-40");
            map.put("工业烤箱","PMC7293307310781244899328-40");
            map.put("嵌入式微烤箱","PMC7293307310781244899328-40");
            map.put("电烤箱","PMC7293307310781244899328-40");
            map.put("电烤箱","PMC7293307310781244899328-40");
            map.put("洗碗机","PMC7272307310768016064512-40");
            map.put("商用洗碗机","PMC7272307310768016064512-40");
            map.put("家电清洁剂","PMC7318307310796759629824-40");
            map.put("家电用电动机","PMC7318307310796759629824-40");
            map.put("其他大家电","PMC7318307310796759629824-40");
            map.put("其他家电配件","PMC7318307310796759629824-40");
            map.put("家电、家具、日用品展","PMC7318307310796759629824-40");
            map.put("壁挂洗衣机","PMC7322307310799024553984-40");
            map.put("洗衣机脱水机配件、附件","PMC7322307310799024553984-40");
            map.put("折叠洗衣机","PMC7322307310799024553984-40");
            map.put("电风扇","PMC7307307310789931302912-40");
            map.put("露营风扇","PMC7307307310789931302912-40");
            map.put("迷你电风扇","PMC7307307310789931302912-40");
            map.put("其他电风扇","PMC7307307310789931302912-40");
            map.put("大功率风扇、工业电风扇","PMC7307307310789931302912-40");
            map.put("USB风扇、迷你风扇","PMC7307307310789931302912-40");
            map.put("风扇","PMC7307307310789931302912-40");
            map.put("其他制冷设备","PMC7310307310791214759936-40");
            map.put("其他食品、饮料加工设备","PMC7317307310795966906368-40");
            map.put("家用食品保鲜机","PMC7317307310795966906368-40");
            map.put("食品药品包装机","PMC7317307310795966906368-40");
            map.put("车载手机充电器","PMC7314307310794083663872-40");
            map.put("其他汽车小电器","PMC7314307310794083663872-40");
            map.put("充电器插头","PMC7314307310794083663872-40");
            map.put("电池充电器","PMC7314307310794083663872-40");
            map.put("电动车充电器","PMC7314307310794083663872-40");
            map.put("平衡车充电器","PMC7314307310794083663872-40");
            map.put("滑板车充电器","PMC7314307310794083663872-40");
            map.put("车载充电器","PMC7314307310794083663872-40");
            map.put("笔记本充电器","PMC7314307310794083663872-40");
            map.put("无线充电器","PMC7314307310794083663872-40");
            map.put("旅行充电器","PMC7314307310794083663872-40");
            map.put("耳机充电器","PMC7314307310794083663872-40");
            map.put("无人机充电器","PMC7314307310794083663872-40");
            map.put("氮化镓充电器","PMC7314307310794083663872-40");
            map.put("其他充电器","PMC7314307310794083663872-40");
            map.put("其他低压电器","PMC7314307310794083663872-40");
            map.put("高压成套电器","PMC7314307310794083663872-40");
            map.put("其他高压电器","PMC7314307310794083663872-40");
            map.put("电器连接线","PMC7314307310794083663872-40");
            map.put("其他影音电器","PMC7314307310794083663872-40");
            map.put("保健电器配件","PMC7314307310794083663872-40");
            map.put("足浴电器","PMC7314307310794083663872-40");
            map.put("两季电器配件","PMC7314307310794083663872-40");
            map.put("其他取暖电器","PMC7314307310794083663872-40");
            map.put("其他夏季电器","PMC7314307310794083663872-40");
            map.put("个护电器配件","PMC7314307310794083663872-40");
            map.put("生活电器配件、附件","PMC7314307310794083663872-40");
            map.put("其他生活电器","PMC7314307310794083663872-40");
            map.put("其他卫浴洗漱用具","PMC6788307310428487155712-200");
            map.put("卫浴扶手","PMC6789307310429116301312-200");
            map.put("卫浴置物架","PMC6789307310429116301312-200");
            map.put("卫浴套件","PMC6789307310429116301312-200");
            map.put("沐浴液、沐浴露","PMC6787307310427727986688-200");
            map.put("沐浴套装","PMC6787307310427727986688-200");
            map.put("一次性洗漱、沐浴用品","PMC6787307310427727986688-200");
            map.put("沐浴球、浴擦、浴刷、澡巾","PMC6787307310427727986688-200");
            map.put("泡脚盆、沐浴桶、折叠浴桶","PMC6787307310427727986688-200");
            map.put("浴帽","PMC6785307310426360643584-200");
            map.put("挂毯/壁毯","PMC6843307310464293928960-200");
            map.put("和田玉挂饰","PMC6854307310471424245760-200");
            map.put("挂饰挂件","PMC6854307310471424245760-200");
            map.put("经文挂饰","PMC6854307310471424245760-200");
            map.put("香薰蜡烛工具","PMC6839307310462154833920-200");
            map.put("LED蜡烛灯","PMC6839307310462154833920-200");
            map.put("祭祀蜡烛","PMC6839307310462154833920-200");
            map.put("蜡烛","PMC6839307310462154833920-200");
            map.put("蜡烛灯","PMC6839307310462154833920-200");
            map.put("烛台、蜡烛器皿","PMC6838307310460837822464-200");
            map.put("冰箱贴","PMC6750307310406546751488-200");
            map.put("招财摆件","PMC6905307310508615139328-200");
            map.put("镇宅摆件","PMC6905307310508615139328-200");
            map.put("风水球","PMC6905307310508615139328-200");
            map.put("水桶、水缸","PMC6995307310580178354176-200");
            map.put("茶水桶","PMC6995307310580178354176-200");
            map.put("乒乓套胶、海绵、单胶片","PMC7020307310599656701952-200");
            map.put("海绵擦","PMC7020307310599656701952-200");
            map.put("抹布、百洁布、洗碗巾","PMC7020307310599656701952-200");
            map.put("商用百洁布、擦拭布","PMC7020307310599656701952-200");
            map.put("废海绵","PMC7020307310599656701952-200");
            map.put("工业百洁布、擦拭布","PMC7020307310599656701952-200");
            map.put("海绵砂","PMC7020307310599656701952-200");
            map.put("洗衣球","PMC7010307310590118854656-200");
            map.put("摄影背景","PMC7127307310673648418816-200");
            map.put("摄影摄像背景","PMC7127307310673648418816-200");
            map.put("行李牌","PMC7129307310675246448640-40");
            map.put("行李牌","PMC7129307310675246448640-40");
            map.put("警示牌","PMC7129307310675246448640-40");
            map.put("临时停车牌","PMC7129307310675246448640-40");
            map.put("礼宾牌","PMC7129307310675246448640-40");
            map.put("酒店指示牌","PMC7129307310675246448640-40");
            map.put("记分牌、换人牌、战术板","PMC7129307310675246448640-40");
            map.put("多米诺骨牌","PMC7129307310675246448640-40");
            map.put("卡片、卡牌、闪卡玩具","PMC7129307310675246448640-40");
            map.put("广告牌","PMC7129307310675246448640-40");
            map.put("门牌","PMC7129307310675246448640-40");
            map.put("门牌","PMC7129307310675246448640-40");
            map.put("LED广告牌","PMC7129307310675246448640-40");
            map.put("LED招牌","PMC7129307310675246448640-40");
            map.put("LED告示牌","PMC7129307310675246448640-40");
            map.put("装饰挂牌","PMC7129307310675246448640-40");
            map.put("动漫/明星立牌","PMC7129307310675246448640-40");
            map.put("应援灯牌","PMC7129307310675246448640-40");
            map.put("佛牌","PMC7129307310675246448640-40");
            map.put("无事牌","PMC7129307310675246448640-40");
            map.put("许愿牌","PMC7129307310675246448640-40");
            map.put("撕名牌贴","PMC7129307310675246448640-40");
            map.put("台牌/台签","PMC7129307310675246448640-40");
            map.put("铭牌","PMC7129307310675246448640-40");
            map.put("其他标签、标牌","PMC7129307310675246448640-40");
            map.put("防爆盾牌","PMC7129307310675246448640-40");
            map.put("A字牌","PMC7129307310675246448640-40");
            map.put("吊牌","PMC7129307310675246448640-40");
            map.put("一次性餐具套装","PMC7130307310675951091712-200");
            map.put("面具","PMC7138307310681869254656-200");
            map.put("防毒面具","PMC7138307310681869254656-200");
            map.put("蛋糕装饰配件","PMC7134307310679612719104-200");
            map.put("电热水瓶","PMC6882307310489786908672-200");
            map.put("冰袋、冰包、野餐包","PMC6993307310578727124992-20");
            map.put("打火机配件","PMC7074307310641629102080-200");
            map.put("咖啡过滤器","PMC7108307310662558679040-40");
            map.put("咖啡研磨器、压粉器","PMC7113307310665154953216-40");
            map.put("茶壶","PMC6901307310503925907456-40");
            map.put("茶具套装","PMC6899307310502072025088-40");
            map.put("其他茶具","PMC6899307310502072025088-40");
            map.put("茶具配件","PMC6899307310502072025088-40");
            map.put("旅行茶具","PMC6899307310502072025088-40");
            map.put("功夫茶具","PMC6899307310502072025088-40");
            map.put("花草茶具","PMC6899307310502072025088-40");
            map.put("电茶炉、电茶具","PMC6899307310502072025088-40");
            map.put("电茶炉、电茶具","PMC6899307310502072025088-40");
            map.put("磨刀石","PMC7073307310640848961536-40");
            map.put("布类公仔、玩偶、娃娃","PMC7673307311009154990080-40");
            map.put("搪胶/塑胶公仔、玩偶、娃娃","PMC7673307311009154990080-40");
            map.put("玩偶","PMC7673307311009154990080-40");
            map.put("其他公仔、玩偶、娃娃","PMC7673307311009154990080-40");
            map.put("厨用剪刀","PMC7069307310638206550016-40");
            map.put("娃娃屋","PMC7671307311008504872960-40");
            map.put("烧烤食材","PMC7063307310633630564352-40");
            map.put("烧烤炉","PMC7062307310632573599744-40");
            map.put("烧烤架、烧烤网","PMC7062307310632573599744-40");
            map.put("其他烧烤用具","PMC7062307310632573599744-40");
            map.put("烧烤叉、签","PMC7062307310632573599744-40");
            map.put("烧烤盘","PMC7062307310632573599744-40");
            map.put("烧烤刷","PMC7062307310632573599744-40");
            map.put("烧烤炭","PMC7062307310632573599744-40");
            map.put("烧烤挡风板","PMC7062307310632573599744-40");
            map.put("电烧烤炉","PMC7062307310632573599744-40");
            map.put("电烧烤炉","PMC7062307310632573599744-40");
            map.put("仿真食物工艺品","PMC7677307311012019699712-40");
            map.put("仿真人物工艺品","PMC7677307311012019699712-40");
            map.put("仿真动物工艺品","PMC7677307311012019699712-40");
            map.put("刺绣织绣工艺品","PMC7677307311012019699712-40");
            map.put("水晶工艺品","PMC7677307311012019699712-40");
            map.put("玻璃工艺品","PMC7677307311012019699712-40");
            map.put("漆器工艺品","PMC7677307311012019699712-40");
            map.put("金属工艺品","PMC7677307311012019699712-40");
            map.put("软陶工艺品","PMC7677307311012019699712-40");
            map.put("陶瓷工艺品","PMC7677307311012019699712-40");
            map.put("玉器工艺品","PMC7677307311012019699712-40");
            map.put("宝石工艺品","PMC7677307311012019699712-40");
            map.put("树脂工艺品","PMC7677307311012019699712-40");
            map.put("塑料工艺品","PMC7677307311012019699712-40");
            map.put("石膏、石料工艺品","PMC7677307311012019699712-40");
            map.put("泥塑工艺品","PMC7677307311012019699712-40");
            map.put("纸质工艺品","PMC7677307311012019699712-40");
            map.put("木质工艺品","PMC7677307311012019699712-40");
            map.put("竹质工艺品","PMC7677307311012019699712-40");
            map.put("植物工艺品","PMC7677307311012019699712-40");
            map.put("皮毛工艺品","PMC7677307311012019699712-40");
            map.put("炭雕工艺品","PMC7677307311012019699712-40");
            map.put("骨雕/牙雕工艺品","PMC7677307311012019699712-40");
            map.put("其他材质工艺品","PMC7677307311012019699712-40");
            map.put("工艺品代理加盟","PMC7677307311012019699712-40");
            map.put("绒沙金工艺品","PMC7677307311012019699712-40");
            map.put("仿玉工艺品","PMC7677307311012019699712-40");
            map.put("琉璃工艺品","PMC7677307311012019699712-40");
            map.put("紫砂工艺品","PMC7677307311012019699712-40");
            map.put("铜雕工艺品","PMC7677307311012019699712-40");
            map.put("仿真书工艺品","PMC7677307311012019699712-40");
            map.put("其他民间工艺品","PMC7677307311012019699712-40");
            map.put("礼品、饰品、工艺品展","PMC7677307311012019699712-40");
            map.put("布艺工艺品","PMC7677307311012019699712-40");
            map.put("其他形状认知/配对玩具","PMC7688307311018575396864-40");
            map.put("几何形状套柱","PMC7688307311018575396864-40");
            map.put("数字屋/形状盒/多孔认知玩具","PMC7688307311018575396864-40");
            map.put("音乐盒","PMC7684307311016419524608-40");
            map.put("煎锅、平底锅","PMC7100307310657378713600-40");
            map.put("炒锅","PMC7100307310657378713600-40");
            map.put("煎锅、平底锅","PMC7100307310657378713600-40");
            map.put("炒锅","PMC7100307310657378713600-40");
            map.put("蒸笼、蒸格","PMC7097307310655965233152-40");
            map.put("家用电蒸笼","PMC7097307310655965233152-40");
            map.put("家用电蒸笼","PMC7097307310655965233152-40");
            map.put("电压力锅","PMC7294307310781945348096-40");
            map.put("电压力锅","PMC7294307310781945348096-40");
            map.put("户外炊具","PMC7131307310676647346176-40");
            map.put("餐巾纸","PMC6890307310495692488704-40");
            map.put("热水壶/瓶","PMC6883307310490604797952-40");
            map.put("冷水壶","PMC6883307310490604797952-40");
            map.put("不锈钢水壶、鸣音壶","PMC6883307310490604797952-40");
            map.put("电热水壶","PMC6883307310490604797952-40");
            map.put("电热水壶","PMC6883307310490604797952-40");
            map.put("园林水壶","PMC6883307310490604797952-40");
            map.put("酒壶","PMC6888307310493947658240-40");
            map.put("帐篷","PMC7720307311038372511744-40");
            map.put("帐篷、天幕、帐篷配件","PMC7720307311038372511744-40");
            map.put("隧道灯具","PMC7720307311038372511744-40");
            map.put("LED隧道灯模组","PMC7720307311038372511744-40");
            map.put("隧道检测仪器","PMC7720307311038372511744-40");
            map.put("调味盒、调料瓶","PMC7096307310655243812864-40");
            map.put("玩具枪","PMC7710307311032848613376-40");
            map.put("披萨铲","PMC7093307310653247324160-40");
            map.put("披萨刀","PMC7093307310653247324160-40");
            map.put("家用冰淇淋机","PMC7086307310649233375232-40");
            map.put("家用冰淇淋机","PMC7086307310649233375232-40");
            map.put("鸡蛋仔模具","PMC7082307310647161389056-40");
            map.put("鸡蛋杯","PMC7082307310647161389056-40");
            map.put("鸡蛋杯","PMC7082307310647161389056-40");
            map.put("飞行器玩具","PMC7713307311034476003328-40");
            map.put("风筝","PMC7715307311035788820480-40");
            map.put("刨子、削皮器","PMC7059307310629209767936-40");
            map.put("船用仪器仪表","PMC7729307311044026433536-40");
            map.put("二手船舶","PMC7729307311044026433536-40");
            map.put("船锚、锚链","PMC7729307311044026433536-40");
            map.put("其他船舶专用配件","PMC7729307311044026433536-40");
            map.put("动物性饲料","PMC6770307310417972035584-40");
            map.put("其他特种养殖动物","PMC6770307310417972035584-40");
            map.put("其他特种养殖动物","PMC6770307310417972035584-40");
            map.put("动物/植物仿真模型","PMC6770307310417972035584-40");
            map.put("动物毛鬃","PMC6770307310417972035584-40");
            map.put("动物胶","PMC6770307310417972035584-40");
            map.put("动物胶","PMC6770307310417972035584-40");
            map.put("遥控坦克","PMC7735307311047457374208-40");
            map.put("关节型机器人","PMC7703307311028297793536-40");
            map.put("线性机器人","PMC7703307311028297793536-40");
            map.put("并联机器人","PMC7703307311028297793536-40");
            map.put("协作机器人","PMC7703307311028297793536-40");
            map.put("擦窗机器人","PMC7703307311028297793536-40");
            map.put("扫拖吸机器人","PMC7703307311028297793536-40");
            map.put("3C智能机器人","PMC7703307311028297793536-40");
            map.put("工业清洗机器人","PMC7703307311028297793536-40");
            map.put("智力魔方","PMC6773307310419893026816-40");
            map.put("拼图、拼板","PMC6759307310411982569472-40");
            map.put("骰子、筹码","PMC6758307310411370201088-40");
            map.put("扑克、桌游牌","PMC6756307310410019635200-40");
            map.put("棋牌、桌游玩具","PMC6756307310410019635200-40");
            map.put("杂技用具","PMC6760307310412901122048-40");
            map.put("舞台演出魔术道具","PMC6760307310412901122048-40");
            map.put("其它新奇玩具","PMC6766307310416143319040-40");
            map.put("布质/软胶积木","PMC7668307311005958930432-40");
            map.put("中颗粒积木","PMC7668307311005958930432-40");
            map.put("电子积木","PMC7668307311005958930432-40");
            map.put("大颗粒积木","PMC7668307311005958930432-40");
            map.put("积木类","PMC7668307311005958930432-40");
            map.put("磁力片/磁力球/磁力积木","PMC7668307311005958930432-40");
            map.put("减压玩具","PMC7706307311029929377792-40");
            map.put("扭蛋","PMC6753307310408467742720-40");
            map.put("陀螺","PMC7700307311025856708608-40");
            map.put("键盘类乐器","PMC6723307310390713253888-40");
            map.put("键盘架","PMC6723307310390713253888-40");
            map.put("手卷钢琴","PMC6723307310390713253888-40");
            map.put("数码钢琴","PMC6723307310390713253888-40");
            map.put("钢琴","PMC6723307310390713253888-40");
            map.put("架子鼓/小军鼓/打击乐器玩具","PMC6729307310394634928128-40");
            map.put("民谣吉他弦","PMC6730307310395326988288-40");
            map.put("古典吉他弦","PMC6730307310395326988288-40");
            map.put("电吉他弦","PMC6730307310395326988288-40");
            map.put("身体护理工具","PMC6173307310037838069760-200");
            map.put("其他身体护理用品","PMC6173307310037838069760-200");
            map.put("男士身体清洁","PMC6173307310037838069760-200");
            map.put("男士身体护理","PMC6173307310037838069760-200");
            map.put("防晒霜、防晒喷雾","PMC6187307310046893572096-200");
            map.put("电动剃须刀","PMC6204307310057043787776-200");
            map.put("电动牙刷充电器","PMC6205307310057672933376-200");
            map.put("电动牙刷","PMC6205307310057672933376-200");
            map.put("口腔清洁","PMC6207307310059237408768-200");
            map.put("卷发器、直发器","PMC6194307310051159179264-200");
            map.put("老花镜","PMC6658307310350154334208-200");
            map.put("防护耳塞","PMC6650307310345364439040-200");
            map.put("卫生棉条","PMC6666307310354914869248-200");
            map.put("书桌","PMC6925307310524977119232-40");
            map.put("书桌","PMC6925307310524977119232-40");
            map.put("书桌+写字椅","PMC6925307310524977119232-40");
            map.put("书桌椅+书柜","PMC6925307310524977119232-40");
            map.put("书桌椅+书柜+休闲沙发","PMC6925307310524977119232-40");
            map.put("书桌+写字椅","PMC6925307310524977119232-40");
            map.put("书桌椅+书柜","PMC6925307310524977119232-40");
            map.put("书桌椅+书柜+休闲沙发","PMC6925307310524977119232-40");
            map.put("定制衣柜","PMC6928307310526751309824-40");
            map.put("简易衣柜","PMC6928307310526751309824-40");
            map.put("衣柜床","PMC6928307310526751309824-40");
            map.put("衣柜","PMC6928307310526751309824-40");
            map.put("双人床+床头柜+衣柜","PMC6928307310526751309824-40");
            map.put("单人床+床头柜+衣柜","PMC6928307310526751309824-40");
            map.put("子母床+写字桌+衣柜","PMC6928307310526751309824-40");
            map.put("双人床+床头柜+梳妆台+衣柜","PMC6928307310526751309824-40");
            map.put("单人床+床头柜+梳妆台+衣柜","PMC6928307310526751309824-40");
            map.put("双人床+床头柜+衣柜","PMC6928307310526751309824-40");
            map.put("单人床+床头柜+衣柜","PMC6928307310526751309824-40");
            map.put("子母床+写字桌+衣柜","PMC6928307310526751309824-40");
            map.put("衣柜贴","PMC6928307310526751309824-40");
            map.put("儿童床头柜","PMC6950307310543406891008-40");
            map.put("床头柜","PMC6950307310543406891008-40");
            map.put("茶几+电视柜","PMC6950307310543406891008-40");
            map.put("沙发+茶几+电视柜","PMC6950307310543406891008-40");
            map.put("沙发+茶几+电视柜+角几","PMC6950307310543406891008-40");
            map.put("双人床+床头柜","PMC6950307310543406891008-40");
            map.put("单人床+床头柜","PMC6950307310543406891008-40");
            map.put("双人床+床头柜+梳妆台","PMC6950307310543406891008-40");
            map.put("单人床+床头柜+梳妆台","PMC6950307310543406891008-40");
            map.put("茶几+电视柜","PMC6950307310543406891008-40");
            map.put("沙发+茶几+电视柜","PMC6950307310543406891008-40");
            map.put("沙发+茶几+电视柜+角几","PMC6950307310543406891008-40");
            map.put("双人床+床头柜","PMC6950307310543406891008-40");
            map.put("单人床+床头柜","PMC6950307310543406891008-40");
            map.put("双人床+床头柜+梳妆台","PMC6950307310543406891008-40");
            map.put("单人床+床头柜+梳妆台","PMC6950307310543406891008-40");
            map.put("户外沙发","PMC6957307310548511358976-40");
            map.put("户外实木桌椅","PMC6923307310522758332416-40");
            map.put("户外折叠桌椅","PMC6923307310522758332416-40");
            map.put("户外金属桌椅","PMC6923307310522758332416-40");
            map.put("儿童沙发","PMC6954307310546972049408-40");
            map.put("橱柜台面","PMC6917307310517825830912-40");
            map.put("其他成套家具","PMC6931307310529427275776-40");
            map.put("儿童衣柜","PMC6914307310516114554880-40");
            map.put("酒店家具","PMC6933307310531507650560-40");
            map.put("酒店家具","PMC6933307310531507650560-40");
            map.put("学校课桌椅","PMC6948307310542593196032-40");
            map.put("餐厅沙发","PMC6945307310539669766144-40");
            map.put("办公柜","PMC6936307310533604802560-40");
            map.put("办公桌","PMC6936307310533604802560-40");
            map.put("办公案、台","PMC6936307310533604802560-40");
            map.put("办公椅","PMC6936307310533604802560-40");
            map.put("办公沙发","PMC6936307310533604802560-40");
            map.put("办公屏风","PMC6936307310533604802560-40");
            map.put("办公柜","PMC6936307310533604802560-40");
            map.put("办公桌","PMC6936307310533604802560-40");
            map.put("办公案、台","PMC6936307310533604802560-40");
            map.put("办公椅","PMC6936307310533604802560-40");
            map.put("办公沙发","PMC6936307310533604802560-40");
            map.put("办公屏风","PMC6936307310533604802560-40");
            map.put("气动铆钉枪","PMC7216307310733673103360-40");
            map.put("气动喷枪","PMC7235307310744347607040-40");
            map.put("喷枪","PMC7235307310744347607040-40");
            map.put("喷枪","PMC7235307310744347607040-40");
            map.put("气动胶枪","PMC7201307310724013621248-40");
            map.put("电动机配件","PMC7223307310737544445952-40");
            map.put("其他电动车配件","PMC7223307310737544445952-40");
            map.put("电动窗帘整体套装","PMC7224307310738240700416-40");
            map.put("齿轮刀具","PMC7210307310729759817728-40");
            map.put("螺纹刀具","PMC7210307310729759817728-40");
            map.put("数控刀具","PMC7210307310729759817728-40");
            map.put("刀具配附件","PMC7210307310729759817728-40");
            map.put("其他刀具","PMC7210307310729759817728-40");
            map.put("车削刀具","PMC7210307310729759817728-40");
            map.put("铣削刀具","PMC7210307310729759817728-40");
            map.put("观赏刀具","PMC7210307310729759817728-40");
            map.put("机床用虎钳","PMC7221307310736319709184-40");
            map.put("路亚钳","PMC7221307310736319709184-40");
            map.put("瓜子钳","PMC7221307310736319709184-40");
            map.put("气动钳子","PMC7221307310736319709184-40");
            map.put("起重钳","PMC7221307310736319709184-40");
            map.put("液压压线钳","PMC7221307310736319709184-40");
            map.put("液压断线钳","PMC7221307310736319709184-40");
            map.put("管子钳","PMC7221307310736319709184-40");
            map.put("电焊钳","PMC7221307310736319709184-40");
            map.put("坩埚钳","PMC7221307310736319709184-40");
            map.put("指甲剪、钳、刀","PMC7221307310736319709184-40");
            map.put("断线钳","PMC7221307310736319709184-40");
            map.put("剥线钳","PMC7221307310736319709184-40");
            map.put("卡簧钳","PMC7221307310736319709184-40");
            map.put("钢丝钳","PMC7221307310736319709184-40");
            map.put("尖嘴钳","PMC7221307310736319709184-40");
            map.put("斜口钳","PMC7221307310736319709184-40");
            map.put("管子钳","PMC7221307310736319709184-40");
            map.put("大力钳","PMC7221307310736319709184-40");
            map.put("鲤鱼钳","PMC7221307310736319709184-40");
            map.put("水泵钳","PMC7221307310736319709184-40");
            map.put("压接钳","PMC7221307310736319709184-40");
            map.put("圆嘴钳","PMC7221307310736319709184-40");
            map.put("顶切钳","PMC7221307310736319709184-40");
            map.put("扁嘴钳","PMC7221307310736319709184-40");
            map.put("台虎钳","PMC7221307310736319709184-40");
            map.put("平口钳","PMC7221307310736319709184-40");
            map.put("紧线钳","PMC7221307310736319709184-40");
            map.put("线缆钳","PMC7221307310736319709184-40");
            map.put("电工钳","PMC7221307310736319709184-40");
            map.put("弯嘴钳","PMC7221307310736319709184-40");
            map.put("切割钳","PMC7221307310736319709184-40");
            map.put("其他钳类","PMC7221307310736319709184-40");
            map.put("水口钳","PMC7221307310736319709184-40");
            map.put("多功能钳","PMC7221307310736319709184-40");
            map.put("打孔钳","PMC7221307310736319709184-40");
            map.put("保险丝钳","PMC7221307310736319709184-40");
            map.put("油锯","PMC7229307310740962803712-40");
            map.put("手锯、木工锯","PMC7229307310740962803712-40");
            map.put("手用锯条","PMC7229307310740962803712-40");
            map.put("机用锯条","PMC7229307310740962803712-40");
            map.put("钢锯架","PMC7229307310740962803712-40");
            map.put("其他锯","PMC7229307310740962803712-40");
            map.put("家用剪刀","PMC7230307310741587755008-40");
            map.put("医用剪刀","PMC7230307310741587755008-40");
            map.put("其他剪刀","PMC7230307310741587755008-40");
            map.put("医用剪刀","PMC7230307310741587755008-40");
            map.put("其他剪刀","PMC7230307310741587755008-40");
            map.put("办公剪刀","PMC7230307310741587755008-40");
            map.put("宝宝剪刀/指甲钳","PMC7230307310741587755008-40");
            map.put("气动螺丝刀","PMC7231307310742296592384-40");
            map.put("一字螺丝刀","PMC7231307310742296592384-40");
            map.put("米字螺丝刀","PMC7231307310742296592384-40");
            map.put("梅花螺丝刀","PMC7231307310742296592384-40");
            map.put("星型螺丝刀","PMC7231307310742296592384-40");
            map.put("六角螺丝刀","PMC7231307310742296592384-40");
            map.put("十字螺丝刀","PMC7231307310742296592384-40");
            map.put("棘轮螺丝刀","PMC7231307310742296592384-40");
            map.put("其他手动螺丝刀","PMC7231307310742296592384-40");
            map.put("三角螺丝刀","PMC7231307310742296592384-40");
            map.put("泡沫轴","PMC7187307310715817951232-40");
            map.put("其他光学仪器","PMC7217307310734352580608-40");
            map.put("光学检测仪","PMC7217307310734352580608-40");
            map.put("光学机具及配件","PMC7217307310734352580608-40");
            map.put("压力计","PMC7225307310738911789056-40");
            map.put("压力表","PMC7225307310738911789056-40");
            map.put("压力校验仪表","PMC7225307310738911789056-40");
            map.put("压力控制器","PMC7225307310738911789056-40");
            map.put("其他压力仪表","PMC7225307310738911789056-40");
            map.put("温度校验仪表","PMC7236307310745010307072-40");
            map.put("静电测试仪","PMC7197307310722042298368-40");
            map.put("车用吸尘器","PMC6974307310564537794560-40");
            map.put("铲子","PMC6978307310567750631424-40");
            map.put("鼻毛修剪器","PMC6976307310566249070592-40");
            map.put("毛球修剪器","PMC6976307310566249070592-40");
            map.put("耙子","PMC6972307310563673767936-40");
            map.put("锄头","PMC6972307310563673767936-40");
            map.put("园艺护栏","PMC6968307310559517212672-40");
            map.put("园艺护栏","PMC6968307310559517212672-40");
            map.put("电烙铁","PMC7195307310720662372352-40");
            map.put("焊台","PMC7232307310742959292416-40");
            map.put("焊工手套","PMC7244307310750064443392-40");
            map.put("维修工具箱","PMC7238307310746750943232-40");
            map.put("家具五金","PMC7200307310723350921216-40");
            map.put("门碰、门吸","PMC7150307310691574874112-40");
            map.put("门夹","PMC7150307310691574874112-40");
            map.put("其他门窗配件","PMC7150307310691574874112-40");
            map.put("门窗密封条","PMC7150307310691574874112-40");
            map.put("纱窗、纱门型材","PMC7150307310691574874112-40");
            map.put("门窗滑轮","PMC7228307310740237189120-40");
            map.put("链条","PMC7228307310740237189120-40");
            map.put("链子/链条","PMC7228307310740237189120-40");
            map.put("起重链条","PMC7228307310740237189120-40");
            map.put("钢丝绳索具","PMC7228307310740237189120-40");
            map.put("滑轮","PMC7228307310740237189120-40");
            map.put("其他绳索、扎带","PMC7228307310740237189120-40");
            map.put("滑轮","PMC7228307310740237189120-40");
            map.put("箱包挂锁","PMC7218307310735019474944-40");
            map.put("箱包挂锁","PMC7218307310735019474944-40");
            map.put("挂锁","PMC7218307310735019474944-40");
            map.put("肩带、搭扣","PMC7218307310735019474944-40");
            map.put("其他建筑用粘合剂","PMC7154307310695047757824-40");
            map.put("电工胶带","PMC7154307310695047757824-40");
            map.put("其他橡胶带","PMC7154307310695047757824-40");
            map.put("和纸胶带","PMC7154307310695047757824-40");
            map.put("办公用胶带","PMC7154307310695047757824-40");
            map.put("胶带座","PMC7154307310695047757824-40");
            map.put("工业产品胶带","PMC7154307310695047757824-40");
            map.put("办公用品胶带","PMC7154307310695047757824-40");
            map.put("其他胶带","PMC7154307310695047757824-40");
            map.put("环保产品胶带","PMC7154307310695047757824-40");
            map.put("交通汽车胶带","PMC7154307310695047757824-40");
            map.put("特殊胶带","PMC7154307310695047757824-40");
            map.put("住房建筑胶带","PMC7154307310695047757824-40");
            map.put("医用胶带","PMC7154307310695047757824-40");
            map.put("电子产品胶带","PMC7154307310695047757824-40");
            map.put("封装打包胶带","PMC7154307310695047757824-40");
            map.put("纺织印染用粘合剂","PMC7154307310695047757824-40");
            map.put("工装夹具","PMC7190307310717717970944-40");
            map.put("男式大码下装","PMC6100307309993340698624-40");
            map.put("男式中老年下装","PMC6100307309993340698624-40");
            map.put("儿童牛仔外套","PMC6107307309997459505152-40");
            map.put("普通外套","PMC6107307309997459505152-40");
            map.put("尿布带","PMC7578307310953119088640-40");
            map.put("防溢乳垫","PMC7613307310973302079488-40");
            map.put("围嘴围兜/口水巾","PMC7602307310966838657024-40");
            map.put("学步车","PMC7654307310997599682560-40");
            map.put("玩具学步车","PMC7654307310997599682560-40");
            map.put("爬行垫/游戏垫","PMC7718307311037714006016-40");
            map.put("儿童游戏围栏","PMC7721307311039236538368-40");
            map.put("活动围栏","PMC7721307311039236538368-40");
            map.put("牙胶","PMC7617307310975285985280-40");
            map.put("学习机/早教机/早教智能","PMC7680307311013651283968-40");
            map.put("早教书籍","PMC7680307311013651283968-40");
            map.put("早教闪卡/潜能开发卡","PMC7680307311013651283968-40");
            map.put("儿童早教教具/套装","PMC7680307311013651283968-40");
            map.put("车用香水","PMC7585307310957095288832-40");
            map.put("香水座","PMC7585307310957095288832-40");
            map.put("香水加工定制","PMC7585307310957095288832-40");
            map.put("女士香水","PMC7585307310957095288832-40");
            map.put("男士香水","PMC7585307310957095288832-40");
            map.put("中性香水","PMC7585307310957095288832-40");
            map.put("其他香水","PMC7585307310957095288832-40");
            map.put("孕妇枕","PMC7621307310977861287936-40");
            map.put("照明电筒配件","PMC7120307310669668024320-200");
            map.put("其他管材","PMC7116307310667315019776-200");
            map.put("其他管材","PMC7116307310667315019776-200");
            map.put("其他建筑、建材类管材","PMC7116307310667315019776-200");
            map.put("彩色灯泡","PMC7116307310667315019776-200");
            map.put("卤钨灯泡","PMC7116307310667315019776-200");
            map.put("摄影灯泡","PMC7116307310667315019776-200");
            map.put("微型灯泡","PMC7116307310667315019776-200");
            map.put("聚光灯泡","PMC7116307310667315019776-200");
            map.put("水下灯泡","PMC7116307310667315019776-200");
            map.put("红外线灯泡","PMC7116307310667315019776-200");
            map.put("指示灯泡","PMC7116307310667315019776-200");
            map.put("塑料管材设备","PMC7116307310667315019776-200");
            map.put("投影机灯泡","PMC7116307310667315019776-200");
            map.put("普通照明台灯","PMC7119307310669051461632-200");
            map.put("室外夜灯","PMC7122307310670859206656-200");
            map.put("其他室外照明灯具","PMC7122307310670859206656-200");
            map.put("室外壁灯","PMC7122307310670859206656-200");
            map.put("室外LED灯串","PMC7122307310670859206656-200");
            map.put("水族照明设备","PMC7117307310668418121728-200");
            map.put("道路照明灯","PMC7117307310668418121728-200");
            map.put("普通照明白炽灯","PMC7117307310668418121728-200");
            map.put("照明器材代理加盟","PMC7117307310668418121728-200");
            map.put("OLED照明","PMC7117307310668418121728-200");
            map.put("其他照明电筒","PMC7117307310668418121728-200");
            map.put("接线端子","PMC7264307310763121311744-200");
            map.put("工业连接器","PMC7264307310763121311744-200");
            map.put("架空线连接器","PMC7264307310763121311744-200");
            map.put("配电箱端子","PMC7264307310763121311744-200");
            map.put("光纤连接器","PMC7264307310763121311744-200");
            map.put("数码连接器","PMC7264307310763121311744-200");
            map.put("USB连接器","PMC7264307310763121311744-200");
            map.put("IDC连接器（牛角/简牛）","PMC7264307310763121311744-200");
            map.put("FFC，FPC连接器","PMC7264307310763121311744-200");
            map.put("以太网/总线连接器","PMC7264307310763121311744-200");
            map.put("D-Sub连接器","PMC7264307310763121311744-200");
            map.put("音频与视频连接器","PMC7264307310763121311744-200");
            map.put("存储器/卡座连接器","PMC7264307310763121311744-200");
            map.put("板对板连接器","PMC7264307310763121311744-200");
            map.put("圆形连接器","PMC7264307310763121311744-200");
            map.put("PCB连接器","PMC7264307310763121311744-200");
            map.put("卡缘连接器","PMC7264307310763121311744-200");
            map.put("矩形连接器","PMC7264307310763121311744-200");
            map.put("RF同轴连接器","PMC7264307310763121311744-200");
            map.put("电源连接器","PMC7264307310763121311744-200");
            map.put("光纤连接器","PMC7264307310763121311744-200");
            map.put("光伏连接器","PMC7264307310763121311744-200");
            map.put("线对线/线对板连接器","PMC7264307310763121311744-200");
            map.put("背板连接器","PMC7264307310763121311744-200");
            map.put("汽车连接器","PMC7264307310763121311744-200");
            map.put("照明连接器","PMC7264307310763121311744-200");
            map.put("管脚和插座连接器","PMC7264307310763121311744-200");
            map.put("端子连接器","PMC7264307310763121311744-200");
            map.put("香蕉和尖头连接器","PMC7264307310763121311744-200");
            map.put("模块化连接器","PMC7264307310763121311744-200");
            map.put("其他连接器","PMC7264307310763121311744-200");
            map.put("线束/连接线/端子线","PMC7264307310763121311744-200");
            map.put("防水连接器","PMC7264307310763121311744-200");
            map.put("wafer连接器","PMC7264307310763121311744-200");
            map.put("液位开关","PMC7267307310765147160576-200");
            map.put("WIFI智能开关","PMC7267307310765147160576-200");
            map.put("脚踏开关","PMC7267307310765147160576-200");
            map.put("倒顺开关","PMC7267307310765147160576-200");
            map.put("微动开关","PMC7267307310765147160576-200");
            map.put("墙壁开关","PMC7267307310765147160576-200");
            map.put("按钮开关","PMC7267307310765147160576-200");
            map.put("船型开关(跷板开关)","PMC7267307310765147160576-200");
            map.put("接近开关","PMC7267307310765147160576-200");
            map.put("行程开关(限位开关)","PMC7267307310765147160576-200");
            map.put("光电开关","PMC7267307310765147160576-200");
            map.put("转换开关(组合开关)","PMC7267307310765147160576-200");
            map.put("薄膜开关","PMC7267307310765147160576-200");
            map.put("旋钮开关","PMC7267307310765147160576-200");
            map.put("高压负荷开关","PMC7267307310765147160576-200");
            map.put("低压负荷开关","PMC7267307310765147160576-200");
            map.put("复合开关(同步开关)","PMC7267307310765147160576-200");
            map.put("钮子开关(摇臂开关)","PMC7267307310765147160576-200");
            map.put("轻触开关(按键开关)","PMC7267307310765147160576-200");
            map.put("温控开关","PMC7267307310765147160576-200");
            map.put("调速开关","PMC7267307310765147160576-200");
            map.put("调光开关","PMC7267307310765147160576-200");
            map.put("调音开关","PMC7267307310765147160576-200");
            map.put("门铃开关","PMC7267307310765147160576-200");
            map.put("钥匙开关","PMC7267307310765147160576-200");
            map.put("感应开关","PMC7267307310765147160576-200");
            map.put("声光控开关","PMC7267307310765147160576-200");
            map.put("流量开关","PMC7267307310765147160576-200");
            map.put("浴霸开关","PMC7267307310765147160576-200");
            map.put("拨码开关(DIP开关SIP开关)","PMC7267307310765147160576-200");
            map.put("漏电保护开关","PMC7267307310765147160576-200");
            map.put("压力开关","PMC7267307310765147160576-200");
            map.put("震动开关","PMC7267307310765147160576-200");
            map.put("遥控开关","PMC7267307310765147160576-200");
            map.put("电动工具开关","PMC7267307310765147160576-200");
            map.put("控制与保护开关","PMC7267307310765147160576-200");
            map.put("取电开关","PMC7267307310765147160576-200");
            map.put("防爆开关","PMC7267307310765147160576-200");
            map.put("其他开关","PMC7267307310765147160576-200");
            map.put("开关配件","PMC7267307310765147160576-200");
            map.put("双电源自动转换开关","PMC7267307310765147160576-200");
            map.put("传感器开关","PMC7267307310765147160576-200");
            map.put("ID钥匙开关","PMC7267307310765147160576-200");
            map.put("高压接地开关","PMC7267307310765147160576-200");
            map.put("高压断路器","PMC7257307310759065419776-200");
            map.put("电磁继电器","PMC7257307310759065419776-200");
            map.put("磁保持继电器","PMC7257307310759065419776-200");
            map.put("固态继电器","PMC7257307310759065419776-200");
            map.put("延时继电器","PMC7257307310759065419776-200");
            map.put("时间继电器","PMC7257307310759065419776-200");
            map.put("电压继电器","PMC7257307310759065419776-200");
            map.put("真空继电器","PMC7257307310759065419776-200");
            map.put("舌簧(磁簧)继电器","PMC7257307310759065419776-200");
            map.put("温度继电器","PMC7257307310759065419776-200");
            map.put("光(耦合)继电器","PMC7257307310759065419776-200");
            map.put("热(过载)继电器","PMC7257307310759065419776-200");
            map.put("信号继电器","PMC7257307310759065419776-200");
            map.put("中间继电器","PMC7257307310759065419776-200");
            map.put("压力继电器","PMC7257307310759065419776-200");
            map.put("汽车继电器","PMC7257307310759065419776-200");
            map.put("通信继电器","PMC7257307310759065419776-200");
            map.put("安全继电器","PMC7257307310759065419776-200");
            map.put("控制继电器","PMC7257307310759065419776-200");
            map.put("功率继电器","PMC7257307310759065419776-200");
            map.put("冲击继电器","PMC7257307310759065419776-200");
            map.put("启动继电器","PMC7257307310759065419776-200");
            map.put("差动继电器","PMC7257307310759065419776-200");
            map.put("普通继电器","PMC7257307310759065419776-200");
            map.put("热继电器","PMC7257307310759065419776-200");
            map.put("电流继电器","PMC7257307310759065419776-200");
            map.put("液位继电器","PMC7257307310759065419776-200");
            map.put("正反转继电器","PMC7257307310759065419776-200");
            map.put("速度继电器（转速继电器）","PMC7257307310759065419776-200");
            map.put("同轴继电器","PMC7257307310759065419776-200");
            map.put("线路板继电器","PMC7257307310759065419776-200");
            map.put("直流继电器","PMC7257307310759065419776-200");
            map.put("交流继电器","PMC7257307310759065419776-200");
            map.put("低压电子继电器","PMC7257307310759065419776-200");
            map.put("其他继电器","PMC7257307310759065419776-200");
            map.put("应急电源（EPS电源）","PMC7266307310764501237760-200");
            map.put("户外电源","PMC7266307310764501237760-200");
            map.put("应急启动电源","PMC7266307310764501237760-200");
            map.put("其他电源","PMC7266307310764501237760-200");
            map.put("电源配件","PMC7266307310764501237760-200");
            map.put("船用发动机配件","PMC7265307310763939201024-200");
            map.put("摩托车发动机及配件","PMC7265307310763939201024-200");
            map.put("液压马达","PMC7265307310763939201024-200");
            map.put("发动机总成","PMC7265307310763939201024-200");
            map.put("其他电线电缆","PMC7259307310760579563520-200");
            map.put("水龙头","PMC7181307310712370233344-200");
            map.put("电热水龙头","PMC7181307310712370233344-200");
            map.put("水槽","PMC7180307310711736893440-200");
            map.put("水槽套装","PMC7180307310711736893440-200");
            map.put("水槽配件","PMC7180307310711736893440-200");
            map.put("水槽过滤网","PMC7180307310711736893440-200");
            map.put("水槽沥水袋","PMC7180307310711736893440-200");
            map.put("厨房壁橱、橱柜","PMC6918307310519117676544-200");
            map.put("其他厨房家具及配件","PMC7177307310709920759808-200");
            map.put("厨房用品配件","PMC7177307310709920759808-200");
            map.put("厨卫配件","PMC7177307310709920759808-200");
            map.put("厨房电器配件","PMC7177307310709920759808-200");
            map.put("厨房电器配件","PMC7177307310709920759808-200");
            map.put("厨房小工具套装","PMC7179307310711103553536-200");
            map.put("智能家居","PMC7162307310699984453632-200");
            map.put("通风系统","PMC7160307310698604527616-200");
            map.put("冷却管","PMC7160307310698604527616-200");
            map.put("加热炬","PMC7160307310698604527616-200");
            map.put("加热帽、焗油帽","PMC7160307310698604527616-200");
            map.put("冷却塔","PMC7160307310698604527616-200");
            map.put("冷却器","PMC7160307310698604527616-200");
            map.put("平移门","PMC7151307310693676220416-200");
            map.put("推拉门","PMC7151307310693676220416-200");
            map.put("平开门","PMC7151307310693676220416-200");
            map.put("伸缩门","PMC7151307310693676220416-200");
            map.put("折叠门","PMC7151307310693676220416-200");
            map.put("旋转门","PMC7151307310693676220416-200");
            map.put("卷闸门","PMC7151307310693676220416-200");
            map.put("其他门","PMC7151307310693676220416-200");
            map.put("进户门","PMC7151307310693676220416-200");
            map.put("门护栏","PMC7151307310693676220416-200");
            map.put("安全门卡","PMC7151307310693676220416-200");
            map.put("墙纸、壁纸","PMC6868307310480173563904-200");
            map.put("PVC壁纸","PMC6868307310480173563904-200");
            map.put("油漆笔","PMC7250307310754812395520-200");
            map.put("实木地板","PMC7174307310707714555904-200");
            map.put("实木复合地板","PMC7174307310707714555904-200");
            map.put("PVC地板","PMC7174307310707714555904-200");
            map.put("SPC石塑地板","PMC7174307310707714555904-200");
            map.put("木塑地板","PMC7174307310707714555904-200");
            map.put("竹地板","PMC7174307310707714555904-200");
            map.put("钢地板","PMC7174307310707714555904-200");
            map.put("塑料地板","PMC7174307310707714555904-200");
            map.put("其他地板","PMC7174307310707714555904-200");
            map.put("实木地板","PMC7174307310707714555904-200");
            map.put("实木复合地板","PMC7174307310707714555904-200");
            map.put("强化复合地板","PMC7174307310707714555904-200");
            map.put("PVC地板","PMC7174307310707714555904-200");
            map.put("SPC石塑地板","PMC7174307310707714555904-200");
            map.put("木塑地板","PMC7174307310707714555904-200");
            map.put("橡胶地板","PMC7174307310707714555904-200");
            map.put("竹地板","PMC7174307310707714555904-200");
            map.put("钢地板","PMC7174307310707714555904-200");
            map.put("塑料地板","PMC7174307310707714555904-200");
            map.put("其他地板","PMC7174307310707714555904-200");
            map.put("梯子","PMC7155307310695655931904-200");
            map.put("物流手推车","PMC7249307310754128723968-200");
            map.put("平板手推车","PMC7249307310754128723968-200");
            map.put("花洒，淋浴器及配件","PMC6802307310436984815616-200");
            map.put("浴缸、淋浴龙头","PMC6802307310436984815616-200");
            map.put("淋浴屏","PMC6802307310436984815616-200");
            map.put("智能马桶","PMC6807307310439740473344-200");
            map.put("移动马桶","PMC6807307310439740473344-200");
            map.put("普通马桶","PMC6807307310439740473344-200");
            map.put("马桶喷枪","PMC6807307310439740473344-200");
            map.put("马桶水箱","PMC6807307310439740473344-200");
            map.put("马桶洁厕剂","PMC6807307310439740473344-200");
            map.put("一次性马桶垫","PMC6807307310439740473344-200");
            map.put("儿童马桶架/梯","PMC6807307310439740473344-200");
            map.put("马桶架","PMC6807307310439740473344-200");
            map.put("马桶清洁贴、垫、套","PMC6807307310439740473344-200");
            map.put("马桶吸、管道疏通器","PMC6807307310439740473344-200");
            map.put("马桶杀菌灯","PMC6807307310439740473344-200");
            map.put("马桶贴","PMC6807307310439740473344-200");
            map.put("其他卫浴洁具及配件","PMC6798307310434153660416-200");
            map.put("卫浴挂件套件","PMC6799307310434950578176-200");
            map.put("迎宾门铃","PMC7254307310756926324736-200");
            map.put("门铃","PMC7254307310756926324736-200");
            map.put("灌溉工具","PMC6981307310569902309376-200");
            map.put("园艺灌溉工具","PMC6981307310569902309376-200");
            map.put("花盆装饰","PMC6991307310576969711616-200");
            map.put("花盆容器","PMC6991307310576969711616-200");
            map.put("女式休闲单鞋","PMC8331307311409866211328-200");
            map.put("鞋带","PMC8331307311409866211328-200");
            map.put("男式休闲单鞋","PMC8331307311409866211328-200");
            map.put("公寓、花园、别墅","PMC8314307311399434977280-200");
            map.put("平底凉鞋","PMC8310307311397220384768-200");
            map.put("高跟凉鞋","PMC8310307311397220384768-200");
            map.put("厚底凉鞋","PMC8310307311397220384768-200");
            map.put("坡跟凉鞋","PMC8310307311397220384768-200");
            map.put("女士胸包、腰包","PMC8224307311344326017024-20");
            map.put("女士胸包、腰包","PMC8224307311344326017024-20");
            map.put("男士公文包","PMC7434307310867614007296-20");
            map.put("男士腰包","PMC7440307310870852009984-20");
            map.put("腰包、臂包、拎包","PMC7440307310870852009984-20");
            map.put("运动腰包、配件包","PMC7440307310870852009984-20");
            map.put("儿童胸包、腰包","PMC7440307310870852009984-20");
            map.put("仪器箱","PMC6728307310393917702144-40");
            map.put("百叶箱","PMC6728307310393917702144-40");
            map.put("腐蚀试验箱","PMC6728307310393917702144-40");
            map.put("高低温箱","PMC6728307310393917702144-40");
            map.put("干燥箱","PMC6728307310393917702144-40");
            map.put("老化箱","PMC6728307310393917702144-40");
            map.put("湿热试验箱","PMC6728307310393917702144-40");
            map.put("其他试验箱及气候设备","PMC6728307310393917702144-40");
            map.put("低温冰箱","PMC6728307310393917702144-40");
            map.put("培养箱","PMC6728307310393917702144-40");
            map.put("户外防水包、防水箱","PMC6728307310393917702144-40");
            map.put("乐器箱包","PMC6728307310393917702144-40");
            map.put("儿童拉杆箱","PMC6728307310393917702144-40");
            map.put("集装箱","PMC6728307310393917702144-40");
            map.put("水族箱","PMC6728307310393917702144-40");
            map.put("狗狗包、箱","PMC6728307310393917702144-40");
            map.put("猫猫包、箱","PMC6728307310393917702144-40");
            map.put("宠物烘干箱","PMC6728307310393917702144-40");
            map.put("车顶箱","PMC6728307310393917702144-40");
            map.put("车载冰箱","PMC6728307310393917702144-40");
            map.put("扶手箱","PMC6728307310393917702144-40");
            map.put("车用置物袋/置物箱","PMC6728307310393917702144-40");
            map.put("花箱","PMC6728307310393917702144-40");
            map.put("花箱","PMC6728307310393917702144-40");
            map.put("衣箱","PMC6728307310393917702144-40");
            map.put("钓箱","PMC6728307310393917702144-40");
            map.put("潜水箱包","PMC6728307310393917702144-40");
            map.put("筹码箱","PMC6728307310393917702144-40");
            map.put("米桶、储米箱","PMC6728307310393917702144-40");
            map.put("收纳箱","PMC6728307310393917702144-40");
            map.put("医药箱","PMC6728307310393917702144-40");
            map.put("首饰架、首饰箱、首饰袋","PMC6728307310393917702144-40");
            map.put("首饰箱、包、袋、盒","PMC6728307310393917702144-40");
            map.put("电阻箱","PMC6728307310393917702144-40");
            map.put("光伏汇流箱","PMC6728307310393917702144-40");
            map.put("电缆分接箱","PMC6728307310393917702144-40");
            map.put("配电箱","PMC6728307310393917702144-40");
            map.put("电缆分支箱","PMC6728307310393917702144-40");
            map.put("灯箱","PMC6728307310393917702144-40");
            map.put("塑胶储物箱","PMC6728307310393917702144-40");
            map.put("pe水箱","PMC6728307310393917702144-40");
            map.put("LED灯箱","PMC6728307310393917702144-40");
            map.put("方箱","PMC6728307310393917702144-40");
            map.put("迷你冰箱","PMC6728307310393917702144-40");
            map.put("冰箱","PMC6728307310393917702144-40");
            map.put("充电冰箱","PMC6728307310393917702144-40");
            map.put("家用冷箱","PMC6728307310393917702144-40");
            map.put("桑拿浴箱","PMC6728307310393917702144-40");
            map.put("汗蒸箱、桑拿足浴设备","PMC6728307310393917702144-40");
            map.put("医用箱","PMC6728307310393917702144-40");
            map.put("邮箱/邮筒/收件箱","PMC6728307310393917702144-40");
            map.put("提款箱","PMC6728307310393917702144-40");
            map.put("塑料箱","PMC6728307310393917702144-40");
            map.put("木箱","PMC6728307310393917702144-40");
            map.put("泡沫箱","PMC6728307310393917702144-40");
            map.put("纸箱","PMC6728307310393917702144-40");
            map.put("防雷箱","PMC6728307310393917702144-40");
            map.put("防爆箱","PMC6728307310393917702144-40");
            map.put("油箱","PMC6728307310393917702144-40");
            map.put("水箱","PMC6728307310393917702144-40");
            map.put("波箱油","PMC6728307310393917702144-40");
            map.put("方箱","PMC6728307310393917702144-40");
            map.put("周转箱","PMC6728307310393917702144-40");
            map.put("家用购物袋、篮","PMC8199307311330119909376-200");
            map.put("包带","PMC8218307311341192871936-200");
            map.put("包带","PMC8218307311341192871936-200");
            map.put("收纳袋、收纳包","PMC8192307311325535535104-200");
            map.put("披肩帽/连体帽","PMC6502307310250577362944-20");
            map.put("披肩帽/连体帽","PMC6502307310250577362944-20");
            map.put("披肩","PMC6502307310250577362944-20");
            map.put("洗头帽","PMC6439307310212962844672-20");
            map.put("成人帽","PMC6439307310212962844672-20");
            map.put("工作帽/职业帽","PMC6439307310212962844672-20");
            map.put("表演/节庆帽","PMC6439307310212962844672-20");
            map.put("成人帽","PMC6439307310212962844672-20");
            map.put("工作帽/职业帽","PMC6439307310212962844672-20");
            map.put("表演/节庆帽","PMC6439307310212962844672-20");
            map.put("腰带","PMC6416307310198035316736-20");
            map.put("手帕纸","PMC6409307310193882955776-40");
            map.put("手巾/手帕","PMC6409307310193882955776-40");
            map.put("袖扣","PMC6408307310193245421568-40");
            map.put("眼镜盒、眼镜袋、眼镜包","PMC6421307310201151684608-40");
            map.put("隐形眼镜盒","PMC6421307310201151684608-40");
            map.put("空中吊饰","PMC6407307310192402366464-40");
            map.put("其他首饰","PMC6467307310230100770816-40");
            map.put("珠宝首饰定制","PMC6467307310230100770816-40");
            map.put("钥匙扣及钥匙扣配件","PMC6414307310196621836288-40");
            map.put("钥匙扣","PMC6414307310196621836288-40");
            map.put("黄金首饰套装","PMC6469307310231472308224-40");
            map.put("k金首饰套装","PMC6469307310231472308224-40");
            map.put("铂金/PT首饰套装","PMC6469307310231472308224-40");
            map.put("翡翠首饰套装","PMC6469307310231472308224-40");
            map.put("彩色宝石/贵重宝石首饰套装","PMC6469307310231472308224-40");
            map.put("发带","PMC6429307310206356815872-40");
            map.put("发夹","PMC6428307310205513760768-40");
            map.put("假发长直发","PMC6433307310208747569152-40");
            map.put("假发短直发","PMC6433307310208747569152-40");
            map.put("真发假发","PMC6433307310208747569152-40");
            map.put("男士假发","PMC6433307310208747569152-40");
            map.put("cos假发","PMC6433307310208747569152-40");
            map.put("假发短卷发","PMC6433307310208747569152-40");
            map.put("假发长卷发","PMC6433307310208747569152-40");
            map.put("假发片","PMC6433307310208747569152-40");
            map.put("假发马尾","PMC6433307310208747569152-40");
            map.put("假发刘海","PMC6433307310208747569152-40");
            map.put("其他假发","PMC6433307310208747569152-40");
            map.put("假发护理用品","PMC6433307310208747569152-40");
            map.put("发梳头饰","PMC6432307310207988400128-40");
            map.put("皇冠","PMC6432307310207988400128-40");
            map.put("手机挂绳","PMC7515307310916142104576-40");
            map.put("手机挂件","PMC7515307310916142104576-40");
            map.put("手机袋","PMC7515307310916142104576-40");
            map.put("手机贴纸","PMC7515307310916142104576-40");
            map.put("手机擦","PMC7515307310916142104576-40");
            map.put("手机座","PMC7515307310916142104576-40");
            map.put("手机防滑垫","PMC7515307310916142104576-40");
            map.put("其他手机饰品","PMC7515307310916142104576-40");
            map.put("手机挂绳","PMC7515307310916142104576-40");
            map.put("手机挂件","PMC7515307310916142104576-40");
            map.put("手机袋","PMC7515307310916142104576-40");
            map.put("手机贴纸","PMC7515307310916142104576-40");
            map.put("手机擦","PMC7515307310916142104576-40");
            map.put("手机座","PMC7515307310916142104576-40");
            map.put("手机防滑垫","PMC7515307310916142104576-40");
            map.put("其他手机饰品","PMC7515307310916142104576-40");
            map.put("手机充电器","PMC7515307310916142104576-40");
            map.put("手机散热器","PMC7515307310916142104576-40");
            map.put("手机专用线控耳机","PMC7515307310916142104576-40");
            map.put("手机免提设备","PMC7515307310916142104576-40");
            map.put("手机放大屏","PMC7515307310916142104576-40");
            map.put("手机保护膜","PMC7515307310916142104576-40");
            map.put("DIY手机壳","PMC7515307310916142104576-40");
            map.put("手机保护套","PMC7515307310916142104576-40");
            map.put("手机防尘塞","PMC7515307310916142104576-40");
            map.put("手机摄像头","PMC7515307310916142104576-40");
            map.put("手机数据线","PMC7515307310916142104576-40");
            map.put("手机按键","PMC7515307310916142104576-40");
            map.put("手机显示屏","PMC7515307310916142104576-40");
            map.put("手机信号屏蔽器","PMC7515307310916142104576-40");
            map.put("手机转接头","PMC7515307310916142104576-40");
            map.put("手机双卡通","PMC7515307310916142104576-40");
            map.put("手机模型","PMC7515307310916142104576-40");
            map.put("其他手机配件","PMC7515307310916142104576-40");
            map.put("手机刷卡器","PMC7515307310916142104576-40");
            map.put("手机支架","PMC7515307310916142104576-40");
            map.put("手机气囊支架","PMC7515307310916142104576-40");
            map.put("手机指环支架","PMC7515307310916142104576-40");
            map.put("手机剪卡器","PMC7515307310916142104576-40");
            map.put("手机取卡器","PMC7515307310916142104576-40");
            map.put("手机稳定器","PMC7515307310916142104576-40");
            map.put("手机投屏器/同屏器","PMC7515307310916142104576-40");
            map.put("蓝牙自拍器","PMC7531307310925256327168-40");
            map.put("手机自拍杆","PMC7531307310925256327168-40");
            map.put("闪光灯","PMC7508307310911993937920-40");
            map.put("手机镜头","PMC7508307310911993937920-40");
            map.put("对讲机","PMC7543307310932424392704-40");
            map.put("移动电源","PMC7528307310924056756224-40");
            map.put("移动电源电芯","PMC7528307310924056756224-40");
            map.put("光学摄像机","PMC6309307310124194594816-40");
            map.put("智能摄像机","PMC6309307310124194594816-40");
            map.put("数码摄像机","PMC6309307310124194594816-40");
            map.put("宠物摄像机","PMC6309307310124194594816-40");
            map.put("运动摄像机配件","PMC6309307310124194594816-40");
            map.put("拍立得相机","PMC6304307310121468297216-40");
            map.put("普通数码相机","PMC6303307310120759459840-40");
            map.put("车载摄像头","PMC6313307310127071887360-40");
            map.put("智能摄像头","PMC6313307310127071887360-40");
            map.put("数码摄像头","PMC6313307310127071887360-40");
            map.put("摄像头模块/视频采集模块","PMC6313307310127071887360-40");
            map.put("相机镜头","PMC6311307310125532577792-20");
            map.put("无人机包","PMC6310307310124840517632-20");
            map.put("放线无人机","PMC6310307310124840517632-20");
            map.put("农业植保无人机","PMC6310307310124840517632-20");
            map.put("航拍无人机","PMC6310307310124840517632-20");
            map.put("物流运输无人机","PMC6310307310124840517632-20");
            map.put("消防救援无人机","PMC6310307310124840517632-20");
            map.put("勘探测绘无人机","PMC6310307310124840517632-20");
            map.put("其他无人机","PMC6310307310124840517632-20");
            map.put("麦克风/话筒","PMC5994307309926236028928-20");
            map.put("投影仪","PMC7325307310800958128128-40");
            map.put("投影仪配件","PMC7325307310800958128128-40");
            map.put("投影机/投影仪","PMC7325307310800958128128-40");
            map.put("家庭影院","PMC5984307309915205009408-40");
            map.put("车载mp3","PMC5991307309922448572416-40");
            map.put("车载mp4","PMC5991307309922448572416-40");
            map.put("MP3","PMC5991307309922448572416-40");
            map.put("MP4","PMC5991307309922448572416-40");
            map.put("车载CD机","PMC5989307309920422723584-40");
            map.put("DVD导航","PMC5989307309920422723584-40");
            map.put("CD包/夹/袋","PMC5989307309920422723584-40");
            map.put("CD架","PMC5989307309920422723584-40");
            map.put("CD机","PMC5989307309920422723584-40");
            map.put("CD包、CD盒","PMC5989307309920422723584-40");
            map.put("CD","PMC5989307309920422723584-40");
            map.put("VCD","PMC5989307309920422723584-40");
            map.put("DVD","PMC5989307309920422723584-40");
            map.put("便携式DVD","PMC5989307309920422723584-40");
            map.put("收音、录音机","PMC5993307309925426528256-40");
            map.put("收音机天线","PMC5992307309924642193408-40");
            map.put("分配系统放大器","PMC5978307309909546893312-20");
            map.put("光纤放大器","PMC5978307309909546893312-20");
            map.put("混音器","PMC5978307309909546893312-20");
            map.put("信号放大器","PMC5978307309909546893312-20");
            map.put("影音电器配件","PMC5985307309916542992384-40");
            map.put("家用游戏机","PMC6601307310314850877440-40");
            map.put("掌上游戏机","PMC6600307310313932324864-40");
            map.put("VR/AR一体游戏机","PMC6602307310315551326208-40");
            map.put("GPS定位器","PMC7546307310933607186432-40");
            map.put("电子书","PMC7511307310913692631040-40");
            map.put("电子词典","PMC7512307310914384691200-40");
            map.put("茶树菇","PMC6535307310271083315200-40");
            map.put("茶烟","PMC6535307310271083315200-40");
            map.put("乌龙茶","PMC6535307310271083315200-40");
            map.put("普洱茶","PMC6535307310271083315200-40");
            map.put("黑茶","PMC6535307310271083315200-40");
            map.put("白茶","PMC6535307310271083315200-40");
            map.put("黄茶","PMC6535307310271083315200-40");
            map.put("绿茶","PMC6535307310271083315200-40");
            map.put("红茶","PMC6535307310271083315200-40");
            map.put("花果茶","PMC6535307310271083315200-40");
            map.put("代用/养生茶","PMC6535307310271083315200-40");
            map.put("再加工茶/配方茶/调味茶","PMC6535307310271083315200-40");
            map.put("其他茶叶","PMC6535307310271083315200-40");
            map.put("茶饮料","PMC6535307310271083315200-40");
            map.put("奶茶","PMC6535307310271083315200-40");
            map.put("姜茶/姜汤","PMC6535307310271083315200-40");
            map.put("可可/巧克力饮品","PMC6523307310263634231296-40");
            map.put("糖果巧克力包装","PMC6523307310263634231296-40");
            map.put("浓缩果汁","PMC6530307310268386377728-40");
            map.put("碳酸饮料","PMC6522307310262409494528-40");
            map.put("小米","PMC6576307310297276743680-40");
            map.put("黑米","PMC6576307310297276743680-40");
            map.put("玉米","PMC6576307310297276743680-40");
            map.put("速冻中式米面包点类","PMC6576307310297276743680-40");
            map.put("大米","PMC6576307310297276743680-40");
            map.put("糯米","PMC6576307310297276743680-40");
            map.put("干制米粉、米线","PMC6576307310297276743680-40");
            map.put("其他米面类","PMC6576307310297276743680-40");
            map.put("待煮面条","PMC6572307310294982459392-40");
            map.put("豆类","PMC6574307310295619993600-40");
            map.put("豆类零食","PMC6574307310295619993600-40");
            map.put("方便米饭类","PMC6545307310277332828160-40");
            map.put("方便粥类","PMC6545307310277332828160-40");
            map.put("调味油","PMC6550307310280252063744-40");
            map.put("酱油","PMC6550307310280252063744-40");
            map.put("植物油","PMC6550307310280252063744-40");
            map.put("动物油","PMC6550307310280252063744-40");
            map.put("调和油","PMC6550307310280252063744-40");
            map.put("其他食用油","PMC6550307310280252063744-40");
            map.put("糖类","PMC6559307310286648377344-40");
            map.put("食品甜味剂","PMC6559307310286648377344-40");
            map.put("黑糖","PMC6559307310286648377344-40");
            map.put("喜糖","PMC6559307310286648377344-40");
            map.put("硬糖","PMC6559307310286648377344-40");
            map.put("软糖","PMC6559307310286648377344-40");
            map.put("口香糖","PMC6559307310286648377344-40");
            map.put("甜味剂","PMC6559307310286648377344-40");
            map.put("葡萄糖","PMC6559307310286648377344-40");
            map.put("果糖","PMC6559307310286648377344-40");
            map.put("蔗糖","PMC6559307310286648377344-40");
            map.put("麦芽糖","PMC6559307310286648377344-40");
            map.put("火锅调味料","PMC6554307310282886086656-40");
            map.put("烧烤调味料","PMC6554307310282886086656-40");
            map.put("汤类调味料","PMC6554307310282886086656-40");
            map.put("西式调味料","PMC6554307310282886086656-40");
            map.put("日韩式调味料","PMC6554307310282886086656-40");
            map.put("复合调味料","PMC6554307310282886086656-40");
            map.put("调味椒盐","PMC6556307310283527815168-40");
            map.put("醋","PMC6557307310285209731072-40");
            map.put("料酒","PMC6505307310252582240256-40");
            map.put("增味剂","PMC6547307310278620479488-40");
            map.put("面粉","PMC6549307310279467728896-40");
            map.put("面粉筛","PMC6549307310279467728896-40");
            map.put("其他调味品","PMC6541307310274556198912-40");
            map.put("其他果酱","PMC6541307310274556198912-40");
            map.put("动物精华/提取物","PMC6512307310257191780352-40");
            map.put("植物精华/提取物","PMC6512307310257191780352-40");
            map.put("工业用植物提取物","PMC6512307310257191780352-40");
            map.put("苏打水","PMC6514307310257858674688-40");
            map.put("黄油","PMC6562307310287961194496-40");
            map.put("婴幼儿糖果","PMC6594307310309440225280-40");
            map.put("草种子","PMC6593307310308475535360-40");
            map.put("种子","PMC6593307310308475535360-40");
            map.put("蔬菜种子、种苗","PMC6593307310308475535360-40");
            map.put("水果种子、种苗","PMC6593307310308475535360-40");
            map.put("药材种子、种苗","PMC6593307310308475535360-40");
            map.put("其他农作物种子、种苗","PMC6593307310308475535360-40");
            map.put("粮食作物种子","PMC6593307310308475535360-40");
            map.put("海藻类","PMC6591307310307699589120-40");
            map.put("其他坚果、干果生货","PMC6584307310302913888256-40");
            map.put("混合坚果","PMC6584307310302913888256-40");
            map.put("其他坚果炒货","PMC6584307310302913888256-40");
            map.put("果冻","PMC6589307310306139308032-40");
            map.put("医用口罩","PMC6624307310329329614848-10");
            map.put("急救箱、急救包","PMC6613307310322002165760-10");
            map.put("体质测试仪器","PMC6614307310322958467072-10");
            map.put("核酸抗原检测试剂","PMC6614307310322958467072-10");
            map.put("玻璃温度计","PMC6642307310339962175488-10");
            map.put("双金属温度计","PMC6642307310339962175488-10");
            map.put("压力式温度计","PMC6642307310339962175488-10");
            map.put("非接触式温度计","PMC6642307310339962175488-10");
            map.put("车用温度计","PMC6642307310339962175488-10");
            map.put("温度计、水温计","PMC6642307310339962175488-10");
            map.put("纸尿裤","PMC7598307310964426932224-40");
            map.put("监控仪","PMC6024307309946540654592-10");
            map.put("食品、药品安全检测仪器","PMC6023307309945768902656-10");
            map.put("安全仪器","PMC6023307309945768902656-10");
            map.put("安全栅","PMC6023307309945768902656-10");
            map.put("安全气囊","PMC6023307309945768902656-10");
            map.put("安全锤工具","PMC6023307309945768902656-10");
            map.put("汽车儿童安全座椅","PMC6023307309945768902656-10");
            map.put("儿童安全座椅附件","PMC6023307309945768902656-10");
            map.put("其他汽车安全用品","PMC6023307309945768902656-10");
            map.put("安全套","PMC6023307309945768902656-10");
            map.put("安全阀","PMC6023307309945768902656-10");
            map.put("安全、防护用品代理加盟","PMC6023307309945768902656-10");
            map.put("安全、防护用品加工","PMC6023307309945768902656-10");
            map.put("女士安全裤","PMC6023307309945768902656-10");
            map.put("安全膜","PMC6023307309945768902656-10");
            map.put("其他安全防护提醒产品","PMC6023307309945768902656-10");
            map.put("安全保卫","PMC6023307309945768902656-10");
            map.put("安全软件","PMC6023307309945768902656-10");
            map.put("公共、安全、防护展","PMC6023307309945768902656-10");
            map.put("其他信息安全产品","PMC6023307309945768902656-10");
            map.put("棉安全帽","PMC6023307309945768902656-10");
            map.put("玻璃钢安全帽","PMC6023307309945768902656-10");
            map.put("塑料安全帽","PMC6023307309945768902656-10");
            map.put("abs安全帽","PMC6023307309945768902656-10");
            map.put("安全绳","PMC6023307309945768902656-10");
            map.put("安全平网","PMC6023307309945768902656-10");
            map.put("建筑安全网","PMC6023307309945768902656-10");
            map.put("其他安全网","PMC6023307309945768902656-10");
            map.put("其他安全检查设备","PMC6023307309945768902656-10");
            map.put("集成防盗报警系统","PMC6023307309945768902656-10");
            map.put("交通安全服装","PMC6023307309945768902656-10");
            map.put("安全凸面镜","PMC6023307309945768902656-10");
            map.put("其他交通安全设施","PMC6023307309945768902656-10");
            map.put("回弹式安全刀","PMC6023307309945768902656-10");
            map.put("生产安全标识","PMC6023307309945768902656-10");
            map.put("消防安全标识","PMC6023307309945768902656-10");
            map.put("交通安全标识","PMC6023307309945768902656-10");
            map.put("其他安全标识","PMC6023307309945768902656-10");
            map.put("其他汽车影音","PMC6028307309948876881920-10");
            map.put("倒车镜/后视镜总成","PMC6008307309935824207872-10");
            map.put("倒车镜/后视镜","PMC6008307309935824207872-10");
            map.put("遮阳板后视镜","PMC6008307309935824207872-10");
            map.put("电动车车罩","PMC5998307309929239150592-10");
            map.put("反光材料","PMC6005307309934259732480-10");
            map.put("车身反光标识","PMC6005307309934259732480-10");
            map.put("静电棒/钥匙包","PMC6052307309963833769984-20");
            map.put("车用钥匙包","PMC6052307309963833769984-20");
            map.put("吸奶器","PMC7607307310970131185664-40");
            map.put("米粉/米糊/汤粥","PMC7638307310987709513728-40");
            map.put("麦片","PMC7638307310987709513728-40");
            map.put("电动拖把","PMC7356307310820734271488-40");
            map.put("电子灭蚊器、灭蚊灯","PMC7338307310809900384256-40");
            map.put("手摇咖啡机","PMC7270307310766610972672-40");
            map.put("咖啡机","PMC7270307310766610972672-40");
            map.put("咖啡机","PMC7270307310766610972672-40");
            map.put("面包机、馒头机","PMC7280307310772734656512-40");
            map.put("面包机、馒头机","PMC7280307310772734656512-40");
            map.put("搅拌机、料理机","PMC7278307310771518308352-40");
            map.put("搅拌机、料理机","PMC7278307310771518308352-40");
            map.put("厨用龙头","PMC7289307310778363412480-40");
            map.put("其他厨卫五金","PMC7289307310778363412480-40");
            map.put("厨房操作台","PMC7289307310778363412480-40");
            map.put("厨房用纸","PMC7289307310778363412480-40");
            map.put("厨卫消毒液","PMC7289307310778363412480-40");
            map.put("厨用点火器、点烟枪","PMC7289307310778363412480-40");
            map.put("厨房秤","PMC7289307310778363412480-40");
            map.put("餐厨具玩具","PMC7289307310778363412480-40");
            map.put("其他酒店餐厨用具","PMC7289307310778363412480-40");
            map.put("后厨陶瓷","PMC7289307310778363412480-40");
            map.put("厨房置物架","PMC7289307310778363412480-40");
            map.put("厨房挂架","PMC7289307310778363412480-40");
            map.put("厨卫挂钩","PMC7289307310778363412480-40");
            map.put("LED厨卫灯","PMC7289307310778363412480-40");
            map.put("其他厨卫大件","PMC7289307310778363412480-40");
            map.put("小厨宝","PMC7289307310778363412480-40");
            map.put("大码连衣裙","PMC7406307310850698379264-40");
            map.put("童连衣裙","PMC7406307310850698379264-40");
            map.put("橄榄球","PMC7929307311166646910976-40");
            map.put("网球","PMC7930307311167347359744-40");
            map.put("种球","PMC6696307310373810208768-10");
            map.put("调味球","PMC6696307310373810208768-10");
            map.put("清洁刷、钢丝球","PMC6696307310373810208768-10");
            map.put("水面浮球","PMC6696307310373810208768-10");
            map.put("泡沫球","PMC6696307310373810208768-10");
            map.put("洗耳球","PMC6696307310373810208768-10");
            map.put("吸球","PMC6696307310373810208768-10");
            map.put("双联球","PMC6696307310373810208768-10");
            map.put("毛球","PMC6696307310373810208768-10");
            map.put("奖杯","PMC6706307310379552210944-10");
            map.put("防烫、防高温手套","PMC6700307310376062550016-10");
            map.put("USB手套","PMC6700307310376062550016-10");
            map.put("潜水手套","PMC6700307310376062550016-10");
            map.put("足球守门员手套","PMC6700307310376062550016-10");
            map.put("骑行手套","PMC6700307310376062550016-10");
            map.put("一次性手套","PMC6700307310376062550016-10");
            map.put("家务手套","PMC6700307310376062550016-10");
            map.put("成人手套","PMC6700307310376062550016-10");
            map.put("手套配件","PMC6700307310376062550016-10");
            map.put("婚庆手套","PMC6700307310376062550016-10");
            map.put("防抓手套","PMC6700307310376062550016-10");
            map.put("儿童手套","PMC6700307310376062550016-10");
            map.put("防静电手套、腕带","PMC6700307310376062550016-10");
            map.put("医用手套","PMC6700307310376062550016-10");
            map.put("绝缘手套","PMC6700307310376062550016-10");
            map.put("耐高温手套","PMC6700307310376062550016-10");
            map.put("耐低温手套","PMC6700307310376062550016-10");
            map.put("防静电手套","PMC6700307310376062550016-10");
            map.put("消防手套","PMC6700307310376062550016-10");
            map.put("机械手套","PMC6700307310376062550016-10");
            map.put("帽子/头巾 配件","PMC6702307310377610248192-10");
            map.put("帽子/头巾 配件","PMC6702307310377610248192-10");
            map.put("跑步鞋","PMC6704307310378906288128-10");
            map.put("童/青少年鞋","PMC6704307310378906288128-10");
            map.put("军迷作训鞋","PMC6704307310378906288128-10");
            map.put("跑步机","PMC6704307310378906288128-10");
            map.put("旗帜","PMC6699307310375370489856-10");
            map.put("应援横幅","PMC6699307310375370489856-10");
            map.put("轮滑、速滑","PMC8073307311253938765824-40");
            map.put("电动滑板车","PMC7974307311193230409728-40");
            map.put("滑板车","PMC7974307311193230409728-40");
            map.put("儿童滑板车","PMC7974307311193230409728-40");
            map.put("运动水壶","PMC7977307311195411447808-40");
            map.put("诊断仪","PMC6064307309971266076672-10");
            map.put("钣金设备","PMC6063307309970645319680-10");
            map.put("LED装饰灯","PMC6002307309931810258944-10");
            map.put("工艺装饰灯具","PMC6002307309931810258944-10");
            map.put("大灯、前照灯","PMC6003307309932875612160-10");
            map.put("转向灯、角灯","PMC6003307309932875612160-10");
            map.put("尾灯","PMC6003307309932875612160-10");
            map.put("雾灯","PMC6003307309932875612160-10");
            map.put("仪表灯","PMC6003307309932875612160-10");
            map.put("刹车灯","PMC6003307309932875612160-10");
            map.put("车顶灯","PMC6003307309932875612160-10");
            map.put("牌照灯","PMC6003307309932875612160-10");
            map.put("工作灯、检修灯","PMC6003307309932875612160-10");
            map.put("其他车灯","PMC6003307309932875612160-10");
            map.put("日行灯","PMC6003307309932875612160-10");
            map.put("示宽灯","PMC6003307309932875612160-10");
            map.put("摩托车大灯、前照灯","PMC6003307309932875612160-10");
            map.put("车用HID氙气灯","PMC6003307309932875612160-10");
            map.put("气动抛光机","PMC6045307309959387807744-10");
            map.put("高压洗车水枪","PMC6048307309961040363520-10");
            map.put("洗车水枪","PMC6048307309961040363520-10");
            map.put("车载洗车器","PMC6048307309961040363520-10");
            map.put("其他洗车工具","PMC6048307309961040363520-10");
            map.put("洗车水管","PMC6048307309961040363520-10");
            map.put("洗车接头","PMC6048307309961040363520-10");
            map.put("补漆笔/油漆笔","PMC6044307309958729302016-10");
            map.put("其它汽车保养/添加剂","PMC6042307309957114494976-10");
            map.put("便携式洗车器","PMC6049307309961841475584-10");
            map.put("洗车机","PMC6049307309961841475584-10");
            map.put("车用眼镜夹/票据夹","PMC7768307311068496003072-10");
            map.put("眼镜片","PMC7768307311068496003072-10");
            map.put("眼镜布","PMC7768307311068496003072-10");
            map.put("眼镜及配件定制","PMC7768307311068496003072-10");
            map.put("眼镜清洗剂","PMC7768307311068496003072-10");
            map.put("眼镜链","PMC7768307311068496003072-10");
            map.put("摩托车传动系统零件","PMC7747307311055057453056-10");
            map.put("摩托车行走系统零件","PMC7747307311055057453056-10");
            map.put("摩托车操纵系统零件","PMC7747307311055057453056-10");
            map.put("摩托车电器与仪表","PMC7747307311055057453056-10");
            map.put("摩托车通用件","PMC7747307311055057453056-10");
            map.put("摩托车安全用品","PMC7747307311055057453056-10");
            map.put("摩托车用品与附件","PMC7747307311055057453056-10");
            map.put("摩托车喇叭总成","PMC7747307311055057453056-10");
            map.put("摩托车车灯总成","PMC7747307311055057453056-10");
            map.put("摩托车挡泥板","PMC7747307311055057453056-10");
            map.put("摩托车头盔防雾膜","PMC7747307311055057453056-10");
            map.put("摩托车把套","PMC7747307311055057453056-10");
            map.put("摩托车钥匙","PMC7747307311055057453056-10");
            map.put("摩托车坐垫","PMC7747307311055057453056-10");
            map.put("摩托车尾翼","PMC7747307311055057453056-10");
            map.put("摩托车头盔饰配","PMC7747307311055057453056-10");
            map.put("摩托车牌照框","PMC7747307311055057453056-10");
            map.put("摩托车头盔耳机","PMC7747307311055057453056-10");
            map.put("摩托车前挡风","PMC7747307311055057453056-10");
            map.put("摩托车排气管","PMC7747307311055057453056-10");
            map.put("摩托车脚踏板","PMC7747307311055057453056-10");
            map.put("摩托车轮毂","PMC7747307311055057453056-10");
            map.put("摩托车化油器","PMC7747307311055057453056-10");
            map.put("摩托车车架","PMC7747307311055057453056-10");
            map.put("摩托车保险杠","PMC7747307311055057453056-10");
            map.put("摩托车儿童座椅","PMC7747307311055057453056-10");
            map.put("摩托车后视镜","PMC7747307311055057453056-10");
            map.put("摩托车尾箱","PMC7747307311055057453056-10");
            map.put("防护面罩","PMC6625307310330080395264-10");
            map.put("防护面屏","PMC6625307310330080395264-10");
            map.put("其他面部防护","PMC6625307310330080395264-10");
            map.put("海洋生物类补充剂","PMC7557307310940515205120-40");
            map.put("其他特殊用途膳食营养补充剂","PMC7557307310940515205120-40");
            map.put("针刺无纺布","PMC6628307310332018163712-10");
            map.put("针织毯","PMC6741307310401387757568-40");
            map.put("串珠、绕珠","PMC6735307310398216863744-40");
            map.put("散珠/串珠","PMC6735307310398216863744-40");
            map.put("DIY/数字油画","PMC6737307310399466766336-40");
            map.put("建筑/DIY小屋/拼装玩具","PMC6746307310404499931136-40");
            map.put("diy饰品","PMC6746307310404499931136-40");
            map.put("徽章","PMC6734307310397407363072-40");
            map.put("陶瓷彩绘/沙画/水彩","PMC6744307310403199696896-40");
            map.put("陶瓷","PMC6744307310403199696896-40");
            map.put("陶瓷罐","PMC6744307310403199696896-40");
            map.put("陶瓷瓶","PMC6744307310403199696896-40");
            map.put("其他陶瓷包装容器","PMC6744307310403199696896-40");
            map.put("DIY家饰","PMC6738307310400095911936-40");
            map.put("领带","PMC6086307309984746569728-40");
            map.put("领结/领花","PMC6086307309984746569728-40");
            map.put("防滑耳套、鼻托","PMC6413307310195837501440-40");
            map.put("一次性日用口罩","PMC6090307309987162488832-40");
            map.put("生活口罩","PMC6090307309987162488832-40");
            map.put("一次性口罩","PMC6090307309987162488832-40");
            map.put("防尘口罩","PMC6090307309987162488832-40");
            map.put("食品口罩","PMC6090307309987162488832-40");
            map.put("电动新风口罩","PMC6090307309987162488832-40");
            map.put("婴儿胎发牙齿收纳屋","PMC7708307311031355441152-40");
            map.put("毛绒公仔、玩偶、娃娃","PMC7669307311006688739328-40");
            map.put("木马/摇马","PMC7698307311025143676928-40");
            map.put("母乳储存保鲜","PMC7608307310970747748352-40");
            map.put("奶杯/牛奶杯","PMC7608307310970747748352-40");
            map.put("牛奶乳品包装","PMC7608307310970747748352-40");
            map.put("麻将桌","PMC7695307311022950055936-40");
            map.put("麻将","PMC7695307311022950055936-40");
            map.put("麻将机","PMC7695307311022950055936-40");
            map.put("麻将席","PMC7695307311022950055936-40");
            map.put("其他干货","PMC6571307310294307176448-40");
            map.put("水产干货类","PMC6571307310294307176448-40");
            map.put("舞蹈服","PMC7996307311207499431936-40");
            map.put("运动裙","PMC7989307311202302689280-40");
            map.put("运动头带","PMC7951307311179917688832-40");
            map.put("男式毛衫背心","PMC7940307311173005475840-40");
            map.put("羽绒背心","PMC7940307311173005475840-40");
            map.put("棉服背心","PMC7940307311173005475840-40");
            map.put("速干背心","PMC7940307311173005475840-40");
            map.put("足球训练背心(分组对抗)","PMC7940307311173005475840-40");
            map.put("男士背心","PMC7940307311173005475840-40");
            map.put("防刺背心","PMC7940307311173005475840-40");
            map.put("泳帽","PMC7944307311175555612672-40");
            map.put("独木舟","PMC8033307311230769430528-40");
            map.put("漂流船、皮划艇、充气艇","PMC8035307311231797035008-40");
            map.put("划船机","PMC8036307311232535232512-40");
            map.put("手球","PMC8095307311266521677824-40");
            map.put("足球","PMC8093307311265213054976-40");
            map.put("曲棍球","PMC8096307311267247292416-40");
            map.put("垒球","PMC8028307311227120386048-40");
            map.put("攀登架","PMC8053307311242823860224-40");
            map.put("体操服、啦啦队服","PMC8094307311265838006272-40");
            map.put("体操垫、运动垫子","PMC8094307311265838006272-40");
            map.put("跳伞用品","PMC8105307311272712470528-40");
            map.put("赛车服","PMC8103307311271324155904-40");
            map.put("电竞椅","PMC8090307311263904432128-40");
            map.put("电竞桌","PMC8090307311263904432128-40");
            map.put("电竞周边外设","PMC8090307311263904432128-40");
            map.put("长款项链","PMC6496307310247343554560-40");
            map.put("项链","PMC6496307310247343554560-40");
            map.put("吊坠","PMC6496307310247343554560-40");
            map.put("戒指","PMC6497307310247997865984-40");
            map.put("天然水晶戒指","PMC6497307310247997865984-40");
            map.put("玛瑙戒指","PMC6497307310247997865984-40");
            map.put("手链","PMC6491307310244764057600-40");
            map.put("耳环","PMC6494307310246001377280-40");
            map.put("钻石首饰套装","PMC6495307310246693437440-40");
            map.put("天然玉石首饰套装","PMC6495307310246693437440-40");
            map.put("舞蹈/健美操/体操配饰","PMC6493307310245380620288-40");
            map.put("情趣配饰","PMC6493307310245380620288-40");
            map.put("军迷配饰","PMC6493307310245380620288-40");
            map.put("天然珍珠项饰","PMC6484307310240339066880-40");
            map.put("天然珍珠手饰","PMC6479307310236975235072-40");
            map.put("天然珍珠耳饰","PMC6482307310238980112384-40");
            map.put("天然珍珠首饰套装","PMC6483307310239672172544-40");
            map.put("天然珍珠胸饰","PMC6480307310237612769280-40");
            map.put("天然珍珠其他饰品","PMC6481307310238241914880-40");
            map.put("琥珀项饰","PMC6458307310224492986368-40");
            map.put("琥珀耳饰","PMC6455307310223192752128-40");
            map.put("琥珀首饰套装","PMC6456307310223805120512-40");
            map.put("琥珀手饰","PMC6454307310222551023616-40");
            map.put("天然水晶半成品饰品","PMC6488307310242889203712-40");
            map.put("玛瑙半成品饰品","PMC6488307310242889203712-40");
            map.put("电磁炉","PMC7287307310777621020672-40");
            map.put("电磁炉","PMC7287307310777621020672-40");
            map.put("电火锅","PMC7283307310774198468608-40");
            map.put("视频服务器","PMC6339307310143370952704-40");
            map.put("服务器、工作站","PMC6339307310143370952704-40");
            map.put("其他整机、服务器","PMC6339307310143370952704-40");
            map.put("服务器配件","PMC6339307310143370952704-40");
            map.put("串口服务器","PMC6339307310143370952704-40");
            map.put("教育教学软件","PMC7513307310914955116544-40");
            map.put("其他无线网络设备","PMC7525307310921015885824-40");
            map.put("浴缸","PMC6784307310425614057472-200");
            map.put("床架/床板","PMC6909307310512306126848-40");
            map.put("割草机","PMC6982307310570690838528-40");
            map.put("宿根花卉","PMC6992307310577938595840-200");
            map.put("一、二年生花卉","PMC6992307310577938595840-200");
            map.put("球根花卉","PMC6992307310577938595840-200");
            map.put("手工刨","PMC7205307310726920273920-40");
            map.put("钟表维修工具套装","PMC7243307310749464657920-40");
            map.put("成人纸尿裤","PMC6646307310342365511680-200");
            map.put("头皮护理用品","PMC6210307310060663472128-200");
            map.put("宠物帽子","PMC7876307311134224941056-40");
            map.put("宠物袜子","PMC7876307311134224941056-40");
            map.put("宠物自背包","PMC7876307311134224941056-40");
            map.put("宠物项圈","PMC7876307311134224941056-40");
            map.put("宠物围巾","PMC7876307311134224941056-40");
            map.put("宠物头饰","PMC7876307311134224941056-40");
            map.put("宠物挂牌/挂饰","PMC7876307311134224941056-40");
            map.put("宠物鞋靴","PMC7876307311134224941056-40");
            map.put("宠物绝育服/生理裤","PMC7876307311134224941056-40");
            map.put("其他水族宠物饲料","PMC7879307311135520980992-40");
            map.put("鸟食","PMC7880307311136577945600-40");
            map.put("相册","PMC6860307310475022958592-200");
            map.put("车载充气泵","PMC7146307310688978599936-40");
            map.put("真空泵","PMC7146307310688978599936-40");
            map.put("增压泵","PMC7146307310688978599936-40");
            map.put("轴流泵","PMC7146307310688978599936-40");
            map.put("混流泵","PMC7146307310688978599936-40");
            map.put("旋涡泵","PMC7146307310688978599936-40");
            map.put("柱塞泵","PMC7146307310688978599936-40");
            map.put("喷射泵","PMC7146307310688978599936-40");
            map.put("往复泵","PMC7146307310688978599936-40");
            map.put("齿轮泵","PMC7146307310688978599936-40");
            map.put("消防泵","PMC7146307310688978599936-40");
            map.put("空调泵","PMC7146307310688978599936-40");
            map.put("转子泵","PMC7146307310688978599936-40");
            map.put("隔膜泵","PMC7146307310688978599936-40");
            map.put("计量泵","PMC7146307310688978599936-40");
            map.put("软管泵","PMC7146307310688978599936-40");
            map.put("磁力泵","PMC7146307310688978599936-40");
            map.put("电动气泵","PMC7146307310688978599936-40");
            map.put("冷凝水泵","PMC7146307310688978599936-40");
            map.put("油气混输泵","PMC7146307310688978599936-40");
            map.put("循环水泵","PMC7146307310688978599936-40");
            map.put("灰渣泵","PMC7146307310688978599936-40");
            map.put("混凝土输送泵","PMC7146307310688978599936-40");
            map.put("泵配件","PMC7146307310688978599936-40");
            map.put("其他泵","PMC7146307310688978599936-40");
            map.put("充气泵","PMC7146307310688978599936-40");
            map.put("液压泵","PMC7146307310688978599936-40");
            map.put("液压制动泵","PMC7146307310688978599936-40");
            map.put("制动总泵","PMC7146307310688978599936-40");
            map.put("制动分泵","PMC7146307310688978599936-40");
            map.put("离合器总泵","PMC7146307310688978599936-40");
            map.put("离合器分泵","PMC7146307310688978599936-40");
            map.put("油泵、油嘴","PMC7146307310688978599936-40");
            map.put("助力器、助力泵","PMC7146307310688978599936-40");
            map.put("水泵","PMC7146307310688978599936-40");
            map.put("管道辅助材料","PMC7145307310687984549888-40");
            map.put("其他管道系统","PMC7145307310687984549888-40");
            map.put("其他管道工具","PMC7145307310687984549888-40");
            map.put("其他阀门","PMC7148307310689981038592-40");
            map.put("消毒抑菌用品加工","PMC7183307310713787908096-40");
            map.put("个人护理加工","PMC7183307310713787908096-40");
            map.put("女性护理加工","PMC7183307310713787908096-40");
            map.put("日化用品加工","PMC7183307310713787908096-40");
            map.put("生活用纸加工","PMC7183307310713787908096-40");
            map.put("假发加工","PMC7183307310713787908096-40");
            map.put("其他厨房电器","PMC7290307310779009335296-40");
            map.put("其他厨房电器","PMC7290307310779009335296-40");
            map.put("电热毯、电热垫","PMC7312307310792598880256-40");
            map.put("电热毯","PMC7312307310792598880256-40");
            map.put("牙刷消毒器","PMC7340307310811234172928-40");
            map.put("电子产品消毒器","PMC7340307310811234172928-40");
            map.put("内衣消毒器","PMC7340307310811234172928-40");
            map.put("点读机/点读笔","PMC7534307310927143763968-40");
            map.put("3D打印耗材","PMC6386307310173880320000-40");
            map.put("传真机","PMC6394307310180524097536-40");
            map.put("墨盒、墨水","PMC6389307310176195575808-40");
            map.put("硒鼓、粉盒","PMC6389307310176195575808-40");
            map.put("墨水","PMC6389307310176195575808-40");
            map.put("台历/日历","PMC8163307311307122540544-40");
            map.put("办公学习垫板","PMC8164307311307919458304-40");
            map.put("办公展示架","PMC8164307311307919458304-40");
            map.put("其他办公设备","PMC8164307311307919458304-40");
            map.put("库存办公、文教用品","PMC8164307311307919458304-40");
            map.put("指示标签、手写标签","PMC8159307311304761147392-40");
            map.put("邮票","PMC8159307311304761147392-40");
            map.put("商标纸/标签纸","PMC8159307311304761147392-40");
            map.put("标签打印纸","PMC8159307311304761147392-40");
            map.put("商标、标签纸","PMC8159307311304761147392-40");
            map.put("快递标签","PMC8159307311304761147392-40");
            map.put("不干胶标签","PMC8159307311304761147392-40");
            map.put("纸类标签","PMC8159307311304761147392-40");
            map.put("塑料、塑胶标签","PMC8159307311304761147392-40");
            map.put("智能标签、名片","PMC8159307311304761147392-40");
            map.put("防盗标签","PMC8159307311304761147392-40");
            map.put("平板电脑保护套","PMC7506307310910769201152-40");
            map.put("平板电脑充电器","PMC7503307310908747546624-40");
            map.put("其他平板电脑配件","PMC7520307310918667075584-40");
            map.put("保护膜","PMC7529307310924648153088-40");
            map.put("平板电脑保护膜","PMC7529307310924648153088-40");
            map.put("平板电脑外接键盘","PMC7519307310918050512896-40");
            map.put("平板电脑支架","PMC7521307310919279443968-40");
            map.put("手写笔","PMC7517307310916733501440-40");
            map.put("平板电脑包","PMC7518307310917467504640-40");
            map.put("湿巾纸","PMC7021307310600386510848-200");
            map.put("婴儿湿巾","PMC7021307310600386510848-200");
            map.put("工业湿巾","PMC7021307310600386510848-200");
            map.put("广告纸巾","PMC7015307310593369440256-200");
            map.put("婴儿纸巾","PMC7015307310593369440256-200");
            map.put("保湿纸巾","PMC7015307310593369440256-200");
            map.put("阀用电磁铁","PMC7211307310730518986752-40");
            map.put("电子磁性材料(电磁铁)","PMC7211307310730518986752-40");
            map.put("一次性鞋套","PMC7003307310585136021504-200");
            map.put("健身服","PMC7964307311187668762624-40");
            map.put("健身套装","PMC7964307311187668762624-40");
            map.put("健身踏板/韵律踏板","PMC7964307311187668762624-40");
            map.put("健身车","PMC7964307311187668762624-40");
            map.put("健身椅","PMC7964307311187668762624-40");
            map.put("弹力棒/健身棒","PMC7964307311187668762624-40");
            map.put("组合健身用品","PMC7964307311187668762624-40");
            map.put("智能健身镜","PMC7964307311187668762624-40");
            map.put("婴儿健身架","PMC7964307311187668762624-40");
            map.put("婴儿健身玩具","PMC7964307311187668762624-40");
            map.put("坐垫/椅垫/美臀垫","PMC6821307310448582066176-40");
            map.put("保护器","PMC6468307310230822191104-40");
            map.put("分体雨衣、雨披","PMC7386307310838471983104-40");
            map.put("连体雨衣、雨披","PMC7386307310838471983104-40");
            map.put("其他雨衣、雨披","PMC7386307310838471983104-40");
            map.put("工作雨衣","PMC7386307310838471983104-40");
            map.put("办公礼品套装","PMC6091307309987758080000-40");
            map.put("商务礼品套装","PMC6091307309987758080000-40");
            map.put("创意礼品套装","PMC6091307309987758080000-40");
            map.put("汽车安全带","PMC7570307310948278861824-40");
            map.put("安全带护肩","PMC7570307310948278861824-40");
            map.put("安全带","PMC7570307310948278861824-40");
            map.put("平衡机","PMC7962307311186217533440-40");
            map.put("平衡车","PMC7962307311186217533440-40");
            map.put("儿童平衡车","PMC7962307311186217533440-40");
            map.put("平衡器","PMC7962307311186217533440-40");
            map.put("平衡块","PMC7962307311186217533440-40");
            map.put("明星海报/写真","PMC6858307310473760473088-200");
            map.put("动漫海报","PMC6858307310473760473088-200");
            map.put("宣传单/海报","PMC6858307310473760473088-200");
            map.put("蒸汽面罩","PMC7939307311172279861248-40");
            map.put("骑行面罩","PMC7939307311172279861248-40");
            map.put("骑行鞋","PMC8135307311290274021376-40");
            map.put("儿童防晒衣/皮肤衣","PMC7991307311203917496320-40");
            map.put("防晒衣","PMC7991307311203917496320-40");
            map.put("运动内衣","PMC8004307311213233045504-40");
            map.put("摩托车安全头盔","PMC7748307311055946645504-10");
            map.put("摩托车头盔配件","PMC7748307311055946645504-10");
            map.put("摩托车(艇)润滑油","PMC7791307311083205427200-10");
            map.put("电影道具/周边","PMC7753307311058891046912-10");
            map.put("颈部护理","PMC6186307310046285398016-200");
            map.put("洗手液","PMC6225307310070104850432-200");
            map.put("抑菌洗手液","PMC6225307310070104850432-200");
            map.put("一次性剃须用具","PMC6272307310098793889792-200");
            map.put("剃须插座","PMC6268307310096759652352-200");
            map.put("剃须刀充电器","PMC6268307310096759652352-200");

        }
    }

}
