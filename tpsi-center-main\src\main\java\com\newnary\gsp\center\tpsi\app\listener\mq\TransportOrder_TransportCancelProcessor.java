package com.newnary.gsp.center.tpsi.app.listener.mq;

import com.newnary.gsp.center.tpsi.app.service.transport.TransportOrderCommandApp;
import com.newnary.gsp.center.tpsi.infra.mq.consumer.TransportOrderCancelConsumer;
import com.newnary.messagebody.gsp.logistics.GSPTransportOrderCancelTopic;
import com.newnary.messagebody.gsp.logistics.mo.TransportOrderCancelMO;
import com.newnary.mq.starter.consumer.AbstractMQProcessor;
import com.newnary.mq.starter.consumer.MQMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class TransportOrder_TransportCancelProcessor extends AbstractMQProcessor<TransportOrderCancelMO> {

    @Resource
    private TransportOrderCommandApp transportOrderCommandApp;

    @Override
    public boolean doProcess(MQMessage<TransportOrderCancelMO> message) {
        log.info("修改第三方订单为已作废");
        TransportOrderCancelMO mo = message.getContent();
        transportOrderCommandApp.transportOrderCancel(mo.referenceId, mo.transportOrderId, mo.cancelType);
        return true;
    }

    @Override
    public Class<?> consumerClz() {
        return TransportOrderCancelConsumer.class;
    }

    @Override
    public String tag() {
        return GSPTransportOrderCancelTopic.Tag.TRANSPORT_CANCELING;
    }
}
