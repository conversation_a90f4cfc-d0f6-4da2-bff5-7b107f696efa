package com.newnary.gsp.center.tpsi.infra.mapper;

import com.newnary.gsp.center.tpsi.infra.model.vo.DeliveryOrderItemEcCangApiAssociation;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.DeliveryOrderItemEcCangApiAssociationPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface DeliveryOrderItemEcCangApiAssociationMapper {

    DeliveryOrderItemEcCangApiAssociationMapper INSTANCE = Mappers.getMapper(DeliveryOrderItemEcCangApiAssociationMapper.class);

    List<DeliveryOrderItemEcCangApiAssociation> pos2Models(List<DeliveryOrderItemEcCangApiAssociationPO> pos);

    DeliveryOrderItemEcCangApiAssociationPO model2Po(DeliveryOrderItemEcCangApiAssociation model);
}
