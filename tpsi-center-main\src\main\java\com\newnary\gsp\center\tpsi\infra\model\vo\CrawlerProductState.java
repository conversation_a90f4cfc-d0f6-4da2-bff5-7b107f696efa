package com.newnary.gsp.center.tpsi.infra.model.vo;

/**
 * <AUTHOR>
 * @Date 2023/04/07 17:08
 */
public enum CrawlerProductState {

    /**
     *未入库
     */
    UN_WAREHOUSED(0),

    /**
     * 入库
     */
    SUCCESS_WAREHOUSED(1),

    /**
     * 入库失败
     */
    FAIL_WAREHOUSED(2),

    /**
     * 已下架或者失效商品
     */
    FAILURE_PRODUCT(20);


    private Integer state;

    CrawlerProductState(Integer state) {
        this.state = state;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }
}
