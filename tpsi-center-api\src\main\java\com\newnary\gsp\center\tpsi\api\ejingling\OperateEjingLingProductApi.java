package com.newnary.gsp.center.tpsi.api.ejingling;

import com.newnary.api.base.common.CommonResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@RequestMapping("/tpsi-center/EJINGLING")
public interface OperateEjingLingProductApi {

    @PostMapping("updateProductInfo")
    CommonResponse<String> updateProductInfo(List<String> customCodeList);
}
