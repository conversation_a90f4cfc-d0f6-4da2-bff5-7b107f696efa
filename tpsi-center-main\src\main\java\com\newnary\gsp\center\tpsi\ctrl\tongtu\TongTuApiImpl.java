package com.newnary.gsp.center.tpsi.ctrl.tongtu;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.gsp.center.tpsi.api.tongtu.TongTuApi;
import com.newnary.gsp.center.tpsi.api.tongtu.request.SyncStockFromTongTuCommand;
import com.newnary.gsp.center.tpsi.service.ISyncProductBizSve;
import com.newnary.gsp.center.tpsi.service.tongtu.ITongTuErp2Sve;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class TongTuApiImpl implements TongTuApi {

    @Resource
    private ISyncProductBizSve syncProductBizSve;

    @Resource
    private ITongTuErp2Sve tongTuErp2Sve;

    @Override
    public CommonResponse<Void> syncProductFromTongTu(String thirdPartySystemId) {
        syncProductBizSve.syncTongTuProduct(thirdPartySystemId);
        return CommonResponse.successWithoutBody();
    }

    @Override
    public CommonResponse<Integer> syncStockFromTongTu(SyncStockFromTongTuCommand req) {
        return CommonResponse.success(
                tongTuErp2Sve.stocksQuery(req)
        );
    }
    /*@Override
    public CommonResponse<String> syncWarehouseFromTongTu(String thirdPartySystemId) {
        return CommonResponse.success(
                tongTuErp2Sve.warehouseQuery(thirdPartySystemId)
        );
    }*/

}
