package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp;

import lombok.Data;

@Data
public class EcCangERPGetWarehouseLocationRequest {

    private Integer page;
    private Integer pageSize;

    //是否被锁 :0
    //否，1
    //是
    private Integer lcLock;

    //是否空库位：2
    //否，1
    //是
    private Integer lcEmpty;

    //冻结状态：0
    //否，1
    //是
    private Integer lcHold;

    //库位代码模糊
    private String  lcCodeLike;

    //库位代码可查多个，格式["A","B"]
    private String lcCodes;

    private Integer warehouseId;

    //库位类型代码
    private String ltCode;

    //分区代码
    private String waCode;
}
