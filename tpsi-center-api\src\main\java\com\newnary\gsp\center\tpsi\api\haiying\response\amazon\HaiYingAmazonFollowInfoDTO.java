package com.newnary.gsp.center.tpsi.api.haiying.response.amazon;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: jack
 * @CreateTime: 2022-9-2
 */
@Data
public class HaiYingAmazonFollowInfoDTO {

    /**
     * 商品id
     */
    private String asin;

    /**
     * 父类asin
     */
    private String parent_asin;

    /**
     * 店铺编码
     */
    private String merchant_code;

    /**
     * 发货地址
     */
    private String delivery;

    /**
     * 店铺名
     */
    private String merchant;

    /**
     * 店铺卖家评论数
     */
    private Integer merchant_review;

    /**
     * 商品卖家类型
     */
    private String saler_type;

    /**
     * 商品跟卖价格
     */
    private BigDecimal follow_price;

    /**
     * 最新抓取日期
     */
    private Long ins_date;

}
