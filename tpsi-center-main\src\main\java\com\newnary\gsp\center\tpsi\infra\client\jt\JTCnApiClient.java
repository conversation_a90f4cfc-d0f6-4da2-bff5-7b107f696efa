package com.newnary.gsp.center.tpsi.infra.client.jt;

import com.alibaba.fastjson.JSONObject;
import com.newnary.gsp.center.tpsi.api.common.ApiPaths;
import com.newnary.gsp.center.tpsi.api.common.util.MD5Util;
import com.newnary.gsp.center.tpsi.api.jt.response.cn.JTCnApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.jt.params.JTCnParam;
import com.newnary.gsp.center.tpsi.infra.rpc.SpaceFileRpc;
import com.newnary.spring.cloud.extend.SpringContext;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.Buffer;

import java.util.Date;


/**
 * 中国极兔-对接
 *
 * <AUTHOR>
 */
@Slf4j
public class JTCnApiClient {


    private static final SpaceFileRpc spaceFileRpc = SpringContext.context().getBean(SpaceFileRpc.class);

    MediaType jsonMediaType = MediaType.get("application/json; charset=utf-8");

    final Long currentTimeMillis = new Date().getTime();

    @Getter
    private JTCnParam param;

    public JTCnApiClient(String jtJsonParam) {
        JTCnParam params = JSONObject.parseObject(jtJsonParam, JTCnParam.class);
        this.param = params;
    }


    private void addHeaders(Request.Builder builder, String command, long currentTimeMillis) {
        builder.addHeader("apikey", param.getApiKey())
                .addHeader("signature", getSignature(command, currentTimeMillis))
                .addHeader("timestamp", String.valueOf(currentTimeMillis))
                .addHeader("usertoken", param.getUserToken())
                .addHeader("Content-Type", "application/json;charset=utf-8");
    }

    /**
     * 极兔-中国-物流  创建&预报 订单接口
     *
     * @param command 请求体
     * @return
     */
    public JTCnApiBaseResult<String> createOrder(String command) {
        log.info("JT-CN 创建并预报订单 command: {} ", command);


        RequestBody requestBody = RequestBody.create(command, jsonMediaType);

        try {
            final Buffer buffer = new Buffer();
            requestBody.writeTo(buffer);
            log.info("JT-CN 创建并预报订单: {} ", buffer.readUtf8());

            OkHttpClient okHttpClient = new OkHttpClient();
            Request.Builder builder = new Request.Builder()
                    .url(param.getBaseUrl().concat(ApiPaths.JtCn.CREATE_ORDER_METHOD))
                    .post(requestBody);
            this.addHeaders(builder, command, currentTimeMillis);
            final Request request = builder.build();


            try (Response response = okHttpClient.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    String responseBody = response.body().string();
                    log.info("JT-CN 创建并预报订单 结束, response: {}", responseBody);
                    System.out.println(responseBody);
                    return this.buildDataBaseResult(responseBody);
                } else {
                    log.error("JT-CN 创建并预报订单 请求失败, status: {}", response.code());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("JT-CN 创建并预报订单异常: {}", e);
        }
        return new JTCnApiBaseResult<>();
    }


    private String getSignature(String params, Long currentTimeMillis) {
        final String signature = param.getApiKey() +
                param.getApiSecret() +
                param.getUserToken() +
                currentTimeMillis +
                params;
        return MD5Util.encryptMD5(signature);
    }


    /**
     * 面单绘制
     *
     * @param command
     * @return
     */
    public JTCnApiBaseResult<String> printSheet(String command) {
        log.info("JT-CN 面单获取 command: {} ", command);

        RequestBody requestBody = RequestBody.create(command, jsonMediaType);
        try {
            final Buffer buffer = new Buffer();
            requestBody.writeTo(buffer);
            log.info("JT-CN 面单获取: {} ", buffer.readUtf8());

            OkHttpClient okHttpClient = new OkHttpClient();
            Request.Builder builder = new Request.Builder()
                    .url(param.getBaseUrl().concat(ApiPaths.JtCn.QUERY_LABEL_METHOD))
                    .post(requestBody);
            addHeaders(builder, command, currentTimeMillis);
            final Request request = builder.build();

            try (Response response = okHttpClient.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    String responseBody = response.body().string();
                    log.info("JT-CN 面单获取 结束, response: {}", responseBody);
                    System.out.println(responseBody);
                    return this.buildPrintSheetDataBaseResult(responseBody);
                } else {
                    log.error("JT-CN 面单获取 请求失败, status: {}", response.code());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("JT-CN 面单获取异常: {}", e);
        }
        return new JTCnApiBaseResult<>();
    }


    /**
     * 获取所有物流渠道
     *
     * @return
     */
    public JTCnApiBaseResult<String> queryLogistics() {

        RequestBody emptyBody = RequestBody.create("", jsonMediaType);

        try {
            OkHttpClient okHttpClient = new OkHttpClient();
            Request.Builder builder = new Request.Builder()
                    .url(param.getBaseUrl().concat(ApiPaths.JtCn.QUERY_LOGISTICS_METHOD))
                    .post(emptyBody);
            addHeaders(builder, null, currentTimeMillis);
            final Request request = builder.build();

            try (Response response = okHttpClient.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    String responseBody = response.body().string();
                    log.info("JT-CN 获取所有物流渠道结束, response: {}", responseBody);
                    System.out.println(responseBody);
                    return this.buildDataBaseResult(responseBody);
                } else {
                    log.error("JT-CN 面单获取请求失败, status: {}", response.code());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("JT-CN 所有物流产品获取异常: {}", e);
        }
        return new JTCnApiBaseResult<>();
    }


    /**
     * 轨迹获取
     *
     * @param command
     * @return
     */
    public JTCnApiBaseResult<String> queryTrack(String command) {

        log.info("JT-CN 轨迹获取 command: {} ", command);

        RequestBody requestBody = RequestBody.create(command, jsonMediaType);
        try {
            final Buffer buffer = new Buffer();
            requestBody.writeTo(buffer);
            log.info("JT-CN 轨迹获取: {} ", buffer.readUtf8());

            OkHttpClient okHttpClient = new OkHttpClient();
            Request.Builder builder = new Request.Builder()
                    .url(param.getBaseUrl().concat(ApiPaths.JtCn.QUERY_TRACK_METHOD))
                    .post(requestBody);
            addHeaders(builder, command, currentTimeMillis);
            final Request request = builder.build();


            try (Response response = okHttpClient.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    String responseBody = response.body().string();
                    log.info("JT-CN 轨迹获取 结束, response: {}", responseBody);
                    System.out.println(responseBody);
                    return this.buildDataBaseResult(responseBody);
                } else {
                    log.error("JT-CN 轨迹获取 请求失败, status: {}", response.code());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("JT-CN 轨迹获取: {}", e);
        }
        return new JTCnApiBaseResult<>();
    }


    /**
     * 取消预报
     *
     * @param command
     * @return
     */
    public JTCnApiBaseResult<String> cancelOrder(String command) {

        log.info("JT-CN 取消预报 command: {} ", command);

        RequestBody requestBody = RequestBody.create(command, jsonMediaType);

        try {
            final Buffer buffer = new Buffer();
            requestBody.writeTo(buffer);
            log.info("JT-CN 取消预报: {} ", buffer.readUtf8());

            OkHttpClient okHttpClient = new OkHttpClient();
            Request.Builder builder = new Request.Builder()
                    .url(param.getBaseUrl().concat(ApiPaths.JtCn.CANCEL_ORDER))
                    .post(requestBody);
            addHeaders(builder, command, currentTimeMillis);
            final Request request = builder.build();


            try (Response response = okHttpClient.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    String responseBody = response.body().string();
                    log.info("JT-CN 取消预报 结束, response: {}", responseBody);
                    System.out.println(responseBody);
                    return this.buildDataBaseResult(responseBody);
                } else {
                    log.error("JT-CN 取消预报 请求失败, status: {}", response.code());
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.error("JT-CN 取消预报异常: {}", e);
        }
        return new JTCnApiBaseResult<>();
    }


    /**
     * 查询订单账单
     *
     * @param command
     * @return
     */
    public JTCnApiBaseResult<String> queryBill(String command) {

        log.info("JT-CN 查询订单账单 command: {} ", command);

        RequestBody requestBody = RequestBody.create(command, jsonMediaType);

        try {
            final Buffer buffer = new Buffer();
            requestBody.writeTo(buffer);
            log.info("JT-CN 查询订单账单: {} ", buffer.readUtf8());

            OkHttpClient okHttpClient = new OkHttpClient();
            Request.Builder builder = new Request.Builder()
                    .url(param.getBaseUrl().concat(ApiPaths.JtCn.QUERY_BILL))
                    .post(requestBody);
            addHeaders(builder, command, currentTimeMillis);
            final Request request = builder.build();


            try (Response response = okHttpClient.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    String responseBody = response.body().string();
                    log.info("JT-CN 查询订单账单 结束, response: {}", responseBody);
                    System.out.println(responseBody);
                    return this.buildDataBaseResult(responseBody);
                } else {
                    log.error("JT-CN 查询订单账单 请求失败, status: {}", response.code());
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.error("JT-CN 查询订单账单 异常: {}", e);
        }
        return new JTCnApiBaseResult<>();
    }



    private JTCnApiBaseResult<String> buildDataBaseResult(String resultStr) {
        JTCnApiBaseResult<String> apiBaseResult = new JTCnApiBaseResult<>();
        final JSONObject jsonObject = JSONObject.parseObject(resultStr);
        apiBaseResult.setMessage((String) jsonObject.get("msg"));
        apiBaseResult.setCode((Integer) jsonObject.get("code"));
        final Object dataObject = jsonObject.get("data");
        apiBaseResult.setResult(dataObject == null ? null : dataObject.toString());
        return apiBaseResult;
    }

    private JTCnApiBaseResult<String> buildPrintSheetDataBaseResult(String resultStr) {
        JTCnApiBaseResult<String> apiBaseResult = new JTCnApiBaseResult<>();
        final JSONObject jsonObject = JSONObject.parseObject(resultStr);
        apiBaseResult.setMessage((String) jsonObject.get("msg"));
        apiBaseResult.setCode((Integer) jsonObject.get("code"));
        apiBaseResult.setResult(resultStr);
        return apiBaseResult;
    }

}
