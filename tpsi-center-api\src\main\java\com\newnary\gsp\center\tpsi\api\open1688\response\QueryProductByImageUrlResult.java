package com.newnary.gsp.center.tpsi.api.open1688.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Data
public class QueryProductByImageUrlResult {

    /**
     * 历史成交信息
     */
    private List<OfferHistoryTradeInfo> offerHistoryTradeInfo;

    /**
     * 商品ID
     */
    private String offerId;

    /**
     * 商品图片信息
     */
    private OfferImage offerImage;

    /**
     * 商品价格信息
     */
    private OfferPrice offerPrice;

    /**
     * 商品服务
     */
    private List<OfferTradeServiceInfo> offerTradeServiceInfo;

    /**
     * 商品评价分
     */
    private QualityEvaluation qualityEvaluation;

    /**
     * 标题
     */
    private String subject;

    @Setter
    @Getter
    public static class OfferHistoryTradeInfo {

        /**
         * 历史交易信息key
         */
        private String historyTradeKey;

        /**
         * 历史交易信息value
         */
        private String historyTradeValue;
    }

    @Setter
    @Getter
    public static class OfferImage {

        /**
         * 图片地址
         */
        private String imageUrl;
    }

    @Setter
    @Getter
    public static class OfferPrice {

        /**
         * 分销价
         */
        private String consignPrice;

        /**
         * 市场价
         */
        private String price;

        /**
         * 价格类型
         */
        private String priceType;

        /**
         * 划线价
         */
        private String priceUnderLine;

        /**
         * 数量区间报价
         */
        private List<QuantityPrice> quantityPrice;
    }

    @Setter
    @Getter
    public static class QuantityPrice {

        /**
         * 数量区间
         */
        private String quantity;

        /**
         * 价格
         */
        private String value;
    }

    @Setter
    @Getter
    public static class OfferTradeServiceInfo {
        /**
         * 是否生效
         */
        private Boolean enable;

        /**
         * 服务名称
         */
        private String serviceName;

        /**
         * 服务tag
         */
        private String serviceTag;

        /**
         * 服务类型
         */
        private String serviceType;
    }

    @Setter
    @Getter
    public static class QualityEvaluation {

        /**
         * 综合得分
         */
        private BigDecimal compositeScore;

        /**
         *采购咨询分
         */
        private BigDecimal consultationScore;

        /**
         * 纠纷解决分
         */
        private BigDecimal disputeScore;

        /**
         * 品质体验分
         */
        private BigDecimal goodsScore;

        /**
         * 物流时效分
         */
        private BigDecimal logisticsScore;

        /**
         * 退换体验分
         */
        private BigDecimal returnScore;
    }

}
