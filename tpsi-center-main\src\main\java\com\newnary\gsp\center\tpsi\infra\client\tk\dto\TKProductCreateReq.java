package com.newnary.gsp.center.tpsi.infra.client.tk.dto;


import com.newnary.gsp.center.tpsi.infra.client.tk.dto.vo.SaleAttribute;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class TKProductCreateReq {
    public String product_name;
    public String description;
    public String category_id;
    public String brand_id;
    public List<Image> images;
    public Integer warranty_period;
    public String warranty_policy;
    public Integer package_length;
    public Integer package_width;
    public Integer package_height;
    public String package_weight;
    public SizeChart size_chart;
    public List<ProductCertification> product_certifications;
    public boolean is_cod_open;
    public List<Sku> skus;
    public List<String> delivery_service_ids;
    public List<ProductAttribute> product_attributes;
    public ExemptionOfIdentifierCode exemption_of_identifier_code;
    public String package_dimension_unit;
    public ProductVideo product_video;
    public String outer_product_id;


    @Getter
    @Setter
   public static class SizeChart {
        public String img_id;
    }


    @Getter
    @Setter
    public static class ProductCertification {
        public String id;
        public List<Image> images;
        public List<File> files;
    }

    @Getter
    @Setter
    public static class File {
        public String id;
        public String name;
        public String type;
    }

    @Getter
    @Setter
    public  static class Sku {
        public List<SaleAttribute> sales_attributes;
        public List<StockInfo> stock_infos;
        public String seller_sku;
        public String original_price;
        public ProductIdentifierCode product_identifier_code;
        public String outer_sku_id;
    }


    @Getter
    @Setter
    public  static class StockInfo {
        public String warehouse_id;
        public Integer available_stock;
    }

    // Code type value: 1-GTIN、2-EAN、3-UPC、4-ISBN (input one of them to this field)
    @Getter
    @Setter
    @AllArgsConstructor
    public  static class ProductIdentifierCode {
        public String identifier_code;
        public Integer identifier_code_type;
    }

    @Getter
    @Setter
    public static class ProductAttribute {
        public String attribute_id;
        public List<ProductAttributeValue> attribute_values;
    }


    @Getter
    @Setter
    public  static class ProductAttributeValue {
        public String value_id;
        public String value_name;
    }

    //This is the exemption reason of the product：
//input one of them Integero this field, Type value：
//1-Brand or manufacturer does not provide a GTIN,
//2-Generic or non-branded product without GTIN,
//3-Product bundle,
//4-Product part without a GTIN
    @Getter
    @Setter
    public  static class ExemptionOfIdentifierCode {
        public String exemption_reason;
    }

    @Getter
    @Setter
    public  static class ProductVideo {
        public String video_id;
    }


    @Getter
    @Setter
    public  static class Image {
        public String id;
    }
}







