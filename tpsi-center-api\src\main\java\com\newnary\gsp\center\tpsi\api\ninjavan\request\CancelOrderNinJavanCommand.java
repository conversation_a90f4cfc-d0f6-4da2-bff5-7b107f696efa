package com.newnary.gsp.center.tpsi.api.ninjavan.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 「取消」能者物流运输单，请求体
 *
 * <AUTHOR>
 * @since Created on 2023-08-11
 **/
@Data
public class CancelOrderNinJavanCommand {


    /**
     * 追踪号:能者物流返回的追踪号。
     **/
    @NotBlank(message = "能者物流追踪号不能为空")
    private String trackingId;

    /**
     * 取消原因
     **/
    @NotBlank(message = "取消原因(不能为空)")
    private String cancelReason;



}
