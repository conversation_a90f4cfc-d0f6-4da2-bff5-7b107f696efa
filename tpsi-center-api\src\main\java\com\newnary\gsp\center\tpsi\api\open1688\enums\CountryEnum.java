package com.newnary.gsp.center.tpsi.api.open1688.enums;

import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;

import java.util.Arrays;

public enum CountryEnum {

    EN("en", "英语"),
    JA("ja", "日语"),
    K<PERSON>("ko", "韩语"),
    RU("ru", "俄罗斯语"),
    VI("vi", "越南语"),
    FR("fr", "法语"),
    PT("pt", "葡萄牙语"),
    ZHTW("zh-tw", "繁体中文"),
    ES("es", "西班牙语"),
    ID("id", "印尼语"),
    TH("th", "泰语");


    private String name;
    private String value;

    CountryEnum(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public String getValue() {
        return value;
    }

    public static String getInstance(String type) {

        return Arrays.stream(values())
                .filter(countryEnum -> type.equals(countryEnum.getName()))
                .map(CountryEnum::name)
                .findFirst()
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "错误的语言类型: " + type));
    }

}
