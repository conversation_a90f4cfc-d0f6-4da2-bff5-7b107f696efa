package com.newnary.gsp.center.tpsi.infra.rpc;

import com.newnary.gsp.center.logistics.api.warehouse.feign.WarehouseFeignApi;
import com.newnary.gsp.center.logistics.api.warehouse.request.WarehouseGetByCustomCodeReq;
import com.newnary.gsp.center.logistics.api.warehouse.response.ReceiptWarehouseRes;
import com.newnary.gsp.center.logistics.api.warehouse.response.WarehouseLiteRes;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class WarehouseRpc {

    @Resource
    private WarehouseFeignApi warehouseFeignApi;

    public WarehouseLiteRes getLiteByCustomCode(String supplierId, String warehouseCustomCode) {
        WarehouseGetByCustomCodeReq warehouseGetByCustomCodeReq = new WarehouseGetByCustomCodeReq();
        warehouseGetByCustomCodeReq.setSupplierId(supplierId);
        warehouseGetByCustomCodeReq.setCustomCode(warehouseCustomCode);
        return warehouseFeignApi.getLiteByCustomCode(warehouseGetByCustomCodeReq).mustSuccessOrThrowOriginal();
    }

    public List<ReceiptWarehouseRes> getReceiptWarehouseList(String type) {
        return warehouseFeignApi.getReceiptWarehouseList(type).mustSuccessOrThrowOriginal();
    }
}
