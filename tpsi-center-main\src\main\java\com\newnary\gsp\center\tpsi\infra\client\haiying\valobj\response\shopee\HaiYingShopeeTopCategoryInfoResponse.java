package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.shopee;

import lombok.Data;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingShopeeTopCategoryInfoResponse {

    /**
     * 类目ID
     */
    private String cid;

    /**
     * 一级类目商品总数占全部商品总数百分比
     */
    private String products_num_top_total_percent;

    /**
     * 一级类目总销售件数占全部总销售件数百分比
     */
    private String historical_sold_top_total_percent;

    /**
     * 类目最新统计时间
     */
    private String ins_time;

    /**
     * 类目商品总数
     */
    private String products_num;

    /**
     * 类目商品总数(本地)
     */
    private String local_products_num;

    /**
     * 类目商品总数(海外)
     */
    private String overseas_products_num;

    /**
     * 类目总销售件数
     */
    private String historical_sold;

    /**
     * 类目总销售件数(本地)
     */
    private String local_historical_sold;

    /**
     * 类目总销售件数(海外)
     */
    private String overseas_historical_sold;

    /**
     * 类目名称
     */
    private String cname;

    /**
     * 一级类目销售件数在全部类目中的占比
     */
    private String sold_top_total_percent;

    /**
     * 类目前30天销售件数
     */
    private String sold;

    /**
     * 类目前30天销售件数(本地)
     */
    private String local_sold;

    /**
     * 类目前30天销售件数(海外)
     */
    private String overseas_sold;

    /**
     * 一级类目销售金额在全部类目中的占比
     */
    private String payment_top_total_percent;

    /**
     * 类目前30天销售金额
     */
    private String payment;

    /**
     * 类目前30天销售金额(本地)
     */
    private String local_payment;

    /**
     * 类目前30天销售金额(海外)
     */
    private String overseas_payment;

}
