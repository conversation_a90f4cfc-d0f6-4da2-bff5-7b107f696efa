package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.amazon;

import lombok.Data;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingAmazonCategoryTreeResponse {

    /**
     * 类目id
     */
    private String cate_id;

    /**
     * 类目名称
     */
    private String cate_name;

    /**
     * 类目等级
     */
    private String level;

    /**
     * 1级类目id
     */
    private String p_l1_id;

    /**
     * 1级类目名
     */
    private String p_l1_name;

    /**
     * 2级类目id
     */
    private String p_l2_id;

    /**
     * 2级类目名
     */
    private String p_l2_name;

    /**
     * 3级类目id
     */
    private String p_l3_id;

    /**
     * 3级类目名
     */
    private String p_l3_name;

    /**
     * 4级类目id
     */
    private String p_l4_id;

    /**
     * 4级类目名
     */
    private String p_l4_name;

    /**
     * 5级类目id
     */
    private String p_l5_id;

    /**
     * 5级类目名
     */
    private String p_l5_name;

    /**
     * 6级类目id
     */
    private String p_l6_id;

    /**
     * 6级类目名
     */
    private String p_l6_name;

    /**
     * 7级类目id
     */
    private String p_l7_id;

    /**
     * 7级类目名
     */
    private String p_l7_name;

    /**
     * 8级类目id
     */
    private String p_l8_id;

    /**
     * 8级类目名
     */
    private String p_l8_name;

    /**
     * 9级类目id
     */
    private String p_l9_id;

    /**
     * 9级类目名
     */
    private String p_l9_name;

    /**
     * 10级类目id
     */
    private String p_l10_id;

    /**
     * 10级类目名
     */
    private String p_l10_name;

    /**
     * 是否是叶子类目
     * (1:是   0:否)
     */
    private String is_leaf;

    /**
     * 类目结构抓取时间
     */
    private String created_date;

    /**
     * 是否启用
     * 1:启用    0:不启用
     */
    private String enable;

}
