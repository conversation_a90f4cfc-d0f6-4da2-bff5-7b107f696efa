package com.newnary.gsp.center.tpsi.app.listener.mq;

import com.newnary.gsp.center.tpsi.infra.mq.consumer.ApiDockingMQConsumer;
import com.newnary.gsp.center.tpsi.service.ISyncProductBizSve;
import com.newnary.messagebody.gsp.tpsi.GSPApiDockingTopic;
import com.newnary.messagebody.gsp.tpsi.mo.ProductCreateMO;
import com.newnary.mq.starter.consumer.AbstractMQProcessor;
import com.newnary.mq.starter.consumer.MQMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class ProductCreate_ProductCreateProcessor extends AbstractMQProcessor<ProductCreateMO> {

    @Resource
    private ISyncProductBizSve syncProductBizSve;

    @Override
    public boolean doProcess(MQMessage<ProductCreateMO> message) {
        log.info("调用发送商品创建方法开始");
        ProductCreateMO mo = message.getContent();
        syncProductBizSve.doOpenSupplierProductCreateReq(mo.getOpenSupplierProductCreateReqStr(), mo.getValueKey(), mo.getValueJson(), mo.getValueType());
        log.info("调用发送商品创建方法结束");
        return true;
    }

    @Override
    public Class<?> consumerClz() {
        return ApiDockingMQConsumer.class;
    }

    @Override
    public String tag() {
        return GSPApiDockingTopic.Tag.PRODUCT_CREATE;
    }

}
