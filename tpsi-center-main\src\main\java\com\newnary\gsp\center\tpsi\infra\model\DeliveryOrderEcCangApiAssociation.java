package com.newnary.gsp.center.tpsi.infra.model;

import com.newnary.gsp.center.tpsi.infra.model.creator.DeliveryOrderEcCangApiAssociationCreator;
import com.newnary.gsp.center.tpsi.infra.model.id.AssociationId;
import com.newnary.gsp.center.tpsi.infra.model.vo.DeliveryOrderItemEcCangApiAssociation;
import com.newnary.spring.cloud.domain.Aggregate;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 出库单与易仓API关联
 */
@Getter
@Setter
public class DeliveryOrderEcCangApiAssociation extends Aggregate {

    private AssociationId associationId;

    private String deliveryOrderId;

    private String stockoutOrderId;

    private String transportOrderId;

    private String erpTpsId;

    private String wmsTpsId;

    /**
     * 供应链主订单号
     */
    private String tradeOrderId;

    /**
     * 关联子单号
     */
    private String orderRefNo;

    /**
     * erp订单-销售单号
     */
    private String erpOrderSaleOrderCode;

    /**
     * erp订单-状态
     */
    private Integer erpOrderStatus;

    /**
     * erp订单-跟踪号
     */
    private String erpOrderShippingMethodNo;

    /**
     * wms集运订单-订单号
     */
    private String wmsOrderCode;

    /**
     * wms集运订单-仓库id
     */
    private Integer wmsWarehouseId;

    /**
     * 出库面单号
     */
    private String wmsOrderLabelCode;

    /**
     * 装箱单号
     */
    private String wmsBoxCode;

    /**
     * wms集运订单-状态
     */
    private Integer wmsFreigntOrderStatus;

    //补充运费信息
    private Double wmsCblValue;
    private String wmsCblValueCurrency;
    private String wmsFtNameCn;
    //重量体积重信息
    public Double wmsWeight;
    public String wmsVolume;
    public Double wmsVolumeWeight;

    private List<DeliveryOrderItemEcCangApiAssociation> items;

    public static DeliveryOrderEcCangApiAssociation createWith(@NotNull DeliveryOrderEcCangApiAssociationCreator creator) {
        return new DeliveryOrderEcCangApiAssociation(creator);
    }

    public static DeliveryOrderEcCangApiAssociation loadWith(@NotNull Long id, @NotNull DeliveryOrderEcCangApiAssociationCreator creator) {
        return new DeliveryOrderEcCangApiAssociation(id, creator);
    }

    private DeliveryOrderEcCangApiAssociation(DeliveryOrderEcCangApiAssociationCreator creator) {
        setAssociationId(new AssociationId());
        buildBasic(creator);
    }

    private DeliveryOrderEcCangApiAssociation(Long id, DeliveryOrderEcCangApiAssociationCreator creator) {
        super(id);
        setAssociationId(new AssociationId(creator.getAssociationId()));
        buildBasic(creator);
    }

    private void buildBasic(DeliveryOrderEcCangApiAssociationCreator source) {
        setDeliveryOrderId(source.getDeliveryOrderId());
        setStockoutOrderId(source.getStockoutOrderId());
        setTransportOrderId(source.getTransportOrderId());
        setErpTpsId(source.getErpTpsId());
        setWmsTpsId(source.getWmsTpsId());
        setTradeOrderId(source.getTradeOrderId());
        setOrderRefNo(source.getOrderRefNo());
        setErpOrderSaleOrderCode(source.getErpOrderSaleOrderCode());
        setErpOrderStatus(source.getErpOrderStatus());
        setErpOrderShippingMethodNo(source.getErpOrderShippingMethodNo());
        setWmsOrderCode(source.getWmsOrderCode());
        setWmsOrderLabelCode(source.getWmsOrderLabelCode());
        setWmsBoxCode(source.getWmsBoxCode());
        setWmsWarehouseId(source.getWmsWarehouseId());
        setWmsFreigntOrderStatus(source.getWmsFreigntOrderStatus());
        setWmsCblValue(source.getWmsCblValue());
        setWmsCblValueCurrency(source.getWmsCblValueCurrency());
        setWmsFtNameCn(source.getWmsFtNameCn());
        setWmsVolume(source.getWmsVolume());
        setWmsVolumeWeight(source.getWmsVolumeWeight());
        setWmsWeight(source.getWmsWeight());
        setItems(source.getItems());
    }
}
