package com.newnary.gsp.center.tpsi.api.common;

public interface ApiPaths {

    /* 极兔能者-中国区 */
    final class JtCn {
        /*创建订单*/
        public static final String CREATE_ORDER_METHOD = "/its-api/cs/api/createOrder";

        /*获取面单*/
        public static final String QUERY_LABEL_METHOD = "/its-api/cs/api/label";

        /*获取所有物流渠道*/
        public static final String QUERY_LOGISTICS_METHOD = "/its-api/cs/api/getLogistics";

        /*获取轨迹*/
        public static final String QUERY_TRACK_METHOD = "/its-api/cs/api/queryTrack";

        /*取消预报*/
        public static final String CANCEL_ORDER = "/its-api/cs/api/cancelOrder";

        /*查询订单账单*/
        public static final String QUERY_BILL = "/its-api/cs/api/getBill";
    }


}