package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.wms;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
public class EcCangModifyOrderRequest {
    @NotNull(message = "订单号不能为空")
    private String order_code;
    @NotNull(message = "参考号不能为空")
    private String reference_no;
    private String aliexpress_order_no;
    private String platform;
    @NotNull(message = "配送方式不能为空")
    private String shipping_method;
    @NotNull(message = "配送仓库编码不能为空")
    private String warehouse_code;
    @NotNull(message = "国家编码不能为空")
    private String country_code;
    private String province;
    private String city;
    @NotNull(message = "地址1不能为空")
    private String address1;
    private String address2;
    private String address3;
    @NotNull(message = "邮编不能为空")
    private String zipcode;
    private String doorplate;
    @NotNull(message = "收件人姓名不能为空")
    private String name;
    private String phone;
    private String cell_phone;
    private String email;
    private String platform_shop;
    private String order_desc;
    private Integer verify;
    private Integer forceVerify;
    @NotNull(message = "订单明细不能为空")
    private List<Item> items;
    private String customs_company_name;
    private String customs_address;
    private String customs_contact_name;
    private String customs_email;
    private String customs_tax_code;
    private String customs_phone;
    private String customs_city;
    private String customs_state;
    private String customs_country_code;
    private String customs_postcode;
    private String customs_doorplate;
    private String consignee_tax_number;
    private String order_battery_type;
    private String vat_tax_code;
    private String distribution_information;
    private String consignee_tax_type;
    private String api_source;
    private String assign_date;
    private String assign_time;
    private Integer is_merge;
    private Integer merge_order_count;
    private Integer insurance_type;
    private String insurance_type_goods_value;
    private Integer is_ju_order;

    public static class Item {
        @NotNull(message = "商品sku不能为空")
        public String product_sku;
        public String product_name_en;
        public BigDecimal product_declared_value;
        @NotNull(message = "商品数量不能为空")
        public Integer quantity;
        public List<BatchInfo> batch_info;
    }

    public static class BatchInfo{
        public String inventory_code;
        public Integer sku_quantity;
    }
}
