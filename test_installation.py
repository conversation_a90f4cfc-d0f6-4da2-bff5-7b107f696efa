#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试安装脚本
检查所有必需的依赖包是否正确安装
"""

import sys
import importlib

def test_import(module_name, package_name=None):
    """测试导入模块"""
    try:
        importlib.import_module(module_name)
        print(f"✓ {package_name or module_name} 安装正确")
        return True
    except ImportError as e:
        print(f"✗ {package_name or module_name} 安装失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("发票OCR工具依赖检查")
    print("=" * 50)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 7):
        print("⚠️  警告: 建议使用Python 3.7或更高版本")
    else:
        print("✓ Python版本符合要求")
    
    print("\n检查依赖包:")
    print("-" * 30)
    
    # 要检查的包列表
    packages = [
        ("paddleocr", "PaddleOCR"),
        ("paddle", "PaddlePaddle"),
        ("PIL", "Pillow"),
        ("cv2", "OpenCV"),
        ("fitz", "PyMuPDF"),
        ("pandas", "Pandas"),
        ("openpyxl", "OpenPyXL"),
        ("numpy", "NumPy"),
        ("pathlib", "Pathlib")
    ]
    
    success_count = 0
    total_count = len(packages)
    
    for module_name, package_name in packages:
        if test_import(module_name, package_name):
            success_count += 1
    
    print("\n" + "=" * 50)
    print(f"检查结果: {success_count}/{total_count} 个包安装成功")
    
    if success_count == total_count:
        print("🎉 所有依赖包安装正确，可以开始使用发票OCR工具！")
        
        # 测试创建OCR处理器
        try:
            print("\n测试创建OCR处理器...")
            from invoice_ocr_processor import InvoiceOCRProcessor
            processor = InvoiceOCRProcessor(use_gpu=False)
            print("✓ OCR处理器创建成功")
        except Exception as e:
            print(f"✗ OCR处理器创建失败: {e}")
            print("这可能是首次运行，PaddleOCR正在下载模型文件...")
    else:
        print("❌ 部分依赖包安装失败，请运行以下命令安装:")
        print("pip install -r requirements.txt")
    
    print("\n使用方法:")
    print("1. 将发票文件放入一个文件夹中")
    print("2. 运行: python invoice_ocr_processor.py 文件夹路径")
    print("3. 或者运行示例: python example_usage.py")

if __name__ == "__main__":
    main()
