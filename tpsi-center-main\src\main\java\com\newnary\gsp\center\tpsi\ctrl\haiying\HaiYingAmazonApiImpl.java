package com.newnary.gsp.center.tpsi.ctrl.haiying;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.common.CommonResponse;
import com.newnary.api.base.common.PageCondition;
import com.newnary.api.base.common.PageList;
import com.newnary.api.base.common.PageMeta;
import com.newnary.gsp.center.tpsi.api.haiying.HaiYingAmazonApi;
import com.newnary.gsp.center.tpsi.api.haiying.request.amazon.HaiYingAmazonCategoryTreeCommand;
import com.newnary.gsp.center.tpsi.api.haiying.request.amazon.HaiYingAmazonProductDetailInfoCommand;
import com.newnary.gsp.center.tpsi.api.haiying.request.amazon.HaiYingAmazonProductHistoryInfoCommand;
import com.newnary.gsp.center.tpsi.api.haiying.request.amazon.HaiYingAmazonProductListCommand;
import com.newnary.gsp.center.tpsi.api.haiying.response.amazon.HaiYingAmazonCategoryTreeDTO;
import com.newnary.gsp.center.tpsi.api.haiying.response.amazon.HaiYingAmazonProductDetailInfoDTO;
import com.newnary.gsp.center.tpsi.api.haiying.response.amazon.HaiYingAmazonProductHistoryInfoDTO;
import com.newnary.gsp.center.tpsi.api.haiying.response.amazon.HaiYingAmazonProductListDTO;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.HaiYingDataApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.amazon.HaiYingAmazonCategoryTreeResponse;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.amazon.HaiYingAmazonProductDetailInfoResponse;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.amazon.HaiYingAmazonProductHistoryInfoResponse;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.amazon.HaiYingAmazonProductListResponse;
import com.newnary.gsp.center.tpsi.service.haiying.IHaiYingDataAmazonApiSve;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-13
 */
@RestController
@Slf4j
public class HaiYingAmazonApiImpl implements HaiYingAmazonApi {

    private static final Integer pageLimit = 100000;

    @Resource
    private IHaiYingDataAmazonApiSve haiYingAmazonDataApiSve;

    @Override
    public CommonResponse<PageList<HaiYingAmazonProductListDTO>> getAmazonProductList(HaiYingAmazonProductListCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingAmazonDataApiSve.getProductList(HaiYingAmazonCommand2RequestTranslator.transAmazonProductList(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingAmazonProductListResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingAmazonProductListResponse.class);
            if (apiBaseResult.getTotalSize() > pageLimit)
                apiBaseResult.setTotalSize(pageLimit); //TODO 因海鹰api限制返回前10w条
            return CommonResponse.success(HaiYingAmazonResponse2DTOTranslator.transAmazonProductListList(command.getStation(), responseList, getResultPageMeta(command.getPageCondition(), apiBaseResult)));
        } else {
            log.error("{}获取海鹰amazon商品列表失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            PageList<HaiYingAmazonProductListDTO> ret = new PageList<>();
            PageMeta pageMeta = new PageMeta();
            pageMeta.pageNum = command.getPageCondition().pageNum;
            pageMeta.pageSize = command.getPageCondition().pageSize;
            ret.setPageMeta(pageMeta);
            return CommonResponse.success(ret);
        }
    }

    @Override
    public CommonResponse<List<HaiYingAmazonProductDetailInfoDTO>> getAmazonProductDetailInfo(HaiYingAmazonProductDetailInfoCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingAmazonDataApiSve.getProductDetailInfo(HaiYingAmazonCommand2RequestTranslator.transAmazonProductDetailInfo(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingAmazonProductDetailInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingAmazonProductDetailInfoResponse.class);
            return CommonResponse.success(HaiYingAmazonResponse2DTOTranslator.transAmazonProductDetailInfoList(responseList));
        } else {
            log.error("{}获取海鹰amazon商品详情信息失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            return CommonResponse.success(new ArrayList<>());
        }
    }

    @Override
    public CommonResponse<List<HaiYingAmazonProductHistoryInfoDTO>> getAmazonProductHistoryInfo(HaiYingAmazonProductHistoryInfoCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingAmazonDataApiSve.getProductHistoryInfo(HaiYingAmazonCommand2RequestTranslator.transAmazonProductHistoryInfo(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingAmazonProductHistoryInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingAmazonProductHistoryInfoResponse.class);
            return CommonResponse.success(HaiYingAmazonResponse2DTOTranslator.transAmazonProductHistoryInfoList(responseList));
        } else {
            log.error("{}获取海鹰amazon商品历史信息失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            return CommonResponse.success(new ArrayList<>());
        }
    }

    @Override
    public CommonResponse<List<HaiYingAmazonCategoryTreeDTO>> getAmazonCategoryTree(HaiYingAmazonCategoryTreeCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingAmazonDataApiSve.getCategoryTree(HaiYingAmazonCommand2RequestTranslator.transAmazonCategoryTree(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingAmazonCategoryTreeResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingAmazonCategoryTreeResponse.class);
            return CommonResponse.success(HaiYingAmazonResponse2DTOTranslator.transAmazonCategoryTreeList(responseList));
        } else {
            log.error("{}获取海鹰amazon类目树失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            return CommonResponse.success(new ArrayList<>());
        }
    }

    private PageMeta getResultPageMeta(PageCondition pageCondition, HaiYingDataApiBaseResult<String> apiBaseResult) {
        if (apiBaseResult.getTotalSize() > 0) {
            PageMeta pageMeta = new PageMeta();
            if (null != pageCondition) {
                pageMeta.pageNum = pageCondition.pageNum;
            } else {
                pageMeta.pageNum = 1;
            }
            pageMeta.pageSize = apiBaseResult.getSize();
            if (apiBaseResult.getTotalSize() % apiBaseResult.getSize() == 0) {
                pageMeta.pages = apiBaseResult.getTotalSize() / apiBaseResult.getSize();
            } else {
                pageMeta.pages = (apiBaseResult.getTotalSize() / apiBaseResult.getSize()) + 1;
            }
            pageMeta.total = apiBaseResult.getTotalSize();
            return pageMeta;
        }
        return null;
    }

}
