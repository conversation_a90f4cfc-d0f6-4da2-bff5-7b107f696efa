package com.newnary.gsp.center.tpsi.service;

import com.newnary.gsp.center.tpsi.api.ninjavan.request.CancelOrderNinJavanCommand;
import com.newnary.gsp.center.tpsi.api.ninjavan.request.CreateOrderNinJavanCommand;
import com.newnary.gsp.center.tpsi.api.ninjavan.request.SheetOrderNinJavanCommand;
import com.newnary.gsp.center.tpsi.api.ninjavan.response.NinJavanOrderResp;
import com.newnary.gsp.center.tpsi.api.ninjavan.response.NinJavanPrintSheetResp;

public interface ILogisticsApiSve {

//    NinJavanOrderResp createOrder(CreateOrderNinJavanCommand command);
//
////    String loadAccessToken();
//
//    void doCancel(CancelOrderNinJavanCommand command);
//
//    NinJavanPrintSheetResp printSheet(SheetOrderNinJavanCommand command);

    void createOrder();

    void doCancel();

    void printSheet();

    void queryTrack();
}
