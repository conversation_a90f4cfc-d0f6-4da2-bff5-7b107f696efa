package com.newnary.gsp.center.tpsi.api.jt.vo.cn;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 2023-12-26
 * 发件信息对象
 * <AUTHOR>
 */
@Data
public class CreateOrderCnJTShipper {

    @Size(max = 64,message = "shipperName(最大64个字符)")
    @NotBlank(message = "shipperName(不能为空)")
    private String shipperName;

    @Size(max = 64,message = "shipperCompany(最大64个字符)")
    private String shipperCompany;

    @Size(max = 2,message = "shipperCountry(最大2个字符)")
    @NotBlank(message = "shipperCountry(不能为空)")
    private String shipperCountry;

    @Size(max = 32,message = "shipperProvince(最大32个字符)")
    @NotBlank(message = "shipperProvince(不能为空)")
    private String shipperProvince;

    @Size(max = 32,message = "shipperCity(最大32个字符)")
    @NotBlank(message = "shipperCity(不能为空)")
    private String shipperCity;

    @Size(max = 32,message = "shipperDistrict(最大32个字符)")
    private String shipperDistrict;

    @Size(max = 255,message = "shipperAddress(最大255个字符)")
    @NotBlank(message = "shipperAddress(不能为空)")
    private String shipperAddress;

    @Size(max = 32,message = "shipperDoorplate(最大32个字符)")
    private String shipperDoorplate;

    @Size(max = 32,message = "shipperStreet(最大32个字符)")
    private String shipperStreet;

    @Size(max = 32,message = "shipperPostcode(最大32个字符)")
    private String shipperPostcode;

    @Size(max = 20,message = "shipperPostcode(最大20个字符)")
    @NotBlank(message = "shipperPhone(不能为空)")
    private String shipperPhone;

    @Size(max = 20,message = "shipperEmail(最大20个字符)")
    private String shipperEmail;


}
