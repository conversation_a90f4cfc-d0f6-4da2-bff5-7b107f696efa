package com.newnary.gsp.center.tpsi.app.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.distributed.tools.concurrent.template.DConcurrentTemplate;
import com.newnary.gsp.center.logistics.api.delivery.dto.TrackInfoDTO;
import com.newnary.gsp.center.logistics.api.delivery.dto.TransportItemDTO;
import com.newnary.gsp.center.logistics.api.delivery.dto.TransportOrderPackageDTO;
import com.newnary.gsp.center.logistics.api.delivery.enums.*;
import com.newnary.gsp.center.logistics.api.delivery.request.*;
import com.newnary.gsp.center.logistics.api.delivery.response.*;
import com.newnary.gsp.center.product.api.product.response.ChannelSaleItemDetailInfo;
import com.newnary.gsp.center.tpsi.infra.client.eccang.params.EcCangERPParams;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.wms.EcCangFreightCreateOrderRequest;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.wms.EcCangFreightGetOrderInfoResponse;
import com.newnary.gsp.center.tpsi.infra.model.ApiRequestParams;
import com.newnary.gsp.center.tpsi.infra.model.DeliveryOrderEcCangApiAssociation;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.creator.DeliveryOrderEcCangApiAssociationCreator;
import com.newnary.gsp.center.tpsi.infra.model.vo.ApiRequestParamsType;
import com.newnary.gsp.center.tpsi.infra.model.vo.DeliveryOrderItemEcCangApiAssociation;
import com.newnary.gsp.center.tpsi.infra.repository.IApiRequestParamsRepository;
import com.newnary.gsp.center.tpsi.infra.repository.IDeliveryOrderEcCangApiAssociationRepository;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.infra.rpc.*;
import com.newnary.gsp.center.tpsi.infra.translator.DeliveryOrderEcCangApiAssociationTranslator;
import com.newnary.gsp.center.tpsi.service.eccang.IEccangERPApiSve;
import com.newnary.gsp.center.tpsi.service.eccang.IEccangWMSApiSve;
import com.newnary.gsp.center.trade.api.order.response.OrderDTO;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.job.core.handler.annotation.Job;
import com.newnary.spring.cloud.domain.Asserts;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TransportOrderEccangJobManager {

    private static final String AUTO_CHECK_TRANSPORT_CREATED_ORDER_BASE_ECCANG_PREFIX = "AUTO_CHECK_TRANSPORT_CREATED_ORDER_BASE_ECCANG";

    private static final String AUTO_CHECK_TRANSPORT_ORDER_STATE_BASE_ECCANG_PREFIX = "AUTO_CHECK_TRANSPORT_ORDER_STATE_BASE_ECCANG";

    private static final String AUTO_COMPLETE_ABROAD_TRANSPORT_ORDER_PREFIX = "AUTO_COMPLETE_ABROAD_TRANSPORT_ORDER";

    private static final String AUTO_CHECK_TRANSPORT_CANCELLING_ORDER_ECCANG_PREFIX = "AUTO_CHECK_TRANSPORT_CANCELLING_ORDER_ECCANG";

    private static final String LOCK_DELIVERY_ORDER_ECCANG_API_ASSOCIATION_TABLE_PREFIX = "LOCK_DELIVERY_ORDER_ECCANG_API_ASSOCIATION_TABLE";

    @Resource
    private DeliveryOrderRpc deliveryOrderRpc;
    @Resource
    private StockoutOrderRpc stockoutOrderRpc;
    @Resource
    private TransportOrderRpc transportOrderRpc;
    @Resource
    private TradeOrderRpc tradeOrderRpc;
    @Resource
    private SaleItemRpc saleItemRpc;
    @Resource
    private PurchaseRpc purchaseRpc;
    @Resource
    private IEccangERPApiSve eccangERPApiSve;
    @Resource
    private IEccangWMSApiSve eccangWMSApiSve;
    @Resource
    private IApiRequestParamsRepository apiOrderCreateParamsRepository;
    @Resource
    private IDeliveryOrderEcCangApiAssociationRepository deliveryOrderEcCangApiAssociationRepository;
    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Job("autoCheckTransportCreatedOrderBaseEcCang")
    public ReturnT<String> autoCheckTransportCreatedOrderBaseEcCang(String param) {
        log.info("基于易仓API定时任务检查已创建的运输单 - 开始 --- {}", new Date());
        //从参数中获取需要执行的第三方系统bizId
        JSONObject paramObject = JSONObject.parseObject(param);
        //易仓ERP系统id
        String erpTpsId = paramObject.getString("erpTpsId");
        Asserts.assertNotBlank(erpTpsId, "erp系统id不为空");
        //易仓WMS系统id
        String wmsTpsId = paramObject.getString("wmsTpsId");
        Asserts.assertNotBlank(wmsTpsId, "wms系统id不为空");

        String transportOrderId = paramObject.getString("transportOrderId");

        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem erpThirdPartySystem = loadSystem(erpTpsId);
        EcCangERPParams params = JSON.parseObject(erpThirdPartySystem.getParams(), EcCangERPParams.class);
        ThirdPartySystem wmsThirdPartySystem = loadSystem(wmsTpsId);

        Optional<ApiRequestParams> wmsCreateFreightOrderRequestParams = apiOrderCreateParamsRepository.loadBySystemBizIdAndType(wmsTpsId, ApiRequestParamsType.FREIGHT_ORDER.name());
        Asserts.assertTrue(wmsCreateFreightOrderRequestParams.isPresent(), "wms创建集运订单请求的配置参数信息不存在");

        DConcurrentTemplate.tryLockMode(
                AUTO_CHECK_TRANSPORT_CREATED_ORDER_BASE_ECCANG_PREFIX.concat(param),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    log.info("执行 核心逻辑--- {}", new Date());
                    TransportOrderWithDeliveryQueryCommand queryCommand = new TransportOrderWithDeliveryQueryCommand();
                    queryCommand.setTransportOrderState(TransportOrderState.CREATED.getName());
                    if (StringUtils.isNotEmpty(transportOrderId)) {
                        queryCommand.setTransportOrderId(transportOrderId);
                    }
                    queryCommand.setIsDomestic(true);  //TODO 暂时只有跨境段运输单需要创建易仓WMS集运订单 2023-8-28
                    queryCommand.setPushState(TransportPushState.NOT_PUSHED.name());
                    queryCommand.setDeliveryOrderChannelId(params.getChannelId());

                    List<TransportOrderLiteInfo> transportOrderInfoList = transportOrderRpc.getListWithDelivery(queryCommand);

                    if (CollectionUtils.isNotEmpty(transportOrderInfoList)) {
                        int resultSize = transportOrderInfoList.size();
                        log.info("获取的已创建未推送状态运输单数量为{}", resultSize);
                        transportOrderInfoList.forEach(transportOrderInfo -> {
                            TransportOrderDetailInfo transportOrderDetailInfo = transportOrderRpc.getDetail(transportOrderInfo.getTransportOrderId());
                            log.info("现在获取运输单{}关联的发货单{}", transportOrderDetailInfo.getTransportOrderId(), transportOrderDetailInfo.getItemReferenceIds());
                            //第三步 获取发货单详细信息
                            if (CollectionUtils.isEmpty(transportOrderDetailInfo.getItemReferenceIds())) {
                                log.error("运输单{}下找不到关联单号", transportOrderDetailInfo.getTransportOrderId());
                                return;
                            }
                            DeliveryOrderDetailInfo deliveryOrderDetailInfo = deliveryOrderRpc.getDetailInfo(transportOrderDetailInfo.getItemReferenceIds().get(0));
                            if (null != deliveryOrderDetailInfo) {
                                log.info("发货单{}详情已获取", deliveryOrderDetailInfo.getDeliveryOrderId());

                                List<DeliveryOrderDetailInfo> deliOrders = deliveryOrderRpc.getDetailsByOrderId(deliveryOrderDetailInfo.tradeOrderId);
                                for (DeliveryOrderDetailInfo deliOrder : deliOrders) {
                                    if (deliOrder.getOrderState() == DeliveryOrderState.CLOSED) {
                                        continue;
                                    }
                                    //TODO 跨境场景下，CN集运暂时需等齐所有商品再出库
                                    if (deliOrder.getOrderState() == DeliveryOrderState.WAIT_AUDIT
                                            || deliOrder.getOrderState() == DeliveryOrderState.AUDITED
                                            || deliOrder.getOrderState() == DeliveryOrderState.WAIT_DELIVERY
//                                            || deliOrder.getOrderState() == DeliveryOrderState.IN_DELIVERY
                                    ) {
                                        log.error("当前运输单{}对应订单{}仍有部分商品等待供应商受理", transportOrderDetailInfo.getTransportOrderId(), deliveryOrderDetailInfo.tradeOrderId);
                                        return;
                                    }
                                }
                                Optional<DeliveryOrderEcCangApiAssociation> tempAssociation = deliveryOrderEcCangApiAssociationRepository.loadByTransportOrderId(transportOrderDetailInfo.getTransportOrderId());
                                if (tempAssociation.isPresent()) {
                                    DeliveryOrderEcCangApiAssociation deliAssociation = tempAssociation.get();
                                    try {
                                        if (CollectionUtils.isEmpty(transportOrderDetailInfo.getOrderPackages()) || transportOrderDetailInfo.getOrderPackages().size() > 1) {
                                            //TODO 基于跨境运输单只有一个包裹逻辑
                                            log.error("找不到运输单下的包裹 或 运输单多于一个包裹");
                                            return;
                                        }
                                        TransportOrderPackageInfo transportPackageInfo = transportOrderRpc.loadTransportPackage(transportOrderDetailInfo.getOrderPackages().get(0).getTransportOrderPackageId());
                                        createFreightOrder(transportPackageInfo, deliAssociation, deliveryOrderDetailInfo, wmsThirdPartySystem, wmsCreateFreightOrderRequestParams.get());
                                        if (StringUtils.isNotEmpty(deliAssociation.getTransportOrderId()) && StringUtils.isNotEmpty(deliAssociation.getWmsOrderCode())) {
                                            TransportOrderThirdPushedCommand transportOrderThirdPushedCommand = new TransportOrderThirdPushedCommand();
                                            TransportOrderPackageDTO transportOrderPackage = transportOrderDetailInfo.getOrderPackages().get(0);
                                            transportOrderThirdPushedCommand.setTransportOrderPackageId(transportOrderPackage.getTransportOrderPackageId());
                                            transportOrderThirdPushedCommand.setThirdOrderId(deliAssociation.getWmsOrderCode());
                                            //成功创建ERP订单 调用运输单标记推送接口
                                            transportOrderRpc.thirdPushed(transportOrderThirdPushedCommand);
                                        }
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                        log.error("{}调用标记推送方法出错", deliAssociation.getTransportOrderId());
                                        //标记推送失败,重新创建集运订单时WMS会自动取消同个关联单号的集运订单
                                    }
                                    for (DeliveryOrderDetailInfo deliOrder : deliOrders) {
                                        Optional<DeliveryOrderEcCangApiAssociation> ecCangApiAssociation = deliveryOrderEcCangApiAssociationRepository.loadByDeliveryOrderId(deliOrder.getDeliveryOrderId());
                                        if (ecCangApiAssociation.isPresent()) {
                                            DeliveryOrderEcCangApiAssociation association = ecCangApiAssociation.get();
                                            try {
                                                association.setTransportOrderId(deliAssociation.getTransportOrderId());
                                                association.setWmsOrderCode(deliAssociation.getWmsOrderCode());
                                                association.setWmsWarehouseId(deliAssociation.getWmsWarehouseId());
                                                List<DeliveryOrderItemEcCangApiAssociation> items = new ArrayList<>();
                                                deliOrder.getItems().forEach(deliItem -> {
                                                    deliAssociation.getItems().forEach(associationItem -> {
                                                        if (deliItem.getSaleItemCode().equals(associationItem.getSaleItemCode())) {
                                                            items.add(associationItem);
                                                        }
                                                    });
                                                });
                                                association.setItems(items);
                                                deliveryOrderEcCangApiAssociationRepository.store(association);
                                            } catch (Exception e) {
                                                log.error("捕获保存异常{}", e.getMessage());
                                            }
                                            log.info("{}创建WMS集运订单{}记录已保存", association.getDeliveryOrderId(), association.getWmsOrderCode());
                                        } else {
                                            //提示出错
                                            log.error("{}找不到已有关联信息", transportOrderInfo.getTransportOrderId());

                                            createAssociation(deliOrder.getTradeOrderId(), erpTpsId, wmsTpsId);
                                        }
                                    }
                                } else {
                                    //提示出错
                                    log.error("{}找不到已有关联信息", transportOrderInfo.getTransportOrderId());

                                    createAssociation(deliveryOrderDetailInfo.getTradeOrderId(), erpTpsId, wmsTpsId);
                                }
                            } else {
                                log.warn("运输单{}获取不到关联发货单{}", transportOrderInfo.getTransportOrderId(), transportOrderInfo.getReferenceIds());
                            }
                        });
                    }
                });
        log.info("基于易仓API定时任务检查已创建未推送的运输单 - 结束 --- {}", new Date());
        return ReturnT.SUCCESS;
    }

    private void createAssociation(String tradeOrderId, String erpTpsId, String wmsTpsId) {
        OrderDTO tradeOrder = tradeOrderRpc.getTradeOrder(tradeOrderId);
        tradeOrder.getOrderSubItemList().forEach(subItem -> {
            DeliveryOrderDetailInfo deliveryOrderDetailInfo = deliveryOrderRpc.getDetailInfo(subItem.getDeliveryOrderId());
            if (StringUtils.isNotEmpty(deliveryOrderDetailInfo.getStockoutOrderId())) {
                StockoutOrderInfo stockoutOrderInfo = stockoutOrderRpc.getStockoutOrder(deliveryOrderDetailInfo.getStockoutOrderId());
                //TODO 暂时只对接头程运输单
                if (StringUtils.isNotEmpty(deliveryOrderDetailInfo.getCrossTransportOrderId())) {
                    TransportOrderDetailInfo transportOrderInfo = transportOrderRpc.getDetail(deliveryOrderDetailInfo.getCrossTransportOrderId());
                    if (ObjectUtils.isNotEmpty(stockoutOrderInfo) && ObjectUtils.isNotEmpty(transportOrderInfo)) {
                        DeliveryOrderEcCangApiAssociationCreator associationCreator = DeliveryOrderEcCangApiAssociationTranslator.buildCreator(tradeOrder, deliveryOrderDetailInfo, stockoutOrderInfo.getStockoutOrderId(), transportOrderInfo.getTransportOrderId(), stockoutOrderInfo.getThirdOrderId(), erpTpsId, wmsTpsId);
                        associationCreator.getItems().forEach(item -> {
                            ChannelSaleItemDetailInfo channelSaleItemDetailInfo = saleItemRpc.querySaleItem(deliveryOrderDetailInfo.getChannelId(), item.getSaleItemCode());
                            JSONArray specsArray = new JSONArray();
                            if (CollectionUtils.isNotEmpty(channelSaleItemDetailInfo.getSkuDetailInfo().getSkuInfo().getSpecs())) {
                                channelSaleItemDetailInfo.getSkuDetailInfo().getSkuInfo().getSpecs().forEach(supplierSkuSpecInfo -> {
                                    JSONObject specsJson = new JSONObject();
                                    specsJson.put(supplierSkuSpecInfo.getSpecName(), supplierSkuSpecInfo.getSpecValue());
                                    specsArray.add(specsJson);
                                });
                            }
                            item.setWmsProductStandard(specsArray.toJSONString());
                        });

                        //记录出库单与ERP和WMS的关联
                        try {
                            deliveryOrderEcCangApiAssociationRepository.store(DeliveryOrderEcCangApiAssociation.createWith(associationCreator));
                            log.info("对接系统保存发货单关联成功 {}", subItem.getDeliveryOrderId());
                        } catch (Exception e) {
                            e.printStackTrace();
                            log.error("{}保存发货单关联出错", subItem.getDeliveryOrderId());
                        }
                    } else {
                        log.error("发货单{}缺失关联出库单或运输单", subItem.getDeliveryOrderId());
                    }
                } else {
                    log.error("发货单{}未有跨境运输单关联", subItem.getDeliveryOrderId());
                }
            } else {
                log.error("发货单{}未有出库单关联", subItem.getDeliveryOrderId());
            }
        });
    }

    private void createFreightOrder(TransportOrderPackageInfo transportPackageInfo,
                                    DeliveryOrderEcCangApiAssociation association,
                                    DeliveryOrderDetailInfo deliveryOrderDetailInfo,
                                    ThirdPartySystem wmsThirdPartySystem,
                                    ApiRequestParams wmsCreateFreightOrderRequestParams) {
        //创建WMS集运订单
        log.info("{} - {} 创建WMS集运订单", transportPackageInfo.getTransportOrderId(), transportPackageInfo.getTransportOrderPackageId());
        EcCangFreightCreateOrderRequest freightCreateOrderRequest = new EcCangFreightCreateOrderRequest();
        EcCangApiBaseResult<String> freightCreateOrderApiBaseResult = null;

        //本土和跨境第二个集运订单，创建为国外的集运订单
//        if (deliveryOrderDetailInfo.getIsLocalShip() || (!deliveryOrderDetailInfo.getIsLocalShip() && transportOrderInfo.getIsDomestic() && StringUtils.isNotEmpty(association.getWmsOrderLabelCode()))) {
//            String trackingNum = association.getWmsOrderLabelCode();
//            freightCreateOrderApiBaseResult = eccangWMSApiSve.freightCreateAbroadOrder(deliveryOrderDetailInfo, wmsTpsId, wmsCreateFreightOrderRequestParams, freightCreateOrderRequest, trackingNum);
//            association.setWmsOrderLabelCode("");   //恢复默认空值
//            association.setStockoutOrderId("");     //出库单关联置空
//        } else {
        if (!deliveryOrderDetailInfo.getIsLocalShip() && transportPackageInfo.getIsDomestic()) {
            //跨境第一个集运订单
            freightCreateOrderApiBaseResult = eccangWMSApiSve.freightCreateOrder(transportPackageInfo, deliveryOrderDetailInfo, wmsThirdPartySystem, wmsCreateFreightOrderRequestParams, freightCreateOrderRequest);
        }
        if (null != freightCreateOrderApiBaseResult && freightCreateOrderApiBaseResult.getCode().equals("200")
                && freightCreateOrderApiBaseResult.getMessage().equalsIgnoreCase("Success")) {
            JSONObject successFreightOrderJsonObject = JSONObject.parseObject(freightCreateOrderApiBaseResult.getData());

            String thirdOrderId = successFreightOrderJsonObject.getString("order_code");

            log.info("{} - {} 已创建WMS集运订单 {}", transportPackageInfo.getTransportOrderId(), transportPackageInfo.getTransportOrderPackageId(), thirdOrderId);
            try {
                association.setTransportOrderId(transportPackageInfo.getTransportOrderId());
                association.setWmsOrderCode(thirdOrderId);
                association.setWmsWarehouseId(freightCreateOrderRequest.warehouse_id);
                JSONArray products = successFreightOrderJsonObject.getJSONArray("product");
                Map<String, Integer> productMap = products.stream().filter(Objects::nonNull).collect(Collectors.toMap(
                        object -> {
                            JSONObject item = (JSONObject) object;
                            return item.getString("child_code");
                        },
                        object -> {
                            JSONObject item = (JSONObject) object;
                            return item.getInteger("pid");
                        }
                ));
                Map<String, EcCangFreightCreateOrderRequest.ProductData> productDataMap = freightCreateOrderRequest.product_data.stream().collect(Collectors.toMap(
                        object -> {
                            return object.child_code;
                        },
                        object -> {
                            return object;
                        }
                ));
                association.getItems().forEach(item -> {
                    item.setWmsProductPid(productMap.get(item.getCustomCode()));
                    EcCangFreightCreateOrderRequest.ProductData productData = productDataMap.get(item.getCustomCode());
                    item.setWmsChildCode(productData.child_code);
                    item.setWmsProductCategory(productData.product_category);
                    item.setWmsProductCount(productData.product_count);
                    item.setWmsProductDeclared(productData.product_declared);
                    item.setWmsProductImgUrl(productData.product_img_url);
                    item.setWmsProductNameCn(productData.product_name_cn);
                    item.setWmsProductNameEn(productData.product_name_en);
                    item.setWmsProductStandard(productData.product_standard);
                    if (StringUtils.isNotEmpty(productData.package_code)) {
                        item.setWmsProductPackageCode(productData.package_code);
                    }
                });
            } catch (Exception e) {
                log.error("捕获异常{}", e.getMessage());
            }
        } else {
            log.error("{} - {} 创建易仓集运订单失败{}", transportPackageInfo.getTransportOrderId(), transportPackageInfo.getTransportOrderPackageId(), ObjectUtils.isNotEmpty(freightCreateOrderApiBaseResult) ? freightCreateOrderApiBaseResult.getError() : "创建WMS返回为空");
        }

    }

    @Job("autoCheckTransportOrderStateBaseShengWei")
    public ReturnT<String> autoCheckTransportOrderStateBaseShengWei(String param) {
        log.info("基于易仓API定时任务检查运输单状态 - 开始 --- {}", new Date());
        //从参数中获取需要执行的第三方系统bizId
        JSONObject paramObject = JSONObject.parseObject(param);
        //易仓ERP系统id
        String erpTpsId = paramObject.getString("erpTpsId");
        Asserts.assertNotBlank(erpTpsId, "erp系统id不为空");
        //易仓WMS系统id
        String wmsTpsId = paramObject.getString("wmsTpsId");
        Asserts.assertNotBlank(wmsTpsId, "wms系统id不为空");

        String transportOrderId = paramObject.getString("transportOrderId");

        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem erpThirdPartySystem = loadSystem(erpTpsId);
        EcCangERPParams params = JSON.parseObject(erpThirdPartySystem.getParams(), EcCangERPParams.class);
        ThirdPartySystem wmsThirdPartySystem = loadSystem(wmsTpsId);

        //查询租户下的易仓店铺，国内仓收货地址
        log.info("开始获取api订单、商品创建请求参数");

        DConcurrentTemplate.tryLockMode(
                AUTO_CHECK_TRANSPORT_ORDER_STATE_BASE_ECCANG_PREFIX.concat(param),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    log.info("执行 核心逻辑--- {}", new Date());
                    TransportOrderWithDeliveryQueryCommand queryCommand = new TransportOrderWithDeliveryQueryCommand();
                    queryCommand.setTransportOrderState("IN_TRANSIT");
                    if (StringUtils.isNotEmpty(transportOrderId)) {
                        queryCommand.setTransportOrderId(transportOrderId);
                    }
//                    queryCommand.setThirdPushed(true);
                    queryCommand.setPushState(TransportPushState.PUSHED.name());
                    //queryCommand.setDeliveryOrderSupplierId(params.getSupplierId());
                    queryCommand.setDeliveryOrderChannelId(params.getChannelId());
//                    queryCommand.setDeliveryOrderType(OrderType.APO.name());
                    //List<TransportOrderInfo> transportOrderInfoList = transportOrderRpc.getListWithDelivery(queryCommand);
                    List<TransportOrderLiteInfo> transportOrderInfoList = new ArrayList<>();
                    String supplierIdArrayStr = params.getSupplierIds();
                    if (StringUtils.isBlank(supplierIdArrayStr)) {
                        queryCommand.setDeliveryOrderSupplierId(params.getSupplierId());
                        transportOrderInfoList.addAll(transportOrderRpc.getListWithDelivery(queryCommand));
                    } else {
                        String[] supplierIds = supplierIdArrayStr.split(",");
                        for (String supplierId : supplierIds) {
                            queryCommand.setDeliveryOrderSupplierId(supplierId);
                            transportOrderInfoList.addAll(transportOrderRpc.getListWithDelivery(queryCommand));
                        }
                    }
                    if (CollectionUtils.isNotEmpty(transportOrderInfoList)) {
                        transportOrderInfoList.forEach(transportOrderInfo -> {
                            try {
                                TransportOrderDetailInfo transportOrderDetailInfo = transportOrderRpc.getDetail(transportOrderInfo.getTransportOrderId());
                                transportOrderDetailInfo.getItemReferenceIds().forEach(deliveryOrderId -> {
                                    try {
                                        //调用易仓查询订单接口
                                        coreHandleTransportReceivedOrder(deliveryOrderId, wmsThirdPartySystem);
                                    } catch (Exception e) {
                                        log.error("{} - {}处理已出库单出错{}", transportOrderDetailInfo.getTransportOrderId(), deliveryOrderId, e.getMessage());
                                    }
                                });
                            } catch (ServiceException e) {
                                log.error("基于易仓API定时任务检查运输单状态:异常单号 ： ".concat(transportOrderInfo.getTransportOrderId()), e);
                            }
                            //获取认领单
                            //getFreightClaimOrders(wmsTpsId);
                        });
                    }
                }
        );
        log.info("基于易仓API定时任务检查运输单状态 - 结束 --- {}", new Date());
        return ReturnT.SUCCESS;
    }

    @Job("autoCheckTransportOrderStateBaseEcCang")
    public ReturnT<String> autoCheckTransportOrderStateBaseEcCang(String param) {
        log.info("基于易仓API定时任务检查运输单状态 - 开始 --- {}", new Date());
        //从参数中获取需要执行的第三方系统bizId
        JSONObject paramObject = JSONObject.parseObject(param);
        //易仓ERP系统id
        String erpTpsId = paramObject.getString("erpTpsId");
        Asserts.assertNotBlank(erpTpsId, "erp系统id不为空");
        //易仓WMS系统id
        String wmsTpsId = paramObject.getString("wmsTpsId");
        Asserts.assertNotBlank(wmsTpsId, "wms系统id不为空");

        String transportOrderId = paramObject.getString("transportOrderId");

        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem erpThirdPartySystem = loadSystem(erpTpsId);
        EcCangERPParams params = JSON.parseObject(erpThirdPartySystem.getParams(), EcCangERPParams.class);
        ThirdPartySystem wmsThirdPartySystem = loadSystem(wmsTpsId);

        //查询租户下的易仓店铺，国内仓收货地址
        log.info("开始获取api订单、商品创建请求参数");

        DConcurrentTemplate.tryLockMode(
                AUTO_CHECK_TRANSPORT_ORDER_STATE_BASE_ECCANG_PREFIX.concat(param),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    log.info("执行 核心逻辑--- {}", new Date());
                    TransportOrderWithDeliveryQueryCommand queryCommand = new TransportOrderWithDeliveryQueryCommand();
                    queryCommand.setTransportOrderState("IN_TRANSIT");
                    if (StringUtils.isNotEmpty(transportOrderId)) {
                        queryCommand.setTransportOrderId(transportOrderId);
                    }
//                    queryCommand.setThirdPushed(true);
                    queryCommand.setPushState(TransportPushState.PUSHED.name());
                    //queryCommand.setDeliveryOrderSupplierId(params.getSupplierId());
                    queryCommand.setDeliveryOrderChannelId(params.getChannelId());
                    //queryCommand.setDeliveryOrderType(OrderType.APO.name());
                    List<TransportOrderLiteInfo> transportOrderInfoList = transportOrderRpc.getListWithDelivery(queryCommand);
                    if (CollectionUtils.isNotEmpty(transportOrderInfoList)) {
                        transportOrderInfoList.forEach(transportOrderInfo -> {
                            try {
                                TransportOrderDetailInfo transportOrderDetailInfo = transportOrderRpc.getDetail(transportOrderInfo.getTransportOrderId());
                                transportOrderDetailInfo.getItemReferenceIds().forEach(deliveryOrderId -> {
                                    try {
                                        //调用易仓查询订单接口
                                        coreHandleTransportReceivedOrder(deliveryOrderId, erpThirdPartySystem, wmsThirdPartySystem);
                                    } catch (Exception e) {
                                        log.error("{} - {}处理已出库单出错{}", transportOrderDetailInfo.getTransportOrderId(), deliveryOrderId, e.getMessage());
                                    }
                                });
                            } catch (ServiceException e) {
                                log.error("基于易仓API定时任务检查运输单状态:异常单号 ： ".concat(transportOrderInfo.getTransportOrderId()), e);
                            }
                            //获取认领单
                            //getFreightClaimOrders(wmsTpsId);
                        });
                    }
                }
        );
        log.info("基于易仓API定时任务检查运输单状态 - 结束 --- {}", new Date());
        return ReturnT.SUCCESS;
    }

    private void coreHandleTransportReceivedOrder(String deliveryOrderId, ThirdPartySystem wmsThirdPartySystem) {
        log.info("现在获取{}的运输单", deliveryOrderId);
        DConcurrentTemplate.tryLockMode(
                LOCK_DELIVERY_ORDER_ECCANG_API_ASSOCIATION_TABLE_PREFIX.concat(deliveryOrderId),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    Optional<DeliveryOrderEcCangApiAssociation> ecCangApiAssociation = deliveryOrderEcCangApiAssociationRepository.loadByDeliveryOrderId(deliveryOrderId);
                    if (ecCangApiAssociation.isPresent()) {
                        DeliveryOrderEcCangApiAssociation association = ecCangApiAssociation.get();

                        //更新集运订单信息
                        checkWmsFreightOrder(association, null, wmsThirdPartySystem);

                        deliveryOrderEcCangApiAssociationRepository.store(association);
                    } else {
                        //提示出错
                        log.error("{}找不到已有关联信息", deliveryOrderId);
                    }
                });
    }

    private void coreHandleTransportReceivedOrder(String deliveryOrderId,
                                                  ThirdPartySystem erpThirdPartySystem,
                                                  ThirdPartySystem wmsThirdPartySystem) {
        log.info("现在获取{}的运输单", deliveryOrderId);
        DConcurrentTemplate.tryLockMode(
                LOCK_DELIVERY_ORDER_ECCANG_API_ASSOCIATION_TABLE_PREFIX.concat(deliveryOrderId),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    Optional<DeliveryOrderEcCangApiAssociation> ecCangApiAssociation = deliveryOrderEcCangApiAssociationRepository.loadByDeliveryOrderId(deliveryOrderId);
                    if (ecCangApiAssociation.isPresent()) {
                        DeliveryOrderEcCangApiAssociation association = ecCangApiAssociation.get();

                        //更新集运订单信息
                        checkWmsFreightOrder(association, erpThirdPartySystem, wmsThirdPartySystem);

                        deliveryOrderEcCangApiAssociationRepository.store(association);
                    } else {
                        //提示出错
                        log.error("{}找不到已有关联信息", deliveryOrderId);
                    }
                });
    }

    private void checkWmsFreightOrder(DeliveryOrderEcCangApiAssociation association,
                                      ThirdPartySystem erpThirdPartySystem,
                                      ThirdPartySystem wmsThirdPartySystem) {
//        if (StringUtils.isEmpty(association.getWmsFtNameCn())) {
        if (StringUtils.isEmpty(association.getTransportOrderId())) {
            return;
        }
        TransportOrderDetailInfo transportOrderDetailInfo = transportOrderRpc.getDetail(association.getTransportOrderId());
        if (CollectionUtils.isEmpty(transportOrderDetailInfo.getOrderPackages()) || transportOrderDetailInfo.getOrderPackages().size() > 1) {
            //TODO 基于跨境运输单只有一个包裹逻辑
            log.error("找不到运输单下的包裹 或 运输单多于一个包裹");
            return;
        }
        TransportOrderPackageDTO transportOrderPackage = transportOrderDetailInfo.getOrderPackages().get(0);
        if (StringUtils.isEmpty(transportOrderPackage.getThirdOrderId())) {
            return;
        }
        association.setWmsOrderCode(transportOrderPackage.getThirdOrderId());
        EcCangApiBaseResult<String> freightGetOrderInfoApiBaseResult = eccangWMSApiSve.freightGetOrderInfo(transportOrderPackage.getThirdOrderId(), wmsThirdPartySystem);
        if (freightGetOrderInfoApiBaseResult.getCode().equals("200") && freightGetOrderInfoApiBaseResult.getMessage().equalsIgnoreCase("Success")) {
            List<EcCangFreightGetOrderInfoResponse> responseOrders = JSONObject.parseArray(freightGetOrderInfoApiBaseResult.getData(), EcCangFreightGetOrderInfoResponse.class);
            if (responseOrders != null && responseOrders.size() == 1) {
                EcCangFreightGetOrderInfoResponse orderInfoResponse = responseOrders.get(0);
                log.info("获取到{}的WMS集运订单{}的状态为{}", association.getDeliveryOrderId(), association.getOrderRefNo(), orderInfoResponse.order_status);
                association.setWmsWarehouseId(orderInfoResponse.warehouse_id);
                if (orderInfoResponse.weight != null) {
                    association.setWmsWeight(orderInfoResponse.weight);
                }
                if (StringUtils.isNotEmpty(orderInfoResponse.volume)) {
                    association.setWmsVolume(orderInfoResponse.volume);
                }
                if (orderInfoResponse.volume_weight != null) {
                    association.setWmsVolumeWeight(orderInfoResponse.volume_weight);
                }
                if (StringUtils.isNotEmpty(orderInfoResponse.order_label_code)) {
                    association.setWmsOrderLabelCode(orderInfoResponse.order_label_code);
                }
                if (StringUtils.isNotEmpty(orderInfoResponse.box_code)) {
                    association.setWmsBoxCode(orderInfoResponse.box_code);
                }
                if (CollectionUtils.isNotEmpty(orderInfoResponse.product_data)) {
                    Map<String, TransportItemDTO> transportItemMap = transportOrderPackage.getItems().stream().collect(Collectors.toMap(TransportItemDTO::getCustomCode, Function.identity(), (k1, k2) -> k1));

                    orderInfoResponse.product_data.forEach(productData -> {
                        association.getItems().forEach(item -> {
                            if (productData.child_code.equals(item.getCustomCode())) {
                                if (StringUtils.isNotEmpty(productData.product_id)) {
                                    item.setWmsProductPid(Integer.valueOf(productData.product_id));
                                }
                                item.setWmsChildCode(productData.child_code);
                                item.setWmsProductPackageCode(productData.package_code);
                                item.setWmsProductStandard(productData.product_standard);
                                item.setWmsProductCount(productData.product_count);
                                item.setWmsProductNameCn(productData.product_name_cn);
                                item.setWmsProductNameEn(productData.product_name_en);
                                item.setWmsProductDeclared(productData.product_declared);
                                TransportItemDTO transportItem = transportItemMap.get(item.getCustomCode());
                                if (ObjectUtils.isNotEmpty(transportItem)) {
                                    item.setWmsProductCategory(StringUtils.isEmpty(transportItem.getCategoryName()) ? "未分类" : transportItem.getCategoryName());
                                    if (CollectionUtils.isNotEmpty(transportItem.getSupplierSkuPicUrl()))
                                        item.setWmsProductImgUrl(transportItem.getSupplierSkuPicUrl().get(0));
                                    else if (CollectionUtils.isNotEmpty(productData.product_img))
                                        item.setWmsProductImgUrl(productData.product_img.get(0));
                                }
                            }
                        });
                    });
                }

                if (orderInfoResponse.cost_log != null) {
                    EcCangFreightGetOrderInfoResponse.CostLog shippingCostLog = orderInfoResponse.cost_log.stream().filter(costLog -> costLog.ft_name_cn.equals("运输费")).findAny().orElse(null);
                    if (shippingCostLog != null) {
                        association.setWmsCblValue(Double.valueOf(shippingCostLog.cbl_value));
                        association.setWmsCblValueCurrency(shippingCostLog.currency_code);
                        association.setWmsFtNameCn(shippingCostLog.ft_name_cn);
                    }
                }

                if (orderInfoResponse.order_status >= 3) {
                    if (orderInfoResponse.order_status == 3) {
                        transportOrderRpc.localReceive(transportOrderPackage.getTransportOrderPackageId());

                        if (StringUtils.isNotEmpty(association.getStockoutOrderId())) {
                            //不从易仓ERP获取订单发货状态变化直接将出库单变为已发货
                            stockoutOrderRpc.deliver(association.getStockoutOrderId());
                        }
                    } else if (orderInfoResponse.order_status == 4 || orderInfoResponse.order_status == 5) {
                        if (null == association.getWmsFreigntOrderStatus() || association.getWmsFreigntOrderStatus() < 3) {   //定时任务有可能错过了收件完成状态获取
                            log.info("{}--系统记录的WMS集运订单状态为{}，实际获取到的WMS集运订单状态为{}，补做收件完成动作", association.getDeliveryOrderId(), association.getWmsFreigntOrderStatus(), orderInfoResponse.order_status);
                            transportOrderRpc.localReceive(transportOrderPackage.getTransportOrderPackageId());

                            if (StringUtils.isNotEmpty(association.getStockoutOrderId())) {
                                //不从易仓ERP获取订单发货状态变化直接将出库单变为已发货
                                stockoutOrderRpc.deliver(association.getStockoutOrderId());
                            }
                        }

                        Asserts.assertNotNull(association.wmsWeight, "重量不能为空");
                        Asserts.assertNotBlank(association.wmsVolume, "体积不能为空");
                        String volumeStr = association.wmsVolume;
                        if (StringUtils.isNotEmpty(volumeStr)) {
                            String[] volumeStrs = volumeStr.split("\\*");
                            if (null != volumeStrs && volumeStrs.length == 3) {
                                BigDecimal length = new BigDecimal(volumeStrs[0]);
                                BigDecimal width = new BigDecimal(volumeStrs[1]);
                                BigDecimal height = new BigDecimal(volumeStrs[2]);
                                length = length.divide(new BigDecimal(100));
                                width = width.divide(new BigDecimal(100));
                                height = height.divide(new BigDecimal(100));
                                if (length.compareTo(BigDecimal.ZERO) != 0
                                        || width.compareTo(BigDecimal.ZERO) != 0
                                        || height.compareTo(BigDecimal.ZERO) != 0 || association.wmsWeight != 0) {   //有值才操作处理
                                    TransportOrderUpdatePropertyCommand command = new TransportOrderUpdatePropertyCommand();
                                    command.setTransportOrderPackageId(transportOrderPackage.getTransportOrderPackageId());
                                    command.setLength(length);
                                    command.setWidth(width);
                                    command.setHeight(height);
                                    command.setWeight(new BigDecimal(association.getWmsWeight()));
                                    command.setVolumeWeight(new BigDecimal(association.getWmsVolumeWeight()));
                                    transportOrderRpc.updateProperty(command);
                                }
                            } else {
                                log.error("{}体积{}不正确", association.getDeliveryOrderId(), volumeStr);
                            }
                        } else {
                            log.error("{}体积不存在", association.getDeliveryOrderId());
                        }
                    } else if (orderInfoResponse.order_status == 7) {
                        Asserts.assertNotNull(association.wmsWeight, "重量不能为空");
                        Asserts.assertNotBlank(association.wmsVolume, "体积不能为空");
                        String volumeStr = association.wmsVolume;
                        if (StringUtils.isNotEmpty(volumeStr)) {
                            String[] volumeStrs = volumeStr.split("\\*");
                            if (null != volumeStrs && volumeStrs.length == 3) {
                                BigDecimal length = new BigDecimal(volumeStrs[0]);
                                BigDecimal width = new BigDecimal(volumeStrs[1]);
                                BigDecimal height = new BigDecimal(volumeStrs[2]);
                                length = length.divide(new BigDecimal(100));
                                width = width.divide(new BigDecimal(100));
                                height = height.divide(new BigDecimal(100));
                                if (length.compareTo(BigDecimal.ZERO) != 0
                                        || width.compareTo(BigDecimal.ZERO) != 0
                                        || height.compareTo(BigDecimal.ZERO) != 0 || association.wmsWeight != 0) {   //有值才操作处理
                                    TransportOrderUpdatePropertyCommand command = new TransportOrderUpdatePropertyCommand();
                                    command.setTransportOrderPackageId(transportOrderPackage.getTransportOrderPackageId());
                                    command.setLength(length);
                                    command.setWidth(width);
                                    command.setHeight(height);
                                    command.setWeight(new BigDecimal(association.getWmsWeight()));
                                    command.setVolumeWeight(new BigDecimal(association.getWmsVolumeWeight()));
                                    transportOrderRpc.updateProperty(command);
                                }
                            } else {
                                log.error("{}体积{}不正确", association.getDeliveryOrderId(), volumeStr);
                            }
                        } else {
                            log.error("{}体积不存在", association.getDeliveryOrderId());
                        }
                        if (StringUtils.isNotEmpty(association.getWmsBoxCode())) {
                            transportOrderRpc.updateReferenceId(transportOrderPackage.getTransportOrderPackageId(), association.getWmsBoxCode());
                        }
                        transportOrderRpc.localPacked(transportOrderPackage.getTransportOrderPackageId());
                    } else if (orderInfoResponse.order_status == 6) {
                        TransportOrderConfirmCancelCommand command = new TransportOrderConfirmCancelCommand();
                        command.setTransportOrderId(association.getTransportOrderId());
                        command.setCancelType(CancelType.WAREHOUSE_CANCEL.name());
                        transportOrderRpc.confirmCancel(command);
                    }

                    if (StringUtils.isNotEmpty(association.getWmsOrderLabelCode())) {
                        TransportOrderUpdateTrackCommand command = new TransportOrderUpdateTrackCommand();
                        command.setTransportOrderPackageId(transportOrderPackage.getTransportOrderPackageId());
                        command.setTrackInfo(new TrackInfoDTO("PGL", association.getWmsOrderLabelCode()));   //承运商先固定为派菲顾
                        transportOrderRpc.updateTrack(command);
                    }

                    //修改采购单状态
                    updatePurchaseOrderState(association, erpThirdPartySystem);

                }
                association.setWmsFreigntOrderStatus(orderInfoResponse.order_status);
            }
        } else {
            log.error("{}获取集运订单信息失败{}，数据为空", association.getWmsOrderCode(), freightGetOrderInfoApiBaseResult.getMessage());
        }
//        } else {
//            log.error("{}运费已有,状态为{}，不更新", association.getWmsOrderCode(), association.getWmsFreigntOrderStatus());
//        }

    }

    private void updatePurchaseOrderState(DeliveryOrderEcCangApiAssociation association, ThirdPartySystem erpThirdPartySystem) {
        if (Objects.isNull(erpThirdPartySystem) || StringUtils.isBlank(erpThirdPartySystem.getBizId())) {
            //升威的修改采购单
            association.getItems().stream().filter(item -> null != item.getErpPurchaseOrderStatus() && item.getErpPurchaseOrderStatus() != 50).forEach(item -> {

                String isSuceess = null;
                try {
                    isSuceess = purchaseRpc.updateOrderState(item.getErpPurchaseOrderCode());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (StringUtils.equals("SUCCESS", isSuceess)) {
                    item.setErpPurchaseOrderStatus(50);
                } else {
                    log.error("{}采购单入库失败{}", item.getErpPurchaseOrderCode());
                }
            });
        } else {
            //易仓的修改采购单
            //采购单不是"已完成"，则采购单入库,
            association.getItems().forEach(item -> {
                if (null != item.getErpPurchaseOrderStatus() && item.getErpPurchaseOrderStatus() != 8) {
                    //采购单不是"已完成"，则采购单入库,
                    EcCangApiBaseResult<String> purchaseOrderInboundApiBaseResult = eccangERPApiSve.purchaseOrderInbound(association.getOrderRefNo(), item, erpThirdPartySystem);
                    if (purchaseOrderInboundApiBaseResult.getCode().equals("200")) {
                        item.setErpPurchaseOrderStatus(8);
                    } else {
                        log.error("{}采购单入库失败{}", item.getErpPurchaseOrderCode(), purchaseOrderInboundApiBaseResult.getMessage());
                    }
                }
            });
        }
    }

    @Job("autoCompleteTransportOrder")
    public ReturnT<String> autoCompleteTransportOrder(String param) {
        log.info("基于易仓API定时任务检查派送中的国外运输单 - 开始 --- {}", new Date());
        //从参数中获取需要执行的第三方系统bizId
        JSONObject paramObject = JSONObject.parseObject(param);
        //易仓ERP系统id
        String erpTpsId = paramObject.getString("erpTpsId");
        Asserts.assertNotBlank(erpTpsId, "erp系统id不为空");
        //易仓WMS系统id
        String wmsTpsId = paramObject.getString("wmsTpsId");
        Asserts.assertNotBlank(wmsTpsId, "wms系统id不为空");

        String transportOrderId = paramObject.getString("transportOrderId");

        Integer seaDay = paramObject.getInteger("SEA");
        Integer airDay = paramObject.getInteger("AIR");
        Integer localDay = paramObject.getInteger("LOCAL");

        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(erpTpsId);
        EcCangERPParams params = JSON.parseObject(thirdPartySystem.getParams(), EcCangERPParams.class);

        DConcurrentTemplate.tryLockMode(
                AUTO_COMPLETE_ABROAD_TRANSPORT_ORDER_PREFIX.concat(param),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    log.info("执行 核心逻辑--- {}", new Date());
                    TransportOrderWithDeliveryQueryCommand queryCommand = new TransportOrderWithDeliveryQueryCommand();
                    queryCommand.setTransportOrderState(TransportOrderState.WAIT_DELIVERY.getName());  //TODO 现有需求暂时将已装箱时间作为基础操作判断
                    if (StringUtils.isNotEmpty(transportOrderId)) {
                        queryCommand.setTransportOrderId(transportOrderId);
                    }
                    queryCommand.setIsCod(false);
//                    queryCommand.setThirdPushed(true);
                    queryCommand.setPushState(TransportPushState.PUSHED.name());
//                    queryCommand.setIsDomestic(false);
//                    queryCommand.setDeliveryOrderSupplierId(params.getSupplierId());
                    queryCommand.setDeliveryOrderChannelId(params.getChannelId());
                    //queryCommand.setDeliveryOrderType(OrderType.APO.name());
                    List<TransportOrderLiteInfo> transportOrderInfoList = transportOrderRpc.getListWithDelivery(queryCommand);
                    if (CollectionUtils.isNotEmpty(transportOrderInfoList)) {
                        int resultSize = transportOrderInfoList.size();
                        log.info("获取的派送中状态运输单数量为{}", resultSize);
                        transportOrderInfoList.forEach(transportOrderInfo -> {
                            OrderDTO orderDTO = tradeOrderRpc.getTradeOrder(transportOrderInfo.getTradeOrderId());
                            Integer transitDays = null;
                            if (ObjectUtils.isNotEmpty(seaDay) && orderDTO.getTransportType().equalsIgnoreCase("SEA")) {
                                transitDays = seaDay;
                            } else if (ObjectUtils.isNotEmpty(airDay) && orderDTO.getTransportType().equalsIgnoreCase("AIR")) {
                                transitDays = airDay;
                            } else if (ObjectUtils.isNotEmpty(localDay) && orderDTO.getTransportType().equalsIgnoreCase("LOCAL")) {
                                transitDays = localDay;
                            }
                            if (ObjectUtils.isNotEmpty(transitDays)
                                    && Instant.now().isAfter(Instant.ofEpochMilli(transportOrderInfo.getGmtTransit() + transitDays.longValue() * 24L * 60L * 60L * 1000L))) {
                                log.info("运输类型{}的运输单{}的装箱出库时间{}小于现在时间{},执行自动完成操作"
                                        , orderDTO.getTransportType(), transportOrderInfo.getTransportOrderId()
                                        , transportOrderInfo.getGmtTransit() + transitDays.longValue() * 24L * 60L * 60L * 1000L, Instant.now().toEpochMilli());
                                TransportOrderDetailInfo transportOrderDetailInfo = transportOrderRpc.getDetail(transportOrderInfo.getTransportOrderId());
                                if (CollectionUtils.isEmpty(transportOrderDetailInfo.getOrderPackages()) || transportOrderDetailInfo.getOrderPackages().size() > 1) {
                                    //TODO 基于跨境运输单只有一个包裹逻辑
                                    log.error("找不到运输单下的包裹 或 运输单多于一个包裹");
                                    return;
                                }
                                transportOrderRpc.finishedByPackage(transportOrderDetailInfo.getOrderPackages().get(0).getTransportOrderPackageId());
                            }
                        });
                    }
                });
        log.info("基于易仓API定时任务检查派送中的国外运输单 - 结束 --- {}", new Date());
        return ReturnT.SUCCESS;
    }

    @Job("autoCheckTransportCancellingOrder4Eccang")
    public ReturnT<String> autoCheckTransportCancellingOrder4Eccang(String param) {
        log.info("定时任务检查在易仓上待取消订单的运输单 - 开始 --- {}", new Date());

        //根据参数获取需要执行的第三方系统id
        JSONObject paramObject = JSONObject.parseObject(param);
        //易仓ERP系统id
        String erpTpsId = paramObject.getString("erpTpsId");
        Asserts.assertNotBlank(erpTpsId, "erp系统id不为空");
        //易仓WMS系统id
        String wmsTpsId = paramObject.getString("wmsTpsId");
        Asserts.assertNotBlank(wmsTpsId, "wms系统id不为空");

        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem erpThirdPartySystem = loadSystem(erpTpsId);
        EcCangERPParams params = JSON.parseObject(erpThirdPartySystem.getParams(), EcCangERPParams.class);
        ThirdPartySystem wmsThirdPartySystem = loadSystem(wmsTpsId);

        DConcurrentTemplate.tryLockMode(
                AUTO_CHECK_TRANSPORT_CANCELLING_ORDER_ECCANG_PREFIX.concat(param),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    TransportOrderWithDeliveryQueryCommand queryCommand = new TransportOrderWithDeliveryQueryCommand();
                    queryCommand.setCancelState(CancelState.CANCELING.name());
//                    queryCommand.setDeliveryOrderSupplierId(params.getSupplierId());
                    queryCommand.setDeliveryOrderChannelId(params.getChannelId());
                    //queryCommand.setDeliveryOrderType(OrderType.APO.name());
                    List<TransportOrderLiteInfo> transportOrderInfoList = transportOrderRpc.getListWithDelivery(queryCommand);
                    if (CollectionUtils.isNotEmpty(transportOrderInfoList)) {
                        int resultSize = transportOrderInfoList.size();
                        log.info("获取的待取消状态运输单数量为{}", resultSize);
                        transportOrderInfoList.forEach(transportOrderInfo -> {
                            Optional<DeliveryOrderEcCangApiAssociation> ecCangApiAssociation = deliveryOrderEcCangApiAssociationRepository.loadByTransportOrderId(transportOrderInfo.getTransportOrderId());
                            if (ecCangApiAssociation.isPresent()) {
                                DeliveryOrderEcCangApiAssociation association = ecCangApiAssociation.get();
                                try {
                                    if (cancelWMSOrder(association, wmsThirdPartySystem)) {
                                        TransportOrderConfirmCancelCommand command = new TransportOrderConfirmCancelCommand();
                                        command.setTransportOrderId(transportOrderInfo.getTransportOrderId());
                                        command.setCancelType(CancelType.WAREHOUSE_CANCEL.name());
                                        transportOrderRpc.confirmCancel(command);
                                    } else {
                                        TransportOrderRejectCancelCommand command = new TransportOrderRejectCancelCommand();
                                        command.setTransportOrderId(transportOrderInfo.getTransportOrderId());
                                        transportOrderRpc.rejectCancel(command);
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    log.error("{}调用取消方法出错", transportOrderInfo.getTransportOrderId());
                                }
                            } else {
                                //提示出错
                                log.error("{}找不到已有关联信息", transportOrderInfo.getTransportOrderId());
                            }
                        });
                    }
                }
        );

        log.info("定时任务检查在易仓上待取消订单的运输单 - 结束 --- {}", new Date());
        return ReturnT.SUCCESS;
    }

    private boolean cancelWMSOrder(DeliveryOrderEcCangApiAssociation association, ThirdPartySystem thirdPartySystem) {
        EcCangApiBaseResult<String> cancelOrderApiBaseResult = eccangWMSApiSve.freightCancelOrder(association, thirdPartySystem);
        if (cancelOrderApiBaseResult.getCode().equals("200") && cancelOrderApiBaseResult.getMessage().equals("Success")) {
            log.info("{}取消易仓WMS集运订单成功", association.getDeliveryOrderId());
            return true;
        } else {
            log.error("{}取消易仓WMS集运订单失败{}", association.getDeliveryOrderId(), cancelOrderApiBaseResult.getError());
            return false;
        }
    }

    private void getFreightClaimOrders(ThirdPartySystem thirdPartySystem) {
        EcCangApiBaseResult<String> getClaimOrdersApiBaseResult = eccangWMSApiSve.getClaimOrders(thirdPartySystem);
        if (getClaimOrdersApiBaseResult.getCode().equals("200") && getClaimOrdersApiBaseResult.getMessage().equals("Success")) {
            //传递认领单
        } else {
            log.error("wms获取认领单失败{}", getClaimOrdersApiBaseResult.getError());
        }
    }

    private ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }

}
