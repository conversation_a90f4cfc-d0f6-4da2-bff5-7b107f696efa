package com.newnary.gsp.center.tpsi.service.open1688;

import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.bijia.request.QueryOpen1688MultiLanguageProductDetailRequest;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.bijia.response.QueryOpen1688MultiLanguageProductDetailResponse;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.comon.response.Category1688;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.bijia.request.QueryOpen1688MultiLanguageKeywordRequest;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.bijia.response.QueryOpen1688MultiLanguageKeywordResponse;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.request.*;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.response.*;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;

import java.util.List;

public interface Open1688Service {
    //根据id搜索类目
    Category1688 getCategoryById(ThirdPartySystem thirdPartySystem, String categoryId);

    //关键词搜索
    SearchByKeywordsResponse searchByKeywords(ThirdPartySystem thirdPartySystem, SearchByKeywordsRequest searchByKeywordsRequest);

    //根据商品id获取商品详情
    QueryProductResponse queryProductById(ThirdPartySystem thirdPartySystem, QueryProductRequest queryProductRequest);

    //创建订单
    CreateOpen1688OrderResponse createOpen1688Order(ThirdPartySystem thirdPartySystem, CreateOpen1688OrderRequest createOpen1688OrderRequest);

    //查看订单详情
    QueryOpen1688OrderDetailsResponse getOpen1688OrderDetail(ThirdPartySystem thirdPartySystem, QueryOpen1688OrderDetailsRequest queryOpen1688OrderDetailsRequest);

    //取消订单
    Boolean closeOpen1688Order(ThirdPartySystem thirdPartySystem, CloseOpen1688OrderRequest closeOpen1688OrderRequest);

    //获取订单物流信息
    GetOpen1688OrderLogisticsInfoResponse getOpen1688OrderLogisticsInfo(ThirdPartySystem thirdPartySystem, GetOpen1688OrderLogisticsInfoRequest request);

    //获取订单物流跟踪信息
    GetOpen1688OrderLogisticsTraceInfoResponse getOpen1688OrderLogisticsTraceInfo(ThirdPartySystem thirdPartySystem, GetOpen1688OrderLogisticsTraceInfoRequest request);

    //图搜
    QueryOpen1688ProductByImageUrlResponse queryOpen1688ProductByImageUrl(ThirdPartySystem thirdPartySystem,QueryOpen1688ProductByImageUrlRequest request);

    //精选货源商品列表
    QueryOpen1688JXHYProductResponse queryJXHYProduct(ThirdPartySystem thirdPartySystem,QueryOpen1688JXHYProductRequest request);

    //精选货源商品详情列表
    QueryJXHYProductDetailResponse queryJXHYProductDetail(ThirdPartySystem thirdPartySystem, List<Long> offerIds);

    // 多语言关键词搜索
    QueryOpen1688MultiLanguageKeywordResponse queryMultiLanguageKeyWords(ThirdPartySystem thirdPartySystem, QueryOpen1688MultiLanguageKeywordRequest request);

    // 多语言商详
    QueryOpen1688MultiLanguageProductDetailResponse queryMultiLanguageProductDetail(ThirdPartySystem thirdPartySystem, QueryOpen1688MultiLanguageProductDetailRequest request);

}
