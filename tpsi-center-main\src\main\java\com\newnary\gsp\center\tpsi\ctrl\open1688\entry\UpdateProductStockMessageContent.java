package com.newnary.gsp.center.tpsi.ctrl.open1688.entry;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class UpdateProductStockMessageContent {
    /**
     * 库存变更列表
     */
    private List<OfferInventoryChange> OfferInventoryChangeList;

    @Setter
    @Getter
    public static class OfferInventoryChange{

        /**
         * spuId
         */
        private String offerId;

        /**
         * 可售数量
         */
        private String offerOnSale;

        /**
         * skuId
         */
        private String skuId;

        /**
         * sku可售数量
         */
        private String skuOnSale;

        /**
         * 整体变化数量
         */
        private String quantity;

        /**
         * 库存变更时间
         */
        private String bizTime;
    }
}
