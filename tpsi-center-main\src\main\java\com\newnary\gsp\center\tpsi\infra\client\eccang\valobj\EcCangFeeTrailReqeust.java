package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj;

/**
 * 运费试算实体
 *
 * <AUTHOR>
 * @date 2022/2/24
 */
public class EcCangFeeTrailReqeust {
    /**
     * 国家简码,和仓库代码不能同时为空
     */
    public String country_code;
    /**
     * 订单重量
     */
    public String weight;
    /**
     * 包裹长度CM
     */
    public String length;
    /**
     * 包裹宽度CM
     */
    public String width;
    /**
     * 包裹高度CM
     */
    public String height;
    /**
     * 货物类型，D-文件，L-信封，W-包裹
     */
    public String shipping_type_id;

    /**
     * 产品组
     */
    public String group;
}
