package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.shopee;

import lombok.Data;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingShopeeCategoryTreeResponse {

    /**
     * 类目id
     */
    private String cid;

    /**
     * 类目名称
     */
    private String cname;

    /**
     * 类目层级
     */
    private String level;

    /**
     * 是否叶子类目
     * (0:否   1:是)
     */
    private String is_leaf;

    /**
     * 1级类目id
     */
    private String p_l1_id;

    /**
     * 1级类目名称
     */
    private String p_l1_name;

    /**
     * 2级类目id
     */
    private String p_l2_id;

    /**
     * 2级类目名称
     */
    private String p_l2_name;

    /**
     * 类目获取时间
     */
    private String insert_time;

}
