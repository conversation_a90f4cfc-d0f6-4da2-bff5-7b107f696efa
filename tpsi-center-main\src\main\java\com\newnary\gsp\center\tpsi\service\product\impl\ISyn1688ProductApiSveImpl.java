package com.newnary.gsp.center.tpsi.service.product.impl;

import com.newnary.common.utils.idgenerator.core.UUIDUtils;
import com.newnary.gsp.center.tpsi.infra.model.CrawlerProduct;
import com.newnary.gsp.center.tpsi.infra.model.creator.CrawlerProductCreate;
import com.newnary.gsp.center.tpsi.infra.model.event.CrawlerProduct_WarehousingEvent;
import com.newnary.gsp.center.tpsi.infra.model.vo.CrawlerProductFlushState;
import com.newnary.gsp.center.tpsi.infra.model.vo.CrawlerProductReturnState;
import com.newnary.gsp.center.tpsi.infra.model.vo.CrawlerProductState;
import com.newnary.gsp.center.tpsi.infra.repository.ICrawProductRepository;
import com.newnary.gsp.center.tpsi.service.product.ISync1688ProductApiSve;
import com.newnary.spring.cloud.DomainEventBusHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class ISyn1688ProductApiSveImpl implements ISync1688ProductApiSve {

    @Resource
    private ICrawProductRepository crawProductRepository;

    @Override
    public void storeProductIds(List<String> productList, String keywords, int beginPage, Integer pageSize) {
        productList.forEach(id->{
            CrawlerProductCreate crawlerProductCreate = new CrawlerProductCreate();
            crawlerProductCreate.
                    setProductId(id).
                    setCategoryName(keywords).
                    setBeginPage(beginPage).
                    setPageSize(pageSize).
                    setState(CrawlerProductState.UN_WAREHOUSED.getState()).
                    setCrawlerReturnState(CrawlerProductReturnState.NONE_DETAIL_RETURN.getDetailState()).
                    setFlushState(CrawlerProductFlushState.NONE_FLUSH.getFlushState()).
                    setFlushVersion(0);

            CrawlerProduct crawlerProduct = CrawlerProduct.createWith(crawlerProductCreate);
            if (!crawProductRepository.loadProduct(crawlerProduct).isPresent()){
                //创建落库事件
                crawlerProduct.storeProductId();
            }
        });
    }

    @Override
    public List<CrawlerProduct> loadCrawlerProductByCategoryList(List<String> categoryList, Integer limit, Integer crawlerReturnState,Integer state,Integer flushState) {
        return crawProductRepository.loadCrawlerProductByCategoryList(categoryList,limit,crawlerReturnState,state,flushState);
    }

    @Override
    public CrawlerProduct loadCrawlerDetailReturn(String productId) {
        return crawProductRepository.loadCrawlerDetailReturn(productId);
    }



    @Override
    public void updateProductState(String productId,Integer state,Integer flushState,Integer flushVersion) {
        DomainEventBusHolder.bus().publish(
                new CrawlerProduct_WarehousingEvent(
                        CrawlerProduct.createWith(new CrawlerProductCreate().setProductId(productId).setState(state).setFlushState(flushState).setFlushVersion(flushVersion)),
                        UUIDUtils.getUUID_32()));
    }

}
