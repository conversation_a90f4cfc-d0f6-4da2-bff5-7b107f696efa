package com.newnary.gsp.center.tpsi.infra.model;

import com.newnary.gsp.center.tpsi.infra.model.creator.ThirdPartyAddressMappingCreator;
import com.newnary.gsp.center.tpsi.infra.model.creator.ThirdPartySystemCreator;
import com.newnary.gsp.center.tpsi.infra.model.updater.ThirdPartySystemUpdater;
import com.newnary.gsp.center.tpsi.infra.model.vo.SystemBizType;
import com.newnary.gsp.center.tpsi.infra.model.vo.SystemId;
import com.newnary.gsp.center.tpsi.infra.model.vo.SystemProvider;
import com.newnary.gsp.center.tpsi.infra.model.vo.SystemStatus;
import com.newnary.spring.cloud.domain.Aggregate;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2021/12/10 11:57
 */
@Getter
public class ThirdPartyAddressMapping extends Aggregate {

    /**
     * 系统Id
     */
    private SystemId systemId;

    /**
     * 内部邮编
     */
    private String virtualPostcode;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 国家
     */
    private String country;

    /**
     * 省州
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * 街/镇
     */
    private String town;

    private String address;

    public static ThirdPartyAddressMapping createWith(ThirdPartyAddressMappingCreator creator) {
        return new ThirdPartyAddressMapping(creator);
    }

    public static ThirdPartyAddressMapping loadWith(ThirdPartyAddressMappingCreator creator) {
        return new ThirdPartyAddressMapping(creator.getId(), creator);
    }

    private ThirdPartyAddressMapping(ThirdPartyAddressMappingCreator creator) {
         setSystemId(creator.getSystemId());
         setVirtualPostcode(creator.getVirtualPostcode());
         setPostCode(creator.getPostCode());
         setCountry(creator.getCountry());
         setProvince(creator.getProvince());
         setCity(creator.getCity());
         setArea(creator.getArea());
         setTown(creator.getTown());
         setAddress(creator.getAddress());
    }

    private ThirdPartyAddressMapping(Long id, ThirdPartyAddressMappingCreator creator) {
        super(id);
        setSystemId(creator.getSystemId());
        setVirtualPostcode(creator.getVirtualPostcode());
        setPostCode(creator.getPostCode());
        setCountry(creator.getCountry());
        setProvince(creator.getProvince());
        setCity(creator.getCity());
        setArea(creator.getArea());
        setTown(creator.getTown());
        setAddress(creator.getAddress());
    }

    public SystemId getSystemId() {
        return systemId;
    }

    public void setSystemId(SystemId systemId) {
        this.systemId = systemId;
    }

    public String getVirtualPostcode() {
        return virtualPostcode;
    }

    public void setVirtualPostcode(String virtualPostcode) {
        this.virtualPostcode = virtualPostcode;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }
}
