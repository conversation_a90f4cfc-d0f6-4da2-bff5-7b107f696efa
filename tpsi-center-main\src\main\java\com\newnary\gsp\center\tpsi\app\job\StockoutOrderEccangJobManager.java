package com.newnary.gsp.center.tpsi.app.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.distributed.tools.concurrent.template.DConcurrentTemplate;
import com.newnary.gsp.center.logistics.api.delivery.dto.TrackInfoDTO;
import com.newnary.gsp.center.logistics.api.delivery.dto.TransportOrderPackageDTO;
import com.newnary.gsp.center.logistics.api.delivery.enums.CancelState;
import com.newnary.gsp.center.logistics.api.delivery.enums.CancelType;
import com.newnary.gsp.center.logistics.api.delivery.enums.StockoutOrderState;
import com.newnary.gsp.center.logistics.api.delivery.request.*;
import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderDetailInfo;
import com.newnary.gsp.center.logistics.api.delivery.response.StockoutOrderInfo;
import com.newnary.gsp.center.logistics.api.delivery.response.TransportOrderDetailInfo;
import com.newnary.gsp.center.product.api.product.response.ChannelSaleItemDetailInfo;
import com.newnary.gsp.center.tpsi.infra.client.eccang.params.EcCangERPParams;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.erp.EcCangERPGetOrderListResponse;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.erp.EcCangERPGetPurchaseOrdersResponse;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.wms.EcCangFreightGetOrderInfoResponse;
import com.newnary.gsp.center.tpsi.infra.model.ApiRequestParams;
import com.newnary.gsp.center.tpsi.infra.model.DeliveryOrderEcCangApiAssociation;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.creator.DeliveryOrderEcCangApiAssociationCreator;
import com.newnary.gsp.center.tpsi.infra.model.vo.ApiRequestParamsType;
import com.newnary.gsp.center.tpsi.infra.model.vo.DeliveryOrderItemEcCangApiAssociation;
import com.newnary.gsp.center.tpsi.infra.repository.IApiRequestParamsRepository;
import com.newnary.gsp.center.tpsi.infra.repository.IDeliveryOrderEcCangApiAssociationRepository;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.infra.rpc.*;
import com.newnary.gsp.center.tpsi.infra.translator.DeliveryOrderEcCangApiAssociationTranslator;
import com.newnary.gsp.center.tpsi.service.eccang.IEccangERPApiSve;
import com.newnary.gsp.center.tpsi.service.eccang.IEccangWMSApiSve;
import com.newnary.gsp.center.trade.api.order.response.OrderDTO;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.job.core.handler.annotation.Job;
import com.newnary.spring.cloud.domain.Asserts;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Component
@Slf4j
public class StockoutOrderEccangJobManager {

    private static final String AUTO_PUSH_STOCKOUT_WAITING_ORDER_2_ECCANG_PREFIX = "AUTO_PUSH_STOCKOUT_WAITING_ORDER_2_ECCANG";

    private static final String AUTO_CHECK_STOCKOUTING_ORDER_BASE_ECCANG_PREFIX = "AUTO_CHECK_STOCKOUTING_ORDER_BASE_ECCANG";

    private static final String AUTO_CHECK_STOCKOUT_CANCELLING_ORDER_ECCANG_PREFIX = "AUTO_CHECK_STOCKOUT_CANCELLING_ORDER_ECCANG";

    private static final String LOCK_DELIVERY_ORDER_ECCANG_API_ASSOCIATION_TABLE_PREFIX = "LOCK_DELIVERY_ORDER_ECCANG_API_ASSOCIATION_TABLE";

    @Resource
    private DeliveryOrderRpc deliveryOrderRpc;
    @Resource
    private StockoutOrderRpc stockoutOrderRpc;
    @Resource
    private TransportOrderRpc transportOrderRpc;
    @Resource
    private TradeOrderRpc tradeOrderRpc;
    @Resource
    private SaleItemRpc saleItemRpc;
    @Resource
    private IEccangERPApiSve eccangERPApiSve;
    @Resource
    private IEccangWMSApiSve eccangWMSApiSve;
    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;
    @Resource
    private IApiRequestParamsRepository apiOrderCreateParamsRepository;
    @Resource
    private IDeliveryOrderEcCangApiAssociationRepository deliveryOrderEcCangApiAssociationRepository;

    @Deprecated
    @Job("autoPushStockoutWaitingOrder2Eccang")
    public ReturnT<String> autoPushStockoutWaitingOrder2Eccang(String param) {
        log.info("定时任务推送系统待发货出库单到易仓 - 开始 --- {}", new Date());
        //根据参数获取需要执行的第三方系统id
        JSONObject paramObject = JSONObject.parseObject(param);
        //易仓ERP系统id
        String erpTpsId = paramObject.getString("erpTpsId");
        Asserts.assertNotBlank(erpTpsId, "erp系统id不为空");
        //易仓WMS系统id
        String wmsTpsId = paramObject.getString("wmsTpsId");
        Asserts.assertNotBlank(wmsTpsId, "wms系统id不为空");

        String stockoutOrderId = paramObject.getString("stockoutOrderId");
        long gmtCreateEc = Long.parseLong(paramObject.getString("gmtCreateEc"));

        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(erpTpsId);
        EcCangERPParams params = JSON.parseObject(thirdPartySystem.getParams(), EcCangERPParams.class);

        //查询租户下的易仓店铺，国内仓收货地址
        log.info("开始获取api订单、商品创建请求参数");
        Optional<ApiRequestParams> erpCreateProductRequestParams = apiOrderCreateParamsRepository.loadBySystemBizIdAndType(erpTpsId, ApiRequestParamsType.PRODUCT.name());
        Asserts.assertTrue(erpCreateProductRequestParams.isPresent(), "erp创建产品请求的配置参数信息不存在");
        Optional<ApiRequestParams> erpCreateOrderRequestParams = apiOrderCreateParamsRepository.loadBySystemBizIdAndType(erpTpsId, ApiRequestParamsType.ORDER.name());
        Asserts.assertTrue(erpCreateOrderRequestParams.isPresent(), "erp创建订单请求的配置参数信息不存在");

        DConcurrentTemplate.tryLockMode(
                AUTO_PUSH_STOCKOUT_WAITING_ORDER_2_ECCANG_PREFIX.concat(param),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    log.info("执行 核心逻辑--- {}", new Date());
                    StockoutOrderWithDeliveryQueryCommand queryCommand = new StockoutOrderWithDeliveryQueryCommand();
                    queryCommand.setStockoutOrderState(StockoutOrderState.WAIT_STOCKOUT.getState());
                    if (StringUtils.isNotEmpty(stockoutOrderId)) {
                        queryCommand.setStockoutOrderId(stockoutOrderId);
                    }
                    if (gmtCreateEc > 0) {
                        queryCommand.setGmtCreateEc(gmtCreateEc);
                    }
                    queryCommand.setDeliveryOrderSupplierId(params.getSupplierId());
                    queryCommand.setDeliveryOrderChannelId(params.getChannelId());
                    //queryCommand.setDeliveryOrderType(OrderType.APO.name());
                    List<StockoutOrderInfo> stockoutOrderInfoList = stockoutOrderRpc.getListWithDelivery(queryCommand);
                    if (CollectionUtils.isNotEmpty(stockoutOrderInfoList)) {
                        int resultSize = stockoutOrderInfoList.size();
                        log.info("获取的待出库状态出库单数量为{}", resultSize);
                        stockoutOrderInfoList.forEach(stockoutOrderInfo -> {
                            log.info("现在获取出库单{}关联的发货单{}", stockoutOrderInfo.getStockoutOrderId(), stockoutOrderInfo.getReferenceId());
                            //第三步 获取发货单详细信息
                            DeliveryOrderDetailInfo deliveryOrderDetailInfo = deliveryOrderRpc.getDetailInfo(stockoutOrderInfo.getReferenceId());
                            if (null != deliveryOrderDetailInfo) {
                                log.info("发货单{}详情已获取", deliveryOrderDetailInfo.getDeliveryOrderId());

                                //创建易仓ERP产品
                                if (createErpProduct(deliveryOrderDetailInfo, thirdPartySystem, erpCreateProductRequestParams.get())) {
                                    //创建ecangERP订单
                                    createErpOrder(stockoutOrderInfo.getStockoutOrderId(), deliveryOrderDetailInfo, thirdPartySystem, wmsTpsId, erpCreateOrderRequestParams.get());
                                }
                            } else {
                                log.warn("出库单{}获取不到关联发货单{}", stockoutOrderInfo.getStockoutOrderId(), stockoutOrderInfo.getReferenceId());
                            }
                        });
                    }
                }
        );

        log.info("定时任务推送系统待发货出库单到易仓ERP订单 - 结束 --- {}", new Date());
        return ReturnT.SUCCESS;
    }

    private boolean createErpProduct(DeliveryOrderDetailInfo deliveryOrderDetailInfo, ThirdPartySystem thirdPartySystem, ApiRequestParams erpCreateProductRequestParams) {
        AtomicBoolean createSuccess = new AtomicBoolean(false);
        deliveryOrderDetailInfo.items.forEach(item -> {
                    EcCangApiBaseResult<String> syncProductApiBaseResult = eccangERPApiSve.syncProduct(item, thirdPartySystem, erpCreateProductRequestParams, deliveryOrderDetailInfo.getChannelId());
                    if (syncProductApiBaseResult.getCode().equals("200") && syncProductApiBaseResult.getMessage().equals("Success")) {
                        log.info("{}创建易仓ERP产品成功", item.supplierSkuId);
                        createSuccess.set(true);
                    } else {
                        log.error("{}创建易仓ERP产品失败{}", item.supplierSkuId, syncProductApiBaseResult.getError());
                        createSuccess.set(false);
                    }
                }
        );
        return createSuccess.get();
    }

    private void createErpOrder(String stockoutOrderId, DeliveryOrderDetailInfo deliveryOrderDetailInfo, ThirdPartySystem thirdPartySystem, String wmsTpsId, ApiRequestParams erpCreateOrderRequestParams) {
        EcCangApiBaseResult<String> apiBaseResult = eccangERPApiSve.createOrder(deliveryOrderDetailInfo, thirdPartySystem, erpCreateOrderRequestParams);

        if (apiBaseResult.getCode().equals("200") && apiBaseResult.getMessage().equals("Success")) {
            log.info("{}创建易仓ERP订单成功", stockoutOrderId);
            JSONObject successDataJsonObject = JSONObject.parseObject(apiBaseResult.getData());
            String thirdOrderId = successDataJsonObject.getString("saleOrderCode");

            StockoutOrderThirdPushedCommand stockoutOrderThirdPushedCommand = new StockoutOrderThirdPushedCommand();
            stockoutOrderThirdPushedCommand.setStockoutOrderId(stockoutOrderId);
            stockoutOrderThirdPushedCommand.setThirdOrderId(thirdOrderId);

            try {
                //成功创建ERP订单 调用出库单受理接口
                stockoutOrderRpc.accept(stockoutOrderId);
                stockoutOrderRpc.thirdPushed(stockoutOrderThirdPushedCommand);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("{}调用受理方法出错", stockoutOrderId);
                //受理失败则取消订单
                EcCangApiBaseResult<String> cancelOrderApiBaseResult = eccangERPApiSve.cancelOrder(thirdPartySystem, stockoutOrderThirdPushedCommand.getThirdOrderId());
                log.error("取消订单{}的结果为{}", thirdOrderId, cancelOrderApiBaseResult.getMessage());
            }
            createAssociation(deliveryOrderDetailInfo.getTradeOrderId(), thirdPartySystem.getBizId(), wmsTpsId);
        } else {
            log.error("{}创建易仓ERP订单失败{}", stockoutOrderId, apiBaseResult.getError());
        }
    }

    @Job("autoCheckStockoutingOrderBaseEcCang")
    public ReturnT<String> autoCheckStockoutingOrderBaseEcCang(String param) {
        log.info("基于易仓API定时任务检查出库中出库单 - 开始 --- {}", new Date());
        //从参数中获取需要执行的第三方系统bizId
        JSONObject paramObject = JSONObject.parseObject(param);
        //易仓ERP系统id
        String erpTpsId = paramObject.getString("erpTpsId");
        Asserts.assertNotBlank(erpTpsId, "erp系统id不为空");
        //易仓WMS系统id
        String wmsTpsId = paramObject.getString("wmsTpsId");
        Asserts.assertNotBlank(wmsTpsId, "wms系统id不为空");

        String stockoutOrderId = paramObject.getString("stockoutOrderId");
        long gmtCreateEc = Long.parseLong(paramObject.getString("gmtCreateEc"));

        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem erpThirdPartySystem = loadSystem(erpTpsId);
        EcCangERPParams params = JSON.parseObject(erpThirdPartySystem.getParams(), EcCangERPParams.class);
        ThirdPartySystem wmsThirdPartySystem = loadSystem(wmsTpsId);

        //查询租户下的易仓店铺，国内仓收货地址
        log.info("开始获取api订单、商品创建请求参数");
        Optional<ApiRequestParams> erpCreateProductRequestParams = apiOrderCreateParamsRepository.loadBySystemBizIdAndType(erpTpsId, ApiRequestParamsType.PRODUCT.name());
        Asserts.assertTrue(erpCreateProductRequestParams.isPresent(), "erp创建产品请求的配置参数信息不存在");
        Optional<ApiRequestParams> erpCreateOrderRequestParams = apiOrderCreateParamsRepository.loadBySystemBizIdAndType(erpTpsId, ApiRequestParamsType.ORDER.name());
        Asserts.assertTrue(erpCreateOrderRequestParams.isPresent(), "erp创建订单请求的配置参数信息不存在");
        Optional<ApiRequestParams> erpCreatePurchaseOrderRequestParams = apiOrderCreateParamsRepository.loadBySystemBizIdAndType(erpTpsId, ApiRequestParamsType.PURCHASE_ORDER.name());
        Asserts.assertTrue(erpCreatePurchaseOrderRequestParams.isPresent(), "erp创建采购单请求的配置参数信息不存在");
        Optional<ApiRequestParams> wmsCreateFreightOrderRequestParams = apiOrderCreateParamsRepository.loadBySystemBizIdAndType(wmsTpsId, ApiRequestParamsType.FREIGHT_ORDER.name());
        Asserts.assertTrue(wmsCreateFreightOrderRequestParams.isPresent(), "wms创建集运订单请求的配置参数信息不存在");

        DConcurrentTemplate.tryLockMode(
                AUTO_CHECK_STOCKOUTING_ORDER_BASE_ECCANG_PREFIX.concat(param),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    log.info("执行 核心逻辑--- {}", new Date());
                    StockoutOrderWithDeliveryQueryCommand queryCommand = new StockoutOrderWithDeliveryQueryCommand();
                    queryCommand.setStockoutOrderState(StockoutOrderState.STOCKOUTING.getState());
                    if (StringUtils.isNotEmpty(stockoutOrderId)) {
                        queryCommand.setStockoutOrderId(stockoutOrderId);
                    }
                    if (gmtCreateEc > 0) {
                        queryCommand.setGmtCreateEc(gmtCreateEc);
                    }
                    queryCommand.setDeliveryOrderSupplierId(params.getSupplierId());
                    queryCommand.setDeliveryOrderChannelId(params.getChannelId());
                    //queryCommand.setDeliveryOrderType(OrderType.APO.name());
                    List<StockoutOrderInfo> stockoutOrderInfoList = stockoutOrderRpc.getListWithDelivery(queryCommand);
                    queryCommand.setStockoutOrderState(StockoutOrderState.STOCKOUT_SHIPPED.getState());
                    stockoutOrderInfoList.addAll(stockoutOrderRpc.getListWithDelivery(queryCommand));
                    if (CollectionUtils.isNotEmpty(stockoutOrderInfoList)) {
                        int resultSize = stockoutOrderInfoList.size();
                        stockoutOrderInfoList.forEach(stockoutOrderInfo -> {
                            try {
                                //调用易仓查询订单接口
                                coreHandleStockoutingOrder(stockoutOrderInfo, erpThirdPartySystem, wmsThirdPartySystem,
                                        erpCreateProductRequestParams.get(),
                                        erpCreateOrderRequestParams.get(),
                                        erpCreatePurchaseOrderRequestParams.get());
                            } catch (Exception e) {
                                log.error("{}处理已出库单出错{}", stockoutOrderInfo.getStockoutOrderId(), e.getMessage());
                            }
                        });
                        log.info("获取的待出库状态出库单数量为{}", resultSize);
                    }
                });
        log.info("基于易仓API定时任务检查出库中出库单 - 结束 --- {}", new Date());
        return ReturnT.SUCCESS;
    }

    /**
     * 对出库单详情的处理逻辑
     *
     * @param stockoutOrderInfo
     */
    private void coreHandleStockoutingOrder(StockoutOrderInfo stockoutOrderInfo,
                                            ThirdPartySystem erpThirdPartySystem,
                                            ThirdPartySystem wmsThirdPartySystem,
                                            ApiRequestParams erpCreateProductRequestParams,
                                            ApiRequestParams erpCreateOrderRequestParams,
                                            ApiRequestParams erpCreatePurchaseOrderRequestParams) {
        log.info("现在获取出库单{}关联的发货单{}", stockoutOrderInfo.getStockoutOrderId(), stockoutOrderInfo.getReferenceId());
        //第三步 获取发货单详细信息
        DeliveryOrderDetailInfo deliveryOrderDetailInfo = deliveryOrderRpc.getDetailInfo(stockoutOrderInfo.getReferenceId());
        if (null != deliveryOrderDetailInfo) {
            log.info("发货单{}详情已获取", deliveryOrderDetailInfo.getDeliveryOrderId());

            //检查是否已经推送第三方,如果未推送第三方，则先推送第三方
            checkStockoutThirdPushed(stockoutOrderInfo, deliveryOrderDetailInfo, erpThirdPartySystem, erpCreateProductRequestParams, erpCreateOrderRequestParams);

            DConcurrentTemplate.tryLockMode(
                    LOCK_DELIVERY_ORDER_ECCANG_API_ASSOCIATION_TABLE_PREFIX.concat(deliveryOrderDetailInfo.getDeliveryOrderId()),
                    lock -> lock.tryLock(3, TimeUnit.SECONDS),
                    () -> {
                        Optional<DeliveryOrderEcCangApiAssociation> ecCangApiAssociation = deliveryOrderEcCangApiAssociationRepository.loadByDeliveryOrderId(deliveryOrderDetailInfo.getDeliveryOrderId());
                        if (ecCangApiAssociation.isPresent()) {
                            DeliveryOrderEcCangApiAssociation association = ecCangApiAssociation.get();

                            //获取erp订单信息、获取erp采购单信息
                            checkErpOrder(association, stockoutOrderInfo, deliveryOrderDetailInfo, erpThirdPartySystem, erpCreatePurchaseOrderRequestParams);

                            //更新erp采购单信息,如果有货运单号,则更新集运订单产品包裹号
                            checkPurchaseOrderTrackingNo(association, erpThirdPartySystem, wmsThirdPartySystem);

                            deliveryOrderEcCangApiAssociationRepository.store(association);
                        } else {
                            //提示出错
                            log.error("{}找不到已有关联信息", stockoutOrderInfo.getStockoutOrderId());

                            createAssociation(deliveryOrderDetailInfo.getTradeOrderId(), erpThirdPartySystem.getBizId(), wmsThirdPartySystem.getBizId());
                        }
                    });
        } else {
            log.warn("出库单{}获取不到关联发货单{}", stockoutOrderInfo.getStockoutOrderId(), stockoutOrderInfo.getReferenceId());
        }
    }

    private void createAssociation(String tradeOrderId, String erpTpsId, String wmsTpsId) {
        OrderDTO tradeOrder = tradeOrderRpc.getTradeOrder(tradeOrderId);
        tradeOrder.getOrderSubItemList().forEach(subItem -> {
            DeliveryOrderDetailInfo deliveryOrderDetailInfo = deliveryOrderRpc.getDetailInfo(subItem.getDeliveryOrderId());
            if (StringUtils.isNotEmpty(deliveryOrderDetailInfo.getStockoutOrderId())) {
                StockoutOrderInfo stockoutOrderInfo = stockoutOrderRpc.getStockoutOrder(deliveryOrderDetailInfo.getStockoutOrderId());
                //TODO 暂时只对接头程运输单
                if (StringUtils.isNotEmpty(deliveryOrderDetailInfo.getCrossTransportOrderId())) {
                    TransportOrderDetailInfo transportOrderInfo = transportOrderRpc.getDetail(deliveryOrderDetailInfo.getCrossTransportOrderId());
                    if (ObjectUtils.isNotEmpty(stockoutOrderInfo) && ObjectUtils.isNotEmpty(transportOrderInfo)) {
                        DeliveryOrderEcCangApiAssociationCreator associationCreator = DeliveryOrderEcCangApiAssociationTranslator.buildCreator(tradeOrder, deliveryOrderDetailInfo, stockoutOrderInfo.getStockoutOrderId(), transportOrderInfo.getTransportOrderId(), stockoutOrderInfo.getThirdOrderId(), erpTpsId, wmsTpsId);
                        associationCreator.getItems().forEach(item -> {
                            ChannelSaleItemDetailInfo channelSaleItemDetailInfo = saleItemRpc.querySaleItem(deliveryOrderDetailInfo.getChannelId(), item.getSaleItemCode());
                            JSONArray specsArray = new JSONArray();
                            if (CollectionUtils.isNotEmpty(channelSaleItemDetailInfo.getSkuDetailInfo().getSkuInfo().getSpecs())) {
                                channelSaleItemDetailInfo.getSkuDetailInfo().getSkuInfo().getSpecs().forEach(supplierSkuSpecInfo -> {
                                    JSONObject specsJson = new JSONObject();
                                    specsJson.put(supplierSkuSpecInfo.getSpecName(), supplierSkuSpecInfo.getSpecValue());
                                    specsArray.add(specsJson);
                                });
                            }
                            item.setWmsProductStandard(specsArray.toJSONString());
                        });

                        //记录发货单与ERP和WMS的关联
                        try {
                            deliveryOrderEcCangApiAssociationRepository.store(DeliveryOrderEcCangApiAssociation.createWith(associationCreator));
                            log.info("对接系统保存发货单关联成功 {}", subItem.getDeliveryOrderId());
                        } catch (Exception e) {
                            e.printStackTrace();
                            log.error("{}保存发货单关联出错", subItem.getDeliveryOrderId());
                        }
                    } else {
                        log.error("发货单{}缺失关联出库单或运输单", subItem.getDeliveryOrderId());
                    }
                } else {
                    log.error("发货单{}未有跨境运输单关联", subItem.getDeliveryOrderId());
                }
            } else {
                log.error("发货单{}未有出库单关联", subItem.getDeliveryOrderId());
            }
        });
    }

    private void checkErpOrder(DeliveryOrderEcCangApiAssociation association, StockoutOrderInfo stockoutOrderInfo, DeliveryOrderDetailInfo deliveryOrderDetailInfo, ThirdPartySystem thirdPartySystem, ApiRequestParams erpCreatePurchaseOrderRequestParams) {
        log.info("判断出库单{}在易仓上的状态", stockoutOrderInfo.getStockoutOrderId());
        EcCangApiBaseResult<String> getOrderListApiResult = eccangERPApiSve.getOrderListBySaleOrderCode(thirdPartySystem, stockoutOrderInfo.getThirdOrderId());
        if (getOrderListApiResult.getCode().equals("200") && getOrderListApiResult.getMessage().equals("Success")) {
            List<EcCangERPGetOrderListResponse> responseOrders = JSONObject.parseArray(getOrderListApiResult.getData(), EcCangERPGetOrderListResponse.class);
            if (responseOrders != null && responseOrders.size() == 1) {
                EcCangERPGetOrderListResponse orderResponse = responseOrders.get(0);
                association.setErpOrderSaleOrderCode(orderResponse.saleOrderCode);
                association.setErpOrderShippingMethodNo(orderResponse.shippingMethodNo);
                association.setErpOrderStatus(orderResponse.status);

                switch (orderResponse.status) {
                    case 0:
                        StockoutOrderConfirmCancelCommand stockoutOrderConfirmCancelCommand = new StockoutOrderConfirmCancelCommand();
                        stockoutOrderConfirmCancelCommand.setStockoutOrderId(stockoutOrderInfo.getStockoutOrderId());
                        stockoutOrderConfirmCancelCommand.setCancelType(CancelType.SUPPLIER_CANCEL.name());
                        stockoutOrderRpc.confirmCancel(stockoutOrderConfirmCancelCommand);
                        break;
                    case 4:
                        //不需要从易仓ERP获取订单发货状态来更新出库单已发货
//                        if (StringUtils.isNotEmpty(orderResponse.shippingMethod) || StringUtils.isNotEmpty(orderResponse.shippingMethodNo)) {
//                            log.info("出库单{}已有tn，调用发货方法", stockoutOrderInfo.getStockoutOrderId());
//                            //调用出库单发货接口（国内tn）
//                            StockoutOrderUpdateTrackCommand stockoutOrderUpdateTrackCommand = new StockoutOrderUpdateTrackCommand();
//                            stockoutOrderUpdateTrackCommand.setStockoutOrderId(stockoutOrderInfo.getStockoutOrderId());
//                            stockoutOrderUpdateTrackCommand.setTrackInfos(Arrays.asList(new TrackInfoDTO(orderResponse.shippingMethod, orderResponse.shippingMethodNo)));
//                            stockoutOrderRpc.updateTrack(stockoutOrderUpdateTrackCommand);
//                        }
//                        //如果易仓ERP订单状态已完成，标记出库单已出库
//                        stockoutOrderRpc.deliver(stockoutOrderInfo.getStockoutOrderId());

                        break;
                    case 6:
                        //"缺货处理"
                        log.info("出库单{}在erp的订单状态为缺货", stockoutOrderInfo.getStockoutOrderId());
                        //先检查是否已经创建了采购单
                        association.getItems().forEach(item -> {
                            if (StringUtils.isEmpty(item.getErpPurchaseOrderCode())) {
                                //创建采购单
                                EcCangApiBaseResult<String> syncPurchaseOrdersApiBaseResult = eccangERPApiSve.syncPurchaseOrders(association.getOrderRefNo(), item, thirdPartySystem, erpCreatePurchaseOrderRequestParams);
                                if (ObjectUtils.isNotEmpty(syncPurchaseOrdersApiBaseResult)
                                        && syncPurchaseOrdersApiBaseResult.getCode().equals("200") && syncPurchaseOrdersApiBaseResult.getMessage().equals("Success")) {
                                    log.info("{}创建易仓ERP采购单成功", item.getSaleItemCode());

                                    Object object = JSONObject.parse(syncPurchaseOrdersApiBaseResult.getData());
                                    if (object instanceof JSONObject) {
                                        //更新信息
                                        JSONObject syncPurchaseOrdersRetObj = JSONObject.parseObject(syncPurchaseOrdersApiBaseResult.getData());
                                        String poCode = syncPurchaseOrdersRetObj.getString("poCode");
                                        item.setErpPurchaseOrderCode(poCode);
                                        item.setErpPurchaseOrderStatus(1);
                                    } else if (object instanceof JSONArray) {
                                        List<EcCangERPGetPurchaseOrdersResponse> responsePurchaseOrders = JSONObject.parseArray(syncPurchaseOrdersApiBaseResult.getData(), EcCangERPGetPurchaseOrdersResponse.class);
                                        if (CollectionUtils.isNotEmpty(responsePurchaseOrders)) {
                                            item.setErpPurchaseOrderCode(responsePurchaseOrders.get(0).po_code);
                                            item.setErpPurchaseOrderStatus(responsePurchaseOrders.get(0).po_staus);
                                        }
                                    }
                                } else {
                                    log.error("{}创建易仓ERP采购单失败{}", item.getSaleItemCode(), syncPurchaseOrdersApiBaseResult.getError());
                                }
                            }
                        });
                        break;
                    default:
                        log.info("状态：{},暂无需处理", orderResponse.status);
                        break;
                }
            } else {
                log.error("{}获取易仓ERP订单失败{}，数据为空", stockoutOrderInfo.getStockoutOrderId(), getOrderListApiResult.getData());
            }
        } else {
            log.error("{}获取易仓ERP订单失败{}", stockoutOrderInfo.getStockoutOrderId(), getOrderListApiResult.getError());
        }
    }

    @Job("autoCheckStockoutedOrderBaseEcCang")
    public ReturnT<String> autoCheckStockoutedOrderBaseEcCang(String param) {
        log.info("基于易仓API定时任务检查已出库出库单 - 开始 --- {}", new Date());
        //从参数中获取需要执行的第三方系统bizId
        JSONObject paramObject = JSONObject.parseObject(param);
        //易仓ERP系统id
        String erpTpsId = paramObject.getString("erpTpsId");
        Asserts.assertNotBlank(erpTpsId, "erp系统id不为空");
        //易仓WMS系统id
        String wmsTpsId = paramObject.getString("wmsTpsId");
        Asserts.assertNotBlank(wmsTpsId, "wms系统id不为空");

        String stockoutOrderId = paramObject.getString("stockoutOrderId");
        long gmtCreateEc = Long.parseLong(paramObject.getString("gmtCreateEc"));

        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem erpThirdPartySystem = loadSystem(erpTpsId);
        EcCangERPParams params = JSON.parseObject(erpThirdPartySystem.getParams(), EcCangERPParams.class);
        ThirdPartySystem wmsThirdPartySystem = loadSystem(wmsTpsId);

        //查询租户下的易仓店铺，国内仓收货地址
        log.info("开始获取api订单、商品创建请求参数");
        Optional<ApiRequestParams> erpCreateProductRequestParams = apiOrderCreateParamsRepository.loadBySystemBizIdAndType(erpTpsId, ApiRequestParamsType.PRODUCT.name());
        Asserts.assertTrue(erpCreateProductRequestParams.isPresent(), "erp创建产品请求的配置参数信息不存在");
        Optional<ApiRequestParams> erpCreateOrderRequestParams = apiOrderCreateParamsRepository.loadBySystemBizIdAndType(erpTpsId, ApiRequestParamsType.ORDER.name());
        Asserts.assertTrue(erpCreateOrderRequestParams.isPresent(), "erp创建订单请求的配置参数信息不存在");
        Optional<ApiRequestParams> erpCreatePurchaseOrderRequestParams = apiOrderCreateParamsRepository.loadBySystemBizIdAndType(erpTpsId, ApiRequestParamsType.PURCHASE_ORDER.name());
        Asserts.assertTrue(erpCreatePurchaseOrderRequestParams.isPresent(), "erp创建采购单请求的配置参数信息不存在");
        Optional<ApiRequestParams> wmsCreateFreightOrderRequestParams = apiOrderCreateParamsRepository.loadBySystemBizIdAndType(wmsTpsId, ApiRequestParamsType.FREIGHT_ORDER.name());
        Asserts.assertTrue(wmsCreateFreightOrderRequestParams.isPresent(), "wms创建集运订单请求的配置参数信息不存在");

        DConcurrentTemplate.tryLockMode(
                AUTO_CHECK_STOCKOUTING_ORDER_BASE_ECCANG_PREFIX.concat(param),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    log.info("执行 核心逻辑--- {}", new Date());
                    StockoutOrderWithDeliveryQueryCommand queryCommand = new StockoutOrderWithDeliveryQueryCommand();
                    queryCommand.setStockoutOrderState(StockoutOrderState.STOCKOUT_SHIPPED.getState());
                    if (StringUtils.isNotEmpty(stockoutOrderId)) {
                        queryCommand.setStockoutOrderId(stockoutOrderId);
                    }
                    if (gmtCreateEc > 0) {
                        queryCommand.setGmtCreateEc(gmtCreateEc);
                    }
                    queryCommand.setDeliveryOrderSupplierId(params.getSupplierId());
                    queryCommand.setDeliveryOrderChannelId(params.getChannelId());
                    //queryCommand.setDeliveryOrderType(OrderType.APO.name());
                    List<StockoutOrderInfo> stockoutOrderInfoList = stockoutOrderRpc.getListWithDelivery(queryCommand);
                    if (CollectionUtils.isNotEmpty(stockoutOrderInfoList)) {
                        int resultSize = stockoutOrderInfoList.size();
                        stockoutOrderInfoList.forEach(stockoutOrderInfo -> {
                            try {
                                //调用易仓查询订单接口
                                coreHandleStockoutedOrder(stockoutOrderInfo, erpThirdPartySystem, wmsThirdPartySystem);
                            } catch (Exception e) {
                                log.error("{}处理已出库单出错{}", stockoutOrderInfo.getStockoutOrderId(), e.getMessage());
                            }
                        });
                        log.info("获取的已出库状态出库单数量为{}", resultSize);
                    }
                });
        log.info("基于易仓API定时任务检查已出库出库单 - 结束 --- {}", new Date());
        return ReturnT.SUCCESS;
    }

    /**
     * 对出库单详情的处理逻辑
     *
     * @param stockoutOrderInfo
     */
    private void coreHandleStockoutedOrder(StockoutOrderInfo stockoutOrderInfo,
                                           ThirdPartySystem erpThirdPartySystem,
                                           ThirdPartySystem wmsThirdPartySystem) {
        log.info("现在获取出库单{}关联的发货单{}", stockoutOrderInfo.getStockoutOrderId(), stockoutOrderInfo.getReferenceId());
        //第三步 获取发货单详细信息
        DeliveryOrderDetailInfo deliveryOrderDetailInfo = deliveryOrderRpc.getDetailInfo(stockoutOrderInfo.getReferenceId());
        if (null != deliveryOrderDetailInfo) {
            log.info("发货单{}详情已获取", deliveryOrderDetailInfo.getDeliveryOrderId());

            DConcurrentTemplate.tryLockMode(
                    LOCK_DELIVERY_ORDER_ECCANG_API_ASSOCIATION_TABLE_PREFIX.concat(deliveryOrderDetailInfo.getDeliveryOrderId()),
                    lock -> lock.tryLock(3, TimeUnit.SECONDS),
                    () -> {
                        Optional<DeliveryOrderEcCangApiAssociation> ecCangApiAssociation = deliveryOrderEcCangApiAssociationRepository.loadByDeliveryOrderId(deliveryOrderDetailInfo.getDeliveryOrderId());
                        if (ecCangApiAssociation.isPresent()) {
                            DeliveryOrderEcCangApiAssociation association = ecCangApiAssociation.get();

                            //获取出库单跟踪号信息,如果有货运单号,则更新集运订单产品包裹号
                            checkPurchaseOrderTrackingNoByStockout(association, deliveryOrderDetailInfo, wmsThirdPartySystem);

                            deliveryOrderEcCangApiAssociationRepository.store(association);
                        } else {
                            //提示出错
                            log.error("{}找不到已有关联信息", stockoutOrderInfo.getStockoutOrderId());

                            createAssociation(deliveryOrderDetailInfo.getTradeOrderId(), erpThirdPartySystem.getBizId(), wmsThirdPartySystem.getBizId());

                        }
                    });
        } else {
            log.warn("出库单{}获取不到关联发货单{}", stockoutOrderInfo.getStockoutOrderId(), stockoutOrderInfo.getReferenceId());
        }
    }

    private void checkPurchaseOrderTrackingNoByStockout(DeliveryOrderEcCangApiAssociation association, DeliveryOrderDetailInfo deliveryOrderDetailInfo, ThirdPartySystem wmsThirdPartySystem) {
        //先更新出库单跟踪号信息
        updateStockoutOrderTrack(association, deliveryOrderDetailInfo, association.getStockoutOrderId());
        //再更新易仓WMS集运订单包裹信息
        editFreightProduct(association, wmsThirdPartySystem);

    }

    private void checkPurchaseOrderTrackingNo(DeliveryOrderEcCangApiAssociation association,
                                              ThirdPartySystem erpThirdPartySystem,
                                              ThirdPartySystem wmsThirdPartySystem) {
        //先更新易仓ERP采购信息
        getERPPurchaseOrder(association, erpThirdPartySystem, association.getOrderRefNo(), association.getStockoutOrderId());
        //更新易仓采购单本地运费
        updatePurchaseShippingAmount(association);
        //再更新易仓WMS集运订单包裹信息
        editFreightProduct(association, wmsThirdPartySystem);

    }

    private void updatePurchaseShippingAmount(DeliveryOrderEcCangApiAssociation association) {
        BigDecimal purchaseShippingFee = BigDecimal.ZERO;
        for (DeliveryOrderItemEcCangApiAssociation item : association.getItems()) {
            if (null != item.getErpPurchaseOrderShippingFee()) {
                purchaseShippingFee = purchaseShippingFee.add(new BigDecimal(item.getErpPurchaseOrderShippingFee()));
            }
        }
        tradeOrderRpc.updatePurchaseShippingAmount(association.getDeliveryOrderId(), "CNY", purchaseShippingFee);
    }

    private void getERPPurchaseOrder(DeliveryOrderEcCangApiAssociation association, ThirdPartySystem thirdPartySystem, String orderRefNo, String stockoutOrderId) {
        EcCangApiBaseResult<String> getPurchaseOrdersApiBaseResult = eccangERPApiSve.getPurchaseOrders(orderRefNo, thirdPartySystem);
        if (getPurchaseOrdersApiBaseResult.getCode().equals("200") && getPurchaseOrdersApiBaseResult.getMessage().equals("Success")) {
            //更新关联信息
            List<EcCangERPGetPurchaseOrdersResponse> responsePurchaseOrders = JSONObject.parseArray(getPurchaseOrdersApiBaseResult.getData(), EcCangERPGetPurchaseOrdersResponse.class);
            if (CollectionUtils.isNotEmpty(responsePurchaseOrders)) {
                log.info("{}获取易仓ERP采购单信息成功", orderRefNo);
                responsePurchaseOrders.forEach(respPurchaseOrder -> {
                    if (respPurchaseOrder.po_staus != 9) {  //非已撤销
                        respPurchaseOrder.detail.forEach(purchaseOrderDetail -> {
                            association.getItems().forEach(item -> {
                                if (item.getCustomCode().equalsIgnoreCase(purchaseOrderDetail.product_sku)) {
                                    item.setErpPurchaseOrderCode(respPurchaseOrder.po_code);
                                    item.setErpPurchaseOrderStatus(respPurchaseOrder.po_staus);
                                    item.setErpPurchaseOrderTrackingNo(respPurchaseOrder.tracking_no);
                                    item.setErpPurchaseOrderShippingFee(respPurchaseOrder.pay_ship_amount);
                                    item.setErpPurchaseOrderPayableAmount(respPurchaseOrder.payable_amount);
                                    if (StringUtils.isNotEmpty(item.getErpPurchaseOrderTrackingNo())) {
                                        StockoutOrderUpdateTrackCommand stockoutOrderUpdateTrackCommand = new StockoutOrderUpdateTrackCommand();
                                        stockoutOrderUpdateTrackCommand.setStockoutOrderId(stockoutOrderId);
                                        String carrierCode = respPurchaseOrder.ps_name;
                                        if (StringUtils.isEmpty(carrierCode))
                                            carrierCode = "自动获取无承运商";
                                        stockoutOrderUpdateTrackCommand.setTrackInfos(Arrays.asList(new TrackInfoDTO(carrierCode, respPurchaseOrder.tracking_no)));
                                        stockoutOrderRpc.updateTrack(stockoutOrderUpdateTrackCommand);
                                    }
                                }
                            });
                        });
                    }
                });
            }
        }
    }

    private void updateStockoutOrderTrack(DeliveryOrderEcCangApiAssociation association, DeliveryOrderDetailInfo deliveryOrderDetailInfo, String stockoutOrderId) {
        //TODO 供应商手动发货更新跟踪号获取
        //更新关联信息
        if (CollectionUtils.isNotEmpty(deliveryOrderDetailInfo.getItems())) {
            log.info("{}获取出库单信息成功", stockoutOrderId);
            deliveryOrderDetailInfo.getItems().forEach(deliveryItem -> {
                association.getItems().forEach(item -> {
                    if (item.getCustomCode().equalsIgnoreCase(deliveryItem.getCustomCode())) {
                        log.info("更新{} - {}的跟踪号信息{}", deliveryOrderDetailInfo.getDeliveryOrderId(), deliveryItem.getCustomCode(), deliveryOrderDetailInfo.getStockoutTrackInfoTrackingNumberStr());
                        List<TrackInfoDTO> stockoutTrackingInfos = deliveryOrderDetailInfo.getStockoutTrackingInfos();
                        if (CollectionUtils.isNotEmpty(stockoutTrackingInfos)) {
                            item.setErpPurchaseOrderTrackingNo(StringUtils.isNotEmpty(stockoutTrackingInfos.get(0).getTrackingNo()) ? stockoutTrackingInfos.get(0).getTrackingNo() : null);
                        }
                    }
                });
            });
        }
    }

    private void editFreightProduct(DeliveryOrderEcCangApiAssociation association, ThirdPartySystem wmsThirdPartySystem) {
        if (StringUtils.isEmpty(association.getTransportOrderId())) {
            return;
        }
        TransportOrderDetailInfo transportOrderDetailInfo = transportOrderRpc.getDetail(association.getTransportOrderId());
        if (CollectionUtils.isEmpty(transportOrderDetailInfo.getOrderPackages()) || StringUtils.isEmpty(transportOrderDetailInfo.getOrderPackages().get(0).getThirdOrderId())) {
            return;
        }
        TransportOrderPackageDTO transportOrderPackage = transportOrderDetailInfo.getOrderPackages().get(0);
        association.setWmsOrderCode(transportOrderPackage.getThirdOrderId());
        EcCangApiBaseResult<String> getFreightOrderApiBaseResult = eccangWMSApiSve.freightGetOrderInfo(transportOrderPackage.getThirdOrderId(), wmsThirdPartySystem);
        if (getFreightOrderApiBaseResult.getCode().equals("200") && getFreightOrderApiBaseResult.getMessage().equalsIgnoreCase("Success")) {
            List<EcCangFreightGetOrderInfoResponse> responseOrders = JSONObject.parseArray(getFreightOrderApiBaseResult.getData(), EcCangFreightGetOrderInfoResponse.class);
            if (CollectionUtils.isNotEmpty(responseOrders)) {
                responseOrders.forEach(respOrder -> {
                    if (respOrder.order_status == 1 || respOrder.order_status == 2) {  //只有集运订单还是待签收或部分签收状态下才能修改

                        Map<String, String> productMap = respOrder.product_data.stream().filter(Objects::nonNull).collect(Collectors.toMap(
                                object -> {
                                    return object.child_code;
                                },
                                object -> {
                                    return object.package_code;
                                }
                        ));
                        boolean needEdit = false;
                        for (DeliveryOrderItemEcCangApiAssociation item : association.getItems()) {
                            String packageCode = productMap.get(item.getWmsChildCode());
                            if ((StringUtils.isEmpty(packageCode) && StringUtils.isNotEmpty(item.getErpPurchaseOrderTrackingNo()))
                                    || (StringUtils.isNotEmpty(packageCode) && StringUtils.isNotEmpty(item.getErpPurchaseOrderTrackingNo()) && !packageCode.equals(item.getErpPurchaseOrderTrackingNo()))) {
                                needEdit = true;
                                break;
                            }
                        }

                        if (needEdit) {
                            //TODO 重新按运输单维度加载集运订单商品,暂时看不太合理,后续需调整改进
                            Optional<DeliveryOrderEcCangApiAssociation> transportAssociation = deliveryOrderEcCangApiAssociationRepository.loadByTransportOrderId(association.getTransportOrderId());
                            if (transportAssociation.isPresent()) {
                                transportAssociation.get().getItems().forEach(associationItem -> {
                                    Optional<DeliveryOrderEcCangApiAssociation> tempAssociation = deliveryOrderEcCangApiAssociationRepository.getByAssociationId(associationItem.getAssociationId());
                                    if (!tempAssociation.isPresent()) {
                                        return;
                                    }
                                    DeliveryOrderDetailInfo deliveryOrderDetailInfo = deliveryOrderRpc.getDetailInfo(tempAssociation.get().getDeliveryOrderId());
                                    List<TrackInfoDTO> stockoutTrackingInfos = deliveryOrderDetailInfo.getStockoutTrackingInfos();
                                    if (CollectionUtils.isNotEmpty(stockoutTrackingInfos)) {
                                        associationItem.setErpPurchaseOrderTrackingNo(StringUtils.isNotEmpty(stockoutTrackingInfos.get(0).getTrackingNo()) ? stockoutTrackingInfos.get(0).getTrackingNo() : null);
                                    }
                                });

                                EcCangApiBaseResult<String> freightCreateProductApiBaseResult = eccangWMSApiSve.freightCreateProduct(transportAssociation.get(), wmsThirdPartySystem);
                                if (null != freightCreateProductApiBaseResult && freightCreateProductApiBaseResult.getCode().equals("200") && freightCreateProductApiBaseResult.getMessage().equalsIgnoreCase("Success")) {
                                    JSONArray productResult = JSONArray.parseArray(freightCreateProductApiBaseResult.getData());
                                    productResult.forEach(productObj -> {
                                        JSONObject productJson = (JSONObject) productObj;
                                        association.getItems().forEach(item -> {
                                            if (item.getWmsProductPid().equals(productJson.getInteger("pid"))) {
                                                item.setWmsProductPid(productJson.getInteger("pid"));
                                                item.setWmsChildCode(productJson.getString("child_code"));
                                                item.setWmsProductPackageCode(productJson.getString("package_code"));
                                            }
                                        });
                                    });
                                } else {
                                    //出错不处理，等待下次重新创建
                                    log.error("{}更新集运订单产品信息失败{}，数据为空", association.getWmsOrderCode(), freightCreateProductApiBaseResult == null ? "" : freightCreateProductApiBaseResult.getMessage());
                                }
                            } else {
                                log.error("{}运输单关联信息不存在,暂不处理");
                            }
                        } else {
                            respOrder.product_data.forEach(productJson -> {
                                association.getItems().forEach(item -> {
                                    if (item.getWmsChildCode().equals(productJson.child_code)) {
                                        item.setWmsProductPackageCode(productJson.package_code);
                                    }
                                });
                            });
                        }
                    }
                });
            }
        }
    }

    private void checkStockoutThirdPushed(StockoutOrderInfo stockoutOrderInfo,
                                          DeliveryOrderDetailInfo deliveryOrderDetailInfo,
                                          ThirdPartySystem thirdPartySystem,
                                          ApiRequestParams erpCreateProductRequestParams,
                                          ApiRequestParams erpCreateOrderRequestParams) {
        if (!stockoutOrderInfo.getThirdPushed()) {
            //创建易仓ERP产品
            deliveryOrderDetailInfo.items.forEach(item -> {
                        EcCangApiBaseResult<String> syncProductApiBaseResult = eccangERPApiSve.syncProduct(item, thirdPartySystem, erpCreateProductRequestParams, deliveryOrderDetailInfo.getChannelId());
                        if (syncProductApiBaseResult.getCode().equals("200") && syncProductApiBaseResult.getMessage().equals("Success")) {
                            log.info("{}创建易仓ERP产品成功", item.supplierSkuId);
                        } else {
                            log.error("{}创建易仓ERP产品失败{}", item.supplierSkuId, syncProductApiBaseResult.getError());
                        }
                    }
            );

            //创建易仓ERP订单
            EcCangApiBaseResult<String> apiBaseResult = eccangERPApiSve.createOrder(deliveryOrderDetailInfo, thirdPartySystem, erpCreateOrderRequestParams);

            if (apiBaseResult.getCode().equals("200") && apiBaseResult.getMessage().equals("Success")) {
                log.info("{}创建易仓订单成功", stockoutOrderInfo.getStockoutOrderId());
                JSONObject successDataJsonObject = JSONObject.parseObject(apiBaseResult.getData());
                String thirdOrderId = successDataJsonObject.getString("saleOrderCode");

                //第五步 调用出库单ThirdPushed接口
                StockoutOrderThirdPushedCommand stockoutOrderThirdPushedCommand = new StockoutOrderThirdPushedCommand();
                stockoutOrderThirdPushedCommand.setStockoutOrderId(stockoutOrderInfo.getStockoutOrderId());
                stockoutOrderThirdPushedCommand.setThirdOrderId(thirdOrderId);

                try {
                    stockoutOrderRpc.thirdPushed(stockoutOrderThirdPushedCommand);
                    stockoutOrderInfo.setThirdOrderId(thirdOrderId);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("{}调用受理方法出错", stockoutOrderInfo.getStockoutOrderId());
                    //受理失败则作废订单
                    EcCangApiBaseResult<String> cancelOrderApiBaseResult = eccangERPApiSve.cancelOrder(thirdPartySystem, stockoutOrderThirdPushedCommand.getThirdOrderId());
                    log.error("取消订单{}的结果为{}", stockoutOrderThirdPushedCommand.getThirdOrderId(), cancelOrderApiBaseResult.getMessage());
                }
//                createAssociation(deliveryOrderDetailInfo.getTradeOrderId(), erpTpsId, wmsTpsId);
            } else {
                log.error("{}创建易仓ERP订单失败{}", stockoutOrderInfo.getStockoutOrderId(), apiBaseResult.getError());
            }
        }
    }

    @Job("autoCheckStockoutCancellingOrder4Eccang")
    public ReturnT<String> autoCheckStockoutCancellingOrder4Eccang(String param) {
        log.info("定时任务检查在易仓上待取消订单的出库单 - 开始 --- {}", new Date());

        //根据参数获取需要执行的第三方系统id
        JSONObject paramObject = JSONObject.parseObject(param);
        //易仓ERP系统id
        String erpTpsId = paramObject.getString("erpTpsId");
        Asserts.assertNotBlank(erpTpsId, "erp系统id不为空");
        //易仓WMS系统id
        String wmsTpsId = paramObject.getString("wmsTpsId");
        Asserts.assertNotBlank(wmsTpsId, "wms系统id不为空");

        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(erpTpsId);
        EcCangERPParams params = JSON.parseObject(thirdPartySystem.getParams(), EcCangERPParams.class);

        DConcurrentTemplate.tryLockMode(
                AUTO_CHECK_STOCKOUT_CANCELLING_ORDER_ECCANG_PREFIX.concat(param),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    StockoutOrderWithDeliveryQueryCommand queryCommand = new StockoutOrderWithDeliveryQueryCommand();
                    queryCommand.setCancelState(CancelState.CANCELING.name());
                    queryCommand.setDeliveryOrderSupplierId(params.getSupplierId());
                    queryCommand.setDeliveryOrderChannelId(params.getChannelId());
                    //queryCommand.setDeliveryOrderType(OrderType.APO.name());
                    List<StockoutOrderInfo> stockoutOrderInfoList = stockoutOrderRpc.getListWithDelivery(queryCommand);
                    if (CollectionUtils.isNotEmpty(stockoutOrderInfoList)) {
                        int resultSize = stockoutOrderInfoList.size();
                        log.info("获取的待取消状态出库单数量为{}", resultSize);
                        stockoutOrderInfoList.forEach(stockoutOrderInfo -> {
                            Optional<DeliveryOrderEcCangApiAssociation> ecCangApiAssociation = deliveryOrderEcCangApiAssociationRepository.loadByStockoutOrderId(stockoutOrderInfo.getStockoutOrderId());
                            if (ecCangApiAssociation.isPresent()) {
                                DeliveryOrderEcCangApiAssociation association = ecCangApiAssociation.get();
                                try {
                                    if (cancelErpOrder(association, thirdPartySystem)) {
                                        StockoutOrderConfirmCancelCommand command = new StockoutOrderConfirmCancelCommand();
                                        command.stockoutOrderId = stockoutOrderInfo.getStockoutOrderId();
                                        command.cancelType = CancelType.SUPPLIER_CANCEL.name();
                                        stockoutOrderRpc.confirmCancel(command);
                                    } else {
                                        StockoutOrderRejectCancelCommand command = new StockoutOrderRejectCancelCommand();
                                        command.stockoutOrderId = stockoutOrderInfo.getStockoutOrderId();
                                        stockoutOrderRpc.rejectCancel(command);
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    log.error("{}调用取消方法出错", stockoutOrderInfo.getStockoutOrderId());
                                }
                            } else {
                                //提示出错
                                log.error("{}找不到已有关联信息", stockoutOrderInfo.getStockoutOrderId());
                            }
                        });
                    }
                }
        );

        log.info("定时任务检查在易仓上待取消订单的出库单 - 结束 --- {}", new Date());
        return ReturnT.SUCCESS;
    }

    private boolean cancelErpOrder(DeliveryOrderEcCangApiAssociation association, ThirdPartySystem thirdPartySystem) {
        Set<String> erpPurchaseOrderCodeSet = new HashSet<>();
        association.getItems().forEach(item -> {
            erpPurchaseOrderCodeSet.add(item.getErpPurchaseOrderCode());
        });
        EcCangApiBaseResult<String> cancelPurchaseOrderApiBaseResult = eccangERPApiSve.cancelPurchaseOrder(thirdPartySystem, erpPurchaseOrderCodeSet);
        if (cancelPurchaseOrderApiBaseResult.getCode().equals("200") && cancelPurchaseOrderApiBaseResult.getMessage().equals("Success")) {
            EcCangApiBaseResult<String> cancelOrderApiBaseResult = eccangERPApiSve.cancelOrder(thirdPartySystem, association.getErpOrderSaleOrderCode());
            if (cancelOrderApiBaseResult.getCode().equals("200") && cancelOrderApiBaseResult.getMessage().equals("Success")) {
                log.info("{}取消易仓采购单及订单成功", association.getDeliveryOrderId());
                return true;
            } else {
                log.error("{}取消易仓采购单失败{}", association.getDeliveryOrderId(), cancelOrderApiBaseResult.getError());
                return false;
            }
        } else {
            log.error("{}取消易仓采购单失败{}", association.getDeliveryOrderId(), cancelPurchaseOrderApiBaseResult.getError());
            return false;
        }
    }

    private ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }

}
