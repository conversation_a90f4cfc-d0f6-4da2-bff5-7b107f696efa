package com.newnary.gsp.center.tpsi.app.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.common.PageCondition;
import com.newnary.api.base.common.PageList;
import com.newnary.gsp.center.tpsi.app.service.wms.StockSyncMgmtApp;
import com.newnary.gsp.center.wms.api.stock.request.BatchBinLocationStockPageQueryReq;
import com.newnary.gsp.center.wms.api.stock.response.BatchStockRes;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.job.core.handler.annotation.Job;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class WmsStockSyncSupplierSkuJobManager {

    @Resource
    private StockSyncMgmtApp stockSyncMgmtApp;

    private static String WMS_FOR_GSP_JOB_NAME = "WMS同步库存到供应商商品定时任务";

    private final ThreadPoolExecutor processExecutor = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors() * 20,
            Runtime.getRuntime().availableProcessors() * 20,
            20L, TimeUnit.SECONDS,
            new LinkedBlockingDeque<>(100),
            new ThreadPoolExecutor.CallerRunsPolicy());

    @Job("wmsSyncStock2SupplierSkuJob")
    public ReturnT<String> wmsSyncStock2SupplierSkuJob(String param){
        log.info("[{}] 开始执行！", WMS_FOR_GSP_JOB_NAME);
        try {
            // 1. 初始化任务参数
            JSONObject paramObj = JSON.parseObject(param);
            Integer pageSize = paramObj.getInteger("pageSize");
            if(pageSize == null) {
                pageSize = 50;
            }
            List<BatchStockRes> items = null;
            // 2.1. 初始化分页参数
            PageCondition pageCondition = new PageCondition();
            pageCondition.setPageSize(pageSize);
            pageCondition.setPageNum(1);
            // 2.2. 查询待处理任务列表
            BatchBinLocationStockPageQueryReq req = new BatchBinLocationStockPageQueryReq();
            req.setPageCondition(pageCondition);
            PageList<BatchStockRes> batchStockResPageList = stockSyncMgmtApp.queryPageByBatchBLStock(req);
            items = batchStockResPageList.getItems();
            // 2.3. 批量处理
            int reTryCount = 0;
            if(CollectionUtils.isNotEmpty(items)) {
                for(BatchStockRes item : items) {
                    try {
                        stockSyncMgmtApp.syncSupplierSkuStock(item.getSku(),item.getWarehouseId(),item.getShipperId());
                    } catch (Exception e) {
                        log.error("[{}] 处理单条数据异常！skuId={}, e={}", item.getSku(), e);
                    }
                    reTryCount++;
                }
            }
            log.info("[{}] 任务执行完成！共处理数据 reTryCount={}", WMS_FOR_GSP_JOB_NAME, reTryCount);
        } catch (Exception e) {
            log.info("[{}] 任务执行异常！e={}", WMS_FOR_GSP_JOB_NAME, e);
        }
        return ReturnT.SUCCESS;
    }

}
