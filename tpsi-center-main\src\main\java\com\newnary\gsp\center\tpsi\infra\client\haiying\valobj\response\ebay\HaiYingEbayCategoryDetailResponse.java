package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.ebay;

import lombok.Data;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingEbayCategoryDetailResponse {

    /**
     * 类目id
     */
    private String cid;

    /**
     * 类目名称
     */
    private String cname;

    /**
     * 类目层级
     */
    private String level;

    /**
     * 是否叶子类目
     * (0:否   1:是)
     */
    private String is_leaf;

    /**
     * 1级类目id
     */
    private String p_l1_id;

    /**
     * 1级类目名称
     */
    private String p_l1_name;

    /**
     * 2级类目id
     */
    private String p_l2_id;

    /**
     * 2级类目名称
     */
    private String p_l2_name;

    /**
     * 3级类目id
     */
    private String p_l3_id;

    /**
     * 3级类目名称
     */
    private String p_l3_name;

    /**
     * 4级类目id
     */
    private String p_l4_id;

    /**
     * 4级类目名称
     */
    private String p_l4_name;

    /**
     * 5级类目id
     */
    private String p_l5_id;

    /**
     * 5级类目名称
     */
    private String p_l5_name;

    /**
     * 6级类目id
     */
    private String p_l6_id;

    /**
     * 6级类目名称
     */
    private String p_l6_name;

    /**
     * 7级类目id
     */
    private String p_l7_id;

    /**
     * 7级类目名称
     */
    private String p_l7_name;

    /**
     * 有销量的商品总数
     */
    private String product_count;

    /**
     * 店铺总数
     */
    private String merchant_count;

    /**
     * 前1天类目销售件数
     */
    private String sold_the_previous_day;

    /**
     * 前1天类目销售金额
     */
    private String payment_the_previous_day;

    /**
     * 前1天类目销售增幅
     */
    private String sold_the_previous_growth;

    /**
     * 前1-7天类目销售件数
     */
    private String sales_week1;

    /**
     * 前8-14天类目销售件数
     */
    private String sales_week2;

    /**
     * 前1-7天类目销售金额
     */
    private String payment_week1;

    /**
     * 前7天类目销售增幅
     */
    private String sales_week_growth;

    /**
     * 统计时间
     */
    private String stat_time;

    /**
     * 前1-3天类目销售件数
     */
    private String sales_three_day1;

    /**
     * 前4-6天类目销售件数
     */
    private String sales_three_day2;

    /**
     * 前1-3天类目销售金额
     */
    private String payment_three_day1;

    /**
     * 前3天类目销售增幅
     */
    private String sales_three_day_growth;

    /**
     * 商品总数
     */
    private String all_product_count;

    /**
     * 销量为0的商品总数
     */
    private String unsold_product_count;

}
