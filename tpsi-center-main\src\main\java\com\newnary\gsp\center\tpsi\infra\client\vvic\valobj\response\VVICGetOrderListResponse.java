package com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class VVICGetOrderListResponse {

    /**
     * 商品数量
     */
    private Integer total;

    /**
     *页码
     */
    private Integer page;

    /**
     *总页数
     */
    private Integer total_page;

    /**
     *订单列表
     */
    private List<Order> order_list;

    @Getter
    @Setter
    public static class Order {
        /**
         *收货人姓名
         */
        private String consignee;

        /**
         *收货人国家
         */
        private String country;

        /**
         *收货人省份
         */
        private String province;

        /**
         *收货人的所在城市
         */
        private String city;

        /**
         *收货人的所在地区
         */
        private String area;

        /**
         *收货人详细地址
         */
        private String address;

        /**
         *收货人手机号码
         */
        private String mobile;

        /**
         * 收货人固定电话
         */
        private String tel;

        /**
         * 搜款网订单号
         */
        private String order_no;

        /**
         * 第三方订单号
         */
        private String out_order_no;

        /**
         * 订单状态 	支持：待付款“1”；未付款交易关闭“2”；拿货中“3”；配送中“5”；交易成功“6”;已付款交易关闭“8”;部分发货“9”
         */
        private String order_status;

        /**
         * 快递编码
         */
        private String express_code;

        /**
         * 快递号码
         */
        private String express_no;

        /**
         * 快递名称
         */
        private String express_name;

        /**
         * 快递费用
         */
        private String express_fee;

        /**
         * 发货方式 货齐再发，有货先发
         */
        private String express_type;

        /**
         * 有货先发的最迟发货时间
         */
        private String delivery_time;

        /**
         * 发货人姓名
         */
        private String shipper_name;

        /**
         * 发货人手机号码
         */
        private String shipper_mobile;

        /**
         * 拿货方式 目前只有普通拿货
         */
        private String purchase_id;

        /**
         * 拿货费
         */
        private String purchase_fee;

        /**
         * 质检方式 目前有高级打包和普通打包
         */
        private String quality_id;

        /**
         * 质检费
         */
        private String quality_fee;

        /**
         * 打包方式 目前有高级打包和普通打包
         */
        private String packing_id;

        /**
         * 打包费
         */
        private String packing_fee;

        /**
         * 手续费
         */
        private String handling_fee;

        /**
         * 商品总价
         */
        private String goods_amount;

        /**
         * 订单总价 	商品总价+运费+拿货费+质检费+打包费+增值服务费+手续费
         */
        private String order_amount;

        /**
         * 创建时间
         */
        private String create_time;

        /**
         * 更新时间
         */
        private String update_time;

        /**
         * 	发货时间
         */
        private String shipping_time;

        /**
         * 	增值服务费
         */
        private String vas_fee;

        /**
         * 增值服务集合
         */
        private List<Vas> vas_list;

        /**
         * 商品集合
         */
        private List<OrderDetail> order_details;
    }

    @Setter
    @Getter
    public static class Vas {

        /**
         * 增值服务名称
         */
        private String vas_name;

        /**
         * 增值服务数量
         */
        private String vas_num;

        /**
         * 单价
         */
        private String vas_price;

        /**
         *总价
         */
        private String total_amount;

        /**
         * 增值服务Id
         */
        private Long vasId;

        /**
         * 增值服务图片地址
         */
        private String vasImg;

        /**
         * 增值服务类型 1-拿货服务，2-质检服务，4-打包服务，5-增值服务
         */
        private String type;
    }

    @Setter
    @Getter
    public static class OrderDetail {
        /**
         *订单号
         */
        private String order_no;

        /**
         *子订单号（主键）
         */
        private String order_details_id;

        /**
         *商品VID
         */
        private String item_vid;

        /**
         * 商品名称
         */
        private String item_name;

        /**
         * 商品货号
         */
        private String item_sn;

        /**
         * 商品件数
         */
        private String item_num;

        /**
         * 可退数量
         */
        private String return_num;

        /**
         * 款式VID
         */
        private String sku_vid;

        /**
         * 款式单价
         */
        private String sku_price;

        /**
         *颜色
         */
        private String sku_color;

        /**
         * 尺码
         */
        private String sku_size;

        /**
         * 已拿货数量
         */
        private String has_purchase_num;

        /**
         * 缺货数量
         */
        private String not_purchase_num;

        /**
         * 拿货中数量
         */
        private String ing_purchase_num;

        /**
         * 待拿货数量
         */
        private String wait_purchase_num;

        /**
         * 采购状态 0：无，1：待拿货，2：拿货中，3：缺货，4：已拿货 5：配送中 6：已送达 9：已取消
         */
        private Integer delivery_status;

        /**
         * 商品合计 单价X数量
         */
        private String total_amount;
    }


}
