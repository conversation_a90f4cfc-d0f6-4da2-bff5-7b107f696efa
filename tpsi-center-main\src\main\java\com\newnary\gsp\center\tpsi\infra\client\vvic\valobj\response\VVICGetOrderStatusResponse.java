package com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class VVICGetOrderStatusResponse {

    /**
     * 订单状态
     */
    private List<Status> order_status_list;

    @Getter
    @Setter
    public static class Status {
        /**
         * 搜款网订单号
         */
        private String order_no;

        /**
         * 第三方订单号
         */
        private String out_order_no;

        /**
         * 订单状态
         * 支持：待付款“1”；未付款交易关闭“2”；拿货中“3”；配送中“5”；交易成功“6”;已付款交易关闭“8”;部分发货“9”
         */
        private String order_status;

        /**
         * 订单的版本号
         * 用于控制修改订单时多人一起修改
         */
        private String order_version;
    }

}
