package com.newnary.gsp.center.tpsi.service.haiying;

import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.HaiYingDataApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.amazon.HaiYingAmazonCategoryTreeRequest;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.amazon.HaiYingAmazonProductDetailInfoRequest;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.amazon.HaiYingAmazonProductHistoryInfoRequest;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.amazon.HaiYingAmazonProductListRequest;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
public interface IHaiYingDataAmazonApiSve {

    HaiYingDataApiBaseResult<String> getProductList(HaiYingAmazonProductListRequest productListRequest);

    HaiYingDataApiBaseResult<String> getProductDetailInfo(HaiYingAmazonProductDetailInfoRequest productDetailInfoRequest);

    HaiYingDataApiBaseResult<String> getCategoryTree(HaiYingAmazonCategoryTreeRequest categoryTreeRequest);

    HaiYingDataApiBaseResult<String> getProductHistoryInfo(HaiYingAmazonProductHistoryInfoRequest productHistoryInfoRequest);

}
