package com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.request;

import lombok.Data;

@Data
public class CloseOpen1688OrderRequest {

    /**
     * 站点信息，指定调用的API是属于国际站（alibaba）还是1688网站（1688）
     */
    private String webSite;

    /**
     * 交易id，订单号
     */
    private Long tradeID;

    /**
     * 原因描述；buyerCancel:买家取消订单;sellerGoodsLack:卖家库存不足;other:其它
     */
    private String cancelReason;

    /**
     * 备注
     */
    private String remark;
}
