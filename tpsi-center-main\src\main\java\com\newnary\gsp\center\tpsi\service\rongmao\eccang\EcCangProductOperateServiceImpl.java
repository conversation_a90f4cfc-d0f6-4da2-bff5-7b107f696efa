package com.newnary.gsp.center.tpsi.service.rongmao.eccang;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.common.CommonResponse;
import com.newnary.common.utils.locale.LanguageLocaleType;
import com.newnary.gsp.center.product.api.product.response.SupplierSkuInfo;
import com.newnary.gsp.center.product.api.product.vo.SupplierSpuDetailInfo;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp.EcCangERPSyncProductRequest;
import com.newnary.gsp.center.tpsi.infra.model.ApiRequestParams;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.ApiRequestParamsType;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingInfo;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingType;
import com.newnary.gsp.center.tpsi.infra.repository.IApiRequestParamsRepository;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.ThirdPartyMappingManager;
import com.newnary.gsp.center.tpsi.infra.rpc.OpenSupplierProductRpc;
import com.newnary.gsp.center.tpsi.service.eccang.IEccangERPApiSve;
import com.newnary.gsp.center.tpsi.service.rongmao.IProductOperateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Optional;

@Component
@Slf4j
public class EcCangProductOperateServiceImpl implements IProductOperateService {

    @Resource
    private IEccangERPApiSve eccangERPApiSveImpl;

    @Resource
    private OpenSupplierProductRpc openSupplierProductRpc;

    @Resource
    private ThirdPartyMappingManager thirdPartyMappingManager;

    @Resource
    private IApiRequestParamsRepository apiOrderCreateParamsRepository;

    @Override
    public CommonResponse<String> create(ThirdPartySystem thirdPartySystem, String productId) {
        try {
            SupplierSpuDetailInfo detail = openSupplierProductRpc.getDetail(productId);
            Optional<ApiRequestParams> optionalApiRequestParams = apiOrderCreateParamsRepository.loadBySystemBizIdAndType(thirdPartySystem.getBizId(), ApiRequestParamsType.PRODUCT.name());
            ApiRequestParams apiRequestParams = null;
            if (optionalApiRequestParams.isPresent()) {
                apiRequestParams = optionalApiRequestParams.get();
            }
            if (detail != null) {
                if (StringUtils.isNotBlank(detail.getCustomCode())) {
                    ThirdPartyMappingInfo spuTarget = thirdPartyMappingManager.getNewestInfoByTarget("ECCANG", "GSP", detail.getCustomCode(), ThirdPartyMappingType.PRODUCT_ID);
                    detail.getSkuList().forEach(supplierSkuInfo -> {
                        if (supplierSkuInfo.getCustomCode() != null && detail.getCustomCode().equals(supplierSkuInfo.getCustomCode())) {
                            thirdPartyMappingManager.updateSourceId(spuTarget, supplierSkuInfo.getSupplierSkuId());
                        }
                        if (supplierSkuInfo.getCustomCode() == null) {
                            createProduct(thirdPartySystem, detail, supplierSkuInfo);
                        }
                    });
                } else {
                    detail.getSkuList().forEach(supplierSkuInfo -> {
                        if (supplierSkuInfo.getCustomCode() == null) {
                            createProduct(thirdPartySystem, detail, supplierSkuInfo);
                        }
                    });
                }
            }
        } catch (Exception e) {
            log.error("创建易仓商品异常：{}", e.getMessage(), e);
        }
        return null;
    }

    private void createProduct(ThirdPartySystem thirdPartySystem, SupplierSpuDetailInfo detail, SupplierSkuInfo supplierSkuInfo) {
        EcCangERPSyncProductRequest ecCangERPSyncProductRequest = buildCreateProductRequest(detail, supplierSkuInfo);
        EcCangApiBaseResult<String> result = eccangERPApiSveImpl.syncProduct(thirdPartySystem, ecCangERPSyncProductRequest);
        if (result != null) {
            thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", supplierSkuInfo.getSupplierSkuId(), result.getData(), ThirdPartyMappingType.PRODUCT_ID.name(), ecCangERPSyncProductRequest);
        }
    }

    private EcCangERPSyncProductRequest buildCreateProductRequest(SupplierSpuDetailInfo detail, SupplierSkuInfo supplierSkuInfo) {
        EcCangERPSyncProductRequest ecCangERPSyncProductRequest = new EcCangERPSyncProductRequest();
        ecCangERPSyncProductRequest.actionType = "ADD";
        ecCangERPSyncProductRequest.productSku = supplierSkuInfo.supplierSkuId;
        if (CollectionUtils.isNotEmpty(detail.getDescInfos())) {
            detail.getDescInfos().forEach(supplierSpuDescInfo -> {
                if ("zh_cn".equals(LanguageLocaleType.zh_CN.getType())) {
                    ecCangERPSyncProductRequest.productTitle = supplierSpuDescInfo.getTitle();
                }

                if ("en_US".equals(LanguageLocaleType.zh_CN.getType())) {
                    ecCangERPSyncProductRequest.productTitleEn = supplierSpuDescInfo.getTitle();
                }
            });
        }

        //TODO 申报价值和币种

        //TODO 供应商代码和组织机构id

        JSONArray specsArray = new JSONArray();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(supplierSkuInfo.getSpecs())) {
            supplierSkuInfo.getSpecs().forEach(supplierSkuSpecInfo -> {
                JSONObject specsJson = new JSONObject();
                specsJson.put(supplierSkuSpecInfo.getSpecName(), supplierSkuSpecInfo.getSpecValue());
                specsArray.add(specsJson);
            });
        }
        ecCangERPSyncProductRequest.productSpecs = specsArray.toJSONString();

        BigDecimal grossWeight = supplierSkuInfo.grossWeight;
        BigDecimal netWeight = supplierSkuInfo.netWeight;
        BigDecimal sizeHeight = supplierSkuInfo.sizeHeight;
        BigDecimal sizeLength = supplierSkuInfo.sizeLength;
        BigDecimal sizeWidth = supplierSkuInfo.sizeWidth;
        BigDecimal packingHeight = supplierSkuInfo.packingHeight;
        BigDecimal packingLength = supplierSkuInfo.packingLength;
        BigDecimal packingWidth = supplierSkuInfo.packingWidth;
        switch (judgeSizeInfo(grossWeight, netWeight)) {
            case 1:
                ecCangERPSyncProductRequest.productWeight = grossWeight.doubleValue();
                ecCangERPSyncProductRequest.pdNetWeight = netWeight.doubleValue();
                break;
            case 2:
                ecCangERPSyncProductRequest.productWeight = netWeight.doubleValue();
                ecCangERPSyncProductRequest.pdNetWeight = netWeight.doubleValue();
                break;
            case 3:
                ecCangERPSyncProductRequest.productWeight = grossWeight.doubleValue();
                ecCangERPSyncProductRequest.pdNetWeight = grossWeight.doubleValue();
                break;
            default:
                ecCangERPSyncProductRequest.productWeight = 1.00;
                ecCangERPSyncProductRequest.pdNetWeight = 1.00;
        }

        switch (judgeSizeInfo(sizeHeight, packingHeight)) {
            case 1:
                ecCangERPSyncProductRequest.productHeight = packingHeight.multiply(new BigDecimal("100")).doubleValue();
                ecCangERPSyncProductRequest.pdNetHeight = sizeHeight.multiply(new BigDecimal("100")).doubleValue();
                break;
            case 2:
                ecCangERPSyncProductRequest.productHeight = packingHeight.multiply(new BigDecimal("100")).doubleValue();
                ecCangERPSyncProductRequest.pdNetHeight = packingHeight.multiply(new BigDecimal("100")).doubleValue();
                break;
            case 3:
                ecCangERPSyncProductRequest.productHeight = sizeHeight.multiply(new BigDecimal("100")).doubleValue();
                ecCangERPSyncProductRequest.pdNetHeight = sizeHeight.multiply(new BigDecimal("100")).doubleValue();
                break;
            default:
                ecCangERPSyncProductRequest.productHeight = 1.00;
                ecCangERPSyncProductRequest.pdNetHeight = 1.00;

        }
        switch (judgeSizeInfo(sizeLength, packingLength)) {
            case 1:
                ecCangERPSyncProductRequest.productLength = packingLength.multiply(new BigDecimal("100")).doubleValue();
                ecCangERPSyncProductRequest.pdNetLength = sizeLength.multiply(new BigDecimal("100")).doubleValue();
                break;
            case 2:
                ecCangERPSyncProductRequest.productLength = packingHeight.multiply(new BigDecimal("100")).doubleValue();
                ecCangERPSyncProductRequest.pdNetLength = packingHeight.multiply(new BigDecimal("100")).doubleValue();
                break;
            case 3:
                ecCangERPSyncProductRequest.productLength = sizeHeight.multiply(new BigDecimal("100")).doubleValue();
                ecCangERPSyncProductRequest.pdNetLength = sizeHeight.multiply(new BigDecimal("100")).doubleValue();
                break;
            default:
                ecCangERPSyncProductRequest.productLength = 1.00;
                ecCangERPSyncProductRequest.pdNetLength = 1.00;

        }
        switch (judgeSizeInfo(sizeWidth, packingWidth)) {
            case 1:
                ecCangERPSyncProductRequest.productWidth = packingWidth.multiply(new BigDecimal("100")).doubleValue();
                ecCangERPSyncProductRequest.pdNetWidth = sizeWidth.multiply(new BigDecimal("100")).doubleValue();
                break;
            case 2:
                ecCangERPSyncProductRequest.productWidth = packingWidth.multiply(new BigDecimal("100")).doubleValue();
                ecCangERPSyncProductRequest.pdNetWidth = packingWidth.multiply(new BigDecimal("100")).doubleValue();
                break;
            case 3:
                ecCangERPSyncProductRequest.productWidth = sizeWidth.multiply(new BigDecimal("100")).doubleValue();
                ecCangERPSyncProductRequest.pdNetWidth = sizeWidth.multiply(new BigDecimal("100")).doubleValue();
                break;
            default:
                ecCangERPSyncProductRequest.productWidth = 1.00;
                ecCangERPSyncProductRequest.pdNetWidth = 1.00;
        }
        return ecCangERPSyncProductRequest;
    }

    //判断尺寸信息
    public int judgeSizeInfo(BigDecimal size1, BigDecimal size2) {
        int i = 0;
        if ((ObjectUtils.isNotEmpty(size1) && size1.doubleValue() > 0.00) && (ObjectUtils.isNotEmpty(size2) && size2.doubleValue() > 0.00)) {
            i = 1;
        } else if ((ObjectUtils.isEmpty(size1) || size1.doubleValue() <= 0.00) && (ObjectUtils.isNotEmpty(size2) && size2.doubleValue() > 0.00)) {
            i = 2;
        } else if ((ObjectUtils.isNotEmpty(size1) && size1.doubleValue() > 0.00) && (ObjectUtils.isEmpty(size2) || size2.doubleValue() <= 0.00)) {
            i = 3;
        }
        return i;
    }

    @Override
    public CommonResponse<String> delete(ThirdPartySystem thirdPartySystem, String productId) {
        //ThirdPartyMappingInfo newestInfoByTarget = thirdPartyMappingManager.getNewestInfoByTarget("ECCANG", "GSP", productId, ThirdPartyMappingType.PRODUCT_ID);
        return null;
    }

    @Override
    public CommonResponse<String> update(ThirdPartySystem thirdPartySystem, String productId) {
        //ThirdPartyMappingInfo newestInfoByTarget = thirdPartyMappingManager.getNewestInfoByTarget("ECCANG", "GSP", productId, ThirdPartyMappingType.PRODUCT_ID);
        SupplierSpuDetailInfo detail = openSupplierProductRpc.getDetail(productId);
        Optional<ApiRequestParams> optionalApiRequestParams = apiOrderCreateParamsRepository.loadBySystemBizIdAndType(thirdPartySystem.getBizId(), ApiRequestParamsType.PRODUCT.name());
        ApiRequestParams apiRequestParams = null;
        if (optionalApiRequestParams.isPresent()) {
            apiRequestParams = optionalApiRequestParams.get();
        }
        if (detail != null && CollectionUtils.isNotEmpty(detail.getSkuList())) {
            for (SupplierSkuInfo supplierSkuInfo : detail.getSkuList()) {
                EcCangERPSyncProductRequest ecCangERPSyncProductRequest = buildCreateProductRequest(detail, supplierSkuInfo);
                EcCangApiBaseResult<String> result = eccangERPApiSveImpl.syncProduct(thirdPartySystem, ecCangERPSyncProductRequest);
                if (result != null) {
                    thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", supplierSkuInfo.getSupplierSkuId(), result.getData(), ThirdPartyMappingType.PRODUCT_ID.name(), ecCangERPSyncProductRequest);
                }
            }
        }
        return CommonResponse.success("商品更新成功");
    }
}
