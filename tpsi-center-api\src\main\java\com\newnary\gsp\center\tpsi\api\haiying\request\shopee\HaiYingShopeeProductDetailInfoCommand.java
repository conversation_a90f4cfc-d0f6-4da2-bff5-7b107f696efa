package com.newnary.gsp.center.tpsi.api.haiying.request.shopee;

import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingStation;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingShopeeProductDetailInfoCommand {

    /**
     * 站点(默认:马来西亚站)
     */
    @NotNull(message = "站点不能为空")
    private HaiYingStation station;

    /**
     * 商品id和店铺id(string型)
     * (以指定json数组的格式，单次最多100个pid和shop_id组合)
     * (如：[{"pid":"xxx","shop_id":"xxx"}])
     */
    @NotNull(message = "商品id和店铺id不能为空")
    private List<HaiYingShopeeShopProductID> pid_and_shop_ids;

}
