package com.newnary.gsp.center.tpsi.service.mabang.impl;

import com.alibaba.fastjson.JSON;
import com.newnary.gsp.center.tpsi.api.mabang.response.MaBangLogisticsChannelList;
import com.newnary.gsp.center.tpsi.infra.client.mabang.MaBangGWApiClient;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.MaBangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.wl.MaBangWlGetMylogisticschannel;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.mq.producer.ApiDockingProducer;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.service.impl.SystemClientSve;
import com.newnary.gsp.center.tpsi.service.mabang.IMaBangWlApiSve;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class MaBangWlApiSveImpl extends SystemClientSve implements IMaBangWlApiSve {

    public static final Logger LOGGER = LoggerFactory.getLogger(MaBangWlApiSveImpl.class);

    @Resource
    private ApiDockingProducer apiDockingProducer;

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Override
    public MaBangLogisticsChannelList wlGetMylogisticschannel(String thirdPartySystemId) {
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        //获取apiClient
        MaBangGWApiClient maBangGWApiClient = getClient(thirdPartySystem.getParams());

        MaBangWlGetMylogisticschannel maBangWlGetMylogisticschannel = new MaBangWlGetMylogisticschannel();

        //调用马帮获取物流渠道列表
        MaBangApiBaseResult<String> ret = maBangGWApiClient.wlGetMylogisticschannel(maBangWlGetMylogisticschannel);
        //返回结果
        MaBangLogisticsChannelList maBangLogisticsChannelList = JSON.parseObject(ret.getData(), MaBangLogisticsChannelList.class);
        return maBangLogisticsChannelList;

    }

    private MaBangGWApiClient getClient(String maBangParams) {
        return new MaBangGWApiClient(maBangParams);
    }

}
