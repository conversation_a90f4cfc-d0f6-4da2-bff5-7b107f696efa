package com.newnary.gsp.center.tpsi.infra.client.pgl.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


@Data
public class GeneratePGLPdfParam {

    /**
     * 订单号
     */
    private String orderNum;

    /**
     * 发件人名称
     */
    private String senderName;

    /**
     * 发件人电话
     */
    private String senderPhone;

    /**
     * 发件人地址
     */
    private String senderAddress;

    /**
     * 收件人名称
     */
    private String receiverName;

    /**
     * 收件人电话
     */
    private String receiverPhone;

    /**
     * 收件人地址
     */
    private String receiverAddress;

    /**
     * 收件人邮编
     */
    private String receiverPostcode;

    /**
     * 物流产品代码
     */
    private String logisticsCode;

    /**
     * 包裹数
     */
    private String pcs;

    /**
     * 是否COD
     */
    private Boolean isCod;

    /**
     * 运输单金额
     */
    private BigDecimal transportPrice;

    /**
     * 商品明细
     */
    private List<GoodsParam> goodsList;

}
