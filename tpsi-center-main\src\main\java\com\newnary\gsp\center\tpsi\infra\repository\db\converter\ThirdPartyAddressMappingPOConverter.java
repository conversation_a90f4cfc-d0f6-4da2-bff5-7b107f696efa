package com.newnary.gsp.center.tpsi.infra.repository.db.converter;

import com.newnary.dao.base.converter.POConverter;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartyAddressMapping;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyAddressMappingPO;

/**
 * <AUTHOR>
 * @Date 2021/12/14 14:30
 */
public class ThirdPartyAddressMappingPOConverter implements POConverter<ThirdPartyAddressMappingPO, ThirdPartyAddressMapping> {
    @Override
    public ThirdPartyAddressMappingPO convert2PO(ThirdPartyAddressMapping domain) {
        ThirdPartyAddressMappingPO po = new ThirdPartyAddressMappingPO();
        po.setId(domain.getId());
        po.setSystemId(domain.getSystemId().getId());
        po.setVirtualPostcode(domain.getVirtualPostcode());
        po.setPostCode(domain.getPostCode());
        po.setCountry(domain.getCountry());
        po.setProvince(domain.getProvince());
        po.setCity(domain.getCity());
        po.setArea(domain.getArea());
        po.setTown(domain.getTown());
        po.setAddress(domain.getAddress());
        return po;
    }
}
