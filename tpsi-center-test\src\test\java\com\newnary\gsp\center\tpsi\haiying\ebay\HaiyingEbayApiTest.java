package com.newnary.gsp.center.tpsi.haiying.ebay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.common.PageCondition;
import com.newnary.api.base.common.PageList;
import com.newnary.api.base.common.PageMeta;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingStation;
import com.newnary.gsp.center.tpsi.api.haiying.request.ebay.HaiYingEbayProductListCommand;
import com.newnary.gsp.center.tpsi.api.haiying.request.lazada.HaiYingLazadaProductListCommand;
import com.newnary.gsp.center.tpsi.api.haiying.response.ebay.HaiYingEbayProductListDTO;
import com.newnary.gsp.center.tpsi.ctrl.haiying.HaiYingEbayCommand2RequestTranslator;
import com.newnary.gsp.center.tpsi.ctrl.haiying.HaiYingEbayResponse2DTOTranslator;
import com.newnary.gsp.center.tpsi.ctrl.haiying.HaiYingLazadaCommand2RequestTranslator;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.HaiYingDataApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.ebay.*;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.ebay.HaiYingEbayProductListResponse;
import com.newnary.gsp.center.tpsi.service.haiying.IHaiYingDataEbayApiSve;
import com.newnary.test.starter.BaseTestInjectTenant;
import org.junit.Test;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
public class HaiyingEbayApiTest extends BaseTestInjectTenant {

    private static final SimpleDateFormat SIMPLE_TIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    protected String tenantId() {
        return "TENANT3924117688555169382400";
    }

    @Resource
    private IHaiYingDataEbayApiSve haiYingEbayDataApiSve;

    @Test
    public void testEbayProductList() throws ParseException {
        HaiYingEbayProductListCommand command = new HaiYingEbayProductListCommand();
        command.setStation(HaiYingStation.EBAY_UK);
        command.setPageCondition(new PageCondition(1, 10));
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingEbayDataApiSve.getProductList(HaiYingEbayCommand2RequestTranslator.transEbayProductList(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingEbayProductListResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingEbayProductListResponse.class);
            PageList<HaiYingEbayProductListDTO> ret = HaiYingEbayResponse2DTOTranslator.transEbayProductListList(command.getStation(), responseList, getResultPageMeta(command.getPageCondition(), apiBaseResult));
            System.out.println(JSON.toJSONString(ret));
        }
    }

    @Test
    public void testEbayProductDetailInfo() {
        HaiYingEbayProductDetailInfoRequest request = new HaiYingEbayProductDetailInfoRequest();
        request.setStation(HaiYingStation.EBAY_UK.getSite());
        request.setItem_ids("223101689219,131466236414,261183138218,151778203614,112435931865,322908956611,304256723768,165288397665,334421630454,255050786073");
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingEbayDataApiSve.getProductDetailInfo(request);
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
//            List<HaiYingEbayProductDetailInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingEbayProductDetailInfoResponse.class);
//            List<HaiYingEbayProductDetailInfoDTO> ret = HaiYingResponse2DTOTranslator.transEbayProductDetailInfoList(responseList);
//            System.out.println(JSON.toJSONString(ret));
        }
    }

    @Test
    public void testEbayCategoryTree() {
        HaiYingEbayCategoryTreeRequest request = new HaiYingEbayCategoryTreeRequest();
        request.setStation(HaiYingStation.EBAY_UK.getSite());
        //request.setLevel(1);
        request.setCid("9800");
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingEbayDataApiSve.getCategoryTree(request);
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
//            List<HaiYingEbayCategoryTreeResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingEbayCategoryTreeResponse.class);
//            List<HaiYingEbayCategoryTreeDTO> ret = HaiYingResponse2DTOTranslator.transEbayCategoryTreeList(responseList);
//            System.out.println(JSON.toJSONString(ret));
        }
    }

    @Test
    public void testEbayTopCategoryInfo() {
        HaiYingEbayTopCategoryInfoRequest request = new HaiYingEbayTopCategoryInfoRequest();
        request.setStation(HaiYingStation.EBAY_UK.getSite());
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingEbayDataApiSve.getTopCategoryInfo(request);
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
//            List<HaiYingEbayCategoryInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingEbayCategoryInfoResponse.class);
//            List<HaiYingEbayTopCategoryInfoDTO> ret = HaiYingResponse2DTOTranslator.transEbayTopCategoryInfoList(responseList);
//            System.out.println(JSON.toJSONString(ret));
        }
    }

    @Test
    public void testEbayCategoryDetail() throws ParseException {
        HaiYingEbayCategoryDetailRequest request = new HaiYingEbayCategoryDetailRequest();
        request.setStation(HaiYingStation.EBAY_UK.getSite());
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingEbayDataApiSve.getCategoryDetail(request);
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
//            List<HaiYingEbayCategoryInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingEbayCategoryInfoResponse.class);
//            PageList<HaiYingEbaySubCategoryInfoDTO> ret = HaiYingResponse2DTOTranslator.transEbaySubCategoryInfoList(responseList, getResultPageMeta(Integer.valueOf(request.getCurrent_page()), apiBaseResult));
//            System.out.println(JSON.toJSONString(ret));
        }
    }

    private PageMeta getResultPageMeta(PageCondition pageCondition, HaiYingDataApiBaseResult<String> apiBaseResult) {
        if (apiBaseResult.getTotalSize() > 0) {
            PageMeta pageMeta = new PageMeta();
            if (null != pageCondition) {
                pageMeta.pageNum = pageCondition.pageNum;
            } else {
                pageMeta.pageNum = 1;
            }
            pageMeta.pageSize = apiBaseResult.getSize();
            if (apiBaseResult.getTotalSize() % apiBaseResult.getSize() == 0) {
                pageMeta.pages = apiBaseResult.getTotalSize() / apiBaseResult.getSize();
            } else {
                pageMeta.pages = (apiBaseResult.getTotalSize() / apiBaseResult.getSize()) + 1;
            }
            pageMeta.total = apiBaseResult.getTotalSize();
            return pageMeta;
        }
        return null;
    }

}
