package com.newnary.gsp.center.tpsi.infra.repository;

import com.newnary.gsp.center.tpsi.infra.mapper.DeliveryOrderEcCangApiAssociationMapper;
import com.newnary.gsp.center.tpsi.infra.mapper.DeliveryOrderItemEcCangApiAssociationMapper;
import com.newnary.gsp.center.tpsi.infra.model.DeliveryOrderEcCangApiAssociation;
import com.newnary.gsp.center.tpsi.infra.model.creator.DeliveryOrderEcCangApiAssociationCreator;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.DeliveryOrderEcCangApiAssociationManager;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.DeliveryOrderItemEcCangApiAssociationManager;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.DeliveryOrderEcCangApiAssociationPO;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.DeliveryOrderItemEcCangApiAssociationPO;
import com.newnary.gsp.center.tpsi.infra.translator.DeliveryOrderEcCangApiAssociationTranslator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class DeliveryOrderEcCangApiAssociationRepository implements IDeliveryOrderEcCangApiAssociationRepository {

    @Resource
    private DeliveryOrderEcCangApiAssociationManager deliveryOrderEcCangApiAssociationManager;

    @Resource
    private DeliveryOrderItemEcCangApiAssociationManager deliveryOrderItemEcCangApiAssociationManager;

    @Override
    public Optional<DeliveryOrderEcCangApiAssociation> getByAssociationId(String associationId) {
        if (StringUtils.isEmpty(associationId)) {
            return Optional.empty();
        }

        // 1. 查询出库单易仓关联主表信息
        DeliveryOrderEcCangApiAssociationPO associationPO = deliveryOrderEcCangApiAssociationManager.getByAssociationId(associationId);
        if (associationPO == null) {
            return Optional.empty();
        }

        // 2. 查询出库单易仓关联明细信息
        List<DeliveryOrderItemEcCangApiAssociationPO> itemPOs = deliveryOrderItemEcCangApiAssociationManager.getByParentId(associationPO.getAssociationId());

        // 3. 构造领域creator
        DeliveryOrderEcCangApiAssociationCreator creator = DeliveryOrderEcCangApiAssociationTranslator.transCreator(
                associationPO,
                itemPOs
        );

        return Optional.of(
                DeliveryOrderEcCangApiAssociation.loadWith(
                        associationPO.getId(),
                        creator
                )
        );
    }


    @Override
    public Optional<DeliveryOrderEcCangApiAssociation> loadByDeliveryOrderId(String deliveryOrderId) {
        if (StringUtils.isEmpty(deliveryOrderId)) {
            return Optional.empty();
        }

        // 1. 查询出库单易仓关联主表信息
        DeliveryOrderEcCangApiAssociationPO associationPO = deliveryOrderEcCangApiAssociationManager.getByDeliveryOrderId(deliveryOrderId);
        if (associationPO == null) {
            return Optional.empty();
        }

        // 2. 查询出库单易仓关联明细信息
        List<DeliveryOrderItemEcCangApiAssociationPO> itemPOs = deliveryOrderItemEcCangApiAssociationManager.getByParentId(associationPO.getAssociationId());

        // 3. 构造领域creator
        DeliveryOrderEcCangApiAssociationCreator creator = DeliveryOrderEcCangApiAssociationTranslator.transCreator(
                associationPO,
                itemPOs
        );

        return Optional.of(
                DeliveryOrderEcCangApiAssociation.loadWith(
                        associationPO.getId(),
                        creator
                )
        );
    }

    @Override
    public Optional<DeliveryOrderEcCangApiAssociation> loadByStockoutOrderId(String stockoutOrderId) {
        if (StringUtils.isEmpty(stockoutOrderId)) {
            return Optional.empty();
        }

        // 1. 查询出库单易仓关联主表信息
        DeliveryOrderEcCangApiAssociationPO associationPO = deliveryOrderEcCangApiAssociationManager.getByStockoutOrderId(stockoutOrderId);
        if (associationPO == null) {
            return Optional.empty();
        }

        // 2. 查询出库单易仓关联明细信息
        List<DeliveryOrderItemEcCangApiAssociationPO> itemPOs = deliveryOrderItemEcCangApiAssociationManager.getByParentId(associationPO.getAssociationId());

        // 3. 构造领域creator
        DeliveryOrderEcCangApiAssociationCreator creator = DeliveryOrderEcCangApiAssociationTranslator.transCreator(
                associationPO,
                itemPOs
        );

        return Optional.of(
                DeliveryOrderEcCangApiAssociation.loadWith(
                        associationPO.getId(),
                        creator
                )
        );
    }

    @Override
    public Optional<DeliveryOrderEcCangApiAssociation> loadByTransportOrderId(String transportOrderId) {
        if (StringUtils.isEmpty(transportOrderId)) {
            return Optional.empty();
        }

        // 1. 查询出库单易仓关联主表信息
        List<DeliveryOrderEcCangApiAssociationPO> associationPOs = deliveryOrderEcCangApiAssociationManager.getByTransportOrderId(transportOrderId);
        if (CollectionUtils.isEmpty(associationPOs)) {
            return Optional.empty();
        }

        // 2. 查询出库单易仓关联明细信息
        List<DeliveryOrderItemEcCangApiAssociationPO> itemPOs = new ArrayList<>();
        associationPOs.forEach(associationPO -> {
            itemPOs.addAll(deliveryOrderItemEcCangApiAssociationManager.getByParentId(associationPO.getAssociationId()));
        });

        // 3. 构造领域creator
        DeliveryOrderEcCangApiAssociationCreator creator = DeliveryOrderEcCangApiAssociationTranslator.transCreator(
                associationPOs.get(0),
                itemPOs
        );

        return Optional.of(
                DeliveryOrderEcCangApiAssociation.loadWith(
                        associationPOs.get(0).getId(),
                        creator
                )
        );
    }

    @Transactional
    @Override
    public void store(DeliveryOrderEcCangApiAssociation association) {
        // 1. 保存主表信息
        DeliveryOrderEcCangApiAssociationPO associationPO = DeliveryOrderEcCangApiAssociationMapper.INSTANCE.model2PO(association);
        if (associationPO.id != null) {
            deliveryOrderEcCangApiAssociationManager.getDao().update(associationPO);

            deliveryOrderItemEcCangApiAssociationManager.deleteByParentId(association.getAssociationId().getId());
        } else {
            deliveryOrderEcCangApiAssociationManager.getDao().insert(associationPO);
        }

        // 2. 保存明细信息
        association.getItems().forEach(item -> {
            item.setAssociationId(associationPO.getAssociationId());
            deliveryOrderItemEcCangApiAssociationManager.getDao().insert(DeliveryOrderItemEcCangApiAssociationMapper.INSTANCE.model2Po(item));
        });
    }

}
