package com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class VVICGetVasResponse {
    /**
     * 发货类型：0-货齐再发，1-有货先发
     */
    private List<Integer> express_type;

    /**
     * 有货先发可选发货时间列表
     */
    private ExpressLeastTime express_least_time;

    /**
     * 	代发服务集合
     */
    private List<Vas> vas_list;

    @Getter
    @Setter
    public static class ExpressLeastTime {
        /**
         * 推荐发货时间
         */
        private String recommend_time;

        /**
         * 默认返货时间
         */
        private String default_time;

        /**
         * 错误提示信息
         */
        private String error_message;

        /**
         * 错误码
         */
        private Integer error_code;

        /**
         * 可选发货时间集合
         */
        private List<DeliveryTime> delivery_time_list;

    }

    @Getter
    @Setter
    public static class DeliveryTime {

        /**
         * 发货日期
         */
        private String delivery_time;

        /**
         * 延迟天数
         */
        private Integer delay_days;
    }

    @Getter
    @Setter
    public static class Vas {
        /**
         * 本类型的服务列表
         */
        private List<VasEntry> vas_entry_list;

        /**
         * 推荐的服务方式
         */
        private String recommend_vas_id;

        /**
         * 默认服务方式
         */
        private String default_vas_id;

        /**
         * 本服务的查询结果状态
         */
        private Integer status_code;

        /**
         * 查询结果状态说明
         */
        private String status_msg;

        /**
         * 类型
         */
        private Integer type;

    }

    @Getter
    @Setter
    public static class VasEntry {
        /**
         * 代发服务ID
         */
        private Integer vas_id;

        /**
         * 服务名称
         */
        private String vas_name;

        /**
         * 图片
         */
        private String vas_img;

        /**
         * 	价格
         */
        private Double price;

    }

}
