package com.newnary.gsp.center.tpsi.api.haiying.response.ebay;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingEbayProductListDTO {

    /**
     * 商品id
     */
    private String item_id;

    /**
     * 商品主图
     */
    private String main_image;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品类目id结构(多个,号分隔)
     */
    private List<String> cids;

    /**
     * 商品价格
     */
    private BigDecimal price;

    /**
     * 商品总销售件数
     */
    private BigDecimal sold;

    /**
     * 商品前1天销售件数
     */
    private BigDecimal sold_the_previous_day;

    /**
     * 商品前1天销售金额
     */
    private BigDecimal payment_the_previous_day;

    /**
     * 商品前1天销售增幅
     */
    private BigDecimal sold_the_previous_growth;

    /**
     * 商品前1-7天销售件数
     */
    private BigDecimal sales_week1;

    /**
     * 商品前8-14天销售件数
     */
    private BigDecimal sales_week2;

    /**
     * 商品前7天销售增幅
     */
    private BigDecimal sales_week_growth;

    /**
     * 商品前1-7天销售金额
     */
    private BigDecimal payment_week1;

    /**
     * 商品前8-14天销售金额
     */
    private BigDecimal payment_week2;

    /**
     * 商品发货地(Item地点)
     */
    private String item_location;

    /**
     * 商品收藏数
     */
    private BigDecimal watchers;

    /**
     * 商品最新抓取时间
     */
    private Long last_modi_time;

    /**
     * 商品统计时间
     */
    private Long stat_time;

    /**
     * 商品上架时间
     */
    private Long gen_time;

    /**
     * 卖家名称
     */
    private String seller;

    /**
     * 店铺名称
     */
    private String store;

    /**
     * 店铺位置
     */
    private String store_location;

    /**
     * 类目结构(多个以<br/>分隔)
     */
    private List<String> category_structure;

    /**
     * 商品前1-3天销售件数
     */
    private BigDecimal sales_three_day1;

    /**
     * 商品前4-6天销售件数
     */
    private BigDecimal sales_three_day2;

    /**
     * 商品前3天销售增幅
     */
    private BigDecimal sales_three_day_growth;

    /**
     * 商品前1-3天销售金额
     */
    private BigDecimal payment_three_day1;

    /**
     * 商品前4-6天销售金额
     */
    private BigDecimal payment_three_day2;

    /**
     * 商品总浏览数
     * (null: 暂无)
     */
    private BigDecimal visit;

    /**
     * 前三天是否连续出单
     * (0: 否   1: 是)
     */
    private Boolean sales_three_day_flag;

    /**
     * ebay商品链接
     */
    private String item_url;

    /**
     * ebay店铺链接
     */
    private String seller_url;

    /**
     * 商品上架地区
     */
    private String marketplace;

    /**
     * 商品小火苗
     * (null:  无小火苗标识
     * 其它: 有小火苗标识)
     */
    private String popular;

    /**
     * 商品描述(已取消,预留)
     */
    @Deprecated
    private String description;

    /**
     * 商品主副图
     * (多张图片以逗号分隔)(已取消,预留)
     */
    @Deprecated
    private List<String> main_images;

    /**
     * 商品品牌
     */
    private String brand;

    /**
     * 货币
     */
    private String currency;

}
