package com.newnary.gsp.center.tpsi.api.externalproduct.vo;

import lombok.Data;

/**
 * 外部系统商品公共响应格式
 *
 * <AUTHOR>
 * @since Created on 2022-05-11
 **/
@Data
public class ExternalProductLiteInfo {

    /**
     * 外部系统商品ID(必须)
     **/
    private String externalProductId;

    /**
     * 商品标题(必须)
     **/
    private String subject;

    /**
     * 主图链接(必须)
     **/
    private String mainImageUrl;

    /**
     * 商品详情链接(外部系统)(必须)
     **/
    private String detailUrl;

    /**
     * 起批数量
     **/
    private Long quantityBegin;

    /**
     * 单位
     **/
    private String unit;

    /**
     * 销售价格(必须)
     **/
    private String salePrice;

    /**
     * 销售价格币种(必须)
     **/
    private String salePriceCurrency;

    /**
     * 库存数
     **/
    private Long stockNum;

    /**
     * 卖家所在省
     **/
    private String province;

    /**
     * 卖家所在市
     **/
    private String city;

}
