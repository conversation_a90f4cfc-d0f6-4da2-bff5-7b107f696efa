package com.newnary.gsp.center.tpsi.api.haiying.response.shopee;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingShopeeProductHistoryDataInfoDTO {

    /**
     * 商品历史销量
     */
    private Integer historical_sold;

    /**
     * 商品总收藏
     */
    private Integer favorite;

    /**
     * 最新抓取时间
     */
    private Long insert_time;

    /**
     * 商品浏览数
     */
    private Integer view_count;

    /**
     * 商品库存数
     */
    private Integer stock;

    /**
     * 商品评分总数
     */
    private Integer rating_count;

    /**
     * 商品最低价格
     */
    private BigDecimal min_price;

    /**
     * 商品最高价格
     */
    private BigDecimal max_price;

    /**
     * 商品默认价格
     */
    private BigDecimal price;

    /**
     * 商品月销量
     */
    private Integer sold;

}
