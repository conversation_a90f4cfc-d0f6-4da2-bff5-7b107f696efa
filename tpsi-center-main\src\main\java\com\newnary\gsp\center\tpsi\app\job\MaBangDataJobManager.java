package com.newnary.gsp.center.tpsi.app.job;

import com.alibaba.fastjson.JSONObject;
import com.newnary.distributed.tools.concurrent.template.DConcurrentTemplate;
import com.newnary.gsp.center.tpsi.api.mabang.request.SyncProductFromMaBangCommand;
import com.newnary.gsp.center.tpsi.api.mabang.request.SyncProductPriceFromMaBangCommand;
import com.newnary.gsp.center.tpsi.api.mabang.request.SyncStockQuantityFromMaBangCommand;
import com.newnary.gsp.center.tpsi.service.ISyncProductBizSve;
import com.newnary.gsp.center.tpsi.service.ISyncStockBizSve;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.job.core.handler.annotation.Job;
import com.newnary.spring.cloud.domain.Asserts;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class MaBangDataJobManager {

    private static final String DO_SYNC_MABANG_PRODUCT_JOB_PREFIX = "DO_SYNC_MABANG_PRODUCT_JOB";

    private static final String DO_SYNC_MABANG_STOCK_JOB_PREFIX = "DO_SYNC_MABANG_STOCK_JOB";

    private static final String DO_SYNC_MABANG_PRODUCT_PRICE_JOB_PREFIX = "DO_SYNC_MABANG_PRODUCT_PRICE_JOB";

    @Resource
    private ISyncProductBizSve syncProductBizSve;

    @Resource
    private ISyncStockBizSve syncStockBizSve;

    /**
     * 同步马帮商品
     *
     * @param param 任务参数
     * @return ReturnT<String>
     */
    @Job("autoDoSyncMaBangProduct")
    public ReturnT<String> autoDoSyncMaBangProduct(String param) {
        log.info("[同步马帮商品] 定时任务 - 开始 --- {}", new Date());

        try {
            JSONObject paramObject = JSONObject.parseObject(param);
            String thirdPartySystemId = paramObject.getString("thirdPartySystemId");
            Asserts.assertNotBlank(thirdPartySystemId, "第三方系统id不为空");
            Integer spuStartPage = paramObject.getInteger("spuStartPage");
            Integer spuEndPage = paramObject.getInteger("spuEndPage");
            Integer skuStartPage = paramObject.getInteger("skuStartPage");
            Integer skuEndPage = paramObject.getInteger("skuEndPage");
            String appointSpu = paramObject.getString("appointSpu");
            String appointSku = paramObject.getString("appointSku");
            Integer pageSize = paramObject.getInteger("pageSize");

            SyncProductFromMaBangCommand req = new SyncProductFromMaBangCommand();
            req.setThirdPartySystemId(thirdPartySystemId);
/*            Set<String> categoryNameSet = new HashSet<>();
            categoryNameSet.add("女上装");
            categoryNameSet.add("女下装");
            categoryNameSet.add("女套装");
            categoryNameSet.add("服配--内衣");
            categoryNameSet.add("男上装");
            categoryNameSet.add("男下装");
            categoryNameSet.add("男套装");
            req.setCategoryNameSet(categoryNameSet);*/
            req.setSpuStartPage(spuStartPage);
            req.setSpuEndPage(spuEndPage);
            req.setSkuStartPage(skuStartPage);
            req.setSkuEndPage(skuEndPage);
            req.setAppointSpu(appointSpu);
            req.setAppointSku(appointSku);
            req.setPageSize(pageSize);
            DConcurrentTemplate.tryLockMode(
                    DO_SYNC_MABANG_PRODUCT_JOB_PREFIX.concat(param),
                    lock -> lock.tryLock(3, TimeUnit.SECONDS),
                    () -> {
                        log.info("执行同步马帮商品 --- {}", new Date());
                        syncProductBizSve.syncProductFromMaBang(req);
                    }
            );
        } catch (Exception e) {
            log.error("[同步马帮商品] 定时任务异常, param={}", param, e);
            return ReturnT.FAIL;
        }

        log.info("[同步马帮商品] 定时任务 - 结束 --- {}", new Date());
        return ReturnT.SUCCESS;
    }

    /**
     * 同步马帮库存
     *
     * @param param 任务参数
     * @return ReturnT<String>
     */
    @Job("autoDoSyncMaBangStock")
    public ReturnT<String> autoDoSyncMaBangStock(String param) {
        log.info("[同步马帮库存] 定时任务 - 开始 --- {}", new Date());

        try {
            JSONObject paramObject = JSONObject.parseObject(param);
            String thirdPartySystemId = paramObject.getString("thirdPartySystemId");
            String appointSku = paramObject.getString("appointSku");
            SyncStockQuantityFromMaBangCommand req = new SyncStockQuantityFromMaBangCommand();
            req.setThirdPartySystemId(thirdPartySystemId);
            req.setAppointSku(appointSku);

            DConcurrentTemplate.tryLockMode(
                    DO_SYNC_MABANG_STOCK_JOB_PREFIX.concat(param),
                    lock -> lock.tryLock(3, TimeUnit.SECONDS),
                    () -> {
                        log.info("执行同步马帮库存 --- {}", new Date());
                        syncStockBizSve.syncStockQuantityFromMaBang(req);
                    }
            );

        } catch (Exception e) {
            log.error("[同步马帮库存] 定时任务异常, param={}", param, e);
            return ReturnT.FAIL;
        }

        log.info("[同步马帮库存] 定时任务 - 结束 --- {}", new Date());
        return ReturnT.SUCCESS;
    }

    /**
     * 同步马帮商品价格
     *
     * @param param 任务参数
     * @return ReturnT<String>
     */
    @Job("autoDoSyncMaBangProductPrice")
    public ReturnT<String> autoDoSyncMaBangProductPrice(String param) {
        log.info("[同步马帮商品价格] 定时任务 - 开始 --- {}", new Date());

        try {
            JSONObject paramObject = JSONObject.parseObject(param);
            String thirdPartySystemId = paramObject.getString("thirdPartySystemId");
            String appointSku = paramObject.getString("appointSku");
            SyncProductPriceFromMaBangCommand req = new SyncProductPriceFromMaBangCommand();
            req.setThirdPartySystemId(thirdPartySystemId);
            req.setAppointSku(appointSku);

            DConcurrentTemplate.tryLockMode(
                    DO_SYNC_MABANG_PRODUCT_PRICE_JOB_PREFIX.concat(param),
                    lock -> lock.tryLock(3, TimeUnit.SECONDS),
                    () -> {
                        log.info("执行同步马帮商品价格 --- {}", new Date());
                        syncProductBizSve.syncProductPriceFromMaBang(req);
                    }
            );

        } catch (Exception e) {
            log.error("[同步马帮商品价格] 定时任务异常, param={}", param, e);
            return ReturnT.FAIL;
        }

        log.info("[同步马帮商品价格] 定时任务 - 结束 --- {}", new Date());
        return ReturnT.SUCCESS;
    }
}
