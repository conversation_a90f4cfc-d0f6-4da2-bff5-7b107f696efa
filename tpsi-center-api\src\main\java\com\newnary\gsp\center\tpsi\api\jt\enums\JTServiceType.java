package com.newnary.gsp.center.tpsi.api.jt.enums;

import lombok.Getter;

/**
 * @Author: jack
 * @CreateTime: 2023-8-15
 */
@Getter
public enum JTServiceType {

    HOME_PICKUP("上门取件","1"),
    HOME_DELIVERY("上门寄件","6");

    private String name;
    private String value;

    JTServiceType(String name, String value){
        this.name = name;
        this.value = value;
    }

    public String getValue(){
        return this.value;
    }

}
