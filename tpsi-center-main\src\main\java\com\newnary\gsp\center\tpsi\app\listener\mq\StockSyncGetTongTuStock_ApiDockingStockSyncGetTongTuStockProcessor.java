package com.newnary.gsp.center.tpsi.app.listener.mq;

import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.mq.consumer.ApiDockingMQConsumer;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.service.ISyncStockBizSve;
import com.newnary.messagebody.gsp.tpsi.GSPApiDockingTopic;
import com.newnary.messagebody.gsp.tpsi.mo.ApiDockingMO;
import com.newnary.mq.starter.consumer.AbstractMQProcessor;
import com.newnary.mq.starter.consumer.MQMessage;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class StockSyncGetTongTuStock_ApiDockingStockSyncGetTongTuStockProcessor extends AbstractMQProcessor<ApiDockingMO> {

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Resource
    private ISyncStockBizSve syncStockBizSve;

    @Override
    public boolean doProcess(MQMessage<ApiDockingMO> message) {
        ApiDockingMO mo = message.getContent();

        //解析message，需要拆分出tongtu的TPS，还有马帮的TPS(第三方对接系统id),具体的sku
        JSONObject dataObject = JSONObject.parseObject(mo.getData());
        String sku = dataObject.getString("stockSku");
        String maBangThirdPartySystemId = dataObject.getString("maBangThirdPartySystemId");
        String tongTuThirdPartySystemId = dataObject.getString("tongTuThirdPartySystemId");

        syncStockBizSve.syncStockFromTongTuToMaBang(sku, maBangThirdPartySystemId, tongTuThirdPartySystemId);

        return true;
    }

    @Override
    public Class<?> consumerClz() {
        return ApiDockingMQConsumer.class;
    }

    @Override
    public String tag() {
        return GSPApiDockingTopic.Tag.STOCK_SYNC_FROM_TONGTU_TO_MABANG;
    }


    private ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }
}
