package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.amazon;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingAmazonCategoryTreeRequest {

    /**
     * 站点
     */
    @NotNull(message = "站点不能为空")
    private String station;

    private Integer level;

    private String cate_id;

    /**
     * 父类目
     */
    private String p_l1_id;
    private String p_l2_id;
    private String p_l3_id;
    private String p_l4_id;
    private String p_l5_id;
    private String p_l6_id;
    private String p_l7_id;
    private String p_l8_id;
    private String p_l9_id;
    private String p_l10_id;
}
