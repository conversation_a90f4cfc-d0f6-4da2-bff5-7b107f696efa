package com.newnary.gsp.center.tpsi.infra.client.soulink;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.common.utils.httpmethod.ApiBaseResult;
import com.newnary.common.utils.httpmethod.HttpMethodUtil;
import com.newnary.gsp.center.tpsi.api.shortlink.response.ConvertShortLinkResp;
import com.newnary.gsp.center.tpsi.infra.client.soulink.params.SuoLinkParam;
import com.newnary.gsp.center.tpsi.infra.client.soulink.valobj.Response.SouLinkDataApiBaseResult;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: WangRUTao
 */
@Slf4j
public class SouLinkApiClient {

    // 设置日期格式为"yyyy-MM-dd HH:mm:ss"
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /*短链转换接口*/
    private static String convertShortLink = "/api";

    private SuoLinkParam suoLinkParam;

    public SouLinkApiClient(String jtJsonParam) {
        SuoLinkParam params = JSON.parseObject(jtJsonParam, SuoLinkParam.class);
        this.suoLinkParam = params;
    }

    /**
     * 请求缩短链接
     *
     * @return SouLinkDataApiBaseResult
     */
    public SouLinkDataApiBaseResult<String> convertShortLink(String url,String sourceDomain) {

        Map<String, Object> bodyParas = getParam(url);
        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");

        ApiBaseResult apiBaseResult = null;
        try {
            log.info("[请求缩链Params]{} " , JSONObject.toJSONString(bodyParas));

            apiBaseResult = HttpMethodUtil.syncGetMethod(suoLinkParam.getBaseUrl(),
                    0,
                    convertShortLink,
                    "application/json",
                    null,
                    bodyParas);
            SouLinkDataApiBaseResult<String> baseResult = buildDataBaseResult(apiBaseResult,bodyParas);

            System.out.println("[请求缩链baseResult {} " + baseResult);
            log.info("[请求缩链baseResult]请求结束, code={}, message={}, result={}, dataParas={}", baseResult.getCode(), baseResult.getMessage(), baseResult.getResult(), JSONObject.toJSONString(bodyParas));
            return baseResult;
        } catch (Exception e) {
            log.error(e.toString());
            e.printStackTrace();
            SouLinkDataApiBaseResult<String> ret = new SouLinkDataApiBaseResult<>();
            ConvertShortLinkResp resp = new ConvertShortLinkResp();
            resp.setShortLink(url);
            resp.setDomain(sourceDomain);
            resp.setExpireDate((String) bodyParas.get("expireDate"));
            ret.setResult(JSONObject.toJSONString(resp));
            return ret;
        }
    }


    private Map<String, Object> getParam(String url) {
        HashMap<String, Object> params = new HashMap<>();
        try {
            params.put("url", URLEncoder.encode(url, "UTF-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        params.put("key",suoLinkParam.getKey());
        params.put("expireDate",dataAfter(suoLinkParam.getExpireDays()));
        params.put("domain",suoLinkParam.getDomain());
        return params;
    }

    private String dataAfter(Long date){
        // 获取当前日期
        LocalDateTime currentDate = LocalDateTime.now();
        // 将日期加上7天
        LocalDateTime newDate = currentDate.plusDays(date);
        return newDate.format(formatter);
    }


    private SouLinkDataApiBaseResult<String> buildDataBaseResult(ApiBaseResult resultStr,Map<String, Object> bodyParas) {
        SouLinkDataApiBaseResult<String> apiBaseResult = new SouLinkDataApiBaseResult<>();
        apiBaseResult.setCode(resultStr.getCode());
        ConvertShortLinkResp resp = new ConvertShortLinkResp();
        resp.setShortLink(resultStr.getRet());
        resp.setDomain(suoLinkParam.getDomain());
        resp.setExpireDate((String) bodyParas.get("expireDate"));
        apiBaseResult.setResult(JSONObject.toJSONString(resp));
        return apiBaseResult;
    }


}
