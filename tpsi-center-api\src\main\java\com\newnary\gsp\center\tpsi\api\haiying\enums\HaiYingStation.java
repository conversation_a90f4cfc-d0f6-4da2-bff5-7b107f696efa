package com.newnary.gsp.center.tpsi.api.haiying.enums;

import lombok.Getter;

/**
 * @Author: jack
 * @CreateTime: 2022-7-13
 */
@Getter
public enum HaiYingStation {

    SHOPEE_MY("malaysia", "马来西亚", HaiYingPlatform.SHOPEE),
    SHOPEE_Taiwan_CHN("taiwan_china", "中国台湾", HaiYingPlatform.SHOPEE),
    SHOPEE_ID("indonesia", "印度尼西亚", HaiYingPlatform.SHOPEE),
    SHOPEE_TH("thailand", "泰国", HaiYingPlatform.SHOPEE),
    SHOPEE_PH("philippines", "菲律宾", HaiYingPlatform.SHOPEE),
    SHOPEE_SG("singapore", "新加坡", HaiYingPlatform.SHOPEE),
    SHOPEE_VN("vietnam", "越南", HaiYingPlatform.SHOPEE),
    SHOPEE_BR("brazil", "巴西", HaiYingPlatform.SHOPEE),
    SHOPEE_MX("mexico", "墨西哥", HaiYingPlatform.SHOPEE),
    SHOPEE_CL("chile", "智利", HaiYingPlatform.SHOPEE),
    <PERSON>OPEE_CO("columbia", "哥伦比亚", HaiYingPlatform.SHOPEE),
    SHOPEE_PL("poland", "波兰", HaiYingPlatform.SHOPEE),
    LAZADA_MY("malaysia", "马来西亚", HaiYingPlatform.LAZADA),
    LAZADA_TH("thailand", "泰国", HaiYingPlatform.LAZADA),
    LAZADA_PH("philippines", "菲律宾", HaiYingPlatform.LAZADA),
    LAZADA_ID("indonesia", "印度尼西亚", HaiYingPlatform.LAZADA),
    EBAY_US("america", "美国站", HaiYingPlatform.EBAY),
    EBAY_DE("germany", "德国站", HaiYingPlatform.EBAY),
    EBAY_AU("australia", "澳大利亚站", HaiYingPlatform.EBAY),
    EBAY_UK("england", "英国站", HaiYingPlatform.EBAY),
    AMAZON_US("america", "美国", HaiYingPlatform.AMAZON),
    AMAZON_UK("england", "英国", HaiYingPlatform.AMAZON),
    AMAZON_DE("germany", "德国", HaiYingPlatform.AMAZON),
    AMAZON_JP("japan", "日本", HaiYingPlatform.AMAZON),
    ;

    private String site;

    private String description;

    private HaiYingPlatform platform;

    HaiYingStation(String site, String description, HaiYingPlatform platform) {
        this.site = site;
        this.description = description;
        this.platform = platform;
    }

}
