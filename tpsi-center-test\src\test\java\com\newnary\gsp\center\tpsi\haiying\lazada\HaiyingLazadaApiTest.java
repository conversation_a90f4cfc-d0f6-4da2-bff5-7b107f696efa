package com.newnary.gsp.center.tpsi.haiying.lazada;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.common.PageCondition;
import com.newnary.api.base.common.PageList;
import com.newnary.api.base.common.PageMeta;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingLazadaProductListOrderBy;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingOrderByType;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingStation;
import com.newnary.gsp.center.tpsi.api.haiying.request.lazada.HaiYingLazadaProductListCommand;
import com.newnary.gsp.center.tpsi.api.haiying.response.lazada.HaiYingLazadaCategoryInfoDTO;
import com.newnary.gsp.center.tpsi.api.haiying.response.lazada.HaiYingLazadaProductListDTO;
import com.newnary.gsp.center.tpsi.ctrl.haiying.HaiYingLazadaCommand2RequestTranslator;
import com.newnary.gsp.center.tpsi.ctrl.haiying.HaiYingLazadaResponse2DTOTranslator;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.HaiYingDataApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.lazada.*;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.lazada.*;
import com.newnary.gsp.center.tpsi.service.haiying.IHaiYingDataLazadaApiSve;
import com.newnary.test.starter.BaseTestInjectTenant;
import org.junit.Test;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
public class HaiyingLazadaApiTest extends BaseTestInjectTenant {

    private static final SimpleDateFormat SIMPLE_TIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    protected String tenantId() {
        return "TENANT3924117688555169382400";
    }

    @Resource
    private IHaiYingDataLazadaApiSve haiYingLazadaDataApiSve;

    @Test
    public void testLazadaProductList() throws ParseException {
        HaiYingLazadaProductListCommand command = new HaiYingLazadaProductListCommand();
        command.setStation(HaiYingStation.LAZADA_PH);
        command.setOrder_by(HaiYingLazadaProductListOrderBy.price);
        command.setOrder_by_type(HaiYingOrderByType.DESC);
        command.setReview_end(80);
        command.setReview_start(0);
        command.setNot_exist_sellers(Arrays.asList("Mobile Topup PH"));
        command.setPageCondition(new PageCondition(1, 20));
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingLazadaDataApiSve.getProductList(HaiYingLazadaCommand2RequestTranslator.transLazadaProductList(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingLazadaProductListResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingLazadaProductListResponse.class);
            PageList<HaiYingLazadaProductListDTO> ret = HaiYingLazadaResponse2DTOTranslator.transLazadaProductListList(command.getStation(), responseList, getResultPageMeta(command.getPageCondition(), apiBaseResult));
            System.out.println(JSON.toJSONString(ret));
        }
    }

    @Test
    public void testLazadaProductDetailInfo() {
        HaiYingLazadaProductDetailInfoRequest request = new HaiYingLazadaProductDetailInfoRequest();
        request.setStations(Arrays.asList(HaiYingStation.LAZADA_MY.getSite()));
        request.setItem_ids(Arrays.asList("1218486514"));
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingLazadaDataApiSve.getProductDetailInfo(request);
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingLazadaProductDetailInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingLazadaProductDetailInfoResponse.class);
//            List<HaiYingLazadaProductDetailInfoDTO> ret = HaiYingResponse2DTOTranslator.transLazadaProductDetailInfoList(responseList);
//            System.out.println(JSON.toJSONString(ret));
        }
    }

    @Test
    public void testLazadaProductExtInfo() {
        HaiYingLazadaProductExtInfoRequest request = new HaiYingLazadaProductExtInfoRequest();
        request.setStations(Arrays.asList(HaiYingStation.LAZADA_MY.getSite()));
        request.setItem_ids(Arrays.asList("1949734431"));
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingLazadaDataApiSve.getProductExtInfo(request);
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingLazadaProductExtInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingLazadaProductExtInfoResponse.class);
//            List<HaiYingLazadaProductExtInfoDTO> ret = HaiYingResponse2DTOTranslator.transLazadaProductExtInfoList(responseList);
//            System.out.println(JSON.toJSONString(ret));
        }
    }

    @Test
    public void testLazadaProductHistoryInfo() {
        HaiYingLazadaProductHistoryInfoRequest request = new HaiYingLazadaProductHistoryInfoRequest();
        request.setStations(Arrays.asList(HaiYingStation.LAZADA_MY.getSite()));
        request.setItem_ids(Arrays.asList("135136755"));
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingLazadaDataApiSve.getProductHistoryInfo(request);
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingLazadaProductHistoryInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingLazadaProductHistoryInfoResponse.class);
//            List<HaiYingLazadaProductHistoryInfoDTO> ret = HaiYingResponse2DTOTranslator.transLazadaProductHistoryInfoList(responseList);
//            System.out.println(JSON.toJSONString(ret));
        }
    }

    @Test
    public void testLazadaProductHistoryDailyReview() {
        HaiYingLazadaProductHistoryDailyReviewRequest request = new HaiYingLazadaProductHistoryDailyReviewRequest();
        request.setStation(HaiYingStation.LAZADA_MY.getSite());
        request.setItem_ids("135136755");
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingLazadaDataApiSve.getProductHistoryDailyReview(request);
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingLazadaProductHistoryDailyReviewResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingLazadaProductHistoryDailyReviewResponse.class);
//            List<HaiYingLazadaProductHistoryInfoDTO> ret = HaiYingResponse2DTOTranslator.transLazadaProductHistoryInfoList(responseList);
//            System.out.println(JSON.toJSONString(ret));
        }
    }

    @Test
    public void testLazadaCategoryTree() {
        HaiYingLazadaCategoryTreeRequest request = new HaiYingLazadaCategoryTreeRequest();
        request.setStation(HaiYingStation.LAZADA_MY.getSite());
/*        request.setP_l1_name("Toys & Games");*/
        request.setP_l1_name("Motor");
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingLazadaDataApiSve.getCategoryTree(request);
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingLazadaCategoryTreeResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingLazadaCategoryTreeResponse.class);
//            List<HaiYingLazadaCategoryTreeDTO> ret = HaiYingResponse2DTOTranslator.transLazadaCategoryTreeList(responseList);
//            System.out.println(JSON.toJSONString(ret));
        }
    }

    @Test
    public void testLazadaTopCategoryInfo() {
        HaiYingLazadaTopCategoryInfoRequest request = new HaiYingLazadaTopCategoryInfoRequest();
        request.setStation(HaiYingStation.LAZADA_MY.getSite());
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingLazadaDataApiSve.getTopCategoryInfo(request);
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingLazadaCategoryInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingLazadaCategoryInfoResponse.class);
            List<HaiYingLazadaCategoryInfoDTO> ret = HaiYingLazadaResponse2DTOTranslator.transLazadaCategoryInfoList(responseList);
            System.out.println(JSON.toJSONString(ret));
        }
    }

    @Test
    public void testLazadaSubCategoryInfo() throws ParseException {
        HaiYingLazadaSubCategoryInfoRequest request = new HaiYingLazadaSubCategoryInfoRequest();
        request.setStation(HaiYingStation.LAZADA_MY.getSite());
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingLazadaDataApiSve.getSubCategoryInfo(request);
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingLazadaCategoryInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingLazadaCategoryInfoResponse.class);
            PageList<HaiYingLazadaCategoryInfoDTO> ret = HaiYingLazadaResponse2DTOTranslator.transLazadaCategoryInfoPageList(responseList,getResultPageMeta(new PageCondition(1,20),apiBaseResult));
            System.out.println(JSON.toJSONString(ret));
        }
    }

    private PageMeta getResultPageMeta(PageCondition pageCondition, HaiYingDataApiBaseResult<String> apiBaseResult) {
        if (apiBaseResult.getTotalSize() > 0) {
            PageMeta pageMeta = new PageMeta();
            if (null != pageCondition) {
                pageMeta.pageNum = pageCondition.pageNum;
            } else {
                pageMeta.pageNum = 1;
            }
            pageMeta.pageSize = apiBaseResult.getSize();
            if (apiBaseResult.getTotalSize() % apiBaseResult.getSize() == 0) {
                pageMeta.pages = apiBaseResult.getTotalSize() / apiBaseResult.getSize();
            } else {
                pageMeta.pages = (apiBaseResult.getTotalSize() / apiBaseResult.getSize()) + 1;
            }
            pageMeta.total = apiBaseResult.getTotalSize();
            return pageMeta;
        }
        return null;
    }

}
