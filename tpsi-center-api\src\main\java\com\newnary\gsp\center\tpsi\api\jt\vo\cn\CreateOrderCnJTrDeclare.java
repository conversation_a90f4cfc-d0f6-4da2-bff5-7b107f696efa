package com.newnary.gsp.center.tpsi.api.jt.vo.cn;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * 2023-12-26
 * 发件信息对象
 * <AUTHOR>
 */
@Data
public class CreateOrderCnJTrDeclare {

    @Size(max = 64,message = "englishName(最大64个字符)")
    @NotBlank(message = "englishName(不能为空)")
    private String englishName;

    @Size(max = 100,message = "chineseName(最大100个字符)")
    @NotBlank(message = "chineseName(不能为空)")
    private String chineseName;

    @NotNull(message = "quantity(不能为空)")
    private Integer quantity;

    @Size(max = 12,message = "consigneeCity(最大12个字符)")
    private String brand;

    @Size(max = 32,message = "goodsBarcode(最大32个字符)")
    private String goodsBarcode;

    @NotNull(message = "unitNetWeightD(不能为空)")
    private BigDecimal unitNetWeightD;

    @NotNull(message = "unitDeclarePriceD(不能为空)")
    private BigDecimal unitDeclarePriceD;

    @Size(max = 12,message = "consigneeCity(最大12个字符)")
    private String hsCode;

    @Size(max = 32,message = "productModel(最大32个字符)")
    private String productModel;

    @Size(max = 64,message = "material(最大64个字符)")
    private String material;

    @Size(max = 64,message = "purpose(最大64个字符)")
    private String purpose;

    @Size(max = 32,message = "sku(最大32个字符)")
    private String sku;

    @Size(max = 512,message = "consigneeCity(最大512个字符)")
    private String pickingRemark;

    @Size(max = 255,message = "consigneeCity(最大255个字符)")
    private String productUrl;

    @Size(max = 2,message = "origin(最大2个字符)")
    private String origin;


}
