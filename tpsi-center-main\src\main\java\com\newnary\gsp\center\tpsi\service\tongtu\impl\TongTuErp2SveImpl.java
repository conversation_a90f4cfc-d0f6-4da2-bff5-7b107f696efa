package com.newnary.gsp.center.tpsi.service.tongtu.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.newnary.gsp.center.tpsi.api.tongtu.request.SyncStockFromTongTuCommand;
import com.newnary.gsp.center.tpsi.infra.client.tongtu.TongTuApiClient;
import com.newnary.gsp.center.tpsi.infra.client.tongtu.valobj.TongTuApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.service.impl.SystemClientSve;
import com.newnary.gsp.center.tpsi.service.tongtu.ITongTuErp2Sve;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class TongTuErp2SveImpl extends SystemClientSve implements ITongTuErp2Sve {

    public static final Logger LOGGER = LoggerFactory.getLogger(TongTuErp2SveImpl.class);

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    private static final String PRODUCT_TYPE_NORMAL = "0";
    private static final String PRODUCT_TYPE_VAR_ARGS = "1";

    @Override
    public Integer stocksQuery(SyncStockFromTongTuCommand req) {
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(req.getThirdPartySystemId());

        //获取apiClient
        TongTuApiClient tongTuApiClient = getClient(thirdPartySystem.getParams());

        Map<String, Object> bodyParas = new HashMap<String, Object>();
        List<String> skus = new ArrayList<String>();
        skus.add(req.getSku());
        bodyParas.put("skus", skus);

        int pageNo = 1;
        int currentPageSize = 0;
        int pageSize = 100;

        int totalStock = 0;

        do {
            System.out.println("当前获取通途库存列表第" + pageNo + "页");
            bodyParas.put("page", pageNo);
            //循环获取分页
            TongTuApiBaseResult<String> ret = tongTuApiClient.stocksQuery(bodyParas);
            JSONArray array = JSON.parseObject(ret.getDatas()).getJSONArray("array");
            for (int i = 0; i < array.size(); i++) {
                JSONObject item = array.getJSONObject(i);
                Integer availableStockQuantity = item.getInteger("availableStockQuantity");
                if (availableStockQuantity != null) {
                    totalStock += availableStockQuantity;
                }
            }

            currentPageSize = array.size();
            pageNo++;
        } while (currentPageSize == pageSize);
        return totalStock;
    }

    @Override
    public String warehouseQuery(String thirdPartySystemId) {
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);

        //获取apiClient
        TongTuApiClient tongTuApiClient = getClient(thirdPartySystem.getParams());

        Map<String, Object> bodyParas = new HashMap<>();
        int pageNo = 1;
        int currentPageSize = 0;
        int pageSize = 100;
        JSONArray warehouseList = new JSONArray();

        do {
            System.out.println("当前获取通途仓库列表第" + pageNo + "页");
            bodyParas.put("page", pageNo);
            TongTuApiBaseResult<String> ret = tongTuApiClient.warehouseQuery(bodyParas);
            JSONArray array = JSON.parseObject(ret.getDatas()).getJSONArray("array");
            warehouseList.addAll(array);
            currentPageSize = array.size();
            pageNo++;
        } while (currentPageSize == pageSize);
        return warehouseList.toString();
    }

    @Override
    public String ordersQuery(String thirdPartySystemId, String context) {
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);

        //获取apiClient
        TongTuApiClient tongTuApiClient = getClient(thirdPartySystem.getParams());

        Map<String, Object> bodyParas = JSONObject.parseObject(JSON.toJSONString(context));

        int pageNo = 1;
        int currentPageSize = 0;
        int pageSize = 100;
        JSONArray ordersList = new JSONArray();

        do {
            System.out.println("当前获取通途订单列表第" + pageNo + "页");
            bodyParas.put("page", pageNo);
            //循环获取分页
            TongTuApiBaseResult<String> ret = tongTuApiClient.stocksQuery(bodyParas);
            JSONArray array = JSON.parseObject(ret.getDatas()).getJSONArray("array");
            ordersList.addAll(array);
            currentPageSize = array.size();
            pageNo++;
        } while (currentPageSize == pageSize);
        return ordersList.toString();
    }

    @Override
    public String orderImport(String thirdPartySystemId, String context) {
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);

        //获取apiClient
        TongTuApiClient tongTuApiClient = getClient(thirdPartySystem.getParams());

        Map<String, Object> bodyParas = JSONObject.parseObject(JSON.toJSONString(context));

        TongTuApiBaseResult<String> ret = tongTuApiClient.orderImport(bodyParas);

        return null;
    }

    private TongTuApiClient getClient(String tongTuParams) {
        return new TongTuApiClient(tongTuParams);
    }

}
