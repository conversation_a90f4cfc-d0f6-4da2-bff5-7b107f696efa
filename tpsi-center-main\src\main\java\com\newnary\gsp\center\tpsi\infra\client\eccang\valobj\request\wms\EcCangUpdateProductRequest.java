package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.wms;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
public class EcCangUpdateProductRequest {
    @NotNull(message = "SKU不能为空")
    private String product_sku;
    private String reference_no;
    @NotNull(message = "产品标题不能为空")
    private String product_title;
    private String product_title_en;
    @NotNull(message = "重量不能为空")
    private Float product_weight;
    @NotNull(message = "长度不能为空")
    private Float product_length;
    @NotNull(message = "宽度不能为空")
    private Float product_width;
    @NotNull(message = "高度不能为空")
    private Float product_height;
    private Integer contain_battery;
    private String battery_type;
    @NotNull(message = "申报价值不能为空")
    private Float product_declared_value;
    @NotNull(message = "申报名称(英文)不能为空")
    private String product_declared_name;
    @NotNull(message = "申报名称(中文)不能为空")
    private String product_declared_name_zh;
    private String product_brand;
    private String product_model;
    private String product_origin;
    private String product_material;
    private String product_use_en;
    private String product_material_en;
    private String product_desc_url;
    private String cat_lang;
    private Integer cat_id_level0;
    private Integer cat_id_level1;
    private Integer cat_id_level2;
    private Integer verify;
    private Integer warning_days;
    private String product_color;
    private Integer is_box_more_sku;
    private Integer fragile_property;
    private String product_size_type;
}
