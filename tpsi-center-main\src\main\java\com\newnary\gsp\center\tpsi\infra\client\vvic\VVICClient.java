package com.newnary.gsp.center.tpsi.infra.client.vvic;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.common.utils.httpmethod.ApiBaseResult;
import com.newnary.common.utils.httpmethod.HttpMethodUtil;
import com.newnary.gsp.center.tpsi.infra.client.vvic.params.VVICBaseParam;
import com.newnary.gsp.center.tpsi.infra.client.vvic.utils.VVICSignUtil;
import com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.VVICBaseApiResult;
import com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.request.*;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import net.sf.cglib.beans.BeanMap;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Setter
@Getter
public class VVICClient {
    //查询商品列表
    private String GETITEMLIST = "/api/item/list/v1";

    //查询商品详情
    private String GETITEMDETIAL = "/api/item/detail/v1";

    //查询商品缺货状态
    private String GETITEMSTATUS= "/api/item/status/v1";

    //创建订单
    private String CREATEORDER= "/api/order/create/v1";

    //取消订单
    private String CANCELORDER= "/api/order/cancel/v1";

    //查看订单状态
    private String GETORDERSTATUS= "/api/order/cancel/v1";

    //查询订单列表
    private String GETORDERLIST= "/api/order/list/v1";

    //获取代发服务
    private String GETVAS= "/api/order/vas/list/v1";

    //快递清单查询
    private String GETEXPRESSLIST= "/api/logistics/express/list/v1";

    //获取档口商品列表
    private String GETSHOPITEMSLIST = "/api/shop/item/list/v1";

    private String app_id;
    private String app_secret;
    private String baseUrl;
    public VVICClient(String vvicDataParam) {
        VVICBaseParam vvicBaseParam = JSON.parseObject(vvicDataParam, VVICBaseParam.class);
        this.baseUrl = vvicBaseParam.getBaseUrl();
        //this.baseUrl = "http://api.vvic.com";
        this.app_id = vvicBaseParam.getApp_id();
        this.app_secret = vvicBaseParam.getApp_secret();
    }

    //Get请求
    private VVICBaseApiResult<String> sendGetResult(String service,String contentType, Map<String, String> params){
        try {
            String url = baseUrl.concat(service);
            ApiBaseResult apiBaseResult = HttpMethodUtil.syncGetMethod(url,3,null,contentType,null,params);
            log.info("service: service{} request: req{}",service,params);
            VVICBaseApiResult<String> result = buildBaseResult(apiBaseResult.getRet());
            if (result != null) {
                Integer status = result.getStatus();
                if (null != status && status != 200) {
                    log.info("status: {} message: {} data: {}", status, result.getMessage(), result.getData());
                }
            }

            return result;
        } catch (Exception e) {
            VVICBaseApiResult<String> result = new VVICBaseApiResult<>();
            result.setStatus(500);
            result.setMessage(e.getMessage());

            return result;
        }
    }

    //Post请求
    private VVICBaseApiResult<String> sendPostResult(String service,String contentType, Map<String, Object> params){
        try {
            String url = baseUrl.concat(service);
            ApiBaseResult apiBaseResult = HttpMethodUtil.syncPostMethod(url,3,null,contentType,null,null,params);
            log.info("service: {} request: {}",service,JSON.toJSONString(params));
            VVICBaseApiResult<String> result = buildBaseResult(apiBaseResult.getRet());
            if (result != null) {
                Integer status = result.getStatus();
                if (null != status && status != 200) {
                    log.info("status: {} message: {} data: {}", status, result.getMessage(), result.getData());
                }
            }
            return result;
        } catch (Exception e) {
            VVICBaseApiResult<String> result = new VVICBaseApiResult<>();
            result.setStatus(500);
            result.setMessage(e.getMessage());
            return result;
        }
    }

    private VVICBaseApiResult<String> buildBaseResult(String result) {
        if (StringUtils.isBlank(result)) {
            return null;
        }
        VVICBaseApiResult<String> baseApiResult = new VVICBaseApiResult<>();
        JSONObject jsonObject = JSON.parseObject(result);
        baseApiResult.setStatus(jsonObject.getInteger("status"));
        baseApiResult.setMessage(jsonObject.getString("message"));
        baseApiResult.setData(jsonObject.getString("data"));
        return baseApiResult;
    }

    //获取商品列表
    public VVICBaseApiResult<String> getItemList(VVICGetItemListReq req) {
        return sendGetResult(GETITEMLIST, "",buildGetParam(req));
    }

    //获取商品详情
    public VVICBaseApiResult<String> getItemDetail(VVICGetItemDetialReq req) {
        return sendGetResult(GETITEMDETIAL, "", buildGetParam(req));
    }

    //获取商品缺货状态
    public VVICBaseApiResult<String> getItemStatus(VVICGetItemStatusReq req) {
        return sendGetResult(GETITEMSTATUS, "", buildGetParam(req));
    }

    //创建订单
    public VVICBaseApiResult<String> createOrder(VVICCreateOrderReq req) {
        return sendPostResult(CREATEORDER,"application/json",buildPostParam(req));
    }

    //取消订单
    public VVICBaseApiResult<String> cancelOrder(VVICCancelOrderReq req) {
        if (req.getOrder_no() == null && req.getOut_order_no()==null) {
            VVICBaseApiResult<String> result = new VVICBaseApiResult<>();
            result.setStatus(500);
            result.setMessage("order_no和out_order_no不能同时为空");
            return result;
        }
        return sendPostResult(CANCELORDER,"application/json",buildPostParam(req));
    }

    //获取订单状态
    public VVICBaseApiResult<String> getOrderStatus(VVICGetOrderStatusReq req) {
        if (req.getOrder_no() == null && req.getOut_order_no()==null) {
            VVICBaseApiResult<String> result = new VVICBaseApiResult<>();
            result.setStatus(500);
            result.setMessage("order_no和out_order_no不能同时为空");
            return result;
        }
        return sendGetResult(GETORDERSTATUS, "", buildGetParam(req));
    }

    //获取订单列表
    public VVICBaseApiResult<String> getOrderList(VVICGetOrderListReq req) {
        return sendGetResult(GETORDERLIST, "", buildGetParam(req));
    }

    //获取代发服务
    public VVICBaseApiResult<String> getVas(VVICGetVasReq req) {
        return sendPostResult(GETVAS, "application/json", buildPostParam(req));
    }

    //快递清单查询
    public VVICBaseApiResult<String> getExpressList(VVICGetExpressReq req) {
        return sendPostResult(GETEXPRESSLIST, "application/json", buildPostParam(req));
    }

    //获取档口itemList
    public VVICBaseApiResult<String> getShopItemList(VVICGetShopItemListReq req) {
        return sendGetResult(GETSHOPITEMSLIST, "", buildGetParam(req));
    }

    //构建get请求参数
    private  Map<String, String> buildGetParam(Object req) {
        Map<String, String> param = new HashMap<>();
        Date date = new Date();
        param.put("sign", VVICSignUtil.getGetSign(app_id,app_secret,date.getTime(),req));
        param.put("app_id", app_id);
        param.put("timestamp", String.valueOf(date.getTime()));
        try {
            Map<String, String> map = BeanUtils.describe(req);
            //替换null为""
            for (Map.Entry<String, String> entry : map.entrySet()) {
                if (entry.getValue() == null || entry.getValue().isEmpty()) {
                    map.put(entry.getKey(), "");
                }
            }
            param.putAll(map);
        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            e.printStackTrace();
        }
        return param;
    }

    //构建post请求参数
    private HashMap<String, Object> buildPostParam(Object req) {
        HashMap<String, Object> param = new HashMap<>();
        Date date = new Date();
        param.put("sign", VVICSignUtil.getPostSign(app_id,app_secret,date.getTime(),req));
        param.put("app_id", app_id);
        param.put("timestamp", date.getTime());
        Map<String,Object> dataMap = new HashMap<>();
        BeanMap beanMap = BeanMap.create(req);
        for (Object key : beanMap.keySet()) {
            dataMap.put(key.toString(),beanMap.get(key));
        }
        param.putAll(dataMap);

        return param;
    }
}
