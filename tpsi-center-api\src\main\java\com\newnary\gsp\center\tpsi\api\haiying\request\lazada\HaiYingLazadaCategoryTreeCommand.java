package com.newnary.gsp.center.tpsi.api.haiying.request.lazada;

import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingStation;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingLazadaCategoryTreeCommand {

    /**
     * 站点
     */
    @NotNull(message = "站点不能为空")
    private HaiYingStation station;

    /**
     * 类目等级
     */
    private Integer level;

    /**
     * 类目名称
     */
    private String cname;

    /**
     * 父类目名称
     */
    private String p_l1_name;
    private String p_l2_name;
    private String p_l3_name;
    private String p_l4_name;
    private String p_l5_name;
    private String p_l6_name;
    private String p_l7_name;
    private String p_l8_name;
    private String p_l9_name;
    private String p_l10_name;
}
