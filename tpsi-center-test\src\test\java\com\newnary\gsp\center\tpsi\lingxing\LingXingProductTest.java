package com.newnary.gsp.center.tpsi.lingxing;

import com.alibaba.fastjson.JSON;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.tpsi.app.job.LingXingERPJobManager;
import com.newnary.gsp.center.tpsi.infra.client.lingxing.params.LingXingDataParam;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.service.lingxing.ILingXingApiService;
import com.newnary.gsp.center.tpsi.service.lingxing.request.QueryLingXingProductInfoListRequest;
import com.newnary.gsp.center.tpsi.service.lingxing.request.QueryLingXingProductInfoRequest;
import com.newnary.gsp.center.tpsi.service.lingxing.request.QueryLingXingProductListRequest;
import com.newnary.gsp.center.tpsi.service.lingxing.response.QueryLingXingProductInfoListResponse;
import com.newnary.gsp.center.tpsi.service.lingxing.response.QueryLingXingProductInfoResponse;
import com.newnary.gsp.center.tpsi.service.lingxing.response.QueryLingXingProductListResponse;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.test.starter.BaseTestInjectTenant;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Collections;

public class LingXingProductTest extends BaseTestInjectTenant {
    @Override
    protected String tenantId() {
        return "GZRM";
    }

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Resource
    private ILingXingApiService iLingXingApiServiceImpl;

    @Resource
    private LingXingERPJobManager lingXingERPJobManager;

    @Test
    public void testQueryProductList() {
        ThirdPartySystem loadSystem = loadSystem("TESTLINGXING0001");
        LingXingDataParam lingXingDataParam = JSON.parseObject(loadSystem.getParams(), LingXingDataParam.class);

        QueryLingXingProductListRequest queryLingXingProductListRequest = new QueryLingXingProductListRequest();
        queryLingXingProductListRequest.setOffset(1);
        queryLingXingProductListRequest.setLength(20);

        QueryLingXingProductListResponse queryLingXingProductListResponse = iLingXingApiServiceImpl.queryProductList(lingXingDataParam, queryLingXingProductListRequest);
        System.out.println(JSON.toJSONString(queryLingXingProductListResponse));
    }

    @Test
    public void testQueryProductInfoList() {
        ThirdPartySystem loadSystem = loadSystem("TESTLINGXING0001");
        LingXingDataParam lingXingDataParam = JSON.parseObject(loadSystem.getParams(), LingXingDataParam.class);

        QueryLingXingProductInfoListRequest queryLingXingProductInfoListRequest = new QueryLingXingProductInfoListRequest();
        queryLingXingProductInfoListRequest.setSkus(Collections.singletonList("DB01457_02"));

        QueryLingXingProductInfoListResponse queryLingXingProductInfoListResponse = iLingXingApiServiceImpl.queryProductInfoList(lingXingDataParam, queryLingXingProductInfoListRequest);
        System.out.println(JSON.toJSONString(queryLingXingProductInfoListResponse));
    }

    @Test
    public void testQueryProductInfo() {
        ThirdPartySystem loadSystem = loadSystem("TESTLINGXING0001");
        LingXingDataParam lingXingDataParam = JSON.parseObject(loadSystem.getParams(), LingXingDataParam.class);

        QueryLingXingProductInfoRequest queryLingXingProductInfoRequest = new QueryLingXingProductInfoRequest();
        queryLingXingProductInfoRequest.setSku("DB01457_02");

        QueryLingXingProductInfoResponse queryLingXingProductInfoResponse = iLingXingApiServiceImpl.queryProductInfo(lingXingDataParam, queryLingXingProductInfoRequest);
        System.out.println(JSON.toJSONString(queryLingXingProductInfoResponse));
    }

    @Test
    public void testQueryProductInfoListForeach() {
        ThirdPartySystem loadSystem = loadSystem("TESTLINGXING0001");
        LingXingDataParam lingXingDataParam = JSON.parseObject(loadSystem.getParams(), LingXingDataParam.class);

        QueryLingXingProductListRequest queryLingXingProductListRequest = new QueryLingXingProductListRequest();
        queryLingXingProductListRequest.setOffset(1);
        queryLingXingProductListRequest.setLength(20);

        QueryLingXingProductListResponse queryLingXingProductListResponse = iLingXingApiServiceImpl.queryProductList(lingXingDataParam, queryLingXingProductListRequest);

        if (CollectionUtils.isNotEmpty(queryLingXingProductListResponse.getData())) {
            QueryLingXingProductInfoRequest queryLingXingProductInfoRequest = new QueryLingXingProductInfoRequest();
            queryLingXingProductListResponse.getData().forEach(data -> {
                queryLingXingProductInfoRequest.setSku(data.getSku());
                System.out.println(JSON.toJSONString(iLingXingApiServiceImpl.queryProductInfo(lingXingDataParam, queryLingXingProductInfoRequest)));
            });
        }
    }

    @Test
    public void testLingXingERPJobManager() {
        ReturnT<String> stringReturnT = lingXingERPJobManager.syncLingXingERPProduct("{\"thirdPartySystemId\":\"TESTLINGXING0001\",\"perGroupCount\":5,\"categoryId\":\"131730495373344\",\"insertSize\":200,\"startOffset\": 300,\"pageSize\": 100}");
    }

    public ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }
}
