package com.newnary.gsp.center.tpsi.api.jt;

import com.newnary.api.base.common.CommonResponse;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @Author: jack
 * @CreateTime: 2023-8-9
 * message: 极兔物流
 */
@RequestMapping("tpsi-center/jt")
public interface JTOpenApi {

    @PostMapping("jt-webhooks/{tenantID}")
    CommonResponse<String> jtWebhooks(@PathVariable(value = "tenantID") String tenantID);

}
