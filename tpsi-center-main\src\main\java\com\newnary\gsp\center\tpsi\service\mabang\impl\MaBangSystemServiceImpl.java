package com.newnary.gsp.center.tpsi.service.mabang.impl;

import com.alibaba.fastjson.JSON;
import com.newnary.gsp.center.tpsi.api.mabang.response.MaBangWarehouseList;
import com.newnary.gsp.center.tpsi.infra.client.mabang.MaBangGWApiClient;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.MaBangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.sys.MaBangSysGetWarehouseList;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.service.impl.SystemClientSve;
import com.newnary.gsp.center.tpsi.service.mabang.IMaBangSystemService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class MaBangSystemServiceImpl extends SystemClientSve implements IMaBangSystemService {

    public static final Logger LOGGER = LoggerFactory.getLogger(MaBangSystemServiceImpl.class);

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Override
    public MaBangWarehouseList sysGetWarehouseList(String thirdPartySystemId) {
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        //获取apiClient
        MaBangGWApiClient maBangGWApiClient = getClient(thirdPartySystem.getParams());

        MaBangSysGetWarehouseList maBangSysGetWarehouseList = new MaBangSysGetWarehouseList();
        maBangSysGetWarehouseList.setType(9);

        MaBangApiBaseResult<String> ret = maBangGWApiClient.sysGetWarehouseList(maBangSysGetWarehouseList);
        //返回结果
        MaBangWarehouseList maBangWarehouseList = JSON.parseObject(ret.getData(), MaBangWarehouseList.class);
        return maBangWarehouseList;
    }

    private MaBangGWApiClient getClient(String maBangParams) {
        return new MaBangGWApiClient(maBangParams);
    }

}
