package com.newnary.gsp.center.tpsi.api.mabang;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.gsp.center.tpsi.api.mabang.request.*;
import com.newnary.gsp.center.tpsi.api.mabang.response.MaBangLogisticsChannelList;
import com.newnary.gsp.center.tpsi.api.mabang.response.MaBangWarehouseList;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@RequestMapping("tpsi-center/mabang")
public interface MaBangApi {

    @PostMapping("syncProductFromMaBang")
    CommonResponse<Void> syncProductFromMaBang(@RequestBody SyncProductFromMaBangCommand req);

    @PostMapping("pushOrder2MaBang")
    CommonResponse<String> pushOrder2MaBang(@RequestBody PushOrder2MaBangCommand req);

    @PostMapping("syncStockQuantityFromMaBang")
    CommonResponse<Void> syncStockQuantityFromMaBang(@RequestBody SyncStockQuantityFromMaBangCommand req);

    @PostMapping("syncWarehouseFromMaBang")
    CommonResponse<MaBangWarehouseList> syncWarehouseFromMaBang(@RequestParam("thirdPartySystemId") String thirdPartySystemId);

    @PostMapping("syncLogisticschannelFromMaBang")
    CommonResponse<MaBangLogisticsChannelList> syncLogisticschannelFromMaBang(@RequestParam("thirdPartySystemId") String thirdPartySystemId);

    @PostMapping("doDeliverOrderInMaBang")
    CommonResponse<String> doDeliverOrderInMaBang(@RequestBody DoDeliverOrderInMaBangCommand req);

    @PostMapping("syncToProductLibraryV2")
    CommonResponse<String> syncToProductLibraryV2(@RequestBody SyncToProductLibraryV2Command req);

    @PostMapping("syncOrderFromMaBang")
    CommonResponse<Void> syncOrderFromMaBang(@RequestBody SyncOrderFromMaBangCommand req);

/*
    @PostMapping("warehouseDoAddStorage")
    CommonResponse<String> warehouseDoAddStorage(@RequestParam("thirdPartySystemId") String thirdPartySystemId, @RequestParam("context") String context);

*/

}
