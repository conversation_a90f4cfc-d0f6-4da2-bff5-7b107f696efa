package com.newnary.gsp.center.tpsi.app.listener.mq.domain;

import com.newnary.distributed.tools.concurrent.template.DConcurrentTemplate;
import com.newnary.gsp.center.tpsi.infra.model.CrawlerProduct;
import com.newnary.gsp.center.tpsi.infra.model.event.CrawlerProduct_FailEvent;
import com.newnary.gsp.center.tpsi.infra.model.event.CrawlerProduct_FillCrawlerReturnEvent;
import com.newnary.gsp.center.tpsi.infra.repository.ICrawProductRepository;
import com.newnary.spring.cloud.domain.DomainEventListener;
import com.newnary.spring.cloud.domain.DomainEventListenerMode;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since Created on 2023-03-22
 **/
@Component
public class CrawlerProduct_FailEventListener implements DomainEventListener<CrawlerProduct_FailEvent> {

    @Resource
    private ICrawProductRepository crawProductRepository;

    @Override
    public void onListen(CrawlerProduct_FailEvent crawlerProduct_failEvent) {
        CrawlerProduct source = crawlerProduct_failEvent.source();
        DConcurrentTemplate.tryLockMode(
                CrawlerProduct.CRAWLER_PRODUCT_LOCK_PREFIX.concat(source.getProductId()),
                lock -> lock.tryLock(1, TimeUnit.SECONDS),
                ()->{
                    crawProductRepository.update(source);
                }
        );
    }

    @Override
    public Class<CrawlerProduct_FailEvent> eventClz() {
        return CrawlerProduct_FailEvent.class;
    }

//    @Override
//    public DomainEventListenerMode mode() {
//        return DomainEventListenerMode.ASYNCHRONOUS;
//    }
}
