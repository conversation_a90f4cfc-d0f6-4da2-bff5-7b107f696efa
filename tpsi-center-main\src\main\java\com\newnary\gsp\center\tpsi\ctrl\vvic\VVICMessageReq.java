package com.newnary.gsp.center.tpsi.ctrl.vvic;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class VVICMessageReq {

    /**
     * 客户端appId
     */
    private String appId;

    /**
     * 	消息的唯一标识
     */
    private String msgId;

    /**
     * 消息签名
     */
    private String sign;

    /**
     * 	消息发送的时间戳，1970.1.1到现在的毫秒数
     */
    private Long timestamp;

    /**
     * 	消息类型
     */
    private String type;

    /**
     * 消息实体,数据类型格式：json
     */
    private Data data;

    @Getter
    @Setter
    public static class Data{
        /**
         * 商品的变更属性
         */
        private List<String> changedFields;

        /**
         * 	商品的vid
         */
        private String itemVid;

        /**
         * 消息发送的时间戳，1970.1.1到现在的毫秒数
         */
        private Long time;

        /**
         * 变更类型
         */
        private String type;
    }
}
