package com.newnary.gsp.center.tpsi.service.mabang.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.newnary.gsp.center.tpsi.api.mabang.request.DoDeliverOrderInMaBangCommand;
import com.newnary.gsp.center.tpsi.api.mabang.request.PushOrder2MaBangCommand;
import com.newnary.gsp.center.tpsi.api.mabang.request.SyncOrderFromMaBangCommand;
import com.newnary.gsp.center.tpsi.infra.client.mabang.MaBangGWApiClient;
import com.newnary.gsp.center.tpsi.infra.client.mabang.MaBangParams;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.MaBangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.MaBangOrder;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.order.MaBangDoChangeOrder;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.order.MaBangDoCreateOrder;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.order.MaBangDoDeliverOrder;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.order.MaBangGetOrderList;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.service.impl.SystemClientSve;
import com.newnary.gsp.center.tpsi.service.mabang.IMaBangOrderApiSve;
import com.newnary.gsp.center.trade.api.order.dto.OrderConsigneeDTO;
import com.newnary.gsp.center.trade.api.order.dto.OrderGoodsDTO;
import com.newnary.gsp.center.trade.api.order.dto.OrderSaleItemDTO;
import com.newnary.gsp.center.trade.api.order.feign.OrderFeignApi;
import com.newnary.gsp.center.trade.api.order.request.command.OrderCreateReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class MaBangOrderApiSveImpl extends SystemClientSve implements IMaBangOrderApiSve {

    @Resource
    private OrderFeignApi orderFeignApi;

    @Override
    public void orderGetOrderList(SyncOrderFromMaBangCommand req) {
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(req.getThirdPartySystemId());
        //获取apiClient
        MaBangGWApiClient maBangGWApiClient = getClient(thirdPartySystem.getParams());

        MaBangGetOrderList maBangGetOrderList = this.maBangGetOrderListDataTransform(req);
        maBangGetOrderList.setStatus(req.getOrderStatus());
        maBangGetOrderList.setCanSend("3");

        //调用马帮获取订单
        int pageCount = 0;
        int pageNo = 1;

        do {
            log.info("现在获取第{}页", pageNo);
            maBangGetOrderList.setPage(pageNo);
            //循环获取分页
            MaBangApiBaseResult<String> ret = maBangGWApiClient.orderGetOrderList(maBangGetOrderList);
            JSONArray orderList = JSON.parseObject(ret.getData()).getJSONArray("data");
            pageCount = JSON.parseObject(ret.getData()).getInteger("pageCount");
            // 三、遍历参数并发送订单创建的mq消息(消费者中进行创建订单)
            orderList.forEach(item -> {
                this.broadcast(req.getThirdPartySystemId(), "ORDER_SYNC", item.toString());
            });
            pageNo++;
        } while (pageNo <= pageCount);
    }

    private MaBangGetOrderList maBangGetOrderListDataTransform(SyncOrderFromMaBangCommand req) {
        MaBangGetOrderList maBangGetOrderList = new MaBangGetOrderList();
        maBangGetOrderList.setStatus(req.getOrderStatus());
        maBangGetOrderList.setCreateDateStart(req.getCreateDateStart());
        maBangGetOrderList.setCreateDateEnd(req.getCreateDateEnd());
        maBangGetOrderList.setUpdateTimeStart(req.getUpdateTimeStart());
        maBangGetOrderList.setUpdateTimeEnd(req.getUpdateTimeEnd());
        maBangGetOrderList.setExpressTimeStart(req.getExpressTimeStart());
        maBangGetOrderList.setExpressTimeEnd(req.getExpressTimeEnd());
        maBangGetOrderList.setPaidtimeStart(req.getPaidtimeStart());
        maBangGetOrderList.setPaidtimeEnd(req.getPaidtimeEnd());
        maBangGetOrderList.setCanSend("3");
        return maBangGetOrderList;
    }

    @Override
    public void doOrderSync(ThirdPartySystem thirdPartySystem, String data) {
        MaBangOrder maBangOrder = JSON.parseObject(data, MaBangOrder.class);
        //订单创建
        OrderCreateReq orderCreateReq = buildErpOrderCreateRequest(thirdPartySystem, maBangOrder);
        orderFeignApi.create(orderCreateReq).mustSuccessOrThrowOriginal();
    }

    @Override
    public String orderDoCreateOrder(PushOrder2MaBangCommand req) {
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(req.getThirdPartySystemId());
        //获取apiClient
        MaBangGWApiClient maBangGWApiClient = getClient(thirdPartySystem.getParams());

        //构建马帮创建订单对象
        MaBangDoCreateOrder maBangDoCreateOrder = new MaBangDoCreateOrder();

        MaBangApiBaseResult<String> ret = maBangGWApiClient.orderDoCreateOrder(maBangDoCreateOrder);
        //处理结果
        if (ret.getCode().equals("200")) {
            JSONObject data = JSONObject.parseObject(ret.getData());
            if (data.getInteger("errorCount") > 0) {
                String errorMessage = data.getString("error");
                return errorMessage;
            } else {
                //创建成功
                log.info("订单推送成功");
                String orderId = data.getString("orderId");
                return "SUCCESS";
            }
        } else {
            String errorMessage = ret.getMessage();
            return errorMessage;
        }
    }

    @Override
    public String orderDoDeliverOrder(DoDeliverOrderInMaBangCommand req) {
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(req.getThirdPartySystemId());
        //获取apiClient
        MaBangGWApiClient maBangGWApiClient = getClient(thirdPartySystem.getParams());

        //构建马帮创建订单对象
        MaBangDoDeliverOrder maBangDoDeliverOrder = maBangDoDeliverOrderDt(req);

        MaBangApiBaseResult<String> ret = maBangGWApiClient.orderDoDeliverOrder(maBangDoDeliverOrder);
        //反馈结果
        if (ret.getCode().equals("200")) {
            JSONObject data = JSONObject.parseObject(ret.getData());
            if (data.getInteger("errorCount") > 0) {
                String errorMessage = data.getString("error");
                return errorMessage;
            } else {
                //创建成功
                log.info("订单推送成功");
                return "SUCCESS";
            }
        } else {
            String errorMessage = ret.getMessage();
            return errorMessage;
        }
    }

    @Override
    public String orderDoCreateOrder(String thirdPartySystemId, MaBangDoCreateOrder req) {
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        //获取apiClient
        MaBangGWApiClient maBangGWApiClient = getClient(thirdPartySystem.getParams());

        MaBangApiBaseResult<String> ret = maBangGWApiClient.orderDoCreateOrder(req);
        //处理结果
        if (ret.getCode().equals("200")) {
            JSONObject data = JSONObject.parseObject(ret.getData());
            if (data.containsKey("errorCount") && data.getInteger("errorCount") > 0) {
                String errorMessage = data.getString("error");
                return errorMessage;
            } else {
                //创建成功
                log.info("订单推送成功");
                String orderId = data.getString("orderId");
                return "SUCCESS";
            }
        } else {
            String errorMessage = ret.getMessage();
            return errorMessage;
        }
    }

    @Override
    public MaBangOrder orderGetOrderListByIdStatus(String thirdPartySystemId, String platformOrderId, String status) {
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        //获取apiClient
        MaBangGWApiClient maBangGWApiClient = getClient(thirdPartySystem.getParams());

        MaBangGetOrderList maBangGetOrderList = new MaBangGetOrderList();
        maBangGetOrderList.setPlatformOrderIds(platformOrderId);
        maBangGetOrderList.setStatus(status);

        //调用马帮获取订单
        MaBangApiBaseResult<String> ret = maBangGWApiClient.orderGetOrderList(maBangGetOrderList);
        if (!ret.getCode().equals("200") || (Objects.isNull(ret.getData()) || !JSON.parseObject(ret.getData()).containsKey("data"))) {
            log.info("请求错误信息为{}", JSON.toJSONString(ret));
        } else {
            JSONArray orderList = JSON.parseObject(ret.getData()).getJSONArray("data");
            if (orderList != null && orderList.size() > 0) {
                MaBangOrder maBangOrder = orderList.getJSONObject(0).toJavaObject(MaBangOrder.class);
                return maBangOrder;
            }
        }
        return null;
    }

    @Override
    public String invalidOrder(String thirdPartySystemId, String platformOrderId) {
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        //获取apiClient
        MaBangGWApiClient maBangGWApiClient = getClient(thirdPartySystem.getParams());
        MaBangDoChangeOrder maBangDoChangeOrder = new MaBangDoChangeOrder();
        maBangDoChangeOrder.setPlatformOrderId(platformOrderId);
        maBangDoChangeOrder.setOrderStatus("5");
        //调用马帮修改订单请求
        MaBangApiBaseResult<String> ret = maBangGWApiClient.orderDoChangeOrder(maBangDoChangeOrder);

        if (ret.getCode().equals("200") && ret.getMessage().equals("success")) {
            return "SUCCESS";
        } else {
            String errorMessage = ret.getMessage();
            return errorMessage;
        }
    }

    private MaBangDoDeliverOrder maBangDoDeliverOrderDt(DoDeliverOrderInMaBangCommand req) {
        MaBangDoDeliverOrder maBangDoDeliverOrder = new MaBangDoDeliverOrder();
        maBangDoDeliverOrder.setPlatformOrderId(req.getMaBangPlatformOrderId());
        maBangDoDeliverOrder.setMyLogisticsChannelId(req.getMaBangLogisticsChannelId());
        maBangDoDeliverOrder.setTrackNumber(req.getTrackNumber());
        maBangDoDeliverOrder.setTrackNumber1(req.getTrackNumber1());
        return maBangDoDeliverOrder;
    }

    /**
     * 调用trade-center创建订单
     *
     * @param system
     * @param maBangOrder
     * @return
     */
    private OrderCreateReq buildErpOrderCreateRequest(ThirdPartySystem system, MaBangOrder maBangOrder) {
        OrderCreateReq orderCreateReq = new OrderCreateReq();
        //channelId需要通过systemParams进行配置
        MaBangParams params = JSON.parseObject(system.getParams(), MaBangParams.class);
        orderCreateReq.channelId = params.getChannelId();
        orderCreateReq.channelOrderNo = maBangOrder.getPlatformOrderId();

        OrderConsigneeDTO orderConsigneeDTO = new OrderConsigneeDTO();
        orderConsigneeDTO.phone = maBangOrder.getPhone1();
        orderConsigneeDTO.name = maBangOrder.getBuyerName();
        orderConsigneeDTO.country = maBangOrder.getCountryCode();
        orderConsigneeDTO.province = maBangOrder.getProvince();
        orderConsigneeDTO.city = maBangOrder.getCity();
        orderConsigneeDTO.district = maBangOrder.getDistrict();
        orderConsigneeDTO.detailInfo = maBangOrder.getStreet1();
        orderConsigneeDTO.backupDetailInfo = maBangOrder.getStreet2();
        orderConsigneeDTO.zip = maBangOrder.getPostCode();
        orderCreateReq.orderConsignee = orderConsigneeDTO;

        List<OrderSaleItemDTO> orderGoodsDTOList = new ArrayList<>();
        maBangOrder.getOrderItem().forEach(item -> {
            OrderSaleItemDTO orderGoodsDTO = new OrderSaleItemDTO();
            orderGoodsDTO.setQuantity(item.getQuantity());
            orderGoodsDTO.setOuterGoodsId(item.getStockSku());
            orderGoodsDTO.setOuterGoodsName(item.getTitle());
            orderGoodsDTO.setTax(BigDecimal.ZERO);
            orderGoodsDTO.setUnitPrice(new BigDecimal(item.getSellPrice()));
            orderGoodsDTO.setUnitPriceCurrency(maBangOrder.getCurrencyId());
            orderGoodsDTOList.add(orderGoodsDTO);
        });
        orderCreateReq.orderGoodsDTOList = orderGoodsDTOList;
        orderCreateReq.paymentPrice = maBangOrder.getItemTotal();
        orderCreateReq.discountPrice = maBangOrder.getPromotionAmount();
        orderCreateReq.shippingPrice = maBangOrder.getShippingCost();
        orderCreateReq.totalGoodsPrice = maBangOrder.getItemTotal();
        orderCreateReq.totalTax = maBangOrder.getOriginFax();
        orderCreateReq.tradeCurrency = maBangOrder.getCurrencyId();

        return orderCreateReq;

    }

    private MaBangGWApiClient getClient(String maBangParams) {
        return new MaBangGWApiClient(maBangParams);
    }

}
