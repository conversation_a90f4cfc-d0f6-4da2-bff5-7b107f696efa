package com.newnary.gsp.center.tpsi.ctrl.haiying;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.newnary.api.base.common.CommonResponse;
import com.newnary.api.base.common.PageCondition;
import com.newnary.api.base.common.PageList;
import com.newnary.api.base.common.PageMeta;
import com.newnary.gsp.center.tpsi.api.haiying.HaiYingShopeeApi;
import com.newnary.gsp.center.tpsi.api.haiying.request.shopee.*;
import com.newnary.gsp.center.tpsi.api.haiying.response.shopee.*;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.HaiYingDataApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.shopee.*;
import com.newnary.gsp.center.tpsi.service.haiying.IHaiYingDataShopeeApiSve;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-13
 */
@RestController
@Slf4j
public class HaiYingShopeeApiImpl implements HaiYingShopeeApi {

    private static final Integer pageLimit = 100000;

    @Resource
    private IHaiYingDataShopeeApiSve haiYingShopeeDataApiSve;

    @Override
    public CommonResponse<PageList<HaiYingShopeeKeywordInfoDTO>> getShopeeKeywordList(HaiYingShopeeKeywordListCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingShopeeDataApiSve.getKeywordList(HaiYingShopeeCommand2RequestTranslator.transShopeeKeywordList(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingShopeeKeywordInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingShopeeKeywordInfoResponse.class);
            if (apiBaseResult.getTotalSize() > pageLimit)
                apiBaseResult.setTotalSize(pageLimit); //TODO 因海鹰api限制返回前10w条
            return CommonResponse.success(HaiYingShopeeResponse2DTOTranslator.transShopeeKeywordInfoList(command.getStation(), responseList, getResultPageMeta(command.getPageCondition(), apiBaseResult)));
        } else {
            log.error("{}获取海鹰shopee关键词列表失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            PageList<HaiYingShopeeKeywordInfoDTO> ret = new PageList<>();
            PageMeta pageMeta = new PageMeta();
            pageMeta.pageNum = command.getPageCondition().pageNum;
            pageMeta.pageSize = command.getPageCondition().pageSize;
            ret.setPageMeta(pageMeta);
            return CommonResponse.success(ret);
        }
    }

    @Override
    public CommonResponse<PageList<HaiYingShopeeKeywordInfoDTO>> getShopeeKeywordInfo(HaiYingShopeeKeywordInfoCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingShopeeDataApiSve.getKeywordInfo(HaiYingShopeeCommand2RequestTranslator.transShopeeKeywordInfo(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingShopeeKeywordInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingShopeeKeywordInfoResponse.class);
            return CommonResponse.success(HaiYingShopeeResponse2DTOTranslator.transShopeeKeywordInfoList(command.getStation(), responseList, getResultPageMeta(command.getPageCondition(), apiBaseResult)));
        } else {
            log.error("{}获取海鹰shopee关键词信息失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            return CommonResponse.success(new PageList<>());
        }
    }

    @Override
    public CommonResponse<PageList<HaiYingShopeeProductListDTO>> getShopeeProductList(HaiYingShopeeProductListCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingShopeeDataApiSve.getProductList(HaiYingShopeeCommand2RequestTranslator.transShopeeProductList(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingShopeeProductListResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingShopeeProductListResponse.class);
            if (apiBaseResult.getTotalSize() > pageLimit)
                apiBaseResult.setTotalSize(pageLimit); //TODO 因海鹰api限制返回前10w条
            return CommonResponse.success(HaiYingShopeeResponse2DTOTranslator.transShopeeProductListList(command.getStation(), responseList, getResultPageMeta(command.getPageCondition(), apiBaseResult)));
        } else {
            log.error("{}获取海鹰shopee商品列表失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            PageList<HaiYingShopeeProductListDTO> ret = new PageList<>();
            PageMeta pageMeta = new PageMeta();
            pageMeta.pageNum = command.getPageCondition().pageNum;
            pageMeta.pageSize = command.getPageCondition().pageSize;
            ret.setPageMeta(pageMeta);
            return CommonResponse.success(ret);
        }
    }

    @Override
    public CommonResponse<List<HaiYingShopeeProductDetailInfoDTO>> getShopeeProductDetailInfo(HaiYingShopeeProductDetailInfoCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingShopeeDataApiSve.getProductDetailInfo(HaiYingShopeeCommand2RequestTranslator.transShopeeProductDetailInfo(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingShopeeProductDetailInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingShopeeProductDetailInfoResponse.class);
            return CommonResponse.success(HaiYingShopeeResponse2DTOTranslator.transShopeeProductDetailInfoList(responseList));
        } else {
            log.error("{}获取海鹰shopee商品详情信息失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            return CommonResponse.success(new ArrayList<>());
        }
    }

    @Override
    public CommonResponse<List<HaiYingShopeeProductExtInfoDTO>> getShopeeProductExtInfo(HaiYingShopeeProductExtInfoCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingShopeeDataApiSve.getProductExtInfo(HaiYingShopeeCommand2RequestTranslator.transShopeeProductExtInfo(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingShopeeProductExtInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingShopeeProductExtInfoResponse.class);
            return CommonResponse.success(HaiYingShopeeResponse2DTOTranslator.transShopeeProductExtInfoList(responseList));
        } else {
            log.error("{}获取海鹰shopee商品附加信息失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            return CommonResponse.success(new ArrayList<>());
        }
    }

    @Override
    public CommonResponse<List<HaiYingShopeeProductHistoryInfoDTO>> getShopeeProductHistoryInfo(HaiYingShopeeProductHistoryInfoCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingShopeeDataApiSve.getProductHistoryInfo(HaiYingShopeeCommand2RequestTranslator.transShopeeProductHistoryInfo(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingShopeeProductHistoryInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingShopeeProductHistoryInfoResponse.class);
            return CommonResponse.success(HaiYingShopeeResponse2DTOTranslator.transShopeeProductHistoryInfoList(responseList));
        } else {
            log.error("{}获取海鹰shopee商品历史信息失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            return CommonResponse.success(new ArrayList<>());
        }
    }
    private static final SerializerFeature[] features = {SerializerFeature.WriteMapNullValue};
    @Override
    public CommonResponse<List<HaiYingShopeeCategoryTreeDTO>> getShopeeCategoryTree(HaiYingShopeeCategoryTreeCommand command) {
        log.info("类目参数："+JSON.toJSONString(command, features));
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingShopeeDataApiSve.getCategoryTree(HaiYingShopeeCommand2RequestTranslator.transShopeeCategoryTree(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingShopeeCategoryTreeResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingShopeeCategoryTreeResponse.class);
            return CommonResponse.success(HaiYingShopeeResponse2DTOTranslator.transShopeeCategoryTreeList(responseList));
        } else {
            log.error("{}获取海鹰shopee类目树失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            return CommonResponse.success(new ArrayList<>());
        }
    }

    @Override
    public CommonResponse<List<HaiYingShopeeTopCategoryInfoDTO>> getShopeeTopCategoryInfo(HaiYingShopeeTopCategoryInfoCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingShopeeDataApiSve.getTopCategoryInfo(HaiYingShopeeCommand2RequestTranslator.transShopeeTopCategoryInfo(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingShopeeTopCategoryInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingShopeeTopCategoryInfoResponse.class);
            return CommonResponse.success(HaiYingShopeeResponse2DTOTranslator.transShopeeTopCategoryInfoList(responseList));
        } else {
            log.error("{}获取海鹰shopee一级类目信息失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            return CommonResponse.success(new ArrayList<>());
        }
    }

    @Override
    public CommonResponse<PageList<HaiYingShopeeSubCategoryInfoDTO>> getShopeeSubCategoryInfo(HaiYingShopeeSubCategoryInfoCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingShopeeDataApiSve.getSubCategoryInfo(HaiYingShopeeCommand2RequestTranslator.transShopeeSubCategoryInfo(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingShopeeSubCategoryInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingShopeeSubCategoryInfoResponse.class);
            return CommonResponse.success(HaiYingShopeeResponse2DTOTranslator.transShopeeSubCategoryInfoList(responseList, getResultPageMeta(command.getPageCondition(), apiBaseResult)));
        } else {
            log.error("{}获取海鹰shopee子类目信息失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            return CommonResponse.success(new PageList<>());
        }
    }

    private PageMeta getResultPageMeta(PageCondition pageCondition, HaiYingDataApiBaseResult<String> apiBaseResult) {
        if (apiBaseResult.getTotalSize() > 0) {
            PageMeta pageMeta = new PageMeta();
            if (null != pageCondition) {
                pageMeta.pageNum = pageCondition.pageNum;
            } else {
                pageMeta.pageNum = 1;
            }
            pageMeta.pageSize = apiBaseResult.getSize();
            if (apiBaseResult.getTotalSize() % apiBaseResult.getSize() == 0) {
                pageMeta.pages = apiBaseResult.getTotalSize() / apiBaseResult.getSize();
            } else {
                pageMeta.pages = (apiBaseResult.getTotalSize() / apiBaseResult.getSize()) + 1;
            }
            pageMeta.total = apiBaseResult.getTotalSize();
            return pageMeta;
        }
        return null;
    }

}
