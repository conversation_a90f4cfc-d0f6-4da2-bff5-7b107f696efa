package com.newnary.gsp.center.tpsi.infra.model;

import com.newnary.gsp.center.tpsi.infra.model.creator.ApiRequestParamsCreator;
import com.newnary.gsp.center.tpsi.infra.model.updater.ApiRequestParamsUpdater;
import com.newnary.gsp.center.tpsi.infra.model.vo.ApiRequestParamsType;
import com.newnary.spring.cloud.domain.Aggregate;
import lombok.Getter;

@Getter
public class ApiRequestParams extends Aggregate {

    private String systemBizId;

    private ApiRequestParamsType apiRequestParamsType;

    private String params;

    public static ApiRequestParams createWith(ApiRequestParamsCreator creator) {
        return new ApiRequestParams(creator);
    }

    public static ApiRequestParams loadWith(ApiRequestParamsCreator creator) {
        return new ApiRequestParams(creator.getId(), creator);
    }

    public void updateWith(ApiRequestParamsUpdater updater) {
        setSystemBizId(updater.getSystemBizId());
        setApiRequestParamsType(ApiRequestParamsType.valueOf(updater.getApiRequestParamsType()));
        setParams(updater.getParams());
    }

    private ApiRequestParams(ApiRequestParamsCreator creator) {
        setSystemBizId(creator.getSystemBizId());
        setApiRequestParamsType(ApiRequestParamsType.valueOf(creator.getApiRequestParamsType()));
        setParams(creator.getParams());
    }

    private ApiRequestParams(Long id, ApiRequestParamsCreator creator) {
        super(id);
        setSystemBizId(creator.getSystemBizId());
        setApiRequestParamsType(ApiRequestParamsType.valueOf(creator.getApiRequestParamsType()));
        setParams(creator.getParams());
    }

    public void setSystemBizId(String systemBizId) {
        this.systemBizId = systemBizId;
    }


    public void setApiRequestParamsType(ApiRequestParamsType apiRequestParamsType) {
        this.apiRequestParamsType = apiRequestParamsType;
    }

    public void setParams(String params) {
        this.params = params;
    }

}
