package com.newnary.gsp.center.tpsi.infra.client.open1688;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.bijia.request.QueryOpen1688MultiLanguageKeywordRequest;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.bijia.request.QueryOpen1688MultiLanguageProductDetailRequest;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.bijia.response.QueryOpen1688MultiLanguageKeywordResponse;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.bijia.response.QueryOpen1688MultiLanguageProductDetailResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

@Slf4j
@Getter
@Setter
public class Open1688CrossProcurementAgencyComparePricesClient extends Open1688Client{

    /**
     * 多语言商详
     * com.alibaba.fenxiao.crossborder/product.search.queryProductDetail/
     */
    private String getMultiLanguageProductDetail;

    /**
     * 多语言关键词搜索
     * com.alibaba.fenxiao.crossborder/product.search.keywordQuery/
     */
    private String getMultiLanguageKeywordQuery;

    public Open1688CrossProcurementAgencyComparePricesClient(String params) {
        super(params);
        setGetMultiLanguageProductDetail("param2/1/com.alibaba.fenxiao.crossborder/product.search.queryProductDetail/".concat(getAppKey()));
        setGetMultiLanguageKeywordQuery("param2/1/com.alibaba.fenxiao.crossborder/product.search.keywordQuery/".concat(getAppKey()));
    }

    // 多语言关键词搜索
    public QueryOpen1688MultiLanguageKeywordResponse getMultiLanguageKeyWords(QueryOpen1688MultiLanguageKeywordRequest request){
        QueryOpen1688MultiLanguageKeywordResponse queryOpen1688DYYKeywordResponse = new QueryOpen1688MultiLanguageKeywordResponse();
        Map<String, String> params = null;
        try {
            params = BeanUtils.describe(request);
            params.put("offerQueryParam", JSON.toJSONString(BeanUtils.describe(request.getOfferQueryParam())));
            String responseStr = callApi(getMultiLanguageKeywordQuery, params);
            if (!"null".equals(responseStr) && StringUtils.isNotEmpty(responseStr)) {
                JSONObject response = JSON.parseObject(responseStr);
                JSONObject result = response.getJSONObject("result");
                if (result.getBoolean("success")){
                    queryOpen1688DYYKeywordResponse = JSON.parseObject(result.toJSONString(), QueryOpen1688MultiLanguageKeywordResponse.class);
                }else {
                    log.info("获取1688多语言关键词搜索：{}",responseStr);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return queryOpen1688DYYKeywordResponse;

    }

    // 多语言商详
    public QueryOpen1688MultiLanguageProductDetailResponse getMultiLanguageProductDetail(QueryOpen1688MultiLanguageProductDetailRequest request){
        QueryOpen1688MultiLanguageProductDetailResponse queryOpen1688DYYProductDetailResponse = new QueryOpen1688MultiLanguageProductDetailResponse();
        Map<String, String> params = null;
        try {
            params = BeanUtils.describe(request);
            params.put("offerDetailParam", JSON.toJSONString(BeanUtils.describe(request.getOfferDetailParam())));
            String responseStr = callApi(getMultiLanguageProductDetail, params);
            if (!"null".equals(responseStr) && StringUtils.isNotEmpty(responseStr)) {
                JSONObject response = JSON.parseObject(responseStr);
                JSONObject result = response.getJSONObject("result");
                if (result.getBoolean("success")){
                    queryOpen1688DYYProductDetailResponse = JSON.parseObject(result.toJSONString(), QueryOpen1688MultiLanguageProductDetailResponse.class);
                }else {
                    log.info("获取1688多语言商品详情列表：{}",responseStr);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return queryOpen1688DYYProductDetailResponse;

    }

}
