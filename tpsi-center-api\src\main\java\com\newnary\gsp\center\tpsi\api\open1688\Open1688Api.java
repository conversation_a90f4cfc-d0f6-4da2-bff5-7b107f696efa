package com.newnary.gsp.center.tpsi.api.open1688;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.gsp.center.tpsi.api.open1688.request.*;
import com.newnary.gsp.center.tpsi.api.open1688.response.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@RequestMapping("tpsi-center/open1688")
public interface Open1688Api {

    //@PathVariable(value = "tenantID") String tenantID, @PathVariable(value = "bizId") String bizId
    @PostMapping("open1688-webhooks/{tenantID}")
    CommonResponse<String> open1688Webhooks(@PathVariable(value = "tenantID") String tenantID,@RequestBody String message);

    //获取1688类目id
    @PostMapping("getCategoryById")
    CommonResponse<QueryCategoryByIdResponse> getCategoryById(String categoryId);
    //1688关键词搜索
    @PostMapping("searchByKeywords")
    CommonResponse<SearchByKeywordsResponse> searchByKeywords(SearchKeyWordsCommand command);
    //根据id获取商品详情
    @PostMapping("queryProductByCustomCode")
    CommonResponse<QueryProductByIdResponse> queryProductByCustomCode(Query1688ProductCommand command);

    //1688图搜接口
    @PostMapping("queryProductImageUrl")
    CommonResponse<QueryOpen1688ProductByImageUrlResponse> queryProductImageUrl(Query1688ProductImageUrlCommand command);

    // 1688多语言关键词搜索
    @PostMapping("queryMultiLanguageKeyword")
    CommonResponse<Query1688MultiLanguageKeywordResponse> queryMultiLanguageKeyWords(Query1688MultiLanguageKeywordCommand command);

    // 1688多语言商详
    @PostMapping("queryMultiLanguageProductDetail")
    CommonResponse<Query1688MultiLanguageProductDetailResponse> queryMultiLanguageProductDetail(Query1688MultiLanguageProductDetailCommand command);
}
