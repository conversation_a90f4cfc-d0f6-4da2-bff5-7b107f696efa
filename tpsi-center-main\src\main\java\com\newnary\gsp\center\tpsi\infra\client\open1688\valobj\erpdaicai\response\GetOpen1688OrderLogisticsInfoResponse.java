package com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class GetOpen1688OrderLogisticsInfoResponse {

    /**
     * 发件人信息
     */
    private Sender sender;

    @Setter
    @Getter
    public static class Sender{

        /**
         * 发件人姓名
         */
        private String senderName;

        /**
         * 发件人电话
         */
        private String senderPhone;

        /**
         * 发件人电话
         */
        private String senderMobile;

        /**
         *
         */
        private String encrypt;

        /**
         * 省编码
         */
        private String senderProvinceCode;

        /**
         * 城市编码
         */
        private String senderCityCode;

        /**
         * 国家编码
         */
        private String senderCountyCode;

        /**
         * 发货人地址
         */
        private String senderAddress;

        /**
         * 省份
         */
        private String senderProvince;

        /**
         * 城市
         */
        private String senderCity;

        /**
         * 国家
         */
        private String senderCounty;

    }

    /**
     *
     */
    private String serviceFeature;

    /**
     * 订单号列表，无子订单的等于主订单编号，否则为对应子订单列表
     */
    private String orderEntryIds;

    /**
     * 物流单号，运单号
     */
    private String logisticsBillNo;

    /**
     * 物流信息ID
     */
    private String logisticsId;

    /**
     * 收件人信息
     */
    private Object receiver;

    @Setter
    @Getter
    public static class Receiver{

        /**
         * 收件人名字
         */
        private String receiverName;

        /**
         * 收件人电话
         */
        private String receiverPhone;

        /**
         * 收件人电话
         */
        private String receiverMobile;

        /**
         *
         */
        private String encrypt;

        /**
         * 省编码
         */
        private String receiverProvinceCode;

        /**
         * 市编码
         */
        private String receiverCityCode;

        /**
         * 国家编码
         */
        private String receiverCountyCode;

        /**
         * 地址
         */
        private String receiverAddress;

        /**
         * 省份
         */
        private String receiverProvince;

        /**
         * 城市
         */
        private String receiverCity;

        /**
         * 国家
         */
        private String receiverCounty;

    }

    /**
     * 物流公司编码
     */
    private String logisticsCompanyName;

    /**
     * 物流状态。WAITACCEPT:未受理;CANCEL:已撤销;ACCEPT:已受理;TRANSPORT:运输中;NOGET:揽件失败;SIGN:已签收;UNSIGN:签收异常
     */
    private String status;

    /**
     * 商品信息
     */
    private List<SendGood> sendGoods;

    @Setter
    @Getter
    public static class SendGood{

        /**
         * 商品名
         */
        private String goodName;

        /**
         * 商品数量
         */
        private String quantity;

        /**
         * 商品单位
         */
        private String unit;
    }

    /**
     * 备注
     */
    private String gmtSystemSend;

    /**
     *
     */
    private String remarks;

    /**
     * 物流公司ID
     */
    private String logisticsCompanyId;
}
