package com.newnary.gsp.center.tpsi.service.alibaba;

import com.alibaba.linkplus.param.AlibabaCrossSimilarOfferSearchParam;
import com.alibaba.linkplus.param.AlibabaCrossSimilarOfferSearchResult;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.distributed.tools.limiter.support.ConfigHelper;
import com.newnary.distributed.tools.limiter.support.DRateLimiterTemplate;
import com.newnary.gsp.center.tpsi.api.externalproduct.request.ExternalProductImageSearchCommand;
import com.newnary.gsp.center.tpsi.api.externalproduct.response.ExternalProductSearchInfo;
import com.newnary.gsp.center.tpsi.infra.client.alibaba.Open1688OceanClient;
import com.newnary.gsp.center.tpsi.service.alibaba.translator.Open1688OceanTranslator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since Created on 2022-05-12
 **/
@Slf4j
@Component
public class OceanProductSearchService extends Open1688OceanClient {

    @Validated
    public ExternalProductSearchInfo imageSearch(@NotNull ExternalProductImageSearchCommand command) {
        return DRateLimiterTemplate.tryAcquire(
                "Open1688O:imageSearch",
                ConfigHelper.perSeconds(2),
                limiter -> limiter.tryAcquire(5, TimeUnit.SECONDS),
                () -> {
                    // 参数转换
                    AlibabaCrossSimilarOfferSearchParam param = Open1688OceanTranslator.transParam(command);

                    // 发送请求
                    AlibabaCrossSimilarOfferSearchResult result = execute(param, "");

                    // 获取结果
                    if (Boolean.TRUE.equals(result.getSuccess())) {
                        return Open1688OceanTranslator.transInfo(result.getResult());
                    } else {
                        log.error("Open1688Ocean>> 以图搜货接口 >> Code=[{}], Message=[{}]", result.getCode(), result.getMessage());
                        throw new ServiceException(CommonErrorInfo.ERROR_100_SYSTEM_ERROR);
                    }
                }
        );
    }

}
