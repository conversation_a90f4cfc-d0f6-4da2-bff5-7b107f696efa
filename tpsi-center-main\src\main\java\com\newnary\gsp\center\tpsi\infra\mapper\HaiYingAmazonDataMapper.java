package com.newnary.gsp.center.tpsi.infra.mapper;


import com.newnary.gsp.center.tpsi.api.haiying.request.amazon.HaiYingAmazonCategoryTreeCommand;
import com.newnary.gsp.center.tpsi.api.haiying.request.amazon.HaiYingAmazonProductDetailInfoCommand;
import com.newnary.gsp.center.tpsi.api.haiying.request.amazon.HaiYingAmazonProductHistoryInfoCommand;
import com.newnary.gsp.center.tpsi.api.haiying.request.amazon.HaiYingAmazonProductListCommand;
import com.newnary.gsp.center.tpsi.api.haiying.response.amazon.HaiYingAmazonCategoryTreeDTO;
import com.newnary.gsp.center.tpsi.api.haiying.response.amazon.HaiYingAmazonProductDetailInfoDTO;
import com.newnary.gsp.center.tpsi.api.haiying.response.amazon.HaiYingAmazonProductHistoryInfoDTO;
import com.newnary.gsp.center.tpsi.api.haiying.response.amazon.HaiYingAmazonProductListDTO;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.amazon.HaiYingAmazonCategoryTreeRequest;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.amazon.HaiYingAmazonProductDetailInfoRequest;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.amazon.HaiYingAmazonProductHistoryInfoRequest;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.amazon.HaiYingAmazonProductListRequest;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.amazon.HaiYingAmazonCategoryTreeResponse;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.amazon.HaiYingAmazonProductDetailInfoResponse;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.amazon.HaiYingAmazonProductHistoryInfoResponse;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.amazon.HaiYingAmazonProductListResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Author: jack
 * @CreateTime: 2022-9-5
 */
@Mapper
public interface HaiYingAmazonDataMapper extends HaiYingDataMapper {

    HaiYingAmazonDataMapper INSTANCE = Mappers.getMapper(HaiYingAmazonDataMapper.class);

    @Mapping(target = "station", source = "station", qualifiedByName = "haiyingStation2Str")
    HaiYingAmazonCategoryTreeRequest transAmazonCategoryTreeRequest(HaiYingAmazonCategoryTreeCommand command);

    @Mapping(target = "station", source = "station", qualifiedByName = "haiyingStation2Str")
    @Mapping(target = "asin", source = "asins", qualifiedByName = "list2StrWithComma")
    @Mapping(target = "fir_arrival_begin", source = "fir_arrival_begin", qualifiedByName = "simpleDateLong2Str")
    @Mapping(target = "fir_arrival_end", source = "fir_arrival_end", qualifiedByName = "simpleDateLong2Str")
    @Mapping(target = "best_seller", source = "best_seller",    qualifiedByName = "boolean2Str")
    @Mapping(target = "prime", source = "prime", qualifiedByName = "boolean2Str")
    @Mapping(target = "amazon_choice", source = "amazon_choice", qualifiedByName = "boolean2Str")
    @Mapping(target = "sign_of_rank_rise", source = "sign_of_rank_rise", qualifiedByName = "boolean2Str")
    @Mapping(target = "last_upd_date_begin", source = "last_upd_date_begin", qualifiedByName = "simpleTimeLong2Str")
    @Mapping(target = "last_upd_date_end", source = "last_upd_date_end", qualifiedByName = "simpleTimeLong2Str")
    @Mapping(target = "merchant_codes", source = "merchant_codes", qualifiedByName = "list2StrWithComma")
    @Mapping(target = "current_page", source = "pageCondition", qualifiedByName = "pageCondition2CurrentPage")
    @Mapping(target = "page_size", source = "pageCondition", qualifiedByName = "pageCondition2PageSize")
    HaiYingAmazonProductListRequest transAmazonProductListRequest(HaiYingAmazonProductListCommand command);

    @Mapping(target = "station", source = "station", qualifiedByName = "haiyingStation2Str")
    @Mapping(target = "asins", source = "asins", qualifiedByName = "list2StrWithComma")
    @Mapping(target = "cate_rank_date_start", source = "cate_rank_date_start", qualifiedByName = "simpleDateLong2Str")
    @Mapping(target = "cate_rank_date_end", source = "cate_rank_date_end", qualifiedByName = "simpleDateLong2Str")
    @Mapping(target = "asin_info_date_start", source = "asin_info_date_start", qualifiedByName = "simpleDateLong2Str")
    @Mapping(target = "asin_info_date_end", source = "asin_info_date_end", qualifiedByName = "simpleDateLong2Str")
    HaiYingAmazonProductDetailInfoRequest transAmazonProductDetailRequest(HaiYingAmazonProductDetailInfoCommand command);

    @Mapping(target = "station", source = "station", qualifiedByName = "haiyingStation2Str")
    @Mapping(target = "asins", source = "asins", qualifiedByName = "list2StrWithComma")
    @Mapping(target = "craw_time_start", source = "craw_time_start", qualifiedByName = "simpleTimeLong2Str")
    @Mapping(target = "craw_time_end", source = "craw_time_end", qualifiedByName = "simpleTimeLong2Str")
    HaiYingAmazonProductHistoryInfoRequest transAmazonProductHistoryRequest(HaiYingAmazonProductHistoryInfoCommand command);



    @Mapping(target = "is_leaf", source = "is_leaf", qualifiedByName = "str2Boolean")
    @Mapping(target = "created_date", source = "created_date", qualifiedByName = "simpleTimeStr2Long")
    @Mapping(target = "enable", source = "enable", qualifiedByName = "str2Boolean")
    HaiYingAmazonCategoryTreeDTO transAmazonCategoryTreeDTO(HaiYingAmazonCategoryTreeResponse response);

    @Mapping(target = "fir_arrival", source = "fir_arrival", qualifiedByName = "simpleDateStr2Long")
    @Mapping(target = "fir_arrival_newest", source = "fir_arrival_newest", qualifiedByName = "simpleDateStr2Long")
    @Mapping(target = "listing_asins", source = "listing_asins", qualifiedByName = "str2ListWithComma")
    @Mapping(target = "best_rank_flag", source = "best_rank_flag", qualifiedByName = "str2Boolean")
    @Mapping(target = "is_ama_choice", source = "is_ama_choice", qualifiedByName = "str2Boolean")
    @Mapping(target = "is_best_seller", source = "is_best_seller", qualifiedByName = "str2Boolean")
    @Mapping(target = "is_prime", source = "is_prime", qualifiedByName = "str2Boolean")
    @Mapping(target = "last_upd_date", source = "last_upd_date", qualifiedByName = "simpleTimeStr2Long")
    @Mapping(target = "not_exist", source = "not_exist", qualifiedByName = "str2Boolean")
    @Mapping(target = "created_date", source = "created_date", qualifiedByName = "simpleDateStr2Long")
    HaiYingAmazonProductListDTO transAmazonProductListDTO(HaiYingAmazonProductListResponse response);

    @Mapping(target = "listing_asins", source = "listing_asins", qualifiedByName = "str2ListWithComma")
    @Mapping(target = "fir_arrival", source = "fir_arrival", qualifiedByName = "simpleDateStr2Long")
    @Mapping(target = "fir_arrival_newest", source = "fir_arrival_newest", qualifiedByName = "simpleDateStr2Long")
    @Mapping(target = "is_ama_choice", source = "is_ama_choice", qualifiedByName = "str2Boolean")
    @Mapping(target = "is_best_seller", source = "is_best_seller", qualifiedByName = "str2Boolean")
    @Mapping(target = "is_prime", source = "is_prime", qualifiedByName = "str2Boolean")
    @Mapping(target = "is_registered_tmview", source = "is_registered_tmview", qualifiedByName = "str2Boolean")
    @Mapping(target = "last_upd_date", source = "last_upd_date", qualifiedByName = "simpleTimeStr2Long")
    @Mapping(target = "is_registered", source = "is_registered", qualifiedByName = "str2Boolean")
    HaiYingAmazonProductDetailInfoDTO transAmazonProductDetailDTO(HaiYingAmazonProductDetailInfoResponse response);

    HaiYingAmazonProductHistoryInfoDTO transAmazonProductHistoryDTO(HaiYingAmazonProductHistoryInfoResponse response);

}
