package com.newnary.gsp.center.tpsi.api.mabang.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class SyncOrderFromMaBangCommand {

    @NotBlank(message = "第三方系统ID（必填）")
    public String thirdPartySystemId;

    public String platformOrderIds;

    /**
     * 1.待处理 2.配货中 3.已发货 4.已完成 5.已作废 6.所有未发货 7.所有非未发货 默认配货中订单 ,示例 ： 2
     */
    @NotBlank(message = "马帮订单状态（必填）")
    public String orderStatus;

    private String updateTimeStart;

    private String updateTimeEnd;

    private String paidtimeStart;

    private String paidtimeEnd;

    private String createDateStart;

    private String createDateEnd;

    private String expressTimeStart;

    private String expressTimeEnd;
}