package com.newnary.gsp.center.tpsi.infra.client.tongtu.valobj.request.erp2;

import lombok.Data;

@Data
public class TongTuOrderImportOrderPaymentInfo {

    /**
     * 订单金额
     */
    private String orderAmount;

    /**
     * 订单金额币种
     */
    private String orderAmountCurrency;

    /**
     * 支付账号
     */
    private String paymentAccount;

    /**
     * 付款时间
     */
    private String paymentDate;

    /**
     * 付款方式
     */
    private String paymentMethod;

    /**
     * 备注
     */
    private String paymentNotes;

    /**
     * 交易流水号
     */
    private String paymentTransactionNum;

    /**
     * 收款账号
     */
    private String recipientAccount;

    /**
     * 相关链接
     */
    private String url;
}
