package com.newnary.gsp.center.tpsi.jt;

import com.alibaba.fastjson.JSONObject;
import com.newnary.gsp.center.tpsi.api.jt.request.cn.*;
import com.newnary.gsp.center.tpsi.api.jt.response.cn.JTCnLogisticsResp;
import com.newnary.gsp.center.tpsi.api.jt.response.cn.JTCnPrintSheetResp;
import com.newnary.gsp.center.tpsi.api.jt.response.cn.JTCnTrackResultResp;
import com.newnary.gsp.center.tpsi.api.jt.vo.cn.CreateOrderCnJTConsignee;
import com.newnary.gsp.center.tpsi.api.jt.vo.cn.CreateOrderCnJTPackage;
import com.newnary.gsp.center.tpsi.api.jt.vo.cn.CreateOrderCnJTShipper;
import com.newnary.gsp.center.tpsi.api.jt.vo.cn.CreateOrderCnJTrDeclare;
import com.newnary.gsp.center.tpsi.service.JT.impl.JTCnLogisticsApiSvelmpl;
import com.newnary.test.starter.BaseTestInjectTenant;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 极兔物流对接
 */
public class JTCnApiTest extends BaseTestInjectTenant {

    private static final SimpleDateFormat SIMPLE_TIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    protected String tenantId() {
        return "TENANT3924117688555169382400";
    }

    @Resource
    private JTCnLogisticsApiSvelmpl jtCn;


    @Test
    public void testCreateOrder() {
        final CreateOrderCnJTCommand command = new CreateOrderCnJTCommand();
        command.setCustomerOrderNo("PLG0001");
        command.setLogisticsProductCode("TH-MP");
//        command.setWaybillNo();
//        command.setDeliveryNo();
        command.setParcelType(10);
//        command.setGoodsCategory();
//        command.setDeclareCurrency();
//        command.setInsuredAmountD();
//        command.setInsuredCurrency();
        command.setCodCurrency("THB");
        command.setTaxPayMode(10);
//        command.setIossTaxType(10);
//        command.setIossNo();
//        command.setEoriNo();
//        command.setVatNo();
//        command.setVatCompanyEnName();
//        command.setVatRegisterCountry();
//        command.setVatRegisterAddress();
//        command.setSalesUrl();
//        command.setCustomerRemark();
        final CreateOrderCnJTShipper shipper = new CreateOrderCnJTShipper();
        shipper.setShipperName("test");
//        shipper.setShipperCompany();
        shipper.setShipperCountry("CN");
        shipper.setShipperProvince("广东");
        shipper.setShipperCity("广州");
//        shipper.setShipperDistrict();
        shipper.setShipperAddress("白云");
//        shipper.setShipperDoorplate();
//        shipper.setShipperStreet();
//        shipper.setShipperPostcode();
        shipper.setShipperPhone("0842362453");
//        shipper.setShipperEmail();

        command.setShipper(shipper);

        final CreateOrderCnJTConsignee consignee = new CreateOrderCnJTConsignee();
        consignee.setConsigneeName("PaiFeiGu2");
//        consignee.setConsigneeCompany();
        consignee.setConsigneeCountry("TH");
        consignee.setConsigneeProvince("อุบลราชธานี");
        consignee.setConsigneeCity("อุบลราชธานี");
        consignee.setConsigneeDistrict("สิรินธร");
        consignee.setConsigneeAddress("manila");
//        consignee.setConsigneeAddress2();
//        consignee.setConsigneeDoorplate();
//        consignee.setConsigneeStreet();
        consignee.setConsigneePostcode("34350");
        consignee.setConsigneePhone("0842362453");
//        consignee.setConsigneeEmail();
//        consignee.setConsigneeIdcard();
//        consignee.setConsigneeTaxNo();
//
        command.setConsignee(consignee);

        List<CreateOrderCnJTrDeclare> orderDeclareList = new ArrayList<>();
        final CreateOrderCnJTrDeclare declare = new CreateOrderCnJTrDeclare();
        declare.setEnglishName("yimi");
        declare.setChineseName("薏米");
        declare.setQuantity(5);
//        declare.setBrand();
//        declare.setGoodsBarcode();
        declare.setUnitNetWeightD(BigDecimal.valueOf(14));
        declare.setUnitDeclarePriceD(BigDecimal.valueOf(15));
//        declare.setHsCode();
//        declare.setProductModel();
//        declare.setMaterial();
//        declare.setPurpose();
//        declare.setSku();
//        declare.setPickingRemark();
//        declare.setProductUrl();
//        declare.setOrigin();
        orderDeclareList.add(declare);
        command.setOrderDeclareList(orderDeclareList);


        List<CreateOrderCnJTPackage> packageList = new ArrayList<>();
        final CreateOrderCnJTPackage jtPackage = new CreateOrderCnJTPackage();
        jtPackage.setPackageLengthD(BigDecimal.valueOf(1));
        jtPackage.setPackageWidthD(BigDecimal.valueOf(1));
        jtPackage.setPackageHeightD(BigDecimal.valueOf(1));
        jtPackage.setPackageWeightD(BigDecimal.valueOf(1));
        packageList.add(jtPackage);

        command.setPackageList(packageList);

        jtCn.createOrder(command);
    }



    @Test
    public void testCreateOrderByJson() {
        JSONObject jsonObject = JSONObject.parseObject("{\"data\":{\"requested_tracking_id\":\"\",\"tracking_id\":\"PRO231143094472434405376\",\"delivery\":{\"delivery_start_date\":\"2023-08-23\",\"allow_self_collection\":false}}}");
        System.out.println(jsonObject);
    }



    @Test
    public void testPrintSheet() {
        final SheetOrderCnJTCommand command = new SheetOrderCnJTCommand();
        command.setNos(Collections.singletonList("TTOI204404846166077"));

        final JTCnPrintSheetResp jtCnPrintSheetResp = jtCn.printSheet(command);
        System.out.println(JSONObject.toJSONString(jtCnPrintSheetResp));
    }


    @Test
    public void testQueryLogistics() {
        final List<JTCnLogisticsResp> jtCnLogisticsResps = jtCn.queryLogistics();
        System.out.println(JSONObject.toJSONString(jtCnLogisticsResps));
    }


    @Test
    public void testQueryTrack() {
        final TrackOrderCnJTCommand command = new TrackOrderCnJTCommand();
        command.setNos("GZTL2410071996602,GZTL2410071996590");
        command.setNoType(1);

        final List<JTCnTrackResultResp> respList = jtCn.queryTrack(command);
        System.out.println(JSONObject.toJSONString(respList));
    }


    @Test
    public void testCancelOrder() {
        final CancelOrderCnJTCommand command = new CancelOrderCnJTCommand();
        command.setCustomerOrderNo("PLG0001");

        jtCn.cancelOrder(command);
    }


    @Test
    public void testQueryOrderBill() {
        final BillOrderCnJTCommand command = new BillOrderCnJTCommand();
        command.setCustomerOrderNo("PLG0001");

        jtCn.queryBill(command);
    }










}
