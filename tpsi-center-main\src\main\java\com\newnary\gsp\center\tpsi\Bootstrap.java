package com.newnary.gsp.center.tpsi;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.newnary.spring.cloud.NewnaryApplication;
import com.newnary.spring.cloud.NewnaryCloudApplication;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@NewnaryCloudApplication
@EnableTransactionManagement
@EnableApolloConfig(value = {
        "newnary.discovery",
        "newnary.distribute",
        "newnary.oss",
        "newnary.mq",
        "newnary.job"})
@MapperScan(basePackages = "com.newnary.gsp.center.tpsi.infra.repository.db.dao")
public class Bootstrap {

    public static void main(String[] args) {
        NewnaryApplication.run(Bootstrap.class, args);
    }
}
