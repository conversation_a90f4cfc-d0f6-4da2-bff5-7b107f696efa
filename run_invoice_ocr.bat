@echo off
chcp 65001 >nul
echo ================================================
echo           发票OCR识别工具
echo ================================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7或更高版本
    pause
    exit /b 1
)

echo 当前Python版本:
python --version
echo.

REM 检查是否存在invoices文件夹
if not exist "invoices" (
    echo 创建invoices文件夹...
    mkdir invoices
    echo 请将发票文件放入invoices文件夹中，然后重新运行此脚本
    echo 支持的格式: PDF, PNG, JPG, JPEG, BMP, TIFF
    pause
    exit /b 0
)

REM 检查invoices文件夹是否为空
dir /b invoices | findstr . >nul
if errorlevel 1 (
    echo invoices文件夹为空，请放入发票文件后重新运行
    pause
    exit /b 0
)

echo 找到发票文件夹，开始处理...
echo.

REM 运行OCR处理
python invoice_ocr_processor.py invoices -o 发票识别结果.xlsx

if errorlevel 1 (
    echo.
    echo 处理过程中出现错误，请检查:
    echo 1. 是否已安装所有依赖包 (运行: pip install -r requirements.txt)
    echo 2. 发票文件格式是否正确
    echo 3. 查看invoice_ocr.log日志文件了解详细错误信息
) else (
    echo.
    echo ================================================
    echo 处理完成！结果已保存到: 发票识别结果.xlsx
    echo ================================================
    
    REM 询问是否打开结果文件
    set /p choice="是否打开结果文件? (y/n): "
    if /i "%choice%"=="y" (
        start 发票识别结果.xlsx
    )
)

echo.
pause
