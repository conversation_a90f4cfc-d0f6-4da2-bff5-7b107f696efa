package com.newnary.gsp.center.tpsi.api.open1688.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class QueryProductByImageUrlResponse {

    private List<QueryProductByImageUrlResult> result;

    private PageInfo pageInfo;

    @Setter
    @Getter
    public static class PageInfo{
        /**
         * 总页数
         */
        private Long totalPage;

        /**
         * 当前页
         */
        private Long currentPage;

        /**
         * 每页大小
         */
        private Long pageSize;

        /**
         * 总数据量
         */
        private Long totalRecords;
    }
}
