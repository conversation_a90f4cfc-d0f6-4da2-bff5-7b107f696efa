package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.ebay;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingEbayCategoryDetailRequest {

    /**
     * 站点
     */
    @NotNull(message = "站点不能为空")
    private String station;

    /**
     * 类目id(string型)
     */
    private String cid;

    /**
     * 商品总数起始值(int 型)
     */
    private String all_product_count_start;

    /**
     * 商品总数结束值(int 型)
     */
    private String all_product_count_end;

    /**
     * 店铺总数起始值(int 型)
     */
    private String merchant_count_start;

    /**
     * 店铺总数结束值(int 型)
     */
    private String merchant_count_end;

    /**
     * 前1天类目销售件数起始值(int 型)
     */
    private String sold_the_previous_day_start;

    /**
     * 前1天类目销售件数结束值(int 型)
     */
    private String sold_the_previous_day_end;

    /**
     * 前1天类目销售金额起始值(double型)
     */
    private String payment_the_previous_day_start;

    /**
     * 前1天类目销售金额结束值(double型)
     */
    private String payment_the_previous_day_end;

    /**
     * 前1天销售增幅起始值(int 型)
     */
    private String sold_the_previous_growth_start;

    /**
     * 前1天销售增幅结束值(int 型)
     */
    private String sold_the_previous_growth_end;

    /**
     * 前3天类目销售件数起始值(int 型)
     */
    private String sales_three_day1_start;

    /**
     * 前3天类目销售件数结束值(int 型)
     */
    private String sales_three_day1_end;

    /**
     * 前3天类目销售金额起始值(double型)
     */
    private String payment_three_day1_start;

    /**
     * 前3天类目销售金额结束值(double型)
     */
    private String payment_three_day1_end;

    /**
     * 前3天销售增幅起始值(int 型)
     */
    private String sales_three_day_growth_start;

    /**
     * 前3天销售增幅结束值(int 型)
     */
    private String sales_three_day_growth_end;

    /**
     * 排序方式:
     * all_product_count(商品总数)
     * merchant_count(店铺总数)
     * sold_the_previous_day(前1天类目销售件数)
     * payment_the_previous_day(前1天类目销售金额)
     * sold_the_previous_growth(前1天类目销售增幅)
     * sales_three_day1(前3天类目销售件数)
     * payment_three_day1(前3天类目销售金额)
     * sales_three_day_growth(前3天类目销售增幅)
     */
    private String order_by;

    /**
     * 排序类型:
     * ASC 升序
     * DESC 降序
     */
    private String order_by_type;

    /**
     * 当前页码(int 型)
     */
    private String current_page;

    /**
     * 每一页的数据量(默认海鹰设置 全部)(int 型)
     * 数值范围[1-10000]
     */
    private String page_size;
}
