package com.newnary.gsp.center.tpsi.api.haiying;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.api.base.common.PageList;
import com.newnary.gsp.center.tpsi.api.haiying.request.shopee.*;
import com.newnary.gsp.center.tpsi.api.haiying.response.shopee.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-13
 */
@RequestMapping("tpsi-center/haiying/shopee")
public interface HaiYingShopeeApi {

    @PostMapping("getShopeeKeywordList")
    CommonResponse<PageList<HaiYingShopeeKeywordInfoDTO>> getShopeeKeywordList(@RequestBody HaiYingShopeeKeywordListCommand command);

    @PostMapping("getShopeeKeywordInfo")
    CommonResponse<PageList<HaiYingShopeeKeywordInfoDTO>> getShopeeKeywordInfo(@RequestBody HaiYingShopeeKeywordInfoCommand command);

    @PostMapping("getShopeeProductList")
    CommonResponse<PageList<HaiYingShopeeProductListDTO>> getShopeeProductList(@RequestBody HaiYingShopeeProductListCommand command);

    @PostMapping("getShopeeProductDetailInfo")
    CommonResponse<List<HaiYingShopeeProductDetailInfoDTO>> getShopeeProductDetailInfo(@RequestBody HaiYingShopeeProductDetailInfoCommand command);

    @PostMapping("getShopeeProductExtInfo")
    CommonResponse<List<HaiYingShopeeProductExtInfoDTO>> getShopeeProductExtInfo(@RequestBody HaiYingShopeeProductExtInfoCommand command);

    @PostMapping("getShopeeProductHistoryInfo")
    CommonResponse<List<HaiYingShopeeProductHistoryInfoDTO>> getShopeeProductHistoryInfo(@RequestBody HaiYingShopeeProductHistoryInfoCommand command);

    @PostMapping("getShopeeCategoryTree")
    CommonResponse<List<HaiYingShopeeCategoryTreeDTO>> getShopeeCategoryTree(@RequestBody HaiYingShopeeCategoryTreeCommand command);

    @PostMapping("getShopeeTopCategoryInfo")
    CommonResponse<List<HaiYingShopeeTopCategoryInfoDTO>> getShopeeTopCategoryInfo(@RequestBody HaiYingShopeeTopCategoryInfoCommand command);

    @PostMapping("getShopeeSubCategoryInfo")
    CommonResponse<PageList<HaiYingShopeeSubCategoryInfoDTO>> getShopeeSubCategoryInfo(@RequestBody HaiYingShopeeSubCategoryInfoCommand command);

}
