package com.newnary.gsp.center.tpsi.infra.mq.consumer;

import com.newnary.messagebody.wms.WMSBatchStockTopic;
import com.newnary.mq.starter.consumer.AbstractMQConsumer;
import org.springframework.stereotype.Component;

@Component
public class WmsStockChangeSyncConsumer extends AbstractMQConsumer {

    @Override
    public String topic() {
        return WMSBatchStockTopic.TOPIC;
    }

    @Override
    public String consumerGroup() {
        return WMSBatchStockTopic.TOPIC;
    }
}
