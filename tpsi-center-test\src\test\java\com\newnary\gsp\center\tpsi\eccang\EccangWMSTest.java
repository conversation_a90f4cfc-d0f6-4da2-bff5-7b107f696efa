package com.newnary.gsp.center.tpsi.eccang;

import com.alibaba.fastjson.JSON;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.tpsi.infra.client.eccang.EcCangWMSApiClient;
import com.newnary.gsp.center.tpsi.infra.client.eccang.params.EcCangWMSParams;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.wms.*;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.wms.EcCangFreightDealOrderSendRequestResponse;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.utils.Base64Util;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.service.eccang.IEccangTMSApiSve;
import com.newnary.gsp.center.tpsi.service.eccang.IEccangWMSApiSve;
import com.newnary.test.starter.BaseTestInjectTenant;
import lombok.Getter;
import lombok.Setter;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: jack
 * @CreateTime: 2025/5/9
 */
public class EccangWMSTest extends BaseTestInjectTenant {

    @Override
    protected String tenantId() {
        return "TENANT3924117688555169382400";
    }

    @Resource
    private IEccangWMSApiSve eccangWMSApiSve;
    @Resource
    private IEccangTMSApiSve eccangTMSApiSve;

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Test
    public void testGetWMSWarehouse() {
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGWMS0001");
        EcCangApiBaseResult<String> result = eccangWMSApiSve.getWarehouse(thirdPartySystem);
        System.out.println(result.toString());
    }

    @Test
    public void testGetProductStock() {
        EcCangGetProductStockRequest req = new EcCangGetProductStockRequest();
        req.setProduct_sku("SK2598259091467551772673");
        req.setPage(1);
        req.setPageSize(1);
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGWMS0001");
        EcCangApiBaseResult<String> result = eccangWMSApiSve.getProductStock(req, thirdPartySystem);
        System.out.println(result.toString());
    }

    @Test
    public void testCreateOrder() {
        EcCangCreateOrderRequest req = new EcCangCreateOrderRequest();
        req.setReference_no("PO5594298568773689020416");
        req.setShipping_method("自提");
        req.setWarehouse_code("HRBW");
        req.setCountry_code("PH");
        req.setAddress1("菲律宾");
        req.setZipcode("010");
        req.setName("name");
        req.setPhone("10086");
        List<EcCangCreateOrderRequest.Item> items = new ArrayList<>();
        EcCangCreateOrderRequest.Item item = new EcCangCreateOrderRequest.Item();
        item.product_sku = "PSI3644298550163050467328";
        item.quantity = 3;
        items.add(item);
        req.setItems(items);
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGWMS0001");
        EcCangApiBaseResult<String> result = eccangWMSApiSve.createOrder(req, thirdPartySystem);
        System.out.println(result.toString());
    }

    @Test
    public void testCancelOrder() {
        EcCangCancelOrderRequest req = new EcCangCancelOrderRequest();
        req.setOrder_code("PO5594298568773689020416");
        req.setReason("原因");
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGWMS0002");
        EcCangApiBaseResult<String> result = eccangWMSApiSve.cancelOrder(req, thirdPartySystem);
        System.out.println(result.toString());
    }

    @Test
    public void testGetOrderState() {
        EcCangGetOrderStateRequest req = new EcCangGetOrderStateRequest();
        req.setOrder_code("PK2302120030490");
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGWMS0002");
        EcCangApiBaseResult<String> result = eccangWMSApiSve.getOrderState(req, thirdPartySystem);
        List list = JSON.parseObject(result.getData(), List.class);

        System.out.println(list.toString());
    }

    @Test
    public void testGetOrderTracking() {
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGWMS0002");
        EcCangApiBaseResult<String> result = eccangWMSApiSve.getOrderTracking("PK2302120030490", thirdPartySystem);
        List list = JSON.parseObject(result.getData(), List.class);
        System.out.println(list.toString());
    }

    @Test
    public void testGetOrderList() {
        EcCangGetOrderListRequest req = new EcCangGetOrderListRequest();
        req.setOrder_status("D");
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGWMS0001");
        EcCangApiBaseResult<String> result = eccangWMSApiSve.getOrderList(req, thirdPartySystem);
        List list = JSON.parseObject(result.getData(), List.class);
        List<String> order_codeList = (List<String>) list.stream().map(entry -> JSON.parseObject(entry.toString()).getString("order_code")).collect(Collectors.toList());
        System.out.println(order_codeList.toString());
    }

    //发货请求
    @Test
    public void testDealOrderSendRequest() {
        EcCangFreightDealOrderSendRequest req = new EcCangFreightDealOrderSendRequest();
        req.setWarehouse_id(4);
        req.setOrder_code("JYRH-230427-0005");
        List<EcCangFreightDealOrderSendRequest.Product> list = new ArrayList<>();
        EcCangFreightDealOrderSendRequest.Product product = new EcCangFreightDealOrderSendRequest.Product();
        list.add(product);
        req.setProduct_data(list);
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGWMS0001");
        EcCangApiBaseResult<String> result = eccangWMSApiSve.dealOrderSendRequest(req, thirdPartySystem);
        EcCangFreightDealOrderSendRequestResponse ecCangFreightDealOrderSendRequestResponse = JSON.parseObject(result.getData(), EcCangFreightDealOrderSendRequestResponse.class);
        System.out.println(JSON.toJSONString(ecCangFreightDealOrderSendRequestResponse));
    }

    @Test
    public void testGetShippingMethodRequest() {
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGWMS0002");
        EcCangApiBaseResult<String> shippingMethod = eccangWMSApiSve.getShippingMethod(new EcCangGetShippingMethodRequest(), thirdPartySystem);
        System.out.println(shippingMethod.getData());
    }

    @Test
    public void testCreateReturnBill() {
        EcCangCreateReturnBillRequest ecCangCreateReturnBillRequest = new EcCangCreateReturnBillRequest();
        ecCangCreateReturnBillRequest.setTracking_no("F00-000000-0001");
        ecCangCreateReturnBillRequest.setWarehouse_code("PGLPH01");
        ecCangCreateReturnBillRequest.setReturn_type("S");
        ecCangCreateReturnBillRequest.setVerify("0");
        EcCangCreateReturnBillRequest.Items items = new EcCangCreateReturnBillRequest.Items();
        items.product_sku = "SS2952302181825307086848";
        items.quantity = 10;
        items.process = "1";
        ecCangCreateReturnBillRequest.setItems(items);
        EcCangCreateReturnBillRequest.Images images = new EcCangCreateReturnBillRequest.Images();
        images.file_data = "https://img-home.csdnimg.cn/images/20230220093713.jpg";
        images.file_type = "jpg";
        ecCangCreateReturnBillRequest.setImages(images);
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGWMS0002");
        EcCangApiBaseResult<String> result = eccangWMSApiSve.createReturnBill(ecCangCreateReturnBillRequest, thirdPartySystem);
        System.out.println(result);
        //EcCangApiBaseResult(code=Failure, data={"errCode":0,"ask":"Failure","Error":"","message":"Product:[R] is Error","errMessage":""}, message=Product:[R] is Error, totalCount=0, page=null, pageSize=null, error=null, responseTime=null)
    }

    @Test
    public void testUpdateReturnBill() {
        EcCangUpdateReturnBillRequest ecCangUpdateReturnBillRequest = new EcCangUpdateReturnBillRequest();
        ecCangUpdateReturnBillRequest.setReturn_code("RMA5-161221-0001");
        ecCangUpdateReturnBillRequest.setTracking_no("F00-000000-0001");
        ecCangUpdateReturnBillRequest.setWarehouse_code("PGLPH01");
        ecCangUpdateReturnBillRequest.setReturn_type("2");
        EcCangUpdateReturnBillRequest.Items items = new EcCangUpdateReturnBillRequest.Items();
        items.product_sku = "SS2952302181825307086848";
        items.quantity = 10;
        items.process = "1";
        ecCangUpdateReturnBillRequest.setItems(items);
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGWMS0002");
        EcCangApiBaseResult<String> result = eccangWMSApiSve.updateReturnBill(ecCangUpdateReturnBillRequest, thirdPartySystem);
        System.out.println(result.toString());
        //EcCangApiBaseResult(code=Failure, data={"errCode":0,"ask":"Failure","Error":"","message":"return_code:RMA5-161221-0001 is Error.","errMessage":""}, message=return_code:RMA5-161221-0001 is Error., totalCount=0, page=null, pageSize=null, error=null, responseTime=null)
    }

    @Test
    public void testGetReturnBill() {
        EcCangGetReturnBillRequest ecCangGetReturnBillRequest = new EcCangGetReturnBillRequest();
        ecCangGetReturnBillRequest.setReturn_code("RMA5-161221-0001");
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGWMS0002");
        EcCangApiBaseResult<String> result = eccangWMSApiSve.getReturnBill(ecCangGetReturnBillRequest, thirdPartySystem);
        System.out.println(result.toString());
        //EcCangApiBaseResult(code=Failure, data={"errCode":404,"ask":"Failure","Error":"","message":"","errMessage":"The Return Order:RMA5-161221-0001 is not find ."}, message=, totalCount=0, page=null, pageSize=null, error=null, responseTime=null)
    }

    @Test
    public void testUploadFileAndFile() {
        EcCangUploadFileRequest ecCangUploadFileRequest = new EcCangUploadFileRequest();
        EcCangSaveOrderAttachRequest ecCangSaveOrderAttachRequest = new EcCangSaveOrderAttachRequest();
        EcCangSaveOrderAttachRequest.Attach attach = new EcCangSaveOrderAttachRequest.Attach();
        List<EcCangSaveOrderAttachRequest.Attach> list = new ArrayList<>();

        ecCangUploadFileRequest.setFile_type("pdf");
        ecCangUploadFileRequest.setFile_data(Base64Util.imageUrlToBase64("https://img-home.csdnimg.cn/images/20230220093713.jpg"));
        ecCangUploadFileRequest.setModule("order_attach");
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGWMS0002");
        EcCangApiBaseResult<String> result = eccangWMSApiSve.uploadFile(ecCangUploadFileRequest, thirdPartySystem);

/*        JSONObject resultObject = JSON.parseObject(result.getData());
        attach.attach_id = resultObject.getString("attach_id");
        attach.file_type = "pdf";
        list.add(attach);
        ecCangSaveOrderAttachRequest.setAttach(list);
        ecCangSaveOrderAttachRequest.setOrder_id("1234");
        EcCangApiBaseResult<String> response = eccangWMSApiSve.saveOrderAttach(ecCangSaveOrderAttachRequest, "TESTECCANGWMS0002");*/

        //System.out.println(response.toString());
    }

    @Test
    public void testModifyOrder() {
        EcCangModifyOrderRequest ecCangModifyOrderRequest = new EcCangModifyOrderRequest();
        ecCangModifyOrderRequest.setAddress1("1");
        ecCangModifyOrderRequest.setOrder_code("1222");
        ecCangModifyOrderRequest.setReference_no("1222");
        ecCangModifyOrderRequest.setShipping_method("TT");
        ecCangModifyOrderRequest.setWarehouse_code("TT");
        ecCangModifyOrderRequest.setCountry_code("PH");
        ecCangModifyOrderRequest.setZipcode("1000");
        ecCangModifyOrderRequest.setName("zz");
        List<EcCangModifyOrderRequest.Item> list = new ArrayList<>();
        EcCangModifyOrderRequest.Item item = new EcCangModifyOrderRequest.Item();
        item.product_sku = "SS2952302181825307086848";
        item.quantity = 10;
        list.add(item);
        ecCangModifyOrderRequest.setItems(list);
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGWMS0002");
        EcCangApiBaseResult<String> result = eccangWMSApiSve.modifyOrder(ecCangModifyOrderRequest, thirdPartySystem);
        System.out.println(result.toString());
    }

    //测试获取wms异常单
    @Test
    public void testGetAbnormalInfo() {
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGWMS0001");
        EcCangWMSParams params = JSON.parseObject(thirdPartySystem.getParams(), EcCangWMSParams.class);
        EcCangWMSApiClient ecCangWMSApiClient = new EcCangWMSApiClient(params);
/*      JSONObject jsonObject = new JSONObject();
        //jsonObject.put("package_code","2");
        JSONArray objects = new JSONArray();
        objects.add("JYDEMO-230718-0008");
        //jsonObject.put("order_code","JYDEMO-230718-0008");
        jsonObject.put("package_code","2");
        jsonObject.put("warehouse_id","4");
        jsonObject.put("page_no","1");*/
        GetAbnormalInfoRequest getAbnormalInfoRequest = new GetAbnormalInfoRequest();
        getAbnormalInfoRequest.setOrder_code("JYDEMO-230718-0008");
        getAbnormalInfoRequest.setPage_no(1);
        getAbnormalInfoRequest.setWarehouse_id("4");
        EcCangApiBaseResult<String> result = ecCangWMSApiClient.sendFreightRequest(getAbnormalInfoRequest, "/freight/freight/get-abnormal-info", "FREIGHT");
        //EcCangApiBaseResult<String> result = ecCangWMSApiClient.sendFreightRequest(jsonObject, "/freight/freight/get-order-info","FREIGHT");
        System.out.println(JSON.toJSONString(result.getData()));
    }

    @Setter
    @Getter
    public static class GetAbnormalInfoRequest {
        private String warehouse_id;
        private Integer page_no;
        private String package_code;
        private String order_code;
        private String child_code;
        private String from_datetime;
        private String to_datetime;
        private Integer time_type;
    }

    @Test
    public void testGetTMSOrder() {
        ThirdPartySystem thirdPartySystem = loadSystem("TESTECCANGTMS0001");
        EcCangApiBaseResult<Object> objectEcCangApiBaseResult = eccangTMSApiSve.queryOrderInfo(thirdPartySystem, "PO2504313732196802367488", "{\"thirdPartySystemId\" : \"TESTECCANGTMS0001\"}");
        try {
            Thread.sleep(5000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    private ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }

}
