package com.newnary.gsp.center.tpsi.app.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.common.utils.locale.LanguageLocaleType;
import com.newnary.gsp.center.product.api.common.dto.MultimediaInfo;
import com.newnary.gsp.center.product.api.common.dto.MultimediaType;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierSkuStopSupplyReq;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierSpuOptSupplyStateReq;
import com.newnary.gsp.center.product.api.product.enums.SupplierSkuSaleState;
import com.newnary.gsp.center.product.api.product.request.*;
import com.newnary.gsp.center.product.api.product.vo.SupplierSpuDescInfo;
import com.newnary.gsp.center.tpsi.infra.client.eccang.params.EcCangWMSParams;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.erp.EcCangERPGetProductListResponse;
import com.newnary.gsp.center.tpsi.infra.client.lingxing.params.LingXingDataParam;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingInfo;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingType;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.ThirdPartyMappingManager;
import com.newnary.gsp.center.tpsi.infra.rpc.OpenSupplierProductRpc;
import com.newnary.gsp.center.tpsi.service.lingxing.ILingXingApiService;
import com.newnary.gsp.center.tpsi.service.lingxing.request.QueryLingXingProductInfoRequest;
import com.newnary.gsp.center.tpsi.service.lingxing.request.QueryLingXingProductListRequest;
import com.newnary.gsp.center.tpsi.service.lingxing.response.QueryLingXingProductInfoResponse;
import com.newnary.gsp.center.tpsi.service.lingxing.response.QueryLingXingProductListResponse;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.job.core.handler.annotation.Job;
import com.newnary.tenant.context.TenantCarrier;
import com.newnary.tenant.context.TenantID;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.*;

@Component
@Slf4j
public class LingXingERPJobManager {

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Resource
    private OpenSupplierProductRpc openSupplierProductRpc;

    @Resource
    private ThirdPartyMappingManager thirdPartyMappingManager;

    @Resource
    private ILingXingApiService iLingXingApiServiceImpl;

    private final ThreadPoolExecutor processExecutor = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors() * 20,
            Runtime.getRuntime().availableProcessors() * 20,
            20L, TimeUnit.SECONDS,
            new LinkedBlockingDeque<>(100),
            new ThreadPoolExecutor.CallerRunsPolicy());

    String categoryId ;
    @Job("autoSyncLingXingERPProduct")
    public ReturnT<String> syncLingXingERPProduct(String param) {
        log.info("同步领星商品定时任务开始, param={}", param);
        try {
            //根据参数获取需要执行的第三方系统id
            JSONObject paramObject = JSONObject.parseObject(param);
            String thirdPartySystemId = paramObject.getString("thirdPartySystemId");
            Integer perGroupCount = paramObject.getInteger("perGroupCount");
            categoryId = paramObject.getString("categoryId");

            //指定插入页数
            Integer insertSize = paramObject.getInteger("insertSize");
            //指定从第几页开始插入
            Integer startOffset = paramObject.getInteger("startOffset");
            //指定每页条数
            Integer pageSize = paramObject.getInteger("pageSize");
            //根据thirdPartySystemId获取第三方系统参数
            ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);

            LingXingDataParam lingXingDataParam = JSON.parseObject(thirdPartySystem.getParams(), LingXingDataParam.class);

            QueryLingXingProductListRequest queryLingXingProductListRequest = new QueryLingXingProductListRequest();

            int offset = 0;
            if (startOffset != null && startOffset > 0) {
                offset = startOffset;
            }
            queryLingXingProductListRequest.setLength(pageSize);

            List<QueryLingXingProductListResponse.Product> productList = null;
            do {
                queryLingXingProductListRequest.setOffset(offset);
                log.info("当前偏移量为：{}",offset);
                QueryLingXingProductListResponse queryLingXingProductListResponse = iLingXingApiServiceImpl.queryProductList(lingXingDataParam, queryLingXingProductListRequest);
                if (queryLingXingProductListResponse == null || queryLingXingProductListResponse.getCode() != 0 || CollectionUtils.isEmpty(queryLingXingProductListResponse.getData())) {
                    productList = null;
                    continue;
                }
                productList = queryLingXingProductListResponse.getData();
                //processBatch(productList,perGroupCount,lingXingDataParam);
                log.info("领星商品列表：商品数量{}",productList.size());
                syncProduct(productList,lingXingDataParam);
                productList = ((startOffset != null && startOffset > 0) ? (offset - startOffset)>=insertSize : offset >= insertSize) ? null : productList;
                offset += pageSize;
            }while (CollectionUtils.isNotEmpty(productList));
        } catch (Exception e) {
            log.error("领星商品同步异常：{}",e.getMessage(),e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    //使用多线程进行同步
    private void processBatch(List<QueryLingXingProductListResponse.Product> productList, Integer perGroupCount, LingXingDataParam params) throws Exception {
        final Optional<TenantID> tenantIdKept = TenantCarrier.getTenantID();
        // 1. 批量任务分组
        //List<String> skuIdList = productList.stream().map(EcCangERPGetProductListResponse::getProductSku).collect(Collectors.toList());
        List<List<QueryLingXingProductListResponse.Product>> groupSkuIds = ListUtils.partition(productList, perGroupCount);
        CountDownLatch processCountDownLatch = new CountDownLatch(groupSkuIds.size());

        // 2. 线程池执行
        groupSkuIds.forEach(groupItem -> {

            CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> {
                boolean tenantIdMiss = false;
                try {
                    if (!TenantCarrier.getTenantID().isPresent()) {
                        TenantID tenantID = tenantIdKept.orElse(null);
                        TenantCarrier.setTenantID(tenantID);
                        tenantIdMiss = tenantID != null;
                    }
                    syncProduct(groupItem, params);
                } catch (Exception e) {
                    log.error("[{}] 领星商品同步异常！e={}", "领星商品同步", e);
                } finally {
                    processCountDownLatch.countDown();
                    if (tenantIdMiss) {
                        TenantCarrier.clearTenantID();
                    }
                }
            }, processExecutor);

        });

        // 3. 等待批次商品同步处理完成
        processCountDownLatch.await();
    }

    public void syncProduct(List<QueryLingXingProductListResponse.Product> productList, LingXingDataParam lingXingDataParam) {
        productList.forEach(sku -> {
            ThirdPartyMappingInfo newestInfoByTarget = thirdPartyMappingManager.getNewestInfoByTarget("LINGXING", "GSP", sku.getSku(), ThirdPartyMappingType.PRODUCT_ID);
            if (ObjectUtils.isNotEmpty(newestInfoByTarget)) return;
            try {
                QueryLingXingProductInfoRequest queryLingXingProductInfoRequest = new QueryLingXingProductInfoRequest();
                queryLingXingProductInfoRequest.setSku(sku.getSku());
                QueryLingXingProductInfoResponse queryLingXingProductInfoResponse = iLingXingApiServiceImpl.queryProductInfo(lingXingDataParam, queryLingXingProductInfoRequest);
                if (queryLingXingProductInfoResponse != null && queryLingXingProductInfoResponse.getCode() == 0 && queryLingXingProductInfoResponse.getData() != null) {
                    QueryLingXingProductInfoResponse.ProductInfo productInfo = queryLingXingProductInfoResponse.getData();
                    String spuId = openSupplierProductRpc.createSpu4StockAsync(buildSupplierSpuCreateV2Command(lingXingDataParam, productInfo));
                    if (spuId != null) {
                        thirdPartyMappingManager.insertOrUpdate("GSP", "LINGXING", spuId, sku.getSku(), ThirdPartyMappingType.PRODUCT_ID.name(), queryLingXingProductInfoResponse.getData());
                        if (0 == sku.getStatus()) {
                            OpenSupplierSpuOptSupplyStateReq openSupplierSpuOptSupplyStateReq = new OpenSupplierSpuOptSupplyStateReq();
                            openSupplierSpuOptSupplyStateReq.setSupplierId(lingXingDataParam.getSupplierId());
                            openSupplierSpuOptSupplyStateReq.setCustomCode(sku.getSku());
                            openSupplierProductRpc.stopSpuSupply(openSupplierSpuOptSupplyStateReq);
                            OpenSupplierSkuStopSupplyReq req = new OpenSupplierSkuStopSupplyReq();
                            req.setSupplierId(lingXingDataParam.getSupplierId());
                            req.setCustomSkuCode(sku.getSku());
                            openSupplierProductRpc.stopSupply(req);
                        }
                    }
                } else {
                    log.info("LingXing商品信息为空：sku:{}",sku.getSku());
                }

            } catch (Exception e) {
                log.error("LINGXING创建商品失败：{}，skuid:{}", e.getMessage(), sku.getSku());
            }

        });
    }

    private SupplierSpuCreateV2Command buildSupplierSpuCreateV2Command(LingXingDataParam lingXingDataParam, QueryLingXingProductInfoResponse.ProductInfo productInfo) {
        SupplierSpuCreateV2Command supplierSpuCreateV2Command = new SupplierSpuCreateV2Command();
        supplierSpuCreateV2Command.setSupplierId(lingXingDataParam.getSupplierId());

        supplierSpuCreateV2Command.setDescInfos(buildSpuInfo(productInfo));

        supplierSpuCreateV2Command.setDefaultLocale(LanguageLocaleType.zh_CN);

        supplierSpuCreateV2Command.setCustomCode(productInfo.getSku());

        supplierSpuCreateV2Command.setCustomBrandId(String.valueOf(productInfo.getBid()));

        supplierSpuCreateV2Command.setCustomCategoryId(String.valueOf(productInfo.getCid()));

        supplierSpuCreateV2Command.setCategoryId(categoryId);

        supplierSpuCreateV2Command.setMeasuringUnitCode(productInfo.getUnit());

        //图
        List<QueryLingXingProductInfoResponse.ProductInfo.Picture> picture_list = productInfo.getPicture_list();
        List<MultimediaInfo> mainImages = new ArrayList<>();
        MultimediaInfo mainImage = new MultimediaInfo();
        mainImage.setType(MultimediaType.IMAGE);
        if (CollectionUtils.isNotEmpty(picture_list)) {
            picture_list.forEach(img -> {
                MultimediaInfo image = new MultimediaInfo();
                image.setType(MultimediaType.IMAGE);
                image.setFileUrl(img.getPic_url());
                mainImages.add(image);
                if (img.getIs_primary() == 1){
                    mainImage.setFileUrl(img.getPic_url());
                }
            });
        }else {
            MultimediaInfo image = new MultimediaInfo();
            image.setType(MultimediaType.IMAGE);
            image.setFileUrl(productInfo.getPic_url());
            mainImages.add(image);
            mainImage.setFileUrl(productInfo.getPic_url());
        }
        supplierSpuCreateV2Command.setMainImages(mainImages);

        supplierSpuCreateV2Command.setSkuList(buildSkuInfo(lingXingDataParam,productInfo,mainImage));

        supplierSpuCreateV2Command.setOperator("tpsi");

        supplierSpuCreateV2Command.setIsTryCreate(true);

        //供应商
        List<QueryLingXingProductInfoResponse.ProductInfo.SupplierQuote> supplier_quotes = productInfo.getSupplier_quote();
        if (CollectionUtils.isNotEmpty(supplier_quotes)) {
            Optional<QueryLingXingProductInfoResponse.ProductInfo.SupplierQuote> first = supplier_quotes.stream().filter(supplier -> supplier.getIs_primary() == 1).findFirst();
            QueryLingXingProductInfoResponse.ProductInfo.SupplierQuote supplierQuote;
            supplierQuote = first.orElseGet(() -> supplier_quotes.get(0));
            supplierSpuCreateV2Command.setDpsName(supplierQuote.getSupplier_name());
            supplierSpuCreateV2Command.setDpsOuterCode(String.valueOf(supplierQuote.getSupplier_id()));
        }

        return supplierSpuCreateV2Command;
    }

    private List<SupplierSpuDescInfo> buildSpuInfo(QueryLingXingProductInfoResponse.ProductInfo productInfo){
        List<SupplierSpuDescInfo> descInfos = new ArrayList<>();
        SupplierSpuDescInfo supplierSpuDescInfo = new SupplierSpuDescInfo();

        supplierSpuDescInfo.setLocale(LanguageLocaleType.zh_CN);
        supplierSpuDescInfo.setTitle(productInfo.getProduct_name());
        supplierSpuDescInfo.setCustomMeasuringUnit(productInfo.getUnit());
        supplierSpuDescInfo.setCustomBrandName(productInfo.getBrand_name());
        supplierSpuDescInfo.setCustomCategoryName(productInfo.getCategory_name());
        supplierSpuDescInfo.setTextDesc(productInfo.getDescription());
        supplierSpuDescInfo.setTransSourceType("LINGXING");
        supplierSpuDescInfo.setTransProvider("LINGXING");
        supplierSpuDescInfo.setTransBaseLocate(LanguageLocaleType.zh_CN);

        descInfos.add(supplierSpuDescInfo);
        return descInfos;
    }

    private List<SupplierSkuCreateInfo> buildSkuInfo(LingXingDataParam lingXingDataParam, QueryLingXingProductInfoResponse.ProductInfo productInfo,MultimediaInfo mainImage){
        List<SupplierSkuCreateInfo> skuCreateInfoList = new ArrayList<>();
        SupplierSkuCreateInfo skuCreateInfo = new SupplierSkuCreateInfo();

        skuCreateInfo.setCustomCode(productInfo.getSku());
        List<QueryLingXingProductInfoResponse.ProductInfo.SupplierQuote> supplier_quotes = productInfo.getSupplier_quote();
        if (CollectionUtils.isNotEmpty(supplier_quotes)) {
            Optional<QueryLingXingProductInfoResponse.ProductInfo.SupplierQuote> first = supplier_quotes.stream().filter(supplier -> supplier.getIs_primary() == 1).findFirst();
            QueryLingXingProductInfoResponse.ProductInfo.SupplierQuote supplierQuote;
            supplierQuote = first.orElseGet(() -> supplier_quotes.get(0));
            skuCreateInfo.setRefProductLink(CollectionUtils.isNotEmpty(supplierQuote.getSupplier_product_url()) ? supplierQuote.getSupplier_product_url().get(0) : null);
            if (CollectionUtils.isNotEmpty(supplierQuote.getQuotes())) {
                List<QueryLingXingProductInfoResponse.ProductInfo.SupplierQuote.Quote> quotes = supplierQuote.getQuotes();
                Optional<QueryLingXingProductInfoResponse.ProductInfo.SupplierQuote.Quote> first1 = quotes.stream().filter(q -> CollectionUtils.isNotEmpty(q.getStep_prices())).findFirst();
                if (first1.isPresent()) {
                    QueryLingXingProductInfoResponse.ProductInfo.SupplierQuote.Quote quote = first1.get();
                    skuCreateInfo.setMoq(quote.getStep_prices().get(0).getMoq());
                }
            }
        }
        if (skuCreateInfo.getMoq() == null || skuCreateInfo.getMoq() <= 0) {
            skuCreateInfo.setMoq(1);
        }

        skuCreateInfo.setLeadTime(productInfo.getCg_delivery());

        BigDecimal decimal1 = new BigDecimal("100");
        BigDecimal decimal2 = new BigDecimal("1000");

        if (productInfo.getCg_product_net_weight() != null) {
            skuCreateInfo.setNetWeight(productInfo.getCg_product_net_weight().divide(decimal2, 4, RoundingMode.HALF_UP));
        }

        if (productInfo.getCg_product_gross_weight() != null) {
            skuCreateInfo.setGrossWeight(productInfo.getCg_product_gross_weight().divide(decimal2, 4, RoundingMode.HALF_UP));
        }

        if (productInfo.getCg_product_height() != null) {
            skuCreateInfo.setSizeHeight(productInfo.getCg_product_height().divide(decimal1, 3, RoundingMode.HALF_UP));
        }

        if (productInfo.getCg_product_length() != null) {
            skuCreateInfo.setSizeLength(productInfo.getCg_product_length().divide(decimal1, 3, RoundingMode.HALF_UP));
        }

        if (productInfo.getCg_product_width() != null) {
            skuCreateInfo.setSizeWidth(productInfo.getCg_product_width().divide(decimal1, 3, RoundingMode.HALF_UP));
        }

        if (productInfo.getCg_package_height() != null) {
            skuCreateInfo.setPackingHeight(productInfo.getCg_package_height().divide(decimal1, 3, RoundingMode.HALF_UP));
        }

        if (productInfo.getCg_package_length() != null) {
            skuCreateInfo.setPackingLength(productInfo.getCg_package_length().divide(decimal1, 3, RoundingMode.HALF_UP));
        }

        if (productInfo.getCg_package_width() != null) {
            skuCreateInfo.setPackingWidth(productInfo.getCg_package_width().divide(decimal1, 3, RoundingMode.HALF_UP));
        }

        skuCreateInfo.setRetailPriceCurrency("CMY");
        skuCreateInfo.setMeasuringUnit(productInfo.getUnit());
        if (0 == productInfo.getStatus()) {
            skuCreateInfo.setSaleState(SupplierSkuSaleState.OFF_SALE);
        }

        //主规格
        SupplierSkuMainSpecInfo mainSpecInfo = new SupplierSkuMainSpecInfo();
        mainSpecInfo.setImage(mainImage);

        //扩展信息
        SupplierSkuCreateExtendInfo extendInfo = new SupplierSkuCreateExtendInfo();
        extendInfo.setReferenceStockNum(0);
        List<SupplierItemCreateOrUpdate4SpuCommand> supplierItems = new ArrayList<>();
        SupplierItemCreateOrUpdate4SpuCommand command = new SupplierItemCreateOrUpdate4SpuCommand();
        if (productInfo.getDeclaration() != null) {
            command.setCountry(productInfo.getDeclaration().getCustoms_declaration_origin_produce());
        }
        if (StringUtils.isBlank(command.getCountry())){
            command.setCountry("CN");
        }
        command.setWarehouseId(lingXingDataParam.getWarehouseId());
        command.setSupplyPrice(productInfo.getCg_price());
        command.setSupplyPriceCurrency("CNY");

        supplierItems.add(command);
        extendInfo.setSupplierItems(supplierItems);
        skuCreateInfo.setExtendInfo(extendInfo);

        skuCreateInfoList.add(skuCreateInfo);
        return skuCreateInfoList;
    }

    public ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }
}
