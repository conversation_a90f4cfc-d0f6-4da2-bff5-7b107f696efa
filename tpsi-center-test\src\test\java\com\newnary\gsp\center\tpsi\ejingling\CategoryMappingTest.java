package com.newnary.gsp.center.tpsi.ejingling;

import com.newnary.gsp.center.product.api.thirdpartymapping.vo.ThirdPartyMappingType;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.ThirdPartyMappingManager;
import com.newnary.test.starter.BaseTestInjectTenant;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.junit.Test;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;

public class CategoryMappingTest extends BaseTestInjectTenant {

    private static final String SOURCE_BIZ_ID = "GSP";
    private static final String TARGET_BIZ_ID = "EJINGLING";

    @Resource
    private ThirdPartyMappingManager thirdPartyMappingManager;

    @Override
    protected String tenantId() {
        return "TENANT3924117688555169382400";
    }

    @Test
    public void initCategory() {
        File file = new File("D:\\备份资料\\open\\项目资料\\类目\\6.12\\衫海经类目对应阿里类目.xlsx");
        int titleRow = 1;
        try {
            Workbook wb = WorkbookFactory.create(file);
            Sheet sheet = wb.getSheet("SheetJS");
            if (sheet == null) {
                throw new RuntimeException("Sheet1无数据");
            }
            int totalRows = sheet.getLastRowNum();

            for (int i = titleRow; i <= totalRows; i++) {
                Row row = sheet.getRow(i);
                if (null == row) {
                    return;
                }
                System.out.println(row.getRowNum());
                thirdPartyMappingManager.insertOrUpdateCategory(SOURCE_BIZ_ID,TARGET_BIZ_ID,row.getCell(11).getStringCellValue(),row.getCell(8).getStringCellValue(),null, ThirdPartyMappingType.CATEGORY.name(),null);
                //thirdPartyMappingManager.insertOrUpdate(SOURCE_BIZ_ID, TARGET_BIZ_ID, "PMC357988525732640194560", row.getCell(0).getStringCellValue(), ThirdPartyMappingType.CATEGORY.name(), null);
                //titleRow++;
                /*else if (null != row.getCell(13) && !"#N/A".equals(row.getCell(13).toString())) {
                    thirdPartyMappingManager.insertOrUpdateCategory(SOURCE_BIZ_ID,TARGET_BIZ_ID,row.getCell(13).getStringCellValue(),null,categoryPath, ThirdPartyMappingType.CATEGORY.name(),null);
                }else if (null != row.getCell(12) && !"#N/A".equals(row.getCell(12).toString())) {
                    thirdPartyMappingManager.insertOrUpdateCategory(SOURCE_BIZ_ID,TARGET_BIZ_ID,row.getCell(12).getStringCellValue(),null,categoryPath, ThirdPartyMappingType.CATEGORY.name(),null);
                }else if (null != row.getCell(11) && !"#N/A".equals(row.getCell(11).toString())) {
                    thirdPartyMappingManager.insertOrUpdateCategory(SOURCE_BIZ_ID,TARGET_BIZ_ID,row.getCell(11).getStringCellValue(),null,categoryPath, ThirdPartyMappingType.CATEGORY.name(),null);
                }*/
            }
        } catch (InvalidFormatException | IOException e) {
            e.printStackTrace();
        }
    }
}

