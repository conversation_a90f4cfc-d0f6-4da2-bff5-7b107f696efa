package com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.request;


import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class QueryOpen1688ProductByImageUrlRequest {


    private String scenario;

    private Param param;

    @Setter
    @Getter
    public static class Param{
        private List<String> filler;
        private String imageUrl;
        private Long pageNum;
        private Long pageSize;
        private String priceEnd;
        private String priceStart;
        private Integer quantityBegin;
    }
}
