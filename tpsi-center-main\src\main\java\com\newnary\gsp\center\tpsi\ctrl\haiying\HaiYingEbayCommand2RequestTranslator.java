package com.newnary.gsp.center.tpsi.ctrl.haiying;

import com.newnary.gsp.center.tpsi.api.haiying.request.ebay.*;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.ebay.*;
import com.newnary.gsp.center.tpsi.infra.mapper.HaiYingEbayDataMapper;

/**
 * <AUTHOR>
 * @Date 2021/12/14 14:52
 */
public class HaiYingEbayCommand2RequestTranslator {

    public static HaiYingEbayProductListRequest transEbayProductList(HaiYingEbayProductListCommand command) {
        HaiYingEbayProductListRequest request = HaiYingEbayDataMapper.INSTANCE.transEbayProductListRequest(command);

        return request;
    }

    public static HaiYingEbayProductDetailInfoRequest transEbayProductDetailInfo(HaiYingEbayProductDetailInfoCommand command) {
        HaiYingEbayProductDetailInfoRequest request = HaiYingEbayDataMapper.INSTANCE.transEbayProductDetailRequest(command);

        return request;
    }

    public static HaiYingEbayCategoryTreeRequest transEbayCategoryTree(HaiYingEbayCategoryTreeCommand command) {
        HaiYingEbayCategoryTreeRequest request = HaiYingEbayDataMapper.INSTANCE.transEbayCategoryTreeRequest(command);

        return request;
    }

    public static HaiYingEbayTopCategoryInfoRequest transEbayTopCategoryInfo(HaiYingEbayTopCategoryInfoCommand command) {
        HaiYingEbayTopCategoryInfoRequest request = HaiYingEbayDataMapper.INSTANCE.transEbayTopCategoryRequest(command);

        return request;
    }

    public static HaiYingEbayCategoryDetailRequest transEbayCategoryDetail(HaiYingEbayCategoryDetailCommand command) {
        HaiYingEbayCategoryDetailRequest request = HaiYingEbayDataMapper.INSTANCE.transEbayCategoryDetailRequest(command);

        return request;
    }

}
