package com.newnary.gsp.center.tpsi.infra.mq.consumer;

import com.newnary.messagebody.gsp.logistics.GSPStockoutOrderTopic;
import com.newnary.mq.starter.consumer.AbstractMQConsumer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class StockoutOrderMQConsumer extends AbstractMQConsumer {

    @Value("${gsp.stockout-order.mq.consumer-id}")
    private String group;

    @Override
    public String topic() {
        return GSPStockoutOrderTopic.TOPIC;
    }

    @Override
    public String consumerGroup() {
        return group;
    }
}
