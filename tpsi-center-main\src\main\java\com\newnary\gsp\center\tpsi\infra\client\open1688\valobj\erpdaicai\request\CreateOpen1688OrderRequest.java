package com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class CreateOpen1688OrderRequest {

    /**
     * 流程：general（创建大市场订单），fenxiao（创建分销订单）。fenxiao流程将校验分销关系,ttpft(批发团下单),boutiquefenxiao(精选货源专项价下单)
     */
    private String flow;

    /**
     * 买家留言
     */
    private String message;

    /**
     * 开放平台业务码,默认为cross。cross(跨境业务),cross_daigou（跨境代购业务）
     */
    private String isvBizType;

    /**
     * 收货地址信息
     */
    private AddressParam addressParam;

    /**
     * 商品信息
     */
    private List<CargoParam> cargoParamList;

    /**
     * 发票信息
     */
    private InvoiceParam invoiceParam;

    /**
     *由于不同的商品支持的交易方式不同，没有一种交易方式是全局通用的，
     * 所以当前下单可使用的交易方式必须通过下单预览接口的tradeModeNameList获取。交易方式类型说明：fxassure（交易4.0通用担保交易），alipay（大市场通用的支付宝担保交易（目前在做切流，后续会下掉）），
     * period（普通账期交易）, assure（大买家企业采购询报价下单时需要使用的担保交易流程）, creditBuy（诚E赊），bank（银行转账），631staged（631分阶段付款），37staged（37分阶段）；
     * 此字段不传则系统默认会选取一个可用的交易方式下单，如果开通了诚E赊默认是creditBuy（诚E赊），未开通诚E赊默认使用的方式是支付宝担宝交易
     */
    private String tradeType;

    /**
     * 店铺优惠ID，通过“创建订单前预览数据接口”获得。为空默认使用默认优惠
     */
    private String shopPromotionId;

    /**
     * 是否匿名下单
     */
    private Boolean anonymousBuyer;

    /**
     * 回流订单下游平台 淘宝-thyny，天猫-tm，淘特-taote，阿里巴巴C2M-c2m，京东-jingdong，
     * 拼多多-pinduoduo，微信-weixin，跨境-kuajing，快手-kuaishou，有赞-youzan，抖音-douyin，
     * 寺库-siku，美团团好货-meituan，小红书-xiaohongshu，当当-dangdang，苏宁-suning，
     * 大V店-davdian，行云-xingyun，蜜芽-miya，菠萝派商城-boluo，快团团-kuaituantuan，其他-other
     */
    private String fenxiaoChannel;


    @Setter
    @Getter
    public static class AddressParam{

        /**
         * 收货地址id
         */
        private Long addressId;

        /**
         * 收货人姓名
         */
        private String fullName;

        /**
         * 手机
         */
        private String mobile;

        /**
         * 电话
         */
        private String phone;

        /**
         * 邮编
         */
        private String postCode;

        /**
         * 市文本
         */
        private String cityText;

        /**
         * 省份文本
         */
        private String provinceText;

        /**
         * 地区文本
         */
        private String areaText;

        /**
         * 镇文本
         */
        private String townText;

        /**
         * 街道地址文本
         */
        private String address;

        /**
         * 地址编码
         */
        private String districtCode;
    }

    @Setter
    @Getter
    public static class CargoParam{

        /**
         * 商品id
         */
        private Long offerId;

        /**
         * 商品规格id
         */
        private String specId;

        /**
         * 数量
         */
        private Double quantity;
    }

    @Setter
    @Getter
    public static class InvoiceParam{

        /**
         * 发票类型 0：普通发票，1:增值税发票
         */
        private Integer invoiceType;

        /**
         * 省份文本
         */
        private String provinceText;

        /**
         * 城市文本
         */
        private String cityText;

        /**
         * 地区文本
         */
        private String areaText;

        /**
         * 镇文本
         */
        private String townText;

        /**
         * 邮编
         */
        private String postCode;

        /**
         * 街道
         */
        private String address;

        /**
         * 收票人姓名
         */
        private String fullName;

        /**
         * 电话
         */
        private String phone;

        /**
         * 手机
         */
        private String mobile;

        /**
         * 购货公司名（发票抬头）
         */
        private String companyName;

        /**
         * 纳税识别码
         */
        private String taxpayerIdentifier;

        /**
         * 开户行及帐号
         */
        private String bankAndAccount;

        /**
         * 增值税本地发票号
         */
        private String localInvoiceId;
    }

}
