package com.newnary.gsp.center.tpsi.service.haiying.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.tpsi.infra.client.haiying.HaiYingDataApiClient;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.HaiYingDataApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.shopee.*;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.service.haiying.IHaiYingDataShopeeApiSve;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Slf4j
@Component
public class HaiYingDataShopeeApiSveImpl implements IHaiYingDataShopeeApiSve {

    private static final String thirdPartySystemId = "TEST_HAIYINGDATE_API";

    private static final String haiyingVersion = "hysj_v2";

    private static final String haiyingPlatform = "shopee_api";

    private static final Integer pageLimit = 100000;

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    private ThirdPartySystem thirdPartySystem;

    private String shopeeApiUrl;

    private HaiYingDataApiClient haiYingDataApiClient;

    private static final SerializerFeature[] features = {SerializerFeature.WriteMapNullValue};

    public HaiYingDataShopeeApiSveImpl() {
        shopeeApiUrl = haiyingVersion.concat(File.separator).concat(haiyingPlatform).concat(File.separator);
    }

    private void init() {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        //获取apiClient
        haiYingDataApiClient = getClient(thirdPartySystem.getParams());
    }

    @Override
    public HaiYingDataApiBaseResult<String> getKeywordList(HaiYingShopeeKeywordListRequest keywordListRequest) {
        if (StringUtils.isNotEmpty(keywordListRequest.getPage_size()) && StringUtils.isNotEmpty(keywordListRequest.getCurrent_page())) {
            Integer pageSize = Integer.valueOf(keywordListRequest.getPage_size());
            Integer currentPage = Integer.valueOf(keywordListRequest.getCurrent_page());
            if (pageSize * currentPage > pageLimit) {
                return commonSendMethod("keywords", JSON.toJSONString(keywordListRequest, features));
            }
        }
        return commonSendMethod("keywords_query", JSON.toJSONString(keywordListRequest, features));
    }

    @Override
    public HaiYingDataApiBaseResult<String> getKeywordInfo(HaiYingShopeeKeywordInfoRequest keywordInfoRequest) {
        return commonSendMethod("keywords", JSON.toJSONString(keywordInfoRequest, features));
    }

    @Override
    public HaiYingDataApiBaseResult<String> getProductList(HaiYingShopeeProductListRequest productsInfoRequest) {
        if (StringUtils.isNotEmpty(productsInfoRequest.getPage_size()) && StringUtils.isNotEmpty(productsInfoRequest.getCurrent_page())) {
            Integer pageSize = Integer.valueOf(productsInfoRequest.getPage_size());
            Integer currentPage = Integer.valueOf(productsInfoRequest.getCurrent_page());
            if (pageSize * currentPage > pageLimit) {
                return commonSendMethod("pro_infos", JSON.toJSONString(productsInfoRequest, features));
            }
        }
        return commonSendMethod("pro_infos_query", JSON.toJSONString(productsInfoRequest, features));
    }

    @Override
    public HaiYingDataApiBaseResult<String> getProductDetailInfo(HaiYingShopeeProductDetailInfoRequest productDetailInfoRequest) {
        return commonSendMethod("pro_info", JSON.toJSONString(productDetailInfoRequest, features));
    }

    @Override
    public HaiYingDataApiBaseResult<String> getProductExtInfo(HaiYingShopeeProductExtInfoRequest productExtInfoRequest) {
        return commonSendMethod("pro_extra_info", JSON.toJSONString(productExtInfoRequest, features));
    }

    @Override
    public HaiYingDataApiBaseResult<String> getProductHistoryInfo(HaiYingShopeeProductHistoryInfoRequest productHistoryInfoRequest) {
        return commonSendMethod("pro_his_data", JSON.toJSONString(productHistoryInfoRequest, features));
    }

    @Override
    public HaiYingDataApiBaseResult<String> getCategoryTree(HaiYingShopeeCategoryTreeRequest categoryTreeRequest) {
        return commonSendMethod("cate_tree", JSON.toJSONString(categoryTreeRequest, features));
    }

    @Override
    public HaiYingDataApiBaseResult<String> getTopCategoryInfo(HaiYingShopeeTopCategoryInfoRequest topCategoryInfoRequest) {
        return commonSendMethod("top_cate_infos", JSON.toJSONString(topCategoryInfoRequest, features));
    }

    @Override
    public HaiYingDataApiBaseResult<String> getSubCategoryInfo(HaiYingShopeeSubCategoryInfoRequest subCategoryInfoRequest) {
        return commonSendMethod("sub_cate_infos", JSON.toJSONString(subCategoryInfoRequest, features));
    }

    private HaiYingDataApiBaseResult<String> commonSendMethod(String serviceName, String requestJson) {
        if (null == haiYingDataApiClient) {
            init();
        }
        return haiYingDataApiClient.sendRequest(shopeeApiUrl.concat(serviceName), JSON.parseObject(requestJson));
    }


    public ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }

    private HaiYingDataApiClient getClient(String ecCangParams) {
        return new HaiYingDataApiClient(ecCangParams);
    }

}
