package com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.dev;

import lombok.Data;

@Data
public class MaBangGetInLibraryProducts {

    /**
     * SKU
     */
    private String sku;

    /**
     * 创建时间 起始值
     */
    private String timeCreatedGEQ;

    /**
     * 创建时间 结束值
     */
    private String timeCreatedLEQ;

    /**
     * 产品状态normal或void 默认 normal, 可传空值
     */
    private String productStatus;

    /**
     * 更多字段(供应商，竞品等) 1是2否，默认 0
     */
    private String moreField;

    /**
     * 页数 默认1
     */
    private Integer page;

    /**
     * 单页数量 默认10,最大500
     */
    private Integer rowsPerPage;
}
