<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.newnary.gsp.center.tpsi.infra.repository.db.dao.ThirdPartyAddressMappingDao">

<resultMap id="thirdPartyAddressMappingPOResult" type="com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyAddressMappingPO">
    <result column="virtual_postcode" property="virtualPostcode"/>
    <result column="post_code" property="postCode"/>
    <result column="system_id" property="systemId"/>
    <result column="province" property="province"/>
    <result column="city" property="city"/>
    <result column="area" property="area"/>
    <result column="town" property="town"/>
    <result column="address" property="address"/>
    <result column="country" property="country"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="gmt_create" property="gmtCreate"/>
    <result column="gmt_modified" property="gmtModified"/>
    <result column="id" property="id"/>
</resultMap>

<sql id="thirdPartyAddressMappingPO_columns">
    virtual_postcode,
    post_code,
    system_id,
    province,
    city,
    area,
    town,
    address,
    country,
    tenant_id,
    gmt_create,
    gmt_modified,
    id
</sql>

<sql id="thirdPartyAddressMappingPO_sqlForInsert">
    virtual_postcode,
    post_code,
    system_id,
    province,
    city,
    area,
    town,
    address,
    country,
    tenant_id,
    gmt_create,
    gmt_modified,
    id
</sql>

<sql id="thirdPartyAddressMappingPO_columnsForInsert">
    #{virtualPostcode},
    #{postCode},
    #{systemId},
    #{province},
    #{city},
    #{area},
    #{town},
    #{address},
    #{country},
    #{tenantId},
    REPLACE(unix_timestamp(NOW(3)),'.',''),
    REPLACE(unix_timestamp(NOW(3)),'.',''),
    #{id}
</sql>

<sql id="thirdPartyAddressMappingPO_setterForUpdate">
    <set>
        gmt_modified = REPLACE(unix_timestamp(NOW(3)),'.',''),
        virtual_postcode = #{virtualPostcode},
        post_code = #{postCode},
        system_id = #{systemId},
        province = #{province},
        city = #{city},
        area = #{area},
        town = #{town},
        address = #{address},
        country = #{country},
    </set>
</sql>

<sql id="thirdPartyAddressMappingPO_selector">
    select
    <include refid="thirdPartyAddressMappingPO_columns"/>
    from third_party_address_mapping
</sql>

<sql id="thirdPartyAddressMappingPO_query_segment">
    <trim prefix="WHERE" prefixOverrides="AND|OR">
        <if test="data.virtualPostcode != null">
            AND virtual_postcode = #{data.virtualPostcode}
        </if>
        <if test="data.postCode != null">
            AND post_code = #{data.postCode}
        </if>
        <if test="data.systemId != null">
            AND system_id = #{data.systemId}
        </if>
        <if test="data.province != null">
            AND province = #{data.province}
        </if>
        <if test="data.city != null">
            AND city = #{data.city}
        </if>
        <if test="data.area != null">
            AND area = #{data.area}
        </if>
        <if test="data.town != null">
            AND town = #{data.town}
        </if>
        <if test="data.address != null">
            AND address = #{data.address}
        </if>
        <if test="data.country != null">
            AND country = #{data.country}
        </if>
        <if test="data.gmtCreate != null">
            AND gmt_create = #{data.gmtCreate}
        </if>
        <if test="data.gmtModified != null">
            AND gmt_modified = #{data.gmtModified}
        </if>
        <if test="data.id != null">
            AND id = #{data.id}
        </if>
        <if test="ins != null and ins.size() > 0">
            AND
            <foreach collection="ins" item="item" open="(" separator="and" close=")">
                ${item.column} in
                <foreach collection="item.values" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </foreach>
        </if>
        <if test="notIns != null and notIns.size() > 0">
            AND
            <foreach collection="notIns" item="item" open="(" separator="and" close=")">
                ${item.column} not in
                <foreach collection="item.values" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </foreach>
        </if>
        <if test="equals != null and equals.size() > 0">
            AND
            <foreach collection="equals" item="item" open="(" separator="and" close=")">
                ${item.column} = #{item.value}
            </foreach>
        </if>
        <if test="notEquals != null and notEquals.size() > 0">
            AND
            <foreach collection="notEquals" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ <> ]]> #{item.value}
            </foreach>
        </if>
        <if test="lts != null and lts.size() > 0">
            AND
            <foreach collection="lts" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ < ]]> #{item.value}
            </foreach>
        </if>
        <if test="gts != null and gts.size() > 0">
            AND
            <foreach collection="gts" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ > ]]> #{item.value}
            </foreach>
        </if>
        <if test="likes != null and likes.size() > 0">
            AND
            <foreach collection="likes" item="item" open="(" separator="and" close=")">
                ${item.column} like concat('%', #{item.value}, '%')
            </foreach>
        </if>
        <if test="ltes != null and ltes.size() > 0">
            AND
            <foreach collection="ltes" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ <= ]]> #{item.value}
            </foreach>
        </if>
        <if test="gtes != null and gtes.size() > 0">
            AND
            <foreach collection="gtes" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ >= ]]> #{item.value}
            </foreach>
        </if>
        <if test="ranges != null and ranges.size() > 0">
            AND
            <foreach collection="ranges" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ >= ]]> #{item.from} and ${item.column} <![CDATA[ < ]]> #{item.to}
            </foreach>
        </if>
        AND tenant_id = #{tenantId}
    </trim>
</sql>


<insert id="insert" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyAddressMappingPO">
    insert into third_party_address_mapping
    (
        <include refid="thirdPartyAddressMappingPO_sqlForInsert"/>
    )
    values
    (
        <include refid="thirdPartyAddressMappingPO_columnsForInsert"/>
    )
</insert>

<update id="update" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyAddressMappingPO">
    update third_party_address_mapping
    <include refid="thirdPartyAddressMappingPO_setterForUpdate" />
    where id = #{id}
    and tenant_id = #{tenantId}
</update>


<update id="updateByQuery" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyAddressMappingPO">
    update third_party_address_mapping
    <set>
        gmt_modified = REPLACE(unix_timestamp(NOW(3)),'.',''),
        virtual_postcode = #{update.virtualPostcode},
        post_code = #{update.postCode},
        system_id = #{update.systemId},
        province = #{update.province},
        city = #{update.city},
        area = #{update.area},
        town = #{update.town},
        address = #{update.address},
        country = #{update.country},
    </set>
    <include refid="thirdPartyAddressMappingPO_query_segment"/>
</update>

<!-- Delete -->
<delete id="delete">
    delete from third_party_address_mapping
    <include refid="thirdPartyAddressMappingPO_query_segment"/>
</delete>
<delete id="deleteById">
    delete from third_party_address_mapping
    where id = #{id}
    and tenant_id = #{tenantId}
</delete>





<!-- query class -->
<sql id="thirdPartyAddressMappingPO_groupBy">
    <if test="groupBys != null and groupBys.size() > 0">
        group by
        <foreach collection="groupBys" item="item" open="" separator="," close="">
            #{item}
        </foreach>
    </if>
</sql>

<sql id="thirdPartyAddressMappingPO_orderby">
    <if test="orderBys != null and orderBys.size() > 0">
        order by
        <foreach collection="orderBys" item="item" open="" separator="," close="">
            ${item.column} ${item.direction}
        </foreach>
    </if>
</sql>


<select id="query" resultMap="thirdPartyAddressMappingPOResult" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyAddressMappingPO">
    <include refid="thirdPartyAddressMappingPO_selector"/>
    <include refid="thirdPartyAddressMappingPO_query_segment"/>
    <include refid="thirdPartyAddressMappingPO_groupBy"/>
    <include refid="thirdPartyAddressMappingPO_orderby"/>
</select>



<select id="count" resultType="java.lang.Long">
    SELECT COUNT(1) FROM third_party_address_mapping
    <include refid="thirdPartyAddressMappingPO_query_segment"/>
</select>

<select id="getById" resultMap="thirdPartyAddressMappingPOResult">
    <include refid="thirdPartyAddressMappingPO_selector"/>
    where id = #{id}
    and tenant_id = #{tenantId}
</select>

<select id="getByIdForUpdate" resultMap="thirdPartyAddressMappingPOResult">
    <include refid="thirdPartyAddressMappingPO_selector"/>
    where id = #{id}
    and tenant_id = #{tenantId}
    for update
</select>

</mapper>
