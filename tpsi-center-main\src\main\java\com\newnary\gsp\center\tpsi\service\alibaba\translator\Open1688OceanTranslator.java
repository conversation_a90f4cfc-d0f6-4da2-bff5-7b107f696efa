package com.newnary.gsp.center.tpsi.service.alibaba.translator;

import com.alibaba.linkplus.param.AlibabaCrossSimilarOfferSearchParam;
import com.alibaba.linkplus.param.LinkResultModel;
import com.newnary.api.base.common.PageList;
import com.newnary.api.base.common.PageMeta;
import com.newnary.gsp.center.tpsi.api.externalproduct.request.ExternalProductImageSearchCommand;
import com.newnary.gsp.center.tpsi.api.externalproduct.response.ExternalProductSearchInfo;
import com.newnary.gsp.center.tpsi.api.externalproduct.vo.ExternalProductCategoryVO;
import com.newnary.gsp.center.tpsi.api.externalproduct.vo.ExternalProductLiteInfo;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since Created on 2022-05-12
 **/
public class Open1688OceanTranslator {

    public static AlibabaCrossSimilarOfferSearchParam transParam(ExternalProductImageSearchCommand command) {
        AlibabaCrossSimilarOfferSearchParam param = new AlibabaCrossSimilarOfferSearchParam();

        param.setPicUrl(command.getImageUrl());
        param.setPriceMin(command.getSalePriceRangeStart());
        param.setPriceMax(command.getSalePriceRangeEnd());
        param.setClassify(command.getExternalCategoryId());
        param.setPage(command.getPageCondition().getPageNum());

        if (StringUtils.isNotBlank(command.getSortField())) {
            String sortDirection = StringUtils.defaultIfEmpty(command.getSortDirection(), "ASC").toLowerCase();
            switch (command.getSortField()) {
                case "SALE_PRICE":
                    param.setSortFields("price:" + sortDirection);
                    break;
                case "SALE_AMOUNT":
                    param.setSortFields("sale_amount:" + sortDirection);
                    break;
            }
        }

        return param;
    }

    public static ExternalProductSearchInfo transInfo(LinkResultModel result) {
        // 分页信息
        PageMeta pageMeta = new PageMeta();
        pageMeta.setTotal(result.getTotal());
        pageMeta.setPageNum(result.getPage());
        pageMeta.setPageSize(result.getPageSize());

        ExternalProductSearchInfo info = new ExternalProductSearchInfo();
        info.setCategoryList(fillOpen1688CategoryVOList());

        info.setPage(
                new PageList<ExternalProductLiteInfo>()
                        .setItems(
                                Stream.of(result.getResult())
                                        .map(linkProduct -> {
                                            ExternalProductLiteInfo lite = new ExternalProductLiteInfo();
                                            lite.setExternalProductId(linkProduct.getOfferId());
                                            lite.setSubject(linkProduct.getSubject());
                                            lite.setMainImageUrl(linkProduct.getImageUrl());
//                                            lite.setDetailUrl(linkProduct.getDetailUrl());
                                            // 1688有时候会返回其他格式的URL, 可能导致前端无法正确解析
                                            lite.setDetailUrl("https://detail.1688.com/offer/" + linkProduct.getOfferId() + ".html");
                                            lite.setQuantityBegin(linkProduct.getQuantityBegin());
                                            lite.setUnit(linkProduct.getUnit());
                                            lite.setSalePrice(centToYuan(linkProduct.getOldPrice()));
                                            // FIXME: 2022/5/12 1688固定值 CNY @yangzc
                                            lite.setSalePriceCurrency("CNY");
                                            lite.setStockNum(linkProduct.getSupplyAmount());
                                            lite.setProvince(linkProduct.getProvince());
                                            lite.setCity(linkProduct.getCity());
                                            return lite;
                                        }).collect(Collectors.toList())
                        )
                        .setPageMeta(pageMeta)
        );

        return info;
    }

    private static String centToYuan(Long price) {
        return BigDecimal.valueOf(price).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).toString();
    }

    private static List<ExternalProductCategoryVO> fillOpen1688CategoryVOList() {
        // 初始化1688商品类别数据
        // 数据来源于官方文档: @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.linkplus:alibaba.cross.similar.offer.search-1" />
        String text = "0=上衣,1=裙装,2=下装,3=箱包,4=鞋子,5=配饰,6=零食,7=美妆,8=瓶饮,9=家具,10=玩具,11=内衣,12=数码,13=其他";
        return Stream.of(text.split(","))
                .map(item -> {
                    String[] split = item.split("=");
                    ExternalProductCategoryVO vo = new ExternalProductCategoryVO();
                    vo.setCategoryId(split[0]);
                    vo.setName(split[1]);
                    return vo;
                }).collect(Collectors.toList());
    }

}
