package com.newnary.gsp.center.tpsi.infra.client.tk;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.luciad.imageio.webp.WebPReadParam;
import com.newnary.api.base.exception.BaseErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.distributed.tools.cache.DistributedCache;
import com.newnary.gsp.center.tpsi.infra.client.tk.dto.*;
import com.newnary.gsp.center.tpsi.infra.client.tk.dto.vo.SaleAttribute;
import com.newnary.gsp.center.tpsi.infra.client.tk.dto.vo.TrackInfo;
import com.newnary.gsp.center.tpsi.infra.client.tk.dto.vo.TrackInfoList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * @see <a href="https://partner.tiktokshop.com/doc/page/262784?onlySelectedDir=13589">tiktok开放平台</a>
 */
@Slf4j
@Component
public class TKClient {

    @Autowired
    private DistributedCache cache;

    private static DistributedCache distributedCache;

    @PostConstruct
    private void init() {
        TKClient.distributedCache = this.cache;
    }

    ;

    private static final String CACHE_TOKEN_KEY = "ACCESS_TOKEN";
    private static final String CACHE_REFRESH_KEY = "REFRESH_TOKEN";


    // 1.上传图片
    private static final String PATH_UPLOAD_IMAGE = "/api/products/upload_imgs";

    // 2.商品增删改查
    private static final String PATH_PRODUCT = "/api/products";

    // 3.修改库存
    private static final String PATH_UPDATE_STOCK = "/api/products/stocks";

    // 4.修改价格
    private static final String PATH_UPDATE_PRICE = "/api/products/prices";

    // 5.类目获取
    private static final String PATH_GET_CATEGORIES = "/api/products/categories";

    // 6.品牌获取
    private static final String PATH_GET_BRAND = "/api/products/brands";

    // 7.获取订单列表
    private static final String PATH_GET_ORDER = "/api/orders/search";

    // 8.获取订单详情
    private static final String PATH_GET_ORDER_DETAIL = "/api/orders/detail/query";

    // 9. 拆单确认
    private static final String PATH_CONFIRM_ORDER_SPLIT = "/api/fulfillment/order_split/confirm";

    // 10.获取面单
    private static final String PATH_GET_PACKAGE_SHIPPING_DOCUMENT = "/api/fulfillment/shipping_document";

    // 11.通知已发货
    private static final String PATH_SHIPPING_PACKAGE = "/api/fulfillment/rts";

    // 12.验证是否订单是否能拆分
    private static final String PATH_VERIFY_ORDER_SPLIT = "/api/fulfillment/order_split/verify";

    private static final String PATH_GET_ORDER_SHIPPING_DOCUMENT = "/api/logistics/shipping_document";

    // 14. 获取包裹详情
    private static final String PATH_GET_PACKAGE_DETAIL = "/api/fulfillment/detail";

    // 15. 获取包裹物流轨迹
    private static final String PATH_GET_PACKAGE_SHIPPING_INFO = "/api/fulfillment/shipping_info";

    private static final String PATH_GET_ORDER_SHIPPING_INFO = "/api/logistics/ship/get";

    private static final String PATH_GET_AFTER_SALE_ORDER = "/api/reverse/reverse_order/list";

    private static final String sandboxapiParam = "{\"access_token\":\"ROW_BbONuwAAAAAoC8HnFARP2S268dFWqDocCEEsh2EaNMcnvgOk4eWePlIvGiPhitNbzYazEy3TWQxL6UdtxJCzHxqyjSdusVE41C4EE3yiOuyCrDUw9spiDw\",\"app_key\":\"67q49sc9tm7q4\",\"app_secret\":\"cd7bba8d1fc7e5ab1426f5efcb8d93fa8bb8a61b\",\"auth_code\":\"ROW_cpWWPgAAAAB2uO_6bf0KOSskkHd1f5XH05Q8kPE9M3cWhsd7iMVP6v4a3RJ-7uiua92EbJPTQg6y2o6dPRrNLOo65-qFW3zk\",\"grant_type\":\"authorized_code\",\"shop_code\":\"IDLCJLWLWJ\",\"shop_id\":0,\"token_url\":\"https://auth-sandbox.tiktok-shops.com\",\"url\":\"https://open-api-sandbox.tiktokglobalshop.com\",\"use_new_token\":\"T\"}";

    private static final String apiParam = "";

    public static List<JSONObject> getOrderList(TKFetchOrderReq req, String apiParam, String channelCode) {
        List<JSONObject> orders = new ArrayList<>();
        getOrders(req, apiParam, channelCode, orders);
        return orders;
    }


    public static List<JSONObject> batchGetOrderDetail(List<String> orderIds, String apiParam, String channelCode) {
        List<JSONObject> orderDetails = new ArrayList<>();
        if (CollectionUtils.isEmpty(orderIds)) {
            return orderDetails;
        }

        Map<String, Object> param = new HashMap<>();
        param.put("order_id_list", orderIds);
        String result = sendRequest(PATH_GET_ORDER_DETAIL, null, JSON.toJSONString(param), HttpMethod.POST, apiParam, channelCode);
        if (StringUtils.isNotBlank(result)) {
            JSONObject resultObj = JSONObject.parseObject(result);
            if (resultObj.getInteger("code") == 0) {
                JSONObject dataObj = resultObj.getJSONObject("data");
                JSONArray orderDetailArray = dataObj.getJSONArray("order_list");
                if (CollectionUtils.isNotEmpty(orderDetailArray)) {
                    orderDetailArray.forEach(item -> orderDetails.add((JSONObject) item));
                }
            }
        }
        return orderDetails;
    }

    public static TKProductMainInfo createProduct(TKProductCreateReq product, String apiParam, String channelCode) {
        String result = sendRequest(PATH_PRODUCT, null, JSON.toJSONString(product), HttpMethod.POST, apiParam, channelCode);
        if (StringUtils.isNotBlank(result)) {
            JSONObject resultObj = JSONObject.parseObject(result);
            if (resultObj.getInteger("code") == 0) {
                return JSONObject.parseObject(resultObj.getString("data"), TKProductMainInfo.class);
            }
        }
        return null;
    }

    public static List<JSONObject> getAfterSaleOrderList(TKAfterSaleOrderFetchReq req, String apiParam, String channelCode) {
        List<JSONObject> afterSaleOrders = new ArrayList<>();
        getAfterSaleOrders(req, apiParam, channelCode, afterSaleOrders);
        return afterSaleOrders;
    }

    private static void getAfterSaleOrders(TKAfterSaleOrderFetchReq req, String apiParam, String channelCode, List<JSONObject> afterSaleOrders) {
        String result = sendRequest(PATH_GET_AFTER_SALE_ORDER, null, JSON.toJSONString(req), HttpMethod.POST, apiParam, channelCode);
        if (StringUtils.isBlank(result)) {
            return;
        }
        JSONObject resultObj = JSONObject.parseObject(result);
        if (resultObj.getInteger("code") != 0) {
            return;
        }
        JSONObject dataObj = resultObj.getJSONObject("data");
        JSONArray reverseOrders = dataObj.getJSONArray("reverse_list");
        if (CollectionUtils.isEmpty(reverseOrders)) {
            return;
        }
        reverseOrders.forEach(order -> afterSaleOrders.add((JSONObject) order));
        if (dataObj.getBoolean("more")) {
            req.offset = ObjectUtils.defaultIfNull(req.getOffset(), 0) + 1;
            getAfterSaleOrders(req, apiParam, channelCode, afterSaleOrders);
        }
    }


    public static List<SaleAttribute> getCategoryAttributes(String categoryId, String apiParam, String channelCode) {
        Map<String, Object> param = new HashMap<>();
        param.put("category_id", categoryId);
        String result = sendRequest("/api/products/attributes", param, null, HttpMethod.GET, apiParam, channelCode);
        List<SaleAttribute> attributes = new ArrayList<>();
        if (StringUtils.isBlank(result)) {
            return attributes;
        }
        JSONObject resultObj = JSONObject.parseObject(result);
        if (resultObj.getInteger("code") != 0) {
            return attributes;
        }

        JSONArray attributeArray = resultObj.getJSONObject("data").getJSONArray("attributes");
        if (CollectionUtils.isNotEmpty(attributeArray)) {
            for (int i = 0; i < attributeArray.size(); i++) {
                JSONObject obj = attributeArray.getJSONObject(i);
                SaleAttribute saleAttribute = new SaleAttribute();
                saleAttribute.setAttribute_id(obj.getString("id"));
                saleAttribute.setAttribute_name(obj.getString("name"));
                saleAttribute.setType(obj.getInteger("attribute_type"));
                attributes.add(saleAttribute);
            }
        }
        return attributes;
    }


    public static void getWarehouseList(String apiParam, String channelCode) {
        sendRequest("/api/logistics/get_warehouse_list", null, null, HttpMethod.GET, apiParam, channelCode);
    }


    public static TKProductMainInfo updateProduct(TKProductUpdateReq product, String apiParam, String channelCode) {
        String result = sendRequest(PATH_PRODUCT, null, JSON.toJSONString(product), HttpMethod.PUT, apiParam, channelCode);
        if (StringUtils.isNotBlank(result)) {
            JSONObject resultObj = JSONObject.parseObject(result);
            if (resultObj.getInteger("code") == 0) {
                return JSONObject.parseObject(resultObj.getString("data"), TKProductMainInfo.class);
            }
        }
        return null;
    }

    public static JSONArray getCategories(String apiParam, String channelCode) {
        JSONArray categories = new JSONArray();
        String result = sendRequest(PATH_GET_CATEGORIES, null, null, HttpMethod.GET, apiParam, channelCode);
        if (StringUtils.isNotBlank(result)) {
            JSONObject resultObj = JSONObject.parseObject(result);
            if (resultObj.getInteger("code") == 0) {
                return resultObj.getJSONArray("category_list");
            }
        }
        return categories;
    }

    public static void getShop(String apiParam, String channelCode) {
        sendRequest("/api/shop/get_authorized_shop", null, null, HttpMethod.GET, apiParam, channelCode);
    }

    public static void getShopList(String apiParam, String channelCode) {
        sendRequest("/api/seller/global/active_shops", null, null, HttpMethod.GET, apiParam, channelCode);
    }

    public static void updateStock(StockUpdateReq stock, String apiParam, String channelCode) {
        sendRequest(PATH_UPDATE_STOCK, null, JSON.toJSONString(stock), HttpMethod.POST, apiParam, channelCode);
    }

    public static void updatePrice(PriceUpdateReq price, String apiParam, String channelCode) {
        sendRequest(PATH_UPDATE_PRICE, null, JSON.toJSONString(price), HttpMethod.POST, apiParam, channelCode);
    }

    public static JSONArray getBrands(Map<String, Object> requestParam, String apiParam, String channelCode) {
        String result = sendRequest(PATH_GET_BRAND, requestParam, null, HttpMethod.GET, apiParam, channelCode);
        if (StringUtils.isNotBlank(result)) {
            JSONObject resultObj = JSONObject.parseObject(result);
            if (resultObj.getInteger("code") == 0) {
                return resultObj.getJSONArray("brand_list");
            }
        }
        return new JSONArray();
    }

    public static JSONObject getPackageDetail(String packageId, String apiParam, String channelCode) {
        Map<String, Object> queryParam = new HashMap<>();
        TKApiParam tkApiParam = JSONObject.parseObject(apiParam, TKApiParam.class);
        queryParam.put("shop_id", tkApiParam.getShop_id());
        queryParam.put("package_id", packageId);
        String result = sendRequest(PATH_GET_PACKAGE_DETAIL, queryParam, null, HttpMethod.GET, apiParam, channelCode);
        if (StringUtils.isBlank(result)) {
            return null;
        }
        JSONObject resultObj = JSONObject.parseObject(result);
        if (resultObj.getInteger("code") != 0) {
            return null;
        }
        return resultObj.getJSONObject("data");
    }


    public static Map<String, String> confirmOrderSplit(Map<String, List<String>> orderLineGroup, String orderId, String apiParam, String channelCode) {
        Map<String, Object> param = new HashMap<>();
        param.put("order_id", Long.valueOf(orderId));
        List<JSONObject> splitGroup = new ArrayList<>();
        for (String packageId : orderLineGroup.keySet()) {
            JSONObject object = new JSONObject();
            object.put("pre_split_pkg_id", Integer.valueOf(packageId));
            object.put("order_line_id_list", orderLineGroup.get(packageId).stream().map(Long::valueOf).collect(Collectors.toList()));
            splitGroup.add(object);
        }

        param.put("split_group", splitGroup);
        String result = sendRequest(PATH_CONFIRM_ORDER_SPLIT, null, JSON.toJSONString(param), HttpMethod.POST, apiParam, channelCode);
        Map<String, String> deliveryOrderIdAndTkPackageIdMapping = new HashMap<>();
        if (StringUtils.isBlank(result)) {
            return deliveryOrderIdAndTkPackageIdMapping;
        }
        JSONObject resultObj = JSONObject.parseObject(result);
        if (resultObj.getInteger("code") != 1) {
            return deliveryOrderIdAndTkPackageIdMapping;
        }
        JSONArray successList = resultObj.getJSONArray("success_list");
        if (CollectionUtils.isEmpty(successList)) {
            return deliveryOrderIdAndTkPackageIdMapping;
        }
        for (int i = 0; i < successList.size(); i++) {
            JSONObject obj = successList.getJSONObject(i);
            deliveryOrderIdAndTkPackageIdMapping.put(obj.getString("pre_split_pkg_id"), obj.getString("package_id"));
        }
        return deliveryOrderIdAndTkPackageIdMapping;
    }

    /**
     * @param packageId
     * @param documentType Available value: SHIPPING_LABEL/ PICK_LIST/ PACK_LIST
     *                     - SHIPPING_LABEL = 1
     *                     - PICK_LIST = 2
     *                     - SL+PL = 3
     *                     PACK_LIST is not available in this version.
     * @param size         Use this field to specify the size of document to obtain. Available value: A6/A5. A6 by default.
     *                     - A6 = 0
     *                     - A5 = 1
     */
    public static String getPackageShippingPdf(String packageId, String documentType, int size, String apiParam, String channelCode) {
        TKApiParam tkApiParam = JSONObject.parseObject(apiParam, TKApiParam.class);

        Map<String, Object> param = new HashMap<>();
        param.put("package_id", packageId);
        param.put("document_type", tkApiParam.getLabelType());
        param.put("document_size", tkApiParam.getLabelSize());
        String result = sendRequest(PATH_GET_PACKAGE_SHIPPING_DOCUMENT, param, null, HttpMethod.GET, apiParam, channelCode);
        if (StringUtils.isNotBlank(result)) {
            JSONObject resultObj = JSONObject.parseObject(result);
            if (resultObj.getInteger("code") == 0) {
                return resultObj.getJSONObject("data").getString("doc_url");
            }
        }
        return "";
    }


    public static String getOrderShippingPdf(String orderId, String documentType, int size, String apiParam, String channelCode) {
        Map<String, Object> queryParam = new HashMap<>();
        TKApiParam tkApiParam = JSONObject.parseObject(apiParam, TKApiParam.class);
        queryParam.put("shop_id", tkApiParam.getShop_id());
        queryParam.put("order_id", orderId);
        queryParam.put("document_type", tkApiParam.getLabelType());
        queryParam.put("document_size", tkApiParam.getLabelSize());
        String result = sendRequest(PATH_GET_ORDER_SHIPPING_DOCUMENT, queryParam, null, HttpMethod.GET, apiParam, channelCode);
        if (StringUtils.isNotBlank(result)) {
            JSONObject resultObj = JSONObject.parseObject(result);
            if (resultObj.getInteger("code") == 0) {
                return resultObj.getJSONObject("data").getString("doc_url");
            }
        }
        return "";
    }


    public static boolean shippingPackage(ShippingPackageReq req, String apiParam, String channelCode) {
        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("shop_id", JSONObject.parseObject(apiParam, TKApiParam.class).getShop_id());
        String result = sendRequest(PATH_SHIPPING_PACKAGE, queryParam, JSON.toJSONString(req), HttpMethod.POST, apiParam, channelCode);
        if (StringUtils.isBlank(result)) {
            return false;
        }
        JSONObject resultObj = JSONObject.parseObject(result);
        if (resultObj.getInteger("code") != 0) {
            return false;
        }
        JSONObject dataObj = resultObj.getJSONObject("data");
        if (null == dataObj) {
            return true;
        }
        JSONArray failPackages = dataObj.getJSONArray("fail_packages");
        if (CollectionUtils.isEmpty(failPackages)) {
            return true;
        }
        for (Object failPackage : failPackages) {
            if (StringUtils.equals(((JSONObject) failPackage).getString("package_id"), req.getPackage_id())) {
                return false;
            }
        }
        return true;
    }

    public static Map<String, String> batchUploadImages(List<ImageVO> images, String apiParam, String channelCode) {
        Map<String, String> imageMap = new ConcurrentHashMap<>();

        if (CollectionUtils.isEmpty(images)) {
            return imageMap;
        }

        ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 2);
        CountDownLatch latch = new CountDownLatch(images.size());
        images.forEach(image -> {
            executorService.execute(() -> {
                Map<String, Object> postParam = new HashMap<>();
                postParam.put("img_data", image2Base64(image.imageUrl));
                postParam.put("img_scene", image.imgScene);
                String result = sendRequest(PATH_UPLOAD_IMAGE, null, JSON.toJSONString(postParam), HttpMethod.POST, apiParam, channelCode);
                if (StringUtils.isNotBlank(result)) {
                    JSONObject resultObj = JSONObject.parseObject(result);
                    if (resultObj.getInteger("code") == 0) {
                        imageMap.put(image.imageUrl, resultObj.getJSONObject("data").getString("img_id"));
                    }
                }

                latch.countDown();

            });
        });
        try {
            latch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return imageMap;
    }


    private static String sendRequest(String requestPath, Map<String, Object> queryParam, String postBody, HttpMethod method, String apiParam, String channelCode) {
        StringBuffer result = new StringBuffer("");
        try {
            TKApiParam tkApiParam = JSONObject.parseObject(apiParam, TKApiParam.class);

            if (ObjectUtils.isEmpty(queryParam)) {
                queryParam = new TreeMap<>();
            } else {
                queryParam = new TreeMap<>(queryParam);
            }
            queryParam.put("timestamp", System.currentTimeMillis() / 1000);
            queryParam.put("app_key", tkApiParam.getApp_key());
            queryParam.put("shop_id", tkApiParam.getShop_id());
            queryParam.put("sign", getSign(queryParam, tkApiParam, requestPath));
            queryParam.put("access_token", getToken(tkApiParam, channelCode));

            StringBuilder fullRequestUrl = new StringBuilder(tkApiParam.getUrl()).append(requestPath);
            if (ObjectUtils.isNotEmpty(queryParam)) {
                fullRequestUrl.append("?");
                for (String key : queryParam.keySet()) {
                    fullRequestUrl.append(key).append("=").append(queryParam.get(key)).append("&");
                }
            }

            doRequest(postBody, method, result, fullRequestUrl);

        } catch (Exception e) {
            log.error("tiktok-请求接口出错, requestPath={}, queryParam={}, postBody={}, method={}, result={}", requestPath, queryParam, postBody, method, result, e);
        }
        if (StringUtils.equals(requestPath, PATH_UPLOAD_IMAGE)) {
            log.info("tiktok-请求接口结束, requestPath={}, queryParam={}, method={},  result={}", requestPath, queryParam, method, result);
        } else {
            log.info("tiktok-请求接口结束, requestPath={}, queryParam={}, postBody={}, method={}, result={}", requestPath, queryParam, postBody, method, result);
        }

        return result.toString();
    }

    private static void doRequest(String postBody, HttpMethod method, StringBuffer result, StringBuilder fullRequestUrl) throws IOException {
        URL restServiceURL = new URL(fullRequestUrl.toString());
        HttpURLConnection httpConnection = (HttpURLConnection) restServiceURL
                .openConnection();
        httpConnection.setRequestProperty("Connection", "keep-Alive");
        httpConnection.setDoOutput(true);
        httpConnection.setDoInput(true);
        httpConnection.setReadTimeout(10000);
        httpConnection.setConnectTimeout(10000);
        httpConnection.setRequestMethod(method.name());

        if (method.equals(HttpMethod.POST) || method.equals(HttpMethod.PUT)) {
            httpConnection.setRequestProperty("Content-Type", "application/json");
        }

        httpConnection.connect();
        if (null != postBody) {
            OutputStream outputStream = httpConnection.getOutputStream();
            outputStream.write(postBody.getBytes());
            outputStream.flush();
        }


        InputStream inputStream = null;
        if (httpConnection.getResponseCode() >= 400) {
            inputStream = httpConnection.getErrorStream();
        } else {
            inputStream = httpConnection.getInputStream();
        }
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
        String line;
        while (null != (line = bufferedReader.readLine())) {
            result.append(line);
        }
    }


    private static String getSign(Map<String, Object> queryParam, TKApiParam tkApiParam, String requestPath) throws Exception {
        try {
            StringBuilder signBuilder = new StringBuilder(tkApiParam.getApp_secret()).append(requestPath);
            for (String key : queryParam.keySet()) {
                signBuilder.append(key).append(queryParam.get(key));
            }
            signBuilder.append(tkApiParam.getApp_secret());
            Mac hmacSHA256 = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(tkApiParam.getApp_secret().getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            hmacSHA256.init(secret_key);
            byte[] array = hmacSHA256.doFinal(signBuilder.toString().getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte item : array) {
                sb.append(Integer.toHexString((item & 0xFF) | 0x100), 1, 3);
            }
            return sb.toString();
        } catch (Exception e) {
            log.error("tiktok-获取签名失败", e);
            throw e;
        }
    }


    private static String image2Base64(String imageURL) {
        Base64.Encoder ENCODER = Base64.getEncoder();
        try (ByteArrayOutputStream os = new ByteArrayOutputStream();) {
            URL url = new URL(imageURL);
            BufferedImage image = ImageIO.read(url);

            String imageType = getImageType(imageURL);
            if (StringUtils.isBlank(imageType)) {
                return "";
            }

            if (StringUtils.equals("webp", imageType)) {
                ImageReader reader = ImageIO.getImageReadersByMIMEType("image/webp").next();
                WebPReadParam readParam = new WebPReadParam();
                readParam.setBypassFiltering(true);
                reader.setInput(ImageIO.createImageInputStream(url.openStream()));

                BufferedImage image1 = reader.read(0, readParam);
                int height = image1.getHeight();
                int width = image1.getWidth();
                // todo 商品主图按1：1裁剪
                int minLength = height <= width ? height : width;

                BufferedImage subimage = image1.getSubimage(0, 0, minLength, minLength);
                ImageIO.write(subimage, "png", os);
            } else {
                ImageIO.write(image, imageType, os);
            }

            return ENCODER.encodeToString(os.toByteArray());
        } catch (Exception e) {
            log.warn("tiktok-转换图片为base64异常, imageURL={}", imageURL, e);
        }
        return "";
    }

    /**
     * 有效期7天
     */
    public static String getToken(TKApiParam apiParam, String channelCode) throws Exception {
        String token = "";
/*        if (StringUtils.isNotBlank(apiParam.getAccess_token())) {
            return apiParam.getAccess_token();
        }*/

/*        boolean isUseCache = true;
        if (StringUtils.isNotBlank(apiParam.getUse_new_token())) {
            isUseCache = !StringUtils.equals(apiParam.getUse_new_token(), "T");
        }*/

        // if (isUseCache) {
        token = distributedCache.pojoHolder(createCacheKey(channelCode, CACHE_TOKEN_KEY), String.class).get();
        if (StringUtils.isNotBlank(token)) {
            return token;
        } else {
            token = getNewToken(apiParam, channelCode);
        }
        // }
        return token;
    }

    public static void verifyOrderSplit(List<String> orderList, String apiParam, String channelCode) {
        Map<String, Object> param = new HashMap<>();
        param.put("order_id_list", orderList.stream().map(Long::valueOf).collect(Collectors.toList()));
        sendRequest(PATH_VERIFY_ORDER_SPLIT, null, JSON.toJSONString(param), HttpMethod.POST, apiParam, channelCode);
    }

    private static String getNewToken(TKApiParam apiParam, String channelCode) throws IOException {
        StringBuilder fullPath = new StringBuilder();
        fullPath.append(apiParam.getToken_url()).append("/api/v2/token/get?")
                .append("app_key=").append(apiParam.getApp_key()).append("&")
                .append("auth_code=").append(apiParam.getAuth_code()).append("&")
                .append("app_secret=").append(apiParam.getApp_secret()).append("&")
                .append("grant_type=").append(apiParam.getGrant_type());

        StringBuffer result = new StringBuffer();
        try {
            doRequest(null, HttpMethod.GET, result, fullPath);
        } catch (Exception e) {
            log.error("tiktok-请求token异常", e);
            throw e;
        }
        if (StringUtils.isNotBlank(result.toString())) {
            log.info("tiktok 获取新token请求结束, result={}", result);
            JSONObject resultObj = JSONObject.parseObject(result.toString());

            //请求成功设置到redis中，失败打印失败信息
            if (Integer.parseInt(resultObj.getString("code")) == 0) {
                JSONObject data = resultObj.getJSONObject("data");
                String refreshToken = data.getString("refresh_token");
                String accessToken = data.getString("access_token");
                /*long refreshTokenExpireTime = data.getLong("refresh_token_expire_in") - System.currentTimeMillis() / 1000;
                long accessTokenExpireTime = data.getLong("access_token_expire_in") - System.currentTimeMillis() / 1000;*/
                distributedCache.pojoHolder(createCacheKey(channelCode, CACHE_TOKEN_KEY), String.class).set(accessToken);
                distributedCache.pojoHolder(createCacheKey(channelCode, CACHE_REFRESH_KEY), String.class).set(refreshToken);
                return accessToken;
                //throw new ServiceException(new BaseErrorInfo("500", resultObj.getString("message").concat(" - channelCode: " + channelCode)));
            }else {
                log.error("tiktok-请求token异常："+resultObj.getString("message").concat(" - channelCode: " + channelCode));
            }
        }
        return "";
    }

    public static void refreshToken(TKApiParam apiParam, String channelCode) throws IOException {
        String token = distributedCache.pojoHolder(createCacheKey(channelCode, CACHE_TOKEN_KEY), String.class).get();
        if (StringUtils.isBlank(token)) {
            getNewToken(apiParam, channelCode);
            return;
        }
        String refreshToken = distributedCache.pojoHolder(createCacheKey(channelCode, CACHE_REFRESH_KEY), String.class).get();
        StringBuilder fullPath = new StringBuilder(apiParam.getToken_url())
                .append("/api/v2/token/refresh?")
                .append("app_key=").append(apiParam.getApp_key()).append("&")
                .append("app_secret=").append(apiParam.getApp_secret()).append("&")
                .append("refresh_token=").append(refreshToken).append("&")
                .append("grant_type=").append("refresh_token");
        StringBuffer result = new StringBuffer();

        doRequest(null, HttpMethod.GET, result, fullPath);
        if (StringUtils.isNotBlank(result.toString())) {
            log.info("tiktok 刷新token请求结束, result={}", result);
            JSONObject resultObj = JSONObject.parseObject(result.toString());

            if (Integer.parseInt(resultObj.getString("code")) != 0) {
                return;
            }
            JSONObject data = resultObj.getJSONObject("data");
            String accessToken = data.getString("access_token");
            refreshToken = data.getString("refresh_token");

/*            long refreshTokenExpireTime = data.getLong("refresh_token_expire_in") - System.currentTimeMillis() / 1000;
            long accessTokenExpireTime = data.getLong("access_token_expire_in") - System.currentTimeMillis() / 1000;*/

            distributedCache.pojoHolder(createCacheKey(channelCode, CACHE_TOKEN_KEY), String.class).set(accessToken);
            distributedCache.pojoHolder(createCacheKey(channelCode, CACHE_REFRESH_KEY), String.class).set(refreshToken);
        }
    }


    private static String createCacheKey(String channelCode, String type) {
        return "TRADE:MERCHANT" + ":" + channelCode + ":" + type;
    }


    private static String getImageType(String imageUrl) {
        if (imageUrl.endsWith("jpg")) {
            return "jpg";
        }
        if (imageUrl.endsWith("jpeg")) {
            return "jpeg";
        }
        if (imageUrl.endsWith("png")) {
            return "png";
        }
        if (imageUrl.endsWith("webp")) {
            return "webp";
        }

        log.warn("tiktok-未知图片类型, imageUrl={}", imageUrl);
        return "";
    }


    private static void getOrders(TKFetchOrderReq req, String apiParam, String channelCode, List<JSONObject> orders) {
        String result = sendRequest(PATH_GET_ORDER, null, JSON.toJSONString(req), HttpMethod.POST, apiParam, channelCode);
        if (StringUtils.isNotBlank(result)) {
            JSONObject resultObj = JSONObject.parseObject(result);
            if (resultObj.getInteger("code").equals(0)) {
                JSONObject dataObj = resultObj.getJSONObject("data");
                JSONArray order_list = dataObj.getJSONArray("order_list");
                if (CollectionUtils.isNotEmpty(order_list)) {
                    List<String> orderIds = order_list.stream().map(item -> ((JSONObject) item).getString("order_id")).collect(Collectors.toList());
                    List<JSONObject> orderDetails = batchGetOrderDetail(orderIds, apiParam, channelCode);
                    if (CollectionUtils.isNotEmpty(orderDetails)) {
                        orders.addAll(orderDetails);
                    }
                }
                if (dataObj.getBoolean("more")) {
                    req.setCursor(dataObj.getString("next_cursor"));
                    getOrders(req, apiParam, channelCode, orders);
                }
            }

        }
    }


    public static List<TrackInfo> getPackageShippingInfo(String packageId, String param, String channelCode) {
        TKApiParam tkApiParam = JSONObject.parseObject(param, TKApiParam.class);
        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("shop_id", tkApiParam.getShop_id());
        queryParam.put("package_id", packageId);
        String result = sendRequest(PATH_GET_PACKAGE_SHIPPING_INFO, queryParam, null, HttpMethod.GET, param, channelCode);
        if (StringUtils.isBlank(result)) {
            return new ArrayList<>();
        }
        JSONObject resultObj = JSONObject.parseObject(result);
        if (resultObj.getInteger("code") != 0) {
            return new ArrayList<>();
        }
        String trackInfos = resultObj.getJSONObject("data").getString("tracking_info_list");
        if (StringUtils.isBlank(trackInfos)) {
            return new ArrayList<>();
        }
        return JSONArray.parseArray(trackInfos, TrackInfo.class);
    }

    public static TrackInfoList getOrderShippingInfo(String orderId, String param, String channelCode) {
        TKApiParam tkApiParam = JSONObject.parseObject(param, TKApiParam.class);
        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("shop_id", tkApiParam.getShop_id());
        queryParam.put("order_id", orderId);
        String result = sendRequest(PATH_GET_ORDER_SHIPPING_INFO, queryParam, null, HttpMethod.GET, param, channelCode);
        if (StringUtils.isBlank(result)) {
            return new TrackInfoList();
        }
        JSONObject resultObj = JSONObject.parseObject(result);
        if (resultObj.getInteger("code") != 0) {
            return new TrackInfoList();
        }
        String trackInfos = resultObj.getJSONObject("data").getString("tracking_info_list");
        if (StringUtils.isBlank(trackInfos)) {
            return new TrackInfoList();
        }
        return JSON.parseObject(trackInfos, TrackInfoList.class);
    }
}
