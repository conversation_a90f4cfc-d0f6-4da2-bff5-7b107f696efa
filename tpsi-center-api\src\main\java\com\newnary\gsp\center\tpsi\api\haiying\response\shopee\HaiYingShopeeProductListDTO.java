package com.newnary.gsp.center.tpsi.api.haiying.response.shopee;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingShopeeProductListDTO {

    /**
     * 商品唯一ID
     */
    private String pid;

    /**
     * 商品前30天销售件数
     */
    private Integer sold;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 店铺名称
     */
    private String shop_name;

    /**
     * 商品评分
     */
    private BigDecimal rating;

    /**
     * 商品默认价
     */
    private BigDecimal price;

    /**
     * 商品最低价
     */
    private BigDecimal min_price;

    /**
     * 商品最高价
     */
    private BigDecimal max_price;

    /**
     * 商品上架时间
     */
    private Long gen_time;

    /**
     * 商品主图
     */
    private String image;

    /**
     * 商品收藏人数
     */
    private Integer favorite;

    /**
     * 商品评分数
     */
    private Integer ratings;

    /**
     * 商品所属店铺id
     */
    private String shop_id;

    /**
     * 店铺开张时间
     */
    private Long approved_date;

    /**
     * 商品状态
     */
    private Integer status;

    /**
     * 商品是否存在
     */
    private Boolean not_exist;

    /**
     * 商品最新抓取时间
     */
    private Long last_modi_time;

    /**
     * 商品归属的类目ID(由一级至子类，多个以逗号分隔)
     */
    private List<String> cids;

    /**
     * 商品所属的类目结构
     */
    private String category_structure;

    /**
     * 商品统计时间
     */
    private Long stat_time;

    /**
     * 商品预计到货时间
     */
    private Integer estimated_days;

    /**
     * 商品是否热销
     * (预留字段)
     */
    private Boolean is_hot_sales;

    /**
     * 商品是否虾皮优选
     */
    private Boolean is_shopee_verified;

    /**
     * 商品所属店铺是否官方店铺
     */
    private Boolean is_official_shop;

    /**
     * 店铺所在地
     */
    private String shop_location;

    /**
     * 商品总销售件数
     */
    private Integer historical_sold;

    /**
     * 店主名称
     */
    private String user_name;

    /**
     * 商品前30天销售额
     */
    private BigDecimal payment;

    /**
     * 店铺所在
     * 0: 本地   1:海外   null:未知
     */
    private Integer shipping_icon_type;

    /**
     * shopee商品链接
     */
    private String product_url;

    /**
     * shopee店铺链接
     */
    private String shop_url;

    /**
     * 商品浏览数
     */
    private Integer view_count;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 店铺商品总数
     */
    private Integer shop_products_count;

    /**
     * 货币
     */
    private String currency;

}
