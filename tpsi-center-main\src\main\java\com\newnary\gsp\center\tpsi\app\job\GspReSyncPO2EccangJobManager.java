package com.newnary.gsp.center.tpsi.app.job;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.common.PageCondition;
import com.newnary.api.base.common.PageList;
import com.newnary.gsp.center.purchase.api.order.request.PurchaseOrderPageQueryCommand;
import com.newnary.gsp.center.purchase.api.order.response.PurchaseOrderInfo;
import com.newnary.gsp.center.tpsi.api.common.enums.ThirdPartEventType;
import com.newnary.gsp.center.tpsi.api.eccang.request.GspSyncPo2EccangCommand;
import com.newnary.gsp.center.tpsi.app.service.eccang.EccangPoMgmtApp;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyPushLogInfo;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.ThirdPartyPushLogManager;
import com.newnary.gsp.center.tpsi.infra.rpc.PurchaseRpc;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.job.core.handler.annotation.Job;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * GSP重新同步采购单到易仓定时任务
 */
@Slf4j
@Component
public class GspReSyncPO2EccangJobManager {

    @Resource
    private ThirdPartyPushLogManager thirdPartyPushLogManager;
    @Resource
    private EccangPoMgmtApp eccangPoMgmtApp;
    @Resource
    private PurchaseRpc purchaseRpc;

    private static final String GSP_FOR_EC_JOB_NAME = "GSP同步采购单到易仓失败重试自动任务";

    private static final String EC_FOR_GSP_JOB_NAME = "易仓同步采购单收货信息到GSP系统自动任务";

    private static final String EC_SUCCESS_FOR_GSP_JOB_NAME = "易仓同步采购单收货信息完成的信息到GSP采购系统自动任务";

    private static final String EC_PARTIAL_ARRIVAL_FOR_GSP_JOB_NAME = "易仓同步采购单部分收货信息到GSP采购系统自动任务";

    private static final String GSP_FOR_EC_CREATE_JOB_NAME = "易仓同步GSP创建采购单自动任务";

    private static final String GSP_SYNC_EC_JOB_NAME = "GSP同步采购单物流单号到易仓失败重试自动任务";

    private static final String EC_SUCCESS_GSP_NO_FINISH_FOR_GSP_JOB_NAME = "易仓已完成收货GSP采购系统单据状态未变更自动任务";

    private static final String GSP_CANCEL_STOCK_ORDER_EC_SUCCESS_JOB_NAME = "GSP取消入库重新补发后易仓重新收货完成同步收货信息任务";

    /**
     * 本地线程池, 固定容量, 用于异步对商详落库操作等
     **/
    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors() * 8,
            Runtime.getRuntime().availableProcessors() * 8,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque<>(100),
            new ThreadPoolExecutor.CallerRunsPolicy());

    @Job("gspReSyncPO2EccangJob")
    public ReturnT<String> gspReSyncPO2EccangJob(String param) {
        log.info("[{}] 开始执行！", GSP_FOR_EC_JOB_NAME);
        try {
            // 1. 初始化任务参数
            JSONObject paramObj = JSON.parseObject(param);
            Integer pageSize = paramObj.getInteger("pageSize");
            if (pageSize == null) {
                pageSize = 50;
            }
            List<ThirdPartyPushLogInfo> logInfoList = null;
            // 2.1. 初始化分页参数
            PageCondition pageCondition = new PageCondition();
            pageCondition.setPageSize(pageSize);
            pageCondition.setPageNum(1);
            // 2.2. 查询待处理任务列表
            PageList<ThirdPartyPushLogInfo> logInfoPageResult = thirdPartyPushLogManager.pageQueryByEventTypeAndFailState(ThirdPartEventType.GSP_2_ECCANG_PO_CREATE.name(), pageCondition);
            logInfoList = logInfoPageResult.getItems();
            // 2.3. 批量处理
            int reTryCount = 0;
            if (CollectionUtils.isNotEmpty(logInfoList)) {
                for (ThirdPartyPushLogInfo logInfo : logInfoList) {
                    try {
                        GspSyncPo2EccangCommand command = JSON.parseObject(logInfo.getEventData(), GspSyncPo2EccangCommand.class);
                        eccangPoMgmtApp.doSync(command);
                    } catch (Exception e) {
                        log.error("[{}] 处理单条数据异常！orderId={}, e={}", logInfo.getEventBizId(), e);
                    }
                    reTryCount++;
                }
            }
            log.info("[{}] 任务执行完成！共重试数据 reTryCount={}", GSP_FOR_EC_JOB_NAME, reTryCount);
        } catch (Exception e) {
            log.info("[{}] 任务执行异常！e={}", GSP_FOR_EC_JOB_NAME, e);
        }
        return ReturnT.SUCCESS;
    }

    @Job("ecReSyncPO2GspJob")
    public ReturnT<String> ecReSyncPO2GspJob(String param) {
        log.info("[{}] 开始执行！", EC_FOR_GSP_JOB_NAME);
        try {
            // 1. 初始化任务参数
            JSONObject paramObj = JSON.parseObject(param);
            Integer pageSize = paramObj.getInteger("pageSize");
            if (pageSize == null) {
                pageSize = 50;
            }
            String eventBizId = paramObj.getString("eventBizId");
            List<ThirdPartyPushLogInfo> logInfoList = null;
            // 2.1. 初始化分页参数
            PageCondition pageCondition = new PageCondition();
            pageCondition.setPageSize(pageSize);
            pageCondition.setPageNum(1);
            // 2.2. 查询待处理任务列表
            PageList<ThirdPartyPushLogInfo> logInfoPageResult = thirdPartyPushLogManager.pageQueryByEventTypeAndFailStateSyncEcInfo(ThirdPartEventType.GSP_2_ECCANG_PO_CREATE.name(), pageCondition, eventBizId);
            logInfoList = logInfoPageResult.getItems();
            // 2.3. 批量处理
            int reTryCount = 0;
//            List<List<ThirdPartyPushLogInfo>> groupLogInfoList = ListUtils.partition(logInfoList, 50);
//            if (CollectionUtils.isNotEmpty(groupLogInfoList)) {
//                for (List<ThirdPartyPushLogInfo> logInfos : groupLogInfoList) {
//                    final Optional<TenantID> tenantIdKept = TenantCarrier.getTenantID();
//                    logInfos.forEach(logInfo -> CompletableFuture.runAsync(() -> {
//                        boolean tenantIdMiss = false;
//                        try {
//                            if (!TenantCarrier.getTenantID().isPresent()) {
//                                TenantID tenantID = tenantIdKept.orElse(null);
//                                TenantCarrier.setTenantID(tenantID);
//                                tenantIdMiss = tenantID != null;
//                            }
//                            GspSyncPo2EccangCommand command = JSON.parseObject(logInfo.getEventData(), GspSyncPo2EccangCommand.class);
//                            eccangPoMgmtApp.doSync2GspOrder(command, logInfo);
//                        } catch (Exception e) {
//                            log.error("处理单条数据异常！orderId={}, e={}", logInfo.getEventBizId(), e);
//                        } finally {
//                            if (tenantIdMiss) {
//                                TenantCarrier.clearTenantID();
//                            }
//                        }
//                    }, executor));
//                    reTryCount = reTryCount + logInfos.size();
//                }
//            }
            if (CollectionUtils.isNotEmpty(logInfoList)) {
                // 打乱顺序（易仓限流，永远只能读取前二十个，打乱顺序增加同步概率）
                Collections.shuffle(logInfoList);
                for (ThirdPartyPushLogInfo logInfo : logInfoList) {
                    try {
                        GspSyncPo2EccangCommand command = JSON.parseObject(logInfo.getEventData(), GspSyncPo2EccangCommand.class);
                        eccangPoMgmtApp.doSync2GspOrder(command, logInfo);
                    } catch (Exception e) {
                        log.error("处理单条数据异常！orderId={}, e={}", logInfo.getEventBizId(), e);
                    }
                    reTryCount++;
                }
            }
            log.info("[{}] 任务执行完成！共处理数据 reTryCount={}", EC_FOR_GSP_JOB_NAME, reTryCount);
        } catch (Exception e) {
            log.info("[{}] 任务执行异常！e={}", EC_FOR_GSP_JOB_NAME, e);
        }
        return ReturnT.SUCCESS;
    }

    @Job("ReSyncCreatePurchaseOrderJob")
    public ReturnT<String> reSyncCreatePurchaseOrderJob(String param) {
        log.info("开始执行定时补偿同步创建三方采购单任务");
        try {
            int pageSize = 50;
            List<Long> purchaseOrderIdList = new ArrayList<>();
            PurchaseOrderPageQueryCommand command = new PurchaseOrderPageQueryCommand();
            PageCondition pageCondition = new PageCondition();
            pageCondition.setPageSize(pageSize);
            pageCondition.setPageNum(1);
            command.setPageCondition(pageCondition);
            command.setOrderStatus(Arrays.asList(40));
            command.setDateKey("createDate");
            command.setBeginDate("2025-01-01"); //只从2025年开始
            command.setEndDate(DateUtil.format(new Date(), "yyyy-MM-dd"));
            PageList<PurchaseOrderInfo> purchaseOrderList = purchaseRpc.queryPurchaseOrderList(command);
            purchaseOrderIdList.addAll(purchaseOrderList.getItems().stream().map(PurchaseOrderInfo::getId).collect(Collectors.toList()));
            //循环获取采购单分页直至全部获取
            int totalPage = purchaseOrderList.getPageMeta().getPages();
            for (int i = 2; i <= totalPage; i++) {
                command.setPageCondition(new PageCondition(i, pageSize));
                purchaseOrderList = purchaseRpc.queryPurchaseOrderList(command);
                purchaseOrderIdList.addAll(purchaseOrderList.getItems().stream().map(PurchaseOrderInfo::getId).collect(Collectors.toList()));
            }
            log.info("获取到符合条件采购单总数：{}", purchaseOrderIdList.size());
            int success = 0;
            if (CollectionUtils.isNotEmpty(purchaseOrderIdList)) {
                for (Long purchaseOrderId : purchaseOrderIdList) {
                    try {
                        String purchaseOrderIdStr = String.valueOf(purchaseOrderId);
                        ThirdPartyPushLogInfo thirdPartyPushLogInfo = thirdPartyPushLogManager.queryByEventBizId(ThirdPartEventType.GSP_2_ECCANG_PO_CREATE.name(), purchaseOrderIdStr);
                        if (Objects.nonNull(thirdPartyPushLogInfo)) {
                            continue;
                        }
                        GspSyncPo2EccangCommand createCommand = new GspSyncPo2EccangCommand();
                        createCommand.setPurchaseOrderId(purchaseOrderIdStr);
                        createCommand.setCurrOptUserId("sys");
                        eccangPoMgmtApp.syncCreateEccangPurchaseOrder(createCommand);
                        success++;
                    } catch (Exception e) {
                        log.error("处理单条数据异常！orderId={}, e={}", purchaseOrderId, e);
                    }
                }
            }
            log.info("[{}] 任务执行完成！共处理数据 success={}", GSP_FOR_EC_CREATE_JOB_NAME, success);
        } catch (Exception e) {
            log.info("[{}] 任务执行异常！e={}", GSP_FOR_EC_CREATE_JOB_NAME, e);
        }
        return ReturnT.SUCCESS;
    }

    @Job("PO2ecSyncCreateJob")
    public ReturnT<String> po2ecSyncCreateJob(String param) {
        log.info("[{}] 开始执行！", GSP_FOR_EC_CREATE_JOB_NAME);
        try {
            // 1. 初始化任务参数
            JSONObject paramObj = JSON.parseObject(param);
            Integer pageSize = paramObj.getInteger("pageSize");
            if (pageSize == null) {
                pageSize = 50;
            }
            List<ThirdPartyPushLogInfo> logInfoList = null;
            // 2.1. 初始化分页参数
            PageCondition pageCondition = new PageCondition();
            pageCondition.setPageSize(pageSize);
            pageCondition.setPageNum(1);
            // 2.2. 查询待处理任务列表
            PageList<ThirdPartyPushLogInfo> logInfoPageResult = thirdPartyPushLogManager.pageQueryByEventTypeAndFailStateSyncEcCreate(ThirdPartEventType.GSP_2_ECCANG_PO_CREATE.name(), pageCondition);
            logInfoList = logInfoPageResult.getItems();
            // 2.3. 批量处理
            int reTryCount = 0;
            if (CollectionUtils.isNotEmpty(logInfoList)) {
                for (ThirdPartyPushLogInfo logInfo : logInfoList) {
                    try {
                        GspSyncPo2EccangCommand command = JSON.parseObject(logInfo.getEventData(), GspSyncPo2EccangCommand.class);
                        eccangPoMgmtApp.syncCreateEccangPurchaseOrder(command);
                    } catch (Exception e) {
                        log.error("[{}] 处理单条数据异常！orderId={}, e={}", GSP_FOR_EC_CREATE_JOB_NAME, logInfo.getEventBizId(), e);
                    }
                    reTryCount++;
                }
            }
            log.info("[{}] 任务执行完成！共处理数据 reTryCount={}", GSP_FOR_EC_CREATE_JOB_NAME, reTryCount);
        } catch (Exception e) {
            log.info("[{}] 任务执行异常！e={}", GSP_FOR_EC_CREATE_JOB_NAME, e);
        }
        return ReturnT.SUCCESS;
    }

    @Job("gspReSyncTrackingNumber2EccangJob")
    public ReturnT<String> gspReSyncTrackingNumber2EccangJob(String param) {
        log.info("[{}] 开始执行！", GSP_SYNC_EC_JOB_NAME);
        try {
            // 1. 初始化任务参数
            JSONObject paramObj = JSON.parseObject(param);
            Integer pageSize = paramObj.getInteger("pageSize");
            if (pageSize == null) {
                pageSize = 50;
            }
            List<ThirdPartyPushLogInfo> logInfoList = null;
            // 2.1. 初始化分页参数
            PageCondition pageCondition = new PageCondition();
            pageCondition.setPageSize(pageSize);
            pageCondition.setPageNum(1);
            // 2.2. 查询待处理任务列表
            PageList<ThirdPartyPushLogInfo> logInfoPageResult = thirdPartyPushLogManager.pageQueryByEventTypeAndFailState(ThirdPartEventType.GSP_2_ECCANG_SYNC_TRACKING_NUMBER.name(), pageCondition);
            logInfoList = logInfoPageResult.getItems();
            // 2.3. 批量处理
            int reTryCount = 0;
            if (CollectionUtils.isNotEmpty(logInfoList)) {
                for (ThirdPartyPushLogInfo logInfo : logInfoList) {
                    try {
                        GspSyncPo2EccangCommand command = JSON.parseObject(logInfo.getEventData(), GspSyncPo2EccangCommand.class);
                        eccangPoMgmtApp.syncTrackingNumber2EcangPurchaseOrder(command);
                    } catch (Exception e) {
                        log.error("[{}] 处理单条数据异常！orderId={}, e={}", GSP_SYNC_EC_JOB_NAME, logInfo.getEventBizId(), e);
                    }
                    reTryCount++;
                }
            }
            log.info("[{}] 任务执行完成！共处理数据 reTryCount={}", GSP_SYNC_EC_JOB_NAME, reTryCount);
        } catch (Exception e) {
            log.info("[{}] 任务执行异常！e={}", GSP_SYNC_EC_JOB_NAME, e);
        }
        return ReturnT.SUCCESS;
    }

    @Job("ecReSyncSuccessOrder2EccangJob")
    public ReturnT<String> ecReSyncSuccessOrder2EccangJob(String param) {
        log.info("[{}] 开始执行！", EC_SUCCESS_FOR_GSP_JOB_NAME);
        try {
            //调用易仓的完成收货的采购单，同步回到采购系统
            String orderRefNo = null;
            JSONObject paramObj = JSON.parseObject(param);
            if (Objects.nonNull(paramObj)) {
                orderRefNo = paramObj.getString("orderRefNo");
            }
            eccangPoMgmtApp.completedEcOrder(orderRefNo);
        } catch (Exception e) {
            log.info("[{}] 任务执行异常！e={}", EC_SUCCESS_FOR_GSP_JOB_NAME, e);
        }
        return ReturnT.SUCCESS;
    }

    @Job("ecReSyncPartialArrivalOrder2EccangJob")
    public ReturnT<String> ecReSyncPartialArrivalOrder2EccangJob(String param) {
        log.info("[{}] 开始执行！", EC_PARTIAL_ARRIVAL_FOR_GSP_JOB_NAME);
        try {
            //调用易仓的完成收货的采购单，同步回到采购系统
            eccangPoMgmtApp.partialArrivalEcOrder();
        } catch (Exception e) {
            log.info("[{}] 任务执行异常！e={}", EC_PARTIAL_ARRIVAL_FOR_GSP_JOB_NAME, e);
        }
        return ReturnT.SUCCESS;
    }

    @Job("ecSyncSuccessOrderAndGSPNoSuccess2EccangJob")
    public ReturnT<String> ecSyncSuccessOrderAndGSPNoSuccess2EccangJob(String param) {
        log.info("[{}] 开始执行！", EC_SUCCESS_GSP_NO_FINISH_FOR_GSP_JOB_NAME);
        try {
            // 采购系统已付款、部分收货的单据查询易仓收货状态
            eccangPoMgmtApp.gspStatusChangeEcCompletedOrder(param);
        } catch (Exception e) {
            log.info("[{}] 任务执行异常！e={}", EC_SUCCESS_GSP_NO_FINISH_FOR_GSP_JOB_NAME, e);
        }
        return ReturnT.SUCCESS;
    }

    @Job("gspReSyncCancelStockOrderAndEcSuccessJob")
    public ReturnT<String> gspReSyncCancelStockOrderAndEcSuccessJob(String param) {
        log.info("[{}] 开始执行！", GSP_CANCEL_STOCK_ORDER_EC_SUCCESS_JOB_NAME);
        try {
            // 采购系统已付款、部分收货的单据查询易仓收货状态
            eccangPoMgmtApp.gspReSyncCancelStockOrderAndEcSuccessJob(param);
        } catch (Exception e) {
            log.info("[{}] 任务执行异常！e={}", GSP_CANCEL_STOCK_ORDER_EC_SUCCESS_JOB_NAME, e);
        }
        return ReturnT.SUCCESS;
    }

}