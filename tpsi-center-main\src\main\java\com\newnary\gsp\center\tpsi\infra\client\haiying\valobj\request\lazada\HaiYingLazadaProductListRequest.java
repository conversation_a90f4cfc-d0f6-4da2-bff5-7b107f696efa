package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.lazada;

import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingLazadaProductListOrderBy;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingOrderByType;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingLazadaProductListRequest {

    /**
     * 站点
     */
    @NotNull(message = "站点不能为空")
    private String station;
    /**
     * 商品标题(string[] 型)
     */
    /**
     * 商品标题(string[] 型)
     */
    private List<String> title;

    /**
     * 商品标题查询类型(int 型):
     * 1:整句语句(默认)
     * 2:多个搜索词,”与”关系
     * 3:多个搜索词,”或”关系
     */
    private Integer title_type;

    /**
     * 商品不包含标题(string[] 型)
     */
    private List<String> not_exist_title;

    /**
     * 商品不包含标题查询类型(int 型):
     * 1:整句语句(默认)
     * 2:多个搜索词,”与”关系
     * 3:多个搜索词,”或”关系
     */
    private Integer not_exist_title_type;

    /**
     * 商品id(string[] 型)
     */
    private List<String> item_ids;

    /**
     * 商品品牌(string型)
     */
    private String brand;

    /**
     * 商品价格起始值(double型)
     */
    private BigDecimal price_start;

    /**
     * 商品价格结束值(double型)
     */
    private BigDecimal price_end;

    /**
     * 商品商家名称或商品店铺名称(string型)
     */
    private String seller_or_shop;

    /**
     * 商品评论数起始值(int 型)
     */
    private Integer review_start;

    /**
     * 商品评论数结束值(int 型)
     */
    private Integer review_end;

    /**
     * 商品评分起始值(double型)
     */
    private Double rating_start;

    /**
     * 商品评分结束值(double型)
     */
    private Double rating_end;

    /**
     * 商品发货地(string[] 型)
     */
    private List<String> location;

    /**
     * 商品类目路径(string[] 型)
     * 类目路径英文;分隔
     * 例: p_l1_name+;+p_l2_name+;+cname
     */
    private List<String> cname_path;

    /**
     * 商品发货地：
     * 默认不限
     * 1：中国
     * 2：非中国
     */
    private Integer location_type;

    /**
     * 最新抓取时间起始值(string型格式:年-月-日)
     */
    private String last_modi_time_start;

    /**
     * 最新抓取时间结束值(string型格式:年-月-日)
     */
    private String last_modi_time_end;

    /**
     * 商品所属卖家名称(string[] 型)
     */
    private List<String> sellers;

    /**
     * 商品不包含所属卖家名称(string[] 型)
     */
    private List<String> not_exist_sellers;

    /**
     * 商品前7天销量起始值(int 型)
     */
    private Integer seven_days_sold_start;

    /**
     * 商品前7天销量结束值(int 型)
     */
    private Integer seven_days_sold_end;

    /**
     * 商品前7天销售额起始值(double型)
     */
    private BigDecimal seven_days_payment_start;

    /**
     * 商品前7天销售额结束值(double型)
     */
    private BigDecimal seven_days_payment_end;

    /**
     * 商品总销量起始值(int 型)
     */
    private Integer sold_start;

    /**
     * 商品总销量结束值(int 型)
     */
    private Integer sold_end;

    /**
     * 排序方式:
     * price(价格)
     * rating(商品评分)
     * review(商品评论数)
     * seven_days_sold(商品前7天销量)
     * seven_days_payment(商品前7天销售额)
     * sold(商品总销量)
     * insert_time(商品首次发现时间)
     */
    private String order_by;

    /**
     * 排序类型:    ASC 升序    DESC 降序
     */
    private String order_by_type;

    /**
     * 当前页码(int 型)
     */
    private String current_page;

    /**
     * 每一页的数据量(默认海鹰设置 全部)(int 型)
     * 数值范围[1-10000]
     */
    private String page_size;

}
