package com.newnary.gsp.center.tpsi.service.mabang.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.gsp.center.tpsi.api.mabang.request.SyncToProductLibraryV2Command;
import com.newnary.gsp.center.tpsi.infra.client.mabang.MaBangGWApiClient;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.MaBangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.dev.MaBangSyncToProductLibraryV2;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.mq.producer.ApiDockingProducer;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.service.impl.SystemClientSve;
import com.newnary.gsp.center.tpsi.service.mabang.IMaBangDevApiSve;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class MaBangDevApiSveImpl extends SystemClientSve implements IMaBangDevApiSve {

    public static final Logger LOGGER = LoggerFactory.getLogger(MaBangDevApiSveImpl.class);

    @Resource
    private ApiDockingProducer apiDockingProducer;

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Override
    public void devGetInLibraryProduct(String thirdPartySystemId, String context) {
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        //获取apiClient
        MaBangGWApiClient maBangGWApiClient = getClient(thirdPartySystem.getParams());
        //调用马帮获取产品
        int total = 0;
        int pageNo = 1;

        do {
            LOGGER.info("现在获取第{}页", pageNo);
            //循环获取分页
            MaBangApiBaseResult<String> ret = maBangGWApiClient.devGetInLibraryProducts(context, pageNo);
            total = JSON.parseObject(ret.getData()).getInteger("total");
            //处理结果
            pageNo++;
        } while (pageNo * 100 <= total);
    }

    @Override
    public String devSyncToProductLibrary(SyncToProductLibraryV2Command req) {
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(req.getThirdPartySystemId());
        //获取apiClient
        MaBangGWApiClient maBangGWApiClient = getClient(thirdPartySystem.getParams());

        MaBangSyncToProductLibraryV2 maBangSyncToProductLibraryV2 = this.maBangSyncToProductLibraryV2Dt(req);

        MaBangApiBaseResult<String> ret = maBangGWApiClient.devSyncToProductLibrary(maBangSyncToProductLibraryV2);

        //反馈结果
        if (ret.getCode().equals("200")) {
            JSONObject data = JSONObject.parseObject(ret.getData());
            if (data.getInteger("errorCount") > 0) {
                String errorMessage = data.getString("error");
                return errorMessage;
            } else {
                //创建成功
                LOGGER.info("产品推送成功");
                return "SUCCESS";
            }
        } else {
            String errorMessage = ret.getMessage();
            return errorMessage;
        }

    }

    private MaBangSyncToProductLibraryV2 maBangSyncToProductLibraryV2Dt(SyncToProductLibraryV2Command req) {
        MaBangSyncToProductLibraryV2 maBangSyncToProductLibraryV2 = new MaBangSyncToProductLibraryV2();
        //TODO 1、根据sku查询产品信息

        //TODO 2、对查询到的产品信息转为马帮创建订单请求对象

        return maBangSyncToProductLibraryV2;
    }

    private MaBangGWApiClient getClient(String maBangParams) {
        return new MaBangGWApiClient(maBangParams);
    }

}
