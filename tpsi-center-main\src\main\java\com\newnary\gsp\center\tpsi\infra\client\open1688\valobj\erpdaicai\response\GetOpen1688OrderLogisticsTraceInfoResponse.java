package com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class GetOpen1688OrderLogisticsTraceInfoResponse {

    /**
     * 跟踪单详情
     */
    private List<LogisticsTrace> logisticsTraces;

    @Setter
    @Getter
    public static class LogisticsTrace{

        /**
         * 物流编号，如BX110096003841234
         */
        private String logisticsId;

        /**
         * 订单编号
         */
        private Long orderId;

        /**
         * 物流单编号，如480330616596
         */
        private String logisticsBillNo;

        /**
         * 物流跟踪步骤
         */
        private List<LogisticsStep> logisticsSteps;
        @Setter
        @Getter
        public static class LogisticsStep{

            /**
             * 物流跟踪单该步骤的时间
             */
            private String acceptTime;

            /**
             * 备注，如：“在浙江浦江县公司进行下级地点扫描，即将发往：广东深圳公司”
             */
            private String remark;
        }
    }
}
