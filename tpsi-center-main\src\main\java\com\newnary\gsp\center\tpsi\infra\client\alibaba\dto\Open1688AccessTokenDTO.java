package com.newnary.gsp.center.tpsi.infra.client.alibaba.dto;

import com.alibaba.ocean.rawsdk.client.entity.AuthorizationToken;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 阿里的SDK在Setter方法里面埋屎会导致序列化框架异常, 故自本地定义对象来装载1688开放平台的token
 *
 * <AUTHOR>
 * @since Created on 2022-07-19
 **/
@Getter
@Setter
public class Open1688AccessTokenDTO {

    private String accessToken;
    private String refreshToken;
    private long expiresIn;
    private Date expiresTime;
    private Date refreshTokenTimeout;
    private String resourceOwner;
    private String uid;
    private long aliId;
    private String memberId;

    public Open1688AccessTokenDTO() {
    }

    public Open1688AccessTokenDTO(AuthorizationToken authorizationToken) {
        this.accessToken = authorizationToken.getAccess_token();
        this.refreshToken = authorizationToken.getRefresh_token();
        this.expiresIn = authorizationToken.getExpires_in();
        this.expiresTime = authorizationToken.getExpires_time();
        this.refreshTokenTimeout = authorizationToken.getRefresh_token_timeout();
        this.resourceOwner = authorizationToken.getResource_owner();
        this.uid = authorizationToken.getUid();
        this.aliId = authorizationToken.getAliId();
        this.memberId = authorizationToken.getMemberId();
    }

    public boolean hasExpired() {
        return getExpiresTime().before(new Date());
    }

}
