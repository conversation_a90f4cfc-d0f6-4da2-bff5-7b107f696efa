# 发票OCR工具版本更新说明

## v1.1.0 (当前版本)

### 🔧 重要修复
- **修复PaddleOCR兼容性问题**：移除了已弃用的 `use_gpu` 参数
- **修复弃用警告**：将 `use_angle_cls` 更新为 `use_textline_orientation`
- **自动GPU检测**：新版本PaddleOCR会自动检测和使用GPU，无需手动配置

### ✨ 新增功能
- **自动安装脚本**：`install_dependencies.py` - 自动安装所有依赖
- **快速启动脚本**：`quick_start.py` - 引导式的完整使用流程
- **简单测试脚本**：`simple_test.py` - 快速诊断安装问题
- **进度显示**：处理多个文件时显示实时进度
- **详细统计**：显示处理成功/失败的文件统计

### 🛠️ 改进
- **更好的错误处理**：详细的错误信息和日志记录
- **用户体验优化**：更友好的提示信息和引导
- **文档完善**：添加了详细的故障排除指南

### 📋 API变更
- 移除了 `use_gpu` 参数（PaddleOCR会自动检测GPU）
- 移除了命令行 `--gpu` 选项
- 保持向后兼容，现有代码仍可正常工作

## v1.0.0 (初始版本)

### ✨ 核心功能
- 支持PDF和图片格式发票识别
- 自动识别增值税专用发票和普通发票
- 提取关键字段信息
- 批量处理文件夹
- 导出到Excel格式

---

## 升级指南

### 从v1.0.0升级到v1.1.0

1. **更新代码**：
   ```python
   # 旧版本
   processor = InvoiceOCRProcessor(use_gpu=True)
   
   # 新版本（推荐）
   processor = InvoiceOCRProcessor()  # 自动检测GPU
   ```

2. **重新安装依赖**：
   ```bash
   python install_dependencies.py
   ```

3. **测试安装**：
   ```bash
   python simple_test.py
   ```

### 兼容性说明
- Python 3.7+ 仍然支持
- 所有现有的发票处理功能保持不变
- 输出格式完全兼容

---

## 常见升级问题

### Q: 升级后出现 "Unknown argument: use_gpu" 错误
A: 这是正常的，新版本已经移除了这个参数。请使用新版本的代码。

### Q: 如何确认GPU是否被使用？
A: 新版本PaddleOCR会在初始化时自动检测GPU。如果有可用的GPU，会自动使用。

### Q: 处理速度是否有变化？
A: 新版本的性能应该相同或更好，GPU检测更智能。

---

## 下一步计划

### v1.2.0 (计划中)
- 支持更多发票格式
- 添加发票验证功能
- 优化字段提取准确率
- 支持自定义字段提取规则

### 反馈和建议
如果您在使用过程中遇到问题或有改进建议，请：
1. 查看日志文件 `invoice_ocr.log`
2. 运行 `python simple_test.py` 进行诊断
3. 提供详细的错误信息和使用环境
