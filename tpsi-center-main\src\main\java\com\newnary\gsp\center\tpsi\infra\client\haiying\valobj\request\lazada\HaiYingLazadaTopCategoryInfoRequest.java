package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.lazada;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingLazadaTopCategoryInfoRequest {

    /**
     * 站点
     */
    @NotNull(message = "站点不能为空")
    private String station;

    /**
     * 类目名
     * (存在特殊字符,转码UTF-8)
     */
    private String cname;

}
