package com.newnary.gsp.center.tpsi.infra.client.haiying;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.common.utils.httpmethod.ApiBaseResult;
import com.newnary.common.utils.httpmethod.HttpMethodUtil;
import com.newnary.gsp.center.tpsi.infra.client.haiying.params.HaiYingDataParams;
import com.newnary.gsp.center.tpsi.infra.client.haiying.utils.HaiYingDataUtil;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.HaiYingDataApiBaseResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.File;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Slf4j
public class HaiYingDataApiClient {

    private String u_name;

    private String skey;

    private String apiUrl;

    public HaiYingDataApiClient(String haiyingDataParams) {
        HaiYingDataParams params = JSON.parseObject(haiyingDataParams, HaiYingDataParams.class);
        this.u_name = params.getU_name();
        this.skey = params.getSkey();
        this.apiUrl = params.getApiUrl();
    }

    /**
     * 海鹰接口调用请求
     * @param service 请求接口服务路径
     * @param paramJson 请求参数json字符串(注意顺序)
     * @return
     */
    public HaiYingDataApiBaseResult<String> sendRequest(String service, JSONObject paramJson) {
        String param = getParam(paramJson);
        String url = apiUrl.concat(File.separator).concat(service).concat("?").concat(param);
        ApiBaseResult apiBaseResult = null;
        try {
            System.out.println(url);
            apiBaseResult = HttpMethodUtil.syncPostMethod(url, 3, null, null, null, null, null);
            HaiYingDataApiBaseResult<String> haiyingDataApiBaseResult = buildHaiYingDataBaseResult(apiBaseResult.getRet());
            log.info("[HaiYingData] 请求结束, service={}, code={}, resultSize={}, totalSize={}, message={}, dataParas={}", service, haiyingDataApiBaseResult.getCode(), haiyingDataApiBaseResult.getSize(), haiyingDataApiBaseResult.getTotalSize(), haiyingDataApiBaseResult.getMessage(), paramJson.toJSONString());
            return haiyingDataApiBaseResult;
        } catch (Exception e) {
            e.printStackTrace();
            HaiYingDataApiBaseResult<String> ret = new HaiYingDataApiBaseResult<>();
            ret.setCode(0);
            ret.setMessage("异常:".concat(e.getMessage()));
            return null;
        }
    }

    /**
     * 海鹰接口调用请求
     * @param service 请求接口服务路径
     * @param paramJson 请求参数json字符串(注意顺序)
     * @return
     */
    public HaiYingDataApiBaseResult<String> sendLazadaRequest(String service,String contentType , JSONObject paramJson) {
        String url = apiUrl.concat(File.separator).concat(service);
        Long ts = System.currentTimeMillis();
        String time = ts.toString();
        String sign = HaiYingDataUtil.getMd5(u_name.concat(skey).concat(time));
        paramJson.put("u_name",(u_name == null ? "" : HaiYingDataUtil.getURLEncoderString(u_name)));
        paramJson.put("sign", HaiYingDataUtil.getURLEncoderString(sign));
        paramJson.put("time", ts);

        ApiBaseResult apiBaseResult = null;
        try {
            System.out.println(url);
            apiBaseResult = HttpMethodUtil.syncPostMethod(url, 3, null, contentType, null, null, paramJson);
            HaiYingDataApiBaseResult<String> haiyingDataApiBaseResult = buildHaiYingDataBaseResult(apiBaseResult.getRet());
            log.info("[HaiYingData] 请求结束, service={}, code={}, resultSize={}, totalSize={}, message={}, dataParas={}", service, haiyingDataApiBaseResult.getCode(), haiyingDataApiBaseResult.getSize(), haiyingDataApiBaseResult.getTotalSize(), haiyingDataApiBaseResult.getMessage(), paramJson.toJSONString());
            return haiyingDataApiBaseResult;
        } catch (Exception e) {
            e.printStackTrace();
            HaiYingDataApiBaseResult<String> ret = new HaiYingDataApiBaseResult<>();
            ret.setCode(0);
            ret.setMessage("异常:".concat(e.getMessage()));
            return null;
        }
    }

    private String getParam(JSONObject paramJson) {
        Long ts = System.currentTimeMillis();
        String time = ts.toString();
        String sign = HaiYingDataUtil.getMd5(u_name.concat(skey).concat(time));
        StringBuilder stringBuilder = new StringBuilder("");
        stringBuilder.append("u_name=").append((u_name == null ? "" : HaiYingDataUtil.getURLEncoderString(u_name)))
                .append("&time=").append((time == null ? "" : HaiYingDataUtil.getURLEncoderString(time)))
                .append("&sign=").append((sign == null ? "" : HaiYingDataUtil.getURLEncoderString(sign)));
        stringBuilder = toParamUrl(paramJson, stringBuilder);
        return stringBuilder.toString();
    }

    private StringBuilder toParamUrl(JSONObject paramJson, StringBuilder stringBuilder) {
        for (String key : paramJson.keySet()) {
            Object obj = paramJson.get(key);
            stringBuilder.append("&".concat(key).concat("=")).append((obj == null ? "" : HaiYingDataUtil.getURLEncoderString(String.valueOf(obj))));
        }
        return stringBuilder;
    }

    private HaiYingDataApiBaseResult<String> buildHaiYingDataBaseResult(String resultStr) {
        HaiYingDataApiBaseResult<String> apiBaseResult = new HaiYingDataApiBaseResult<>();

        if (StringUtils.isEmpty(resultStr)) {
            apiBaseResult.setCode(0);
            apiBaseResult.setMessage("海鹰数据接口调用失败");
            return apiBaseResult;
        }

        JSONObject resultObj = JSONObject.parseObject(resultStr);

        apiBaseResult.setCode(resultObj.getInteger("code"));
        apiBaseResult.setStatus(resultObj.getString("status"));
        apiBaseResult.setSize(resultObj.getInteger("size"));
        apiBaseResult.setNoMoreData(resultObj.getBoolean("no_more_data"));
        apiBaseResult.setTotalSize(resultObj.getInteger("total_size"));
        apiBaseResult.setResult(resultObj.getString("result"));

        switch (apiBaseResult.getCode()) {
            case 200:
                apiBaseResult.setMessage("成功");
                break;
            case 401:
                apiBaseResult.setMessage("参数不正确");
                break;
            case 402:
                apiBaseResult.setMessage("用户不存在/没有访问权限");
                break;
            case 403:
                apiBaseResult.setMessage("签名验证失败");
                break;
            case 404:
                apiBaseResult.setMessage("商品id不正确");
                break;
            case 405:
                apiBaseResult.setMessage("商品尚未录入");
                break;
            case 406:
                apiBaseResult.setMessage("每秒请求次数超过限制");
                break;
            case 407:
                apiBaseResult.setMessage("建议使用单线程获取商品数据");
                break;
            case 500:
                apiBaseResult.setMessage("请联系海鹰数据");
                break;
            default:
                break;
        }

        return apiBaseResult;
    }

}
