package com.newnary.gsp.center.tpsi.app.listener.mq;

import com.newnary.gsp.center.tpsi.app.service.eccang.StockoutPoMgmtApp;
import com.newnary.gsp.center.tpsi.infra.mq.consumer.StockoutOrderMQConsumer;
import com.newnary.messagebody.gsp.logistics.GSPStockoutOrderTopic;
import com.newnary.messagebody.gsp.logistics.mo.StockoutOrderStateChangeMO;
import com.newnary.mq.starter.consumer.AbstractMQProcessor;
import com.newnary.mq.starter.consumer.MQMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class StockoutOrderFinishSyncGSPPurchaseOrderProcessor extends AbstractMQProcessor<StockoutOrderStateChangeMO> {

    @Resource
    private StockoutPoMgmtApp stockoutPoMgmtAp;

    @Override
    public boolean doProcess(MQMessage<StockoutOrderStateChangeMO> mqMessage) {
        StockoutOrderStateChangeMO mo = mqMessage.getContent();
        log.info("出库单完成状态同步采购单完成消息消费开始：stockountOrderId: {}", mo.getStockoutOrderId());
        try {
            stockoutPoMgmtAp.checkStockoutFinishSyncPurchaseOrder(mo.getStockoutOrderId());
        }catch (Exception e){
            log.info("出库单完成状态同步采购单完成消息消费异常：stockountOrderId: {}", mo.getStockoutOrderId());
        }

        return false;
    }


    @Override
    public Class<?> consumerClz() {
        return StockoutOrderMQConsumer.class;
    }

    @Override
    public String tag() {
        return GSPStockoutOrderTopic.Tag.COMPLETED;
    }

}
