package com.newnary.gsp.center.tpsi.ctrl.alibaba;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.gsp.center.tpsi.api.alibaba.Open1688OceanProductSearchApi;
import com.newnary.gsp.center.tpsi.api.externalproduct.request.ExternalProductImageSearchCommand;
import com.newnary.gsp.center.tpsi.api.externalproduct.response.ExternalProductSearchInfo;
import com.newnary.gsp.center.tpsi.service.alibaba.OceanProductSearchService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since Created on 2022-05-12
 **/
@RestController
public class Open1688OceanProductSearchApiImpl implements Open1688OceanProductSearchApi {

    @Resource
    private OceanProductSearchService oceanProductSearchService;

    @Override
    public CommonResponse<ExternalProductSearchInfo> imageSearch(ExternalProductImageSearchCommand command) {
        return CommonResponse.success(
                oceanProductSearchService.imageSearch(command)
        );
    }

}
