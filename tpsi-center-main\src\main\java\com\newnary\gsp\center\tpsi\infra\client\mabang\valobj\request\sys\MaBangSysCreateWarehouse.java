package com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.sys;

import lombok.Data;

@Data
public class MaBangSysCreateWarehouse {

    /**
     * 仓库名称
     */
    private String name;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 国家二字码
     */
    private String countryCode;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * 地址
     */
    private String address;

    /**
     * 邮编
     */
    private String zipcode;

    /**
     * 电话
     */
    private String telephone;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 仓库编码
     */
    private String code;

    /**
     * 财务编码
     */
    private String finance_code;

    /**
     * 1、自建本地仓，2、自建第三方海外仓；为空表示本地自建仓
     */
    private String attributetype;

}
