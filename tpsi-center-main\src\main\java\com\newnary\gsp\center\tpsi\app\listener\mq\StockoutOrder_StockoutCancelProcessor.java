package com.newnary.gsp.center.tpsi.app.listener.mq;

import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.logistics.api.delivery.request.StockoutOrderConfirmCancelCommand;
import com.newnary.gsp.center.logistics.api.delivery.request.StockoutOrderRejectCancelCommand;
import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderDetailInfo;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.model.DeliveryOrderEcCangApiAssociation;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.mq.consumer.StockoutOrderCancelConsumer;
import com.newnary.gsp.center.tpsi.infra.repository.IDeliveryOrderEcCangApiAssociationRepository;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.infra.rpc.DeliveryOrderRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.StockoutOrderRpc;
import com.newnary.gsp.center.tpsi.service.eccang.IEccangERPApiSve;
import com.newnary.gsp.center.tpsi.service.eccang.IEccangWMSApiSve;
import com.newnary.gsp.center.tpsi.service.mabang.IMaBangOrderApiSve;
import com.newnary.messagebody.gsp.logistics.GSPStockoutOrderCancelTopic;
import com.newnary.messagebody.gsp.logistics.mo.StockoutOrderCancelMO;
import com.newnary.mq.starter.consumer.AbstractMQProcessor;
import com.newnary.mq.starter.consumer.MQMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;

@Slf4j
@Component
public class StockoutOrder_StockoutCancelProcessor extends AbstractMQProcessor<StockoutOrderCancelMO> {

    @Resource
    private DeliveryOrderRpc deliveryOrderRpc;
    @Resource
    private StockoutOrderRpc stockoutOrderRpc;

    @Resource
    private IDeliveryOrderEcCangApiAssociationRepository deliveryOrderEcCangApiAssociationRepository;
    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Resource
    private IMaBangOrderApiSve maBangOrderApiSve;
    @Resource
    private IEccangERPApiSve eccangERPApiSve;
    @Resource
    private IEccangWMSApiSve eccangWMSApiSve;

    @Override
    public boolean doProcess(MQMessage<StockoutOrderCancelMO> message) {
        log.info("修改第三方订单为已作废");
        StockoutOrderCancelMO mo = message.getContent();


        for (final String referenceId : mo.getReferenceId()) {
            //查询发货单
            final DeliveryOrderDetailInfo deliveryOrderDetailInfo = deliveryOrderRpc.getDetailInfo(referenceId);
            //根据订单类型判断在哪个第三方系统执行取消操作，TODO 后续还需改进
            if (!deliveryOrderDetailInfo.isLocalShip) {
                //代采-易仓逻辑
                Optional<DeliveryOrderEcCangApiAssociation> deliveryOrderEcCangApiAssociation = deliveryOrderEcCangApiAssociationRepository.loadByStockoutOrderId(deliveryOrderDetailInfo.getStockoutOrderId());
                if (deliveryOrderEcCangApiAssociation.isPresent()) {
                    DeliveryOrderEcCangApiAssociation ecCangApiAssociation = deliveryOrderEcCangApiAssociation.get();

                    //判断是否还能取消
                    if (ecCangApiAssociation.getErpOrderStatus() != 0 && ecCangApiAssociation.getErpOrderStatus() != 4) {
                        if (cancelErpOrder(ecCangApiAssociation, ecCangApiAssociation.getErpTpsId())) {
                            //erp订单取消成功
                            log.info("易仓erp订单取消成功");
                            StockoutOrderConfirmCancelCommand stockoutOrderConfirmCancelCommand = new StockoutOrderConfirmCancelCommand();
                            stockoutOrderConfirmCancelCommand.setStockoutOrderId(deliveryOrderDetailInfo.getStockoutOrderId());
                            stockoutOrderConfirmCancelCommand.setCancelType(mo.getCancelType());
                            stockoutOrderRpc.confirmCancel(stockoutOrderConfirmCancelCommand);
                        } else {
                            log.error("易仓erp订单取消失败");
                            StockoutOrderRejectCancelCommand stockoutOrderRejectCancelCommand = new StockoutOrderRejectCancelCommand();
                            stockoutOrderRejectCancelCommand.setStockoutOrderId(deliveryOrderDetailInfo.getStockoutOrderId());
                            stockoutOrderRpc.rejectCancel(stockoutOrderRejectCancelCommand);
                            log.info("调用驳回取消接口");
                        }
                    } else {
                        log.warn("易仓erp订单已取消或者已发货");
                        StockoutOrderRejectCancelCommand stockoutOrderRejectCancelCommand = new StockoutOrderRejectCancelCommand();
                        stockoutOrderRejectCancelCommand.setStockoutOrderId(deliveryOrderDetailInfo.getStockoutOrderId());
                        stockoutOrderRpc.rejectCancel(stockoutOrderRejectCancelCommand);
                        log.info("调用驳回取消接口");
                    }
                }
            } else {
                //分销-马帮逻辑
                String ret = maBangOrderApiSve.invalidOrder(mo.getThirdOrderId(), deliveryOrderDetailInfo.getStockoutOrderId());
                if (ret.equals("SUCCESS")) {
                    log.info("马帮处理取消订单成功");
                    StockoutOrderConfirmCancelCommand stockoutOrderConfirmCancelCommand = new StockoutOrderConfirmCancelCommand();
                    stockoutOrderConfirmCancelCommand.setStockoutOrderId(deliveryOrderDetailInfo.getStockoutOrderId());
                    stockoutOrderConfirmCancelCommand.setCancelType(mo.getCancelType());
                    stockoutOrderRpc.confirmCancel(stockoutOrderConfirmCancelCommand);
                } else {
                    log.info("马帮处理取消订单出错，出错原因为{}", ret);
                    StockoutOrderRejectCancelCommand stockoutOrderRejectCancelCommand = new StockoutOrderRejectCancelCommand();
                    stockoutOrderRejectCancelCommand.setStockoutOrderId(deliveryOrderDetailInfo.getStockoutOrderId());
                    stockoutOrderRpc.rejectCancel(stockoutOrderRejectCancelCommand);
                    log.info("调用驳回取消接口");
                }
            }
        }

        return true;
    }

    private boolean cancelErpOrder(DeliveryOrderEcCangApiAssociation association, String erpTpsId) {
        Set<String> erpPurchaseOrderCodeSet = new HashSet<>();
        association.getItems().forEach(item -> {
            erpPurchaseOrderCodeSet.add(item.getErpPurchaseOrderCode());
        });
        ThirdPartySystem thirdPartySystem = loadSystem(erpTpsId);

        EcCangApiBaseResult<String> cancelPurchaseOrderApiBaseResult = eccangERPApiSve.cancelPurchaseOrder(thirdPartySystem, erpPurchaseOrderCodeSet);
        if (cancelPurchaseOrderApiBaseResult.getCode().equals("200") && cancelPurchaseOrderApiBaseResult.getMessage().equals("Success")) {
            EcCangApiBaseResult<String> cancelOrderApiBaseResult = eccangERPApiSve.cancelOrder(thirdPartySystem, association.getErpOrderSaleOrderCode());
            if (cancelOrderApiBaseResult.getCode().equals("200") && cancelOrderApiBaseResult.getMessage().equals("Success")) {
                log.info("{}取消易仓采购单及订单成功", association.getDeliveryOrderId());
                return true;
            } else {
                log.error("{}取消易仓采购单失败{}", association.getDeliveryOrderId(), cancelOrderApiBaseResult.getError());
                return false;
            }
        } else {
            log.error("{}取消易仓采购单失败{}", association.getDeliveryOrderId(), cancelPurchaseOrderApiBaseResult.getError());
            return false;
        }
    }

    private ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }

    @Override
    public Class<?> consumerClz() {
        return StockoutOrderCancelConsumer.class;
    }

    @Override
    public String tag() {
        return GSPStockoutOrderCancelTopic.Tag.STOCKOUT_CANCELING;
    }
}
