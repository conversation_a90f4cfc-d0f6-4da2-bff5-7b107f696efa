package com.newnary.gsp.center.tpsi.infra.repository;

import com.newnary.dao.base.helper.DaoHelper;
import com.newnary.dao.base.po.BaseQuery;
import com.newnary.gsp.center.tpsi.infra.model.ApiRequestParams;
import com.newnary.gsp.center.tpsi.infra.repository.db.dao.ApiRequestParamsDao;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.ApiRequestParamsPO;
import com.newnary.gsp.center.tpsi.infra.translator.PO2ModelCreatorTranslator;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Component
public class ApiRequestParamsRepository implements IApiRequestParamsRepository {

    @Resource
    private ApiRequestParamsDao apiRequestParamsDao;

    @Override
    public Optional<ApiRequestParams> loadBySystemBizIdAndType(String systemBizId, String type) {
        BaseQuery<ApiRequestParamsPO> query = new BaseQuery<>(new ApiRequestParamsPO());
        query.getData().setSystemBizId(systemBizId);
        query.getData().setApiRequestParamsType(type);
        return loadByQuery(query);
    }

    private Optional<ApiRequestParams> loadByQuery(BaseQuery<ApiRequestParamsPO> query) {
        List<ApiRequestParamsPO> pos = apiRequestParamsDao.query(query);
        if (CollectionUtils.isNotEmpty(pos)) {
            return pos.stream()
                    .map(po -> ApiRequestParams.loadWith(PO2ModelCreatorTranslator.transApiRequestParamsCreator(po)))
                    .findAny();
        }
        return Optional.empty();
    }

    public static void main(String[] args) {
        DaoHelper.genXMLWithFeature(
                "E:/SWWorkspace/sw-center/tpsi-center/tpsi-center-main/src/main/java/com/newnary/gsp/center/tpsi/infra/repository/db/dao/ApiRequestParamsDao.xml",
                ApiRequestParamsDao.class,
                ApiRequestParamsPO.class,
                "api_request_params",
                false);
    }
}
