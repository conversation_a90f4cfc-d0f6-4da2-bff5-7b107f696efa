package com.newnary.gsp.center.tpsi.infra.mapper;


import com.newnary.gsp.center.tpsi.api.haiying.request.lazada.*;
import com.newnary.gsp.center.tpsi.api.haiying.response.lazada.*;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.lazada.*;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.lazada.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Author: jack
 * @CreateTime: 2022-9-5
 */
@Mapper
public interface HaiYingLazadaDataMapper extends HaiYingDataMapper {

    HaiYingLazadaDataMapper INSTANCE = Mappers.getMapper(HaiYingLazadaDataMapper.class);

    @Mapping(target = "station", source = "station", qualifiedByName = "haiyingStation2Str")
    HaiYingLazadaCategoryTreeRequest transLazadaCategoryTreeRequest(HaiYingLazadaCategoryTreeCommand command);

    @Mapping(target = "station", source = "station", qualifiedByName = "haiyingStation2Str")
    HaiYingLazadaTopCategoryInfoRequest transLazadaTopCategoryRequest(HaiYingLazadaTopCategoryInfoCommand command);

    @Mapping(target = "station", source = "station", qualifiedByName = "haiyingStation2Str")
    @Mapping(target = "current_page", source = "pageCondition", qualifiedByName = "pageCondition2CurrentPage")
    @Mapping(target = "page_size", source = "pageCondition", qualifiedByName = "pageCondition2PageSize")
    HaiYingLazadaSubCategoryInfoRequest transLazadaSubCategoryRequest(HaiYingLazadaSubCategoryInfoCommand command);

    @Mapping(target = "station", source = "station", qualifiedByName = "haiyingStation2Str")
    @Mapping(target = "current_page", source = "pageCondition", qualifiedByName = "pageCondition2CurrentPage")
    @Mapping(target = "page_size", source = "pageCondition", qualifiedByName = "pageCondition2PageSize")
    @Mapping(target = "order_by", source = "order_by", qualifiedByName = "haiYingLazadaProductListOrderBy2description")
    @Mapping(target = "order_by_type", source = "order_by_type", qualifiedByName = "haiYingOrderByType2description")
    @Mapping(target = "last_modi_time_start", source = "last_modi_time_start", qualifiedByName = "simpleDateLong2Str")
    @Mapping(target = "last_modi_time_end", source = "last_modi_time_end", qualifiedByName = "simpleDateLong2Str")
    @Mapping(target = "cname_path", source = "cname_paths")
    HaiYingLazadaProductListRequest transLazadaProductListRequest(HaiYingLazadaProductListCommand command);

    @Mapping(target = "stations", source = "stations", qualifiedByName = "haiyingStations2Strs")
    HaiYingLazadaProductDetailInfoRequest transLazadaProductDetailRequest(HaiYingLazadaProductDetailInfoCommand command);

    @Mapping(target = "stations", source = "stations", qualifiedByName = "haiyingStations2Strs")
    HaiYingLazadaProductExtInfoRequest transLazadaProductExtRequest(HaiYingLazadaProductExtInfoCommand command);

    @Mapping(target = "stations", source = "stations", qualifiedByName = "haiyingStations2Strs")
    HaiYingLazadaProductHistoryInfoRequest transLazadaProductHistoryRequest(HaiYingLazadaProductHistoryInfoCommand command);

    @Mapping(target = "station", source = "station", qualifiedByName = "haiyingStation2Str")
    @Mapping(target = "item_ids", source = "item_ids", qualifiedByName = "list2StrWithComma")
    @Mapping(target = "stat_date_start", source = "stat_date_start", qualifiedByName = "simpleDateLong2Str")
    @Mapping(target = "stat_date_end", source = "stat_date_end", qualifiedByName = "simpleDateLong2Str")
    HaiYingLazadaProductHistoryDailyReviewRequest transLazadaProductHistoryDailyReviewRequest(HaiYingLazadaProductHistoryDailyReviewCommand command);


    @Mapping(target = "is_leaf", source = "is_leaf", qualifiedByName = "str2Boolean")
    HaiYingLazadaCategoryTreeDTO transLazadaCategoryTreeDTO(HaiYingLazadaCategoryTreeResponse response);

    @Mapping(target = "is_leaf", source = "is_leaf", qualifiedByName = "str2Boolean")
    @Mapping(target = "stat_time", source = "stat_time", qualifiedByName = "simpleTimeStr2Long")
    HaiYingLazadaCategoryInfoDTO transLazadaCategoryInfoDTO(HaiYingLazadaCategoryInfoResponse response);

    @Mapping(target = "not_exist", source = "not_exist", qualifiedByName = "str2Boolean")
    @Mapping(target = "last_modi_time", source = "last_modi_time", qualifiedByName = "simpleTimeStr2Long")
    @Mapping(target = "insert_time", source = "insert_time", qualifiedByName = "simpleTimeStr2Long")
    @Mapping(target = "category_path", source = "category_path", qualifiedByName = "str2ListWithSemicolon")
    @Mapping(target = "category_structure", source = "category_structure", qualifiedByName = "str2ListWithBr")
    HaiYingLazadaProductListDTO transLazadaProductListDTO(HaiYingLazadaProductListResponse response);

    @Mapping(target = "not_exist", source = "not_exist", qualifiedByName = "str2Boolean")
    @Mapping(target = "last_modi_time", source = "last_modi_time", qualifiedByName = "millTimeStr2Long")
    @Mapping(target = "insert_time", source = "insert_time", qualifiedByName = "millTimeStr2Long")
    HaiYingLazadaProductDetailInfoDTO transLazadaProductDetailDTO(HaiYingLazadaProductDetailInfoResponse response);

    @Mapping(target = "last_modi_time", source = "last_modi_time", qualifiedByName = "simpleTimeStr2Long")
    @Mapping(target = "properties_list", source = "properties_list", qualifiedByName = "str2ListWithSemicolon")
    HaiYingLazadaProductExtInfoDTO transLazadaProductExtDTO(HaiYingLazadaProductExtInfoResponse response);

    HaiYingLazadaProductHistoryInfoDTO transLazadaProductHistoryDTO(HaiYingLazadaProductHistoryInfoResponse response);

    HaiYingLazadaProductHistoryDailyReviewDTO transLazadaProductHistoryDailyReviewDTO(HaiYingLazadaProductHistoryDailyReviewResponse response);

}
