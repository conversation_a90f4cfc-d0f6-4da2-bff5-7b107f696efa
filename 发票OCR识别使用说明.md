# 发票OCR识别工具使用说明

## 功能介绍

本工具使用PaddleOCR技术，能够自动识别PDF和图片格式的发票，提取关键字段信息并保存到Excel文件中。

### 支持的功能
- 支持增值税专用发票和普通发票识别
- 支持PDF和图片格式（PNG, JPG, JPEG, BMP, TIFF）
- 自动提取发票关键字段
- 批量处理文件夹中的所有发票
- 结果导出到Excel文件

### 提取的字段信息
- 文件名和页码
- 发票类型（专票/普票）
- 发票代码
- 发票号码
- 开票日期
- 购买方名称和税号
- 销售方名称和税号
- 金额信息（不含税金额、税额、价税合计）
- 处理时间

## 安装步骤

### 1. 安装Python环境
确保您的系统已安装Python 3.7或更高版本。

### 2. 安装依赖包

#### 方法一：自动安装（推荐）
```bash
python install_dependencies.py
```

#### 方法二：手动安装
```bash
pip install -r requirements.txt
```

### 3. 测试安装
```bash
# 快速测试
python simple_test.py

# 完整测试
python test_installation.py
```

### 4. 首次运行PaddleOCR
首次运行时，PaddleOCR会自动下载模型文件，请确保网络连接正常。

## 使用方法

### 方法一：命令行使用

```bash
# 基本用法
python invoice_ocr_processor.py "发票文件夹路径"

# 指定输出文件名
python invoice_ocr_processor.py "发票文件夹路径" -o "输出文件名.xlsx"

# 使用GPU加速（需要安装GPU版本的PaddlePaddle）
python invoice_ocr_processor.py "发票文件夹路径" --gpu
```

### 方法二：Python脚本调用

```python
from invoice_ocr_processor import InvoiceOCRProcessor

# 创建处理器
processor = InvoiceOCRProcessor(use_gpu=False)

# 处理文件夹
processor.process_folder("发票文件夹路径", "输出文件.xlsx")
```

### 方法三：运行示例脚本

1. 修改 `example_usage.py` 中的文件夹路径
2. 运行示例脚本：
```bash
python example_usage.py
```

## 文件夹结构示例

```
项目文件夹/
├── invoice_ocr_processor.py    # 主处理脚本
├── example_usage.py           # 使用示例
├── requirements.txt           # 依赖包列表
├── 发票OCR识别使用说明.md      # 本说明文档
└── invoices/                  # 发票文件夹（需要创建）
    ├── 发票1.pdf
    ├── 发票2.png
    ├── 发票3.jpg
    └── ...
```

## 输出结果

处理完成后会生成Excel文件，包含以下列：

| 列名 | 说明 |
|------|------|
| file_name | 文件名 |
| page_number | 页码 |
| invoice_type | 发票类型 |
| invoice_code | 发票代码 |
| invoice_number | 发票号码 |
| invoice_date | 开票日期 |
| buyer_name | 购买方名称 |
| buyer_tax_number | 购买方税号 |
| seller_name | 销售方名称 |
| seller_tax_number | 销售方税号 |
| amount_without_tax | 不含税金额 |
| tax_amount | 税额 |
| total_amount | 价税合计 |
| processing_time | 处理时间 |

## 注意事项

1. **文件格式**：支持PDF、PNG、JPG、JPEG、BMP、TIFF格式
2. **图片质量**：建议使用清晰的扫描件或照片，模糊的图片可能影响识别准确率
3. **处理时间**：处理时间取决于文件数量和大小，大量文件需要较长时间
4. **内存使用**：处理大量文件时可能占用较多内存
5. **网络连接**：首次运行需要下载OCR模型，请确保网络畅通

## 性能优化建议

1. **使用GPU加速**：如果有NVIDIA GPU，安装GPU版本的PaddlePaddle可以显著提升处理速度
2. **批量处理**：一次处理多个文件比单个处理更高效
3. **图片预处理**：对于质量较差的图片，可以先进行预处理（如调整对比度、去噪等）

## 故障排除

### 安装问题

#### Q: 出现 "DeprecationWarning: use_angle_cls has been deprecated" 警告
A: 这是正常的，新版本已经修复了这个问题。如果仍有问题，请更新到最新版本。

#### Q: PaddleOCR初始化失败
A:
1. 检查网络连接（首次运行需要下载模型）
2. 重新安装：`pip install --upgrade paddleocr paddlepaddle`
3. 运行测试脚本：`python simple_test.py`

#### Q: 依赖包安装失败
A:
1. 使用自动安装脚本：`python install_dependencies.py`
2. 更新pip：`pip install --upgrade pip`
3. 使用国内镜像：`pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt`

### 使用问题

#### Q: 识别准确率不高怎么办？
A:
- 确保发票图片清晰，避免模糊、倾斜
- 检查图片分辨率，建议不低于300DPI
- 对于扫描件，确保扫描质量良好

#### Q: 处理速度很慢怎么办？
A:
- 考虑使用GPU加速：`--gpu`
- 减少单次处理的文件数量
- 确保系统有足够的内存

#### Q: 某些字段识别不到怎么办？
A:
- 检查发票格式是否标准
- 可以修改正则表达式模式来适配特殊格式
- 查看日志文件了解具体错误信息

#### Q: 程序运行时出现错误
A:
1. 查看详细错误信息和日志文件 `invoice_ocr.log`
2. 运行简单测试：`python simple_test.py`
3. 确认文件格式是否支持
4. 检查文件是否损坏

## 技术支持

如果遇到问题，请：
1. 查看生成的日志文件 `invoice_ocr.log`
2. 确认发票格式是否标准
3. 检查依赖包是否正确安装

## 更新日志

- v1.0.0: 初始版本，支持基本的发票OCR识别功能
