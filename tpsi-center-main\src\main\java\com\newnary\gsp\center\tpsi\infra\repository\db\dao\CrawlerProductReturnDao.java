package com.newnary.gsp.center.tpsi.infra.repository.db.dao;

import com.newnary.gsp.center.tpsi.infra.model.CrawlerProduct;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.CrawlerProductPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CrawlerProductReturnDao {

    void updateByProductId(CrawlerProductPO po);

    void fillCrawlerProduct(CrawlerProduct source);

    CrawlerProductPO loadCrawlerDetailReturn(@Param("productId") String productId);

    List<CrawlerProductPO> queryByCategoryNameList(@Param("categoryList") List<String> categoryList,
                                                   @Param("size") Integer limit,
                                                   @Param("crawlerReturnState") Integer crawlerReturnState,
                                                   @Param("state") Integer state,
                                                   @Param("flushState") Integer flushState);
}
