package com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Data
public class QueryJXHYProductDetailResponse {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误码
     */
    private String code;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 结果值
     */
    private List<Result> result;

    @Getter
    @Setter
    public static class Result{

        /**
         * 批发团实例code
         */
        private String instanceCode;

        /**
         * 旺旺账号
         */
        private String wangwangAccount;

        /**
         * 商品详情
         */
        private ProductInfo productInfo;

        @Getter
        @Setter
        public static class ProductInfo{

            /**
             * 审核时间
             */
            private String approvedTime;

            /**
             * 商品属性和属性值
             */
            private List<ProductAttribute> attributes;

            @Getter
            @Setter
            public static class ProductAttribute{

                /**
                 * 属性ID
                 */
                private Long attributeID;

                /**
                 * 属性名称
                 */
                private String attributeName;

                /**
                 * 是否为自定义属性，国际站无需关注
                 */
                private Boolean isCustom;

                /**
                 * 属性值
                 */
                private String value;

                /**
                 * 属性值ID
                 */
                private Long valueID;
            }

            /**
             * 产品业务的支持信息,support为false说明不支持.
             */
            private List<ProductBizGroupInfo> bizGroupInfos;

            @Getter
            @Setter
            public static class ProductBizGroupInfo{

                /**
                 * 垂直市场标记
                 */
                private String code;

                /**
                 * 垂直市场名字，如微供市场、货品市场
                 */
                private String description;

                /**
                 * 是否支持
                 */
                private Boolean support;
            }

            /**
             * 业务类型。1：商品，2：加工，3：代理，4：合作，5：商务服务。国际站按默认商品。
             */
            private Integer bizType;

            /**
             * 成交量
             */
            private Long bookedCount;

            /**
             * 商品货号，产品属性中的货号
             */
            private String cargoNum;

            /**
             * 类目ID，标识商品所属类目
             */
            private Long categoryID;

            /**
             *  类目名
             */
            private String categoryName;

            /**
             * 创建时间
             */
            private String createTime;

            /**
             * 商品详情描述，可包含图片中心的图片URL
             */
            private String description;

            /**
             * 详情视频
             */
            private String detailVedio;

            /**
             * 过期时间
             */
            private String expireTime;

            /**
             * 商品扩展信息
             */
            private List<ProductExtendInfo> extendInfos;

            @Getter
            @Setter
            public static class ProductExtendInfo{

                /**
                 * 扩展结构的key
                 */
                private String key;

                /**
                 * 扩展结构的value
                 */
                private String value;
            }

            /**
             * 分组ID，确定商品所属分组。1688可传入多个分组ID，国际站同一个商品只能属于一个分组，因此默认只取第一个
             */
            private List<Long> groupID;

            /**
             * 商品主图
             */
            private Image image;

            @Getter
            @Setter
            public static class Image{

                /**
                 * 主图列表，使用相对路径，需要增加域名：https://cbu01.alicdn.com/
                 */
                private List<String> images;

                /**
                 * 是否打水印，是(true)或否(false)，1688无需关注此字段，1688的水印信息在上传图片时处理
                 */
                private Boolean isWatermark;

                /**
                 * 水印是否有边框，有边框(true)或者无边框(false)，1688无需关注此字段，1688的水印信息在上传图片时处理
                 */
                private Boolean isWatermarkFrame;

                /**
                 * 水印位置，在中间(center)或者在底部(bottom)，1688无需关注此字段，1688的水印信息在上传图片时处理
                 */
                private String watermarkPosition;
            }

            /**
             * 商品算法智能改写信息，包含算法优化后的商品标题和图片信息，未改写的则直接返回原标题和原图片
             */
            private List<ProductIntelligentInfo> intelligentInfo;

            @Getter
            @Setter
            public static class ProductIntelligentInfo{

                /**
                 * 算法优化后的详情图片
                 */
                private List<String> descriptionImages;

                /**
                 * 算法优化后的商品图片
                 */
                private List<String> images;

                /**
                 * 算法优化后的规格图片
                 */
                private List<SkuIntelligentInfo> skuImages;

                @Getter
                @Setter
                public static class SkuIntelligentInfo{

                    /**
                     * 算法处理后的图片地址，未处理则返回原图片地址
                     */
                    private String imageUrl;

                    /**
                     * skuId
                     */
                    private Long skuId;
                }

                /**
                 * 算法优化后的商品标题
                 */
                private String title;
            }

            /**
             * 语种，参见FAQ 语种枚举值，1688网站默认传入CHINESE
             */
            private String language;

            /**
             * 最后重发时间
             */
            private String lastRepostTime;

            /**
             * 最后操作时间
             */
            private String lastUpdateTime;

            /**
             * 主图视频播放地址
             */
            private String mainVedio;

            /**
             * 修改时间
             */
            private String modifyTime;

            /**
             * 信息有效期，按天计算，国际站无此信息
             */
            private Integer periodOfValidity;

            /**
             * 是否图片私密信息，国际站此字段无效
             */
            private Boolean pictureAuth;

            /**
             * 私密价格信息
             */
            private PrivateChannelInfo privateChannelInfo;

            @Getter
            @Setter
            public static class PrivateChannelInfo{

                /**
                 * 私密商品私密价格信息
                 */
                private List<OfferPrivatePriceInfo> offerPrivatePriceInfo;

                @Getter
                @Setter
                public static class OfferPrivatePriceInfo{

                    /**
                     * 具体会员等级价格（普通，高级，vip，至尊vip，初级）注意会员顺序
                     */
                    private String privatePriceInfo;

                    /**
                     * skuid，非规格报价为0
                     */
                    private Long skuId;
                }
            }

            /**
             * 加工定制信息
             */
            private ProcessingInfo processingInfo;

            @Getter
            @Setter
            public static class ProcessingInfo{

                /**
                 * 定制方式，可选值: processing、sku
                 */
                private List<String> customType;

                /**
                 * 定制商品样图
                 */
                private List<String> images;

                /**
                 * 价格区间的最高价
                 */
                private BigDecimal maxPrice;

                /**
                 * 最小起订量
                 */
                private Integer minOrderQuantity;

                /**
                 * 价格区间的最低价
                 */
                private BigDecimal minPrice;

                /**
                 * 出货周期
                 */
                private List<ReserveRange> reserveRanges;

                @Getter
                @Setter
                public static class ReserveRange{

                    /**
                     * 开始金额
                     */
                    private Long beginAmount;

                    /**
                     * 周期
                     */
                    private Integer date;

                    /**
                     * 结束金额
                     */
                    private Long endAmount;

                }

                /**
                 * 样品交期
                 */
                private Integer sampleDelivery;

                /**
                 * 打样信息
                 */
                private SampleInfo sampleInfo;

                @Getter
                @Setter
                public static class SampleInfo{

                    /**
                     * 是否支持打样
                     */
                    private Boolean isSupportSample;

                    /**
                     * 打样周期（单位：天）
                     */
                    private Integer period;

                    /**
                     * 打样价格（单位：元）
                     */
                    private BigDecimal price;
                }

                /**
                 * 关联现货商品的id列表
                 */
                private List<Long> stockOfferIds;

                /**
                 * 是否支持拿样
                 */
                private Boolean supportTakeSample;

                /**
                 * 品维度拿样价，在不是 sku 规格报价时有效
                 */
                private BigDecimal takeSamplePrice;
            }

            /**
             * 现货商品关联的定制商品id
             */
            private Long processingOfferId;

            /**
             * 商品ID
             */
            private Long productID;

            /**
             * 产品线
             */
            private String productLine;

            /**
             * 商品类型，在线批发商品(wholesale)或者询盘商品(sourcing)，1688网站缺省为wholesale
             */
            private String productType;

            /**
             * 质量星级(1-7)
             */
            private Integer qualityLevel;

            /**
             * 参考价格，返回价格区间，可能为空
             */
            private String referencePrice;

            /**
             * 订货数据
             */
            private ReserveInfo reserveInfo;

            @Getter
            @Setter
            public static class ReserveInfo{

                /**
                 * 最大订货量
                 */
                private Long maxQuantity;

                /**
                 * 最小订货量
                 */
                private Long minQuantity;

                /**
                 * 订货区间信息
                 */
                private List<ReserveRangeInfo> reserveRangeInfos;

                @Getter
                @Setter
                public static class ReserveRangeInfo{

                    /**
                     * 出货时间(天)
                     */
                    private Integer period;

                    /**
                     * 出货时间(天)
                     */
                    private Long quantity;
                }

                /**
                 * 是否支持订货
                 */
                private Boolean supportReserve;

            }

            /**
             * 商品销售信息
             */
            private ProductSaleInfo saleInfo;

            @Getter
            @Setter
            public static class ProductSaleInfo{

                /**
                 * 可售数量，国际站无需关注此字段
                 */
                private BigDecimal amountOnSale;

                /**
                 * 每批数量，默认为空或者非零值，该属性不为空时sellunit为必填
                 */
                private Integer batchNumber;

                /**
                 * 分销基准价。代销场景均使用该价格。有SKU商品查看skuInfo中的consignPrice
                 */
                private BigDecimal consignPrice;

                /**
                 * 发货时间限制（非买保发货周期），按开计算
                 */
                private Integer deliveryLimit;

                /**
                 * 库存扣减方式，1是下单减库存，2是付款减库存
                 */
                private String invReduceType;

                /**
                 * 最小起订量，范围是1-99999
                 */
                private Integer minOrderQuantity;

                /**
                 * 是否支持混批，国际站无需关注此字段
                 */
                private Boolean mixWholeSale;

                /**
                 *  是否价格私密信息，国际站无需关注此字段
                 */
                private Boolean priceAuth;

                /**
                 * 区间价格。按数量范围设定的区间价格
                 */
                private List<ProductPriceRange> priceRanges;

                @Getter
                @Setter
                public static class ProductPriceRange{

                    /**
                     * 商品价格
                     */
                    private BigDecimal price;

                    /**
                     * 商品起订数量
                     */
                    private Integer startQuantity;
                }

                /**
                 * 普通报价-FIXED_PRICE("0"),SKU规格报价-SKU_PRICE("1"),SKU区间报价（商品维度）-SKU_PRICE_RANGE_FOR_OFFER("2"),SKU区间报价（SKU维度）-SKU_PRICE_RANGE("3")，国际站无需关注
                 */
                private Integer quoteType;

                /**
                 * 精选货源一件包邮价
                 */
                private BigDecimal retailprice;

                /**
                 * 销售方式，按件卖(normal)或者按批卖(batch)，1688站点无需关注此字段
                 */
                private String saleType;

                /**
                 * 售卖单位，如果为批量售卖，代表售卖的单位，该属性不为空时batchNumber为必填，例如1"手"=12“件"的"手"，国际站无需关注
                 */
                private String sellunit;

                /**
                 * 是否支持网上交易。true：支持 false：不支持，国际站不需关注此字段
                 */
                private Boolean supportOnlineTrade;

                /**
                 * 税率相关信息，内容由用户自定，国际站无需关注
                 */
                private String tax;

                /**
                 * 计量单位
                 */
                private String unit;

                /**
                 * 精选货源批发价
                 */
                private BigDecimal pifaPrice;

            }

            /**
             * 是否支持七天无理由退货
             */
            private Boolean sevenDaysRefunds;

            /**
             * 商品物流信息
             */
            private ProductShippingInfo shippingInfo;

            @Getter
            @Setter
            public static class ProductShippingInfo{

                /**
                 * 厂货通渠道专享价是否包邮，要结合非包邮地址，如果收货地址在非包邮地区则商品为不包邮
                 */
                private Boolean channelPriceFreePostage;

                /**
                 * 备货期，单位是天，范围是1-60。1688无需处理此字段
                 */
                private Integer handlingTime;

                /**
                 * OFFER包装高度（cm）
                 */
                private BigDecimal offerHeight;

                /**
                 * OFFER包装长度（cm）
                 */
                private BigDecimal offerLength;

                /**
                 * OFFER净重（kg）
                 */
                private BigDecimal offerSuttleWeight;

                /**
                 * OFFER包装宽度（cm）
                 */
                private BigDecimal offerWidth;

                /**
                 * 尺寸，单位是厘米，长宽高范围是1-9999999。1688无需关注此字段
                 */
                private String packageSize;

                /**
                 * 发货地描述
                 */
                private String sendGoodsAddressText;

                /**
                 * 重量/毛重，单位千克/件
                 */
                private BigDecimal unitWeight;

                /**
                 * 体积，单位是立方厘米，范围是1-9999999，1688无需关注此字段
                 */
                private Boolean volume;

            }

            /**
             * sku信息
             */
            private List<ProductSKUInfo> skuInfos;

            @Getter
            @Setter
            public static class ProductSKUInfo{

                /**
                 * 可销售数量，国际站无需关注
                 */
                private Integer amountOnSale;

                /**
                 * SKU属性值，可填多组信息
                 */
                private List<SKUAttrInfo> attributes;
                @Getter
                @Setter
                public static class SKUAttrInfo{

                    /**
                     * sku值ID，1688不用关注
                     */
                    private Long attValueID;

                    /**
                     * sku属性类型；1-表示该属性是规格属性；2-表示该属性是规格扩展属性
                     */
                    private String attrType;

                    /**
                     * sku属性ID
                     */
                    private Long attributeID;

                    /**
                     * sku属性ID所对应的显示名，比如颜色，尺码
                     */
                    private String attributeName;

                    /**
                     * sku值内容，国际站不用关注
                     */
                    private String attributeValue;

                    /**
                     * 自定义属性值名称，1688无需关注
                     */
                    private String customValueName;

                    /**
                     * sku图片
                     */
                    private String skuImageUrl;
                }

                /**
                 * 指定规格的货号，国际站无需关注
                 */
                private String cargoNumber;

                /**
                 * 分销基准价。代销场景均使用该价格。无SKU商品查看saleInfo中的consignPrice
                 */
                private BigDecimal consignPrice;

                /**
                 * 报价时该规格的单价，国际站注意要点：含有SKU属性的在线批发产品设定具体价格时使用此值，若设置阶梯价格则使用priceRange
                 */
                private BigDecimal price;

                /**
                 * 阶梯报价，1688无需关注
                 */
                private List<ProductPriceRange> priceRange;
                @Getter
                @Setter
                public static class ProductPriceRange{

                    /**
                     * 商品价格
                     */
                    private BigDecimal price;

                    /**
                     * 商品起订数量
                     */
                    private Integer startQuantity;
                }

                /**
                 * 精选货源一件包邮价
                 */
                private BigDecimal retailPrice;

                /**
                 * 商品编码，1688无需关注
                 */
                private String skuCode;

                /**
                 * skuId, 国际站无需关注
                 */
                private Long skuId;

                /**
                 * specId, 国际站无需关注
                 */
                private String specId;

                /**
                 * 加工定制 sku 维度拿样价，在sku规格报价时有效，国际站无需关注
                 */
                private BigDecimal takeSamplePrice;

                /**
                 * 精选货源批发价
                 */
                private BigDecimal pifaPrice;
            }

            /**
             * 商品状态。published:上网状态;member expired:会员撤销;auto expired:自然过期;expired:过期(包含手动过期与自动过期);member deleted:会员删除;
             * modified:修改;new:新发;deleted:删除;TBD:to be delete;approved:审批通过;auditing:审核中;untread:审核不通过;
             */
            private String status;

            /**
             * 商品标题，最多128个字符
             */
            private String subject;
        }
    }
}
