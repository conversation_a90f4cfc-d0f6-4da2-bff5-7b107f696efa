package com.newnary.gsp.center.tpsi.infra.repository.db.po;

import com.newnary.dao.base.po.TenantBasePO;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DeliveryOrderEcCangApiAssociationPO extends TenantBasePO {

    private static final long serialVersionUID = 6621108619167971296L;

    private String associationId;

    private String deliveryOrderId;

    private String stockoutOrderId;

    private String transportOrderId;

    private String erpTpsId;

    private String wmsTpsId;

    /**
     * 供应链主订单号
     */
    private String tradeOrderId;

    /**
     * 关联子单号
     */
    private String orderRefNo;

    /**
     * erp订单-销售单号
     */
    private String erpOrderSaleOrderCode;

    /**
     * erp订单-状态
     */
    private Integer erpOrderStatus;

    /**
     * erp订单-跟踪号
     */
    private String erpOrderShippingMethodNo;

    /**
     * wms集运订单-订单号
     */
    private String wmsOrderCode;

    /**
     * 出库面单号
     */
    private String wmsOrderLabelCode;

    /**
     * 装箱单号
     */
    private String wmsBoxCode;

    /**
     * wms集运订单-仓库id
     */
    private Integer wmsWarehouseId;

    /**
     * wms集运订单-状态
     */
    private Integer wmsFreigntOrderStatus;

    private Double wmsCblValue;
    private String wmsCblValueCurrency;
    private String wmsFtNameCn;

    private Double wmsWeight;
    private String wmsVolume;
    private Double wmsVolumeWeight;

}
