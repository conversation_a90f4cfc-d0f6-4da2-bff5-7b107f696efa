package com.newnary.gsp.center.tpsi.infra.client.common;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;

import java.awt.image.BufferedImage;
import java.io.*;
import java.net.MalformedURLException;
import java.net.URI;
import java.util.AbstractMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.jar.JarFile;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.ZipEntry;

/**
 * @Author: jack
 * @CreateTime: 2023/12/26
 */
@Slf4j
public class CreatePdfUtil {

    /**
     * 增加表格项
     * @param table
     * @param str 文字
     * @param element 居中、居左、居右
     * @param font 字体格式
     */
    public static void addCell(PdfPTable table, String str, int element, Font font) {
        addCell(table, str, element, font, 1, 0, 0);
    }

    /**
     * 增加表格项
     * @param table
     * @param str 文字
     * @param element 居中、居左、居右
     * @param font 字体格式
     * @param colSize 合并列
     * @param rowSize 合并行
     */
    public static void addCell(PdfPTable table, String str, int element, Font font, int colSize, int rowSize) {
        addCell(table, str, element, font, 1, colSize, rowSize);
    }

    /**
     * 增加表格项
     * @param table
     * @param str 文字
     * @param element 居中、居左、居右
     * @param font 字体格式
     * @param border 表格边框
     * @param colSize 合并列
     * @param rowSize 合并行
     */
    public static void addCell(PdfPTable table, String str, int element, Font font, int border, int colSize, int rowSize) {
        addCell(table, str, element, font, border, colSize, rowSize, 0);
    }

    /**
     * 增加表格项
     * @param table
     * @param str 文字
     * @param element 居中、居左、居右
     * @param font 字体格式
     * @param border 表格边框
     * @param colSize 合并列
     * @param rowSize 合并行
     * @param cellRotation 翻转角度
     */
    public static void addCell(PdfPTable table, String str, int element, Font font, int border, int colSize, int rowSize, int cellRotation) {
        addCell(table, str, element, null, font, border, colSize, rowSize, cellRotation, 0);
    }

    /**
     * 增加表格项
     * @param table
     * @param str 文字
     * @param element 居中、居左、居右
     * @param font 字体格式
     * @param border 表格边框
     * @param colSize 合并列
     * @param rowSize 合并行
     * @param rowHeight 行高
     */
    public static void addCell(PdfPTable table, String str, int element, Font font, int border, int colSize, int rowSize, float rowHeight) {
        addCell(table, str, element, null, font, border, colSize, rowSize, 0, rowHeight);
    }

    /**
     * 增加表格项
     * @param table
     * @param str 文字
     * @param element 居中、居左、居右
     * @param verticalAlign 垂直
     * @param font 字体格式
     * @param border 表格边框
     * @param colSize 合并列
     * @param rowSize 合并行
     * @param rowHeight 行高
     * @param
     */
    public static void addCell(PdfPTable table, String str, int element, Integer verticalAlign, Font font, int border, int colSize, int rowSize, float rowHeight) {
        addCell(table, str, element, verticalAlign, font, border, colSize, rowSize, 0, rowHeight);
    }

    /**
     * 增加表格项
     * @param table
     * @param str 文字
     * @param element 居中、居左、居右
     * @param verticalAlign 垂直
     * @param font 字体格式
     * @param border 表格边框
     * @param colSize 合并列
     * @param rowSize 合并行
     * @param rowHeight 行高
     * @param
     */
    public static void addBlackCell(PdfPTable table, String str, int element, Integer verticalAlign, Font font, int border, int colSize, int rowSize, float rowHeight) {
        PdfPCell cell = new PdfPCell(new Phrase(str == null ? "" : str, font));
        cell.setHorizontalAlignment(element);
        if (null != verticalAlign) {
            cell.setVerticalAlignment(verticalAlign);
        } else {
            cell.setVerticalAlignment(Element.ALIGN_CENTER);
        }
        if (border == 0) {
            cell.setBorder(0);
        }
        if (colSize != 0) {
            cell.setColspan(colSize);
        }
        if (rowSize != 0) {
            cell.setRowspan(rowSize);
        }
        if (rowHeight != 0) {
            cell.setFixedHeight(rowHeight);
        }
        cell.setBackgroundColor(BaseColor.BLACK);
        table.addCell(cell);
    }

    /**
     * 增加表格项
     * @param table
     * @param str 文字
     * @param element 居中、居左、居右
     * @param verticalAlign 垂直
     * @param font 字体格式
     * @param border 表格边框
     * @param colSize 合并列
     * @param rowSize 合并行
     * @param cellRotation 翻转角度
     * @param rowHeight 行高
     */
    public static void addCell(PdfPTable table, String str, int element, Integer verticalAlign, Font font, int border, int colSize, int rowSize, int cellRotation, float rowHeight) {
        PdfPCell cell = new PdfPCell(new Phrase(str == null ? "" : str, font));
        cell.setHorizontalAlignment(element);
        if (null != verticalAlign) {
            cell.setVerticalAlignment(verticalAlign);
        } else {
            cell.setVerticalAlignment(Element.ALIGN_CENTER);
        }
        if (border == 0) {
            cell.setBorder(0);
        }
        if (colSize != 0) {
            cell.setColspan(colSize);
        }
        if (rowSize != 0) {
            cell.setRowspan(rowSize);
        }
        if (cellRotation != 0) {
            cell.setRotation(cellRotation);
        }
        if (rowHeight != 0) {
            cell.setFixedHeight(rowHeight);
        }
        table.addCell(cell);
    }

    /**
     * 增加表格项
     * @param table
     * @param str 文字
     * @param element 居中、居左、居右
     * @param font 字体格式
     * @param colSize 合并列
     * @param rowSize 合并行
     * @param borderDirection 表格方向边框
     */
    public static void addCell(PdfPTable table, String str, int element, Font font, int colSize, int rowSize, PDFDirection borderDirection) {
        addCell(table, str, element, font, colSize, rowSize, 0, 0f, borderDirection);
    }

    /**
     * 增加表格项
     * @param table
     * @param str 文字
     * @param element 居中、居左、居右
     * @param font 字体格式
     * @param colSize 合并列
     * @param rowSize 合并行
     * @param rowHeight
     * @param borderDirection 表格方向边框
     */
    public static void addCell(PdfPTable table, String str, int element, Font font, int colSize, int rowSize, float rowHeight, PDFDirection borderDirection) {
        addCell(table, str, element, font, colSize, rowSize, 0, rowHeight, borderDirection);
    }

    /**
     * 增加表格项
     * @param table
     * @param str 文字
     * @param element 居中、居左、居右
     * @param font 字体格式
     * @param colSize 合并列
     * @param rowSize 合并行
     * @param cellRotation
     * @param borderDirections 表格方向边框
     */
    public static void addCell(PdfPTable table, String str, int element, Font font, int colSize, int rowSize, int cellRotation, float rowHeight, PDFDirection borderDirections) {
        PdfPCell cell = new PdfPCell(new Phrase(str == null ? "" : str, font));
        cell.setHorizontalAlignment(element);
        cell.setVerticalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(0);
        if (null != borderDirections && Objects.equals(borderDirections, PDFDirection.UP)) {
            cell.setBorderWidthTop(0.5f);
        }
        if (null != borderDirections && Objects.equals(borderDirections, PDFDirection.DOWN)) {
            cell.setBorderWidthBottom(0.5f);
        }
        if (null != borderDirections && Objects.equals(borderDirections, PDFDirection.LEFT)) {
            cell.setBorderWidthLeft(0.5f);
        }
        if (null != borderDirections && Objects.equals(borderDirections, PDFDirection.RIGHT)) {
            cell.setBorderWidthRight(0.5f);
        }
        if (colSize != 0) {
            cell.setColspan(colSize);
        }
        if (rowSize != 0) {
            cell.setRowspan(rowSize);
        }
        if (cellRotation != 0) {
            cell.setRotation(cellRotation);
        }
        if (rowHeight != 0) {
            cell.setFixedHeight(rowHeight);
        }
        table.addCell(cell);
    }

    /**
     * 增加表格项
     * @param table
     * @param str 文字
     * @param element 居中、居左、居右
     * @param font 字体格式
     * @param colSize 合并列
     * @param rowSize 合并行
     * @param topBorder 表格方向边框
     * @param bottomBorder 表格方向边框
     * @param leftBorder 表格方向边框
     * @param rightBorder 表格方向边框
     */
    public static void addCell(PdfPTable table, String str, int element, Font font, int colSize, int rowSize, float rowHeight, PDFDirection topBorder, PDFDirection bottomBorder, PDFDirection leftBorder, PDFDirection rightBorder) {
        PdfPCell cell = new PdfPCell(new Phrase(str == null ? "" : str, font));
        cell.setHorizontalAlignment(element);
        cell.setVerticalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(0);
        if (null != topBorder && Objects.equals(topBorder, PDFDirection.UP)) {
            cell.setBorderWidthTop(0.5f);
        }
        if (null != bottomBorder && Objects.equals(bottomBorder, PDFDirection.DOWN)) {
            cell.setBorderWidthBottom(0.5f);
        }
        if (null != leftBorder && Objects.equals(leftBorder, PDFDirection.LEFT)) {
            cell.setBorderWidthLeft(0.5f);
        }
        if (null != rightBorder && Objects.equals(rightBorder, PDFDirection.RIGHT)) {
            cell.setBorderWidthRight(0.5f);
        }
        if (colSize != 0) {
            cell.setColspan(colSize);
        }
        if (rowSize != 0) {
            cell.setRowspan(rowSize);
        }
        if (rowHeight != 0) {
            cell.setFixedHeight(rowHeight);
        }
        table.addCell(cell);
    }

    /**
     *
     * @param table
     * @param str
     * @param element
     * @param font
     * @param colSize
     * @param rowSize
     * @param rowHeight
     * @param borderDirection
     */
    public static void addParagraphCell(PdfPTable table, String str, int element, Font font, int colSize, int rowSize, float rowHeight, PDFDirection borderDirection) {
        PdfPCell cell = new PdfPCell();
        cell.setHorizontalAlignment(element);
        cell.setVerticalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(0);
        if (null != borderDirection && Objects.equals(borderDirection, PDFDirection.UP)) {
            cell.setBorderWidthTop(0.5f);
        }
        if (null != borderDirection && Objects.equals(borderDirection, PDFDirection.DOWN)) {
            cell.setBorderWidthBottom(0.5f);
        }
        if (null != borderDirection && Objects.equals(borderDirection, PDFDirection.LEFT)) {
            cell.setBorderWidthLeft(0.5f);
        }
        if (null != borderDirection && Objects.equals(borderDirection, PDFDirection.RIGHT)) {
            cell.setBorderWidthRight(0.5f);
        }
        if (colSize != 0) {
            cell.setColspan(colSize);
        }
        if (rowSize != 0) {
            cell.setRowspan(rowSize);
        }
        if (rowHeight != 0) {
            cell.setFixedHeight(rowHeight);
        }
        Paragraph paragraph = new Paragraph(str == null ? "" : str, font);
        paragraph.setLeading(0, 1);
        cell.addElement(paragraph);

        table.addCell(cell);
    }

    /**
     *
     * @param table
     * @param str
     * @param element
     * @param font
     * @param border
     * @param colSize
     * @param rowSize
     * @param rowHeight
     */
    public static void addParagraphCell(PdfPTable table, String str, int element, Font font, int border, int colSize, int rowSize, float rowHeight) {
        PdfPCell cell = new PdfPCell();
        cell.setHorizontalAlignment(element);
        cell.setVerticalAlignment(Element.ALIGN_CENTER);
        if (border == 0) {
            cell.setBorder(0);
        }
        if (colSize != 0) {
            cell.setColspan(colSize);
        }
        if (rowSize != 0) {
            cell.setRowspan(rowSize);
        }
        if (rowHeight != 0) {
            cell.setFixedHeight(rowHeight);
        }
        Paragraph paragraph = new Paragraph(str == null ? "" : str, font);
        paragraph.setLeading(0, 1);
        cell.addElement(paragraph);

        table.addCell(cell);
    }

    static float mmPx = 2.83f;

    /**
     * 增加图片表格项
     * @param table
     * @param bufferedImage
     * @param border
     * @param colSize
     * @param rowSize
     * @throws BadElementException
     * @throws IOException
     */
    public static void addImageCell(PdfPTable table, BufferedImage bufferedImage, int border, int colSize, int rowSize, float rowHeight) throws BadElementException, IOException {
        addImageCell(table, bufferedImage, border, colSize, rowSize, rowHeight, 0);
    }

    /**
     * 增加图片表格项
     * @param table
     * @param bufferedImage
     * @param border
     * @param colSize
     * @param rowSize
     * @throws BadElementException
     * @throws IOException
     */
    public static void addImageCell(PdfPTable table, BufferedImage bufferedImage, int border, int colSize, int rowSize, float rowHeight, int cellRotation) throws BadElementException, IOException {
        Image image = null;
        image = Image.getInstance(bufferedImage, null);
        image.setAlignment(Element.ALIGN_CENTER);
        image.setWidthPercentage(90);
        image.setScaleToFitHeight(true);
        PdfPCell cell = new PdfPCell();
        cell.addElement(image);
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        if (border == 0) {
            cell.setBorder(0);
        }
        if (colSize != 0) {
            cell.setColspan(colSize);
        }
        if (rowSize != 0) {
            cell.setRowspan(rowSize);
        }
        if (rowHeight != 0) {
            cell.setFixedHeight(rowHeight);
        }
        if (cellRotation != 0) {
            cell.setRotation(cellRotation);
        }
        table.addCell(cell);
    }

    /**
     * 增加图片表格项
     * @param table
     * @param picPath
     * @param border
     * @param colSize
     * @param rowSize
     * @param rowHeight
     * @param fitWidth
     * @param
     * @throws BadElementException
     * @throws IOException
     */
    public static void addImageCell(PdfPTable table, String picPath, int border, int colSize, int rowSize, float rowHeight, Float fitWidth, Integer verticalAlign) throws BadElementException, IOException {
        Image image = null;
        image = Image.getInstance(picPath);
        image.setAlignment(Element.ALIGN_CENTER);
        if (null != fitWidth) {
            image.scaleToFit(fitWidth, rowHeight);
        }
        PdfPCell cell = new PdfPCell();
        cell.addElement(image);
        if (null != verticalAlign) {
            cell.setVerticalAlignment(verticalAlign);
        }
        if (border == 0) {
            cell.setBorder(0);
        }
        if (colSize != 0) {
            cell.setColspan(colSize);
        }
        if (rowSize != 0) {
            cell.setRowspan(rowSize);
        }
        if (rowHeight != 0) {
            cell.setFixedHeight(rowHeight);
        }
        table.addCell(cell);
    }

    /**
     * 增加水印表格项
     * @param table
     * @param picPath
     * @param border
     * @param colSize
     * @param rowSize
     * @throws BadElementException
     * @throws IOException
     */
    public static void addWaterMaskCell(PdfPTable table, String picPath, int border, int colSize, int rowSize, float rowHeight) throws BadElementException, IOException {
        Image image = null;
        image = Image.getInstance(picPath);
        image.setAlignment(Element.ALIGN_CENTER);
        PdfPCell cell = new PdfPCell();
        if (border == 0) {
            cell.setBorder(0);
        }
        if (colSize != 0) {
            cell.setColspan(colSize);
        }
        if (rowSize != 0) {
            cell.setRowspan(rowSize);
        }
        if (rowHeight != 0) {
            cell.setFixedHeight(rowHeight);
        }
        cell.setCellEvent(new ImageBackgroudEvent(image));
        table.addCell(cell);
    }

    static class ImageBackgroudEvent implements PdfPCellEvent {

        protected Image image;

        public ImageBackgroudEvent(Image image) {
            this.image = image;
        }

        @Override
        public void cellLayout(PdfPCell pdfPCell, Rectangle position, PdfContentByte[] canvases) {
            try {
                PdfContentByte cb = canvases[PdfPTable.BACKGROUNDCANVAS];
                image.scaleAbsolute(position);
                image.setAbsolutePosition(position.getLeft(), position.getBottom());
                cb.addImage(image);
            } catch (DocumentException e) {
                throw new ExceptionConverter(e);
            }
        }
    }

    /**
     * 增加图片表格项
     * @param table
     * @param image
     * @param element
     * @param border
     * @param colSize
     * @param rowSize
     */
    public static void addImageCell(PdfPTable table, Image image, int element, int border, int colSize, int rowSize, float rowHeight) {
        PdfPCell cell = new PdfPCell(image,true);
        cell.setHorizontalAlignment(element);
        if (border == 0) {
            cell.setBorder(0);
        }
        if (colSize != 0) {
            cell.setColspan(colSize);
        }
        if (rowSize != 0) {
            cell.setRowspan(rowSize);
        }
        table.addCell(cell);
    }

    /**
     * 初始化图片
     * @param path
     * @return
     * @throws BadElementException
     * @throws IOException
     */
    public static Image initImage(String path) throws BadElementException, IOException {
        Image image = null;
        try {
            image = Image.getInstance(path);
        } catch (Exception e) {
            String nopicture = Thread.currentThread().getContextClassLoader().getResource("").getPath()+ "..\\images\\nopicture.png";
            image = Image.getInstance(nopicture);
        }
        image.scalePercent(25);
        image.setAlignment(Element.ALIGN_CENTER);
        return image;
    }

    /**
     * 增加图片
     * @param doc
     * @param path 图片路径
     * @param isTooLong
     * @throws MalformedURLException
     * @throws IOException
     * @throws DocumentException
     */
    public static void addImage(Document doc, String path, boolean isTooLong) throws MalformedURLException, IOException, DocumentException {
        Image image = null;
        image = Image.getInstance(path);
        if (isTooLong) {
            image.scalePercent(65);
        } else {
            image.scalePercent(50);
        }
        image.setAlignment(Element.ALIGN_CENTER);
        doc.add(image);
    }

    /**
     * 换行
     * @param doc
     * @param size
     * @throws DocumentException
     */
    public static void newline(Document doc, int size) throws DocumentException {
        Paragraph empty = new Paragraph(" ", FontFactory.getFont(FontFactory.COURIER, size, BaseColor.BLACK));
        doc.add(empty);
    }

    /**
     * 增加文字
     * @param doc
     * @param bf 字体
     * @param size 字号
     * @param font 字形(正常NORMAL、是否加粗BOLD)
     * @param color 颜色
     * @param str 文字
     * @param element 居中、居左、居右
     * @throws DocumentException
     */
    public static void addStr(Document doc, BaseFont bf, int size, int font, BaseColor color, String str, int element) throws DocumentException {
        Font fontChine = new Font(bf, size, font, color);
        Paragraph paragraph = new Paragraph(str, fontChine);
        paragraph.setAlignment(element);
        doc.add(paragraph);
    }

    /**
     * 将byte数组数据转换成pdf文件
     * @param bs
     * @param file
     * @throws IOException
     */
    public static void byteArrayToPDF(byte[] bs, File file) throws IOException {
        ByteArrayInputStream bais = new ByteArrayInputStream(bs);
        FileOutputStream fos = new FileOutputStream(file);
        byte[] data = new byte[512];
        while (bais.read(data) != -1) {
            fos.write(data);
        }
        fos.close();
    }

    public static File createFile(String fileFolder, String fileName) {
        File folder = new File(fileFolder);
        File file = new File(fileFolder + File.separator + fileName);
        //如果文件夹不存在，则创建文件夹
        if (folder.exists() == false) {
            folder.mkdirs();	//多级目录
        }
        //如果文件不存在，则创建文件
        if (file.exists() == false) {
            try {
                file.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return file;
    }

    /**
     * 复制本地资源文件到指定目录
     * @param fileRoot      需要复制的资源目录文件夹
     * @param regExpStr     资源文件匹配正则，*表示匹配所有
     * @param tempParent    保存地址
     */
    public static String copyLocalResourcesFileToTemp(String fileRoot, String regExpStr, String tempParent) {
        String filePath = tempParent;
        try {
            ResourcePatternResolver resourcePatternResolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = resourcePatternResolver.getResources("classpath*:".concat(fileRoot).concat(regExpStr));
            for (Resource resource : resources) {
                File newFile = new File(tempParent, resource.getFilename());
                if (newFile.exists()) {
                    newFile.delete();
                }
                InputStream stream = null;
                try {
                    stream = resource.getInputStream();
                } catch (Exception e) {
                    // 如果resource为文件夹时，会报异常，这里直接忽略这个异常
                }
                if (stream == null) {
                    newFile.mkdirs();
                    copyLocalResourcesFileToTemp(fileRoot + resource.getFilename() + File.separator, regExpStr, tempParent + File.separator + resource.getFilename());
                } else {
                    if (!newFile.getParentFile().exists()) {
                        newFile.getParentFile().mkdirs();
                    }
                    FileUtils.copyInputStreamToFile(stream, newFile);
                }
                filePath = newFile.getParentFile().getCanonicalPath();
            }
        } catch (Exception e) {
            log.error("failed to copy local source template", e);
        }
        return filePath;
    }

    /**
     * 复制jar包中的资源文件到指定目录
     * @param path          jar包所在路径
     * @param tempPath      保存目录
     * @param filePrefix    需要进行复制的资源文件目录：以BOOT-INF/classes/开头
     */
    public static String copyJarResourcesFileToTemp(URI path, String tempPath, String filePrefix) {
        String filePath = tempPath;
        try {
            List<Map.Entry<ZipEntry, InputStream>> collect =
                    readJarFile(new JarFile(path.getPath()), filePrefix).collect(Collectors.toList());
            for (Map.Entry<ZipEntry, InputStream> entry : collect) {
                // 文件相对路径
                String key = entry.getKey().getName();
                // 文件流
                InputStream stream = entry.getValue();
                File newFile = new File(tempPath + key.replaceAll("BOOT-INF/classes", ""));
                if (!newFile.getParentFile().exists()) {
                    newFile.getParentFile().mkdirs();
                }
                FileUtils.copyInputStreamToFile(stream, newFile);
                filePath = newFile.getParentFile().getCanonicalPath();
            }
        } catch (IOException e) {
            log.error("failed to copy jar source template", e);
        }
        return filePath;
    }

    @SneakyThrows
    public static Stream<Map.Entry<ZipEntry, InputStream>> readJarFile(JarFile jarFile, String prefix) {
        Stream<Map.Entry<ZipEntry, InputStream>> readingStream =
                jarFile.stream().filter(entry -> !entry.isDirectory() && entry.getName().startsWith(prefix))
                        .map(entry -> {
                            try {
                                return new AbstractMap.SimpleEntry<>(entry, jarFile.getInputStream(entry));
                            } catch (IOException e) {
                                return new AbstractMap.SimpleEntry<>(entry, null);
                            }
                        });
        return readingStream.onClose(() -> {
            try {
                jarFile.close();
            } catch (IOException e) {
                log.error("failed to close jarFile", e);
            }
        });
    }
}
