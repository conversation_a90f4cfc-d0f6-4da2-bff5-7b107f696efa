package com.newnary.gsp.center.tpsi.service;

import com.newnary.gsp.center.tpsi.api.mabang.request.SyncProductFromMaBangCommand;
import com.newnary.gsp.center.tpsi.api.mabang.request.SyncProductPriceFromMaBangCommand;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;

public interface ISyncProductBizSve {

    /**
     * 同步通途商品
     *
     * @param thirdPartySystemId
     */
    void syncTongTuProduct(String thirdPartySystemId);

    /**
     * 接收者处理同步通途商品
     *
     * @param thirdPartySystem
     * @param data
     */
    void doTongTuProductSync(ThirdPartySystem thirdPartySystem, String data);

    /**
     * 同步马帮商品
     *
     * @param req
     */
    void syncProductFromMaBang(SyncProductFromMaBangCommand req);

    /**
     * 商品创建消息接收者执行创建商品请求并记录结果
     */
    void doOpenSupplierProductCreateReq(String reqStr, String valueKey, String valueJson, String type);

    /**
     * 同步马帮商品库存
     *
     * @param req
     */
    void syncProductPriceFromMaBang(SyncProductPriceFromMaBangCommand req);
}
