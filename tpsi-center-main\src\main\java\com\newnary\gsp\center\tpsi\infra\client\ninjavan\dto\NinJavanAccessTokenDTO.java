package com.newnary.gsp.center.tpsi.infra.client.ninjavan.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


@Getter
@Setter
public class NinJavanAccessTokenDTO {

    @NotBlank(message = "访问令牌(不能为空)")
    private String accessToken;
    @NotBlank(message = "令牌类型(不能为空) 默认中 ：bearer")
    private String tokenType;
    @NotNull(message = "超时时间(不能为空)")
    private Long expires;
    @NotNull(message = "有效时长(不能为空)")
    private Long expiresIn;


    public boolean hasExpired() {
        return getExpires()>=System.currentTimeMillis();
    }

}
