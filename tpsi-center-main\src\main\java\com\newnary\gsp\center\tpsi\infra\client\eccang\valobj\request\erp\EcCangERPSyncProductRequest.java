package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp;

import com.alibaba.fastjson.annotation.JSONField;
import com.newnary.gsp.center.product.api.product.request.SupplierSkuSpecInfo;

import java.util.ArrayList;
import java.util.List;

public class EcCangERPSyncProductRequest {

    public String actionType;
    public String productSku;
    public String productTitle;
    public String productTitleEn;
    public String pdOverseaTypeCn;
    public String pdOverseaTypeEn;
    public String productDeclaredValue;
    public String pdDeclareCurrencyCode;


    public Double productPurchaseValue;
    public Double productPurchaseValueNotTax;
    public String currencyCode;
    public Double productWeight;
    public String defaultSupplierCode;
    public Double productPrice;
    public Double referenceUnitPrice;

    /**
     * 采购参考链接  "refUrl":[                 "https://detail.16881.com/offer/39249591406.html",                 "https://detail.16882.com/offer/39249591406.html", ]
     */
    public List<String> refUrl;

    public String procutCategoryName1;
    public String procutCategoryName2;
    public String procutCategoryName3;
    public String procutCategoryNameEn1;
    public String procutCategoryNameEn2;
    public String procutCategoryNameEn3;
    public Integer productCategroyId1;
    public Integer productCategroyId2;
    public Integer productCategroyId3;

    public Integer oprationType;
    public Integer productStatus;
    public Integer saleStatus;
    public String hsCode;
    public Double productLength;
    public Double productWidth;
    public Double productHeight;
    public Double pdNetLength;
    public Double pdNetWidth;
    public Double pdNetHeight;
    public String productSpecs;

    public Double pdNetWeight;
    public Double productPackageWeight;
    public Double allowFloatWeight;
    public Double fboTaxRate;
    public String designerStartTime;
    public String designerEndTime;

    public Integer designerId;
    public Integer personOpraterId;
    public Integer personSellerId;
    public Integer personDevelopId;
    public Integer productColorId;
    public Integer productSizeId;

    public String upcCode;
    public String productMaterial;
    public String materialEn;
    public Integer productFbaSizeType;
    public Integer spEtaTime;
    public Integer spMinQty;

    public List<String> spProductAddress;
    public Integer prlId;
    public Integer parentProductId;
    public Integer isEndProduct;
    public Integer containBattery;
    public Integer isImitation;
    public Integer isQc;
    public String qcTemplateName;
    public Integer productIsCombination;
    public String labelingType;

    public List<SelfProperty> selfPropertyList;
    public String brandCode;
    public String brandName;
    public Integer isExpDate;
    public Integer expDate;
    public WarehouseBarcodeList warehouseBarcodeList;

    public String productOrigin;
    public Double grossProfit;
    public Integer isGift;
    public Double taxRate;
    public List<String> productImgUrlList;
    public String desc;
    public Integer userOrganizationId;
    public Integer defaultWarehouseId;
    public String eanCode;
    public String puc_id;
    public Integer defaultBuyWarehouseId;
    public String logisticAttribute;

    public Fitting fitting;

    @JSONField(name = "package")
    public Package packingType;

    public Integer containSpecialGoods;
    public Integer containNonLiquidCosmetics;
    public Integer periodic;
    public Integer prtId;

    public List<String> seller;

    public static class Fitting {
        public String productSku;
        public String fittingQty;
        public String fittingDesc;
    }

    public static class Package {
        public String warehouseCode;
        public String packageQty;
        public String packageCode;
    }

    public static class SelfProperty {
        public String name;
        public String value;
    }

    public static class WarehouseBarcodeList {
        public String warehouseCode;
        public String barcode;
    }

    public void changeProductProperty(List<SupplierSkuSpecInfo> specInfos) {
        List<SelfProperty> ret = new ArrayList<>();
        specInfos.forEach(specInfo -> {
            SelfProperty property = new SelfProperty();
            property.name = specInfo.specName;
            property.value = specInfo.specValue;
            ret.add(property);
        });
        selfPropertyList = ret;
    }
}
