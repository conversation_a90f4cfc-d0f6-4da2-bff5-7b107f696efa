package com.newnary.gsp.center.tpsi.service.impl;

import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.mq.producer.ApiDockingProducer;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.messagebody.gsp.tpsi.GSPApiDockingTopic;
import com.newnary.messagebody.gsp.tpsi.mo.ApiDockingMO;
import com.newnary.messagebody.gsp.tpsi.mo.ProductCreateMO;
import com.newnary.mq.starter.producer.SendMsgRequest;

import javax.annotation.Resource;

public abstract class SystemClientSve {

    @Resource
    private ApiDockingProducer apiDockingProducer;

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    public void broadcast(String thirdPartySystemId, String tag, String message) {
        SendMsgRequest request = new SendMsgRequest();
        ApiDockingMO mo = new ApiDockingMO();
        mo.thirdPartySystemId = thirdPartySystemId;
        mo.data = message;
        request.setKey(thirdPartySystemId)
                .setContent(mo)
                .setTopic(GSPApiDockingTopic.TOPIC);
        switch (tag) {
            case "PRODUCT_SYNC":
                request.setTag(GSPApiDockingTopic.Tag.PRODUCT_SYNC);
                break;
            case "ORDER_SYNC":
                request.setTag(GSPApiDockingTopic.Tag.ORDER_SYNC);
                break;
            case "STOCK_SYNC_GET_TONGTU_STOCK":
                request.setTag(GSPApiDockingTopic.Tag.STOCK_SYNC_FROM_TONGTU_TO_MABANG);
                break;
            default:
                throw new ServiceException(CommonErrorInfo.ERROR_100_SYSTEM_ERROR, "不支持的消息类型");
        }

        apiDockingProducer.sendMessage(request);
    }

    public void broadcastProductCreate(String tag, String req, String key, String valueJson, String type) {
        SendMsgRequest request = new SendMsgRequest();
        ProductCreateMO mo = new ProductCreateMO();
        mo.setValueKey(key);
        mo.setValueJson(valueJson);
        mo.setOpenSupplierProductCreateReqStr(req);
        mo.setValueType(type);
        request.setKey(key)
                .setContent(mo)
                .setTopic(GSPApiDockingTopic.TOPIC);
        switch (tag) {
            case "PRODUCT_CREATE":
                request.setTag(GSPApiDockingTopic.Tag.PRODUCT_CREATE);
                break;
            default:
                throw new ServiceException(CommonErrorInfo.ERROR_100_SYSTEM_ERROR, "不支持的消息类型");
        }
        apiDockingProducer.sendMessage(request);
    }

    public ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }
}
