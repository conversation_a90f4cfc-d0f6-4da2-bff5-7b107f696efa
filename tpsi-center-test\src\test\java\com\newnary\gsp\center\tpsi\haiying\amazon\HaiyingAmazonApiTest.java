package com.newnary.gsp.center.tpsi.haiying.amazon;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.common.PageCondition;
import com.newnary.api.base.common.PageList;
import com.newnary.api.base.common.PageMeta;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingAmazonProductListOrderBy;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingOrderByType;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingStation;
import com.newnary.gsp.center.tpsi.api.haiying.request.amazon.HaiYingAmazonProductListCommand;
import com.newnary.gsp.center.tpsi.api.haiying.response.amazon.HaiYingAmazonProductListDTO;
import com.newnary.gsp.center.tpsi.ctrl.haiying.HaiYingAmazonCommand2RequestTranslator;
import com.newnary.gsp.center.tpsi.ctrl.haiying.HaiYingAmazonResponse2DTOTranslator;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.HaiYingDataApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.amazon.HaiYingAmazonCategoryTreeRequest;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.amazon.HaiYingAmazonProductDetailInfoRequest;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.amazon.HaiYingAmazonProductHistoryInfoRequest;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.amazon.HaiYingAmazonProductListResponse;
import com.newnary.gsp.center.tpsi.service.haiying.IHaiYingDataAmazonApiSve;
import com.newnary.test.starter.BaseTestInjectTenant;
import org.junit.Test;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
public class HaiyingAmazonApiTest extends BaseTestInjectTenant {

    private static final SimpleDateFormat SIMPLE_TIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    protected String tenantId() {
        return "TENANT3924117688555169382400";
    }

    @Resource
    private IHaiYingDataAmazonApiSve haiYingAmazonDataApiSve;

    @Test
    public void testAmazonProductList() throws ParseException {
        HaiYingAmazonProductListCommand command = new HaiYingAmazonProductListCommand();
        command.setStation(HaiYingStation.AMAZON_US);
        command.setPrice_status(0);
        command.setPageCondition(new PageCondition(1, 10));
        command.setOrder_by(HaiYingAmazonProductListOrderBy.top_sellers_rank);
        command.setOrder_by_type(HaiYingOrderByType.DESC);
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingAmazonDataApiSve.getProductList(HaiYingAmazonCommand2RequestTranslator.transAmazonProductList(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingAmazonProductListResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingAmazonProductListResponse.class);
            PageList<HaiYingAmazonProductListDTO> ret = HaiYingAmazonResponse2DTOTranslator.transAmazonProductListList(command.getStation(), responseList, getResultPageMeta(command.getPageCondition(), apiBaseResult));
            System.out.println(JSON.toJSONString(ret));
        }
    }

    @Test
    public void testAmazonProductDetailInfo() {
        HaiYingAmazonProductDetailInfoRequest request = new HaiYingAmazonProductDetailInfoRequest();
        request.setStation(HaiYingStation.AMAZON_UK.getSite());
        request.setAsins("B08FZGD8PM");
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingAmazonDataApiSve.getProductDetailInfo(request);
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
//            List<HaiYingAmazonProductDetailInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingAmazonProductDetailInfoResponse.class);
//            List<HaiYingAmazonProductDetailInfoDTO> ret = HaiYingResponse2DTOTranslator.transAmazonProductDetailInfoList(responseList);
//            System.out.println(JSON.toJSONString(ret));
        }
    }

    @Test
    public void testAmazonCategoryTree() {
        HaiYingAmazonCategoryTreeRequest request = new HaiYingAmazonCategoryTreeRequest();
        request.setStation(HaiYingStation.AMAZON_UK.getSite());
        request.setCate_id("59624031");
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingAmazonDataApiSve.getCategoryTree(request);
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
//            List<HaiYingAmazonCategoryTreeResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingAmazonCategoryTreeResponse.class);
//            List<HaiYingAmazonCategoryTreeDTO> ret = HaiYingResponse2DTOTranslator.transAmazonCategoryTreeList(responseList);
//            System.out.println(JSON.toJSONString(ret));
        }
    }

    @Test
    public void testAmazonProductHistoryInfo() throws ParseException {
        HaiYingAmazonProductHistoryInfoRequest request = new HaiYingAmazonProductHistoryInfoRequest();
        request.setStation(HaiYingStation.AMAZON_UK.getSite());
        request.setAsins("B08FZGD8PM");
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingAmazonDataApiSve.getProductHistoryInfo(request);
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
//            List<HaiYingAmazonCategoryInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingAmazonCategoryInfoResponse.class);
//            PageList<HaiYingAmazonSubCategoryInfoDTO> ret = HaiYingResponse2DTOTranslator.transAmazonSubCategoryInfoList(responseList, getResultPageMeta(Integer.valueOf(request.getCurrent_page()), apiBaseResult));
//            System.out.println(JSON.toJSONString(ret));
        }
    }

    private PageMeta getResultPageMeta(PageCondition pageCondition, HaiYingDataApiBaseResult<String> apiBaseResult) {
        if (apiBaseResult.getTotalSize() > 0) {
            PageMeta pageMeta = new PageMeta();
            if (null != pageCondition) {
                pageMeta.pageNum = pageCondition.pageNum;
            } else {
                pageMeta.pageNum = 1;
            }
            pageMeta.pageSize = apiBaseResult.getSize();
            if (apiBaseResult.getTotalSize() % apiBaseResult.getSize() == 0) {
                pageMeta.pages = apiBaseResult.getTotalSize() / apiBaseResult.getSize();
            } else {
                pageMeta.pages = (apiBaseResult.getTotalSize() / apiBaseResult.getSize()) + 1;
            }
            pageMeta.total = apiBaseResult.getTotalSize();
            return pageMeta;
        }
        return null;
    }

}
