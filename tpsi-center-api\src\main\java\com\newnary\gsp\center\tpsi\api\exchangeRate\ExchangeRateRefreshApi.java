package com.newnary.gsp.center.tpsi.api.exchangeRate;

import com.newnary.api.base.common.CommonResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

public interface ExchangeRateRefreshApi {

    /**
     * 刷新汇率
     *
     * @param sourceCurrency
     * @return
     */
    @GetMapping("refreshExchangeRate")
    CommonResponse<String> refreshExchangeRate(@RequestParam("sourceCurrency") String sourceCurrency);
}
