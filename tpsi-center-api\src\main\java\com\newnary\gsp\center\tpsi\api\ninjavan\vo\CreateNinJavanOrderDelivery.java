package com.newnary.gsp.center.tpsi.api.ninjavan.vo;

import lombok.Data;

import javax.validation.constraints.Size;

/**
 * 创建能者物流 delivery
 * 派送详情
 *
 * cod 订单才会有此类目
 *
 * <AUTHOR>
 * @since Created on 2023-08-11
 **/
@Data
public class CreateNinJavanOrderDelivery {

    /**
     * 指定货到付款的金额，以目的地币种
     **/
    private Float cashOnDelivery;

    /**
     * ①包裹价值≤2500PHP 时，不保价，不传值
     * ②包裹价值≥50000PHP 时，保价金额写50000PHP
     * ③2500＜包裹价值＜50000PHP 时，保价金额写采购价
     * 判断根据 订单的金额判断
     **/
    private Float insuredValue;


}
