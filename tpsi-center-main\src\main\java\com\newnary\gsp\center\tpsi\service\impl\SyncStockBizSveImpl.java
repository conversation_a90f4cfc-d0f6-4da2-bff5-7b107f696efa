package com.newnary.gsp.center.tpsi.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.common.PageCondition;
import com.newnary.api.base.common.PageList;
import com.newnary.gsp.center.product.api.open.feign.OpenSupplierProductFeignApi;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierUpdateStockReq;
import com.newnary.gsp.center.tpsi.api.mabang.request.SyncStockQuantityFromMaBangCommand;
import com.newnary.gsp.center.tpsi.api.tongtu.request.SyncStockFromTongTuCommand;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.MaBangStockQauntityList;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.warehouse.MaBangDoAddStorage;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.warehouse.MaBangStorageData;
import com.newnary.gsp.center.tpsi.infra.model.ApiDockingResult;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.repository.IApiDockingResultRepository;
import com.newnary.gsp.center.tpsi.service.IMaBangApiDataTranslateSve;
import com.newnary.gsp.center.tpsi.service.ISyncStockBizSve;
import com.newnary.gsp.center.tpsi.service.mabang.IMaBangStockApiSve;
import com.newnary.gsp.center.tpsi.service.mabang.IMaBangWarehouseApiSve;
import com.newnary.gsp.center.tpsi.service.tongtu.ITongTuErp2Sve;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 完成对单个sku的获取通途库存然后更新到马帮系统
 */
@Slf4j
@Component
public class SyncStockBizSveImpl extends SystemClientSve implements ISyncStockBizSve {

    @Resource
    private ITongTuErp2Sve tongTuStocksQuerySve;

    @Resource
    private IMaBangWarehouseApiSve maBangWarehouseApiSve;

    @Resource
    private IMaBangStockApiSve maBangStockApiSve;

    @Resource
    private IApiDockingResultRepository apiDockingResultRepository;

    @Resource
    private IMaBangApiDataTranslateSve maBangApiDataTranslateSve;

    @Resource
    private OpenSupplierProductFeignApi openSupplierProductFeignApi;

    @Override
    public void syncStockFromTongTuToMaBang(String sku, String maBangThirdPartySystemId, String tongTuThirdPartySystemId) {
        //先获取通途库存数量
        SyncStockFromTongTuCommand syncStockFromTongTuCommand = new SyncStockFromTongTuCommand();
        syncStockFromTongTuCommand.setThirdPartySystemId(tongTuThirdPartySystemId);
        syncStockFromTongTuCommand.setSku(sku);
        Integer stockRet = tongTuStocksQuerySve.stocksQuery(syncStockFromTongTuCommand);

        //把结果放到马帮上
        MaBangDoAddStorage maBangWMaBangDoAddStorage = new MaBangDoAddStorage();
        //TODO 待补充马帮的仓库，统一一个仓库，或者把通途的仓库在马帮再创建一次。待补充操作人名字
        maBangWMaBangDoAddStorage.setWarehouseName("");
        maBangWMaBangDoAddStorage.setEmployeeName("");
        MaBangStorageData maBangStorageData = new MaBangStorageData();
        maBangStorageData.setStockSku(sku);
        maBangStorageData.setQuantity(stockRet);
        maBangWMaBangDoAddStorage.setData(maBangStorageData);
        maBangWarehouseApiSve.warehouseDoAddStorage(maBangThirdPartySystemId, JSONObject.toJSONString(maBangWMaBangDoAddStorage));

    }

    @Override
    public void syncStockQuantityFromMaBang(SyncStockQuantityFromMaBangCommand req) {
        ThirdPartySystem thirdPartySystem = loadSystem(req.getThirdPartySystemId());
        List<String> limitList = new ArrayList<>();

        log.info("查询已保存的库存sku列表");
        //第一步，查询请求结果记录表，获取已经保存的子sku
        if (StringUtils.isNotEmpty(req.getAppointSku())) {
            Optional<ApiDockingResult> appoint = apiDockingResultRepository.loadByKeyAndType(req.getAppointSku(), "MaBangStockSku");
            if (appoint.isPresent()) {
                limitList.add(appoint.get().getValueKey());
                handleUpdateStock(limitList, thirdPartySystem);
            }
        } else {
            log.info("分页获取sku");
            PageList<ApiDockingResult> pageList = new PageList<ApiDockingResult>();
            PageCondition pageCondition = new PageCondition();
            pageCondition.setPageSize(50);
            int pageNum = 1;
            do {
                pageCondition.setPageNum(pageNum);
                pageList = apiDockingResultRepository.pageQueryByTypeAndStatus("MaBangStockSku", "child-saved", pageCondition);
                log.info("第{}页,有{}条", pageNum, pageList.getItems().size());
                limitList = pageList.getItems().stream().map(o -> o.getValueKey()).collect(Collectors.toList());
                handleUpdateStock(limitList, thirdPartySystem);
                pageNum++;
            } while (pageList.getItems().size() == 50);
        }
        log.info("库存列表库存处理完毕");

    }

    private void handleUpdateStock(List<String> limitList, ThirdPartySystem thirdPartySystem) {
        String stockSkuStr = StringUtils.join(limitList, ",");
        log.info("开始获取库存sku{}的库存数量信息", stockSkuStr);
        MaBangStockQauntityList maBangStockQauntityList = maBangStockApiSve.stockGetStockQuantity(thirdPartySystem.getBizId(), stockSkuStr);
        //第三步把马帮ERP上的库存信息同步到供应链分销ERP
        if (maBangStockQauntityList != null && maBangStockQauntityList.getData().size() > 0) {
            maBangStockQauntityList.getData().forEach(maBangStockQauntity -> {
                        maBangStockQauntity.getWarehouse().forEach(maBangStockQuantityWarehouse -> {
                            if (StringUtils.isNotEmpty(maBangStockQuantityWarehouse.getWarehouseName()) && maBangStockQuantityWarehouse.getWarehouseName().equals("默认仓库")) {
                                OpenSupplierUpdateStockReq openSupplierUpdateStockReq = maBangApiDataTranslateSve.buildOpenSupplierUpdateStockReq(thirdPartySystem, maBangStockQuantityWarehouse, maBangStockQauntity.getStockSku());
                                log.info("库存sku{}-默认仓库的数量为{}", maBangStockQauntity.getStockSku(), maBangStockQuantityWarehouse.getStockQuantity());
                                try {
                                    openSupplierProductFeignApi.updateStock(openSupplierUpdateStockReq).mustSuccessOrThrowOriginal();
                                    log.info("库存sku{}-默认仓库更新库存成功", maBangStockQauntity.getStockSku());
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    log.error("库存sku{}-默认仓库更新库存失败,原因:{}", maBangStockQauntity.getStockSku(), e.getMessage());
                                }
                            } else {
                                log.info("库存sku{}-仓库{}非默认仓库，不处理", maBangStockQauntity.getStockSku(), maBangStockQuantityWarehouse.getWarehouseName());
                            }
                        });
                    }

            );

        }

    }

}
