package com.newnary.gsp.center.tpsi.app.listener.mq;

import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.mq.consumer.ApiDockingMQConsumer;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.service.mabang.IMaBangOrderApiSve;
import com.newnary.messagebody.gsp.tpsi.GSPApiDockingTopic;
import com.newnary.messagebody.gsp.tpsi.mo.ApiDockingMO;
import com.newnary.mq.starter.consumer.AbstractMQProcessor;
import com.newnary.mq.starter.consumer.MQMessage;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date ：Created in 2022/01/04
 */
@Component
public class OrderSync_ApiDockingOrderSyncProcessor extends AbstractMQProcessor<ApiDockingMO> {

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Resource
    private IMaBangOrderApiSve maBangOrderApiSve;

    @Override
    public boolean doProcess(MQMessage<ApiDockingMO> message) {
        ApiDockingMO mo = message.getContent();
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(mo.getThirdPartySystemId());

        switch (thirdPartySystem.getProvider()) {
            case MABANG:
                maBangOrderApiSve.doOrderSync(thirdPartySystem, mo.data);
                break;
            default:
                break;
        }
        return true;
    }

    @Override
    public Class<?> consumerClz() {
        return ApiDockingMQConsumer.class;
    }

    @Override
    public String tag() {
        return GSPApiDockingTopic.Tag.ORDER_SYNC;
    }

    private ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }
}
