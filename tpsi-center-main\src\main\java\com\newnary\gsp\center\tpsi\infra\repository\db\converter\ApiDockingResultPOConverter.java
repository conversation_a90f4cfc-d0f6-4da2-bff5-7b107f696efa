package com.newnary.gsp.center.tpsi.infra.repository.db.converter;

import com.newnary.dao.base.converter.POConverter;
import com.newnary.gsp.center.tpsi.infra.model.ApiDockingResult;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.ApiDockingResultPO;

public class ApiDockingResultPOConverter implements POConverter<ApiDockingResultPO, ApiDockingResult> {

    @Override
    public ApiDockingResultPO convert2PO(ApiDockingResult domain) {
        ApiDockingResultPO po = new ApiDockingResultPO();
        po.setId(domain.getId());
        po.setValueKey(domain.getValueKey());
        po.setValueType(domain.getValueType().name());
        po.setValue<PERSON>son(domain.getValueJson());
        po.setDataStatus(domain.getDataStatus());
        return po;
    }
}
