package com.newnary.gsp.center.tpsi.infra.client.eccang;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.common.utils.httpmethod.ApiBaseResult;
import com.newnary.common.utils.httpmethod.HttpMethodUtil;
import com.newnary.common.utils.httpmethod.XmlHandleUtil;
import com.newnary.common.utils.xml.JaxbUtil;
import com.newnary.gsp.center.tpsi.infra.client.eccang.params.EcCangTMSParams;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangBaseRequest;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangCreateOrderRequest;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangFeeTrailReqeust;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 派菲顾 TMS 请求客户端
 *
 * <AUTHOR>
 * @date 2022/2/22
 */
@Slf4j
public class EcCangTMSApiClient {

    private static String APP_TOKEN;
    private static String APP_KEY;
    private static String BASE_URL;

    public EcCangTMSApiClient(String ecCangTMSParams) {
        EcCangTMSParams params = JSON.parseObject(ecCangTMSParams, EcCangTMSParams.class);
        this.APP_TOKEN = params.getAPP_TOKEN();
        this.APP_KEY = params.getAPP_KEY();
        this.BASE_URL = params.getBASE_URL();
    }

    public EcCangTMSApiClient(EcCangTMSParams params) {
        this.APP_TOKEN = params.getAPP_TOKEN();
        this.APP_KEY = params.getAPP_KEY();
        this.BASE_URL = params.getBASE_URL();
    }

    private static final String TEST_ORDER_NO = "SO6471193884573568995328";

    public static void main(String[] args) {
        //queryShippingMethod();
        //feeTrail();
        //getCountry();
        //createOrder();
        //getOrder();
        getTrackNumber();
    }

    /**
     * 获取配送方式
     */
    public static void queryShippingMethod() {
        Map<String, String> param = new HashMap<>();
        param.put("country_code", "AM");
        String response = sendRequest(param, "getShippingMethod");
        System.out.println(response);
    }

    /**
     * 创建订单
     * TODO 佳梅 需要修改，定义接口（tpsi起个定时任务，定时拉取供应链分销ERP的出库单（只过滤已发货状态），拉取到后，调用此接口进行tms的创建）
     */
    public static void createOrder() {
        EcCangCreateOrderRequest request = new EcCangCreateOrderRequest();
        // 订单基础信息
        request.reference_no = "TEST" + System.currentTimeMillis();
        // 测试配送方式
        request.shipping_method = "PK0035";
        // 中国
        request.country_code = "NO";
        request.order_weight = 0.185;
        //request.order_pieces = 1;
        //request.insurance_value = 0.0;
        request.mail_cargo_type = "1";
        request.invoice_fee = 0.0;


        // 收货人信息
        EcCangCreateOrderRequest.Consignee consignee = new EcCangCreateOrderRequest.Consignee();
        consignee.consignee_company = "";
        consignee.consignee_province = "Fredrikstad";
        consignee.consignee_city = "Fredrikstad";

        consignee.consignee_street = "Sondre Toppen 8";
        consignee.consignee_street2 = "";
        consignee.consignee_street3 = "";
        consignee.consignee_postcode = "1615";
        consignee.consignee_name = "Hedda Fredrikke Wiese";
        consignee.consignee_telephone = "+47-41420336";
        consignee.consignee_mobile = "+47-41420336";
        consignee.consignee_email = "";
        consignee.consignee_doorplate = "";
        consignee.consignee_taxno = "";
        request.Consignee = consignee;

        // 发件人信息
        EcCangCreateOrderRequest.Shipper shipper = new EcCangCreateOrderRequest.Shipper();
        shipper.shipper_company = "YG";
        shipper.shipper_countrycode = "CN";
        shipper.shipper_province = "GuangXi";
        shipper.shipper_city = "NanNing";
        shipper.shipper_street = "Building 1, Nanning bonded Logistics Center, 1219 Yinhai Ave";
        shipper.shipper_postcode = "530000";
        shipper.shipper_areacode = "";
        shipper.shipper_name = "MR.YANG";
        shipper.shipper_telephone = "86159753000241";
        request.Shipper = shipper;

        // 商品信息
        List<EcCangCreateOrderRequest.Item> items = new ArrayList<>();
        EcCangCreateOrderRequest.Item item = new EcCangCreateOrderRequest.Item();
        item.invoice_enname = "Manicure nail kit";
        item.invoice_cnname = "美甲工具套装";
        item.invoice_quantity = 1;
        //item.unit_code = "PCE";
        item.invoice_unitcharge = 1.0;
//        item.hs_code = "";
//        item.invoice_note = "MH01-PGP010";
//        item.invoice_url = "https://global.mabangerp.com/image/bs_logo2_mabang3.png";
        item.invoice_weight = "0.185";
//        //item.invoice_brand = "服装";
//        item.box_number = "892205321928064";
//        item.sku = "MH01-PGP010";
//        item.material = "";
//        item.material_enture = "";
//        item.invoice_currencycode = "USD";
//        item.use = "";
//        item.country_of_origin = "CN";
        items.add(item);
        request.ItemArr = items;

        // 箱体信息
        List<EcCangCreateOrderRequest.Volume> volumes = new ArrayList<>();
        EcCangCreateOrderRequest.Volume volume = new EcCangCreateOrderRequest.Volume();
        volume.length = "10";
        volume.width = "10";
        volume.height = "10";
        volume.weight = "0.185";
        // volume.box_number = "U001";
        // volume.child_number = request.reference_no;
        volumes.add(volume);
        //request.Volume = volumes;

        System.out.println(JSON.toJSONString(request));
        String response = sendRequest(request, "createOrder");
        System.out.println(response);
    }

    /**
     * 取消订单
     * TODO 佳梅 需要修改，定义接口（供应链分销ERP定义出库单取消的消息，tpsi接口消息，然后调用tms的取消接口）
     */
    public static void cancelOrder() {
        Map<String, String> param = new HashMap<>();
        //单号，注：只有草稿、已预报状态支持取消
        param.put("reference_no", "");
        //单号类型：1-运单号,2-客户订单号,3-跟踪号
        param.put("type", "2");
        String result = sendRequest(param, "cancelOrder");
        System.out.println(result);
    }

    /**
     * 拦截订单
     * TODO 佳梅 需要修改，定义接口（供应链分销ERP定义出库单取消的消息，tpsi接口消息，然后调用tms的取消接口）（重要：可以考虑在tpsi内部，拦截订单的接口 与 取消订单的接口，内部分析判断具体调用哪个）
     */
    public static void interceptOrder() {
        Map<String, String> param = new HashMap<>();
        // 单号
        param.put("reference_no", "");
        // 单号类型，同取消订单
        param.put("type", "1");
        // 拦截原因
        param.put("hold_on_remark", "test");
        String result = sendRequest(param, "interceptOrder");
        System.out.println(result);
    }

    /**
     * 查询订单
     * TODO 佳梅 需要修改，定义接口 （起个定时任务，更新供应链分销ERP物流单状态）
     * TODO matt 供应链分销ERP的状态字段看看是否需要修改（重要）
     */
    public static void getOrder() {
        Map<String, String> param = new HashMap<>();
        // 单号
        param.put("reference_no", "TEST1648577795635");
        String result = sendRequest(param, "getOrder");
        System.out.println(result);
    }

    /**
     * 轨迹查询接口
     */
    public static void getCargoTrack() {
        Map<String, String> param = new HashMap<>();
        //  单号数组,如{"codes":["TFL1"," TFL2"]}
        param.put("codes", "");
        // 单号类型：1-运单号,2-客户订单号,3-跟踪号，不填默认三个单号都支持查询
        param.put("type", "2");
        String result = sendRequest(param, "getCargoTrack");
        System.out.println(result);
    }

    /**
     * 获取订单跟踪号
     * TODO 佳梅 需要修改，定义接口 （和getOrder 是同一个定时任务，查询到数据后，tms订单的状态字段，运单号，跟踪单号字段，更新到供应链分销ERP中）
     */
    public static void getTrackNumber() {
        Map<String, Object> param = new HashMap<>();
        // 单号
        param.put("reference_no", Collections.singletonList(TEST_ORDER_NO));
        String result = sendRequest(param, "getTrackNumber");
        System.out.println(result);
    }


    /**
     * 查询订单运费明细
     */
    public static void getReceivingExpense() {
        Map<String, String> param = new HashMap<>();
        // 单号
        param.put("reference_no", "");
        String result = sendRequest(param, "getReceivingExpense");
        System.out.println(result);
    }

    /**
     * 验证客户单号是否在系统已存在
     */
    public static void checkReferenceNo() {
        Map<String, String> param = new HashMap<>();
        //  客户单号
        param.put("code", "");
        // 单号类型：1-运单号,2-客户订单号,3-跟踪号，不填默认三个单号都支持查询
        param.put("type", "2");
        String paramJson = JSON.toJSONString(param);
        String result = sendRequest(paramJson, "checkReferenceNo");
        System.out.println(result);
    }

    /**
     * 获取单个标签接口
     */
    public static void getLabelUrl() {
        Map<String, String> param = new HashMap<>();
        param.put("reference_no", "");
        //单号类型 1：运单号 2：客户单号 3：跟踪号，不传默认自动匹配三个单号
        param.put("type", "1");
        String result = sendRequest(param, "getLabelUrl");
        System.out.println(result);
    }

    /**
     * 获取国家代码、发货默认获取中国的国家代码
     */
    public static void getCountry() {
        String getCountry = sendRequest(null, "getCountry");
        System.out.println(getCountry);
    }

    /**
     * 获取全部运输方式
     */
    public static void getShippingMethodInfo() {
        Map<String, String> param = new HashMap<>();
        param.put("country_code", "");
        param.put("group_code", "");
        String result = sendRequest(param, "getShippingMethodInfo");
        System.out.println(result);
    }

    /**
     * 获取发件人信息
     */
    public static void getSenderMessage() {
        Map<String, String> param = new HashMap<>();
        param.put("reference_no", "");
        String result = sendRequest(param, "getSenderMessage");
        System.out.println(result);
    }

    /**
     * 根据模板获取发票/配货单
     */
    public static void getLabelByTemplate() {
        Map<String, String> param = new HashMap<>();
        // 模板id（有对方业务人员给定）
        param.put("template_id", "");
        // 运单号数组
        param.put("codes", null);
        String result = sendRequest(param, "");
        System.out.println(result);
    }


    /**
     * 运费试算
     */
    public static void feeTrail() {
        EcCangFeeTrailReqeust request = new EcCangFeeTrailReqeust();
        request.country_code = "US";
        request.weight = "1.35";
        request.shipping_type_id = "W";
        request.group = "PY";
        String paramJson = JSON.toJSONString(request);
        String response = sendRequest(paramJson, "feeTrail");
        System.out.println(response);
    }


    public static String sendRequest(Object param, String service) {

        EcCangBaseRequest baseRequest = getBaseRequest(param, service);

        String requestXml = JaxbUtil.toXml(baseRequest);
        ApiBaseResult apiBaseResult = null;
        try {
            apiBaseResult = HttpMethodUtil.syncPostMethodBySetBodyPara(BASE_URL,
                    3,
                    null,
                    "application/xml",
                    null, null, requestXml);

            //返回的是xml,需要进行处理得到里面的response
            JSONObject o = XmlHandleUtil.xmlHandle(apiBaseResult.getRet());
            String responseStr = o.getJSONObject("Body").getJSONObject("callServiceResponse").getJSONObject("response").toString();
            log.info("[ecang] 请求结束, param={}, requestXml={}, response={}", requestXml, service, responseStr);
            return responseStr;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }


    public static EcCangBaseRequest getBaseRequest(Object param, String service) {
        EcCangBaseRequest request = new EcCangBaseRequest();
        request.body = new EcCangBaseRequest.RequestBody();
        request.body.callService = new EcCangBaseRequest.CallService();
        request.body.callService.appKey = APP_KEY;
        request.body.callService.appToken = APP_TOKEN;
        request.body.callService.service = service;
        if (null != param) {
            request.body.callService.paramJson = JSON.toJSONString(param);
        }
        return request;
    }
}
