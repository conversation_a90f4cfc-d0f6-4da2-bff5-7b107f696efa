package com.newnary.gsp.center.tpsi.infra.mq.consumer;

import com.newnary.messagebody.gsp.tpsi.GSPApiDockingTopic;
import com.newnary.mq.starter.consumer.AbstractMQConsumer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2021/12/22 15:16
 */
@Component
public class ApiDockingMQConsumer extends AbstractMQConsumer {

    @Value("${gsp.api-docking.mq.consumer-id}")
    private String group;

    @Override
    public String topic() {
        return GSPApiDockingTopic.TOPIC;
    }

    @Override
    public String consumerGroup() {
        return group;
    }
}
