package com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.request;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class VVICGetItemListReq {

    /**
     * 商品上架时间-起始时间 ： 2018-09-05 12:00:00
     */
    //@NotNull(message = "商品上架起始时间不能为空")
    private String up_time_start;

    /**
     * 商品上架时间-终止时间(时间范围限制30分钟内) ： 2018-09-05 12:00:00
     */
   // @NotNull(message = "商品上架终止时间不能为空")
    private String up_time_end;

    /**
     * 	档口VVICID
     */
    private String shop_vid;

    /**
     * 	风格，多个用英文逗号分隔  如: 通勤,韩版
     */
    private String style;

    /**
     * 	季节，多个用英文逗号分隔  如: 	春,夏,秋,冬
     */
    private String season;

    /**
     * 	分市场，多个用英文逗号分隔，gz-广州，pn-普宁，不传入参数时，默认取值为权限配置的分市场
     */
    private String city_market_code;

    /**
     * 	返回结果语言，默认为en，可选en，cn
     */
    private String lang;

    /**
     * 		默认1
     */
    private Integer page;
}
