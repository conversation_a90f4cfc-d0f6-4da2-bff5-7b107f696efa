package com.newnary.gsp.center.tpsi.api.haiying.response.shopee;

import lombok.Data;

import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingShopeeProductExtInfoDTO {

    /**
     * 商品id
     */
    private String pid;

    /**
     * 商品请求状态
     * (200:正常   0:商品id异常)
     */
    private Integer product_status;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 商品标签
     */
    private String tags;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 商品附图链接
     */
    private List<String> images_list;

    /**
     * 商品sku列表
     */
    private List<HaiYingShopeeProductSkuInfoDTO> shopee_product_sku_list;

}
