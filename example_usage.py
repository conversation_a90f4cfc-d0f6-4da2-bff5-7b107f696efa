#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
发票OCR处理示例脚本
演示如何使用InvoiceOCRProcessor处理发票文件
"""

from invoice_ocr_processor import InvoiceOCRProcessor
import os

def main():
    """示例用法"""
    
    # 创建发票OCR处理器
    # 如果有GPU，可以设置use_gpu=True来加速处理
    processor = InvoiceOCRProcessor(use_gpu=False)
    
    # 指定包含发票文件的文件夹路径
    # 请将此路径替换为您的实际发票文件夹路径
    invoice_folder = "invoices"  # 示例文件夹路径
    
    # 检查文件夹是否存在
    if not os.path.exists(invoice_folder):
        print(f"文件夹 '{invoice_folder}' 不存在，请创建该文件夹并放入发票文件")
        print("支持的文件格式：PDF, PNG, JPG, JPEG, BMP, TIFF")
        return
    
    # 指定输出Excel文件名
    output_excel = "发票识别结果.xlsx"
    
    print(f"开始处理文件夹: {invoice_folder}")
    print(f"输出文件: {output_excel}")
    print("=" * 50)
    
    # 处理文件夹中的所有发票
    processor.process_folder(invoice_folder, output_excel)
    
    print("=" * 50)
    print("处理完成！")
    
    # 如果输出文件存在，显示一些统计信息
    if os.path.exists(output_excel):
        import pandas as pd
        try:
            df = pd.read_excel(output_excel)
            print(f"共处理发票: {len(df)} 张")
            
            # 按发票类型统计
            if 'invoice_type' in df.columns:
                type_counts = df['invoice_type'].value_counts()
                print("\n发票类型统计:")
                for invoice_type, count in type_counts.items():
                    print(f"  {invoice_type}: {count} 张")
            
            # 显示前几行数据
            print(f"\n前5行数据预览:")
            print(df.head().to_string(index=False))
            
        except Exception as e:
            print(f"读取输出文件时出错: {e}")


if __name__ == "__main__":
    main()
