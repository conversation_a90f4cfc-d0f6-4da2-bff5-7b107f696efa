package com.newnary.gsp.center.tpsi.ctrl.exchangeRate;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.gsp.center.tpsi.api.exchangeRate.ExchangeRateRefreshApi;
import com.newnary.gsp.center.tpsi.service.exchangeRate.ExchangeRateRefreshSve;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class ExchangeRateRefreshApiImpl implements ExchangeRateRefreshApi {

    @Resource
    private ExchangeRateRefreshSve exchangeRateRefreshSve;


    @Override
    public CommonResponse<String> refreshExchangeRate(String sourceCurrency) {
        return CommonResponse.success(
                exchangeRateRefreshSve.refreshExchangeRate(sourceCurrency)
        );
    }
}
