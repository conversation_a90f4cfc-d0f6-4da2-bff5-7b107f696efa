#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
发票OCR识别处理脚本
使用PaddleOCR识别PDF和图片格式的发票，提取关键字段并保存到Excel
支持增值税专用发票和普通发票
"""

import os
import re
import io
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import fitz  # PyMuPDF
from PIL import Image
import numpy as np
from paddleocr import PaddleOCR
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('invoice_ocr.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class InvoiceOCRProcessor:
    """发票OCR处理器"""
    
    def __init__(self, use_gpu: bool = False):
        """
        初始化OCR处理器
        
        Args:
            use_gpu: 是否使用GPU加速
        """
        self.ocr = PaddleOCR(use_angle_cls=True, lang='ch', use_gpu=use_gpu)
        
        # 发票字段正则表达式模式
        self.patterns = {
            # 发票代码
            'invoice_code': [
                r'发票代码[：:]\s*(\d{12})',
                r'代码[：:]\s*(\d{12})',
                r'(\d{12})\s*发票代码'
            ],
            # 发票号码
            'invoice_number': [
                r'发票号码[：:]\s*(\d{8})',
                r'号码[：:]\s*(\d{8})',
                r'(\d{8})\s*发票号码'
            ],
            # 开票日期
            'invoice_date': [
                r'开票日期[：:]\s*(\d{4}年\d{1,2}月\d{1,2}日)',
                r'开票日期[：:]\s*(\d{4}-\d{1,2}-\d{1,2})',
                r'开票日期[：:]\s*(\d{4}/\d{1,2}/\d{1,2})',
                r'(\d{4}年\d{1,2}月\d{1,2}日)'
            ],
            # 购买方名称
            'buyer_name': [
                r'购买方[：:]\s*名称[：:]\s*([^\n\r]+)',
                r'购买方名称[：:]\s*([^\n\r]+)',
                r'名称[：:]\s*([^\n\r]+)'
            ],
            # 购买方税号
            'buyer_tax_number': [
                r'购买方[：:]\s*纳税人识别号[：:]\s*([A-Z0-9]{15,20})',
                r'纳税人识别号[：:]\s*([A-Z0-9]{15,20})',
                r'税号[：:]\s*([A-Z0-9]{15,20})'
            ],
            # 销售方名称
            'seller_name': [
                r'销售方[：:]\s*名称[：:]\s*([^\n\r]+)',
                r'销售方名称[：:]\s*([^\n\r]+)',
                r'开票人[：:]\s*([^\n\r]+)'
            ],
            # 销售方税号
            'seller_tax_number': [
                r'销售方[：:]\s*纳税人识别号[：:]\s*([A-Z0-9]{15,20})',
                r'销售方纳税人识别号[：:]\s*([A-Z0-9]{15,20})'
            ],
            # 合计金额
            'total_amount': [
                r'合计[：:]\s*¥?\s*([\d,]+\.?\d*)',
                r'价税合计[（(]大写[）)][：:]\s*[^\d]*¥?\s*([\d,]+\.?\d*)',
                r'总计[：:]\s*¥?\s*([\d,]+\.?\d*)',
                r'金额合计[：:]\s*¥?\s*([\d,]+\.?\d*)'
            ],
            # 税额
            'tax_amount': [
                r'税额[：:]\s*¥?\s*([\d,]+\.?\d*)',
                r'税额合计[：:]\s*¥?\s*([\d,]+\.?\d*)'
            ],
            # 不含税金额
            'amount_without_tax': [
                r'金额[：:]\s*¥?\s*([\d,]+\.?\d*)',
                r'不含税金额[：:]\s*¥?\s*([\d,]+\.?\d*)'
            ]
        }
    
    def pdf_to_images(self, pdf_path: str) -> List[np.ndarray]:
        """
        将PDF转换为图片
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            图片数组列表
        """
        images = []
        try:
            doc = fitz.open(pdf_path)
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                # 设置缩放比例以提高图片质量
                mat = fitz.Matrix(2.0, 2.0)
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("png")
                
                # 转换为PIL Image然后转为numpy数组
                pil_image = Image.open(io.BytesIO(img_data))
                img_array = np.array(pil_image)
                images.append(img_array)
            doc.close()
        except Exception as e:
            logger.error(f"PDF转换失败 {pdf_path}: {e}")
        
        return images
    
    def load_image(self, image_path: str) -> Optional[np.ndarray]:
        """
        加载图片文件
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            图片数组或None
        """
        try:
            image = Image.open(image_path)
            # 转换为RGB格式
            if image.mode != 'RGB':
                image = image.convert('RGB')
            return np.array(image)
        except Exception as e:
            logger.error(f"图片加载失败 {image_path}: {e}")
            return None
    
    def extract_text_from_image(self, image: np.ndarray) -> str:
        """
        从图片中提取文本
        
        Args:
            image: 图片数组
            
        Returns:
            提取的文本
        """
        try:
            result = self.ocr.ocr(image, cls=True)
            if result and result[0]:
                text_lines = []
                for line in result[0]:
                    if len(line) >= 2:
                        text_lines.append(line[1][0])
                return '\n'.join(text_lines)
        except Exception as e:
            logger.error(f"OCR识别失败: {e}")
        
        return ""
    
    def extract_invoice_fields(self, text: str) -> Dict[str, str]:
        """
        从文本中提取发票字段
        
        Args:
            text: OCR识别的文本
            
        Returns:
            提取的字段字典
        """
        fields = {}
        
        for field_name, patterns in self.patterns.items():
            for pattern in patterns:
                match = re.search(pattern, text, re.IGNORECASE | re.MULTILINE)
                if match:
                    value = match.group(1).strip()
                    # 清理金额字段中的逗号
                    if 'amount' in field_name and value:
                        value = value.replace(',', '')
                    fields[field_name] = value
                    break
        
        return fields
    
    def determine_invoice_type(self, text: str) -> str:
        """
        判断发票类型
        
        Args:
            text: OCR识别的文本
            
        Returns:
            发票类型
        """
        if '增值税专用发票' in text or '专用发票' in text:
            return '增值税专用发票'
        elif '增值税普通发票' in text or '普通发票' in text:
            return '增值税普通发票'
        else:
            return '未知类型'
    
    def process_file(self, file_path: str) -> List[Dict[str, str]]:
        """
        处理单个文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            提取的发票信息列表
        """
        file_path = Path(file_path)
        file_extension = file_path.suffix.lower()
        
        logger.info(f"处理文件: {file_path}")
        
        invoices_data = []
        
        try:
            if file_extension == '.pdf':
                # 处理PDF文件
                images = self.pdf_to_images(str(file_path))
                for i, image in enumerate(images):
                    text = self.extract_text_from_image(image)
                    if text.strip():
                        fields = self.extract_invoice_fields(text)
                        fields['file_name'] = file_path.name
                        fields['page_number'] = i + 1
                        fields['invoice_type'] = self.determine_invoice_type(text)
                        fields['processing_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        invoices_data.append(fields)
            
            elif file_extension in ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']:
                # 处理图片文件
                image = self.load_image(str(file_path))
                if image is not None:
                    text = self.extract_text_from_image(image)
                    if text.strip():
                        fields = self.extract_invoice_fields(text)
                        fields['file_name'] = file_path.name
                        fields['page_number'] = 1
                        fields['invoice_type'] = self.determine_invoice_type(text)
                        fields['processing_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        invoices_data.append(fields)
        
        except Exception as e:
            logger.error(f"处理文件失败 {file_path}: {e}")
        
        return invoices_data
    
    def process_folder(self, folder_path: str, output_excel: str = "invoices_data.xlsx"):
        """
        处理文件夹中的所有发票文件

        Args:
            folder_path: 文件夹路径
            output_excel: 输出Excel文件名
        """
        folder_path = Path(folder_path)

        if not folder_path.exists():
            logger.error(f"文件夹不存在: {folder_path}")
            return

        # 支持的文件扩展名
        supported_extensions = {'.pdf', '.png', '.jpg', '.jpeg', '.bmp', '.tiff'}

        # 查找所有支持的文件
        files_to_process = []
        for ext in supported_extensions:
            files_to_process.extend(folder_path.glob(f"*{ext}"))
            files_to_process.extend(folder_path.glob(f"*{ext.upper()}"))

        if not files_to_process:
            logger.warning(f"在文件夹 {folder_path} 中未找到支持的文件")
            return

        logger.info(f"找到 {len(files_to_process)} 个文件待处理")

        all_invoices_data = []
        processed_count = 0
        error_count = 0

        # 处理每个文件，显示进度
        for i, file_path in enumerate(files_to_process, 1):
            try:
                print(f"处理进度: {i}/{len(files_to_process)} - {file_path.name}")
                invoices_data = self.process_file(file_path)
                if invoices_data:
                    all_invoices_data.extend(invoices_data)
                    processed_count += len(invoices_data)
                    logger.info(f"成功处理 {file_path.name}，提取 {len(invoices_data)} 张发票")
                else:
                    logger.warning(f"文件 {file_path.name} 未提取到发票信息")
            except Exception as e:
                error_count += 1
                logger.error(f"处理文件 {file_path.name} 时出错: {e}")
                continue

        # 显示处理统计
        print(f"\n处理统计:")
        print(f"  总文件数: {len(files_to_process)}")
        print(f"  成功提取发票数: {processed_count}")
        print(f"  错误文件数: {error_count}")

        # 保存到Excel
        if all_invoices_data:
            self.save_to_excel(all_invoices_data, output_excel)
            logger.info(f"处理完成，共提取 {len(all_invoices_data)} 张发票信息")
            logger.info(f"结果已保存到: {output_excel}")
            print(f"结果已保存到: {output_excel}")
        else:
            logger.warning("未提取到任何发票信息")
            print("警告: 未提取到任何发票信息，请检查文件格式和内容")
    
    def save_to_excel(self, invoices_data: List[Dict[str, str]], output_file: str):
        """
        将发票数据保存到Excel文件
        
        Args:
            invoices_data: 发票数据列表
            output_file: 输出文件路径
        """
        # 定义Excel列名
        columns = [
            'file_name', 'page_number', 'invoice_type', 'invoice_code', 'invoice_number',
            'invoice_date', 'buyer_name', 'buyer_tax_number', 'seller_name', 'seller_tax_number',
            'amount_without_tax', 'tax_amount', 'total_amount', 'processing_time'
        ]
        
        # 创建DataFrame
        df = pd.DataFrame(invoices_data)
        
        # 确保所有列都存在
        for col in columns:
            if col not in df.columns:
                df[col] = ''
        
        # 重新排序列
        df = df[columns]
        
        # 保存到Excel
        try:
            df.to_excel(output_file, index=False, engine='openpyxl')
            logger.info(f"数据已保存到Excel文件: {output_file}")
        except Exception as e:
            logger.error(f"保存Excel文件失败: {e}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='发票OCR识别处理工具')
    parser.add_argument('folder_path', help='包含发票文件的文件夹路径')
    parser.add_argument('-o', '--output', default='invoices_data.xlsx', help='输出Excel文件名')
    parser.add_argument('--gpu', action='store_true', help='使用GPU加速')
    
    args = parser.parse_args()
    
    # 创建处理器
    processor = InvoiceOCRProcessor(use_gpu=args.gpu)
    
    # 处理文件夹
    processor.process_folder(args.folder_path, args.output)


if __name__ == "__main__":
    main()
