package com.newnary.gsp.center.tpsi.pms;

import com.alibaba.fastjson.JSON;
import com.newnary.api.base.common.PageCondition;
import com.newnary.gsp.center.purchase.api.product.request.SpuPageQueryCommand;
import com.newnary.gsp.center.purchase.api.product.response.SpuInfo;
import com.newnary.gsp.center.purchase.api.tags.request.TagsCreateCommand;
import com.newnary.gsp.center.tpsi.app.job.PurchaseSystemJobManager;
import com.newnary.gsp.center.tpsi.infra.client.eccang.mapping.EcCangERPMapping;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingType;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.ThirdPartyMappingManager;
import com.newnary.gsp.center.tpsi.infra.rpc.PurchaseRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.PurchaseSpuRpc;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.test.starter.BaseTestInjectTenant;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class PurchaseSystemTest extends BaseTestInjectTenant {
    @Override
    protected String tenantId() {
        return "TENANT3924117688555169382400";
    }

    @Resource
    private PurchaseSpuRpc purchaseSpuRpc;
    @Resource
    private PurchaseRpc purchaseRpc;

    @Resource
    private PurchaseSystemJobManager purchaseSystemJobManager;
    @Resource
    private ThirdPartyMappingManager thirdPartyMappingManager;

    @Test
    public void testQuerySpuList() {
        SpuPageQueryCommand spuPageQueryCommand = new SpuPageQueryCommand();
        PageCondition pageCondition = new PageCondition();
        pageCondition.setPageSize(50);
        pageCondition.setPageNum(1);
        spuPageQueryCommand.setPageCondition(pageCondition);
        List<SpuInfo> spuInfoList = purchaseSpuRpc.getSpuInfoList(spuPageQueryCommand);
        System.out.println(JSON.toJSONString(spuInfoList));
    }

    @Test
    public void testSyncShengWeiProduct() {
        // ReturnT<String> stringReturnT = shengWeiERPJobManager.syncShengWeiProduct("{\"perGroupCount\":20,\"supplierId\":\"VD1726390599342509133824\",\"warehouse\":\"ZJ-TESTSUPPLIER_WAREHOUSE\",\"categoryId\":\"112159730303048\",\"insertPage\":1,\"indexPage\":1,\"pageSize\":20}");

        ReturnT<String> stringReturnT = purchaseSystemJobManager.syncECangProductToShengWei("{\"thirdPartySystemId\":\"TESTECCANGERP0003\",\"perGroupCount\":5,\"insertPage\":20,\"indexPage\":0,\"pageSize\":100,\"supplierId\":\"VD0992389509044584452096\",\"warehouse\":\"W131729237082158\",\"categoryId\":\"PMC4889395323644735262720\",\"products\":\"\"}");
    }

    //创建标签
    @Test
    public void testCreateTags() {
        Iterator<Map.Entry<Integer, String>> iterator = EcCangERPMapping.SaleStatusNameMapping.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Integer, String> next = iterator.next();
            TagsCreateCommand tagsCreateCommand = new TagsCreateCommand();
            tagsCreateCommand.setParentCategoryId("0");
            tagsCreateCommand.setName(next.getValue());
            tagsCreateCommand.setSameLevelSequence(1);
            purchaseRpc.createTag(tagsCreateCommand);
        }
    }

    @Test
    public void testSyncSupplierMapping() {
        purchaseSystemJobManager.syncEcSupplierInfoMapping("{\"thirdPartySystemId\":\"TESTECCANGERP0003\",\"targetBizId\":\"ECCANG\",\"sourceBizId\":\"SHENGWEI\"}");
    }

    @Test
    public void testUserMapping() {
//        thirdPartyMappingManager.insertOrUpdate("PMS", "ECCANG", "广州-MT-测试仓库", "228", ThirdPartyMappingType.WAREHOUSE.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("PMS", "ECCANG", "佛山01仓", "7", ThirdPartyMappingType.WAREHOUSE.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("PMS", "ECCANG", "广州仓", "8", ThirdPartyMappingType.WAREHOUSE.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("PMS", "ECCANG", "佛山中转仓", "322", ThirdPartyMappingType.WAREHOUSE.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("PMS", "ECCANG", "国内虚拟仓", "105", ThirdPartyMappingType.WAREHOUSE.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("PMS", "ECCANG", "中国包材仓", "130", ThirdPartyMappingType.WAREHOUSE.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("PMS", "ECCANG", "BGS办公室", "256", ThirdPartyMappingType.WAREHOUSE.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("PMS", "ECCANG", "OZON半托管仓(爱夫卡)", "347", ThirdPartyMappingType.WAREHOUSE.name(), null);

        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER1596324686150482661376", "1004", ThirdPartyMappingType.USER_ID.name(), null);

//        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER0108329723743964368896", "908", ThirdPartyMappingType.USER_ID.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER0155329723926731165696", "941", ThirdPartyMappingType.USER_ID.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER0204329724121976016896", "942", ThirdPartyMappingType.USER_ID.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER0271329724388276572160", "943", ThirdPartyMappingType.USER_ID.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER0310329724541351890944", "944", ThirdPartyMappingType.USER_ID.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER0361329724741722181632", "945", ThirdPartyMappingType.USER_ID.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER1935332659855187906560", "911", ThirdPartyMappingType.USER_ID.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER2226332661056449155072", "835", ThirdPartyMappingType.USER_ID.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER9408341709106769432576", "892", ThirdPartyMappingType.USER_ID.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER9518341709550237388800", "916", ThirdPartyMappingType.USER_ID.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER7287363443528652689408", "889", ThirdPartyMappingType.USER_ID.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER3774397046625312313344", "834", ThirdPartyMappingType.USER_ID.name(), null);
//
//        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER0108329723743964368896", "jayden", ThirdPartyMappingType.USER_ID2CODE.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER0155329723926731165696", "heliangli", ThirdPartyMappingType.USER_ID2CODE.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER0204329724121976016896", "yeyingying", ThirdPartyMappingType.USER_ID2CODE.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER0271329724388276572160", "zhuqihong", ThirdPartyMappingType.USER_ID2CODE.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER0310329724541351890944", "yuanhong", ThirdPartyMappingType.USER_ID2CODE.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER0361329724741722181632", "zhangyepin", ThirdPartyMappingType.USER_ID2CODE.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER1935332659855187906560", "LQZ", ThirdPartyMappingType.USER_ID2CODE.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER2226332661056449155072", "feng2", ThirdPartyMappingType.USER_ID2CODE.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER9408341709106769432576", "ysc", ThirdPartyMappingType.USER_ID2CODE.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER9518341709550237388800", "ZYM", ThirdPartyMappingType.USER_ID2CODE.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER7287363443528652689408", "dong", ThirdPartyMappingType.USER_ID2CODE.name(), null);
//        thirdPartyMappingManager.insertOrUpdate("GSP", "ECCANG", "USER3774397046625312313344", "linda2", ThirdPartyMappingType.USER_ID2CODE.name(), null);
    }

    @Test
    public void testSyncPurchaseOrders2EcCang() {
        ReturnT<String> stringReturnT = purchaseSystemJobManager.syncPurchaseOrders2EcCang("");
    }

    //测试供应商更新
    @Test
    public void testUpdateSupplierAddress() {
/*        UpdateSkuAlilinkCommand updateSkuAlilinkCommand = new UpdateSkuAlilinkCommand();
        updateSkuAlilinkCommand.setCustomCode("4874922100795");
        updateSkuAlilinkCommand.setAlilink("http://1688");
        purchaseRpc.updateSkuAlilink(updateSkuAlilinkCommand);*/
        ReturnT<String> stringReturnT = purchaseSystemJobManager.syncSkuSupplierLink("{\"thirdPartySystemId\":\"TESTECCANGERP0003\",\"perGroupCount\":5,\"insertPage\":2,\"indexPage\":10,\"pageSize\":100}");
        System.out.println(stringReturnT);
    }

}
