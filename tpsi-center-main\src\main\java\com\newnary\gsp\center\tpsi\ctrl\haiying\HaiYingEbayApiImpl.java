package com.newnary.gsp.center.tpsi.ctrl.haiying;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.common.CommonResponse;
import com.newnary.api.base.common.PageCondition;
import com.newnary.api.base.common.PageList;
import com.newnary.api.base.common.PageMeta;
import com.newnary.gsp.center.tpsi.api.haiying.HaiYingEbayApi;
import com.newnary.gsp.center.tpsi.api.haiying.request.ebay.*;
import com.newnary.gsp.center.tpsi.api.haiying.response.ebay.*;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.HaiYingDataApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.ebay.*;
import com.newnary.gsp.center.tpsi.service.haiying.IHaiYingDataEbayApiSve;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-13
 */
@RestController
@Slf4j
public class HaiYingEbayApiImpl implements HaiYingEbayApi {

    private static final Integer pageLimit = 100000;

    @Resource
    private IHaiYingDataEbayApiSve haiYingEbayDataApiSve;

    @Override
    public CommonResponse<PageList<HaiYingEbayProductListDTO>> getEbayProductList(HaiYingEbayProductListCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingEbayDataApiSve.getProductList(HaiYingEbayCommand2RequestTranslator.transEbayProductList(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingEbayProductListResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingEbayProductListResponse.class);
            if (apiBaseResult.getTotalSize() > pageLimit)
                apiBaseResult.setTotalSize(pageLimit); //TODO 因海鹰api限制返回前10w条
            return CommonResponse.success(HaiYingEbayResponse2DTOTranslator.transEbayProductListList(command.getStation(), responseList, getResultPageMeta(command.getPageCondition(), apiBaseResult)));
        } else {
            log.error("{}获取海鹰ebay商品列表失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            PageList<HaiYingEbayProductListDTO> ret = new PageList<>();
            PageMeta pageMeta = new PageMeta();
            pageMeta.pageNum = command.getPageCondition().pageNum;
            pageMeta.pageSize = command.getPageCondition().pageSize;
            ret.setPageMeta(pageMeta);
            return CommonResponse.success(ret);
        }
    }

    @Override
    public CommonResponse<List<HaiYingEbayProductDetailInfoDTO>> getEbayProductDetailInfo(HaiYingEbayProductDetailInfoCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingEbayDataApiSve.getProductDetailInfo(HaiYingEbayCommand2RequestTranslator.transEbayProductDetailInfo(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingEbayProductDetailInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingEbayProductDetailInfoResponse.class);
            return CommonResponse.success(HaiYingEbayResponse2DTOTranslator.transEbayProductDetailInfoList(responseList));
        } else {
            log.error("{}获取海鹰ebay商品详情信息失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            return CommonResponse.success(new ArrayList<>());
        }
    }

    @Override
    public CommonResponse<List<HaiYingEbayCategoryTreeDTO>> getEbayCategoryTree(HaiYingEbayCategoryTreeCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingEbayDataApiSve.getCategoryTree(HaiYingEbayCommand2RequestTranslator.transEbayCategoryTree(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingEbayCategoryTreeResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingEbayCategoryTreeResponse.class);
            return CommonResponse.success(HaiYingEbayResponse2DTOTranslator.transEbayCategoryTreeList(responseList));
        } else {
            log.error("{}获取海鹰ebay类目树失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            return CommonResponse.success(new ArrayList<>());
        }
    }

    @Override
    public CommonResponse<List<HaiYingEbayTopCategoryInfoDTO>> getEbayTopCategoryInfo(HaiYingEbayTopCategoryInfoCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingEbayDataApiSve.getTopCategoryInfo(HaiYingEbayCommand2RequestTranslator.transEbayTopCategoryInfo(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingEbayTopCategoryInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingEbayTopCategoryInfoResponse.class);
            return CommonResponse.success(HaiYingEbayResponse2DTOTranslator.transEbayTopCategoryInfoList(responseList));
        } else {
            log.error("{}获取海鹰ebay一级类目信息失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            return CommonResponse.success(new ArrayList<>());
        }
    }

    @Override
    public CommonResponse<PageList<HaiYingEbayCategoryDetailDTO>> getEbayCategoryDetail(HaiYingEbayCategoryDetailCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingEbayDataApiSve.getCategoryDetail(HaiYingEbayCommand2RequestTranslator.transEbayCategoryDetail(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingEbayCategoryDetailResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingEbayCategoryDetailResponse.class);
            return CommonResponse.success(HaiYingEbayResponse2DTOTranslator.transEbayCategoryDetailList(responseList, getResultPageMeta(command.getPageCondition(), apiBaseResult)));
        } else {
            log.error("{}获取海鹰ebay子类目信息失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            return CommonResponse.success(new PageList<>());
        }
    }

    private PageMeta getResultPageMeta(PageCondition pageCondition, HaiYingDataApiBaseResult<String> apiBaseResult) {
        if (apiBaseResult.getTotalSize() > 0) {
            PageMeta pageMeta = new PageMeta();
            if (null != pageCondition) {
                pageMeta.pageNum = pageCondition.pageNum;
            } else {
                pageMeta.pageNum = 1;
            }
            pageMeta.pageSize = apiBaseResult.getSize();
            if (apiBaseResult.getTotalSize() % apiBaseResult.getSize() == 0) {
                pageMeta.pages = apiBaseResult.getTotalSize() / apiBaseResult.getSize();
            } else {
                pageMeta.pages = (apiBaseResult.getTotalSize() / apiBaseResult.getSize()) + 1;
            }
            pageMeta.total = apiBaseResult.getTotalSize();
            return pageMeta;
        }
        return null;
    }

}
