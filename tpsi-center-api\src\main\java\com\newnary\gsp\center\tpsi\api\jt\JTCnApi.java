package com.newnary.gsp.center.tpsi.api.jt;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.gsp.center.tpsi.api.jt.request.cn.BillOrderCnJTCommand;
import com.newnary.gsp.center.tpsi.api.jt.request.cn.CancelOrderCnJTCommand;
import com.newnary.gsp.center.tpsi.api.jt.request.cn.CreateOrderCnJTCommand;
import com.newnary.gsp.center.tpsi.api.jt.request.cn.SheetOrderCnJTCommand;
import com.newnary.gsp.center.tpsi.api.jt.response.cn.JTCnCreateResultResp;
import com.newnary.gsp.center.tpsi.api.jt.response.cn.JTCnPrintSheetResp;
import com.newnary.gsp.center.tpsi.api.jt.response.cn.JTCnQueryOrderBillResp;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @Author: jack
 * @CreateTime: 2023-8-9
 * message: 极兔物流-中国
 */
@RequestMapping("tpsi-center/jt-cn")
public interface JTCnApi {

    @PostMapping("create")
    CommonResponse<JTCnCreateResultResp> create(@RequestBody CreateOrderCnJTCommand command);

    @PostMapping("printSheet")
    CommonResponse<JTCnPrintSheetResp> printSheet(@RequestBody SheetOrderCnJTCommand command);

    @PostMapping("cancelOrder")
    CommonResponse<Void> cancelOrder(@RequestBody CancelOrderCnJTCommand command);

    @PostMapping("queryBill")
    CommonResponse<JTCnQueryOrderBillResp> queryBill(@RequestBody BillOrderCnJTCommand command);
}
