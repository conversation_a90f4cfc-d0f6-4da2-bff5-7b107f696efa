//package com.newnary.gsp.center.tpsi.api.ninjavan;
//
//import com.newnary.api.base.common.CommonResponse;
//import com.newnary.gsp.center.tpsi.api.ninjavan.request.CancelOrderNinJavanCommand;
//import com.newnary.gsp.center.tpsi.api.ninjavan.request.CreateOrderNinJavanCommand;
//import com.newnary.gsp.center.tpsi.api.ninjavan.request.SheetOrderNinJavanCommand;
//import com.newnary.gsp.center.tpsi.api.ninjavan.response.NinJavanDataApiBaseResult;
//import com.newnary.gsp.center.tpsi.api.ninjavan.response.NinJavanOrderResp;
//import com.newnary.gsp.center.tpsi.api.ninjavan.response.NinJavanPrintSheetResp;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//
///**
// * @Author: WangRuTao
// * @CreateTime: 2023-8-9
// * message: 能者物流
// */
//@RequestMapping("tpsi-center/ninjavan")
//public interface NinjavanApi {
//
//    /**
//     * 创建订单
//     * @param command
//     * @return
//     */
//    @PostMapping("createOrder")
//    CommonResponse<NinJavanOrderResp> createOrder(@RequestBody CreateOrderNinJavanCommand command);
//
//    /**
//     * 取消订单
//     * @param command
//     * @return
//     */
//    @PostMapping("doCancel")
//    CommonResponse<Void> doCancel(@RequestBody CancelOrderNinJavanCommand command);
//
//
//    /**
//     * 获取面单
//     * @param command
//     * @return
//     */
//    @PostMapping("printSheet")
//    CommonResponse<NinJavanPrintSheetResp> printSheet(@RequestBody SheetOrderNinJavanCommand command);
//}
