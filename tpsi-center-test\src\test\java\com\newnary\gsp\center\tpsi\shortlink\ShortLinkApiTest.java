package com.newnary.gsp.center.tpsi.shortlink;

import com.alibaba.fastjson.JSONObject;
import com.newnary.gsp.center.tpsi.api.shortlink.request.ShortLinkCommand;
import com.newnary.gsp.center.tpsi.api.shortlink.response.ConvertShortLinkResp;
import com.newnary.gsp.center.tpsi.ctrl.shortlink.ShortLinkImpl;
import com.newnary.test.starter.BaseTestInjectTenant;
import org.junit.Test;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;

/**
 * 小码短链对接
 */
public class ShortLinkApiTest extends BaseTestInjectTenant {

    private static final SimpleDateFormat SIMPLE_TIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    protected String tenantId() {
        return "TENANT3924117688555169382400";
    }

    @Resource
    private ShortLinkImpl shortLink;


    @Test
    public void testConvertShortLink() {
        ShortLinkCommand command = new ShortLinkCommand();
        command.setUrl("https://m.elephantpal.com/#/pages/goods/goodsDetails?saleItemId=PSI123356921856048&code=FN6R1S");
        command.setSourceDomain("https://m.elephantpal.com");
        ConvertShortLinkResp resp = shortLink.convertShortLink(command).mustSuccessOrThrowOriginal();
        System.out.println(JSONObject.toJSONString(resp));
    }



}
