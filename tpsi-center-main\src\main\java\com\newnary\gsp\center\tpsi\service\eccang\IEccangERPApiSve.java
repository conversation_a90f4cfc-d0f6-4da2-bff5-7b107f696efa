package com.newnary.gsp.center.tpsi.service.eccang;

import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderDetailInfo;
import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderDetailItemInfo;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp.*;
import com.newnary.gsp.center.tpsi.infra.model.ApiRequestParams;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.DeliveryOrderItemEcCangApiAssociation;

import java.util.List;
import java.util.Set;

public interface IEccangERPApiSve {

    EcCangApiBaseResult<String> createOrder(DeliveryOrderDetailInfo deliveryOrderDetailInfo, ThirdPartySystem thirdPartySystem, ApiRequestParams apiRequestParams);

    EcCangApiBaseResult<String> getOrderListBySaleOrderCode(ThirdPartySystem thirdPartySystem, String saleOrderCode);

    EcCangApiBaseResult<String> syncProduct(DeliveryOrderDetailItemInfo deliveryOrderDetailItemInfo, ThirdPartySystem thirdPartySystem, ApiRequestParams apiRequestParams, String channlId);

    EcCangApiBaseResult<String> cancelOrder(ThirdPartySystem thirdPartySystem, String saleOrderCode);

    EcCangApiBaseResult<String> syncPurchaseOrders(String orderRefNo, DeliveryOrderItemEcCangApiAssociation item, ThirdPartySystem thirdPartySystem, ApiRequestParams apiRequestParams);

    EcCangApiBaseResult<String> getPurchaseOrders(String orderRefNo, ThirdPartySystem thirdPartySystem);

    EcCangApiBaseResult<String> getPurchaseOrdersNoDecode(String orderRefNo, ThirdPartySystem thirdPartySystem,Integer poStaus,Integer page);

    EcCangApiBaseResult<String> cancelPurchaseOrder(ThirdPartySystem thirdPartySystem, Set<String> poCodes);

    /**
     * 易仓ERP采购入库
     * @param referenceNo 由创建时的ref_no决定
     * @param item 
     * @param thirdPartySystem
     * @return
     */
    EcCangApiBaseResult<String> purchaseOrderInbound(String referenceNo, DeliveryOrderItemEcCangApiAssociation item, ThirdPartySystem thirdPartySystem);

    EcCangApiBaseResult<String> getWarehouseList(ThirdPartySystem thirdPartySystem);

    EcCangApiBaseResult<String> getUserList(ThirdPartySystem thirdPartySystem);

    //获取商品列表
    EcCangApiBaseResult<String> getProductList(ThirdPartySystem thirdPartySystem, EcCangERPGetProductListRequest request);

    //获取单个产品信息
    EcCangApiBaseResult<String> getProductBySku(ThirdPartySystem thirdPartySystem, EcCangERPGetProductBySkuRequest request);

    //获取单个产品信息
    EcCangApiBaseResult<String> getSupplierList(ThirdPartySystem thirdPartySystem, EcCangERPGetSupplierListRequest request);

    EcCangApiBaseResult<String> syncSupplier(ThirdPartySystem thirdPartySystem, EcCangERPSyncSupplierRequest request);

    EcCangApiBaseResult<String> getSupplierProductList(ThirdPartySystem thirdPartySystem, EcCangERPGetSupplierProductRequest request);

    /*
        下面重载的接口用作采购对接
     */
    EcCangApiBaseResult<String> syncProduct(ThirdPartySystem thirdPartySystem, EcCangERPSyncProductRequest request);

    EcCangApiBaseResult<String> syncBatchProduct(ThirdPartySystem thirdPartySystem, List<EcCangERPSyncProductRequest> requestList);

    EcCangApiBaseResult<String> syncPurchaseOrders(ThirdPartySystem thirdPartySystem, EcCangERPSyncPurchaseOrdersRequest request);

    EcCangApiBaseResult<String> purchaseOrderInbound(ThirdPartySystem thirdPartySystem, EcCangERPPurchaseOrderInboundRequest request);

    EcCangApiBaseResult<String> getSupplier(ThirdPartySystem thirdPartySystem);
    /**
     * 入库单创建 & 编辑
     * 备注：https://eccang.yuque.com/gko3h7/sw0gov/nrhysk
     *
     * @param thirdPartySystem
     * @param request
     * @return
     */
    EcCangApiBaseResult<String> syncReceiving(ThirdPartySystem thirdPartySystem, EcCangERPStockinOrderSyncReceivingRequest request);

    /**
     * 查询入库单（分页）
     * 备注：https://eccang.yuque.com/gko3h7/sw0gov/ssbwc6
     *
     * @param thirdPartySystem
     * @param request
     * @return
     */
    EcCangApiBaseResult<String> getReceiving(ThirdPartySystem thirdPartySystem, EcCangERPStockinOrderGetReceivingRequest request);

    /**
     * 入库单收货
     *
     * @param thirdPartySystem
     * @param request
     * @return
     */
    EcCangApiBaseResult<String> purchaseOrderReceiving(ThirdPartySystem thirdPartySystem, EcCangERPPurchaseOrderReceivingRequest request);

    //创建和绑定供应商
    EcCangApiBaseResult<String> syncSupplierProduct(ThirdPartySystem thirdPartySystem, EcCangERPSyncSupplierProductRequest request);

    //获取仓库库位
    EcCangApiBaseResult<String> getWarehouseLocation(ThirdPartySystem thirdPartySystem, EcCangERPGetWarehouseLocationRequest request);

    //审核采购单
    EcCangApiBaseResult<String> verifyPurchase(ThirdPartySystem thirdPartySystem, EcCangERPPurchaseOrderVerifyPurchaseRequest verifyPurchaseRequest);

    //强制完成入库单
    EcCangApiBaseResult<String> syncConfirmReceiving(ThirdPartySystem thirdPartySystem, EcCangERPPurchaseOrderSyncConfirmReceivingRequest request);

    EcCangApiBaseResult<String> syncPurchaseTrackingNote(ThirdPartySystem thirdPartySystem, EcCangERPPurchaseOrderSyncPurchaseTrackingNoteRequest request);
}
