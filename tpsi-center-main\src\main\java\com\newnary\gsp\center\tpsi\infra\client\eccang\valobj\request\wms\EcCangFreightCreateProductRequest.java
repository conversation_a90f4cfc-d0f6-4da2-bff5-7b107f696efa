package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.wms;

import java.util.List;

public class EcCangFreightCreateProductRequest {

    public String order_code;

    public Integer warehouse_id;

    public List<ProductData> product_data;

    public static class ProductData {
        public Integer pid;
        public String child_code;
        public String package_code;
        public String product_standard;
        public String product_img_url;
        public Integer product_count;
        public String product_category;
        public String product_name_cn;
        public String product_name_en;
        public Double product_declared;
        public String old_order_code;
        public String old_child_code;
    }
}
