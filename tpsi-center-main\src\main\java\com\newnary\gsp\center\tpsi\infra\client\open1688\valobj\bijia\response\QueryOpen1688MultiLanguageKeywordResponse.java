package com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.bijia.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

// 多语言关键词搜索响应信息
@Data
public class QueryOpen1688MultiLanguageKeywordResponse {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 状态码
     */
    private String code;

    /**
     * 提示
     */
    private String message;

    /**
     * 响应体
     */
    private KeywordResult result;

    @Getter
    @Setter
    public static class KeywordResult{
        /**
         * 总条数
         */
        private Integer totalRecords;

        /**
         * 总页码
         */
        private Integer totalPage;

        /**
         * 分页大小
         */
        private Integer pageSize;

        /**
         * 当前页数
         */
        private Integer currentPage;

        private List<KeywordData> data;

    }

    @Getter
    @Setter
    public static class KeywordData {
        /**
         * 图片地址
         */
        private String imageUrl;

        /**
         * 中文标题
         */
        private String subject;

        /**
         * 外文标题
         */
        private String subjectTrans;

        /**
         * 商品id
         */
        private Long offerId;

        /**
         * 是否精选货源
         */
        private Boolean isJxhy;

        /**
         * 价格
         */
        private PriceInfo priceInfo;

        /**
         * 复购率
         */
        private String repurchaseRate;

        /**
         * 30天销量
         */
        private Integer monthSold;

        /**
         * 向1688上报打点数据
         */
        private String traceInfo;

        /**
         * 是否一件代发
         */
        private Boolean isOnePsale;

        /**
         * 商家身份
         */
        private String[] sellerIdentities;

        /**
         * 商品标
         */
        private String[] offerIdentities;

        /**
         * 商品交易评分
         */
        private String tradeScore;

        /**
         * 商品白底图
         */
        private String whiteImage;

        /**
         * 是否有营销信息
         */
        private PromotionModel promotionModel;

        /**
         * 是否有营销
         */
        private Boolean hasPromotion;

        /**
         * 营销类型
         */
        private String promotionType;

        /**
         * 一级类目
         */
        private Long topCategoryId;

        /**
         * 二级类目
         */
        private Long secondCategoryId;

        /**
         * 三级类目
         */
        private Long thirdCategoryId;

        /**
         * 是否专利商品
         */
        private Boolean isPatentProduct;

        /**
         * 商品上架时间
         */
        private String createDate;

        /**
         * 商品修改时间
         */
        private String modifyDate;

        /**
         * 跨境select货盘
         */
        private Boolean isSelect;

    }

    // 价格信息
    @Getter
    @Setter
    public static class PriceInfo {
        /**
         * 批发价
         */
        private String price;

        /**
         * 代发精选货源价
         */
        private String jxhyPrice;

        /**
         * 批发精选货源价
         */
        private String pfJxhyPrice;

        /**
         * 一件代发价
         */
        private String consignPrice;

        /**
         * 营销价
         */
        private String promotionPrice;

    }

    // 营销信息
    @Getter
    @Setter
    public static class PromotionModel {
        /**
         * 是否有营销
         */
        private Boolean hasPromotion;

        /**
         * 营销类型
         */
        private String promotionType;

    }
}
