package com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Data
public class QueryOpen1688OrderDetailsResponse {

    /**
     * 订单基础信息
     */
    private BaseInfo baseInfo;

    /**
     * 订单业务信息
     */
    private OrderBizInfo orderBizInfo;

    @Setter
    @Getter
    public static class OrderBizInfo{

        /**
         * 是否采源宝订单
         */
        private Boolean odsCyd;

        /**
         * 诚e赊支付详情，只有使用诚e赊付款时返回
         */
        private CreditOrderDetail creditOrderDetail;

        @Setter
        @Getter
        public static class CreditOrderDetail {

            /**
             * 订单金额
             */
            private Long payAmount;

            /**
             * 支付时间
             */
            private String createTime;

            /**
             * 状态
             */
            private String status;

            /**
             * 不再建议使用
             */
            private String gracePeriodEndTime;

            /**
             * 状态描述
             */
            private String statusStr;

            /**
             * 应还金额
             */
            private Long restRepayAmount;

            /**
             * 最晚还款时间
             */
            private String lastRepayTime;

            /**
             * 还款来源 KJPAY-跨境宝还款 OWN_FUNDS-自有资金还款 INSTALLMENT_REPAY-分期、贷款付还款
             */
            private String repaySource;

        }

        /**
         * 预订单信息
         */
        private PreOrderInfo preOrderInfo;

        @Setter
        @Getter
        public static class PreOrderInfo {

            /**
             * 创建预订单时传入的市场名
             */
            private String marketName;

            /**
             * 预订单是否为当前查询的通过当前查询的ERP创建
             */
            private Boolean createPreOrderApp;
        }

        /**
         * 账期交易订单的到账时间
         */
        private String accountPeriodTime;

        /**
         * 为true，表示下单时选择了诚e赊交易方式。注意不等同于“诚e赊支付”，支付时有可能是支付宝付款，具体支付方式查询tradeTerms.payWay
         */
        private Boolean creditOrder;
    }


    /**
     * 交易条款
     */
    private List<TradeTerm> tradeTerms;

    @Getter
    @Setter
    public static class TradeTerm{

        /**
         * 支付状态。国际站：WAIT_PAY(未支付),PAYER_PAID(已完成支付),PART_SUCCESS(部分支付成功),
         * PAY_SUCCESS(支付成功),CLOSED(风控关闭),CANCELLED(支付撤销),SUCCESS(成功),FAIL(失败)。
         * 1688:1(未付款);2(已付款);4(全额退款);6(卖家有收到钱，回款完成) ;7(未创建外部支付单);8 (付款前取消) ; 9(正在支付中);12(账期支付,待到账)
         */
        private String payStatus;

        /**
         * 完成阶段支付时间
         */
        private String payTime;

        /**
         * 支付方式。 国际站：ECL(融资支付),CC(信用卡),TT(线下TT),ACH(echecking支付)。
         * 1688:1-支付宝,2-网商银行信任付,3-诚e赊,4-银行转账,5-赊销宝,6-电子承兑票据,7-账期支付,8-合并支付渠道,9-无打款,10-零售通赊购,13-支付平台,12-声明付款
         */
        private String payWay;

        /**
         * 付款额
         */
        private BigDecimal phasAmount;

        /**
         * 阶段单id
         */
        private Long phase;

        /**
         * 阶段条件，1688无此内容
         */
        private String phaseCondition;

        /**
         * 阶段时间，1688无此内容
         */
        private String phaseDate;

        /**
         * 是否银行卡支付
         */
        private Boolean cardPay;

        /**
         * 是否快捷支付
         */
        private Boolean expressPay;

        /**
         * 支付方式
         */
        private String payWayDesc;
    }

    /**
     * 商品条目信息
     */
    private List<ProductItem> productItems;

    @Setter
    @Getter
    public static class ProductItem {

        /**
         * 指定单品货号，国际站无需关注。该字段不一定有值，仅仅在下单时才会把货号记录(如果卖家设置了单品货号的话)。别的订单类型的货号只能通过商品接口去获取。请注意：通过商品接口获取时的货号和下单时的货号可能不一致，因为下单完成后卖家可能修改商品信息，改变了货号。
         */
        private String cargoNumber;

        /**
         * 描述,1688无此信息
         */
        private String description;

        /**
         * 实付金额，单位为元
         */
        private BigDecimal itemAmount;

        /**
         * 商品名称
         */
        private String name;

        /**
         * 原始单价，以元为单位
         */
        private BigDecimal price;

        /**
         * 产品ID（非在线产品为空）
         */
        private Long productID;

        /**
         * 商品图片url
         */
        private List<String> productImgUrl;

        /**
         *
         * 产品快照url，交易订单产生时会自动记录下当时的商品快照，供后续纠纷时参考
         */
        private String productSnapshotUrl;

        /**
         * 以unit为单位的数量，例如多少个、多少件、多少箱、多少吨
         */
        private BigDecimal quantity;

        /**
         * 退款金额，单位为元
         */
        private BigDecimal refund;

        /**
         * skuID
         */
        private Long skuID;

        /**
         * 排序字段，商品列表按此字段进行排序，从0开始，1688不提供
         */
        private Integer sort;

        /**
         * 子订单状态
         */
        private String status;

        /**
         * 子订单号，或商品明细条目ID
         */
        private Long subItemID;

        /**
         * 类型，国际站使用，供卖家标注商品所属类型
         */
        private String type;

        /**
         * 售卖单位 例如：个、件、箱、吨
         */
        private String unit;

        /**
         * 重量 按重量单位计算的重量，例如：100
         */
        private String weight;

        /**
         * 重量单位 例如：g，kg，t
         */
        private String weightUnit;

        /**
         * 保障条款，此字段仅针对1688
         */
        private List<GuaranteesTerm> guaranteesTerms;

        @Setter
        @Getter
        public static class GuaranteesTerm {

            /**
             * 保障条款
             */
            private String assuranceInfo;

            /**
             * 保障方式。国际站：TA(信保)
             */
            private String assuranceType;

            /**
             * 质量保证类型。国际站：pre_shipment(发货前),post_delivery(发货后)
             */
            private String qualityAssuranceType;

        }

        /**
         * 指定商品货号，该字段不一定有值，在下单时才会把货号记录。别的订单类型的货号只能通过商品接口去获取。
         * 请注意：通过商品接口获取时的货号和下单时的货号可能不一致，因为下单完成后卖家可能修改商品信息，改变了货号。该字段和cargoNUmber的区别是：该字段是定义在商品级别上的货号，cargoNUmber是定义在单品级别的货号
         */
        private String productCargoNumber;

        /**
         *  skuInfos
         */
        private List<SkuInfo> skuInfos;

        @Setter
        @Getter
        public static class SkuInfo {

            /**
             * 属性名
             */
            private String name;

            /**
             * 属性值
             */
            private String value;

        }

        /**
         * 订单明细涨价或降价的金额
         */
        private Long entryDiscount;

        /**
         * 订单销售属性ID
         */
        private String specId;

        /**
         * 以unit为单位的quantity精度系数，值为10的幂次，例如:quantityFactor=1000,unit=吨，那么quantity的最小精度为0.001吨
         */
        private BigDecimal quantityFactor;

        /**
         * 子订单状态描述
         */
        private String statusStr;

        /**
         * WAIT_SELLER_AGREE 等待卖家同意 REFUND_SUCCESS 退款成功 REFUND_CLOSED 退款关闭 WAIT_BUYER_MODIFY 待买家修改 WAIT_BUYER_SEND 等待买家退货 WAIT_SELLER_RECEIVE 等待卖家确认收货
         */
        private String refundStatus;

        /**
         * 关闭原因
         */
        private String closeReason;

        /**
         * 1 未发货 2 已发货 3 已收货 4 已经退货 5 部分发货 8 还未创建物流订单
         */
        private Integer logisticsStatus;

        /**
         * 创建时间
         */
        private String gmtCreate;

        /**
         * 修改时间
         */
        private String gmtModified;

        /**
         * 明细完成时间
         */
        private String gmtCompleted;

        /**
         * 库存超时时间，格式为“yyyy-MM-dd HH:mm:ss”
         */
        private String gmtPayExpireTime;

        /**
         * 售中退款单号
         */
        private String refundId;

        /**
         * 子订单号，或商品明细条目ID(字符串类型，由于Long类型的ID可能在JS和PHP中处理有问题，所以可以用字符串类型来处理)
         */
        private String subItemIDString;

        /**
         * 售后退款单号
         */
        private String refundIdForAs;


    }


    /**
     * 国内物流
     */
    private NativeLogistics nativeLogistics;

    @Getter
    @Setter
    public static class NativeLogistics {

        /**
         * 详细地址
         */
        private String address;

        /**
         * 县，区
         */
        private String area;

        /**
         * 省市区编码
         */
        private String areaCode;

        /**
         * 城市
         */
        private String city;

        /**
         * 联系人姓名
         */
        private String contactPerson;

        /**
         * 传真
         */
        private String fax;

        /**
         * 手机
         */
        private String mobile;

        /**
         * 省份
         */
        private String province;

        /**
         * 电话
         */
        private String telephone;

        /**
         * 邮编
         */
        private String zip;

        /**
         * 运单明细
         */
        private List<LogisticsItem> logisticsItems;

        @Setter
        @Getter
        public static class LogisticsItem{

            /**
             * 发货时间
             */
            private String deliveredTime;

            /**
             * 物流编号
             */
            private String logisticsCode;

            /**
             * SELF_SEND_GOODS("0")自行发货，在线发货ONLINE_SEND_GOODS("1"，不需要物流的发货 NO_LOGISTICS_SEND_GOODS("2")
             */
            private String type;

            /**
             * 主键id
             */
            private Long id;

            /**
             * 状态
             */
            private String status;

            /**
             * 修改时间
             */
            private String gmtModified;

            /**
             * 创建时间
             */
            private String gmtCreate;

            /**
             * 运费(单位为元)
             */
            private BigDecimal carriage;

            /**
             * 发货省
             */
            private String fromProvince;

            /**
             * 发货市
             */
            private String fromCity;

            /**
             * 发货区
             */
            private String fromArea;

            /**
             * 发货街道地址
             */
            private String fromAddress;

            /**
             * 发货联系电话
             */
            private String fromPhone;

            /**
             * 发货联系手机
             */
            private String fromMobile;

            /**
             * 发货地址邮编
             */
            private String fromPost;

            /**
             * 物流公司Id
             */
            private Long logisticsCompanyId;

            /**
             * 物流公司编号
             */
            private String logisticsCompanyNo;

            /**
             * 物流公司名称
             */
            private String logisticsCompanyName;

            /**
             * 物流公司运单号
             */
            private String logisticsBillNo;

            /**
             * 商品明细条目id，如有多个以,分隔
             */
            private String subItemIds;

            /**
             * 收货省
             */
            private String toProvince;

            /**
             * 收货市
             */
            private String toCity;

            /**
             * 收货区
             */
            private String toArea;

            /**
             * 收货街道地址
             */
            private String toAddress;

            /**
             * 收货联系电话
             */
            private String toPhone;

            /**
             * 收货联系手机
             */
            private String toMobile;

            /**
             * 收货地址邮编
             */
            private String toPost;

            /**
             * 物流姓名
             */
            private String noLogisticsName;

            /**
             * 联系方式
             */
            private String noLogisticsTel;

            /**
             * 无需物流业务单号
             */
            private String noLogisticsBillNo;

            /**
             * 无需物流类别,noLogisticsCondition=1， 表示其他第三方物流、小型物充商、车队等, noLogisticsCondition=2 表示补运费、差价, noLogisticsCondition=3 表示卖家配送, noLogisticsCondition=4 表示买家自提 noLogisticsCondition=5 表示其他原因
             */
            private String noLogisticsCondition;
        }

        /**
         * 镇，街道地址码
         */
        private String townCode;

        /**
         * 镇，街道
         */
        private String town;

    }


    /**
     * 发票信息
     */
    private OrderInvoiceInfo orderInvoiceInfo;

    @Getter
    @Setter
    public static class OrderInvoiceInfo {

        /**
         * 发票公司名称(即发票抬头-title)
         */
        private String invoiceCompanyName;

        /**
         * 发票类型. 0：普通发票，1:增值税发票，9未知类型
         */
        private Integer invoiceType;

        /**
         * 本地发票号
         */
        private Long localInvoiceId;

        /**
         * 订单Id
         */
        private Long orderId;

        /**
         * (收件人)址区域编码
         */
        private String receiveCode;

        /**
         * (收件人) 省市区编码对应的文案(增值税发票信息)
         */
        private String receiveCodeText;

        /**
         * （收件者）发票收货人手机
         */
        private String receiveMobile;

        /**
         * （收件者）发票收货人
         */
        private String receiveName;

        /**
         * （收件者）发票收货人电话
         */
        private String receivePhone;

        /**
         * （收件者）发票收货地址邮编
         */
        private String receivePost;

        /**
         * (收件人) 街道地址(增值税发票信息)
         */
        private String receiveStreet;

        /**
         * (公司)银行账号
         */
        private String registerAccountId;

        /**
         * (公司)开户银行
         */
        private String registerBank;

        /**
         * (注册)省市区编码
         */
        private String registerCode;

        /**
         * (注册)省市区文本
         */
        private String registerCodeText;

        /**
         * （公司）注册电话
         */
        private String registerPhone;

        /**
         * (注册)街道地址
         */
        private String registerStreet;

        /**
         * 纳税人识别号
         */
        private String taxpayerIdentify;
    }

    /**
     * 保障条款
     */
    private GuaranteesTerm guaranteesTerms;

    @Getter
    @Setter
    public static class GuaranteesTerm {

        /**
         * 保障条款
         */
        private String assuranceInfo;

        /**
         * 保障方式。国际站：TA(信保)
         */
        private String assuranceType;

        /**
         * 质量保证类型。国际站：pre_shipment(发货前),post_delivery(发货后)
         */
        private String qualityAssuranceType;
    }

    /**
     * 订单评价信息
     */
    private OrderRateInfo orderRateInfo;

    @Getter
    @Setter
    public static class OrderRateInfo {

        /**
         * 买家评价状态(4:已评论,5:未评论,6;不需要评论)
         */
        private Integer buyerRateStatus;

        /**
         * 卖家评价状态(4:已评论,5:未评论,6;不需要评论)
         */
        private Integer sellerRateStatus;

        /**
         * 卖家給买家的评价
         */
        private List<Rate> buyerRateList;

        @Getter
        @Setter
        public static class Rate {

            /**
             * 评价星级
             */
            private Integer starLevel;

            /**
             * 评价详情
             */
            private String content;

            /**
             * 收到评价的用户昵称
             */
            private String receiverNick;

            /**
             * 发送评价的用户昵称
             */
            private String posterNick;

            /**
             * 评价上线时间
             */
            private String publishTime;
        }

        /**
         * 买家給卖家的评价
         */
        private List<Rate> sellerRateList;
    }

    /**
     * 跨境地址扩展信息
     */
    private OverseasExtraAddress overseasExtraAddress;

    @Getter
    @Setter
    public static class OverseasExtraAddress {

        /**
         * 路线名称
         */
        private String channelName;

        /**
         * 路线id
         */
        private String channelId;

        /**
         * 货代公司id
         */
        private String shippingCompanyId;

        /**
         * 货代公司名称
         */
        private String shippingCompanyName;

        /**
         * 国家code
         */
        private String countryCode;

        /**
         * 国家
         */
        private String country;

        /**
         * 买家邮箱
         */
        private String email;
    }

    /**
     * 跨境报关信息
     */
    private Custom customs;

    @Getter
    @Setter
    public static class Custom {

        /**
         * id
         */
        private Long id;

        /**
         * 创建时间
         */
        private String gmtCreate;

        /**
         * 修改时间
         */
        private String gmtModified;

        /**
         * 买家id
         */
        private Long buyerId;

        /**
         * 主订单id
         */
        private String orderId;

        /**
         * 业务数据类型,默认1：报关单
         */
        private String type;

        /**
         * 报关信息列表
         */
        private List<Attribute> attributes;

        @Getter
        @Setter
        public static class Attribute {

            /**
             * sku标识
             */
            private String sku;

            /**
             * 中文名称
             */
            private String cName;

            /**
             * 英文名称
             */
            private String enName;

            /**
             * 申报价值
             */
            private String amount;

            /**
             * 数量
             */
            private String quantity;

            /**
             * 重量（kg）
             */
            private String weight;

            /**
             * 报关币种
             */
            private String currency;
        }

    }

    /**
     * 采购单详情列表，为大企业采购订单独有域。
     */
    private List<Quote> quoteList;

    @Getter
    @Setter
    public static class Quote {

        /**
         * 供应单项的名称
         */
        private String productQuoteName;

        /**
         * 价格，单位：元
         */
        private BigDecimal price;

        /**
         * 购买数量
         */
        private Integer count;
    }

    /**
     * 订单扩展属性
     */
    private List<ExtAttribute> extAttributes;

    @Getter
    @Setter
    public static class ExtAttribute {

        /**
         * 键
         */
        private String key;

        /**
         * 值
         */
        private String value;

        /**
         * 描述
         */
        private String description;
    }

    @Setter
    @Getter
    public static class BaseInfo{

        /**
         * 完全发货时间
         */
        private String allDeliveredTime;

        /**
         * 卖家诚信等级
         */
        private String sellerCreditLevel;

        /**
         * 付款时间，如果有多次付款，这里返回的是首次付款时间
         */
        private String payTime;

        /**
         * 折扣信息，单位分
         */
        private Long discount;

        /**
         * 外部支付交易Id
         */
        private String alipayTradeId;

        /**
         * 产品总金额(该订单产品明细表中的产品金额的和)，单位元
         */
        private BigDecimal sumProductPayment;

        /**
         * 买家留言，不超过500字
         */
        private String buyerFeedback;

        /**
         * 4.0交易流程模板code
         */
        private String flowTemplateCode;

        /**
         * 是否自主订单（邀约订单）
         */
        private Boolean sellerOrder;

        /**
         * 买家loginId，旺旺Id
         */
        private String buyerLoginId;

        /**
         * 修改时间
         */
        private String modifyTime;

        /**
         * 买家子账号
         */
        private String subBuyerLoginId;

        /**
         * 交易id
         */
        private Long id;

        /**
         * 关闭原因。buyerCancel:买家取消订单，sellerGoodsLack:卖家库存不足，other:其它
         */
        private String closeReason;

        /**
         * 买家联系人
         */
        private BuyerContact buyerContact;

        /**
         * 卖家支付宝id
         */
        private String sellerAlipayId;

        /**
         * 完成时间
         */
        private String completeTime;

        /**
         * 卖家oginId，旺旺Id
         */
        private String sellerLoginId;

        /**
         * 买家主账号id
         */
        private String buyerID;

        /**
         * 关闭订单操作类型。CLOSE_TRADE_BY_SELLER:卖家关闭交易,CLOSE_TRADE_BY_BOPS:BOPS后台关闭交易,CLOSE_TRADE_BY_SYSTEM:系统（超时）关闭交易,CLOSE_TRADE_BY_BUYER:买家关闭交易,CLOSE_TRADE_BY_CREADIT:诚信保障投诉关闭
         */
        private String closeOperateType;

        /**
         * 应付款总金额，totalAmount = ∑itemAmount + shippingFee，单位为元
         */
        private BigDecimal totalAmount;

        /**
         * 卖家主账号id
         */
        private String sellerID;

        /**
         * 运费，单位为元
         */
        private BigDecimal shippingFee;

        /**
         * 买家数字id
         */
        private Long buyerUserId;

        /**
         * 买家备忘信息
         */
        private String buyerMemo;

        /**
         * 退款金额，单位为元
         */
        private String refund;

        /**
         * 交易状态，waitbuyerpay:等待买家付款;waitsellersend:等待卖家发货;waitbuyerreceive:等待买家收货;confirm_goods:已收货;success:交易成功;cancel:交易取消;terminated:交易终止;未枚举:其他状态
         */
        private String status;

        /**
         * 退款金额
         */
        private Long refundPayment;

        /**
         * 卖家联系人信息
         */
        private SellerContact sellerContact;

        @Setter
        @Getter
        public static class SellerContact {
            /**
             * 红包金额，实付金额（totalAmount）已经计算过红包金额
             */
            private BigDecimal couponFee;

            /**
             * 买家备忘标志
             */
            private String buyerRemarkIcon;
        }

        /**
         * 红包金额，实付金额（totalAmount）已经计算过红包金额
         */
        private BigDecimal couponFee;

        /**
         * 买家备忘标志
         */
        private String buyerRemarkIcon;

        /**
         * 收件人信息
         */
        private ReceiverInfo receiverInfo;

        /**
         * 订单的售中退款状态，等待卖家同意：waitselleragree ，待买家修改：waitbuyermodify，等待买家退货：waitbuyersend，等待卖家确认收货：waitsellerreceive，退款成功：refundsuccess，退款失败：refundclose
         */
        private String refundStatus;

        /**
         * 备注，1688指下单时的备注
         */
        private String remark;

        /**
         * 预订单ID
         */
        private Long preOrderId;

        /**
         * 确认时间
         */
        private String confirmedTime;

        /**
         * 关闭订单备注
         */
        private String closeRemark;

        /**
         * 1:担保交易 2:预存款交易 3:ETC境外收单交易 4:即时到帐交易 5:保障金安全交易 6:统一交易流程 7:分阶段付款 8.货到付款交易 9.信用凭证支付交易 10.账期支付交易，50060 交易4.0
         */
        private String tradeType;

        /**
         * 收货时间，这里返回的是完全收货时间
         */
        private String receivingTime;

        /**
         * 分阶段法务协议地址
         */
        private String stepAgreementPath;

        /**
         * 交易id(字符串格式)
         */
        private String idOfStr;

        /**
         * 订单的售后退款状态
         */
        private String refundStatusForAs;

        /**
         * 是否一次性付款
         */
        private String stepPayAll;

        /**
         * 卖家数字id
         */
        private String sellerUserId;

        /**
         * [交易3.0]分阶段交易，分阶段订单list
         */
        private List<StepOrder> stepOrderList;

        @Getter
        @Setter
        public static class StepOrder{

            /**
             * 阶段id
             */
            private Long stepOrderId;

            /**
             * waitactivate 未开始（待激活） waitsellerpush 等待卖家推进 success 本阶段完成 settlebill 分账 cancel 本阶段终止 inactiveandcancel 本阶段未开始便终止 waitbuyerpay 等待买家付款
             * waitsellersend 等待卖家发货 waitbuyerreceive 等待买家确认收货 waitselleract 等待卖家XX操作 waitbuyerconfirmaction 等待买家确认XX操作
             */
            private String stepOrderStatus;

            /**
             * 1 未冻结/未付款 2 已冻结/已付款 4 已退款 6 已转交易 8 交易未付款被关闭
             */
            private Integer stepPayStatus;

            /**
             * 阶段序列：1、2、3...
             */
            private Integer stepNo;

            /**
             * 是否最后一个阶段
             */
            private Boolean lastStep;

            /**
             * 是否已打款给卖家
             */
            private Boolean hasDisbursed;

            /**
             * 创建时需要付款的金额，不含运费
             */
            private BigDecimal payFee;

            /**
             * 应付款（含运费）= 单价×数量-单品优惠-店铺优惠+运费+修改的金额（除运费外，均指分摊后的金额）
             */
            private BigDecimal actualPayFee;

            /**
             * 本阶段分摊的店铺优惠
             */
            private BigDecimal discountFee;

            /**
             * 本阶段分摊的单品优惠
             */
            private BigDecimal itemDiscountFee;

            /**
             * 本阶段分摊的单价
             */
            private BigDecimal price;

            /**
             * 购买数量
             */
            private Long amount;

            /**
             * 运费
             */
            private BigDecimal postFee;

            /**
             * 修改价格修改的金额
             */
            private BigDecimal adjustFee;

            /**
             * 创建时间
             */
            private String gmtCreate;

            /**
             * 修改时间
             */
            private String gmtModified;

            /**
             * 开始时间
             */
            private String enterTime;

            /**
             * 付款时间
             */
            private String payTime;

            /**
             * 卖家操作时间
             */
            private String sellerActionTime;

            /**
             * 本阶段结束时间
             */
            private String endTime;

            /**
             * 卖家操作留言路径
             */
            private String messagePath;

            /**
             * 卖家上传图片凭据路径
             */
            private String picturePath;

            /**
             * 卖家操作留言
             */
            private String message;

            /**
             * 使用的模板id
             */
            private String templateId;

            /**
             * 当前步骤的名称
             */
            private String stepName;

            /**
             * 卖家操作名称
             */
            private String sellerActionName;

            /**
             * 买家不付款的超时时间(秒)
             */
            private Boolean buyerPayTimeout;

            /**
             * 买家不确认的超时时间
             */
            private Boolean buyerConfirmTimeout;

            /**
             * 是否需要物流
             */
            private Boolean needLogistics;

            /**
             * 是否需要卖家操作和买家确认
             */
            private Boolean needSellerAction;

            /**
             * 阶段结束是否打款
             */
            private Boolean transferAfterConfirm;

            /**
             * 是否需要卖家推进
             */
            private Boolean needSellerCallNext;

            /**
             * 是否允许即时到帐
             */
            private Boolean instantPay;
        }

        /**
         * 买家支付宝id
         */
        private String buyerAlipayId;

        /**
         * 创建时间
         */
        private String createTime;

        /**
         * 业务类型。国际站：ta(信保),wholesale(在线批发)。 中文站：普通订单类型 = "cn"; 大额批发订单类型 = "ws"; 普通拿样订单类型 = "yp"; 一分钱拿样订单类型 = "yf"; 倒批(限时折扣)订单类型 = "fs"; 加工定制订单类型 = "cz";
         * 协议采购订单类型 = "ag"; 伙拼订单类型 = "hp"; 供销订单类型 = "supply"; 淘工厂订单 = "factory"; 快订下单 = "quick"; 享拼订单 = "xiangpin"; 当面付 = "f2f"; 存样服务 = "cyfw"; 代销订单 = "sp"; 微供订单 = "wg";
         * 零售通 = "lst";跨境='cb';分销='distribution';采源宝='cab';加工定制="manufact"
         */
        private String businessType;

        /**
         * 是否海外代发订单，是：true
         */
        private Boolean overSeaOrder;

        /**
         * 退款单ID
         */
        private String refundId;

        /**
         * 下单时指定的交易方式
         */
        private String tradeTypeDesc;

        /**
         * 支付渠道名称列表。一笔订单可能存在多种支付渠道。枚举值：支付宝,网商银行信任付,诚e赊,对公转账,赊销宝,账期支付,合并支付渠道,支付平台,声明付款,网商电子银行承兑汇票,银行转账,跨境宝,红包,其它
         */
        private List<String> payChannelList;

        /**
         * 下单时指定的交易方式tradeType
         */
        private String tradeTypeCode;

        /**
         * 支付超时时间，定长情况时单位：秒，目前都是定长
         */
        private Long payTimeout;

        /**
         * 支付超时TYPE，0：定长，1：固定时间
         */
        private String payTimeoutType;

        /**
         * 支付渠道code，payChannelCodeList的中文示意参见payChannelList
         */
        private List<String> payChannelCodeList;

        @Getter
        @Setter
        public static class BuyerContact {

            /**
             * 联系电话
             */
            private String phone;

            /**
             * 传真
             */
            private String fax;

            /**
             * 邮箱
             */
            private String email;

            /**
             * 联系人在平台的IM账号
             */
            private String imInPlatform;

            /**
             * 联系人名称
             */
            private String name;

            /**
             * 联系人手机号
             */
            private String mobile;

            /**
             * 公司名称
             */
            private String companyName;

        }

        @Getter
        @Setter
        public static class ReceiverInfo {

            /**
             * 收件人
             */
            private String toFullName;

            /**
             * 收货人地址区域编码
             */
            private String toDivisionCode;

            /**
             * 收件人移动电话
             */
            private String toMobile;

            /**
             * 收件人电话
             */
            private String toPhone;

            /**
             * 邮编
             */
            private String toPost;

            /**
             * 收货人街道或镇区域编码，可能为空
             */
            private String toTownCode;

            /**
             * 收货地址
             */
            private String toArea;
        }
    }
}
