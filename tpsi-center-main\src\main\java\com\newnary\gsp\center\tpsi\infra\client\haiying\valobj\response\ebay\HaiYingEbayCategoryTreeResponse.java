package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.ebay;

import lombok.Data;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingEbayCategoryTreeResponse {

    /**
     * 类目id
     */
    private String cid;

    /**
     * 类目名称
     */
    private String cname;

    /**
     * 类目层级
     */
    private String level;

    /**
     * 是否叶子类目
     * (0:否   1:是)
     */
    private String is_leaf;

    /**
     * 1级类目id
     */
    private String p_l1_id;

    /**
     * 1级类目名称
     */
    private String p_l1_name;

    /**
     * 2级类目id
     */
    private String p_l2_id;

    /**
     * 2级类目名称
     */
    private String p_l2_name;

    /**
     * 3级类目id
     */
    private String p_l3_id;

    /**
     * 3级类目名称
     */
    private String p_l3_name;

    /**
     * 4级类目id
     */
    private String p_l4_id;

    /**
     * 4级类目名称
     */
    private String p_l4_name;

    /**
     * 5级类目id
     */
    private String p_l5_id;

    /**
     * 5级类目名称
     */
    private String p_l5_name;

    /*
     * 6级类目id
     */
    private String p_l6_id;

    /**
     * 6级类目名称
     */
    private String p_l6_name;

    /**
     * 7级类目id
     */
    private String p_l7_id;

    /**
     * 7级类目名称
     */
    private String p_l7_name;

}
