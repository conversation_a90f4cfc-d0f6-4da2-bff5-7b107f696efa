package com.newnary.gsp.center.tpsi.open1688;

import com.alibaba.fastjson.JSON;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.tpsi.api.open1688.response.QueryOpen1688ProductByImageUrlResult;
import com.newnary.gsp.center.tpsi.app.job.Open1688JobManager;
import com.newnary.gsp.center.tpsi.api.open1688.enums.CountryEnum;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.bijia.request.QueryOpen1688MultiLanguageProductDetailRequest;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.bijia.response.QueryOpen1688MultiLanguageProductDetailResponse;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.comon.response.Category1688;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.bijia.request.QueryOpen1688MultiLanguageKeywordRequest;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.bijia.response.QueryOpen1688MultiLanguageKeywordResponse;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.request.QueryOpen1688JXHYProductRequest;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.request.QueryOpen1688ProductByImageUrlRequest;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.request.QueryProductRequest;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.request.SearchByKeywordsRequest;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.response.*;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.service.open1688.Open1688Service;
import com.newnary.test.starter.BaseTestInjectTenant;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class Open1688ApiTest extends BaseTestInjectTenant {
    @Override
    protected String tenantId() {
        return "TENANT3924117688555169382400";
    }

    @Resource
    Open1688Service open1688ServiceImpl;

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Resource
    private Open1688JobManager open1688JobManager;

    @Test
    public void testGetCategoryById() {
        Category1688 category1688 = open1688ServiceImpl.getCategoryById(loadSystem("TEST1688FX0001"),"0");
        System.out.println(JSON.toJSONString(category1688));
    }

    @Test
    public void testSearchKeywords() {
        SearchByKeywordsRequest searchByKeywordsRequest = new SearchByKeywordsRequest();
        searchByKeywordsRequest.setScenario("all");
        SearchByKeywordsRequest.Param param = new SearchByKeywordsRequest.Param();
        param.setFilter("[jxhy]");
        searchByKeywordsRequest.setParam(param);
        SearchByKeywordsResponse response = open1688ServiceImpl.searchByKeywords(loadSystem("TEST1688FX0001"),searchByKeywordsRequest);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testQueryProduct() {
        String[] spuArr = {"************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************"};
        QueryProductRequest queryProductRequest = new QueryProductRequest();
        for (String spuId : spuArr) {
            queryProductRequest.setOfferId(spuId);
            QueryProductResponse queryProductResponse = open1688ServiceImpl.queryProductById(loadSystem("TEST1688FX0001"),queryProductRequest);
            System.out.println(JSON.toJSONString(queryProductResponse));
        }

    }

    //图搜
    @Test
    public void testQueryOpen1688ProductByImageUrl() {
        QueryOpen1688ProductByImageUrlRequest queryOpen1688ProductByImageUrlRequest = new QueryOpen1688ProductByImageUrlRequest();
        queryOpen1688ProductByImageUrlRequest.setScenario("all");
        QueryOpen1688ProductByImageUrlRequest.Param param = new QueryOpen1688ProductByImageUrlRequest.Param();
        param.setImageUrl("https://t14.baidu.com/it/u=2703698020,302029968&fm=224&app=112&f=JPEGw=500&h=500");
        queryOpen1688ProductByImageUrlRequest.setParam(param);
        QueryOpen1688ProductByImageUrlResponse response = open1688ServiceImpl.queryOpen1688ProductByImageUrl(loadSystem("TEST1688FX0001"), queryOpen1688ProductByImageUrlRequest);
        //System.out.println(JSON.toJSONString(response));

        com.newnary.gsp.center.tpsi.api.open1688.response.QueryOpen1688ProductByImageUrlResponse queryOpen1688ProductByImageUrlResponse = JSON.parseObject(JSON.toJSONString(response), com.newnary.gsp.center.tpsi.api.open1688.response.QueryOpen1688ProductByImageUrlResponse.class);
        com.newnary.gsp.center.tpsi.api.open1688.response.QueryOpen1688ProductByImageUrlResponse queryOpen1688ProductByImageUrlResponse1 = dealWithPrice(queryOpen1688ProductByImageUrlResponse);
    }

    private com.newnary.gsp.center.tpsi.api.open1688.response.QueryOpen1688ProductByImageUrlResponse dealWithPrice(com.newnary.gsp.center.tpsi.api.open1688.response.QueryOpen1688ProductByImageUrlResponse response){
        List<QueryOpen1688ProductByImageUrlResult> resultList = response.getResult();
        if (CollectionUtils.isNotEmpty(resultList)) {
            for (QueryOpen1688ProductByImageUrlResult result : resultList) {
                QueryOpen1688ProductByImageUrlResult.OfferPrice offerPrice = result.getOfferPrice();
                if (null != offerPrice) {
                    List<QueryOpen1688ProductByImageUrlResult.QuantityPrice> quantityPriceList = offerPrice.getQuantityPrice();

                    if (CollectionUtils.isNotEmpty(quantityPriceList)) {
                        List<QueryOpen1688ProductByImageUrlResult.PriceRange> startQuantityPriceList = new ArrayList<>();
                        for (QueryOpen1688ProductByImageUrlResult.QuantityPrice quantityPrice : quantityPriceList) {
                            QueryOpen1688ProductByImageUrlResult.PriceRange priceRange = new QueryOpen1688ProductByImageUrlResult.PriceRange();
                            String quantity = quantityPrice.getQuantity();
                            if (quantity.contains("~")){
                                String[] split = quantity.split("~");
                                priceRange.setStartQuantity(Integer.valueOf(split[0]));
                            }else if (quantity.contains("≥")){
                                String[] split = quantity.split("≥");
                                priceRange.setStartQuantity(Integer.valueOf(split[1]));
                            }else {
                                priceRange.setStartQuantity(Integer.valueOf(quantity));
                            }

                            priceRange.setPrice(new BigDecimal(quantityPrice.getValue()));
                            startQuantityPriceList.add(priceRange);
                        }
                        offerPrice.setStartQuantityPrice(startQuantityPriceList);
                    }
                }
            }
        }
        return response;
    }

    @Test
    public void testSyncManager() {
        open1688JobManager.createOpen1688Product("{\"thirdPartySystemId\":\"TEST1688FX0001\",\"pageNum\":19,\"endPageNum\":0,\"pageSize\":50,\"insertNum\":100,\"perGroupCount\":5,\"categoryId\":\"\"}");
    }

    //精选货源商品接口测试
    @Test
    public void testJXHY(){
        ThirdPartySystem thirdPartySystem = loadSystem("TEST1688FX0001");
        QueryOpen1688JXHYProductRequest queryOpen1688JXHYProductRequest = new QueryOpen1688JXHYProductRequest();
        queryOpen1688JXHYProductRequest.setCategoryId(1033223L);
        QueryOpen1688JXHYProductResponse queryOpen1688JXHYProductResponse = open1688ServiceImpl.queryJXHYProduct(thirdPartySystem, queryOpen1688JXHYProductRequest);
        List<QueryOpen1688JXHYProductResponse.Result.QueryResult> resultList = queryOpen1688JXHYProductResponse.getResult().getResultList();
        if (CollectionUtils.isNotEmpty(resultList)) {
            List<Long> collect = resultList.stream().map(QueryOpen1688JXHYProductResponse.Result.QueryResult::getItemId).collect(Collectors.toList());
            QueryJXHYProductDetailResponse queryJXHYProductDetailResponse = open1688ServiceImpl.queryJXHYProductDetail(thirdPartySystem, collect);
            System.out.println(JSON.toJSONString(queryJXHYProductDetailResponse));
        }
    }

    @Test
    public void testCreateJXHY(){
        open1688JobManager.createOpen1688JXHYProduct("{\"thirdPartySystemId\":\"TEST1688FX0001\",\"pageNum\":56,\"endPageNum\":0,\"pageSize\":50,\"insertSize\":100,\"perGroupCount\":20,\"categoryId\":\"\",\"insertPage\":0}");
    }

    public ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }

    @Test
    public void testQueryMultiLanguageKeyWord(){
        QueryOpen1688MultiLanguageKeywordRequest request = new QueryOpen1688MultiLanguageKeywordRequest();
        QueryOpen1688MultiLanguageKeywordRequest.OfferQueryParam offerQueryParam = new QueryOpen1688MultiLanguageKeywordRequest.OfferQueryParam();
        offerQueryParam.setBeginPage(1);
        offerQueryParam.setCountry(CountryEnum.EN.getName());
        offerQueryParam.setKeyword("xperia 1 vi");
        offerQueryParam.setPageSize(1);
        request.setOfferQueryParam(offerQueryParam);
        QueryOpen1688MultiLanguageKeywordResponse response = open1688ServiceImpl.queryMultiLanguageKeyWords(loadSystem("TEST1688FX0002"), request);
        System.out.println(JSON.toJSONString(response));

    }

    @Test
    public void testQueryMultiLanguageProductDetail(){
        QueryOpen1688MultiLanguageProductDetailRequest request = new QueryOpen1688MultiLanguageProductDetailRequest();
        QueryOpen1688MultiLanguageProductDetailRequest.OfferDetailParam offerDetailParam = new QueryOpen1688MultiLanguageProductDetailRequest.OfferDetailParam();
        offerDetailParam.setOfferId("655328202541");
        offerDetailParam.setCountry(CountryEnum.EN.getName());
        request.setOfferDetailParam(offerDetailParam);
        QueryOpen1688MultiLanguageProductDetailResponse response = open1688ServiceImpl.queryMultiLanguageProductDetail(loadSystem("TEST1688FX0002"), request);
        System.out.println(JSON.toJSONString(response));
    }

}
