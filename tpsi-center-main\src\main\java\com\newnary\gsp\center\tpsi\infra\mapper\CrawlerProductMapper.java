package com.newnary.gsp.center.tpsi.infra.mapper;

import com.newnary.gsp.center.tpsi.infra.model.CrawlerProduct;
import com.newnary.gsp.center.tpsi.infra.model.creator.CrawlerProductCreate;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.CrawlerProductPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since Created on 2023-03-17
 **/
@Mapper
public interface CrawlerProductMapper {

    CrawlerProductMapper INSTANCE = Mappers.getMapper(CrawlerProductMapper.class);

    @Mapping(target = "crawlerProductId",source = "crawlerProductId.id")
    CrawlerProductPO model2PO(CrawlerProduct crawlerProduct);

//    CrawlerProductPO model2PO(CrawlerProduct crawlerProduct);
//    @Mapping(target = "crawlerProductId.id",source = "crawlerProductId")
//    CrawlerProductCreate model2PO(CrawlerProductPO crawlerProductPO);
}
