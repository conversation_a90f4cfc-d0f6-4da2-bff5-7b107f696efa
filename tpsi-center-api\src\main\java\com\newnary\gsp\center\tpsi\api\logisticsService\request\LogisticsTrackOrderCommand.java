package com.newnary.gsp.center.tpsi.api.logisticsService.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 物流服务轨迹请求体
 *
 * <AUTHOR>
 * @since Created on 2023-11-17
 **/
@Data
public class LogisticsTrackOrderCommand {


    /**
     * 物流返回的追踪号。
     **/
    @NotEmpty(message = "物流追踪号不能为空")
    @Size(max = 40,message = "trackingId最大坐标40")
    private String trackingId;


    /**
     * 语言
     */
    private String language;



}
