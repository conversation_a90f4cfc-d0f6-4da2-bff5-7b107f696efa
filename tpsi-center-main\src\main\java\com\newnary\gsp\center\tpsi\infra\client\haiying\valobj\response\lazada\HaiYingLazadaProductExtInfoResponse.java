package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.lazada;

import com.newnary.gsp.center.tpsi.api.haiying.response.lazada.HaiYingLazadaProductSkuInfoDTO;
import lombok.Data;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingLazadaProductExtInfoResponse {

    /**
     * 商品id
     */
    private String item_id;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品所属类目
     */
    private String cname;

    /**
     * 商品品牌
     */
    private String brand;

    /**
     * 商品最近抓取时间
     */
    private String last_modi_time;

    /**
     * 商品价格
     */
    private String price;

    /**
     * 商品热销件数
     */
    private String sold;

    /**
     * 商品评分
     */
    private String rating;

    /**
     * 商品评论数
     */
    private String review;

    /**
     * 商品问答数
     */
    private String qna;

    /**
     * 商品库存数
     */
    private String stock;

    /**
     * 商品发货地
     * (1本地，2海外，3Lazada)
     */
    private String delivery;

    /**
     * 商品所属卖家名称
     */
    private String seller;

    /**
     * 商品主图
     */
    private String main_image;

    /**
     * 商品状态   (0正常，1异常)
     */
    private String status;

    /**
     * 商品是否存在
     * (0存在，1不存在)
     */
    private String not_exist;

    /**
     * 商品首次抓取时间
     */
    private String insert_time;

    /**
     * 商品长描述
     */
    private String long_description;

    /**
     * 商品短描述
     */
    private String short_description;

    /**
     * 商品SPU
     */
    private String spu;

    /**
     * lazada商品链接
     */
    private String item_url;

    /**
     * 规格选项list, 商品属性
     */
    private String properties_list;

    /**
     * 店铺id
     */
    private String shop_id;

    /**
     * 商品sku列表
     */
    private HaiYingLazadaProductSkuInfoDTO[] sku_list;

}
