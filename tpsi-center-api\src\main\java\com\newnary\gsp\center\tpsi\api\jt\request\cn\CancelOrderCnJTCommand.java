package com.newnary.gsp.center.tpsi.api.jt.request.cn;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 取消
 *
 * <AUTHOR>
 * @since Created on 2023-08-11
 **/
@Data
@Valid
public class CancelOrderCnJTCommand {

    @NotBlank(message = "customerOrderNo(不能为空)")
    @Size(max = 32,message = "最大字符(32)")
    private String customerOrderNo;


}
