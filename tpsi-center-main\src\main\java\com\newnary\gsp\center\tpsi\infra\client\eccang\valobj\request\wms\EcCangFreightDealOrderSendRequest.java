package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.wms;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class EcCangFreightDealOrderSendRequest {
    /**
     * 仓库ID
     */
    @NotNull(message = "仓库id不能为空")
    private Integer warehouse_id;
    /**
     * 订单号
     */
    @NotNull(message = "订单号不能为空")
    private String order_code;
    /**
     * 订单发货的产品数据
     */
    @NotNull(message = "产品数据不能为空")
    private List<Product> product_data;

    @Getter
    @Setter
    public static class Product{

        /**
         * 产品id(pid>0修改产品数据，pid=0新增产品数据)
         */
        @NotNull(message = "产品id不能为空")
        private Integer pid;
        /**
         * 包裹号
         */
        @NotNull(message = "包裹号不能为空")
        private String package_code;
        /**
         * 产品⼦单
         */
        private String child_code;
        /**
         * 产品规格（新增产品必填）
         */
        private String product_standard;
        /**
         * 产品图⽚（新增产品必填）
         */
        private String product_img_url;
        /**
         * 产品数量（新增产品必填）
         */
        private Integer product_count;
        /**
         * 产品分类（新增产品必填）
         */
        private String product_category;
        /**
         * 产品中⽂名称（新增产品必填）
         */
        private String product_name_cn;
        /**
         * 产品英⽂名称（新增产品必填）
         */
        private String product_name_en;
        /**
         * 产品申报价值（新增产品必填）
         */
        private float product_declared;
        /**
         * HS海关编码
         */
        private String hs_code;
    }
}
