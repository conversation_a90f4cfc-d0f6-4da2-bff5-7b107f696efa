package com.newnary.gsp.center.tpsi.service.exchangeRate;

import com.newnary.distributed.tools.concurrent.template.DConcurrentTemplate;
import com.newnary.gsp.center.basicdata.api.currency.feign.ExchangeRateFeignApi;
import com.newnary.gsp.center.basicdata.api.currency.feign.ExchangeRateRealTimeFeignApi;
import com.newnary.gsp.center.tpsi.infra.client.exchangeRate.ExchangeRateClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class ExchangeRateRefreshSve {

    private static final String PREFIX = "EXCHANGE_RATE_REFRESH_SVE";

    @Resource
    private ExchangeRateClient exchangeRateClient;

    @Resource
    private ExchangeRateRealTimeFeignApi exchangeRateRealTimeFeignApi;

    @Transactional
    public String refreshExchangeRate(String source) {
        return DConcurrentTemplate.tryLockMode(
                PREFIX.concat(source),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    String dataJson = exchangeRateClient.getExchangeRateBySourceCurrency(source);
                    if (StringUtils.isEmpty(dataJson)) {
                        log.info("调用exchangeRate-api出错");
                        return "FAIL";
                    } else {
                        log.info("汇率相关信息获取成功");
                        return exchangeRateRealTimeFeignApi.storeAll(dataJson).getBody();
                    }
                }
        );
    }

}
