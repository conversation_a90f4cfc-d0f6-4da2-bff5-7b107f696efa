package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.erp;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class EcCangERPGetSupplierListResponse {

    /**
     * 供应商ID
     */
    private String supplierId;

    /**
     * 供应商代码：数字字母下划线和中横线最佳
     */
    private String supplierCode;

    /**
     * 名称CN
     */
    private String supplierName;

    /**
     * 等级
     */
    private String level;

    /**
     * 合作类型
     */
    private Integer supplierTeamworkType;

    /**
     * 供应商类型：1零售、2批发、3生产商、4通用虚拟、5显示、6市场 (0：表示为填写)
     */
    private Integer supplierType;

    /**
     * 组织机构ID
     */
    private Integer supplierOrganizationId;

    /**
     * 支付周期类型：1月结、2隔月结、3日结、4周结、5半月结 (0：表示为填写)
     */
    private Integer pcId;

    /**
     * 结算方式：1货到付款、2款到发货、3帐期
     */
    private Integer accountType;

    /**
     * 默认支付方式：1现金、2在线、3银行卡
     */
    private Integer payType;

    /**
     * 运输承担方：1供应商、2采购方
     */
    private Integer supplierCarrier;

    /**
     * 运输支付方式： 1预付、2到付
     */
    private Integer supplierShipPayType;

    /**
     * 默认运输方式：1自提、2快递、3物流、4送货
     */
    private Integer shippingMethodIdHead;

    /**
     * QC不良品处理：1退货、2换货、3采购方承担
     */
    private Integer supplierQcException;

    /**
     * 合同注意事项
     */
    private String supplierTreaty;

    /**
     * 比例值
     */
    private Float accountProportion;

    /**
     * 名称EN
     */
    private String supplierNameEn;

    /**
     * 默认采购员
     */
    private Integer buyerId;

    /**
     * 默认跟单员
     */
    private Integer trackId;

    /**
     * 状态：0正式供应商，1草稿，2待审核，3暂停供应商
     */
    private Integer supplierStatus;

    /**
     * 供应商佣金比例：例如0.03
     */
    private Float supplierCommissionRatio;

    /**
     * 合同采购金额
     */
    private Float purchaseContractAmount;

    /**
     * 支付账户数据
     */
    private List<PaymentAccount> paymentAccountList;

    /**
     * 联系方式数据
     */
    private List<SupplierContact> contactList;

    /**
     * 附属图片URL数据
     */
    private List<SupplierImage> supplierImagesList;

    /**
     * 默认运输方式承运商Id
     */
    private Integer psId;

    /**
     * 主营品类id
     */
    private String supplierMainCategoryId;

    /**
     * 备注
     */
    private String supplierNote;

    @Setter
    @Getter
    private static class PaymentAccount {

        /**
         * 银行名称（支付方式为现金，值为空）
         */
        private String platformName;

        /**
         * 支付平台：1paypal、2财付通、3支付宝、4快钱、5网银、6微信、7诚e赊（支付方式为现金，值为空）
         */
        private String platformType;

        /**
         * 账户
         */
        private String pmAccount;

        /**
         * 开户人
         */
        private String pmName;

        /**
         * 支付方式，1现金、2在线，3银行账号
         */
        private Integer paymentMethod;

        /**
         * 收款人
         */
        private String pmLead;

        /**
         * 收款公司
         */
        private String pmCompany;

        /**
         * 状态：0:停用、1:可用
         */
        private Integer pmStatus;

    }

    @Setter
    @Getter
    private static class SupplierContact {

        /**
         * 联系人名称
         */
        private String contactName;

        /**
         * 联系人电话
         */
        private String contactTel;

        /**
         * 联系人ID，主键ID
         */
        private Integer contactId;

        /**
         * Fax
         */
        private String contactFax;

        /**
         * 中文联系地址
         */
        private String contactAddress;

        /**
         * 英文联系地址
         */
        private String contactAddressEn;

        /**
         * 联系邮编
         */
        private String contactPostCode;

        /**
         * QQ
         */
        private String contactQQ;

        /**
         * 微信号
         */
        private String contactWechat;

        /**
         * 旺旺号
         */
        private String contactWangwang;

        /**
         * Skype
         */
        private String contactSkype;

    }

    @Setter
    @Getter
    private static class SupplierImage {

        /**
         * 图片URL
         */
        private String url;

    }

}
