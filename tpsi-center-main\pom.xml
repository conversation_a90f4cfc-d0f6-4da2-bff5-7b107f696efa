<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>tpsi-center</artifactId>
        <groupId>com.newnary.gsp.center.tpsi</groupId>
        <version>2.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>tpsi-center-main</artifactId>

    <properties>
        <maven.install.skip>true</maven.install.skip>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.newnary</groupId>
            <artifactId>spring-cloud-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.newnary</groupId>
            <artifactId>dao-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.newnary</groupId>
            <artifactId>distributed-tools-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.newnary</groupId>
            <artifactId>mq-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.newnary</groupId>
            <artifactId>job-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.newnary.gsp.center.tpsi</groupId>
            <artifactId>tpsi-center-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.newnary.gsp.center.product</groupId>
            <artifactId>product-center-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.newnary.gsp.transfer.sunway</groupId>
            <artifactId>sunway-transfer-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.newnary.gsp.center.trade</groupId>
            <artifactId>trade-center-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.newnary.gsp.center.logistics</groupId>
            <artifactId>logistics-center-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.newnary.gsp.center.basicdata</groupId>
            <artifactId>basic-data-center-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.newnary.gsp.center.crawlerproxy</groupId>
            <artifactId>crawler-proxy-center-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.newnary.gsp.center.purchase</groupId>
            <artifactId>purchase-center-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.newnary.gsp.center.user</groupId>
            <artifactId>user-center-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.newnary.gsp.center.wms</groupId>
            <artifactId>wms-center-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.newnary.gsp.center.stock</groupId>
            <artifactId>stock-center-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.newnary</groupId>
            <artifactId>newnary-monitor-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>ocean-client-java-biz-sdk</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.sejda.imageio</groupId>
            <artifactId>webp-imageio</artifactId>
            <version>0.1.6</version>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.12.1</version>
        </dependency>

        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
        </dependency>
        <!--oss-->
        <dependency>
            <groupId>com.newnary</groupId>
            <artifactId>oss-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.15</version>
        </dependency>

        <!-- pdf样式 -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>5.2.0</version>
        </dependency>
        <!--pdf相关操作-->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.13.3</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.5.2</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>3.5.2</version>
        </dependency>

        <!-- 领星sdk -->
        <dependency>
            <groupId>com.newnary.asinking</groupId>
            <artifactId>openapi</artifactId>
            <version>20240730</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.9.0</version>
        </dependency>


        <dependency>
            <groupId>com.newnary.gsp.center.tms</groupId>
            <artifactId>tms-center-api</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.money</groupId>
            <artifactId>money-api</artifactId>
            <version>1.1</version>
        </dependency>
        <dependency>
            <groupId>org.javamoney</groupId>
            <artifactId>moneta</artifactId>
            <version>1.4.4</version>
            <type>pom</type>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.parent.artifactId}</finalName>

        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <!-- 是否替换资源中的属性-->
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.newnary.gsp.center.tpsi.Bootstrap</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>