package com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class VVICCreateOrderReq {

    /**
     *  网站用户名
     */
    @NotNull(message = "网站用户名不能为空")
    private String user_name;

    /**
     *网站用户手机号码
     */
    @NotNull(message = "网站用户手机号码不能为空")
    private String user_mobile;

    /**
     *发件人姓名
     */
    @NotNull(message = "发件人姓名不能为空")
    private String shipper_name;

    /**
     *发件人手机
     */
    @NotNull(message = "发件人手机不能为空")
    private String shipper_mobile;

    /**
     *收件人姓名
     */
    @NotNull(message = "收件人姓名不能为空")
    private String consignee;

    /**
     *收件人的国家
     */
    @NotNull(message = "收件人的国家不能为空")
    private String country;

    /**
     *收件人的省份
     */
    @NotNull(message = "收件人的省份不能为空")
    private String province;

    /**
     *收件人的城市
     */
    @NotNull(message = "收件人的城市不能为空")
    private String city;

    /**
     *收件人的地区
     */
    private String area;

    /**
     *	收货人的国家id
     */
    @NotNull(message = "收货人的国家id不能为空")
    private Integer country_id;

    /**
     *收货人的省份id
     */
    @NotNull(message = "收货人的省份id不能为空")
    private Integer province_id;

    /**
     *收货人的城市id
     */
    @NotNull(message = "收货人的城市id不能为空")
    private Integer city_id;

    /**
     *收货人的地区id
     */
    private Integer area_id;

    /**
     *	收货人的详细地址
     */
    @NotNull(message = "收货人的详细地址不能为空")
    private String address;

    /**
     *	收货人的手机号码
     */
    @NotNull(message = "收货人的手机号码不能为空")
    private String mobile;

    /**
     *快递id
     */
    @NotNull(message = "快递id不能为空")
    private Long express_id;

    /**
     *快递名称
     */
    @NotNull(message = "快递名称不能为空")
    private String express_name;

    /**
     *外部订单id
     */
    //@NotNull(message = "外部订单id不能为空")
    private Long out_order_id;

    /**
     *发货方式 1:有货先发 2:货齐再发
     */
    @NotNull(message = "发货方式不能为空")
    private Integer express_type;

    /**
     *	发货时间.格式:yyyy-MM-dd，当选择有货先发时为必填项且大于今天
     */
    private String delivery_time;

    /**
     *外部订单号(必须唯一)
     */
    @NotNull(message = "外部订单号不能为空")
    private String out_order_no;

    /**
     *商品详情列表
     */
    @NotNull(message = "商品详情列表不能为空")
    private List<OrderDetail> order_details;

    /**
     *	代发服务列表（必须包含拿货服务、质检服务和打包服务，增值服务为非必须）
     */
    @NotNull(message = "代发服务列表不能为空")
    private List<VasEntry> vas_entries;

    @Getter
    @Setter
    public static class OrderDetail {
        /**
         *商品购买数量，至少为1
         */
        @NotNull(message = "商品购买数量不能为空")
        private Integer item_num;

        /**
         *款式vid
         */
        @NotNull(message = "款式vid不能为空")
        private String sku_vid;

        /**
         *	sku价格（以商家价格为准）
         */
        @NotNull(message = "sku价格不能为空")
        private String sku_price;
    }

    @Getter
    @Setter
    public static class VasEntry {
        /**
         *代发服务Id
         */
        @NotNull(message = "代发服务Id不能为空")
        private Long vas_id;

        /**
         *代发服务名称
         */
        @NotNull(message = "代发服务名称不能为空")
        private String vas_name;

        /**
         *	服务类型：1-拿货服务，2-质检服务， 4-打包服务，5-增值服务
         */
        @NotNull(message = "服务类型不能为空")
        private Integer type;

        /**
         *服务的数量，当服务类型为增值服务时，数量至少为1；其他服务类型无需传入此参数
         */
        @NotNull(message = "服务的数量不能为空")
        private Integer quantity;

    }


}
