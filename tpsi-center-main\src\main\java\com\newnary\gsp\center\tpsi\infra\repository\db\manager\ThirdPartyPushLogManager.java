package com.newnary.gsp.center.tpsi.infra.repository.db.manager;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.newnary.api.base.common.PageCondition;
import com.newnary.api.base.common.PageList;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.common.utils.page.PageUtil;
import com.newnary.dao.base.enums.DatabaseDialectType;
import com.newnary.dao.base.helper.DaoHelper;
import com.newnary.dao.base.po.BaseQuery;
import com.newnary.gsp.center.tpsi.api.common.enums.Gsp2EccangPOCreateEventBizState;
import com.newnary.gsp.center.tpsi.infra.mapper.ThirdPartyPushLogMapper;
import com.newnary.gsp.center.tpsi.api.common.enums.ThirdPartyEventState;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyPushLogInfo;
import com.newnary.gsp.center.tpsi.infra.repository.db.dao.ThirdPartyPushLogDao;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyPushLogPO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class ThirdPartyPushLogManager {

    public static void main(String[] args) {
        DaoHelper.genXMLWithFeature(
                "D:\\it-center\\codes\\newnary_gz_codes\\tpsi-center\\tpsi-center-main\\src\\main\\java\\com\\newnary\\gsp\\center\\tpsi\\infra\\repository\\db\\dao\\ThirdPartyPushLogDao.xml",
                ThirdPartyPushLogDao.class,
                ThirdPartyPushLogPO.class,
                "third_party_push_log",
                false,
                DatabaseDialectType.MYSQL);
    }

    @Resource
    private ThirdPartyPushLogDao thirdPartyPushLogDao;

    /**
     * 执行更新或插入
     *
     * @param logInfo
     */
    public void insertOrUpdate(ThirdPartyPushLogInfo logInfo) {

        if(StringUtils.isAnyBlank(logInfo.getEventBizId(), logInfo.getEventType())) {
            throw new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "必填参数不能为空");
        }

        BaseQuery<ThirdPartyPushLogPO> query = new BaseQuery<>(new ThirdPartyPushLogPO());
        query.getData().setEventType(logInfo.getEventType());
        query.getData().setEventBizId(logInfo.getEventBizId());
        List<ThirdPartyPushLogPO> result = thirdPartyPushLogDao.query(query);
        if(CollectionUtils.isNotEmpty(result)) {
            ThirdPartyPushLogPO updatePO = result.get(0);
            // 如果已经同步成功，只更新系统备注
            if(updatePO.getEventState() == 1) {
                updatePO.setSysRemark(logInfo.getSysRemark());
                thirdPartyPushLogDao.update(updatePO);
            } else {
                updatePO.setEventData(logInfo.getEventData());
                updatePO.setReqData(logInfo.getReqData());
                updatePO.setRespData(logInfo.getRespData());
                updatePO.setSysRemark(logInfo.getSysRemark());
                updatePO.setEventState(logInfo.getEventState());
                updatePO.setEventBizState(logInfo.getEventBizState());
                thirdPartyPushLogDao.update(updatePO);
            }
        } else {
            // 执行插入
            thirdPartyPushLogDao.insert(
                    ThirdPartyPushLogMapper.INSTANCE.info2Po(logInfo)
            );
        }
    }

    /**
     * 执行更新或插入
     *
     * @param logInfo
     */
    public void insertOrUpdateV2(ThirdPartyPushLogInfo logInfo) {

        if(StringUtils.isAnyBlank(logInfo.getEventBizId(), logInfo.getEventType())) {
            throw new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "必填参数不能为空");
        }

        BaseQuery<ThirdPartyPushLogPO> query = new BaseQuery<>(new ThirdPartyPushLogPO());
        query.getData().setEventType(logInfo.getEventType());
        query.getData().setEventBizId(logInfo.getEventBizId());
        List<ThirdPartyPushLogPO> result = thirdPartyPushLogDao.query(query);
        if(CollectionUtils.isNotEmpty(result)) {
            ThirdPartyPushLogPO updatePO = result.get(0);
            // 如果已经同步成功，只更新系统备注
            if(updatePO.getEventState() == 1) {
                updatePO.setSysRemark(logInfo.getSysRemark());
                thirdPartyPushLogDao.update(updatePO);
            } else {
                updatePO.setRespData(logInfo.getRespData());
                updatePO.setSysRemark(logInfo.getSysRemark());
                updatePO.setEventState(logInfo.getEventState());
                updatePO.setEventBizState(logInfo.getEventBizState());
                thirdPartyPushLogDao.update(updatePO);
            }
        } else {
            // 执行插入
            thirdPartyPushLogDao.insert(
                    ThirdPartyPushLogMapper.INSTANCE.info2Po(logInfo)
            );
        }
    }

    public PageList<ThirdPartyPushLogInfo> pageQueryByEventTypeAndFailState(String eventType, PageCondition pageCondition) {

        if(StringUtils.isBlank(eventType) || null == pageCondition) {
            throw new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "必填参数不能为空");
        }

        Page<ThirdPartyPushLogPO> page = PageHelper.startPage(pageCondition.pageNum, pageCondition.pageSize, false);

        BaseQuery<ThirdPartyPushLogPO> query = new BaseQuery<>(new ThirdPartyPushLogPO());
        query.getData().setEventType(eventType);
        query.getData().setEventState(ThirdPartyEventState.FAIL.getValue());
        query.addOrderBy("gmt_create",0);
        thirdPartyPushLogDao.query(query);

        PageList<ThirdPartyPushLogPO> poPageList = PageUtil.createPageList(page);
        List<ThirdPartyPushLogInfo> resultList = poPageList.getItems().stream().map(ThirdPartyPushLogMapper.INSTANCE::po2Info).collect(Collectors.toList());
        return PageUtil.createPageList(poPageList.getPageMeta(), resultList);
    }

    public PageList<ThirdPartyPushLogInfo> pageQueryByEventTypeAndFailStateSyncEcInfo(String eventType, PageCondition pageCondition, String eventBizId) {
        if(StringUtils.isBlank(eventType) || null == pageCondition) {
            throw new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "必填参数不能为空");
        }

        Page<ThirdPartyPushLogPO> page = PageHelper.startPage(pageCondition.pageNum, pageCondition.pageSize, false);
        BaseQuery<ThirdPartyPushLogPO> query = new BaseQuery<>(new ThirdPartyPushLogPO());
        if (StringUtils.isNotBlank(eventBizId)) {
            query.getData().setEventBizId(eventBizId);
        }
        query.getData().setEventType(eventType);
        query.getData().setEventState(ThirdPartyEventState.FAIL.getValue());
        query.addGte("event_biz_state",Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_CREATED.getValue());
        query.addOrderBy("gmt_create",1);
        thirdPartyPushLogDao.query(query);

        PageList<ThirdPartyPushLogPO> poPageList = PageUtil.createPageList(page);
        List<ThirdPartyPushLogInfo> resultList = poPageList.getItems().stream().map(ThirdPartyPushLogMapper.INSTANCE::po2Info).collect(Collectors.toList());
        return PageUtil.createPageList(poPageList.getPageMeta(), resultList);
    }

    public PageList<ThirdPartyPushLogInfo> pageQueryByEventTypeAndFailStateSyncEcCreate(String eventType, PageCondition pageCondition) {

        if(StringUtils.isBlank(eventType) || null == pageCondition) {
            throw new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "必填参数不能为空");
        }

        Page<ThirdPartyPushLogPO> page = PageHelper.startPage(pageCondition.pageNum, pageCondition.pageSize, false);

        BaseQuery<ThirdPartyPushLogPO> query = new BaseQuery<>(new ThirdPartyPushLogPO());
        query.getData().setEventType(eventType);
        query.getData().setEventState(ThirdPartyEventState.FAIL.getValue());
        query.addOrderBy("gmt_create",0);
        query.addLt("event_biz_state",Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_CREATED.getValue());
        thirdPartyPushLogDao.query(query);

        PageList<ThirdPartyPushLogPO> poPageList = PageUtil.createPageList(page);
        List<ThirdPartyPushLogInfo> resultList = poPageList.getItems().stream().map(ThirdPartyPushLogMapper.INSTANCE::po2Info).collect(Collectors.toList());
        return PageUtil.createPageList(poPageList.getPageMeta(), resultList);
    }



    public ThirdPartyPushLogInfo queryByEventBizId(String name, String purchaseOrderId) {
        if(StringUtils.isBlank(purchaseOrderId)) {
            throw new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "必填参数不能为空");
        }

        BaseQuery<ThirdPartyPushLogPO> query = new BaseQuery<>(new ThirdPartyPushLogPO());
        query.getData().setEventType(name);
        query.getData().setEventBizId(purchaseOrderId);
        List<ThirdPartyPushLogPO> po = thirdPartyPushLogDao.query(query);
        List<ThirdPartyPushLogInfo> resultList = po.stream().map(ThirdPartyPushLogMapper.INSTANCE::po2Info).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(resultList)){
            return resultList.get(0);
        }
        return null;
    }


}
