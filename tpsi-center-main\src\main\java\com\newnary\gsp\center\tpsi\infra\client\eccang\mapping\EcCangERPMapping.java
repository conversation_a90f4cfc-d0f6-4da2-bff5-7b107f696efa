package com.newnary.gsp.center.tpsi.infra.client.eccang.mapping;

import java.util.HashMap;
import java.util.Map;

public class EcCangERPMapping {

    //供应商编码
    public static final Map<String, String> SupplierCodeMapping = new HashMap<String, String>() {
        {
            put("VD3476395680056359391232", "Xuqiao");
        }
    };

    //供应商id
    public static final Map<String, String> SupplierIdMapping = new HashMap<String, String>() {
        {
            put("VD3476395680056359391232", "1");
        }
    };

    //仓库id
    public static final Map<String, Integer> WarehouseIdMapping = new HashMap<String, Integer>() {
        {
            put("广州-MT-测试仓库", 228);
            put("佛山01仓", 7);
            put("广州仓", 8);
            put("佛山中转仓", 322);
            put("国内虚拟仓", 105);
            put("中国包材仓", 130);
            put("BGS办公室", 256);
            put("OZON半托管仓(爱夫卡)", 347);
        }
    };

    //仓库编码
    public static final Map<String, String> WarehouseCodeMapping = new HashMap<String, String>() {
        {
            put("广州-MT-测试仓库".toUpperCase(), "W_1688_AGENT".toUpperCase());
            put("TIKTOK-SELF-PH".toUpperCase(), "PGLPH01".toUpperCase());
        }
    };

    //组织机构
    public static final Map<Long, Integer> OrganizationIdMapping = new HashMap<Long, Integer>() {
        {
            put(1234L, 14100);
        }
    };

    //结算方式
    public static final Map<String, Integer> AccountTypeMapping = new HashMap<String, Integer>() {
        {
            put("CASH_ON_DELIVERY", 1);
            put("DELIVERY_ON_ARRIVAL", 2);
            put("ACCOUNT_PERIOD", 3);
        }
    };

    //销售状态
    public static final Map<Integer, String> SaleStatusMapping = new HashMap<Integer, String>() {
        {
            put(1, "50");
            put(2, "20");
            put(3, "50");
            put(4, "60");
            put(5, "20");
            put(6, "50");
            put(7, "50");
            put(8, "50");
            put(9, "50");
            put(10, "20");
            put(11, "60");
            put(12, "60");
            put(13, "60");
            put(14, "60");
            put(15, "60");
            put(16, "20");
            put(17, "50");
            put(18, "20");
            put(19, "60");
            put(20, "50");
            put(21, "50");
        }
    };

    //销售状态
    public static final Map<Integer, String> SaleStatusNameMapping = new HashMap<Integer, String>() {
        {
            put(1, "清货待下架");
            put(2, "在线商品");
            put(3, "定制类产品");
            put(4, "平台下架");
            put(5, "缺货产品（缺货大于15天）");
            put(6, "海外仓分销");
            put(7, "万圣节专卖");
            put(8, "圣诞节专卖");
            put(9, "清货待下架(侵权品)");
            put(10, "飙乘一件代发(无需备货，供应商代发)");
            put(11, "断货下架");
            put(12, "重复开发下架");
            put(13, "违反美国加州65号提案-下架");
            put(14, "做不过竞争对手下架");
            put(15, "礼品-清货待下架");
            put(16, "美客多专卖");
            put(17, "升威-分销sku");
            put(18, "铭车一件代发(无需备货，供应商代发)");
            put(19, "销毁");
            put(20, "积压清货");
            put(21, "重复弃用");
        }
    };
}
