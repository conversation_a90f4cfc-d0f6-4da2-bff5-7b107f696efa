package com.newnary.gsp.center.tpsi.infra.model;

import com.newnary.gsp.center.tpsi.infra.model.creator.ApiDockingResultCreator;
import com.newnary.gsp.center.tpsi.infra.model.updater.ApiDockingResultUpdater;
import com.newnary.gsp.center.tpsi.infra.model.vo.ApiDockingResultType;
import com.newnary.spring.cloud.domain.Aggregate;
import lombok.Getter;

@Getter
public class ApiDockingResult extends Aggregate {

    /**
     * 主键
     */
    private String valueKey;

    /**
     * 数据类型
     */
    private ApiDockingResultType valueType;

    /**
     * 对应json
     */
    private String valueJson;

    /**
     * 状态
     */
    private String dataStatus;


    public static ApiDockingResult createWith(ApiDockingResultCreator creator) {
        return new ApiDockingResult(creator);
    }

    public static ApiDockingResult loadWith(ApiDockingResultCreator creator) {
        return new ApiDocking<PERSON>esult(creator.getId(), creator);
    }

    public void updateWith(ApiDockingResultUpdater updater) {
        setValueKey(updater.getValueKey());
        setValueType(ApiDockingResultType.valueOf(updater.getValueType()));
        setValueJson(updater.getValueJson());
        setDataStatus(updater.getDataStatus());
    }

    private ApiDockingResult(ApiDockingResultCreator creator) {
        setValueKey(creator.getValueKey());
        setValueType(ApiDockingResultType.valueOf(creator.getValueType()));
        setValueJson(creator.getValueJson());
        setDataStatus(creator.getDataStatus());
    }

    private ApiDockingResult(Long id, ApiDockingResultCreator creator) {
        super(id);
        setValueKey(creator.getValueKey());
        setValueType(ApiDockingResultType.valueOf(creator.getValueType()));
        setValueJson(creator.getValueJson());
        setDataStatus(creator.getDataStatus());
    }

    public void setValueKey(String valueKey) {
        this.valueKey = valueKey;
    }

    public void setValueType(ApiDockingResultType valueType) {
        this.valueType = valueType;
    }

    public void setValueJson(String valueJson) {
        this.valueJson = valueJson;
    }

    public void setDataStatus(String dataStatus) {
        this.dataStatus = dataStatus;
    }
}
