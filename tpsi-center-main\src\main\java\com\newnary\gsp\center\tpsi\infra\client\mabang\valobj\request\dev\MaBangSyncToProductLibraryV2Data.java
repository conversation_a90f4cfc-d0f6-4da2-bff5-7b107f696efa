package com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.dev;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class MaBangSyncToProductLibraryV2Data {

    /**
     * 产品id (做修改时传入)
     */
    private String productId;

    /**
     * 产品sku (必填)
     */
    @NotBlank(message = "产品sku（必填）")
    private String sku;

    /**
     * 默认标题 (必填)
     */
    @NotBlank(message = "默认标题（必填）")
    private String defaultTitle;

    /**
     * 单属性尺寸信息 单位默认cm (必填)
     */
    @NotNull(message = "尺寸信息（必填）")
    private MaBangSyncToProductLibraryV2DataSize size;

    /**
     * 产品多语言标题
     */
    private List<MaBangSyncToProductLibraryV2DataMulLanguageInfo> mulTitle;

    /**
     * 成本价 (必填，不填不会报错)
     */
    private String price;

    /**
     * 默认纯文本描述 (必填，不填不会报错)
     */
    private String defaultTextDescription;

    /**
     * 多语言纯文本描述信息
     */
    private List<MaBangSyncToProductLibraryV2DataMulLanguageInfo> mulTextDescription;

    /**
     * 产品图片 (必填，不填不会报错)
     */
    private MaBangSyncToProductLibraryV2DataImages images;

    /**
     * 0/1（0：未认证，1已认证）
     */
    private String cecert;

    /**
     * 取值：'0' => '无', '1' => '普货', '2' => '防疫普货', '3' => '无电池的电子产品', '4' => '小剪刀类', '5' => '螺丝刀工具类', '6' => '折刀类', '7' => '管制刀具类'
     */
    private String logisticsattr;

    /**
     * 单属性商品备注
     */
    private String productremark;

    /**
     * "categorys":[{
     * "categoryId":"int 产品目录编号"
     * }
     */
    private List<MaBangSyncToProductLibraryV2DataCategorysInfo> categorys;

    /**
     * 产品重量
     */
    private String weight;

    /**
     * 重量单位 enum g/kg
     */
    private String weightUnit;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 是否赠品 enum true/false
     */
    private String isGiveaway;

    /**
     * 产品id
     */
    private String UENumber;

    /**
     * 产品id类型 enum UPC/ISBN/EAN/ASIN/GTIN/GCID
     */
    private String UENumberType;

    /**
     * "tags":[
     * "tag1",
     * "tag2",
     * ......
     * ]
     */
    private List<String> tags;

    /**
     * 视频链接
     */
    private String videoUrl;

    /**
     * 申报品名(中文)
     */
    private String declareCn;

    /**
     * 申报品名(英文)
     */
    private String declareEn;

    /**
     * 申报价格
     */
    private String declarePrice;

    /**
     * 报关编码
     */
    private String declareCode;

    /**
     * 开发员 - 非必填
     */
    private String developer;

    /**
     * 采购员 - 非必填
     */
    private String buyer;

    private List<MaBangSyncToProductLibraryV2DataWarehouseInfo> warehouse;

    /**
     * 是否同步到库存sku true/false（暂不支持）
     */
    private String synToStock;

    /**
     * "annexUrl": [
     * "附件url",
     * ......
     * ]
     */
    private List<String> annexUrl;

    /**
     * a特殊标记 可选范围 ["带电","侵权","带磁","非液体化妆品","液体化妆品","粉末","违禁品"]
     */
    private List<String> specialMark;

    /**
     * 富文本描述 可包含html
     */
    private String complexDescription;

    /**
     * 体积重基数 5000 / 6000 或自定义
     */
    private String volumeBase;

    /**
     * 库存警戒天数
     */
    private String stockWarningDays;

    /**
     * 采购天数
     */
    private String purchaseDays;

    /**
     * 美工备注
     */
    private String designerRemark;

    /**
     * 销售备注
     */
    private String salesRemark;

    /**
     * 采购备注
     */
    private String purchaseRemark;

    /**
     * 质检标准
     */
    private String qualityMessage;

    /**
     * 多属性信息 - 非必填
     */
    private MaBangSyncToProductLibraryV2DataPropertysInfo propertys;

    /**
     * 竞品信息
     */
    private List<String> compets;

    /**
     * 供应商信息
     */
    private List<MaBangSyncToProductLibraryV2DataSupplierInfo> supplier;

    /**
     * 物品状况描述
     */
    private List<MaBangSyncToProductLibraryV2DataMulLanguageInfo> itemCondtion;

    /**
     * 亮点描述
     */
    private List<MaBangSyncToProductLibraryV2DataMulLanguageInfo> bulletPoint;
}
