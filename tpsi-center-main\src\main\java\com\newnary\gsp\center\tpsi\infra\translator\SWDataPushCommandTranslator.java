package com.newnary.gsp.center.tpsi.infra.translator;

import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderDetailInfo;
import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderDetailItemInfo;
import com.newnary.gsp.center.logistics.api.delivery.response.StockoutOrderInfo;
import com.newnary.gsp.center.product.api.product.response.SupplierSkuDetailInfo;
import com.newnary.gsp.center.product.api.product.response.SupplierSkuInfo;
import com.newnary.gsp.center.product.api.product.response.SupplierSpuBaseInfo;
import com.newnary.gsp.center.purchase.api.plan.request.PurchasePlanItem;
import com.newnary.gsp.center.purchase.api.plan.request.PurchasePlanPushCommand;
import com.newnary.gsp.center.purchase.api.product.request.SpuSkuSupplierCommand;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

public class SWDataPushCommandTranslator {

    /**
     * 爬虫调用1688供应商信息URL
     */
    private static final String getSupplierInfoUrl = "http://10.23.32.49:8340/get_1688ShopUrl";

    public static SpuSkuSupplierCommand constructSpuSkuSupplierCommand(SupplierSkuDetailInfo supplierSku, DeliveryOrderDetailItemInfo item){

        SpuSkuSupplierCommand command = new SpuSkuSupplierCommand();
        SupplierSpuBaseInfo spuBaseInfo = supplierSku.spuBaseInfo;
        SupplierSkuInfo skuInfo = supplierSku.skuInfo;

        //设置spu sku基础信息
        command.setSpu(spuBaseInfo.getCustomCode());
        command.setTitle(spuBaseInfo.descInfo.title);
        command.setSku(skuInfo.customCode);
        command.setSkuName(item.supplierSkuTitle);
        command.setCategoryName(spuBaseInfo.categoryName);
        command.setSizeLength(BigDecimal.ONE);
        command.setSizeWidth(BigDecimal.ONE);
        command.setSizeHeight(BigDecimal.ONE);
        Optional.of(skuInfo).ifPresent(info->{
            if (Objects.nonNull(info.mainSpecInfo)){
                command.setSkuImgUrl(info.mainSpecInfo.image.fileUrl);
            }else{
                command.setSkuImgUrl(spuBaseInfo.mainImageUrls.stream().findFirst().get());
            }
        });

        //设置规格型号
        if (CollectionUtils.isNotEmpty(skuInfo.specs)){
            StringBuffer specName = new StringBuffer();
            StringBuffer specValue = new StringBuffer();
            skuInfo.specs.stream().forEach(spec->{
                 specName.append(spec.specName).append("+");
                 specValue.append(spec.specValue).append("+");
            });
            command.setSkuAttrName(specName.deleteCharAt(specName.length()-1).toString());
            command.setSkuAttrValue(specValue.deleteCharAt(specValue.length()-1).toString());
        }

        command.setUnit(skuInfo.measuringUnit);
        command.setPurchaseCycle(String.valueOf(skuInfo.leadTime));
        command.setSpecialTags(null);
        command.setZhDeclaredName(item.invoiceCnName);
        command.setEnDeclaredName(item.invoiceEnName);
        command.setDeclaredPrice(new BigDecimal(item.supplyPrice));
        Optional.ofNullable(skuInfo.netWeight).ifPresent(netWeight->command.setNetWeight(netWeight.setScale(3).toString()));
        Optional.ofNullable(skuInfo.grossWeight).ifPresent(grossWeight->command.setGrossWeight(grossWeight.setScale(3).toString()));
        command.setReferenceCost(item.supplyPrice);
        if (Objects.isNull(skuInfo.moq)){
            skuInfo.moq = 1;
        }
        command.setMinPurchaseQuantity(String.valueOf(skuInfo.moq));
        command.setPackingFee(null);
        command.setPurchaser("czh35953595");
        command.setAliLink("https://detail.1688.com/offer/"+spuBaseInfo.customCode+".html");
        if (StringUtils.isNotBlank(spuBaseInfo.getDpsName())) {
            command.setSupplierName(spuBaseInfo.getDpsName());
        }else{
            command.setSupplierName("默认供应商");
        }
        return command;
    }

    public static PurchasePlanPushCommand constructPurchasePlanCommand(DeliveryOrderDetailInfo deliveryOrderDetailInfo, StockoutOrderInfo stockoutOrderInfo) {
        List<DeliveryOrderDetailItemInfo> item = deliveryOrderDetailInfo.items;
        PurchasePlanPushCommand command = new PurchasePlanPushCommand();
        //参好号推送
        String refOrderNumber = stockoutOrderInfo.getStockoutOrderId();
        command.setRefOrderNumber(refOrderNumber);
        command.setWarehouseName("广州仓");
        command.setRemark("系统推送");
        List<PurchasePlanItem> itemList =new ArrayList<>();
        item.forEach(itemInfo->{
            PurchasePlanItem planItem = new PurchasePlanItem();
            planItem.setSku(itemInfo.getCustomCode());
            planItem.setPurchaseQuantity(itemInfo.getQuantity());
            planItem.setGoodsRemark("系统推送采购计划");
            planItem.setIsUrgent(1);
            planItem.setSalesWarehouse("广州仓");
            planItem.setPurchaseEntity("a");
            planItem.setMerchandiserUsername("czh35953595");
            planItem.setRefOrderNumber(refOrderNumber);
            itemList.add(planItem);
        });
        command.setPurchasePlanItems(itemList);
        return command;
    }
}
