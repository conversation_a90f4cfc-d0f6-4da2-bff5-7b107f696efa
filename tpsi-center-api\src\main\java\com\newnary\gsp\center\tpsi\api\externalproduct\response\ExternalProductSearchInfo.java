package com.newnary.gsp.center.tpsi.api.externalproduct.response;

import com.newnary.api.base.common.PageList;
import com.newnary.gsp.center.tpsi.api.externalproduct.vo.ExternalProductCategoryVO;
import com.newnary.gsp.center.tpsi.api.externalproduct.vo.ExternalProductLiteInfo;
import lombok.Data;

import java.util.List;

/**
 * 外部系统商品-搜索结果公共响应体
 *
 * <AUTHOR>
 * @since Created on 2022-05-10
 **/
@Data
public class ExternalProductSearchInfo {

    /**
     * 商品归类信息列表(非必有)
     **/
    private List<ExternalProductCategoryVO> categoryList;

    /**
     * 结果分页信息
     **/
    private PageList<ExternalProductLiteInfo> page;

}
