package com.newnary.gsp.center.tpsi.infra.client.tk.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TKFetchOrderReq {
    // 单位：秒
    public Integer start_time;
    public Integer end_time;
    /**
     * Use this field to obtain orders in a specific status
     * - UNPAID = 100;
     * - AWAITING_SHIPMENT = 111;
     * - AWAITING_COLLECTION = 112;
     * - PARTIALLY_SHIPPING = 114;
     * - IN_TRANSIT = 121;
     * - DELIVERED = 122;
     * - COMPLETED = 130;
     * - CANCELLED = 140;
     */
    public Integer order_status;
    // 1-50
    public Integer page_size;
    //CREATE_TIME
    public String sort_by;
    public String cursor;
    public Integer create_time_from;
    public Integer create_time_to;
    public Integer update_time_from;
    public Integer update_time_to;
    // Available value: ASCE = 1;DESC = 2; (default)
    public Integer sort_type;
    public Integer delivery_option_type;
}
