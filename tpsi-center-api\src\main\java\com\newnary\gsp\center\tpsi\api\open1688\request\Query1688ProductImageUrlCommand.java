package com.newnary.gsp.center.tpsi.api.open1688.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class Query1688ProductImageUrlCommand {
    //场景
    private String scenario;

    //图搜参数
    private Param param;

    @Setter
    @Getter
    public static class Param{
        //需要筛选的商品标签
        private List<String> filler;

        //图片url
        private String imageUrl;

        //当前页
        private Long pageNum;

        //页大小
        private Long pageSize;

        //价格区间end
        private String priceEnd;

        //价格区间start
        private String priceStart;

        //起批量
        private Integer quantityBegin;
    }
}
