package com.newnary.gsp.center.tpsi.infra.mapper;


import com.newnary.gsp.center.tpsi.api.haiying.request.ebay.*;
import com.newnary.gsp.center.tpsi.api.haiying.response.ebay.*;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.ebay.*;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.ebay.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Author: jack
 * @CreateTime: 2022-9-5
 */
@Mapper
public interface HaiYingEbayDataMapper extends HaiYingDataMapper {

    HaiYingEbayDataMapper INSTANCE = Mappers.getMapper(HaiYingEbayDataMapper.class);

    @Mapping(target = "station", source = "station", qualifiedByName = "haiyingStation2Str")
    HaiYingEbayCategoryTreeRequest transEbayCategoryTreeRequest(HaiYingEbayCategoryTreeCommand command);

    @Mapping(target = "station", source = "station", qualifiedByName = "haiyingStation2Str")
    @Mapping(target = "current_page", source = "pageCondition", qualifiedByName = "pageCondition2CurrentPage")
    @Mapping(target = "page_size", source = "pageCondition", qualifiedByName = "pageCondition2PageSize")
    HaiYingEbayCategoryDetailRequest transEbayCategoryDetailRequest(HaiYingEbayCategoryDetailCommand command);

    @Mapping(target = "station", source = "station", qualifiedByName = "haiyingStation2Str")
    HaiYingEbayTopCategoryInfoRequest transEbayTopCategoryRequest(HaiYingEbayTopCategoryInfoCommand command);

    @Mapping(target = "station", source = "station", qualifiedByName = "haiyingStation2Str")
    @Mapping(target = "item_ids", source = "item_ids", qualifiedByName = "list2StrWithComma")
    @Mapping(target = "item_location", source = "item_locations", qualifiedByName = "list2StrWithComma")
    @Mapping(target = "seller", source = "sellers", qualifiedByName = "list2StrWithComma")
    @Mapping(target = "store_location", source = "store_locations", qualifiedByName = "list2StrWithComma")
    @Mapping(target = "gen_time_start", source = "gen_time_start", qualifiedByName = "simpleDateLong2Str")
    @Mapping(target = "gen_time_end", source = "gen_time_end", qualifiedByName = "simpleDateLong2Str")
    @Mapping(target = "marketplace", source = "marketplaces", qualifiedByName = "list2StrWithComma")
    @Mapping(target = "last_modi_time_start", source = "last_modi_time_start", qualifiedByName = "simpleTimeLong2Str")
    @Mapping(target = "last_modi_time_end", source = "last_modi_time_end", qualifiedByName = "simpleTimeLong2Str")
    @Mapping(target = "current_page", source = "pageCondition", qualifiedByName = "pageCondition2CurrentPage")
    @Mapping(target = "page_size", source = "pageCondition", qualifiedByName = "pageCondition2PageSize")
    HaiYingEbayProductListRequest transEbayProductListRequest(HaiYingEbayProductListCommand command);

    @Mapping(target = "station", source = "station", qualifiedByName = "haiyingStation2Str")
    @Mapping(target = "item_ids", source = "item_ids", qualifiedByName = "list2StrWithComma")
    @Mapping(target = "main_his_date_start", source = "main_his_date_start", qualifiedByName = "simpleDateLong2Str")
    @Mapping(target = "main_his_date_end", source = "main_his_date_end", qualifiedByName = "simpleDateLong2Str")
    @Mapping(target = "sold_his_date_start", source = "sold_his_date_start", qualifiedByName = "simpleDateLong2Str")
    @Mapping(target = "sold_his_date_end", source = "sold_his_date_end", qualifiedByName = "simpleDateLong2Str")
    HaiYingEbayProductDetailInfoRequest transEbayProductDetailRequest(HaiYingEbayProductDetailInfoCommand command);



    @Mapping(target = "is_leaf", source = "is_leaf", qualifiedByName = "str2Boolean")
    HaiYingEbayCategoryTreeDTO transEbayCategoryTreeDTO(HaiYingEbayCategoryTreeResponse response);

    @Mapping(target = "is_leaf", source = "is_leaf", qualifiedByName = "str2Boolean")
    @Mapping(target = "stat_time", source = "stat_time", qualifiedByName = "simpleTimeStr2Long")
    HaiYingEbayCategoryDetailDTO transEbayCategoryDetailDTO(HaiYingEbayCategoryDetailResponse response);

    @Mapping(target = "stat_date", source = "stat_date", qualifiedByName = "simpleDateStr2Long")
    @Mapping(target = "ins_date", source = "ins_date", qualifiedByName = "simpleDateStr2Long")
    HaiYingEbayTopCategoryInfoDTO transEbayTopCategoryDTO(HaiYingEbayTopCategoryInfoResponse response);

    @Mapping(target = "cids", source = "cids", qualifiedByName = "str2ListWithComma")
    @Mapping(target = "last_modi_time", source = "last_modi_time", qualifiedByName = "simpleTimeStr2Long")
    @Mapping(target = "stat_time", source = "stat_time", qualifiedByName = "simpleTimeStr2Long")
    @Mapping(target = "gen_time", source = "gen_time", qualifiedByName = "simpleDateStr2Long")
    @Mapping(target = "sales_three_day_flag", source = "sales_three_day_flag", qualifiedByName = "str2Boolean")
    @Mapping(target = "category_structure", source = "category_structure", qualifiedByName = "str2ListWithBr")
    @Mapping(target = "main_images", source = "main_images", qualifiedByName = "str2ListWithComma")
    HaiYingEbayProductListDTO transEbayProductListDTO(HaiYingEbayProductListResponse response);

    @Mapping(target = "main_images", source = "main_images", qualifiedByName = "str2ListWithComma")
    @Mapping(target = "listed_time", source = "listed_time", qualifiedByName = "simpleTimeStr2Long")
    @Mapping(target = "last_modi_time", source = "last_modi_time", qualifiedByName = "simpleTimeStr2Long")
    @Mapping(target = "ended_time", source = "ended_time", qualifiedByName = "simpleTimeStr2Long")
    @Mapping(target = "not_exist", source = "not_exist", qualifiedByName = "str2Boolean")
    @Mapping(target = "category_structure", source = "category_structure", qualifiedByName = "str2ListWithSemicolon")
    HaiYingEbayProductDetailInfoDTO transEbayProductDetailDTO(HaiYingEbayProductDetailInfoResponse response);

}
