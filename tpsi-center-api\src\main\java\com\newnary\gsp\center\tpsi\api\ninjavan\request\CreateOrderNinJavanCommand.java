package com.newnary.gsp.center.tpsi.api.ninjavan.request;

import com.newnary.gsp.center.tpsi.api.ninjavan.vo.*;
import lombok.Data;

import java.util.List;

/**
 * 「创建」能者物流运输单，请求体
 *
 * <AUTHOR>
 * @since Created on 2023-08-11
 **/
@Data
public class CreateOrderNinJavanCommand {

    /**
     * 我们的系统将使用由能者物流客户经理提供的代码来识别改代码下的订单服务通道类型。
     * TODO 不传递
     **/
    private String serviceCode;

    /**
     * 您系统中的订单标识符。若国家代码为：VN和税号没提供，则必须填写订单编号
     **/
    private String sourceOrderId;
    private String sourceReferenceId;

    /**
     *  发件地。
     *  TODO 不传递
     **/
    private CreateNinJavanOrderFrom from;

    /**
     * 收件地
     */
    private CreateNinJavanOrderTo to;

    /**
     * 退件地址
     */
    private CreateNinJavanOrderReturn orderReturn;

    //TODO 最新确认，生成环境可以不填写-

    /**
     * 包裹详情
     */
    private CreateNinJavanOrderParcelDetail parcelDetails ;

    /**
     * 商品
     */
    private List<CreateNinJavanOrderItem> items;

    /**
     * cod 订单需要传递到付金额
     */
    private CreateNinJavanOrderDelivery delivery;

}
