package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.amazon;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingAmazonProductDetailInfoRequest {

    /**
     * 站点
     */
    @NotNull(message = "站点不能为空")
    private String station;

    /**
     * 商品id
     * (多个商品id用逗号分隔，最多100个商品id)
     */
    @NotNull(message = "商品id不能为空")
    private String asins;

    /**
     * 商品排名抓取时间起始值(string型,格式:年-月-日)
     */
    private String cate_rank_date_start;

    /**
     * 商品排名抓取时间结束值(string型,格式:年-月-日)
     */
    private String cate_rank_date_end;

    /**
     * 商品走势数据时间起始值(string型,格式:年-月-日)
     */
    private String asin_info_date_start;

    /**
     * 商品走势数据时间结束值(string型,格式:年-月-日)
     */
    private String asin_info_date_end;

}
