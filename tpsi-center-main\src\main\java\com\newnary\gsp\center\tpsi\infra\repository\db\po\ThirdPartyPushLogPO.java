package com.newnary.gsp.center.tpsi.infra.repository.db.po;

import com.newnary.dao.base.po.TenantBasePO;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ThirdPartyPushLogPO extends TenantBasePO {

    /** 事件类型 */
    private String eventType;
    /** 事件业务id */
    private String eventBizId;
    /** 事件处理数据 */
    private String eventData;
    /** 事件状态（必填） */
    private Integer eventState;
    /** 事件业务状态（非必填，按照业务类型自定义扩展） */
    private Integer eventBizState;
    /** 请求信息 */
    private String reqData;
    /** 响应信息 */
    private String respData;
    /** 系统备注 */
    private String sysRemark;
    /** 人工备注 */
    private String manualRemark;
}
