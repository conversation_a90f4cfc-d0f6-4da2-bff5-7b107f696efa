package com.newnary.gsp.center.tpsi.api.jt.enums;

import lombok.Getter;

/**
 * @Author: jack
 * @CreateTime: 2023-8-15
 */
@Getter
public enum JTOrderType {

    NORMAL_ORDER("普通订单","1"),
    REFUND_ORDER("退货订单","2");

    private String name;
    private String value;

    JTOrderType(String name, String value){
        this.name = name;
        this.value = value;
    }

    public String getValue(){
        return this.value;
    }

}
