package com.newnary.gsp.center.tpsi.app.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.gsp.center.crawlerproxy.api.crawler.request.AliProductDetailCommand;
import com.newnary.gsp.center.crawlerproxy.api.crawler.request.AliProductSearchCommand;
import com.newnary.gsp.center.crawlerproxy.api.crawler.response.AliProductCrawlerReturnInfo;
import com.newnary.gsp.center.product.api.product.request.SupplierSpuCreateV2Command;
import com.newnary.gsp.center.tpsi.infra.model.CrawlerProduct;
import com.newnary.gsp.center.tpsi.infra.model.creator.ProductCategoryInitMap;
import com.newnary.gsp.center.tpsi.infra.model.vo.CrawlerProductFlushState;
import com.newnary.gsp.center.tpsi.infra.model.vo.CrawlerProductReturnState;
import com.newnary.gsp.center.tpsi.infra.model.vo.CrawlerProductState;
import com.newnary.gsp.center.tpsi.infra.model.vo.ProductMappingInfo;
import com.newnary.gsp.center.tpsi.infra.rpc.CrawlerProductRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.ProductWarehousingRpc;
import com.newnary.gsp.center.tpsi.infra.translator.CrawlerReplyTranslator;
import com.newnary.gsp.center.tpsi.service.product.ISync1688ProductApiSve;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.job.core.handler.annotation.Job;
import com.newnary.tenant.context.TenantCarrier;
import com.newnary.tenant.context.TenantID;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
@Slf4j
public class ProductDetailsJobManager {

    @Resource
    private CrawlerProductRpc crawlerProductRpc;

    @Resource
    private ProductWarehousingRpc productWarehousingRpc;

    @Resource
    private ISync1688ProductApiSve i1688ProductApiSve;

    private final ProductCategoryInitMap categoryMap = new ProductCategoryInitMap();

    /**
     * 供应商id
     **/
    @Value("${tpsi-center.config.product1688Detail.supplier-id}")
    private String supplierId;

    /**
     * 仓库Id
     */
    @Value("${tpsi-center.config.product1688Detail.custom-warehouse-code}")
    private String customWarehouseCode;

    /**
     * 任务开关
     */
    @Value("${tpsi-center.config.product1688Detail.jobSwitch}")
    private boolean jobSwitch = true;

    @Value("${tpsi-center.config.product1688Detail.flushPriceAndInventory}")
    private boolean flushPriceAndInventory = true;

    /**
     * 本地线程池, 固定容量, 用于异步对商详落库操作等
     **/
    private final ThreadPoolExecutor productDetailExecutor = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors() * 12,
            Runtime.getRuntime().availableProcessors() * 12,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque<>(384),
            new ThreadPoolExecutor.CallerRunsPolicy());


    /**
     * 初始化所有类目任务，需先填充好类目对象 ProductCategoryInitMap
     */
    @Job("autoDoSyncAllCategoryNameProductId")
    public ReturnT<String> autoDoSyncAllCategoryNameProductId(String param) {
        log.info("1688商品类目 --->> 全量任务执行开始 --- {}", new Date());
        categoryMap.getMap().forEach((k, v) -> {
            Integer turnPage = (Integer.valueOf(v.split("-")[1]) / 20) + 1;
            String bizId = "USER6625304286001382166528-" + System.currentTimeMillis() + "-91e10022536835e470c88b4c513064b1";
            try {
                String params = "{" +
                        "\"keywords\": \"" + k + "\"," +
                        "\"beginPage\": 1," +
                        "\"pageSize\": 20," +
                        "\"sourceType\": \"ALI_1688\"," +
                        "\"psOptBizId\": \"" + bizId + "\"," +
                        "\"resultCount\": 100," +
                        "\"turnPage\":" + turnPage +
                        "}";
                autoDoSync1688ProductId(params);
                Thread.sleep(2000);
            } catch (InterruptedException e) {
//                e.printStackTrace();
                log.info("1688{}商品类目执行失败--->> 异常e:{} --- {}", k, e.getMessage(), new Date());
            }
        });
        log.info("1688商品类目 --->> 全量任务执行结束 --- {}", new Date());
        return ReturnT.SUCCESS;
    }

    //爬虫商品id Job
    @Job("autoDoSync1688ProductId")
    public ReturnT<String> autoDoSync1688ProductId(String param) {
        JSONObject paramObject = JSONObject.parseObject(param);
        String keywords = paramObject.getString("keywords");
        String psOptBizId = paramObject.getString("psOptBizId");
        Integer beginPage = paramObject.getInteger("beginPage");
        Integer resultCount = paramObject.getInteger("resultCount");
        Integer pageSize = paramObject.getInteger("pageSize");
        Integer turnPage = paramObject.getInteger("turnPage");
        log.info("阿里1688商品--{}类目商品ID入库,开始 --- {}", keywords, new Date());
        try {
            AliProductSearchCommand aliProductSearchCommand = new AliProductSearchCommand();
            aliProductSearchCommand.setPsOptBizId(psOptBizId);
            aliProductSearchCommand.setKeywords(keywords);
            aliProductSearchCommand.setResultCount(resultCount);
            aliProductSearchCommand.setPageSize(pageSize);
            for (int i = beginPage; i < beginPage + turnPage; i++) {
                aliProductSearchCommand.setBeginPage(i);
                List<String> productList = crawlerProductRpc.getProductId(aliProductSearchCommand);

                if (CollectionUtils.isNotEmpty(productList)) {
                    i1688ProductApiSve.storeProductIds(productList, keywords, i, pageSize);
                }
            }
        } catch (Exception e) {
            log.error("阿里1688商品--{}类目ID商品入库失败,异常原因:{},结束 --- {}", keywords, e.getMessage(), new Date());
            return ReturnT.FAIL;
        }
        log.info("阿里1688商品--{}类目商品ID入库成功,结束 --- {}", keywords, new Date());
        return ReturnT.SUCCESS;
    }

    //爬取商品详情信息job
    @Job("autoDoSyncWarehousingProductDetail")
    public ReturnT autoDoSyncWarehousingProductDetail(String param) {
        JSONObject paramObject = JSONObject.parseObject(param);
        String categoryName = paramObject.getString("categoryName");
        List<String> categoryList = Arrays.stream(categoryName.split("-")).filter(s -> StringUtils.isNotBlank(s)).collect(Collectors.toList());
        Integer limit = paramObject.getInteger("limit");
        log.info("阿里1688商品--{}类目商品id入库,开始 --- {}", categoryList, new Date());
        try {
            //查询需要入库商品id
            List<CrawlerProduct> crawlerProductList = i1688ProductApiSve.loadCrawlerProductByCategoryList(categoryList, limit, null, CrawlerProductState.UN_WAREHOUSED.getState(),null);

            Integer max_send = 20;
            int limits = (crawlerProductList.size() + max_send - 1) / max_send;

            //分组 --> 避免爬虫压力负载过大
            List<List<CrawlerProduct>> crawlerProductPage = Stream.iterate
                    (0, n -> n + 1).limit(limits).parallel().map
                    (a -> crawlerProductList.stream().skip(a * max_send).limit(max_send).parallel().collect(Collectors.toList())).collect(Collectors.toList());


            for (List<CrawlerProduct> crawlerProducts : crawlerProductPage) {
                if (jobSwitch) {
                    log.info("商品id--{}商品详情入库 , 开始 --- {}", crawlerProducts.stream().map(CrawlerProduct::getProductId).collect(Collectors.toList()), new Date());
                    warehousingProductDetail(crawlerProducts);
                    Thread.sleep(5000);
                } else {
                    //stop当前任务
                    log.info("手动停止当前入库任务----{}", new Date());
                    break;
                }
            }

        } catch (Exception e) {
//            e.printStackTrace();
            log.info("阿里1688商品 --- {}类目商品id详情入库异常{} --- {} --- ", categoryName, e.getMessage(), new Date());
        }
        log.info("阿里1688商品 --- {}类目商品id详情入库结束 --- {}", categoryName, new Date());
        return ReturnT.SUCCESS;
    }

    //爬取商品详情信息job
    @Job("autoDoSyncWarehousingCrawlerReturn")
    public ReturnT autoDoSyncWarehousingCrawlerReturn(String param) {
        JSONObject paramObject = JSONObject.parseObject(param);
        String categoryName = paramObject.getString("categoryName");
        List<String> categoryList = Arrays.stream(categoryName.split("-")).filter(s -> StringUtils.isNotBlank(s)).collect(Collectors.toList());
        Integer limit = paramObject.getInteger("limit");
        Integer crawlerReturnState = CrawlerProductReturnState.NONE_DETAIL_RETURN.getDetailState();
        log.info("阿里1688商品--{}类目商品详情id填充,开始 --- {}", categoryList, new Date());
        try {
            //查询需要入库商品id
            List<CrawlerProduct> crawlerProductList = i1688ProductApiSve.loadCrawlerProductByCategoryList(categoryList, limit, crawlerReturnState, CrawlerProductState.UN_WAREHOUSED.getState(),null);
            Integer max_send = 10;
            int limits = (crawlerProductList.size() + max_send - 1) / max_send;

            //分组 --> 避免爬虫压力负载过大
            List<List<CrawlerProduct>> crawlerProductPage = Stream.iterate
                    (0, n -> n + 1).limit(limits).parallel().map
                    (a -> crawlerProductList.stream().skip(a * max_send).limit(max_send).parallel().collect(Collectors.toList())).collect(Collectors.toList());

            for (List<CrawlerProduct> crawlerProducts : crawlerProductPage) {
                if (jobSwitch) {
                    log.info("商品id--{}商品详情填充 , 开始 --- {}", crawlerProducts.stream().map(CrawlerProduct::getProductId).collect(Collectors.toList()), new Date());
                    warehousingCrawlerReturnDetail(crawlerProducts);
                    Thread.sleep(3000);
                } else {
                    //stop当前任务
                    log.info("手动停止当前商品详情填充任务----{}", new Date());
                    break;
                }
            }

        } catch (Exception e) {
//            e.printStackTrace();
            log.info("阿里1688商品 --- {}类目商品id详情填充异常{} --- {} --- ", categoryName, e.getMessage(), new Date());
        }
        log.info("阿里1688商品 --- {}类目商品id详情填充结束 --- {}", categoryName, new Date());
        return ReturnT.SUCCESS;
    }

    @Job("autoDoChangePriceAndSetInventory")
    public ReturnT autoDoChangePriceAndSetInventory(String param) throws InterruptedException {
        JSONObject paramObject = JSONObject.parseObject(param);
        String categoryName = paramObject.getString("categoryName");
        List<String> categoryList = Arrays.stream(categoryName.split("-")).filter(s -> StringUtils.isNotBlank(s)).collect(Collectors.toList());
        Integer limit = paramObject.getInteger("limit");
        log.info("阿里1688商品--{}类目商品,刷新价格以及库存,任务开始 --- {}", categoryList, new Date());
        List<CrawlerProduct> crawlerProductList = i1688ProductApiSve.loadCrawlerProductByCategoryList(categoryList, limit, CrawlerProductReturnState.ALREADY_DETAIL_RETURN.getDetailState(), CrawlerProductState.SUCCESS_WAREHOUSED.getState(), CrawlerProductFlushState.NONE_FLUSH.getFlushState());
        Integer max_send = 20;
        int limits = (crawlerProductList.size() + max_send - 1) / max_send;

        //分组 --> 避免爬虫压力负载过大
        List<List<CrawlerProduct>> crawlerProductPage = Stream.iterate
                (0, n -> n + 1).limit(limits).parallel().map
                (a -> crawlerProductList.stream().skip(a * max_send).limit(max_send).parallel().collect(Collectors.toList())).collect(Collectors.toList());

        for (List<CrawlerProduct> crawlerProducts : crawlerProductPage) {
            if (flushPriceAndInventory) {
                log.info("商品id--{}商品改价以及更新库存 , 开始 --- {}", crawlerProducts.stream().map(CrawlerProduct::getProductId).collect(Collectors.toList()), new Date());
                parallelChangePriceAndSetInventory(crawlerProducts);
                Thread.sleep(2000);
            } else {
                //stop当前任务
                log.info("手动停止当前 刷新价格以及库存 任务----{}", new Date());
                break;
            }
        }
        log.info("商品改价以及更新库存 , 结束 --- {}", new Date());

        return ReturnT.SUCCESS;
    }


    private void warehousingCrawlerReturnDetail(List<CrawlerProduct> crawlerProducts) {
        final Optional<TenantID> tenantIdKept = TenantCarrier.getTenantID();
        crawlerProducts.forEach(crawlerProduct -> {
            CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> {
                if (!jobSwitch) {
                    //线程停止操作
                    return;
                }
                boolean tenantIdMiss = false;
                SupplierSpuCreateV2Command createCommand = new SupplierSpuCreateV2Command();
                ProductMappingInfo mappingInfo = new ProductMappingInfo();
                try {
                    if (!TenantCarrier.getTenantID().isPresent()) {
                        TenantID tenantID = (TenantID) tenantIdKept.orElse(null);
                        TenantCarrier.setTenantID(tenantID);
                        tenantIdMiss = tenantID != null;
                    }
                    //爬虫发起
                    AliProductDetailCommand command = new AliProductDetailCommand();
                    command.setProductId(crawlerProduct.getProductId());
                    AliProductCrawlerReturnInfo crawlerReturnInfo = crawlerProductRpc.getProductDetail(command);
                    if (!StringUtils.isEmpty(crawlerReturnInfo.getCrawlerReturn())) {
                        // 将返回结果值落库
                        crawlerProduct.setCrawlerReturn(crawlerReturnInfo.getCrawlerReturn());
                        crawlerProduct.storeProductDetail();
                    } else {
                        if (!StringUtils.isEmpty(crawlerReturnInfo.getErrorMessage()) &&
                                (StringUtils.equals(crawlerReturnInfo.getErrorMessage(), "已不存在") || StringUtils.equals(crawlerReturnInfo.getErrorMessage(), "新版字段"))) {
                            //排除掉无效商品
                            crawlerProduct.setFailReason(crawlerReturnInfo.getErrorMessage());
                            crawlerProduct.failProductDetail();
                        }
                    }
                } finally {
                    if (tenantIdMiss) {
                        TenantCarrier.clearTenantID();
                    }
                }
            }, productDetailExecutor);
//            try {
//                CompletableFuture.allOf(voidCompletableFuture).get();
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            } catch (ExecutionException e) {
//                e.printStackTrace();
//            }
        });

    }


    private void warehousingProductDetail(List<CrawlerProduct> crawlerProducts) {
        final Optional<TenantID> tenantIdKept = TenantCarrier.getTenantID();
        crawlerProducts.forEach(crawlerProduct -> {
            CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> {
                if (!jobSwitch) {
                    //线程停止操作
                    return;
                }
                boolean tenantIdMiss = false;
                SupplierSpuCreateV2Command createCommand = new SupplierSpuCreateV2Command();
                ProductMappingInfo mappingInfo = new ProductMappingInfo();
                try {
                    if (!TenantCarrier.getTenantID().isPresent()) {
                        TenantID tenantID = (TenantID) tenantIdKept.orElse(null);
                        TenantCarrier.setTenantID(tenantID);
                        tenantIdMiss = tenantID != null;
                    }
                    if (crawlerProduct.getCrawlerReturnState()== CrawlerProductReturnState.ALREADY_DETAIL_RETURN.getDetailState()) {
                        warehousing(createCommand, crawlerProduct, mappingInfo,
                                i1688ProductApiSve.loadCrawlerDetailReturn(crawlerProduct.getProductId()).getCrawlerReturn());
                    } else {
                        //爬虫发起
                        AliProductDetailCommand command = new AliProductDetailCommand();
                        command.setProductId(crawlerProduct.getProductId());
                        AliProductCrawlerReturnInfo crawlerReturnInfo = crawlerProductRpc.getProductDetail(command);
                        if (!StringUtils.isEmpty(crawlerReturnInfo.getCrawlerReturn())) {
                            // 将返回结果值落库
                            crawlerProduct.setCrawlerReturn(crawlerReturnInfo.getCrawlerReturn());
                            crawlerProduct.storeProductDetail();
                            //进行落库操作
                            warehousing(createCommand, crawlerProduct, mappingInfo, crawlerReturnInfo.getCrawlerReturn());
                        } else {
                            if (!StringUtils.isEmpty(crawlerReturnInfo.getErrorMessage()) &&
                                    (StringUtils.equals(crawlerReturnInfo.getErrorMessage(), "已不存在") || StringUtils.equals(crawlerReturnInfo.getErrorMessage(), "新版字段"))) {
                                //排除掉无效商品
                                crawlerProduct.setFailReason(crawlerReturnInfo.getErrorMessage());
                                crawlerProduct.failProductDetail();
                            }
                        }
                    }
                } catch (RetryableException e) {
                    //无法控制超时异常时，进行改价和设库存后置补救，或者再写一个job进行后补
                    remedialStrategy(crawlerProduct, createCommand, mappingInfo, CrawlerProductState.SUCCESS_WAREHOUSED.getState(),null,null);
                } finally {
                    if (tenantIdMiss) {
                        TenantCarrier.clearTenantID();
                    }
                }
            }, productDetailExecutor);
//            try {
//                CompletableFuture.allOf(voidCompletableFuture).get();
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            } catch (ExecutionException e) {
//                e.printStackTrace();
//            }
        });
    }


    private void parallelChangePriceAndSetInventory(List<CrawlerProduct> crawlerProductList) {
        final Optional<TenantID> tenantIdKept = TenantCarrier.getTenantID();
        crawlerProductList.forEach(crawlerProduct -> {
            CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> {
                if (!flushPriceAndInventory) {
                    //线程停止操作
                    return;
                }
                boolean tenantIdMiss = false;
                SupplierSpuCreateV2Command createCommand = new SupplierSpuCreateV2Command();
                ProductMappingInfo mappingInfo = new ProductMappingInfo();
                try {
                    if (!TenantCarrier.getTenantID().isPresent()) {
                        TenantID tenantID = (TenantID) tenantIdKept.orElse(null);
                        TenantCarrier.setTenantID(tenantID);
                        tenantIdMiss = tenantID != null;
                    }
                    String crawlerReturn = i1688ProductApiSve.loadCrawlerDetailReturn(crawlerProduct.getProductId()).getCrawlerReturn();
                    if (StringUtils.isNotBlank(crawlerReturn)) {
                        CrawlerReplyTranslator.fill1688ProductDetailsReply(createCommand, JSON.parseObject(crawlerReturn), crawlerProduct.getProductId(), mappingInfo, supplierId);
                        //重置价格以及库存
                        remedialStrategy(crawlerProduct, createCommand, mappingInfo,null,CrawlerProductFlushState.ALREADY_FLUSH.getFlushState(),crawlerProduct.getFlushVersion()+1);
                    }

                } finally {
                    if (tenantIdMiss) {
                        TenantCarrier.clearTenantID();
                    }
                }
            }, productDetailExecutor);
//            try {
//                CompletableFuture.allOf(voidCompletableFuture).get();
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            } catch (ExecutionException e) {
//                e.printStackTrace();
//            }
        });

    }

    private void remedialStrategy(CrawlerProduct crawlerProduct, SupplierSpuCreateV2Command createCommand, ProductMappingInfo mappingInfo,Integer state, Integer flushState,Integer flushVersion) {
        productWarehousingRpc.changePriceAndSetInventory(mappingInfo, createCommand, supplierId, customWarehouseCode);
        i1688ProductApiSve.updateProductState(crawlerProduct.getProductId(),state, flushState,flushVersion);
    }

    private void warehousing(SupplierSpuCreateV2Command createCommand, CrawlerProduct crawlerProduct, ProductMappingInfo mappingInfo, String productDetail) {
        Optional<String> categoryName = Optional.ofNullable(categoryMap.getMap().get(crawlerProduct.getCategoryName()));
        categoryName.ifPresent(cate -> {
            createCommand.setCategoryId(cate.split("-")[0]);
        });
        CrawlerReplyTranslator.fill1688ProductDetailsReply(createCommand, JSON.parseObject(productDetail), crawlerProduct.getProductId(), mappingInfo, supplierId);
        productWarehousingRpc.productWarehousing(crawlerProduct, mappingInfo, createCommand, supplierId, customWarehouseCode);

        //修改成功状态
        i1688ProductApiSve.updateProductState(crawlerProduct.getProductId(), CrawlerProductState.SUCCESS_WAREHOUSED.getState(),null,null);
    }

}


