package com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.order;

import lombok.Data;

/**
 * 订单查询接口必须传时间范围参数（updateTimeStart／paidtimeStart／createDateStart／expressTimeStart）才可以获取数据
 */
@Data
public class MaBangGetOrderList {

    /**
     * 1.待处理 2.配货中 3.已发货 4.已完成 5.已作废 6.所有未发货 7.所有非未发货 默认配货中订单
     */
    private String status;

    /**
     * 订单编号，多个用英文逗号隔开，最大支持10个
     */
    private String platformOrderIds;

    /**
     * 店铺名称
     */
    private String shopName;

    private String updateTimeStart;

    private String updateTimeEnd;

    private String paidtimeStart;

    private String paidtimeEnd;

    /**
     * 创建时间开始时间
     */
    private String createDateStart;

    private String createDateEnd;

    private String expressTimeStart;

    private String expressTimeEnd;

    private String canSend;

    private Integer page;
}
