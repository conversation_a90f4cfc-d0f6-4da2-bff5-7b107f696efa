package com.newnary.gsp.center.tpsi.app.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.common.PageCondition;
import com.newnary.api.base.common.PageList;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.common.utils.locale.LanguageLocaleType;
import com.newnary.distributed.tools.concurrent.template.DConcurrentTemplate;
import com.newnary.gsp.center.product.api.category.common.CategoryLevel;
import com.newnary.gsp.center.product.api.category.response.CategoryInfo;
import com.newnary.gsp.center.product.api.common.dto.MultimediaInfo;
import com.newnary.gsp.center.product.api.common.dto.MultimediaType;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierAdjustSupplyPriceReq;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierItemCreateCombineDetailInfo;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierUpdateStock4BatchReq;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierUpdateStockReq;
import com.newnary.gsp.center.product.api.product.request.*;
import com.newnary.gsp.center.product.api.product.vo.SupplierSpuDescInfo;
import com.newnary.gsp.center.product.api.product.vo.SupplierSpuParamInfo;
import com.newnary.gsp.center.product.api.product.vo.SupplierSpuParamValueInfo;
import com.newnary.gsp.center.product.api.product.vo.SupplierSpuParamsInfo;
import com.newnary.gsp.center.tpsi.infra.client.ejingling.params.EJingLingDataParam;
import com.newnary.gsp.center.tpsi.infra.client.ejingling.utils.DateUtils;
import com.newnary.gsp.center.tpsi.infra.client.ejingling.valobj.request.EJingLingFullGoodsListReq;
import com.newnary.gsp.center.tpsi.infra.client.ejingling.valobj.response.EJingLingGoodsListResponse;
import com.newnary.gsp.center.tpsi.infra.client.vvic.params.VVICBaseParam;
import com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.response.VVICGetItemDetialResponse;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingInfo;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingType;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.ThirdPartyMappingManager;
import com.newnary.gsp.center.tpsi.infra.rpc.CategoryRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.OpenSupplierProductRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.SupplierSkuRpc;
import com.newnary.gsp.center.tpsi.service.ejingling.IEJingLingApiSev;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.job.core.handler.annotation.Job;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
@Slf4j
public class EJingLingJobManager {
    @Resource
    private IEJingLingApiSev eJingLingApiSevImpl;

    @Resource
    private SupplierSkuRpc supplierSkuRpc;

    @Resource
    private OpenSupplierProductRpc openSupplierProductRpc;

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Resource
    private ThirdPartyMappingManager thirdPartyMappingManager;

    @Resource
    private CategoryRpc categoryRpc;

    @Job("autoCreateEJingLingProduct")
    public ReturnT<String> createEJingLingProduct(String param) {
        DConcurrentTemplate.tryLockMode(
                "EJINGLING:FETCH_PRODUCT",
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    log.info("ejingling-获取衫海精商品同步到供应商后台 定时任务开始, param={}", param);
                    JSONObject paramObject = JSON.parseObject(param);
                    String thirdPartySystemId = paramObject.getString("thirdPartySystemId");
                    long timeStamp = Long.parseLong(String.valueOf(paramObject.get("time"))) * 60000;
                    long nowStamp = new Date().getTime();
                    String dateString = DateUtils.timeStamp2DateString("yyyy-MM-dd HH:mm:ss", nowStamp - timeStamp);
                    //根据thirdPartySystemId获取第三方系统参数
                    ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
                    //EJingLingDataParam eJingLingDataParam = JSON.parseObject(thirdPartySystem.getParams(), EJingLingDataParam.class);

/*                    if (null == thirdPartySystem) {
                        log.error("第三方系统参数为空，param {}", param);
                        throw new ServiceException(Status.error(new BaseErrorInfo("500", "第三方系统参数为空"), "thirdPartySystem", "createVVICProduct"));
                    }*/
                    //获取EJ商品
                    EJingLingFullGoodsListReq eJingLingFullGoodsListReq = new EJingLingFullGoodsListReq();
                    eJingLingFullGoodsListReq.setSize(500);
                    int page = 0;
                    eJingLingFullGoodsListReq.setCreatedTime(dateString);
                    EJingLingGoodsListResponse eJingLingGoodsListResponse = new EJingLingGoodsListResponse();
                    do {
                        try {
                            eJingLingFullGoodsListReq.setPage(page += 1);
                            eJingLingGoodsListResponse = eJingLingApiSevImpl.fullGoodsList(thirdPartySystemId, eJingLingFullGoodsListReq);
                            if (null != eJingLingGoodsListResponse) {
                                List<EJingLingGoodsListResponse.OuterGoodsVo> outerGoodsVoList = eJingLingGoodsListResponse.getOuterGoodsVoList();
                                if (CollectionUtils.isNotEmpty(outerGoodsVoList)) {
                                    Map<String, ThirdPartyMappingInfo> categoryMapping = thirdPartyMappingManager.batchGetIdMappingInfoByTargetIds("GSP", "EJINGLING", outerGoodsVoList.stream().map(item -> String.valueOf(item.getPlatformCategoryId())).distinct().collect(Collectors.toList()), ThirdPartyMappingType.CATEGORY);
                                    if (null == categoryMapping || categoryMapping.size() == 0) {
                                        log.error("类目映射map为空，categoryMapping：{}", categoryMapping);
                                        continue;
                                    }
                                    outerGoodsVoList.forEach(vo -> {
                                        ThirdPartyMappingInfo newestInfoByTarget = thirdPartyMappingManager.getNewestInfoByTarget("EJINGLING", "GSP", String.valueOf(vo.getGoodsId()), ThirdPartyMappingType.PRODUCT_ID);
                                        if (ObjectUtils.isEmpty(newestInfoByTarget)) {
                                            try {
                                                String supplierProductSpuId = openSupplierProductRpc.createSpu(buildCreateSpuReq(thirdPartySystem, categoryMapping, vo));
                                                //创建成功更新商品库存
                                                updateStock(thirdPartySystem, vo);
                                                //更新商品价格
                                                updatePrice(thirdPartySystem, vo);
                                                //存一个商品映射信息
                                                thirdPartyMappingManager.insertOrUpdate("GSP", "EJINGLING", supplierProductSpuId, String.valueOf(vo.getGoodsId()), ThirdPartyMappingType.PRODUCT_ID.name(), vo);
                                            } catch (Exception e) {
                                                //e.printStackTrace();
                                                log.error("创建EJINGLING供应商商品失败 provider{} supplierId{} message{}", thirdPartySystem.getProvider(), JSON.parseObject(thirdPartySystem.getParams()).get("supplierId"), e.getMessage());
                                            }
                                        } /*else {
                                        //创建成功更新商品库存
                                        updateStock(thirdPartySystem, vo);
                                        //更新商品价格
                                        updatePrice(thirdPartySystem, vo);
                                    }*/
                                    });
                                }
                            }
                        } catch (Exception e) {
                            log.error("autoCreateEJingLingProduct: {}", e.getMessage());
                        }
                    } while (eJingLingGoodsListResponse != null && eJingLingGoodsListResponse.getLastPage() != null && !"1".equals(eJingLingGoodsListResponse.getLastPage()));
                }
        );

        return ReturnT.SUCCESS;
    }

    //构建创建供应商SPU请求参数
    public SupplierSpuCreateV2Command buildCreateSpuReq(ThirdPartySystem thirdPartySystem, Map<String, ThirdPartyMappingInfo> categoryMapping, EJingLingGoodsListResponse.OuterGoodsVo vo) {
        SupplierSpuCreateV2Command supplierSpuCreateV2Command = new SupplierSpuCreateV2Command();
        EJingLingDataParam param = JSON.parseObject(thirdPartySystem.getParams(), EJingLingDataParam.class);
        //供应商id
        supplierSpuCreateV2Command.setSupplierId(param.getSupplierId());
        //spu描述信息
        supplierSpuCreateV2Command.setDescInfos(buildSpuDescInfo(vo));
        //默认语言
        //supplierSpuCreateV2Command.setDefaultLocale(LanguageLocaleType.en_US);
        supplierSpuCreateV2Command.setDefaultLocale(LanguageLocaleType.zh_CN);
        //商家自定义spu编码
        supplierSpuCreateV2Command.setCustomCode(String.valueOf(vo.getGoodsId()));
        //品牌
        supplierSpuCreateV2Command.setBrandId(param.getBrandId());
        //设置类目id和类目等级
        setCategoryInfo(supplierSpuCreateV2Command,
                categoryMapping,
                vo);
        //商品主图列表
        List<MultimediaInfo> multimediaInfos = new ArrayList<>();
        MultimediaInfo mainImage = new MultimediaInfo();
        mainImage.setFileUrl(vo.getMainPicUrl());
        mainImage.setType(MultimediaType.IMAGE);
        multimediaInfos.add(mainImage);
        supplierSpuCreateV2Command.setMainImages(multimediaInfos);
        //明细图片
        //List<MultimediaInfo> detailImageList = new ArrayList<>();
        EJingLingGoodsListResponse.GoodsPic goodsPic = vo.getGoodsPicList();
        if (ObjectUtils.isNotEmpty(goodsPic)) {
            if (StringUtils.isNotBlank(goodsPic.getMainPics())) {
                String imageUrls = goodsPic.getMainPics();
                String[] split = imageUrls.split(",");
                for (String imageUrl : split) {
                    MultimediaInfo detailImage = new MultimediaInfo();
                    detailImage.setFileUrl(imageUrl);
                    detailImage.setType(MultimediaType.IMAGE);
                    multimediaInfos.add(detailImage);
                }
            }
        }
        //supplierSpuCreateV2Command.setDetailImages(detailImageList);
        //skuList
        supplierSpuCreateV2Command.setSkuList(buildSkuList(vo));

        //类目参数
        buildParamsInfo(supplierSpuCreateV2Command, vo);
        //自动审核通过
        supplierSpuCreateV2Command.setIsAutoAuditPass(true);

        //操作人
        supplierSpuCreateV2Command.setOperator("tpsi");

        return supplierSpuCreateV2Command;
    }

    //构建商品规格信息
    private List<SupplierSpuDescInfo> buildSpuDescInfo(EJingLingGoodsListResponse.OuterGoodsVo vo) {
        SupplierSpuDescInfo supplierSpuDescInfo = new SupplierSpuDescInfo();
        //语言
        supplierSpuDescInfo.setLocale(LanguageLocaleType.zh_CN);
        //标题
        supplierSpuDescInfo.setTitle(vo.getGoodsName());
        //描述
        EJingLingGoodsListResponse.GoodsPic goodsPicList = vo.getGoodsPicList();
        if (ObjectUtils.isNotEmpty(goodsPicList) && StringUtils.isNotBlank(goodsPicList.getHtmlUrl())) {
            supplierSpuDescInfo.setTextDesc(goodsPicList.getHtmlUrl());
        }

        //商家类目名称
        supplierSpuDescInfo.setCustomCategoryName(vo.getCategoryName());

        //翻译来源类型
        supplierSpuDescInfo.setTransSourceType("中文");
        //翻译提供者
        supplierSpuDescInfo.setTransProvider("ejingling");
        //翻译基准语言
        supplierSpuDescInfo.setTransBaseLocate(LanguageLocaleType.zh_CN);
        List<SupplierSpuDescInfo> descInfos = new ArrayList<>();
        descInfos.add(supplierSpuDescInfo);
        return descInfos;
    }

    //构建商品类目信息
    private void setCategoryInfo(SupplierSpuCreateV2Command supplierSpuCreateV2Command, Map<String, ThirdPartyMappingInfo> categoryMapping, EJingLingGoodsListResponse.OuterGoodsVo vo) {
        //String path = item.getCategory_name_one().concat("/".concat(item.getCategory_name_sub()).concat("/").concat(item.getCategory_name_two()));
        try {
            ThirdPartyMappingInfo thirdPartyMappingInfo = categoryMapping.get(String.valueOf(vo.getPlatformCategoryId()));
            CategoryInfo categoryInfo = categoryRpc.getCategoryById(thirdPartyMappingInfo.getSourceId());
            if (ObjectUtils.isNotEmpty(categoryInfo)) {
                supplierSpuCreateV2Command.setCategoryId(categoryInfo.getCategoryId());
                supplierSpuCreateV2Command.setMgmtCategoryLevel(CategoryLevel.getByValue(categoryInfo.getCategoryLevel()));
            }
        } catch (Exception e) {
            log.error("EJINGLING商品设置类目失败，goodId: {} categorlyMapping: {} targetId: {}", vo.getGoodsId(), JSON.toJSONString(categoryMapping), vo.getPlatformCategoryId());
        }


    }

    //构建商品sku列表
    private List<SupplierSkuCreateInfo> buildSkuList(EJingLingGoodsListResponse.OuterGoodsVo vo) {
        List<SupplierSkuCreateInfo> skuList = new ArrayList<>();
        vo.getSkuList().forEach(sku -> {
            SupplierSkuCreateInfo supplierSkuCreateInfo = new SupplierSkuCreateInfo();
            supplierSkuCreateInfo.setCustomCode(String.valueOf(sku.getSkuId()));
            //设置主规格信息
            SupplierSkuMainSpecInfo supplierSkuMainSpecInfo = new SupplierSkuMainSpecInfo();
            MultimediaInfo multimediaInfo = new MultimediaInfo();
            multimediaInfo.setType(MultimediaType.IMAGE);
            multimediaInfo.setFileUrl(sku.getPicUrl());
            supplierSkuMainSpecInfo.setImage(multimediaInfo);
            supplierSkuCreateInfo.setMainSpecInfo(supplierSkuMainSpecInfo);
            //设置规格参数
            List<SupplierSkuSpecInfo> specs = new ArrayList<>();
            String[] specArray = sku.getGoodsSpec().split(";");
            List<String> list2 = new ArrayList<>();
            Collections.addAll(list2, specArray);
            if (list2.contains("颜色") || list2.contains("颜色分类")) {
                List<String> collect = list2.stream().filter(s -> s.contains("颜色") || s.contains("颜色分类")).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    SupplierSkuSpecInfo supplierSkuSpecInfo = new SupplierSkuSpecInfo();
                    String[] split = collect.get(0).split(":");
                    supplierSkuSpecInfo.setSpecName(split[0]);
                    supplierSkuSpecInfo.setSpecValue(split[1]);
                    supplierSkuMainSpecInfo.setSpec(supplierSkuSpecInfo);
                }
            }
            for (String s : specArray) {
                String[] split = s.split(":");
                SupplierSkuSpecInfo supplierSkuSpecInfo = new SupplierSkuSpecInfo();
                supplierSkuSpecInfo.setSpecName(split[0]);
                supplierSkuSpecInfo.setSpecValue(split[1]);
                specs.add(supplierSkuSpecInfo);
                if (null == supplierSkuMainSpecInfo.getSpec()) {
                    supplierSkuMainSpecInfo.setSpec(supplierSkuSpecInfo);
                }
            }
            supplierSkuCreateInfo.setSpecs(specs);

            //最小起订量
            supplierSkuCreateInfo.setMoq(1);

            //售价
            supplierSkuCreateInfo.setLowestRetailPrice(new BigDecimal(String.valueOf(sku.getSkuSalePrice())));
            skuList.add(supplierSkuCreateInfo);
        });
        return skuList;

    }

    //构建产品参数信息
    private static void buildParamsInfo(SupplierSpuCreateV2Command supplierSpuCreateV2Command, EJingLingGoodsListResponse.OuterGoodsVo vo) {
        SupplierSpuParamsInfo supplierSpuParamsInfo = new SupplierSpuParamsInfo();
        List<SupplierSpuParamInfo> customParams = new ArrayList<>();

        String businessProps = vo.getBusinessProps();
        if (StringUtils.isNotBlank(businessProps)) {
            String[] attrs = businessProps.split(";");
            for (String attr : attrs) {
                if (StringUtils.isNotBlank(attr)) {
                    String[] split = attr.split(":");
                    if (split.length == 4) {
                        if (StringUtils.isNotBlank(split[2]) && StringUtils.isNotBlank(split[3])) {
                            SupplierSpuParamInfo supplierSpuParamInfo = new SupplierSpuParamInfo();
                            supplierSpuParamInfo.setParamName(split[2]);
                            List<SupplierSpuParamValueInfo> values = new ArrayList<>();
                            SupplierSpuParamValueInfo supplierSpuParamValueInfo = new SupplierSpuParamValueInfo();
                            supplierSpuParamValueInfo.setParamValue(split[3]);
                            values.add(supplierSpuParamValueInfo);
                            supplierSpuParamInfo.setValues(values);
                            customParams.add(supplierSpuParamInfo);
                        }

                    }
                }
            }
        }
        supplierSpuParamsInfo.setCustomParams(customParams);
        supplierSpuCreateV2Command.setParamsInfo(supplierSpuParamsInfo);
    }

    @Job("autoUpdateEJingLingProductStock")
    public ReturnT<String> updateEJingLingProductStock(String param) {
        DConcurrentTemplate.tryLockMode(
                "EJINGLING:FETCH_PRODUCT_STOCK",
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    log.info("vvic-获取衫海精商品库存同步到供应商后台 定时任务开始, param={}", param);
                    JSONObject paramObject = JSON.parseObject(param);
                    String thirdPartySystemId = paramObject.getString("thirdPartySystemId");

                    //根据thirdPartySystemId获取第三方系统参数
                    ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
                    EJingLingDataParam eJingLingDataParam = JSON.parseObject(thirdPartySystem.getParams(), EJingLingDataParam.class);
                    String supplierId = eJingLingDataParam.getSupplierId();

                    //使用供应商id查询商品
                    SupplierSkuPageQueryCommand supplierSkuPageQueryCommand = new SupplierSkuPageQueryCommand();
                    supplierSkuPageQueryCommand.setSupplierId(supplierId);
                    PageList<ThirdPartyMappingInfo> pageResult;
                    int page = 0;
                    do {
                        pageResult = thirdPartyMappingManager.getNewestInfoByTargetBizId("EJINGLING", "GSP", ThirdPartyMappingType.PRODUCT_ID, new PageCondition(page += 1, 200));
                        if (null != pageResult && CollectionUtils.isNotEmpty(pageResult.getItems())) {
                            List<List<ThirdPartyMappingInfo>> lists = groupListByQuantity(pageResult.getItems(), 40);
                            //根据商品skuid查询衫海精商品明细
                            lists.forEach(list -> {
                                StringBuilder stringBuilder = new StringBuilder();
                                list.forEach(thirdPartyMappingInfo -> {
                                    stringBuilder.append(thirdPartyMappingInfo.getTargetId());
                                    stringBuilder.append(",");
                                });
                                List<EJingLingGoodsListResponse.OuterGoodsVo> goodsByGoods = eJingLingApiSevImpl.getGoodsByGoodsId(thirdPartySystemId, stringBuilder.toString());
                                //根据商品明细库存更新供应商sku库存
                                if (CollectionUtils.isNotEmpty(goodsByGoods)) {
                                    batchUpdateStock(thirdPartySystem, goodsByGoods);
                                }
                            });
                        }
                    } while (null != pageResult && CollectionUtils.isNotEmpty(pageResult.getItems()));
                }
        );
        return ReturnT.SUCCESS;
    }

    @Job("autoUpdateEJINGLINGProductPrice")
    public ReturnT<String> updateEJINGLINGProductPrice(String param) {
        DConcurrentTemplate.tryLockMode(
                "EJINGLING:FETCH_PRODUCT_PRICE",
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    log.info("vvic-获取衫海精商品价格同步到供应商后台 定时任务开始, param={}", param);
                    JSONObject paramObject = JSON.parseObject(param);
                    String thirdPartySystemId = paramObject.getString("thirdPartySystemId");

                    //根据thirdPartySystemId获取第三方系统参数
                    ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
                    EJingLingDataParam eJingLingDataParam = JSON.parseObject(thirdPartySystem.getParams(), EJingLingDataParam.class);
                    String supplierId = eJingLingDataParam.getSupplierId();

                    //根据供应商id查询供应商商品
                    SupplierSkuSearchDetailsCommand supplierSkuSearchDetailsCommand = new SupplierSkuSearchDetailsCommand();
                    supplierSkuSearchDetailsCommand.setSupplierId(supplierId);
                    //使用供应商id查询商品
                    PageList<ThirdPartyMappingInfo> pageResult;
                    int page = 0;
                    do {
                        pageResult = thirdPartyMappingManager.getNewestInfoByTargetBizId("EJINGLING", "GSP", ThirdPartyMappingType.PRODUCT_ID, new PageCondition(page += 1, 200));
                        if (null != pageResult && CollectionUtils.isNotEmpty(pageResult.getItems())) {
                            updatePrice(thirdPartySystem, pageResult);
                        }
                    } while (null != pageResult && CollectionUtils.isNotEmpty(pageResult.getItems()));
                }
        );

        return ReturnT.SUCCESS;
    }

    //更新商品价格1
    private void updatePrice(ThirdPartySystem thirdPartySystem, PageList<ThirdPartyMappingInfo> pageResult) {

        List<List<ThirdPartyMappingInfo>> lists = groupListByQuantity(pageResult.getItems(), 20);
        //根据商品skuid查询衫海精商品明细
        lists.forEach(list -> {
            try {
                StringBuilder stringBuilder = new StringBuilder();
                list.forEach(item -> stringBuilder.append(item.getTargetId()).append(","));
                String skuIds = stringBuilder.toString();
                List<EJingLingGoodsListResponse.OuterGoodsVo> voList = eJingLingApiSevImpl.getGoodsByGoodsId(thirdPartySystem.getBizId(), skuIds);
                if (CollectionUtils.isNotEmpty(voList)) {
                    //根据商品明细库存更新供应商sku库存
                    voList.forEach(vo -> {
                        try {
                            updatePrice(thirdPartySystem, vo);
                        } catch (Exception e) {
                            log.error("EjingLing更新商品价格失败：vo:{} message:{}", JSON.toJSONString(vo), e.getMessage());
                        }
                        //updatePrice(thirdPartySystem,item);
                    });
                }
            } catch (Exception e) {
                log.error("EjingLing更新商品价格失败：list:{} message:{}", JSON.toJSONString(list), e.getMessage());
            }
        });
    }

    /**
     * 将集合按指定数量分组
     *
     * @param list     数据集合
     * @param quantity 分组数量
     * @return 分组结果
     */
    public <T> List<List<T>> groupListByQuantity(List<T> list, int quantity) {

        if (list == null || list.size() == 0) {
            return null;
        }
        if (quantity <= 0) {
            throw new IllegalArgumentException("Wrong quantity.");
        }
        List<List<T>> wrapList = new ArrayList<>();
        int count = 0;
        while (count < list.size()) {
            wrapList.add(list.subList(count, Math.min((count + quantity), list.size())));
            count += quantity;
        }

        return wrapList;
    }

    //更新库存
    public void updateStock(ThirdPartySystem thirdPartySystem, EJingLingGoodsListResponse.OuterGoodsVo vo) {
        OpenSupplierUpdateStockReq req = new OpenSupplierUpdateStockReq();
        req.setBizSerialNumber(UUID.randomUUID().toString().replace("-", "").substring(0, 18));
        EJingLingDataParam eJingLingDataParam = JSON.parseObject(thirdPartySystem.getParams(), EJingLingDataParam.class);
        String supplierId = eJingLingDataParam.getSupplierId();
        req.setSupplierId(supplierId);
        req.setCustomWarehouseCode(eJingLingDataParam.getWarehouse());
        vo.getSkuList().forEach(sku -> {
            req.setCustomSkuCode(sku.getSkuId().toString());
            req.setStockNum(sku.getStockNum());
            try {
                openSupplierProductRpc.updateStock(req);
            } catch (ServiceException e) {
                log.error("更新库存失败：supplier: {}  supplierSkuId: {} message: {}", supplierId, sku.getSkuId(), e.getMessage());
            }
        });
    }

    //更新库存
    private void batchUpdateStock(ThirdPartySystem thirdPartySystem, List<EJingLingGoodsListResponse.OuterGoodsVo> voList) {
        EJingLingDataParam eJingLingDataParam = JSON.parseObject(thirdPartySystem.getParams(), EJingLingDataParam.class);
        String supplierId = eJingLingDataParam.getSupplierId();
        if (CollectionUtils.isNotEmpty(voList)) {
            OpenSupplierUpdateStock4BatchReq openSupplierUpdateStock4BatchReq = new OpenSupplierUpdateStock4BatchReq();

            voList.forEach(vo -> {
                try {
                    List<OpenSupplierUpdateStockReq> list = new ArrayList<>();
                    vo.getSkuList().forEach(sku -> {
                        OpenSupplierUpdateStockReq req = new OpenSupplierUpdateStockReq();
                        req.setBizSerialNumber(UUID.randomUUID().toString().replace("-", "").substring(0, 18));
                        req.setSupplierId(supplierId);
                        req.setCustomWarehouseCode(eJingLingDataParam.getWarehouse());
                        req.setCustomSkuCode(sku.getSkuId().toString());
                        req.setStockNum(sku.getStockNum());
                        list.add(req);
                    });
                    openSupplierUpdateStock4BatchReq.setReqs(list);

                    openSupplierProductRpc.updateStockBatch(openSupplierUpdateStock4BatchReq);
                } catch (Exception e) {
                    log.error("更新库存失败：supplier: {}  supplierSkuIds: {} message: {}", supplierId, openSupplierUpdateStock4BatchReq.getReqs().stream().map(OpenSupplierUpdateStockReq::getCustomSkuCode).collect(Collectors.toList()), e.getMessage());

                }
            });
        }
    }

    //更新商品价格
    public void updatePrice(ThirdPartySystem thirdPartySystem, EJingLingGoodsListResponse.OuterGoodsVo vo) {
        OpenSupplierAdjustSupplyPriceReq req = new OpenSupplierAdjustSupplyPriceReq();
        JSONObject jsonObject = JSON.parseObject(thirdPartySystem.getParams());
        String supplierId = jsonObject.get("supplierId").toString();
        req.setSupplierId(supplierId);
        req.setCountry("CN");
        req.setSupplyPriceCurrency("CNY");

        vo.getSkuList().forEach(sku -> {
            try {
                req.setCustomSkuCode(String.valueOf(sku.getSkuId()));
                req.setSupplyPrice(new BigDecimal(String.valueOf(sku.getSkuSalePrice())));
                //供货明细表
                List<OpenSupplierItemCreateCombineDetailInfo> detailInfos = new ArrayList<>();
                OpenSupplierItemCreateCombineDetailInfo info = new OpenSupplierItemCreateCombineDetailInfo();
                info.setCombineNum(1);
                info.setSupplyPriceCurrency("CNY");
                detailInfos.add(info);
                info.setSupplyPrice(new BigDecimal(String.valueOf(sku.getSkuSalePrice())));
                req.setCombineDetails(detailInfos);

                openSupplierProductRpc.adjustSupplyPrice(req);
            } catch (ServiceException e) {
                log.error("更新商品价格：supplier: {}  supplierSkuId: {} message: {}", supplierId, sku.getSkuId(), e.getMessage());
            }
        });
    }

    //更新商品信息
    @Job("autoUpdateEjingLingProductInfo")
    public ReturnT<String> updateEJINGLINGProductInfo(String param) {
        return DConcurrentTemplate.tryLockMode(
                "EJINGLING:UPDATE_PRODUCTINFO",
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    log.info("ejingling-根据商品映射数据更新商品信息 定时任务开始, param={}", param);
                    JSONObject paramObject = JSON.parseObject(param);
                    String thirdPartySystemId = paramObject.getString("thirdPartySystemId");
                    long gmtCreateLess = DateUtils.DateString2TimeStamp(paramObject.getString("gmtCreateLess"));
                    long gmtCreateGreater = DateUtils.DateString2TimeStamp(paramObject.getString("gmtCreateGreater"));

                    //根据thirdPartySystemId获取第三方系统参数
                    ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
                    VVICBaseParam vvicBaseParam = JSON.parseObject(thirdPartySystem.getParams(), VVICBaseParam.class);
                    String supplierId = vvicBaseParam.getSupplierId();

                    //根据供应商id查询供应商商品
                    SupplierSkuSearchDetailsCommand supplierSkuSearchDetailsCommand = new SupplierSkuSearchDetailsCommand();
                    supplierSkuSearchDetailsCommand.setSupplierId(supplierId);
                    //使用供应商id查询商品
                    //List<SupplierSkuInfo> skuInfos = supplierSkuRpc.getSkuInfo(supplierSkuSearchDetailsCommand);
                    PageList<ThirdPartyMappingInfo> pageResult;
                    int page = 0;
                    do {
                        pageResult = thirdPartyMappingManager.getNewestInfoByTargetBizIdAndGmtCreate("EJINGLING", "GSP", ThirdPartyMappingType.PRODUCT_ID, new PageCondition(page += 1, 200), gmtCreateLess, gmtCreateGreater);
                        if (null != pageResult && CollectionUtils.isNotEmpty(pageResult.getItems())) {
                            //List<String> vidList = pageResult.getItems().stream().map(ThirdPartyMappingInfo::getSourceId).collect(Collectors.toList());
                            List<EJingLingGoodsListResponse.OuterGoodsVo> collect = pageResult.getItems().stream().map(thirdPartyMappingInfoJSON->{ return JSON.parseObject(thirdPartyMappingInfoJSON.getExtendBizInfo(), EJingLingGoodsListResponse.OuterGoodsVo.class);}).collect(Collectors.toList());
                            List<String> categoryList = collect.stream().map(vo -> String.valueOf(vo.getGoodsId())).distinct().collect(Collectors.toList());
                            Map<String, String> idMap = pageResult.getItems().stream().collect(Collectors.toMap(ThirdPartyMappingInfo::getTargetId, ThirdPartyMappingInfo::getSourceId));
                            Map<String, ThirdPartyMappingInfo> categoryMapping = thirdPartyMappingManager.batchGetIdMappingInfoByTargetCategoryPath("GSP", "EJINGLING", categoryList, ThirdPartyMappingType.CATEGORY);
                            collect.forEach(vo -> {
                                SupplierSpuCreateV2Command spuCreateV2Command = buildCreateSpuReq(thirdPartySystem, categoryMapping, vo);
                                SupplierSpuUpdateV2Command supplierSpuUpdateV2Command = buildSupplierSpuUpdateV2Command(idMap.get(String.valueOf(vo.getGoodsId())), spuCreateV2Command);
                                //TODO 等商品更新接口写好进行商品更新 时间点为	2023-04-26 17:49:15开始
                            });
                        }
                    } while (null != pageResult && CollectionUtils.isNotEmpty(pageResult.getItems()));

                    return ReturnT.SUCCESS;
                });
    }

    private SupplierSpuUpdateV2Command buildSupplierSpuUpdateV2Command(String supplierSpuId,SupplierSpuCreateV2Command spuCreateV2Command) {
        SupplierSpuUpdateV2Command supplierSpuUpdateV2Command = new SupplierSpuUpdateV2Command();
        supplierSpuUpdateV2Command.setSupplierSpuId(supplierSpuId);
        supplierSpuUpdateV2Command.setSupplierId(spuCreateV2Command.getSupplierId());
        supplierSpuUpdateV2Command.setDescInfos(spuCreateV2Command.getDescInfos());
        supplierSpuUpdateV2Command.setDefaultLocale(spuCreateV2Command.getDefaultLocale());
        supplierSpuUpdateV2Command.setCustomCode(spuCreateV2Command.getCustomCode());
        supplierSpuUpdateV2Command.setCustomBrandId(spuCreateV2Command.getCustomBrandId());
        supplierSpuUpdateV2Command.setCategoryId(spuCreateV2Command.getCategoryId());
        supplierSpuUpdateV2Command.setCustomCategoryId(spuCreateV2Command.getCustomCategoryId());
        supplierSpuUpdateV2Command.setMgmtCategoryLevel(spuCreateV2Command.getMgmtCategoryLevel());
        supplierSpuUpdateV2Command.setMgmtCategoryId(spuCreateV2Command.getMgmtCategoryId());
        supplierSpuUpdateV2Command.setMainImages(spuCreateV2Command.getMainImages());
        supplierSpuUpdateV2Command.setDetailImages(spuCreateV2Command.getDetailImages());
        supplierSpuUpdateV2Command.setVideos(spuCreateV2Command.getVideos());
        supplierSpuUpdateV2Command.setCountryOfOriginCode(spuCreateV2Command.getCountryOfOriginCode());
        supplierSpuUpdateV2Command.setLogisticsAttrInfo(spuCreateV2Command.getLogisticsAttrInfo());
        supplierSpuUpdateV2Command.setSkuList(spuCreateV2Command.getSkuList());
        supplierSpuUpdateV2Command.setParamsInfo(spuCreateV2Command.getParamsInfo());
        supplierSpuUpdateV2Command.setIsAutoAuditPass(spuCreateV2Command.getIsAutoAuditPass());
        supplierSpuUpdateV2Command.setIsCheckAttribute(spuCreateV2Command.getIsCheckAttribute());
        supplierSpuUpdateV2Command.setOperator(spuCreateV2Command.getOperator());

        return supplierSpuUpdateV2Command;
    }

    public ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }

    public static void main(String[] args) {
/*        String attrs = "20000:29534:男装品牌:other/其他;13021751:GM113:货号:GM113;42722636:248572013:基础风格:青春流行;122216345:29457:适用季节:夏季;122216348:29445:袖长:短袖;122216507:3226292:厚薄:常规;122216586:4049881:版型:标准;149422948:聚酯纤维100%:材质成分:聚酯纤维100%;";
        EJingLingGoodsListResponse.OuterGoodsVo outerGoodsVo = new EJingLingGoodsListResponse.OuterGoodsVo();
        outerGoodsVo.setBusinessProps(attrs);
        SupplierSpuCreateV2Command supplierSpuCreateV2Command = new SupplierSpuCreateV2Command();
        buildParamsInfo(supplierSpuCreateV2Command, outerGoodsVo);*/
    }
}
