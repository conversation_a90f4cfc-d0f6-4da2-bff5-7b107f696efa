package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.lazada;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingLazadaCategoryTreeRequest {

    /**
     * 站点
     */
    @NotNull(message = "站点不能为空")
    private String station;

    private Integer level;

    private String cname;

    private String p_l1_name;
    private String p_l2_name;
    private String p_l3_name;
    private String p_l4_name;
    private String p_l5_name;
    private String p_l6_name;
    private String p_l7_name;
    private String p_l8_name;
    private String p_l9_name;
    private String p_l10_name;

}
