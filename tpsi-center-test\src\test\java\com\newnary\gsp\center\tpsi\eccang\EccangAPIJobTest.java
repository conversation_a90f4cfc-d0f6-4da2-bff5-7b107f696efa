package com.newnary.gsp.center.tpsi.eccang;

import com.newnary.gsp.center.product.api.product.response.ChannelSaleItemDetailInfo;
import com.newnary.gsp.center.tpsi.api.eccang.request.GspSyncPo2EccangCommand;
import com.newnary.gsp.center.tpsi.app.job.*;
import com.newnary.gsp.center.tpsi.app.service.eccang.EccangPoMgmtApp;
import com.newnary.gsp.center.tpsi.infra.rpc.SaleItemRpc;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.test.starter.BaseTestInjectTenant;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * @Author: jack
 * @CreateTime: 2022-6-10
 */
public class EccangAPIJobTest extends BaseTestInjectTenant {

    @Override
    protected String tenantId() {
        return "TENANT3924117688555169382400";
    }

    @Resource
    private StockoutOrderEccangJobManager stockoutOrderEccangJobManager;
    @Resource
    private StockoutOrderShengWeiJobManager stockoutOrderShengWeiJobManager;
    @Resource
    private TransportOrderEccangJobManager transportOrderEccangJobManager;
    @Resource
    private GspReSyncPO2EccangJobManager gspReSyncPO2EccangJobManager;
    @Resource
    private EccangERPJobManager eccangERPJobManager;
    @Resource
    private EccangPoMgmtApp eccangPoMgmtApp;
    @Resource
    private SaleItemRpc saleItemRpc;

    @Test
    public void testSaleItemQuery() {
        ChannelSaleItemDetailInfo channelSaleItemDetailInfo = saleItemRpc.querySaleItem("C8737379310826718367745", "PSI179795038109731");
        System.out.println(channelSaleItemDetailInfo);
    }

    /**
     * 运输中的运输单---升威
     */
    @Test
    public void testAutoCheckTransportOrderStateBaseSW() {
        transportOrderEccangJobManager.autoCheckTransportOrderStateBaseShengWei(
                "{\"erpTpsId\" : \"TESTECCANGERP0003\", \"wmsTpsId\":\"TESTECCANGWMS0001\"}");
    }

    /**
     * 出库中的出库单---升威
     */
    @Test
    public void testAutoCheckStockoutingOrderBaseShengWei() {
        stockoutOrderShengWeiJobManager.autoCheckStockoutingOrderBaseShengWei(
                "{\"erpTpsId\" : \"TESTECCANGERP0003\", \"wmsTpsId\":\"TESTECCANGWMS0001\"}");
    }

    /**
     * 未出库的出库单---升威
     */
    @Test
    public void testAutoPushStockoutWaittingOrder2ShengWeiPurchase() {
        stockoutOrderShengWeiJobManager.autoPushStockoutWaittingOrder2ShengWeiPurchase(
                "{\"erpTpsId\" : \"TESTECCANGERP0003\", \"wmsTpsId\":\"TESTECCANGWMS0001\"}"
        );
    }

    /**
     * 未出库的出库单--易仓
     */
    @Deprecated
    @Test
    public void testAutoPushStockoutWaitingOrder2Eccang() {
        stockoutOrderEccangJobManager.autoPushStockoutWaitingOrder2Eccang(
                "{\"erpTpsId\" : \"TESTECCANGERP0003\", \"wmsTpsId\":\"TESTECCANGWMS0001\", \"gmtCreateEc\":\"1688434214000\"}");
    }

    /**
     * 出库中出库单--易仓
     */
    @Test
    public void testAutoCheckStockoutingOrderBaseEcCang() {
        stockoutOrderEccangJobManager.autoCheckStockoutingOrderBaseEcCang(
                "{\"erpTpsId\" : \"TESTECCANGERP0003\", \"wmsTpsId\":\"TESTECCANGWMS0001\", \"stockoutOrderId\":\"SO167017815539800\", \"gmtCreateEc\":\"0\"}");
    }

    /**
     * 更新已出库的出库单--易仓
     */
    @Test
    public void testAutoCheckStockoutedOrderBaseEcCang() {
        stockoutOrderEccangJobManager.autoCheckStockoutedOrderBaseEcCang(
                "{\"erpTpsId\" : \"TESTECCANGERP0003\", \"wmsTpsId\":\"TESTECCANGWMS0001\", \"stockoutOrderId\":\"SO167017815539800\", \"gmtCreateEc\":\"0\"}");
    }

    /**
     * 创建集运订单
     */
    @Test
    public void testAutoCheckTransportCreatedOrderBaseEcCang() {
        transportOrderEccangJobManager.autoCheckTransportCreatedOrderBaseEcCang(
                "{\"erpTpsId\" : \"TESTECCANGERP0003\", \"wmsTpsId\":\"TESTECCANGWMS0001\", \"transportOrderId\":\"TO167017869017112\"}");
        //,"transportOrderId":"TO4756352560739325710337"
    }

    @Test
    public void testAutoCheckTransportOrderStateBaseEcCang() {
        transportOrderEccangJobManager.autoCheckTransportOrderStateBaseEcCang(
                "{\"erpTpsId\" : \"TESTECCANGERP0003\", \"wmsTpsId\":\"TESTECCANGWMS0001\", \"transportOrderId\":\"TO167318081568786\"}");
    }

    @Deprecated
    @Test
    public void testAutoCompleteTransportOrder() {
        transportOrderEccangJobManager.autoCompleteTransportOrder(
                "{\"erpTpsId\" : \"TESTECCANGERP0003\", \"wmsTpsId\":\"TESTECCANGWMS0001\", \"transportOrderId\":\"TO1363343571218961666048\"}");
    }

    //易仓商品同步测试
    @Test
    public void testSyncProduct() {
        ReturnT<String> stringReturnT = eccangERPJobManager.syncECangERPProduct("{\"thirdPartySystemId\":\"TESTECCANGERP0003\",\"perGroupCount\":5,\"insertPage\":3,\"indexPage\":348,\"pageSize\":100,\"supplierId\":\"VD0992389509044584452096\",\"warehouse\":\"W131729237082158\",\"categoryId\":\"131730495373344\",\"products\":[\"EBC-28T\",\"EBV-28T1000WRC\",\"RA01565A\"]}");
    }

    @Test
    public void testSyncTrackingNumber2EcangPurchaseOrder() {
        GspSyncPo2EccangCommand command = new GspSyncPo2EccangCommand();
        command.setPurchaseOrderId("446731201147965440");
        command.setCurrOptUserId(null);
        eccangPoMgmtApp.syncTrackingNumber2EcangPurchaseOrder(command);
    }

    @Test
    public void testGspReSyncPO2EccangJob() {
        gspReSyncPO2EccangJobManager.gspReSyncPO2EccangJob("{\"pageSize\": 1}");
    }

    @Test
    public void testEcReSyncPO2GspJob() {
        gspReSyncPO2EccangJobManager.ecReSyncPO2GspJob("{\"pageSize\": 300, \"eventBizId\":\"1446570402840579\"}");
    }

    @Test
    public void testPO2ecSyncCreateJob() {
        gspReSyncPO2EccangJobManager.po2ecSyncCreateJob("{\"pageSize\": 300}");
    }

    @Test
    public void testGspReSyncTrackingNumber2EccangJob() {
        gspReSyncPO2EccangJobManager.gspReSyncTrackingNumber2EccangJob("{\"pageSize\": 300}");
    }

    @Test
    public void testEcReSyncSuccessOrder2EccangJob() {
        gspReSyncPO2EccangJobManager.ecReSyncSuccessOrder2EccangJob(null);
    }

    @Test
    public void testPartialArrivalOrder2EccangJob() {
        gspReSyncPO2EccangJobManager.ecReSyncPartialArrivalOrder2EccangJob(null);
    }

    @Test
    public void testEcSyncSuccessOrderAndGSPNoSuccess2EccangJob() {
        String str = "{\"startDay\":60,\"endDay\":5}";
        gspReSyncPO2EccangJobManager.ecSyncSuccessOrderAndGSPNoSuccess2EccangJob(str);
    }

}
