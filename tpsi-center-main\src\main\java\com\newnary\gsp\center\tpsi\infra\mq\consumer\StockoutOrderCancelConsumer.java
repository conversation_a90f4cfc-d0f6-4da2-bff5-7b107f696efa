package com.newnary.gsp.center.tpsi.infra.mq.consumer;

import com.newnary.messagebody.gsp.logistics.GSPStockoutOrderCancelTopic;
import com.newnary.mq.starter.consumer.AbstractMQConsumer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class StockoutOrderCancelConsumer extends AbstractMQConsumer {

    @Value("${gsp.stockout-order-cancel.mq.consumer-id}")
    private String group;

    @Override
    public String topic() {
        return GSPStockoutOrderCancelTopic.TOPIC;
    }

    @Override
    public String consumerGroup() {
        return group;
    }
}
