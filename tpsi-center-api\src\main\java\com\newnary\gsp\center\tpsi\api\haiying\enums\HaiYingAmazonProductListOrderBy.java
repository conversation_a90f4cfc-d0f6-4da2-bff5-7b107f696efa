package com.newnary.gsp.center.tpsi.api.haiying.enums;

/**
 * @Author: jack
 * @CreateTime: 2022-7-13
 */
public enum HaiYingAmazonProductListOrderBy {

    top_sellers_rank("行业现排名"),
    @Deprecated
    three_day_rank("3天前行业排名(已取消)"),
    @Deprecated
    three_day_rank_change("前3天排名变化(已取消)"),
    @Deprecated
    three_day_rank_change_rate("前3天排名变化率(已取消)"),
    three_day_new_reviews("前3天新增评论数"),
    asin_price_max("最高价格"),
    asin_price_min("最低价格"),
    customer_reviews("总评论数"),
    score("评分"),
    answered_questions("Q&A数"),
    follow_sellers_num("卖家数"),
    shipping_weight("重量"),
    fir_arrival("商家时间"),
    rank_rise_avg_change("前9天的排名均值变化"),
    ;

    private String description;

    HaiYingAmazonProductListOrderBy(String description) {
        this.description = description;
    }

}
