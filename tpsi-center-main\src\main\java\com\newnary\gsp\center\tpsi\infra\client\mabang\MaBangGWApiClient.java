package com.newnary.gsp.center.tpsi.infra.client.mabang;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.newnary.common.utils.httpmethod.ApiBaseResult;
import com.newnary.common.utils.httpmethod.EncryptUtil;
import com.newnary.common.utils.httpmethod.HttpMethodUtil;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.MaBangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.dev.MaBangGetInLibraryProducts;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.dev.MaBangSyncToProductLibraryV2;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.order.*;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.stock.MaBangDoSearchSalesSku;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.stock.MaBangDoSearchSkuList;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.stock.MaBangGetStockQuantity;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.sys.MaBangSysGetWarehouseList;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.wl.MaBangWlGetMylogisticschannel;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 马帮新版api ，入口为：https://gwapi.mabangerp.com/api/v2
 *
 * <AUTHOR>
 * @date ：Created in 2022/01/07
 */
@Slf4j
public class MaBangGWApiClient {

    public static final String BASE_URL = "https://gwapi.mabangerp.com/api/v2";

    private String appkey;

    private String developerKey;

    private String warehouse;

    private static Integer RETRY_COUNT = 3;

    public MaBangGWApiClient(String maBangParams) {
        MaBangParams params = JSON.parseObject(maBangParams, MaBangParams.class);
        this.appkey = params.getAppkey();
        this.developerKey = params.getDeveloperKey();
        this.warehouse = params.getWarehouse();
    }

    public MaBangApiBaseResult<String>  syncPostMethod(String api, String dataParas) {
        long now = System.currentTimeMillis() / 1000;
        Map<String, String> header = new HashMap<>(1);
        Map<String, Object> bodyParas = new HashMap<>(8);
        bodyParas.put("api", api);
        bodyParas.put("appkey", appkey);
        bodyParas.put("data", dataParas);
        bodyParas.put("timestamp", now);
        bodyParas.put("version", "1");

        try {
            //HMAC-SHA256加密
            header.put("Authorization", EncryptUtil.HMAC_SHA256(JSON.toJSONString(bodyParas), developerKey));
            int count = 0;
            do {
                ApiBaseResult apiBaseResult = HttpMethodUtil.syncPostMethod(BASE_URL, 3,
                        null,
                        "application/json",
                        header, null, bodyParas);

                MaBangApiBaseResult<String> ret = JSON.parseObject(apiBaseResult.getRet(), new TypeReference<MaBangApiBaseResult<String>>() {
                });
                //对请求过多时返回的情况进行处理
                if (ret.getMessage().equals("Too Many Attempts.")) {
                    try {
                        Random a = new Random();
                        int second = a.nextInt(10);
                        log.info("请求过多导致出错,,等待{}秒", second);
                        TimeUnit.SECONDS.sleep(second);
                        log.info("重新请求");
                        count++;
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                } else {
                    return ret;
                }
            } while (count < RETRY_COUNT);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 查询商品（stockSku维度）
     *
     * @param params
     * @return
     */
    public MaBangApiBaseResult<String> stockDoSearchSkuList(MaBangDoSearchSkuList params) {
        String dataParas = JSON.toJSONString(params);
        return syncPostMethod("stock-do-search-sku-list", dataParas);
    }

    public MaBangApiBaseResult<String> stockDoSearchSalesSku(MaBangDoSearchSalesSku params) {
        String dataParas = JSON.toJSONString(params);
        return syncPostMethod("stock-do-search-sales-sku", dataParas);
    }

    public MaBangApiBaseResult<String> stockGetStockQuantity(MaBangGetStockQuantity params) {
        String dataParas = JSON.toJSONString(params);
        return syncPostMethod("stock-get-stock-quantity", dataParas);
    }

    public MaBangApiBaseResult<String> devSyncToProductLibrary(MaBangSyncToProductLibraryV2 params) {
        String dataParas = JSON.toJSONString(params);
        return syncPostMethod("dev-sync-to-product-library-v2", dataParas);
    }

    public MaBangApiBaseResult<String> devGetInLibraryProducts(String params, Integer page) {
        MaBangGetInLibraryProducts maBangGetInLibraryProducts = JSON.parseObject(params, MaBangGetInLibraryProducts.class);
        if (maBangGetInLibraryProducts == null) {
            maBangGetInLibraryProducts = new MaBangGetInLibraryProducts();
        }
        maBangGetInLibraryProducts.setPage(page);
        maBangGetInLibraryProducts.setRowsPerPage(100);

        String dataParas = JSON.toJSONString(maBangGetInLibraryProducts);
        return syncPostMethod("dev-get-in-library-products", params);
    }

    public MaBangApiBaseResult<String> orderGetOrderList2(MaBangGetOrderList2 params) {
        String dataParas = JSON.toJSONString(params);
        return syncPostMethod("order-get-order-list2", dataParas);
    }

    public MaBangApiBaseResult<String> orderGetOrderList(MaBangGetOrderList params) {
        String dataParas = JSON.toJSONString(params);
        return syncPostMethod("order-get-order-list", dataParas);
    }

    public MaBangApiBaseResult<String> orderDoCreateOrder(MaBangDoCreateOrder params) {
        String dataParas = JSON.toJSONString(params);
        return syncPostMethod("order-do-create-order", dataParas);
    }

    public MaBangApiBaseResult<String> orderDoDeliverOrder(MaBangDoDeliverOrder params) {
        String dataParas = JSON.toJSONString(params);
        return syncPostMethod("order-do-deliver-order", dataParas);
    }

    public MaBangApiBaseResult<String> warehouseDoAddStorage(String params) {
        return syncPostMethod("warehouse-do-add-storage", params);
    }

    public MaBangApiBaseResult<String> sysCreateWarehouse(String params) {
        return syncPostMethod("sys-create-warehouse", params);
    }

    public MaBangApiBaseResult<String> sysGetWarehouseList(MaBangSysGetWarehouseList params) {
        String dataParas = JSON.toJSONString(params);
        return syncPostMethod("sys-get-warehouse-list", dataParas);
    }

    public MaBangApiBaseResult<String> wlGetMylogisticschannel(MaBangWlGetMylogisticschannel params) {
        String dataParas = JSON.toJSONString(params);
        return syncPostMethod("wl-get-mylogisticschannel", dataParas);
    }

    public MaBangApiBaseResult<String> wlGetCustomLogistics(String params) {
        return syncPostMethod("wl-get-custom-logistics", params);
    }

    public MaBangApiBaseResult<String> sysGetLabelList(String params) {
        return syncPostMethod("sys-get-label-list", params);
    }

    /**
     * @param api          api入口名称(注：新旧入口名称不一样)
     * @param appkey       新api为appkey/旧api为developerId
     * @param developerKey 用于加密
     * @param dataParas    新api为bodyParas下的data参数，旧api为bodyParas;
     * @return MaBangApiBaseResult
     */
    public MaBangApiBaseResult<String> syncPostMethod(String api, String appkey, String developerKey, Map<String, Object> dataParas) {
        long now = System.currentTimeMillis() / 1000;
        Map<String, String> header = new HashMap<>(1);
        Map<String, Object> bodyParas = new HashMap<>(8);
        bodyParas.put("api", api);
        bodyParas.put("appkey", appkey);
        bodyParas.put("data", dataParas);
        bodyParas.put("timestamp", now);
        bodyParas.put("version", "1");

        try {
            //HMAC-SHA256加密
            header.put("Authorization", EncryptUtil.HMAC_SHA256(JSON.toJSONString(bodyParas), developerKey));
            ApiBaseResult apiBaseResult = HttpMethodUtil.syncPostMethod(BASE_URL, 3,
                    null,
                    "application/json",
                    header, null, bodyParas);

            return JSON.parseObject(apiBaseResult.getRet(), new TypeReference<MaBangApiBaseResult<String>>() {
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public MaBangApiBaseResult<String> orderDoChangeOrder(MaBangDoChangeOrder params) {
        String dataParas = JSON.toJSONString(params);
        return syncPostMethod("order-do-change-order", dataParas);
    }

}
