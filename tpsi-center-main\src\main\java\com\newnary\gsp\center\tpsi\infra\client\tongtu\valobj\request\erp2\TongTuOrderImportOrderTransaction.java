package com.newnary.gsp.center.tpsi.infra.client.tongtu.valobj.request.erp2;

import lombok.Data;

@Data
public class TongTuOrderImportOrderTransaction {

    /**
     * 货品ID,与SKU二传一即可;如果与SKU都传值了，以这个字段值为准
     */
    private String goodsDetailId;

    /**
     * 货品备注
     */
    private String goodsDetailIdRemark;

    /**
     * 商品总金额
     */
    private String productsTotalPrice;

    /**
     * 商品总金额币种
     */
    private String productsTotalPriceCurrency;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 买家选择的运输方式
     */
    private String shipType;

    /**
     * 买家所支付的运费
     */
    private String shippingFeeIncome;

    /**
     * 买家所支付的运费币种
     */
    private String shippingFeeIncomeCurrency;

    /**
     * 商品 sku
     */
    private String sku;

}
