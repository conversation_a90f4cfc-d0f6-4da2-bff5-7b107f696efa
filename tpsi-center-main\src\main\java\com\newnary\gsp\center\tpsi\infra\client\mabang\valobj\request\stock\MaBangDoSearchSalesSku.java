package com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.stock;

import lombok.Data;

@Data
public class MaBangDoSearchSalesSku {

    /**
     * 主sku
     */
    private String salesSku;

    /**
     * 创建起始时间 (参数要有timeCreatedStar或者updateTimeStart,否则提示签名不正确)
     */
    private String timeCreatedStart;

    /**
     * 创建结束时间
     */
    private String timeCreatedEnd;

    /**
     * 页数
     */
    private Integer page;

    /**
     * 当前每页条数，默认20
     */
    private Integer rowsPerPage;

}
