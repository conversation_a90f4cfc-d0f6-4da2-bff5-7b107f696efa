package com.newnary.gsp.center.tpsi.tiktok;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.client.config.utils.MD5;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.newnary.gsp.center.product.api.category.response.CategoryPathInfo;
import com.newnary.gsp.center.product.api.thirdpartymapping.vo.ThirdPartyCategoryBaseInfo;
import com.newnary.gsp.center.product.api.thirdpartymapping.vo.ThirdPartyMappingType;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyCategoryMappingExtInfo;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingInfo;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.ThirdPartyMappingManager;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyMappingPO;
import com.newnary.tenant.context.asyn.TenantRunnable;
import com.newnary.test.starter.BaseTestInjectTenant;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.poi.ss.usermodel.*;
import org.junit.Test;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class CategoryMappingTest extends BaseTestInjectTenant {

    private static final String SOURCE_BIZ_ID = "GSP";
    private static final String TARGET_BIZ_ID = "TIKTOK";

    @Resource
    private ThirdPartyMappingManager thirdPartyMappingManager;

    public static ExecutorService pushExecutor = new ThreadPoolExecutor(8, 8,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(100), new ThreadFactoryBuilder()
            .build());

    @Override
    protected String tenantId() {
        return "TENANT7941185388049557880832";
    }

    /**
     * 1688-shopee 类目索引对照表，业务方提供数据
     */
    @Test
    public void initByReadExcel() {

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        final int titleRow = 0;
        final String fileName = "D:\\download\\飞书\\类目对照-fat-20230302.xlsx";

        try {
            File file = new File(fileName);
            Workbook wb = WorkbookFactory.create(file);
            Sheet sheet = wb.getSheet("系统与TT分类对照");
            if (sheet == null) {
                throw new RuntimeException("系统与TT分类对照");
            }

            int totalRows = sheet.getLastRowNum();

            for (int i = titleRow + 1; i <= totalRows; i++) {
                Row row = sheet.getRow(i);
                if (isEmptyRow(row)) {
                    continue;
                }
                ThirdPartyMappingPO po = buildThirdPartyMappingPO(row);

                List<ThirdPartyMappingPO> pos = Collections.synchronizedList(new ArrayList<>(100));
                if (ObjectUtils.isNotEmpty(po)) {
                    pos.add(po);
                    if (pos.size() >= 100) {
                        CountDownLatch latch = new CountDownLatch(pos.size());

                        pos.forEach(item -> {
                            pushExecutor.execute(new TenantRunnable() {
                                @Override
                                public void go() {
                                    thirdPartyMappingManager.insertOrUpdate(item.getSourceBizId(), item.getTargetBizId(), item.getSourceId(), item.getTargetId(), ThirdPartyMappingType.CATEGORY.name(), null);
                                    latch.countDown();
                                }
                            });
                        });

                        latch.await();
                        pos = Collections.synchronizedList(new ArrayList<>(100));
                    }
                }
                CountDownLatch latch = new CountDownLatch(pos.size());
                pos.forEach(item -> {
                    pushExecutor.execute(new TenantRunnable() {
                        @Override
                        public void go() {
                            thirdPartyMappingManager.insertOrUpdate(item.getSourceBizId(), item.getTargetBizId(), item.getSourceId(), item.getTargetId(), ThirdPartyMappingType.CATEGORY.name(), null);
                            latch.countDown();
                        }
                    });
                });
                latch.await();
                System.out.println("耗时:" + stopWatch.getTime());

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Test
    public void setCategorySaleAttribute() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        final int titleRow = 0;
        final String fileName = "D:\\download\\飞书\\类目对照-fat-20230302.xlsx";

        try {
            File file = new File(fileName);
            Workbook wb = WorkbookFactory.create(file);
            Sheet sheet = wb.getSheet("系统与TT分类销售属性对照");
            if (sheet == null) {
                throw new RuntimeException("系统与TT分类销售属性对照");
            }

            int totalRows = sheet.getLastRowNum();

            String lastCategoryId = "";
            Map<String, String> sourceAttributeMap = new HashMap<>();

            for (int i = titleRow + 1; i <= totalRows+1; i++) {
                if (i == totalRows + 1) {
                    System.out.println("dddddd");
                }
                Row row = sheet.getRow(i);
                if (isEmptyRow(row) && i != totalRows + 1) {
                    continue;
                }

                String gspCategoryId = getCellStringValue(row, 18);
                if (StringUtils.isBlank(gspCategoryId) && i != totalRows + 1) {
                    continue;
                }

                if (!StringUtils.equals(gspCategoryId, lastCategoryId)) {
                    // 判断当前map是否不为空，进行刷新
                    if (!sourceAttributeMap.isEmpty()) {
                        ThirdPartyMappingInfo infoBySource = thirdPartyMappingManager.getInfoBySource(lastCategoryId, SOURCE_BIZ_ID, TARGET_BIZ_ID, com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingType.CATEGORY);
                        String extendBizInfo = infoBySource.getExtendBizInfo();
                        ThirdPartyCategoryMappingExtInfo extInfo;
                        if (StringUtils.isBlank(extendBizInfo) || StringUtils.equals("null", extendBizInfo)) {
                            extInfo = new ThirdPartyCategoryMappingExtInfo();
                        } else {
                            extInfo = JSONObject.parseObject(extendBizInfo, ThirdPartyCategoryMappingExtInfo.class);
                        }
                        extInfo.setSourceSaleAttributeToTargetAttribute(sourceAttributeMap);
                        thirdPartyMappingManager.insertOrUpdate(SOURCE_BIZ_ID, TARGET_BIZ_ID, infoBySource.getSourceId(), infoBySource.getTargetId(), ThirdPartyMappingType.CATEGORY.name(), extInfo);
                    }
                    lastCategoryId = gspCategoryId;
                    sourceAttributeMap = new HashMap<>();
                }
                String attributeName = getCellStringValue(row, 8);
                String attrID = getCellStringValue(row, 7);
                if (StringUtils.isAnyBlank(attributeName, attrID)) {
                    continue;
                }

                sourceAttributeMap.put(attributeName, attrID);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }



    @Test
    public void setCategoryProductAttribute() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        final int titleRow = 0;
        final String fileName = "D:\\download\\飞书\\类目对照-fat-20230302.xlsx";

        try {
            File file = new File(fileName);
            Workbook wb = WorkbookFactory.create(file);
            Sheet sheet = wb.getSheet("系统与TT分类产品属性对照");
            if (sheet == null) {
                throw new RuntimeException("系统与TT分类产品属性对照");
            }

            int totalRows = sheet.getLastRowNum();

            String lastCategoryId = "";
            Map<String, String> sourceAttributeMap = new HashMap<>();

            for (int i = titleRow + 1; i <= totalRows+1; i++) {
                if (i == totalRows + 1) {
                    System.out.println("dddddd");
                }
                Row row = sheet.getRow(i);
                if (isEmptyRow(row) && i != totalRows + 1) {
                    continue;
                }

                String gspCategoryId = getCellStringValue(row, 19);
                if (StringUtils.isBlank(gspCategoryId) && i != totalRows + 1) {
                    continue;
                }

                if (!StringUtils.equals(gspCategoryId, lastCategoryId)) {
                    // 判断当前map是否不为空，进行刷新
                    if (!sourceAttributeMap.isEmpty()) {
                        ThirdPartyMappingInfo infoBySource = thirdPartyMappingManager.getInfoBySource(lastCategoryId, SOURCE_BIZ_ID, TARGET_BIZ_ID, com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingType.CATEGORY);
                        String extendBizInfo = infoBySource.getExtendBizInfo();
                        ThirdPartyCategoryMappingExtInfo extInfo;
                        if (StringUtils.isBlank(extendBizInfo) || StringUtils.equals("null", extendBizInfo)) {
                            extInfo = new ThirdPartyCategoryMappingExtInfo();
                        } else {
                            extInfo = JSONObject.parseObject(extendBizInfo, ThirdPartyCategoryMappingExtInfo.class);
                        }
                        extInfo.setSourceProductAttributeToTargetAttribute(sourceAttributeMap);
                        thirdPartyMappingManager.insertOrUpdate(SOURCE_BIZ_ID, TARGET_BIZ_ID, infoBySource.getSourceId(), infoBySource.getTargetId(), ThirdPartyMappingType.CATEGORY.name(), extInfo);
                    }
                    lastCategoryId = gspCategoryId;
                    sourceAttributeMap = new HashMap<>();
                }
                String attributeName = getCellStringValue(row, 8);
                String attrID = getCellStringValue(row, 7);
                if (StringUtils.isAnyBlank(attributeName, attrID)) {
                    continue;
                }

                sourceAttributeMap.put(attributeName, attrID);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private ThirdPartyMappingPO buildThirdPartyMappingPO(Row row) {

        String gspCategoryId = getCellStringValue(row, 12);
        String tkCategoryId = getCellStringValue(row, 4);
        if (StringUtils.isAnyBlank(gspCategoryId, tkCategoryId)) {
            return null;
        }
        ThirdPartyMappingPO po = new ThirdPartyMappingPO();
        po.setBizType(ThirdPartyMappingType.CATEGORY.name());
        po.setTargetBizId(TARGET_BIZ_ID);
        po.setSourceBizId(SOURCE_BIZ_ID);
        po.setSourceId(gspCategoryId);
        po.setTargetId(tkCategoryId);
        po.setIndexKey(po.getSourceBizId().concat(po.getSourceId()).concat(po.getTargetBizId()).concat(po.getTargetId()));
        return po;
    }

    /**
     * bizId:ALI_1688、ALI_EXPRESS、SHOPEE、COTTON_ON
     *
     * @param shopee_category_id 一级ID
     * @param shopee_name_1      一级名称
     * @param shopee_name_2      二级名称
     * @param shopee_name_3      三级名称
     * @return 三方类目基础信息
     */
    private ThirdPartyCategoryBaseInfo getShopeeTargetCategoryInfo(String shopee_category_id, String shopee_name_1, String shopee_name_2, String shopee_name_3) {
        if (StringUtils.isBlank(shopee_category_id)) {
            shopee_category_id = MD5.getInstance().getMD5String(shopee_name_1.concat(shopee_name_2).concat(shopee_name_3).getBytes());
        }
        ThirdPartyCategoryBaseInfo info = new ThirdPartyCategoryBaseInfo();
        List<CategoryPathInfo> pathInfos = new ArrayList<>();
        String categoryName = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(shopee_name_1)) {
            pathInfos.add(new CategoryPathInfo("-1", shopee_name_1));
            categoryName = shopee_name_1;
        }
        if (StringUtils.isNotBlank(shopee_name_2)) {
            pathInfos.add(new CategoryPathInfo("-1", shopee_name_2));
            categoryName = shopee_name_2;
        }
        if (StringUtils.isNotBlank(shopee_name_3)) {
            pathInfos.add(new CategoryPathInfo(shopee_category_id, shopee_name_3));
            categoryName = shopee_name_3;
        }
        info.setPathInfos(pathInfos);
        info.setCategoryLevel(pathInfos.size());
        info.setCategoryName(categoryName);
        info.setBizId(BizType.SHOPEE.name());
        info.setCategoryId(shopee_category_id);
        return info;
    }

    private ThirdPartyCategoryBaseInfo get1688LeafNodeInfoCategoryInfo(String alibaba_id_1, String alibaba_id_2, String alibaba_id_3, String alibaba_name_1, String alibaba_name_2, String alibaba_name_3) {
        String categoryId = getCategoryId(alibaba_id_1, alibaba_id_2, alibaba_id_3);
        String categoryName = getCategoryId(alibaba_name_1, alibaba_name_2, alibaba_name_3);

        List<CategoryPathInfo> pathInfos = new ArrayList<>();
        if (StringUtils.isNotBlank(alibaba_name_1)) {
            CategoryPathInfo pathInfo = new CategoryPathInfo();
            pathInfo.setName(alibaba_name_1);
            pathInfo.setId(StringUtils.defaultIfBlank(alibaba_id_1, "-1"));
            pathInfos.add(pathInfo);
        }

        if (StringUtils.isNotBlank(alibaba_name_2)) {
            CategoryPathInfo pathInfo = new CategoryPathInfo();
            pathInfo.setName(alibaba_name_2);
            pathInfo.setId(StringUtils.defaultIfBlank(alibaba_id_2, "-1"));
            pathInfos.add(pathInfo);
        }

        if (StringUtils.isNotBlank(alibaba_name_3)) {
            CategoryPathInfo pathInfo = new CategoryPathInfo();
            pathInfo.setName(alibaba_name_3);
            pathInfo.setId(StringUtils.defaultIfBlank(alibaba_id_3, "-1"));
            pathInfos.add(pathInfo);
        }

        ThirdPartyCategoryBaseInfo info = new ThirdPartyCategoryBaseInfo();
        info.setCategoryName(categoryName);
        info.setCategoryId(categoryId);
        info.setCategoryLevel(pathInfos.size());
        info.setBizId(BizType.ALI_1688.name());
        info.setPathInfos(pathInfos);
        return info;
    }

    private String getCategoryId(String alibaba_id_1, String alibaba_id_2, String alibaba_id_3) {
        if (StringUtils.isNotBlank(alibaba_id_3)) {
            return alibaba_id_3;
        }
        if (StringUtils.isNotBlank(alibaba_id_2)) {
            return alibaba_id_2;
        }
        if (StringUtils.isNotBlank(alibaba_id_1)) {
            return alibaba_id_1;
        }
        return "-1";
    }

    private boolean isEmptyRow(Row row) {
        if (row == null) {
            return true;
        }
        for (int i = row.getFirstCellNum(); i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            if (cell != null && cell.getCellType() != Cell.CELL_TYPE_BLANK) {
                return false;
            }
        }
        return true;
    }


    /**
     * 获取单元格值
     *
     * @param row    获取的行
     * @param column 获取单元格列号
     * @return 单元格值
     */
    public String getCellStringValue(Row row, int column) {
        if (row == null) {
            return "";
        }
        Object val = "";
        try {
            Cell cell = row.getCell(column);
            if (ObjectUtils.isNotEmpty(cell)) {
                if (cell.getCellType() == Cell.CELL_TYPE_NUMERIC || cell.getCellType() == Cell.CELL_TYPE_FORMULA) {
                    val = cell.getNumericCellValue();
                    if (DateUtil.isCellDateFormatted(cell)) {
                        val = DateUtil.getJavaDate((Double) val); // POI Excel 日期格式转换
                    } else {
                        if ((Double) val % 1 != 0) {
                            val = new BigDecimal(val.toString());
                        } else {
                            val = new DecimalFormat("0").format(val);
                        }
                    }
                } else if (cell.getCellType() == Cell.CELL_TYPE_STRING) {
                    val = cell.getStringCellValue();
                } else if (cell.getCellType() == Cell.CELL_TYPE_BOOLEAN) {
                    val = cell.getBooleanCellValue();
                } else if (cell.getCellType() == Cell.CELL_TYPE_ERROR) {
                    val = cell.getErrorCellValue();
                }

            }
        } catch (Exception e) {
            return "";
        }
        return val.toString();
    }


    private static String dropShopeeChineseInfo(String categoryName) {
        if (StringUtils.isBlank(categoryName)) {
            return categoryName;
        }
        return categoryName.replaceAll("\\(.*\\)", "");
    }

    enum BizType {
        /**
         * 未知(未支持解析的)
         **/
        UNDEFINED,

        /**
         * 1688
         **/
        ALI_1688("1688.com"),

        /**
         * 速卖通
         **/
        ALI_EXPRESS("aliexpress.com"),

        /**
         * SHOPEE
         **/
        SHOPEE("shopee.co.id", "shopee.tw", "shopee.vn", "shopee.co.th", "shopee.ph", "shopee.com.my", "shopee.sg", "shopee.com.br", "shopee.com.mx", "shopee.com.co", "shopee.cl", "shopee.pl", "shopee.com.ar"),

        /**
         * Cotton On
         **/
        COTTON_ON("cottonon.com"),
        ;
        @Getter
        private final Set<String> site;

        BizType(String... site) {
            if (site != null && site.length > 0) {
                this.site = Stream.of(site).collect(Collectors.toSet());
            } else {
                this.site = new HashSet<>();
            }
        }

    }
}
