package com.newnary.gsp.center.tpsi.infra.repository;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.newnary.api.base.common.PageCondition;
import com.newnary.api.base.common.PageList;
import com.newnary.common.utils.page.PageUtil;
import com.newnary.dao.base.converter.POConverterFactory;
import com.newnary.dao.base.helper.DaoHelper;
import com.newnary.dao.base.po.BaseQuery;
import com.newnary.gsp.center.tpsi.infra.model.ApiDockingResult;
import com.newnary.gsp.center.tpsi.infra.repository.db.converter.ApiDockingResultPOConverter;
import com.newnary.gsp.center.tpsi.infra.repository.db.dao.ApiDockingResultDao;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.ApiDockingResultPO;
import com.newnary.gsp.center.tpsi.infra.translator.PO2ModelCreatorTranslator;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class ApiDockingResultRepository implements IApiDockingResultRepository {

    @Resource
    private ApiDockingResultDao apiDockingResultDao;

    @Override
    public void store(ApiDockingResult apiDockingResult) {
        ApiDockingResultPO po = POConverterFactory.find(ApiDockingResultPOConverter.class)
                .convert2PO(apiDockingResult);

        if (po.id != null) {
            apiDockingResultDao.update(po);
        } else {
            apiDockingResultDao.insert(po);
        }
    }

    @Override
    public Optional<ApiDockingResult> loadByKeyAndType(String key, String type) {
        if (StringUtils.isEmpty(key) || StringUtils.isEmpty(type)) {
            return Optional.empty();
        }

        BaseQuery<ApiDockingResultPO> query = new BaseQuery<>(new ApiDockingResultPO());
        query.getData().setValueKey(key);
        query.getData().setValueType(type);

        return loadByQuery(query);
    }

    @Override
    public List<ApiDockingResult> loadByTypeAndStatus(String type, String status) {
        BaseQuery<ApiDockingResultPO> query = new BaseQuery<>(new ApiDockingResultPO());
        query.getData().setValueType(type);
        query.getData().setDataStatus(status);

        List<ApiDockingResultPO> pos = apiDockingResultDao.query(query);
        return pos.stream()
                .map(po -> ApiDockingResult.loadWith(PO2ModelCreatorTranslator.transApiDockingResultCreator(po)))
                .collect(Collectors.toList());
    }

    @Override
    public PageList<ApiDockingResult> pageQueryByTypeAndStatus(String type, String status, PageCondition condition) {
        Page<ApiDockingResultPO> page = PageHelper.startPage(condition.pageNum, condition.pageSize);

        BaseQuery<ApiDockingResultPO> query = new BaseQuery<>(new ApiDockingResultPO());
        query.getData().setValueType(type);
        query.getData().setDataStatus(status);

        apiDockingResultDao.query(query);

        PageList<ApiDockingResultPO> pageList = PageUtil.createPageList(page);

        List<ApiDockingResult> resultList = pageList.getItems().stream()
                .map(po -> ApiDockingResult.loadWith(PO2ModelCreatorTranslator.transApiDockingResultCreator(po)))
                .collect(Collectors.toList());

        return PageUtil.createPageList(pageList.getPageMeta(), resultList);

    }

    private Optional<ApiDockingResult> loadByQuery(BaseQuery<ApiDockingResultPO> query) {
        List<ApiDockingResultPO> pos = apiDockingResultDao.query(query);
        if (CollectionUtils.isNotEmpty(pos)) {
            return pos.stream()
                    .map(po -> ApiDockingResult.loadWith(PO2ModelCreatorTranslator.transApiDockingResultCreator(po)))
                    .findAny();
        }
        return Optional.empty();
    }

    public static void main(String[] args) {
        DaoHelper.genXMLWithFeature(
                "E:/SWWorkspace/sw-center/tpsi-center/tpsi-center-main/src/main/java/com/newnary/gsp/center/tpsi/infra/repository/db/dao/ApiDockingResultDao.xml",
                ApiDockingResultDao.class,
                ApiDockingResultPO.class,
                "api_docking_result",
                false);
    }

}
