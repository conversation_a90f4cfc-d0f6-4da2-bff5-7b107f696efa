package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.wms;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Getter
@Setter
public class EcCangUpdateReturnBillRequest {
    @NotNull(message = "退件号不能为空")
    private String return_code;
    @NotNull(message = "跟踪号不能为空")
    private String tracking_no;
    @NotNull(message = "仓库编码不能为空")
    private String warehouse_code;
    @NotNull(message = "退件类型不能为空")
    private String return_type;
    private String verify;
    private String reference_no;
    private String order_code;
    private String claim_code;
    private Date expected_date;
    private String return_desc;
    private String operation_desc;
    private String buyer_name;
    private String buyers_ein;
    @NotNull(message = "产品明细不能为空")
    private Items items;
    public static class Items{
        @NotNull(message = "产品sku不能为空")
        public String product_sku;
        @NotNull(message = "产品数量不能为空")
        public Integer quantity;
        @NotNull(message = "产品处理方式不能为空")
        public String process;
        public String note;

    }
}
