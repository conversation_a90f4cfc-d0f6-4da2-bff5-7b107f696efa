package com.newnary.gsp.center.tpsi.ninjavan;

import com.alibaba.fastjson.JSONObject;
import com.newnary.gsp.center.product.api.category.response.CategoryInfo;
import com.newnary.gsp.center.tpsi.api.logisticsService.request.LogisticsTrackOrderCommand;
import com.newnary.gsp.center.tpsi.app.job.JTTMSJobManager;
import com.newnary.gsp.center.tpsi.app.listener.mq.TransportOrder_ThirdPartyCancelProcessor;
import com.newnary.gsp.center.tpsi.app.listener.mq.TransportOrder_ThirdPartyLabelProcessor;
import com.newnary.gsp.center.tpsi.app.listener.mq.TransportOrder_ThirdPartyPushProcessor;
import com.newnary.gsp.center.tpsi.ctrl.ninjavan.NinjavanRestController;
import com.newnary.gsp.center.tpsi.infra.model.vo.LogisticsServiceConstants;
import com.newnary.gsp.center.tpsi.infra.model.vo.LogisticsServiceContext;
import com.newnary.gsp.center.tpsi.infra.rpc.CategoryRpc;
import com.newnary.gsp.center.tpsi.service.JT.impl.JTLogisticsApiSvelmpl;
import com.newnary.gsp.center.tpsi.service.ninjavan.impl.NinjavanLogisticsApiSvelmpl;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.messagebody.gsp.logistics.mo.TransportOrderThirdPartyMO;
import com.newnary.mq.starter.consumer.MQMessage;
import com.newnary.test.starter.BaseTestInjectTenant;
import org.junit.Test;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;

/**
 * 能者物流对接
 */
public class NinJavanApiTest extends BaseTestInjectTenant {

    private static final SimpleDateFormat SIMPLE_TIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    protected String tenantId() {
        return "TENANT3924117688555169382400";
    }

    @Resource
    private NinjavanLogisticsApiSvelmpl nanJavanApiSve;
    @Resource
    private JTTMSJobManager jttmsJobManager;
    @Resource
    private NinjavanRestController ninjavanCtrl;
    @Resource
    private TransportOrder_ThirdPartyPushProcessor pushProcessor;
    @Resource
    private TransportOrder_ThirdPartyCancelProcessor cancelProcessor;
    @Resource
    private TransportOrder_ThirdPartyLabelProcessor labelProcessor;
    @Resource
    private CategoryRpc categoryRpc;
    @Resource
    private JTLogisticsApiSvelmpl jtLogisticsApiSvelmpl;

    @Test
    public void test_getToken() {
        String accessToken = nanJavanApiSve.loadAccessToken();
        System.out.println(accessToken);

    }


    @Test
    public void test_createOrder() {
//        CreateOrderNinJavanCommand command = new CreateOrderNinJavanCommand();
//
////        command.setServiceCode("CNSG0015");
//        command.setSourceOrderId("222222");
//        command.setSourceReferenceId("222222");
//
//        //发货地
//        CreateNinJavanOrderFrom from = new CreateNinJavanOrderFrom();
//        from.setName("测试");
//        from.setAddressLine1("测试");
//        from.setCity("测试");
//        from.setStateProvince("测试");
//        from.setPostCode("8000");
//        from.setCountryCode("PH");
//        from.setContactNumber("15083485454");
//        from.setContactEmail("<EMAIL>");
//        command.setFrom(from);
//
//        //收货地
//        CreateNinJavanOrderTo to = new CreateNinJavanOrderTo();
//        to.setName("测试人信息");
//        to.setAddressLine1("测试人信息");
//        to.setCity("测试人信息2");
//        to.setStateProvince("测试人信息");
//        to.setPostCode("8000");
//        to.setCountryCode("PH");
//        to.setContactNumber("15083485454");
//        to.setContactEmail("<EMAIL>");
//        command.setTo(to);
//
//        //包裹信息
//        CreateNinJavanOrderParcelDetail orderParcelDetail = new CreateNinJavanOrderParcelDetail();
//        orderParcelDetail.setTaxId("测试包裹信息");
//        orderParcelDetail.setCustomsCurrency("PHP");
//        command.setParcelDetails(orderParcelDetail);
//
//
//        //商品信息
//        CreateNinJavanOrderItem orderItem = new CreateNinJavanOrderItem();
//        orderItem.setDescription("测试描述");
//        orderItem.setUnitValue(12.0F);
//        orderItem.setQuantity(1);
//        orderItem.setUnitWeight(12.0F);
//        command.setItems(Arrays.asList(orderItem));
//        NinJavanOrderResp order = nanJavanApiSve.createOrder(command);
//        System.out.println(JSONObject.toJSONString(order));
    }


    @Test
    public void test_doCancelOrder() {
//        CancelOrderNinJavanCommand command = new CancelOrderNinJavanCommand();
//        command.setTrackingId("PRO23222222");
//        command.setCancelReason("Test cancellation");
//        nanJavanApiSve.doCancel(command);
    }


    @Test
    public void test_printSheet() {
//        SheetOrderNinJavanCommand command = new SheetOrderNinJavanCommand();
//        command.setTrackingId("PRO231143103309367537664");
//        NinJavanPrintSheetResp ninJavanPrintSheetResp = nanJavanApiSve.printSheet(command);
//        System.out.println(JSONObject.toJSONString(ninJavanPrintSheetResp));
    }

    @Test
    public void test_webhookReturn() {
        String payload = "{\"shipper_id\":12345,\"status\":\"Returned to Sender\",\"shipper_ref_no\":\"\",\"tracking_ref_no\":\"ABC12345\",\"shipper_order_ref_no\":\"12345\",\"timestamp\":\"2020-08-15T21:31:24+0800\",\"id\":\"12345\",\"tracking_id\":\"PRO23222222\"}";
        ninjavanCtrl.testDealStatus(payload);
    }

    @Test
    public void testWebhook() {
        String payload = "{\"shipper_id\":12345,\"status\":\"Successful Delivery\",\"shipper_ref_no\":\"\",\"tracking_ref_no\":\"ABC12345\",\"shipper_order_ref_no\":\"12345\",\"timestamp\":\"2020-08-15T21:31:24+0800\",\"id\":\"12345\",\"tracking_id\":\"PRO231147076836562341888\"}";
        ninjavanCtrl.testDealStatus(payload);
    }

    @Test
    public void test() {
        JSONObject jsonObject = JSONObject.parseObject("{\"data\":{\"requested_tracking_id\":\"\",\"tracking_id\":\"PRO231143094472434405376\",\"delivery\":{\"delivery_start_date\":\"2023-08-23\",\"allow_self_collection\":false}}}");
        System.out.println(jsonObject);
    }


    @Test
    public void test_processorPush() {
        MQMessage<TransportOrderThirdPartyMO> objectMQMessage = new MQMessage<>();
        TransportOrderThirdPartyMO transportOrderThirdPartyMO = new TransportOrderThirdPartyMO();
        transportOrderThirdPartyMO.setTransportOrderId("TO150801781686298");
        transportOrderThirdPartyMO.setThirdPartyCode("JT");
        objectMQMessage.setContent(transportOrderThirdPartyMO);
        pushProcessor.doProcess(objectMQMessage);
    }

    @Test
    public void test_processorCancel() {
        MQMessage<TransportOrderThirdPartyMO> objectMQMessage = new MQMessage<>();
        TransportOrderThirdPartyMO transportOrderThirdPartyMO = new TransportOrderThirdPartyMO();
        transportOrderThirdPartyMO.setTransportOrderId("TO150801759666254");
        transportOrderThirdPartyMO.setThirdOrderId("910180763983");
        transportOrderThirdPartyMO.setThirdPartyCode("JT");
        transportOrderThirdPartyMO.setCancelReason("测试");
        objectMQMessage.setContent(transportOrderThirdPartyMO);
        cancelProcessor.doProcess(objectMQMessage);
    }

    @Test
    public void test_processorPrintSheet() {
        MQMessage<TransportOrderThirdPartyMO> objectMQMessage = new MQMessage<>();
        TransportOrderThirdPartyMO transportOrderThirdPartyMO = new TransportOrderThirdPartyMO();
        transportOrderThirdPartyMO.setTransportOrderId("TO135737150406727");
        transportOrderThirdPartyMO.setThirdOrderId("PRO231174975740460371968");
        transportOrderThirdPartyMO.setThirdPartyCode("NINJAVAN");
        objectMQMessage.setContent(transportOrderThirdPartyMO);
        labelProcessor.doProcess(objectMQMessage);
    }

    @Test
    public void test_categoryRpc() {
        CategoryInfo categoryById = categoryRpc.getCategoryById("124734050");
        System.out.println(JSONObject.toJSONString(categoryById));
    }


    @Test
    public void test_queryTrack() {
        LogisticsTrackOrderCommand command = new LogisticsTrackOrderCommand();
        command.setTrackingId("910176596575,941401807769");
        command.setLanguage("en");
        LogisticsServiceContext.getCurrentContext().put(LogisticsServiceConstants.LOGISTICS_TRACK_DOMAIN, command);
        jtLogisticsApiSvelmpl.queryTrack();
    }

    @Test
    public void test_ManagerJob() {
        ReturnT<String> stringReturnT = jttmsJobManager.queryTrack("{\"thirdPartySystemId\":\"TEST_JT\",\"transportOrderId\":\"\"}");

    }




}
