package com.newnary.gsp.center.tpsi.infra.repository;

import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2021/12/10 18:55
 */
public interface IThirdPartySystemRepository {

    void store(ThirdPartySystem thirdPartySystem);

    Optional<ThirdPartySystem> loadByBizId(String bizId);

    Optional<ThirdPartySystem> loadSystemByBizIdAndName(String provider, String name);

    Optional<ThirdPartySystem> loadBySystemId(String systemId);

    List<ThirdPartySystem> getSystemsByProvider(String provider);
}
