package com.newnary.gsp.center.tpsi.api.haiying.enums;

/**
 * @Author: jack
 * @CreateTime: 2022-7-13
 */
public enum HaiYingLazadaProductListOrderBy {

    price("价格"),
    rating("商品评分"),
    review("商品评论数"),
    three_days_new_reviews("商品前3天销售件数"),
    seven_days_new_reviews("商品前7天销售件数"),
    thirty_days_new_reviews("商品前30天销售件数"),
    first_review_date("商品第1个评论时间"),
    insert_time("商品首次发现时间"),
    ;

    private String description;

    HaiYingLazadaProductListOrderBy(String description) {
        this.description = description;
    }

}
