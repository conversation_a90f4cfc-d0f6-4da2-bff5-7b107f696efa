package com.newnary.gsp.center.tpsi.infra.model;

import com.newnary.gsp.center.tpsi.infra.model.creator.ThirdPartySystemCreator;
import com.newnary.gsp.center.tpsi.infra.model.updater.ThirdPartySystemUpdater;
import com.newnary.gsp.center.tpsi.infra.model.vo.SystemBizType;
import com.newnary.gsp.center.tpsi.infra.model.vo.SystemId;
import com.newnary.gsp.center.tpsi.infra.model.vo.SystemProvider;
import com.newnary.gsp.center.tpsi.infra.model.vo.SystemStatus;
import com.newnary.spring.cloud.domain.Aggregate;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2021/12/10 11:57
 */
@Getter
public class ThirdPartySystem extends Aggregate {

    /**
     * 系统名称
     */
    private String name;

    /**
     * 业务id
     */
    private String bizId;

    /**
     * 系统id
     */
    private SystemId systemId;

    /**
     * 系统服务商: 通途系统、易仓系统 TONGTU ECCANG
     */
    private SystemProvider provider;

    /**
     * 系统业务类型,例如: 供应商系统、分销商系统
     */
    private SystemBizType bizType;

    /**
     * 系统状态
     */
    private SystemStatus status;

    /**
     * 商品同步job cron
     * 库存同步job
     */
/*    private List<ScheduleJob> scheduleJobs;*/

    /**
     * 系统对接参数{"appKey":"xxx","appSecret":"xxx","warehouseName":"xxx"}
     */
    private String params;

    public static ThirdPartySystem createWith(ThirdPartySystemCreator creator) {
        return new ThirdPartySystem(creator);
    }

    public static ThirdPartySystem loadWith(ThirdPartySystemCreator creator) {
        return new ThirdPartySystem(creator.getId(), creator);
    }

    public void updateWith(ThirdPartySystemUpdater updater) {
        setName(updater.getName());
        setBizId(updater.getBizId());
        setProvider(SystemProvider.valueOf(updater.getProvider()));
        setBizType(SystemBizType.valueOf(updater.getBizType()));
        setParams(updater.getParams());
    }

    private ThirdPartySystem(ThirdPartySystemCreator creator) {
        setName(creator.getName());
        setBizId(creator.getBizId());
        setSystemId(new SystemId());
        setProvider(SystemProvider.valueOf(creator.getProvider()));
        setBizType(SystemBizType.valueOf(creator.getBizType()));
        setParams(creator.getParams());

        init();
    }

    private ThirdPartySystem(Long id, ThirdPartySystemCreator creator) {
        super(id);
        setName(creator.getName());
        setBizId(creator.getBizId());
        setSystemId(new SystemId(creator.getSystemId()));
        setProvider(SystemProvider.valueOf(creator.getProvider()));
        setBizType(SystemBizType.valueOf(creator.getBizType()));
        setStatus(SystemStatus.valueOf(creator.getStatus()));
        setParams(creator.getParams());


    }

    private void init() {
        setStatus(SystemStatus.ENABLED);
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public void setSystemId(SystemId systemId) {
        this.systemId = systemId;
    }

    public void setProvider(SystemProvider provider) {
        this.provider = provider;
    }

    public void setBizType(SystemBizType bizType) {
        this.bizType = bizType;
    }

    public void setStatus(SystemStatus status) {
        this.status = status;
    }

/*    public void setScheduleJobs(List<ScheduleJob> scheduleJobs) {
        this.scheduleJobs = scheduleJobs;
    }*/

    public void setParams(String params) {
        this.params = params;
    }
}
