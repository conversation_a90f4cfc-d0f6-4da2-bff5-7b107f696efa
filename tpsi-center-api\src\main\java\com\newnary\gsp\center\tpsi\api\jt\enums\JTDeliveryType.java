package com.newnary.gsp.center.tpsi.api.jt.enums;

import lombok.Getter;

/**
 * @Author: jack
 * @CreateTime: 2023-8-15
 */
@Getter
public enum JTDeliveryType {

    NORMAL_DELIVERY("正常派送","1"),
    MYSELF_GET("自提件","2");

    private String name;
    private String value;

    JTDeliveryType(String name, String value){
        this.name = name;
        this.value = value;
    }

    public String getValue(){
        return this.value;
    }

}
