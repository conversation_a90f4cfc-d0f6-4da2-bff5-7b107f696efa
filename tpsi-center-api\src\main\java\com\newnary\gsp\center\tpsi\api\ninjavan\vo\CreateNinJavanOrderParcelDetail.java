package com.newnary.gsp.center.tpsi.api.ninjavan.vo;

import lombok.Data;

import javax.validation.constraints.Size;

/**
 * 创建能者物流 parcel_details
 *
 * <AUTHOR>
 * @since Created on 2023-08-11
 **/
@Data
public class CreateNinJavanOrderParcelDetail {

    /**
     * 收件人的税号。
     **/
    private String taxId;


    /**
     * 海关货币。
     **/
    @Size(max = 1000)
    private String customsCurrency;



}
