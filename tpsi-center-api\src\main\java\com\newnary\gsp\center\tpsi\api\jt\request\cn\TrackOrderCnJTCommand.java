package com.newnary.gsp.center.tpsi.api.jt.request.cn;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 物流服务轨迹请求体
 *
 * <AUTHOR>
 * @since Created on 2023-11-17
 **/
@Data
public class TrackOrderCnJTCommand {


    @NotEmpty(message = "单号(不能为空)")
    private String nos;


    /**
     * 单号类型: 1=运单号，2=尾程派送单号，4=客户单号，
     * 不传则以派送单号查询
     */
    private Integer noType;



}
