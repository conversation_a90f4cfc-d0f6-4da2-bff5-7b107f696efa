package com.newnary.gsp.center.tpsi.infra.repository.db.converter;

import com.newnary.dao.base.converter.POConverter;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartySystemPO;

/**
 * <AUTHOR>
 * @Date 2021/12/14 14:30
 */
public class ThirdPartySystemPOConverter implements POConverter<ThirdPartySystemPO, ThirdPartySystem> {
    @Override
    public ThirdPartySystemPO convert2PO(ThirdPartySystem domain) {
        ThirdPartySystemPO po = new ThirdPartySystemPO();
        po.setId(domain.getId());
        po.setSystemId(domain.getSystemId().getId());
        po.setName(domain.getName());
        po.setBizId(domain.getBizId());
        po.setSystemProvider(domain.getProvider().name());
        po.setBizType(domain.getBizType().name());
        po.setSystemStatus(domain.getStatus().name());
        po.setParams(domain.getParams());
        return po;
    }
}
