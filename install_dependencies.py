#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖安装脚本
自动安装发票OCR工具所需的所有依赖包
"""

import subprocess
import sys
import os

def run_command(command):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def install_package(package_name):
    """安装单个包"""
    print(f"正在安装 {package_name}...")
    success, stdout, stderr = run_command(f"pip install {package_name}")
    
    if success:
        print(f"✓ {package_name} 安装成功")
        return True
    else:
        print(f"✗ {package_name} 安装失败")
        if stderr:
            print(f"错误信息: {stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version < (3, 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        return False
    else:
        print("✓ Python版本符合要求")
        return True

def main():
    """主函数"""
    print("=" * 60)
    print("发票OCR工具依赖安装脚本")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        return
    
    print("\n开始安装依赖包...")
    print("-" * 40)
    
    # 要安装的包列表（按安装顺序）
    packages = [
        "numpy>=1.21.0,<1.25.0",
        "Pillow>=9.0.0",
        "opencv-python>=4.5.0",
        "pandas>=1.3.0",
        "openpyxl>=3.0.0",
        "PyMuPDF>=1.20.0",
        "paddlepaddle>=2.5.0",
        "paddleocr>=2.7.0"
    ]
    
    success_count = 0
    failed_packages = []
    
    for package in packages:
        if install_package(package):
            success_count += 1
        else:
            failed_packages.append(package)
        print()
    
    print("=" * 60)
    print(f"安装结果: {success_count}/{len(packages)} 个包安装成功")
    
    if failed_packages:
        print(f"\n❌ 以下包安装失败:")
        for package in failed_packages:
            print(f"  - {package}")
        
        print(f"\n您可以尝试手动安装失败的包:")
        for package in failed_packages:
            print(f"pip install {package}")
    else:
        print("\n🎉 所有依赖包安装成功！")
        
        # 测试安装
        print("\n正在测试安装...")
        try:
            import paddleocr
            import pandas
            import numpy
            import PIL
            import cv2
            import fitz
            import openpyxl
            print("✓ 所有包导入成功")
            
            # 测试创建OCR对象
            print("正在测试PaddleOCR初始化...")
            ocr = paddleocr.PaddleOCR(use_textline_orientation=True, lang='ch')
            print("✓ PaddleOCR初始化成功")
            
            print("\n🎉 安装完成！现在可以使用发票OCR工具了。")
            print("\n使用方法:")
            print("1. 将发票文件放入一个文件夹")
            print("2. 运行: python invoice_ocr_processor.py 文件夹路径")
            print("3. 或运行: python example_usage.py")
            
        except ImportError as e:
            print(f"❌ 包导入测试失败: {e}")
        except Exception as e:
            print(f"❌ PaddleOCR初始化失败: {e}")
            print("这可能是首次运行，PaddleOCR正在下载模型文件...")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
