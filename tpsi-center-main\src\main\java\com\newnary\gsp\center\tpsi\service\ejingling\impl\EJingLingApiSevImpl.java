package com.newnary.gsp.center.tpsi.service.ejingling.impl;

import com.newnary.gsp.center.tpsi.infra.client.ejingling.EJingClient;
import com.newnary.gsp.center.tpsi.infra.client.ejingling.utils.HttpPageUtil;
import com.newnary.gsp.center.tpsi.infra.client.ejingling.valobj.request.*;
import com.newnary.gsp.center.tpsi.infra.client.ejingling.valobj.response.*;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.service.ejingling.IEJingLingApiSev;
import com.newnary.gsp.center.tpsi.service.impl.SystemClientSve;
import com.newnary.spring.cloud.anno.Validation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

@Slf4j
@Component
public class EJingLingApiSevImpl extends SystemClientSve implements IEJingLingApiSev {

    @Validation
    @Override
    public EJingLingGoodsListResponse fullGoodsList(String thirdPartySystemId, EJingLingFullGoodsListReq req) {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        EJingClient eJingClient = new EJingClient(thirdPartySystem.getParams());
        EJingLingGoodsListResponse fullGoodsList = eJingClient.getFullGoodsList(req);
        if (ObjectUtils.isNotEmpty(fullGoodsList)) {
            List<EJingLingGoodsListResponse.OuterGoodsVo> outerGoodsVoList = fullGoodsList.getOuterGoodsVoList();
            if (CollectionUtils.isNotEmpty(outerGoodsVoList)) {
                outerGoodsVoList.forEach(vo -> {
                    EJingLingGoodsListResponse.GoodsPic goodsPicList = vo.getGoodsPicList();
                    if (ObjectUtils.isNotEmpty(goodsPicList)) {
                        String htmlUrl = goodsPicList.getHtmlUrl();
                        if (StringUtils.isNotBlank(htmlUrl)) {
                            try {
                                goodsPicList.setHtmlUrl(HttpPageUtil.doGet(htmlUrl,new HashMap<>(),new HashMap<>()));
                            } catch (Exception e) {
                                log.error(e.getMessage());
                            }
                        }
                    }
                });
            }
        }
        return fullGoodsList;
    }

    @Validation
    @Override
    public EJingLingGoodsListResponse shopFullGoodsList(String thirdPartySystemId, EJingLingShopFullGoodsListReq req) {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        EJingClient eJingClient = new EJingClient(thirdPartySystem.getParams());
        return eJingClient.getShopFullGoodsList(req);
    }

    @Override
    public List<EJingLingGoodsListResponse.OuterGoodsVo> getGoodsByGoodsId(String thirdPartySystemId, String ids) {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        EJingClient eJingClient = new EJingClient(thirdPartySystem.getParams());
        return eJingClient.getGoodsByGoodsIds(ids);
    }

    @Validation
    @Override
    public EJingLingGoodsListResponse increGoodsList(String thirdPartySystemId, EJingLingIncreGoodsListReq req) {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        EJingClient eJingClient = new EJingClient(thirdPartySystem.getParams());
        return eJingClient.getIncreGoodsList(req);
    }

    @Validation
    @Override
    public EJingLingGoodsListResponse shopIncreGoodsList(String thirdPartySystemId, EJingLingShopIncreGoodsListReq req) {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        EJingClient eJingClient = new EJingClient(thirdPartySystem.getParams());
        return eJingClient.getShopIncreGoodsList(req);
    }

    @Validation
    @Override
    public List<EJingLingCreateOrderResponse> createOrder(String thirdPartySystemId, EJingLingCreateOrderReq req) {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        EJingClient eJingClient = new EJingClient(thirdPartySystem.getParams());
        return eJingClient.createOrder(req);
    }

    @Validation
    @Override
    public String orderDeliver(String thirdPartySystemId, EJingLingOrderDeliverReq req) {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        EJingClient eJingClient = new EJingClient(thirdPartySystem.getParams());
        return eJingClient.orderDeliver(req);
    }

    @Override
    public List<EJingLingGetCourierResponse> getCourier(String thirdPartySystemId, EJingLingGetCourierReq req) {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        EJingClient eJingClient = new EJingClient(thirdPartySystem.getParams());
        return eJingClient.getCourier(req);
    }

    @Override
    public List<EJingLingLogisticsFirmResponse> getLogisticsFirm(String thirdPartySystemId) {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        EJingClient eJingClient = new EJingClient(thirdPartySystem.getParams());
        return eJingClient.getLogisticsFrirm();
    }

    @Override
    public List<EJingLingGetOrderResponse> getOrderByOrderNo(String thirdPartySystemId, String orderNo) {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        EJingClient eJingClient = new EJingClient(thirdPartySystem.getParams());
        return eJingClient.getOrderByOrderNo(orderNo);
    }
}
