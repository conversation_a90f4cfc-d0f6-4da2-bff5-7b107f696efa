package com.newnary.gsp.center.tpsi.infra.mapper;

import com.newnary.gsp.center.tpsi.infra.model.DeliveryOrderEcCangApiAssociation;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.DeliveryOrderEcCangApiAssociationPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface DeliveryOrderEcCangApiAssociationMapper {

    DeliveryOrderEcCangApiAssociationMapper INSTANCE = Mappers.getMapper(DeliveryOrderEcCangApiAssociationMapper.class);

    @Mapping(target = "associationId", source = "associationId.id")
    DeliveryOrderEcCangApiAssociationPO model2PO(DeliveryOrderEcCangApiAssociation model);
}
