package com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.bijia.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

// 多语言关键词搜索
@Data
public class QueryOpen1688MultiLanguageKeywordRequest {

    @NotNull(message = "查询参数不能为空")
    private OfferQueryParam offerQueryParam;

    @Getter
    @Setter
    public static class OfferQueryParam{
        /**
         * 搜索关键词
         */
        private String keyword;

        /**
         * 商品资料语言
         */
        private String country;

        /**
         * 分页起始页
         */
        private Integer beginPage;

        /**
         * 分页大小
         */
        private Integer pageSize;
    }

}
