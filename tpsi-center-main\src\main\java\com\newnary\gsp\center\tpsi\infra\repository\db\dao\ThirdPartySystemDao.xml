<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.newnary.gsp.center.tpsi.infra.repository.db.dao.ThirdPartySystemDao">

<resultMap id="thirdPartySystemPOResult" type="com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartySystemPO">
    <result column="name" property="name"/>
    <result column="biz_id" property="bizId"/>
    <result column="system_id" property="systemId"/>
    <result column="biz_type" property="bizType"/>
    <result column="params" property="params"/>
    <result column="system_provider" property="systemProvider"/>
    <result column="system_status" property="systemStatus"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="id" property="id"/>
    <result column="gmt_create" property="gmtCreate"/>
    <result column="gmt_modified" property="gmtModified"/>
</resultMap>

<sql id="thirdPartySystemPO_columns">
    name,
    biz_id,
    system_id,
    biz_type,
    params,
    system_provider,
    system_status,
    tenant_id,
    id,
    gmt_create,
    gmt_modified
</sql>

<sql id="thirdPartySystemPO_sqlForInsert">
    name,
    biz_id,
    system_id,
    biz_type,
    params,
    system_provider,
    system_status,
    tenant_id,
    id,
    gmt_create,
    gmt_modified
</sql>

<sql id="thirdPartySystemPO_columnsForInsert">
    #{name},
    #{bizId},
    #{systemId},
    #{bizType},
    #{params},
    #{systemProvider},
    #{systemStatus},
    #{tenantId},
    #{id},
    REPLACE(unix_timestamp(NOW(3)),'.',''),
    REPLACE(unix_timestamp(NOW(3)),'.','')
</sql>

<sql id="thirdPartySystemPO_setterForUpdate">
    <set>
        gmt_modified = REPLACE(unix_timestamp(NOW(3)),'.',''),
        name = #{name},
        biz_id = #{bizId},
        system_id = #{systemId},
        biz_type = #{bizType},
        params = #{params},
        system_provider = #{systemProvider},
        system_status = #{systemStatus},
    </set>
</sql>

<sql id="thirdPartySystemPO_selector">
    select
    <include refid="thirdPartySystemPO_columns"/>
    from third_party_system
</sql>

<sql id="thirdPartySystemPO_query_segment">
    <trim prefix="WHERE" prefixOverrides="AND|OR">
        <if test="data.name != null">
            AND name = #{data.name}
        </if>
        <if test="data.bizId != null">
            AND biz_id = #{data.bizId}
        </if>
        <if test="data.systemId != null">
            AND system_id = #{data.systemId}
        </if>
        <if test="data.bizType != null">
            AND biz_type = #{data.bizType}
        </if>
        <if test="data.params != null">
            AND params = #{data.params}
        </if>
        <if test="data.systemProvider != null">
            AND system_provider = #{data.systemProvider}
        </if>
        <if test="data.systemStatus != null">
            AND system_status = #{data.systemStatus}
        </if>
        <if test="data.id != null">
            AND id = #{data.id}
        </if>
        <if test="data.gmtCreate != null">
            AND gmt_create = #{data.gmtCreate}
        </if>
        <if test="data.gmtModified != null">
            AND gmt_modified = #{data.gmtModified}
        </if>
        <if test="ins != null and ins.size() > 0">
            AND
            <foreach collection="ins" item="item" open="(" separator="and" close=")">
                ${item.column} in
                <foreach collection="item.values" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </foreach>
        </if>
        <if test="notIns != null and notIns.size() > 0">
            AND
            <foreach collection="notIns" item="item" open="(" separator="and" close=")">
                ${item.column} not in
                <foreach collection="item.values" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </foreach>
        </if>
        <if test="equals != null and equals.size() > 0">
            AND
            <foreach collection="equals" item="item" open="(" separator="and" close=")">
                ${item.column} = #{item.value}
            </foreach>
        </if>
        <if test="notEquals != null and notEquals.size() > 0">
            AND
            <foreach collection="notEquals" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ <> ]]> #{item.value}
            </foreach>
        </if>
        <if test="lts != null and lts.size() > 0">
            AND
            <foreach collection="lts" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ < ]]> #{item.value}
            </foreach>
        </if>
        <if test="gts != null and gts.size() > 0">
            AND
            <foreach collection="gts" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ > ]]> #{item.value}
            </foreach>
        </if>
        <if test="likes != null and likes.size() > 0">
            AND
            <foreach collection="likes" item="item" open="(" separator="and" close=")">
                ${item.column} like concat('%', #{item.value}, '%')
            </foreach>
        </if>
        <if test="ltes != null and ltes.size() > 0">
            AND
            <foreach collection="ltes" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ <= ]]> #{item.value}
            </foreach>
        </if>
        <if test="gtes != null and gtes.size() > 0">
            AND
            <foreach collection="gtes" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ >= ]]> #{item.value}
            </foreach>
        </if>
        <if test="ranges != null and ranges.size() > 0">
            AND
            <foreach collection="ranges" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ >= ]]> #{item.from} and ${item.column} <![CDATA[ < ]]> #{item.to}
            </foreach>
        </if>
        AND tenant_id = #{tenantId}
    </trim>
</sql>


<insert id="insert" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartySystemPO">
    insert into third_party_system
    (
        <include refid="thirdPartySystemPO_sqlForInsert"/>
    )
    values
    (
        <include refid="thirdPartySystemPO_columnsForInsert"/>
    )
</insert>

<update id="update" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartySystemPO">
    update third_party_system
    <include refid="thirdPartySystemPO_setterForUpdate" />
    where id = #{id}
    and tenant_id = #{tenantId}
</update>


<update id="updateByQuery" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartySystemPO">
    update third_party_system
    <set>
        gmt_modified = REPLACE(unix_timestamp(NOW(3)),'.',''),
        name = #{update.name},
        biz_id = #{update.bizId},
        system_id = #{update.systemId},
        biz_type = #{update.bizType},
        params = #{update.params},
        system_provider = #{update.systemProvider},
        system_status = #{update.systemStatus},
    </set>
    <include refid="thirdPartySystemPO_query_segment"/>
</update>

<!-- Delete -->
<delete id="delete">
    delete from third_party_system
    <include refid="thirdPartySystemPO_query_segment"/>
</delete>
<delete id="deleteById">
    delete from third_party_system
    where id = #{id}
    and tenant_id = #{tenantId}
</delete>





<!-- query class -->
<sql id="thirdPartySystemPO_groupBy">
    <if test="groupBys != null and groupBys.size() > 0">
        group by
        <foreach collection="groupBys" item="item" open="" separator="," close="">
            #{item}
        </foreach>
    </if>
</sql>

<sql id="thirdPartySystemPO_orderby">
    <if test="orderBys != null and orderBys.size() > 0">
        order by
        <foreach collection="orderBys" item="item" open="" separator="," close="">
            ${item.column} ${item.direction}
        </foreach>
    </if>
</sql>


<select id="query" resultMap="thirdPartySystemPOResult" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartySystemPO">
    <include refid="thirdPartySystemPO_selector"/>
    <include refid="thirdPartySystemPO_query_segment"/>
    <include refid="thirdPartySystemPO_groupBy"/>
    <include refid="thirdPartySystemPO_orderby"/>
</select>



<select id="count" resultType="java.lang.Long">
    SELECT COUNT(1) FROM third_party_system
    <include refid="thirdPartySystemPO_query_segment"/>
</select>

<select id="getById" resultMap="thirdPartySystemPOResult">
    <include refid="thirdPartySystemPO_selector"/>
    where id = #{id}
    and tenant_id = #{tenantId}
</select>

<select id="getByIdForUpdate" resultMap="thirdPartySystemPOResult">
    <include refid="thirdPartySystemPO_selector"/>
    where id = #{id}
    and tenant_id = #{tenantId}
    for update
</select>

</mapper>
