package com.newnary.gsp.center.tpsi.infra.repository.db.manager;

import com.newnary.dao.base.helper.DaoHelper;
import com.newnary.dao.base.po.BaseQuery;
import com.newnary.gsp.center.tpsi.infra.repository.db.dao.DeliveryOrderItemEcCangApiAssociationDao;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.DeliveryOrderItemEcCangApiAssociationPO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class DeliveryOrderItemEcCangApiAssociationManager {

    @Resource
    private DeliveryOrderItemEcCangApiAssociationDao deliveryOrderItemEcCangApiAssociationDao;

    public List<DeliveryOrderItemEcCangApiAssociationPO> getByParentId(String associationId) {
        BaseQuery<DeliveryOrderItemEcCangApiAssociationPO> query = new BaseQuery<>(new DeliveryOrderItemEcCangApiAssociationPO());
        query.getData().setAssociationId(associationId);
        return deliveryOrderItemEcCangApiAssociationDao.query(query);
    }

    public void deleteByParentId(String associationId) {
        BaseQuery<DeliveryOrderItemEcCangApiAssociationPO> query = new BaseQuery<>(new DeliveryOrderItemEcCangApiAssociationPO());
        query.getData().setAssociationId(associationId);
        deliveryOrderItemEcCangApiAssociationDao.delete(query);
    }

    public DeliveryOrderItemEcCangApiAssociationDao getDao() {
        return deliveryOrderItemEcCangApiAssociationDao;
    }

    public static void main(String[] args) {
        DaoHelper.genXMLWithFeature(
                "D:\\NewnaryWorkspace\\tpsi-center\\tpsi-center-main\\src\\main\\java\\com\\newnary\\gsp\\center\\tpsi\\infra\\repository\\db\\dao\\DeliveryOrderItemEcCangApiAssociationDao.xml",
                DeliveryOrderItemEcCangApiAssociationDao.class,
                DeliveryOrderItemEcCangApiAssociationPO.class,
                "delivery_order_item_eccang_api_association",
                false);
    }
}
