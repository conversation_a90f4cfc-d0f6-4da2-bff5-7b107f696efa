<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.newnary.gsp.center.tpsi.infra.repository.db.dao.CrawlerProductDao">

    <resultMap id="crawlerProductPOResult" type="com.newnary.gsp.center.tpsi.infra.repository.db.po.CrawlerProductPO">
        <result column="state" property="state"/>
        <result column="begin_page" property="beginPage"/>
        <result column="crawler_return" property="crawlerReturn"/>
        <result column="flush_version" property="flushVersion"/>
        <result column="category_name" property="categoryName"/>
        <result column="product_id" property="productId"/>
        <result column="page_size" property="pageSize"/>
        <result column="flush_state" property="flushState"/>
        <result column="crawler_product_id" property="crawlerProductId"/>
        <result column="crawler_return_state" property="crawlerReturnState"/>
        <result column="fail_reason" property="failReason"/>
        <result column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <sql id="crawlerProductPO_columns">
        state
        ,
    begin_page,
    flush_version,
    category_name,
    product_id,
    page_size,
    flush_state,
    crawler_product_id,
    crawler_return_state,
    fail_reason,
    id,
    gmt_create,
    gmt_modified
    </sql>

    <sql id="crawlerProductPO_sqlForInsert">
        state
        ,
    begin_page,
    flush_version,
    category_name,
    product_id,
    page_size,
    flush_state,
    crawler_product_id,
    crawler_return_state,
    fail_reason,
    id,
    gmt_create,
    gmt_modified
    </sql>

    <sql id="crawlerProductPO_columnsForInsert">
        #{state},
        #{beginPage},
        #{flushVersion},
        #{categoryName},
        #{productId},
        #{pageSize},
        #{flushState},
        #{crawlerProductId},
        #{crawlerReturnState},
        #{failReason},
        #{id},
        REPLACE(unix_timestamp(NOW(3)),'.',''),REPLACE(unix_timestamp(NOW(3)),'.','')
    </sql>

    <sql id="crawlerProductPO_setterForUpdate">
        <set>
            gmt_modified = REPLACE(unix_timestamp(NOW(3)),'.',''),
            state = #{state},
            begin_page = #{beginPage},
            flush_version = #{flushVersion},
            category_name = #{categoryName},
            product_id = #{productId},
            page_size = #{pageSize},
            flush_state = #{flushState},
            crawler_product_id = #{crawlerProductId},
            crawler_return_state = #{crawlerReturnState},
            fail_reason = #{failReason},
        </set>
    </sql>

    <sql id="crawlerProductPO_selector">
        select
        <include refid="crawlerProductPO_columns"/>
        from crawler_product
    </sql>

    <sql id="crawlerProductPO_query_segment">
        <trim prefix="WHERE" prefixOverrides="AND|OR">
            <if test="data.state != null">
                AND state = #{data.state}
            </if>
            <if test="data.beginPage != null">
                AND begin_page = #{data.beginPage}
            </if>
            <if test="data.flushVersion != null">
                AND flush_version = #{data.flushVersion}
            </if>
            <if test="data.categoryName != null">
                AND category_name = #{data.categoryName}
            </if>
            <if test="data.productId != null">
                AND product_id = #{data.productId}
            </if>
            <if test="data.pageSize != null">
                AND page_size = #{data.pageSize}
            </if>
            <if test="data.flushState != null">
                AND flush_state = #{data.flushState}
            </if>
            <if test="data.crawlerProductId != null">
                AND crawler_product_id = #{data.crawlerProductId}
            </if>
            <if test="data.crawlerReturnState != null">
                AND crawler_return_state = #{data.crawlerReturnState}
            </if>
            <if test="data.failReason != null">
                AND fail_reason = #{data.failReason}
            </if>
            <if test="data.id != null">
                AND id = #{data.id}
            </if>
            <if test="data.gmtCreate != null">
                AND gmt_create = #{data.gmtCreate}
            </if>
            <if test="data.gmtModified != null">
                AND gmt_modified = #{data.gmtModified}
            </if>
            <if test="ins != null and ins.size() > 0">
                AND
                <foreach collection="ins" item="item" open="(" separator="and" close=")">
                    ${item.column} in
                    <foreach collection="item.values" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </foreach>
            </if>
            <if test="notIns != null and notIns.size() > 0">
                AND
                <foreach collection="notIns" item="item" open="(" separator="and" close=")">
                    ${item.column} not in
                    <foreach collection="item.values" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </foreach>
            </if>
            <if test="equals != null and equals.size() > 0">
                AND
                <foreach collection="equals" item="item" open="(" separator="and" close=")">
                    ${item.column} = #{item.value}
                </foreach>
            </if>
            <if test="notEquals != null and notEquals.size() > 0">
                AND
                <foreach collection="notEquals" item="item" open="(" separator="and" close=")">
                    ${item.column} <![CDATA[ <> ]]> #{item.value}
                </foreach>
            </if>
            <if test="lts != null and lts.size() > 0">
                AND
                <foreach collection="lts" item="item" open="(" separator="and" close=")">
                    ${item.column} <![CDATA[ < ]]> #{item.value}
                </foreach>
            </if>
            <if test="gts != null and gts.size() > 0">
                AND
                <foreach collection="gts" item="item" open="(" separator="and" close=")">
                    ${item.column} <![CDATA[ > ]]> #{item.value}
                </foreach>
            </if>
            <if test="likes != null and likes.size() > 0">
                AND
                <foreach collection="likes" item="item" open="(" separator="and" close=")">
                    ${item.column} like concat('%', #{item.value}, '%')
                </foreach>
            </if>
            <if test="ltes != null and ltes.size() > 0">
                AND
                <foreach collection="ltes" item="item" open="(" separator="and" close=")">
                    ${item.column} <![CDATA[ <= ]]> #{item.value}
                </foreach>
            </if>
            <if test="gtes != null and gtes.size() > 0">
                AND
                <foreach collection="gtes" item="item" open="(" separator="and" close=")">
                    ${item.column} <![CDATA[ >= ]]> #{item.value}
                </foreach>
            </if>
            <if test="ranges != null and ranges.size() > 0">
                AND
                <foreach collection="ranges" item="item" open="(" separator="and" close=")">
                    ${item.column} <![CDATA[ >= ]]> #{item.from} and ${item.column} <![CDATA[ < ]]> #{item.to}
                </foreach>
            </if>
        </trim>
    </sql>


    <insert id="insert" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.CrawlerProductPO">
        insert into crawler_product
        (
        <include refid="crawlerProductPO_sqlForInsert"/>
        )
        values
        (
        <include refid="crawlerProductPO_columnsForInsert"/>
        )
    </insert>

    <update id="update" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.CrawlerProductPO">
        update crawler_product
        <include refid="crawlerProductPO_setterForUpdate"/>
        where id = #{id}
    </update>


    <update id="updateByQuery" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.CrawlerProductPO">
        update crawler_product
        <set>
            gmt_modified = REPLACE(unix_timestamp(NOW(3)),'.',''),
            state = #{update.state},
            begin_page = #{update.beginPage},
            flush_version = #{update.flushVersion},
            category_name = #{update.categoryName},
            product_id = #{update.productId},
            page_size = #{update.pageSize},
            flush_state = #{update.flushState},
            crawler_product_id = #{update.crawlerProductId},
            crawler_return_state = #{update.crawlerReturnState},
            fail_reason = #{update.failReason},
        </set>
        <include refid="crawlerProductPO_query_segment"/>
    </update>

    <!-- Delete -->
    <delete id="delete">
        delete from crawler_product
        <include refid="crawlerProductPO_query_segment"/>
    </delete>
    <delete id="deleteById">
        delete
        from crawler_product
        where id = #{id}
    </delete>


    <!-- query class -->
    <sql id="crawlerProductPO_groupBy">
        <if test="groupBys != null and groupBys.size() > 0">
            group by
            <foreach collection="groupBys" item="item" open="" separator="," close="">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="crawlerProductPO_orderby">
        <if test="orderBys != null and orderBys.size() > 0">
            order by
            <foreach collection="orderBys" item="item" open="" separator="," close="">
                ${item.column} ${item.direction}
            </foreach>
        </if>
    </sql>


    <select id="query" resultMap="crawlerProductPOResult"
            parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.CrawlerProductPO">
        <include refid="crawlerProductPO_selector"/>
        <include refid="crawlerProductPO_query_segment"/>
        <include refid="crawlerProductPO_groupBy"/>
        <include refid="crawlerProductPO_orderby"/>
    </select>


    <select id="count" resultType="java.lang.Long">
        SELECT COUNT(1) FROM crawler_product
        <include refid="crawlerProductPO_query_segment"/>
    </select>

    <select id="getById" resultMap="crawlerProductPOResult">
        <include refid="crawlerProductPO_selector"/>
        where id = #{id}
    </select>

    <select id="getByIdForUpdate" resultMap="crawlerProductPOResult">
        <include refid="crawlerProductPO_selector"/>
        where id = #{id}
        for update
    </select>

</mapper>
