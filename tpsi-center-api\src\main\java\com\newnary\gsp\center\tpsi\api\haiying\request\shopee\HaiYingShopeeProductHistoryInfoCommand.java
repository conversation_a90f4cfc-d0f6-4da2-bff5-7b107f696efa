package com.newnary.gsp.center.tpsi.api.haiying.request.shopee;

import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingStation;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingShopeeProductHistoryInfoCommand {

    /**
     * 站点(默认:马来西亚站)
     */
    @NotNull(message = "站点不能为空")
    private HaiYingStation station;

    /**
     * 商品id
     * (多个id用逗号分隔，单次最多100个id)
     */
    @NotNull(message = "商品id不能为空")
    private List<String> pids;

    /**
     * 商品数据时间起始值(格式:年-月-日时:分:秒)
     * (默认近三个月时间值)
     */
    private Long time_start;

    /**
     * 商品数据时间结束值(格式:年-月-日时:分:秒)
     */
    private Long time_end;

}
