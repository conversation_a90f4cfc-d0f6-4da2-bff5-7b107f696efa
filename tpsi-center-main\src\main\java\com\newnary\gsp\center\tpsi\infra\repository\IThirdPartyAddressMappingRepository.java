package com.newnary.gsp.center.tpsi.infra.repository;

import com.newnary.gsp.center.tpsi.infra.model.ThirdPartyAddressMapping;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.SystemId;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2021/12/10 18:55
 */
public interface IThirdPartyAddressMappingRepository {

    void store(ThirdPartyAddressMapping addressMapping);

    Optional<ThirdPartyAddressMapping> loadByBizId(SystemId SystemId);

    Optional<ThirdPartyAddressMapping> loadByVirtualPostcode(SystemId systemId,String virtualPostcode);

}
