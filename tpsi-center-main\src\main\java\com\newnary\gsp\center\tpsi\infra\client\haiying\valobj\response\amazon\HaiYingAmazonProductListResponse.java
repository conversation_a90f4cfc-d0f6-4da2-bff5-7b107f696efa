package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.amazon;

import com.newnary.gsp.center.tpsi.api.haiying.response.amazon.HaiYingAmazonProductSkuInfoDTO;
import com.newnary.gsp.center.tpsi.api.haiying.response.amazon.HaiYingAmazonSubCategoryInfoDTO;
import com.newnary.gsp.center.tpsi.api.haiying.response.amazon.HaiYingAmazonTopCategoryInfoDTO;
import lombok.Data;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingAmazonProductListResponse {

    /**
     * 商品id
     */
    private String asin;

    /**
     * 商品父类id
     */
    private String parent_asin;

    /**
     * 商品最低价格
     */
    private String asin_price_min;

    /**
     * 商品最高价格
     */
    private String asin_price_max;

    /**
     * 商品最初时间(yyyy-mm-dd)
     * (格式:1.null 2.2017-10-17)
     */
    private String fir_arrival;

    /**
     * 商品最新时间(yyyy-mm-dd)
     * (格式:1.null 2.2017-10-17)
     */
    private String fir_arrival_newest;

    /**
     * 商品发货方式
     * (Amanzon，FBM，FBA，other)
     */
    private String delivery;

    /**
     * 商品总评论数
     */
    private String customer_reviews;

    /**
     * 商品评分
     */
    private String score;

    /**
     * 商品图片url
     */
    private String img_url;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品卖家数量
     */
    private String follow_sellers_num;

    /**
     * 商品Q&A总数
     */
    private String answered_questions;

    /**
     * 商品品牌
     */
    private String brand;

    /**
     * 商品TOP排名HTML文本
     */
    private String best_sellers_rank;

    /**
     * 店铺名
     * (buy box所有者)
     */
    private String merchant;

    /**
     * 商品关联的asin Id(多个以逗号分隔)
     */
    private String listing_asins;

    /**
     * 是否是子类排名最高标识
     * (0:否   1:是)
     */
    private String best_rank_flag;

    /**
     * 商品前3天新增评论数(null为暂无的情况)
     * (已删除，暂留)
     */
    private String three_day_new_reviews;

    /**
     * 商品重量(0: 暂无)
     * 美国站：磅
     * 英国站：KG
     * 日本站：KG
     */
    private String shipping_weight;

    /**
     * Amazon's Choice
     * (0:否   1:是)
     */
    private String is_ama_choice;

    /**
     * Best Seller
     * (-1:不确定  0:否   1:是)
     */
    private String is_best_seller;

    /**
     * 支持Prime
     * (0:否   1:是)
     */
    private String is_prime;

    /**
     * 预留字段
     */
    private String sign_of_rank_rise;

    /**
     * 预留字段
     */
    private String rank_rise_avg_change;

    /**
     * FBM(中国卖家)标志
     * (0:否   1:是   null:暂无的情况)
     */
    private String chinese_sellers;

    /**
     * FBM(商家列表中出现中国卖家)标志
     * (0:否   1:是   null:暂无的情况)
     * (0:未发现  1:曾经出现  null:未知)
     */
    private String chinese_sellers_in_merhants;

    /**
     * 商品1级类目信息
     */
    private HaiYingAmazonTopCategoryInfoDTO[] top_cates;

    /**
     * 商品子级类目信息
     */
    private HaiYingAmazonSubCategoryInfoDTO[] sub_cates;

    /**
     * 商品最新抓取时间
     */
    private String last_upd_date;

    /**
     * 商品存在状态:
     * 0上架
     * 1下架
     */
    private String not_exist;

    /**
     * 商品状态:
     * 0正常
     * 1(Currently unavailable)
     * 2(Available from)
     */
    private String status;

    /**
     * 品牌是否注册
     * 0为未注册
     * 1为注册
     * 2为无品牌
     * 3为未核对
     */
    private String is_registered;

    /**
     * 公司注册地
     * HK，CN，GB，DE，FR等等国家代码
     * NOMESSAGE无信息
     * NOCHECKED未核对
     */
    private String registration;

    /**
     * amazon商品链接
     */
    private String asin_url;

    /**
     * 商品附图
     */
    private String[] asin_images_url;

    /**
     * 商品描述
     */
    private String prod_desc;

    /**
     * 店铺code
     */
    private String merchant_code;

    /**
     * 商品原价
     * (null: 无或者没有抓取到)
     */
    private String list_price;

    /**
     * 销售标识(coupon)
     * (null: 无或者没有抓取到)
     */
    private String is_coupon;

    /**
     * 销售标识(with deal)
     * (null: 无或者没有抓取到)
     */
    private String is_with_deal;

    /**
     * 销售标识(deal of the day)
     * (null: 无或者没有抓取到)
     */
    private String is_deal_of_the_day;

    /**
     * review评分分布信息
     * (null: 无或者没有抓取到)
     */
    private String review_info;

    /**
     * 是否新品
     * 1 是新品     0 不是     2 未核对
     */
    private String is_new;

    /**
     * 商品首次发现时间
     * (格式:1.null     2.2017-10-17)
     */
    private String created_date;

    /**
     * 商品关联的asin数量
     */
    private String listing_asins_number;

    /**
     * 商品空运匹配度
     */
    private String air_freight_match;

    /**
     *
     */
    private String month_new_review;

    /**
     * 商品详情页左上角类目路径
     */
    private String left_top_category_path;

    /**
     * 商品要点
     */
    private String bullet_point;

    /**
     * sku 规格列表
     */
    private HaiYingAmazonProductSkuInfoDTO[] sku_list;

}
