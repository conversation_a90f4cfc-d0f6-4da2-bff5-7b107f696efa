package com.newnary.gsp.center.tpsi.infra.model.vo;

/**
 * <AUTHOR>
 * @Date 2023/04/10 17:08
 */
public enum CrawlerProductFlushState {

    NONE_FLUSH(0),
    ALREADY_FLUSH(1);

    private Integer flushState;

    CrawlerProductFlushState(Integer flushState) {
        this.flushState = flushState;
    }

    public Integer getFlushState() {
        return flushState;
    }

    public void setFlushState(Integer flushState) {
        this.flushState = flushState;
    }
}
