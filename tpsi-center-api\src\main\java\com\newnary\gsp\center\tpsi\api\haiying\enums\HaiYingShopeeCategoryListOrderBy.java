package com.newnary.gsp.center.tpsi.api.haiying.enums;

/**
 * @Author: jack
 * @CreateTime: 2022-7-13
 */
public enum HaiYingShopeeCategoryListOrderBy {

    @Deprecated
    /** (已取消) */
    products_sold_num("类目的前30天出过单的商品总数"),
    payment("类目前30天销售金额"),
    sold_sub_top_percent("类目销售件数占一级类目销售件数百分比"),
    payment_sub_top_percent("类目销售额占一级类目销售额百分比"),
    sold("类目商前30天销售件数"),
    products_num_sub_top_percent("类目销售件数占一级类目销售件数百分比"),
    historical_sold_sub_top_percent("类目销售额占一级类目销售额百分比"),
    products_num("类目商品总数"),
    local_products_num("类目商品总数(本地)"),
    overseas_products_num("目商品总数(海外)"),
    historical_sold("类目总销售件数"),
    local_historical_sold("类目总销售件数(本地)"),
    overseas_historical_sold("类目总销售件数(海外)"),
//    sold_sub_top_percent("类目前30天总销售件数占一级类目前30天总销售件数百分比"),
//    sold("类目前30天总销售件数"),
    local_sold("类目前30天总销售件数(本地)"),
    overseas_sold("类目前30天总销售件数(海外)"),
    Payment("类目前30天总销售金额"),
    local_payment("类目前30天总销售金额(本地)"),
    overseas_payment("类目前30天总销售金额(海外)"),
    ;

    private String description;

    HaiYingShopeeCategoryListOrderBy(String description) {
        this.description = description;
    }

}
