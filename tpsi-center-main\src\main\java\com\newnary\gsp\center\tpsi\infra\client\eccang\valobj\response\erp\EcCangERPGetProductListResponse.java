package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.erp;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Data
public class EcCangERPGetProductListResponse {

    /**
     * skuid
     */
    private String productSku;

    /**
     * 组织机构
     */
    private Integer userOrganizationId;

    /**
     * 一级品类代码
     */
    private String procutCategoryCode1;

    /**
     * 二级品类代码
     */
    private String procutCategoryCode2;

    /**
     * 三级品类代码
     */
    private String procutCategoryCode3;


    /**
     * 名称CN
     */
    private String productTitle;


    /**
     * 名称EN
     */
    private String productTitleEn;


    /**
     * 产品款式代码
     */
    private String productSpu;


    /**
     * -	申报价值 （11-8以后版本，币种固定USD）；
     */
    private BigDecimal productDeclaredValue;


    /**
     * -	申报币种：例如
     * USD   已删除（11-8以后版本）
     */
    private String pdDeclareCurrencyCode;


    /**
     * 重量，单位
     * Kg
     */
    private BigDecimal productWeight;


    /**
     * -	净重，单位
     * Kg
     */
    private BigDecimal productNetWeight;


    /**
     * 默认供应商代码。提供接口
     * getAllSupplier
     * 查询
     */
    private String defaultSupplierCode;


    /**
     * 供应商品号
     */
    private String supplierSku;


    /**
     * 产品状态: 0:不可用,1:可用,2:开发产品
     */
    private String productStatus;


    /**
     * 产品销售状态Id，数据可自定义。提供接口
     * getSaleStatus
     * 查询
     */
    private Integer saleStatus;


    /**
     * -	是否质检
     * 0
     * 否
     * 1
     * 是
     */
    private Integer isQc;


    /**
     * 是否存在有效期
     * 0
     * 否
     * 1
     * 是
     */
    private Integer isExpDate;


    /**
     * 是否赠品
     * 0
     * 否
     * 1
     * 是
     */
    private Integer isGift;


    /**
     * 仓库条码
     */
    private String warehouseBarcode;


    /**
     * 产品长度，单位
     * CM(包装尺寸)
     */
    private BigDecimal productLength;


    /**
     * 产品宽度，单位
     * CM(包装尺寸)
     */
    private BigDecimal productWidth;


    /**
     * -	产品高度，单位
     * CM(包装尺寸)
     */
    private BigDecimal productHeight;


    /**
     * 设计师Id
     */
    private Integer designerId;


    /**
     * -	采购负责人Id
     */
    private Integer personOpraterId;


    /**
     * 销售负责人Id
     */
    private Integer personSellerId;


    /**
     * 开发负责人Id
     */
    private Integer personDevelopId;


    /**
     * 附属销售员字段 附属销售员有多条数据时，用“，”逗号隔开
     */
    private String sellerId;


    /**
     * 产品创建时间
     */
    private String productAddTime;


    /**
     * 产品更新时间
     */
    private String productUpdateTime;


    /**
     * -	审核时间
     */
    private String ppnReleaseDate;


    /**
     * 是否是组合产品 ，
     * 0
     * 否
     * 1
     * 是
     */
    private Integer isCombination;


    /**
     * 组合产品明细
     * getProductCombination
     * 为
     * 1
     * 时返回
     */
    private ProductCombination productCombination;

    @Setter
    @Getter
    public static class ProductCombination {


        /**
         * FNSKU
         */
        private String pcrFnsku;


        /**
         * FBA-ASIN
         */
        private String pcrFbaAsin;


        /**
         * -	仓库Id，
         * 0
         * 为全部仓库
         */
        private String warehouseId;


        /**
         * -	创建时间
         */
        private String pcrAddTime;


        /**
         * 更新时间
         */
        private String pcrUpdateTime;


        /**
         * 组合子产品SKU明细
         */
        private List<SubProduct> subProducts;

        @Setter
        @Getter
        public static class SubProduct {


            /**
             * 组合子产品SKU
             */
            private String pcrProductSku;


            /**
             * 组合子产品数量
             */
            private Integer pcrQty;
        }
    }


    /**
     * 产品箱规信息
     * getProperty
     * 为
     * 1
     * 时返回
     */
    private ProductBox ductBox;

    @Setter
    @Getter
    public static class ProductBox {


        /**
         * 名称
         */
        private String boxName;


        /**
         * 英文名称
         */
        private String boxNameEn;


        /**
         * 长
         */
        private BigDecimal boxLength;

        /**
         * 宽
         */
        private BigDecimal boxWidth;


        /**
         * 高
         */
        private BigDecimal boxHeight;


        /**
         * 重量
         */
        private BigDecimal boxWeight;


        /**
         * 数量
         */
        private Integer quantity;


        /**
         * 箱规状态：
         * 0
         * 不可用
         * 1
         * 可用
         */
        private Integer boxStatus;
    }


    /**
     * 产品自定义属性
     * getProperty
     * 为
     * 1
     * 时返回
     */
    private Property property;

    @Setter
    @Getter
    public static class Property {


        /**
         * 属性名称
         */
        private String attrName;


        /**
         * 属性英文名称
         */
        private String attrNameEn;


        /**
         * 属性值
         */
        private BigDecimal attrValue;
    }


    /**
     * 产品颜色Id，数据可自定义。提供接口
     * getProductColor
     * 查询
     */
    private Integer productColorId;


    /**
     * 产品颜色名称
     */
    private String productColorName;


    /**
     * 产品颜色英文名称
     */
    private String productColorNameEn;


    /**
     * 产品尺寸Id，数据可自定义。提供接口
     * getProductSize
     * 查询
     */
    private Integer productSizeId;


    /**
     * 产品尺寸名称
     */
    private String productSizeName;


    /**
     * 产品尺寸英文名称
     */
    private String productSizeNameEn;


    /**
     * 单位名称
     */
    private String puName;


    /**
     * 默认发货仓库ID
     */
    private Integer defaultWarehouseId;


    /**
     * EAN码
     */
    private String eanCode;


    /**
     * 一级品类名称
     */
    private String procutCategoryName1;


    /**
     * 二级品类名称
     */
    private String procutCategoryName2;


    /**
     * 三级品类名称
     */
    private String procutCategoryName3;


    /**
     * 运营方式：
     * 1
     * 代运营、
     * 2
     * 自运营
     */
    private Integer oprationType;


    /**
     * 品牌代码
     */
    private String brandCode;


    /**
     * 品牌名称
     */
    private String brandName;


    /**
     * 产品级别代码
     */
    private String prl_code;


    /**
     * 产品级别名称
     */
    private String prl_name;


    /**
     * -	海关编码
     */
    private String hsCode;


    /**
     * 规格
     */
    private String productSpecs;


    /**
     * 供应商产品币种
     */
    private String currency_code;


    /**
     * 供应商产品单价
     */
    private BigDecimal sp_unit_price;


    /**
     * 自定义分类
     * getProductCustomCategory
     * 为
     * 1
     * 时返回
     */
    private ProductCustomCategory productCustomCategory;

    @Setter
    @Getter
    public static class ProductCustomCategory {

        /**
         * 自定义分类名称
         */
        private String pucName;
    }


    /**
     * 图片链接 (如果有多个产品图片，以逗号分隔 如"url1,url2")
     */
    private String productImages;


    /**
     * 默认采购仓库
     */
    private Integer defaulBuyWarehouseId;


    /**
     * 产品物流属性
     */
    private String logisticAttribute;


    /**
     * 申报品名CN
     */
    private String pdOverseaTypeCn;


    /**
     * 申报品名EN
     */
    private String pdOverseaTypeEn;


    /**
     * 建议售价
     */
    private BigDecimal suggestPrice;


    /**
     * 建议售价币种
     */
    private String suggestPriceCurrencyCode;


    /**
     * 参考采购链接
     */
    private String refUrl;


    /**
     * 产品包材
     */
    private List<ProductPackage> productPackage;

    @Setter
    @Getter
    public static class ProductPackage {

        /**
         * 包材代码
         */
        private String packageCode;

        /**
         * 包材名称
         */
        private String packageName;

        /**
         * 数量
         */
        private Integer packageQty;

        /**
         * 仓库
         */
        private String packageWarehouse;
    }

    /**
     * 产品成本
     */
    private List<ProductCost> productCost;

    @Setter
    @Getter
    public static class ProductCost {

        /**
         * FNSKU
         */
        private String psSku;


        /**
         * FBA-ASIN
         */
        private String psCountry;

        /**
         * 默认采购成本
         */
        private BigDecimal psDefaultPurchaseCost;

        /**
         * 默认采购运费
         */
        private BigDecimal psDefaultPurchaseFreight;


        /**
         * 默认头程成本
         */
        private BigDecimal psDefaultFirstJourneyCost;

        /**
         * 默认关税成本
         */
        private BigDecimal psDefaultTariffCost;

        /**
         *
         */
        private String updateTime;
    }


    /**
     * 产品详情描述
     */
    private String pdDesc;


}
