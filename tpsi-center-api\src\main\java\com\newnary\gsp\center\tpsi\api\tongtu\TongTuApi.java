package com.newnary.gsp.center.tpsi.api.tongtu;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.gsp.center.tpsi.api.tongtu.request.SyncStockFromTongTuCommand;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@RequestMapping("tpsi-center/tongtu")
public interface TongTuApi {

    @PostMapping("syncProduct")
    CommonResponse<Void> syncProductFromTongTu(@RequestParam("thirdPartySystemId")String thirdPartySystemId);

    @PostMapping("stocksQuery")
    CommonResponse<Integer> syncStockFromTongTu(@RequestBody SyncStockFromTongTuCommand req);

/*    @PostMapping("warehouseQuery")
    CommonResponse<String> syncWarehouseFromTongTu(@RequestParam("thirdPartySystemId") String thirdPartySystemId);*/
}
