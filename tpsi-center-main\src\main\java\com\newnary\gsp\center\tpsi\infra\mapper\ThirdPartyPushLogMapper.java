package com.newnary.gsp.center.tpsi.infra.mapper;

import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyPushLogInfo;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyPushLogPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ThirdPartyPushLogMapper {

    ThirdPartyPushLogMapper INSTANCE = Mappers.getMapper(ThirdPartyPushLogMapper.class);

    ThirdPartyPushLogPO info2Po(ThirdPartyPushLogInfo info);

    ThirdPartyPushLogInfo po2Info(ThirdPartyPushLogPO po);
}
