package com.newnary.gsp.center.tpsi.infra.rpc;

import com.newnary.gsp.center.logistics.api.warehouse.response.WarehouseLiteRes;
import com.newnary.gsp.center.stock.api.logistics.enums.LogisticsStockBizAction;
import com.newnary.gsp.center.stock.api.logistics.feign.SupplierStockFeignApi;
import com.newnary.gsp.center.stock.api.logistics.request.SupplierUpdateDeliveryStockCommand;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class SupplierStockApiRpc {

    @Resource
    private SupplierStockFeignApi supplierStockFeignApi;

    public void syncStock(WarehouseLiteRes warehouseByThirdInfo, String supplierSkuId, Integer usableCount) {


        SupplierUpdateDeliveryStockCommand command = new SupplierUpdateDeliveryStockCommand();
        command.setSupplierId(warehouseByThirdInfo.getBizContext());
        command.setCount(usableCount);
        command.setSupplierSkuId(supplierSkuId);
        command.setBizSerialNumber(String.valueOf(System.currentTimeMillis()));
        command.setWarehouseId(warehouseByThirdInfo.warehouseId);
        command.setBizAction(LogisticsStockBizAction.SUPPLIER_UPDATE);
        supplierStockFeignApi.updateStock4SupplierDelivery(command).mustSuccessOrThrowOriginal();
    }

}

