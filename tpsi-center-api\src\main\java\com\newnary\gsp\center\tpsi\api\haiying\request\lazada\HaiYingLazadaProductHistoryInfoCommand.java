package com.newnary.gsp.center.tpsi.api.haiying.request.lazada;

import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingStation;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingLazadaProductHistoryInfoCommand {

    /**
     * 站点
     */
    @NotNull(message = "站点不能为空")
    private List<HaiYingStation> stations;

    /**
     * 商品id(string[]型)
     * (多个商品id用逗号分隔，最多100个商品id)
     */
    @NotNull(message = "商品id不能为空")
    private List<String> item_ids;

    /**
     * 商品抓取时间起始值(string[]型,格式:年-月-日 时:分:秒)
     * (默认查询近一个月的数据)
     */
    private List<String> insert_time_starts;

    /**
     * 商品抓取时间结束值(string[]型,格式:年-月-日 时:分:秒)
     * (默认查询近一个月的数据)
     */
    private List<String> insert_time_ends;

}
