package com.newnary.gsp.center.tpsi.infra.mq.consumer;

import com.newnary.messagebody.gsp.logistics.GSPTransportOrderThirdPartyTopic;
import com.newnary.mq.starter.consumer.AbstractMQConsumer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class TransportOrderThirdPartyConsumer extends AbstractMQConsumer {

    @Value("${gsp.transport-order-thirdParty.mq.consumer-id}")
    private String group;

    @Override
    public String topic() {
        return GSPTransportOrderThirdPartyTopic.TOPIC;
    }

    @Override
    public String consumerGroup() {
        return group;
    }
}
