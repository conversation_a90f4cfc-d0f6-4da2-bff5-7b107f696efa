package com.newnary.gsp.center.tpsi.infra.mq.consumer;

import com.newnary.messagebody.gsp.purchase.GSPPurchaseGoodsTopic;
import com.newnary.mq.starter.consumer.AbstractMQConsumer;
import org.springframework.stereotype.Component;

@Component
public class GSPPurchaseGoodsMQConsumer extends AbstractMQConsumer {

    @Override
    public String topic() {
        return GSPPurchaseGoodsTopic.TOPIC;
    }

    @Override
    public String consumerGroup() {
        return GSPPurchaseGoodsTopic.TOPIC;
    }

}
