package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp;

import java.util.List;

public class EcCangERPSyncOrderRequest {

    public String actionType;

    public Order order;

    public List<OrderDetail> orderDetails;

    public OrderAddress orderAddress;

    public OrderProperty orderProperty;

    /**
     * 1：直接审核，0：不审核
     */
    public String orderVerify;

    /**
     * 1：唯一，0：不唯一 （当 唯一 时 refNo 参考号 将是 易仓订单系统的订单号）
     */
    public String refNoIsUnique;

    public String saleOrderCode;

    public String isUpdateOrderStatus;

    public static class Order {

        public String platform;
        /**
         * 订单类型，sale：正常销售订单，resend：重发订单，line：线下订单
         */
        public String orderType;
        public String refNo;
        public String userAccount;
        public String currency;
        public String buyerId;
        public String buyerName;
        public String warehouseId;
        public String shippingMethod;
        public String shippingMethodPlatform;
        /**
         * 平台创建时间
         */
        public String dateCreatePlatform;
        /**
         * 付款时间 可更新
         */
        public String datePaidPlatform;

        public Double shipFee;
        public String buyerMail;
        public String site;
        public String orderDesc;
        public String transactionId;
        public String orderStatus;
        public String COD;
        public String discount;
        public String warehouseCode;
        public Integer DiscountDeductionMethod;
        public String customOrderTypeName;
        public String paymentMethod;
    }

    public static class OrderDetail {
        public String productSku;
        public Double unitPrice;
        public Integer qty;
        public String productTitle;
        public String productUrl;
        public String refItemId;
        public String opRefTnx;

        public Double unitFinalValueFee;
        public Double transactionPrice;
        public String orderRemark;
        public String saleOrderCodeOrg;
        public String refItemLocation;
        public Integer action;
    }

    public static class OrderAddress {
        public String name;
        public String countryCode;
        public String cityName;
        public String postalCode;
        public String line1;
        public String line2;
        public String line3;
        public String district;
        public String state;
        public String doorplate;
        public String phone;
        public String company;
    }

    public static class OrderProperty {
        public String customerServiceNote;
        public String taxNumber;
        public String ioss;
    }
}


