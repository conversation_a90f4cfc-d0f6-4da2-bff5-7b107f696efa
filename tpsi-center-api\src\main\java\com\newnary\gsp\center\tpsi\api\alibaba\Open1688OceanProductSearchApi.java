package com.newnary.gsp.center.tpsi.api.alibaba;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.gsp.center.tpsi.api.externalproduct.request.ExternalProductImageSearchCommand;
import com.newnary.gsp.center.tpsi.api.externalproduct.response.ExternalProductSearchInfo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 1688开放平台跨境商品搜索对接
 *
 * <AUTHOR>
 * @since Created on 2022-05-11
 **/
@RequestMapping("tpsi-center/alibaba/open1688")
public interface Open1688OceanProductSearchApi {

    @PostMapping("imageSearch")
    CommonResponse<ExternalProductSearchInfo> imageSearch(@RequestBody ExternalProductImageSearchCommand command);

}
