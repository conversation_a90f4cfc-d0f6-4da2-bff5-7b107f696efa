package com.newnary.gsp.center.tpsi.service.eccang;

import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderDetailInfo;
import com.newnary.gsp.center.logistics.api.delivery.response.TransportOrderPackageInfo;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.wms.*;
import com.newnary.gsp.center.tpsi.infra.model.ApiRequestParams;
import com.newnary.gsp.center.tpsi.infra.model.DeliveryOrderEcCangApiAssociation;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.DeliveryOrderItemEcCangApiAssociation;

public interface IEccangWMSApiSve {

    /**
     * 创建集运订单
     */
    EcCangApiBaseResult<String> freightCreateOrder(TransportOrderPackageInfo transportPackageInfo, DeliveryOrderDetailInfo deliveryOrderDetailInfo, ThirdPartySystem thirdPartySystem, ApiRequestParams apiRequestParams, EcCangFreightCreateOrderRequest freightCreateOrderRequest);

    /**
     * 创建集运订单
     */
    EcCangApiBaseResult<String> freightCreateAbroadOrder(DeliveryOrderDetailInfo deliveryOrderDetailInfo, ThirdPartySystem thirdPartySystem, ApiRequestParams apiRequestParams, EcCangFreightCreateOrderRequest freightCreateOrderRequest, String trackingNum);

    /**
     * 集运订单增加包裹数据
     */
    EcCangApiBaseResult<String> freightCreateProduct(DeliveryOrderItemEcCangApiAssociation item, ThirdPartySystem thirdPartySystem, String wmsOrderCode, Integer wmsWarehouseId);

    EcCangApiBaseResult<String> freightCreateProduct(DeliveryOrderEcCangApiAssociation association, ThirdPartySystem thirdPartySystem);

    /**
     * 获取集运订单
     */
    EcCangApiBaseResult<String> freightGetOrderInfo(String wmsOrderId, ThirdPartySystem thirdPartySystem);

    /**
     * 取消集运订单
     */
    EcCangApiBaseResult<String> freightCancelOrder(DeliveryOrderEcCangApiAssociation association, ThirdPartySystem thirdPartySystem);

    /**
     * 出库面单上传
     */
    EcCangApiBaseResult<String> freightCreateOrderLabel(String wmsOrderCode, String orderLabelCode, String labelType, String labelUrl, ThirdPartySystem thirdPartySystem);

    EcCangApiBaseResult<String> getClaimOrders(ThirdPartySystem thirdPartySystem);

    /**
     * WMS获取系统仓库列表
     *
     * @param thirdPartySystem
     * @return
     */
    EcCangApiBaseResult<String> getWarehouse(ThirdPartySystem thirdPartySystem);


    /**
     * 创建产品
     */
    EcCangApiBaseResult<String> createProduct(EcCangCreateProductRequest request, ThirdPartySystem thirdPartySystem);

    /**
     * 修改产品
     */
    EcCangApiBaseResult<String> updateProduct(EcCangUpdateProductRequest request, ThirdPartySystem thirdPartySystem);

    /**
     * 获取产品库存
     */
    EcCangApiBaseResult<String> getProductStock(EcCangGetProductStockRequest req, ThirdPartySystem thirdPartySystem);

    /**
     * 新建订单
     */
    EcCangApiBaseResult<String> createOrder(EcCangCreateOrderRequest request, ThirdPartySystem thirdPartySystem);

    /**
     * 取消订单
     */
    EcCangApiBaseResult<String> cancelOrder(EcCangCancelOrderRequest request, ThirdPartySystem thirdPartySystem);

    /**
     * 获取订单状态
     */
    EcCangApiBaseResult<String> getOrderState(EcCangGetOrderStateRequest request, ThirdPartySystem thirdPartySystem);

    /**
     * 获取订单轨迹信息
     */
    EcCangApiBaseResult<String> getOrderTracking(String orderNumber, ThirdPartySystem thirdPartySystem);

    /**
     * 获取订单列表
     */
    EcCangApiBaseResult<String> getOrderList(EcCangGetOrderListRequest request, ThirdPartySystem thirdPartySystem);

    /**
     * 获取运输方式
     */
    EcCangApiBaseResult<String> getShippingMethod(EcCangGetShippingMethodRequest request, ThirdPartySystem thirdPartySystem);

    /**
     * 创建退件
     */
    EcCangApiBaseResult<String> createReturnBill(EcCangCreateReturnBillRequest request, ThirdPartySystem thirdPartySystem);

    /**
     * 修改退件
     */
    EcCangApiBaseResult<String> updateReturnBill(EcCangUpdateReturnBillRequest request, ThirdPartySystem thirdPartySystem);

    /**
     * 获取退件信息
     */
    EcCangApiBaseResult<String> getReturnBill(EcCangGetReturnBillRequest request, ThirdPartySystem thirdPartySystem);

    /**
     * 上传附件
     */
    EcCangApiBaseResult<String> uploadFile(EcCangUploadFileRequest request, ThirdPartySystem thirdPartySystem);

    /**
     * 更新附件
     */
    EcCangApiBaseResult<String> saveOrderAttach(EcCangSaveOrderAttachRequest request, ThirdPartySystem thirdPartySystem);

    /**
     * 更新附件
     */
    EcCangApiBaseResult<String> modifyOrder(EcCangModifyOrderRequest request, ThirdPartySystem thirdPartySystem);

    /**
     * 发货请求
     */
    EcCangApiBaseResult<String> dealOrderSendRequest(EcCangFreightDealOrderSendRequest request, ThirdPartySystem thirdPartySystem);
}
