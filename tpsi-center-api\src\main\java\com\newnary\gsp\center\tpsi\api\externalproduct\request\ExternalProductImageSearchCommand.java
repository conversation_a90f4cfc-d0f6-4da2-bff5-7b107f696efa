package com.newnary.gsp.center.tpsi.api.externalproduct.request;

import com.newnary.api.base.common.PageCondition;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 公用的外部以图搜货请求格式
 *
 * <AUTHOR>
 * @since Created on 2022-05-12
 **/
@Data
public class ExternalProductImageSearchCommand {

    /**
     * 搜索图片链接(必须)
     **/
    @NotBlank(message = "搜索图片链接(必须)")
    private String imageUrl;

    /**
     * 销售价范围开始(大于等于)
     **/
    private String salePriceRangeStart;

    /**
     * 销售价范围结束(小于等于)
     **/
    private String salePriceRangeEnd;

    /**
     * 排序字段(可能不支持, 具体看外部系统支持情况): 销售价 SALE_PRICE, 销量 SALE_AMOUNT
     **/
    private String sortField;

    /**
     * 排序方向: 升序(默认) ASC, 降序 DESC
     **/
    private String sortDirection;

    /**
     * 附加过滤条件- 外部商品类目ID(可能不支持, 具体看外部系统支持情况)
     **/
    private String externalCategoryId;

    /**
     * 分页信息(注: 每页大小字段可能无效)(可能不支持, 具体看外部系统支持情况)(必须)
     **/
    @Valid
    @NotNull(message = "分页信息(必须)")
    public PageCondition pageCondition;

}
