package com.newnary.gsp.center.tpsi.infra.client.ejingling.valobj.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class EJingLingCreateOrderResponse {
    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单商品列表
     */
    private List<sku> skuList;

    @Getter
    @Setter
    public static class sku{
        private Long goodsSkuId;
        private Integer skuNum;
    }
}
