package com.newnary.gsp.center.tpsi.infra.model.vo;

public enum ThirdPartyPushType {
        PURCHASE_ORDER;

    public static ThirdPartyPushType getEnumByName(String enumName) {
        ThirdPartyPushType[] values = ThirdPartyPushType.class.getEnumConstants();
        for (ThirdPartyPushType enumValue : values) {
            if (enumValue.name().equals(enumName)) {
                return enumValue;
            }
        }
        return null; // 如果找不到匹配的枚举名，返回null或者抛出异常
    }
}
