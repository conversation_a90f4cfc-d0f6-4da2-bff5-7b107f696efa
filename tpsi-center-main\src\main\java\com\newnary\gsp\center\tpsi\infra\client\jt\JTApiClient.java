package com.newnary.gsp.center.tpsi.infra.client.jt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.gsp.center.tpsi.api.common.util.MD5Util;
import com.newnary.gsp.center.tpsi.api.jt.enums.JTMsgType;
import com.newnary.gsp.center.tpsi.api.jt.response.*;
import com.newnary.gsp.center.tpsi.infra.client.jt.params.JTParam;
import com.newnary.gsp.center.tpsi.infra.rpc.SpaceFileRpc;
import com.newnary.spring.cloud.extend.SpringContext;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okio.Buffer;
import org.apache.commons.codec.binary.Base64;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: WangRUTao
 */
@Slf4j
public class JTApiClient {


    private static final SpaceFileRpc spaceFileRpc = SpringContext.context().getBean(SpaceFileRpc.class);

    /*创建订单*/
    private static String createOrderMethod = "/jts-phl-order-api/api/order/create";
    /*取消订单*/
    private static String cancelOrderMethod = "/jts-phl-order-api/api/order/cancel";
    /*轨迹获取*/
    private static String queryTrackMethod = "/jts-phl-order-api/api/track/trackForJson";
    /*订单查询*/
    private static String queryOrderMethod = "/jts-phl-order-api/api/order/queryOrder";


    private JTParam param;

    public JTApiClient(String jtJsonParam) {
        JTParam params = JSON.parseObject(jtJsonParam, JTParam.class);
        this.param = params;
    }


    /**
     * 极兔物流创建订单接口调用请求
     *
     * @param command 请求体
     * @return
     */
    public JTDataApiBaseResult<String> createOrder(String command) {
        log.info("[JT 创建订单command]{} " , command);

        Map<String, String> header = new HashMap<String, String>();
        RequestBody requestBody = buildImmobilization(command, JTMsgType.ORDERCREATE);
        header.put("Content-Type", "application/x-www-form-urlencoded");

        try {
            final Buffer buffer = new Buffer();
            requestBody.writeTo(buffer);
            log.info("[JT 创建订单]{} " , buffer.readUtf8());

            OkHttpClient okHttpClient = new OkHttpClient();
            Request request = new Request.Builder()
                    .addHeader("content-type", "application/x-www-form-urlencoded")
                    .url(param.getBaseUrl().concat(createOrderMethod))
                    .post(requestBody)
                    .build();
            String result = Objects.requireNonNull(okHttpClient.newCall(request).execute().body()).string();
            log.info("JTCreateResult" + result);

            JTDataApiBaseResult<String> baseResult = buildDataBaseResult(result);

            log.info("[JT 订单创建]请求结束, code={}, message={}, result={}, dataParas={}", baseResult.getCode(), baseResult.getMessage(), baseResult.getResult(), JSONObject.toJSONString(requestBody));
            return baseResult;
        } catch (Exception e) {
            e.printStackTrace();
            JTDataApiBaseResult<String> ret = new JTDataApiBaseResult<>();
            ret.setCode(0);
            ret.setMessage("异常:".concat(e.getMessage()));
        }
        return new JTDataApiBaseResult<>();
    }


    private Map<String, Object> getParam(String command) {
        return JSONObject.parseObject(command, Map.class);
    }

    /**
     * build 固定参数
     *
     * @param params
     * @return
     */
    private RequestBody buildImmobilization(String params, JTMsgType jtMsgType) {
        return new FormBody.Builder()
                .add("eccompanyid", param.getEccompanyid())
                .add("logistics_interface", params)
                .add("msg_type", jtMsgType.name())
                .add("data_digest", new String(Base64.encodeBase64((MD5Util.encryptMD5(params + param.getKey()).getBytes(StandardCharsets.UTF_8)))))
                .build();
    }


    private JTDataApiBaseResult<String> buildDataBaseResult(String resultStr) {
        JTDataApiBaseResult<String> apiBaseResult = new JTDataApiBaseResult<>();

        JTCreateResultResp jtCreateResultDTO = JSONObject.parseObject(resultStr, JTCreateResultResp.class);
        Optional<JTCreateResultRespItemDTO> first = jtCreateResultDTO.getResponseitems()
                .stream()
                .findFirst();

        if (first.isPresent() && "false".equals(first.get().getSuccess())) {
            throw new RuntimeException("系统异常{}" + first.get().getReason());
        }

        apiBaseResult.setCode(200);
        apiBaseResult.setResult(JSONObject.toJSONString(jtCreateResultDTO));

        return apiBaseResult;
    }


    public JTDataApiBaseResult<String> doCancel(String command) {

        RequestBody requestBody = buildImmobilization(command, JTMsgType.ORDERCANCEL);
        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/x-www-form-urlencoded");

        try {
            final Buffer buffer = new Buffer();
            requestBody.writeTo(buffer);
            log.info("[JT 订单取消]{} " , buffer.readUtf8());

            OkHttpClient okHttpClient = new OkHttpClient();
            Request request = new Request.Builder()
                    .addHeader("content-type", "application/x-www-form-urlencoded")
                    .url(param.getBaseUrl().concat(cancelOrderMethod))
                    .post(requestBody)
                    .build();
            String result = Objects.requireNonNull(okHttpClient.newCall(request).execute().body()).string();

            JTDataApiBaseResult<String> baseResult = buildDataBaseResult(result);

            log.info("[JT 订单取消]请求结束, code={}, message={}, result={}, dataParas={}", baseResult.getCode(), baseResult.getMessage(), baseResult.getResult(), JSONObject.toJSONString(requestBody));
            return baseResult;
        } catch (Exception e) {
            e.printStackTrace();
            JTDataApiBaseResult<String> ret = new JTDataApiBaseResult<>();
            ret.setCode(0);
            ret.setMessage("异常:".concat(e.getMessage()));
        }
        return new JTDataApiBaseResult<>();
    }


    /**
     * 面单绘制
     *
     * @param command
     * @return
     */
    public JTDataApiBaseResult<String> printSheet(String command) {
        RequestBody requestBody = buildImmobilization(command, JTMsgType.ORDERQUERY);
        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/x-www-form-urlencoded");

        try {
            final Buffer buffer = new Buffer();
            requestBody.writeTo(buffer);

            log.info("[JT 订单查询]{} " , buffer.readUtf8());

            OkHttpClient okHttpClient = new OkHttpClient();
            Request request = new Request.Builder()
                    .addHeader("content-type", "application/x-www-form-urlencoded")
                    .url(param.getBaseUrl().concat(queryOrderMethod))
                    .post(requestBody)
                    .build();
            String result = Objects.requireNonNull(okHttpClient.newCall(request).execute().body()).string();

            JTDataApiBaseResult<String> baseResult = buildQueryOrderBaseResult(result);

            log.info("[JT 订单查询]请求结束, code={}, message={}, result={}, dataParas={}", baseResult.getCode(), baseResult.getMessage(), baseResult.getResult(), JSONObject.toJSONString(requestBody));
            return baseResult;
        } catch (Exception e) {
            e.printStackTrace();
            JTDataApiBaseResult<String> ret = new JTDataApiBaseResult<>();
            ret.setCode(0);
            ret.setMessage("异常:".concat(e.getMessage()));
        }
        return new JTDataApiBaseResult<>();
    }

    private JTDataApiBaseResult<String> buildQueryOrderBaseResult(String resultStr) {
        JTDataApiBaseResult<String> apiBaseResult = new JTDataApiBaseResult<>();
        JTQueryOrderResultResp resultResp = JSONObject.parseObject(resultStr, JTQueryOrderResultResp.class);
        apiBaseResult.setCode(200);
        apiBaseResult.setResult(JSONObject.toJSONString(resultResp));
        return apiBaseResult;
    }

    /**
     * 轨迹获取
     *
     * @param command
     * @return
     */
    public JTDataApiBaseResult<String> queryTrack(String command) {
        RequestBody requestBody = buildImmobilization(command, JTMsgType.TRACKQUERY);
        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/x-www-form-urlencoded");

        try {
            final Buffer buffer = new Buffer();
            requestBody.writeTo(buffer);

            log.info("[JT 轨迹查询]{} " , buffer.readUtf8());

            OkHttpClient okHttpClient = new OkHttpClient();
            Request request = new Request.Builder()
                    .addHeader("content-type", "application/x-www-form-urlencoded")
                    .url(param.getBaseUrl().concat(queryTrackMethod))
                    .post(requestBody)
                    .build();
            String result = Objects.requireNonNull(okHttpClient.newCall(request).execute().body()).string();

            JTDataApiBaseResult<String> baseResult = buildTrackDataBaseResult(result);

            log.info("[JT 轨迹查询]请求结束, code={}, message={}, result={}, dataParas={}", baseResult.getCode(), baseResult.getMessage(), baseResult.getResult(), JSONObject.toJSONString(requestBody));
            return baseResult;
        } catch (Exception e) {
            e.printStackTrace();
            JTDataApiBaseResult<String> ret = new JTDataApiBaseResult<>();
            ret.setCode(0);
            ret.setMessage("异常:".concat(e.getMessage()));
        }
        return new JTDataApiBaseResult<>();
    }


    private JTDataApiBaseResult<String> buildTrackDataBaseResult(String resultStr) {
        JTDataApiBaseResult<String> apiBaseResult = new JTDataApiBaseResult<>();
        JTTrackResultResp trackResultResp = JSONObject.parseObject(resultStr, JTTrackResultResp.class);
        apiBaseResult.setCode(200);
        apiBaseResult.setResult(JSONObject.toJSONString(trackResultResp));
        return apiBaseResult;
    }


    public JTParam getParam() {
        return param;
    }
}
