<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.newnary.gsp.center.tpsi.infra.repository.db.dao.ApiRequestParamsDao">

<resultMap id="apiRequestParamsPOResult" type="com.newnary.gsp.center.tpsi.infra.repository.db.po.ApiRequestParamsPO">
    <result column="system_biz_id" property="systemBizId"/>
    <result column="params" property="params"/>
    <result column="api_request_params_type" property="apiRequestParamsType"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="id" property="id"/>
    <result column="gmt_create" property="gmtCreate"/>
    <result column="gmt_modified" property="gmtModified"/>
</resultMap>

<sql id="apiRequestParamsPO_columns">
    system_biz_id,
    params,
    api_request_params_type,
    tenant_id,
    id,
    gmt_create,
    gmt_modified
</sql>

<sql id="apiRequestParamsPO_sqlForInsert">
    system_biz_id,
    params,
    api_request_params_type,
    tenant_id,
    id,
    gmt_create,
    gmt_modified
</sql>

<sql id="apiRequestParamsPO_columnsForInsert">
    #{systemBizId},
    #{params},
    #{apiRequestParamsType},
    #{tenantId},
    #{id},
    REPLACE(unix_timestamp(NOW(3)),'.',''),
    REPLACE(unix_timestamp(NOW(3)),'.','')
</sql>

<sql id="apiRequestParamsPO_setterForUpdate">
    <set>
        gmt_modified = REPLACE(unix_timestamp(NOW(3)),'.',''),
        system_biz_id = #{systemBizId},
        params = #{params},
        api_request_params_type = #{apiRequestParamsType},
    </set>
</sql>

<sql id="apiRequestParamsPO_selector">
    select
    <include refid="apiRequestParamsPO_columns"/>
    from api_request_params
</sql>

<sql id="apiRequestParamsPO_query_segment">
    <trim prefix="WHERE" prefixOverrides="AND|OR">
        <if test="data.systemBizId != null">
            AND system_biz_id = #{data.systemBizId}
        </if>
        <if test="data.params != null">
            AND params = #{data.params}
        </if>
        <if test="data.apiRequestParamsType != null">
            AND api_request_params_type = #{data.apiRequestParamsType}
        </if>
        <if test="data.id != null">
            AND id = #{data.id}
        </if>
        <if test="data.gmtCreate != null">
            AND gmt_create = #{data.gmtCreate}
        </if>
        <if test="data.gmtModified != null">
            AND gmt_modified = #{data.gmtModified}
        </if>
        <if test="ins != null and ins.size() > 0">
            AND
            <foreach collection="ins" item="item" open="(" separator="and" close=")">
                ${item.column} in
                <foreach collection="item.values" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </foreach>
        </if>
        <if test="notIns != null and notIns.size() > 0">
            AND
            <foreach collection="notIns" item="item" open="(" separator="and" close=")">
                ${item.column} not in
                <foreach collection="item.values" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </foreach>
        </if>
        <if test="equals != null and equals.size() > 0">
            AND
            <foreach collection="equals" item="item" open="(" separator="and" close=")">
                ${item.column} = #{item.value}
            </foreach>
        </if>
        <if test="notEquals != null and notEquals.size() > 0">
            AND
            <foreach collection="notEquals" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ <> ]]> #{item.value}
            </foreach>
        </if>
        <if test="lts != null and lts.size() > 0">
            AND
            <foreach collection="lts" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ < ]]> #{item.value}
            </foreach>
        </if>
        <if test="gts != null and gts.size() > 0">
            AND
            <foreach collection="gts" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ > ]]> #{item.value}
            </foreach>
        </if>
        <if test="likes != null and likes.size() > 0">
            AND
            <foreach collection="likes" item="item" open="(" separator="and" close=")">
                ${item.column} like concat('%', #{item.value}, '%')
            </foreach>
        </if>
        <if test="ltes != null and ltes.size() > 0">
            AND
            <foreach collection="ltes" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ <= ]]> #{item.value}
            </foreach>
        </if>
        <if test="gtes != null and gtes.size() > 0">
            AND
            <foreach collection="gtes" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ >= ]]> #{item.value}
            </foreach>
        </if>
        <if test="ranges != null and ranges.size() > 0">
            AND
            <foreach collection="ranges" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ >= ]]> #{item.from} and ${item.column} <![CDATA[ < ]]> #{item.to}
            </foreach>
        </if>
        AND tenant_id = #{tenantId}
    </trim>
</sql>


<insert id="insert" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.ApiRequestParamsPO">
    insert into api_request_params
    (
        <include refid="apiRequestParamsPO_sqlForInsert"/>
    )
    values
    (
        <include refid="apiRequestParamsPO_columnsForInsert"/>
    )
</insert>

<update id="update" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.ApiRequestParamsPO">
    update api_request_params
    <include refid="apiRequestParamsPO_setterForUpdate" />
    where id = #{id}
    and tenant_id = #{tenantId}
</update>


<update id="updateByQuery" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.ApiRequestParamsPO">
    update api_request_params
    <set>
        gmt_modified = REPLACE(unix_timestamp(NOW(3)),'.',''),
        system_biz_id = #{update.systemBizId},
        params = #{update.params},
        api_request_params_type = #{update.apiRequestParamsType},
    </set>
    <include refid="apiRequestParamsPO_query_segment"/>
</update>

<!-- Delete -->
<delete id="delete">
    delete from api_request_params
    <include refid="apiRequestParamsPO_query_segment"/>
</delete>
<delete id="deleteById">
    delete from api_request_params
    where id = #{id}
    and tenant_id = #{tenantId}
</delete>





<!-- query class -->
<sql id="apiRequestParamsPO_groupBy">
    <if test="groupBys != null and groupBys.size() > 0">
        group by
        <foreach collection="groupBys" item="item" open="" separator="," close="">
            #{item}
        </foreach>
    </if>
</sql>

<sql id="apiRequestParamsPO_orderby">
    <if test="orderBys != null and orderBys.size() > 0">
        order by
        <foreach collection="orderBys" item="item" open="" separator="," close="">
            ${item.column} ${item.direction}
        </foreach>
    </if>
</sql>


<select id="query" resultMap="apiRequestParamsPOResult" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.ApiRequestParamsPO">
    <include refid="apiRequestParamsPO_selector"/>
    <include refid="apiRequestParamsPO_query_segment"/>
    <include refid="apiRequestParamsPO_groupBy"/>
    <include refid="apiRequestParamsPO_orderby"/>
</select>



<select id="count" resultType="java.lang.Long">
    SELECT COUNT(1) FROM api_request_params
    <include refid="apiRequestParamsPO_query_segment"/>
</select>

<select id="getById" resultMap="apiRequestParamsPOResult">
    <include refid="apiRequestParamsPO_selector"/>
    where id = #{id}
    and tenant_id = #{tenantId}
</select>

<select id="getByIdForUpdate" resultMap="apiRequestParamsPOResult">
    <include refid="apiRequestParamsPO_selector"/>
    where id = #{id}
    and tenant_id = #{tenantId}
    for update
</select>

</mapper>
