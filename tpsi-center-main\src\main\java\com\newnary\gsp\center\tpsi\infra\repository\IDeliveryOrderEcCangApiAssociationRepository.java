package com.newnary.gsp.center.tpsi.infra.repository;

import com.newnary.gsp.center.tpsi.infra.model.DeliveryOrderEcCangApiAssociation;

import java.util.Optional;

public interface IDeliveryOrderEcCangApiAssociationRepository {

    Optional<DeliveryOrderEcCangApiAssociation> loadByDeliveryOrderId(String deliveryOrderId);

    Optional<DeliveryOrderEcCangApiAssociation> loadByStockoutOrderId(String stockoutOrderId);

    Optional<DeliveryOrderEcCangApiAssociation> loadByTransportOrderId(String transportOrderId);

    Optional<DeliveryOrderEcCangApiAssociation> getByAssociationId(String associationId);

    void store(DeliveryOrderEcCangApiAssociation association);

}
