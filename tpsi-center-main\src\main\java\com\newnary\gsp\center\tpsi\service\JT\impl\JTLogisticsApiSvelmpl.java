package com.newnary.gsp.center.tpsi.service.JT.impl;

import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.logistics.api.LogisticsCenterErrorInfo;
import com.newnary.gsp.center.logistics.api.TransportOrderErrorInfo;
import com.newnary.gsp.center.logistics.api.delivery.dto.TransportConsigneeDTO;
import com.newnary.gsp.center.logistics.api.delivery.dto.TransportItemDTO;
import com.newnary.gsp.center.logistics.api.delivery.response.TransportOrderPackageInfo;
import com.newnary.gsp.center.logistics.api.warehouse.response.ReceiptWarehouseRes;
import com.newnary.gsp.center.product.api.category.response.CategoryInfo;
import com.newnary.gsp.center.product.api.product.response.SupplierSkuDetailInfo;
import com.newnary.gsp.center.tpsi.api.jt.enums.*;
import com.newnary.gsp.center.tpsi.api.jt.request.CancelOrderJTCommand;
import com.newnary.gsp.center.tpsi.api.jt.request.CreateOrderJTCommand;
import com.newnary.gsp.center.tpsi.api.jt.request.SheetOrderJTCommand;
import com.newnary.gsp.center.tpsi.api.jt.request.TrackOrderJTCommand;
import com.newnary.gsp.center.tpsi.api.jt.response.*;
import com.newnary.gsp.center.tpsi.api.jt.vo.CreateJTOrderItems;
import com.newnary.gsp.center.tpsi.api.jt.vo.CreateOrderJTReceiver;
import com.newnary.gsp.center.tpsi.api.jt.vo.CreateOrderJTSender;
import com.newnary.gsp.center.tpsi.api.logisticsService.request.LogisticsCancelOrderCommand;
import com.newnary.gsp.center.tpsi.api.logisticsService.request.LogisticsPrintOrderCommand;
import com.newnary.gsp.center.tpsi.api.logisticsService.request.LogisticsTrackOrderCommand;
import com.newnary.gsp.center.tpsi.api.logisticsService.response.LogisticsOrderResp;
import com.newnary.gsp.center.tpsi.api.logisticsService.response.LogisticsPrintSheetResp;
import com.newnary.gsp.center.tpsi.infra.client.common.CreatePdfUtil;
import com.newnary.gsp.center.tpsi.infra.client.jt.JTApiClient;
import com.newnary.gsp.center.tpsi.infra.client.jt.utils.GenerateJTLabelPdfUtil;
import com.newnary.gsp.center.tpsi.infra.client.jt.valobj.request.GenerateJTPdfParam;
import com.newnary.gsp.center.tpsi.infra.model.ApiDockingResult;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartyAddressMapping;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.creator.ApiDockingResultCreator;
import com.newnary.gsp.center.tpsi.infra.model.vo.ApiDockingResultType;
import com.newnary.gsp.center.tpsi.infra.model.vo.LogisticsServiceConstants;
import com.newnary.gsp.center.tpsi.infra.model.vo.LogisticsServiceContext;
import com.newnary.gsp.center.tpsi.infra.repository.IApiDockingResultRepository;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartyAddressMappingRepository;
import com.newnary.gsp.center.tpsi.infra.rpc.ExchangeRateRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.ProductRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.SpaceFileRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.TransportOrderRpc;
import com.newnary.gsp.center.tpsi.service.ILogisticsApiSve;
import com.newnary.gsp.center.tpsi.service.impl.SystemClientSve;
import com.newnary.gsp.center.trade.api.order.response.OrderDTO;
import com.newnary.spring.cloud.extend.Extend;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.math.BigDecimal;
import java.net.URI;
import java.net.URISyntaxException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Extend("JT")
@Component
@Slf4j
public class JTLogisticsApiSvelmpl extends SystemClientSve implements ILogisticsApiSve {

    private static final String thirdPartySystemId = "TEST_JT";

    private static final String JTVersion = "1.0.0";

    private static final Integer pageLimit = 100000;

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    //保价金额阀值
    private static final BigDecimal insuredThresholdValue = new BigDecimal(2500);

    //派送时间阀值
    private static final Integer thresholdDateValue = 16;

    private static final BigDecimal zero = BigDecimal.ZERO;

    //投保比例
    private static final BigDecimal insuredThresholdRatio = new BigDecimal("0.01");

    private static ThirdPartySystem thirdPartySystem;

    @Resource
    private IApiDockingResultRepository apiDockingResultRepository;
    @Resource
    private SpaceFileRpc spaceFileRpc;
    @Resource
    private ExchangeRateRpc exchangeRateRpc;
    @Resource
    private IThirdPartyAddressMappingRepository thirdPartyAddressMappingRepository;
    @Resource
    private ProductRpc productRpc;
    @Resource
    private TransportOrderRpc transportOrderRpc;

    private JTApiClient client;

    private void init() {
         thirdPartySystem = loadSystem(thirdPartySystemId);
        //获取apiClient
        client = getClient(thirdPartySystem.getParams());
    }

    private JTApiClient getClient(String jtParams) {
        return new JTApiClient(jtParams);
    }


    private void saveDockingResult(JTDataApiBaseResult<String> result, String valueKey, String valueType) {
        ApiDockingResultCreator apiDockingResultCreator = new ApiDockingResultCreator();
        apiDockingResultCreator.setValueKey(valueKey);
        apiDockingResultCreator.setValueType(valueType);
        apiDockingResultCreator.setValueJson(result.getResult());
        apiDockingResultCreator.setDataStatus("saved");
        ApiDockingResult apiDockingResult = ApiDockingResult.createWith(apiDockingResultCreator);
        apiDockingResultRepository.store(apiDockingResult);
    }

    @Override
    public void createOrder() {
        init();
        CreateOrderJTCommand command = buildCreateCommand();
        if (ObjectUtils.isEmpty(command)) {
            throw new ServiceException(LogisticsCenterErrorInfo.ERROR_5010_LOGISTICS_SERVICE_API_ERROR);
        }
        JTDataApiBaseResult<String> baseResult = client.createOrder(JSONObject.toJSONString(command));
        String result = baseResult.getResult();

        saveDockingResult(baseResult, command.getTxlogisticid(), ApiDockingResultType.JTCreate.name());
        buildOrderResponse(JSONObject.parseObject(result, JTCreateResultResp.class), Float.valueOf(command.getOfferFee()), command);
    }

    private void buildOrderResponse(JTCreateResultResp jtCreateResultDTO, Float insuredValue, CreateOrderJTCommand command) {
        if (ObjectUtils.isEmpty(jtCreateResultDTO) || ObjectUtils.isEmpty(jtCreateResultDTO.getResponseitems())) {
            throw new ServiceException(LogisticsCenterErrorInfo.ERROR_5010_LOGISTICS_SERVICE_API_ERROR);
        }
        LogisticsOrderResp ret = new LogisticsOrderResp();
        JTCreateResultRespItemDTO jtCreateResultRespItemDTO = jtCreateResultDTO.getResponseitems()
                .stream()
                .findFirst()
                .get();

        ret.setTrackingId(jtCreateResultRespItemDTO.getMailno());
        ret.setRequestedTrackingId(jtCreateResultRespItemDTO.getTxlogisticid());
        if (ObjectUtils.isNotEmpty(command.getSendendtime())) {
            ret.setDeliveryStartDate(command.getSendstarttime());
        }
        ret.setInsuredValue(insuredValue.toString());
        LogisticsServiceContext.getCurrentContext().put(LogisticsServiceConstants.CALL_CREATE_RESPONSE, ret);
    }

    private CreateOrderJTCommand buildCreateCommand() {
        OrderDTO tradeOrderInfo = (OrderDTO) LogisticsServiceContext.getCurrentContext().get(LogisticsServiceConstants.LOGISTICS_TRADE_DOMAIN);
        TransportOrderPackageInfo transportOrderPackageInfo = (TransportOrderPackageInfo) LogisticsServiceContext.getCurrentContext().get(LogisticsServiceConstants.LOGISTICS_TRANSPORT_DOMAIN);
        ReceiptWarehouseRes warehouseInfo = (ReceiptWarehouseRes) LogisticsServiceContext.getCurrentContext().get(LogisticsServiceConstants.LOGISTICS_WAREHOUSE_DOMAIN);
        Map<String, CategoryInfo> categoryMap = (Map<String, CategoryInfo>) LogisticsServiceContext.getCurrentContext().get(LogisticsServiceConstants.LOGISTICS_CATEGORY_DOMAIN);
        if (ObjectUtils.isEmpty(tradeOrderInfo) || ObjectUtils.isEmpty(transportOrderPackageInfo)
                || ObjectUtils.isEmpty(warehouseInfo) || ObjectUtils.isEmpty(categoryMap)) {
            return null;
        }
        CreateOrderJTCommand ret = new CreateOrderJTCommand();
        //1 基础参数
        buildBasics(transportOrderPackageInfo, ret);
        //2 发件地址
        buildSender(ret, warehouseInfo);
        //3 收件地址
        buildReceiver(ret, transportOrderPackageInfo.getConsignee());
        //4 取货时间等
        buildSendTime(ret);
        //5 商品信息
        buildGoods(transportOrderPackageInfo, ret);
        // 是否保价
        if (transportOrderPackageInfo.getInsuredValue().compareTo(insuredThresholdValue) != 1) {
            ret.setIsInsured(zero.toString());
            ret.setOfferFee(zero.toString());
        } else {
            ret.setIsInsured("1");

            //保价金额 是商品供货价总值的0.01
            BigDecimal zero = BigDecimal.ZERO;
            for (CreateJTOrderItems item : ret.getItems()) {
                zero = zero.add(new BigDecimal(item.getItemvalue()).multiply(new BigDecimal(item.getNumber())));
            }
            String v = zero.multiply(insuredThresholdRatio).toString();
            ret.setOfferFee(String.valueOf(v));
        }
        //cod金额
        if (transportOrderPackageInfo.getIsCod()) {
            ret.setItemsvalue(transportOrderPackageInfo.getTransportAmount().toString());
        } else {
            ret.setItemsvalue(zero.toString());
        }

        return ret;
    }

    private void buildGoods(TransportOrderPackageInfo transportOrderPackageInfo, CreateOrderJTCommand ret) {
        int totalQuantity = transportOrderPackageInfo.getItems()
                .stream()
                .mapToInt(TransportItemDTO::getQuantity)
                .sum();
        ret.setTotalquantity(String.valueOf(totalQuantity));
        List<CreateJTOrderItems> transportOrderItemVOS = new ArrayList<>();
        for (TransportItemDTO orderItem : transportOrderPackageInfo.getItems()) {
            CreateJTOrderItems items = new CreateJTOrderItems();
            items.setItemname(orderItem.getCategoryName());
            items.setNumber(orderItem.getQuantity().toString());
            BigDecimal itemValue = orderItem.getSupplyPrice()
                    .multiply(new BigDecimal(orderItem.getQuantity()));
            //币种兑换
            BigDecimal phpAmount = exchangeRateRpc.currencyConvert(itemValue, orderItem.getSupplyPriceCurrency(), "PHP");
            items.setItemvalue(phpAmount.toString());
            transportOrderItemVOS.add(items);
        }
        ret.setItems(transportOrderItemVOS);
    }

    private void buildSendTime(CreateOrderJTCommand ret) {
        ret.setCreateordertime(dateFormat.format(System.currentTimeMillis()));

        LocalDateTime currentDateTime = LocalDateTime.now();
        int currentHour = currentDateTime.getHour();
        if (currentHour >= thresholdDateValue) {
            LocalDateTime nextDay16PM = currentDateTime.toLocalDate().plusDays(1).atTime(16, 0);
            LocalDateTime nextDay18PM = currentDateTime.toLocalDate().plusDays(1).atTime(18, 0);
            ret.setSendstarttime(dateFormat.format(Timestamp.valueOf(nextDay16PM).getTime()));
            ret.setSendendtime(dateFormat.format(Timestamp.valueOf(nextDay18PM).getTime()));
        } else {
            LocalDateTime day16PM = currentDateTime.toLocalDate().atTime(16, 0);
            LocalDateTime day18PM = currentDateTime.toLocalDate().atTime(18, 0);
            ret.setSendstarttime(dateFormat.format(Timestamp.valueOf(day16PM).getTime()));
            ret.setSendendtime(dateFormat.format(Timestamp.valueOf(day18PM).getTime()));
        }
    }

    private void buildBasics(TransportOrderPackageInfo transportOrderInfo, CreateOrderJTCommand ret) {
        ret.setMsg_type(JTMsgType.ORDERCREATE.name());
        ret.setActiontype(JTActionType.add.name());
        ret.setEnvironment(client.getParam().getEnvironment());
        ret.setEccompanyid(client.getParam().getEccompanyid());
        ret.setCustomerid(client.getParam().getCustomerid());
        // TODO 此处为临时方案解决地址映射不全的重新推送
        if (StringUtils.isNotBlank(transportOrderInfo.getPushOrderId())) {
            String pushOrderId = transportOrderInfo.getPushOrderId();
            String[] split = pushOrderId.split("-");
            int i = Integer.parseInt(split[1]);
            i++;
            ret.setTxlogisticid(transportOrderInfo.getTransportOrderPackageId()
                    .concat("-")
                    .concat(String.valueOf(i)));
        } else {
            ret.setTxlogisticid(transportOrderInfo.getTransportOrderPackageId()
                    .concat("-")
                    .concat(String.valueOf("1")));
        }
        ret.setPaytype("1");
        BigDecimal weight = transportOrderInfo.getWeight().compareTo(transportOrderInfo.getVolumeWeight()) > 0 ? transportOrderInfo.getWeight() : transportOrderInfo.getVolumeWeight();
        ret.setWeight(weight.toString());
//        if (CollectionUtils.isNotEmpty(transportOrderInfo.getTrackInfoVOs())) {
//            List<TrackInfoVO> collect = transportOrderInfo.getTrackInfoVOs()
//                    .stream()
//                    .filter(p -> "JT".equals(p.getCarrierCode()))
//                    .collect(Collectors.toList());
//            if (CollectionUtils.isNotEmpty(collect)) {
//                ret.setActiontype(JTActionType.update.name());
//                ret.setMailno(collect.get(0).getTrackingNo());
//            }
//        }

        ret.setOrdertype(JTOrderType.NORMAL_ORDER.getValue());
        ret.setServicetype(JTServiceType.HOME_PICKUP.getValue());
        ret.setDeliverytype(JTDeliveryType.NORMAL_DELIVERY.getValue());
    }

    private void buildSender(CreateOrderJTCommand command, ReceiptWarehouseRes receiptWarehouseRes) {
        CreateOrderJTSender sender = new CreateOrderJTSender();
        sender.setName(receiptWarehouseRes.getName());
        sender.setPostcode(receiptWarehouseRes.getCountryCode());
        sender.setMailbox(receiptWarehouseRes.getContactEmail());
        sender.setMobile(receiptWarehouseRes.getContactNumber());
        sender.setPhone(receiptWarehouseRes.getContactNumber());
        sender.setProv(receiptWarehouseRes.getStateProvince());
        sender.setCity(receiptWarehouseRes.getCity());
        sender.setArea(receiptWarehouseRes.getArea());
        sender.setAddress(receiptWarehouseRes.getAddrDetail());
        command.setSender(sender);
    }

    private void buildReceiver(CreateOrderJTCommand command, TransportConsigneeDTO consigneeVO) {
        CreateOrderJTReceiver receiver = new CreateOrderJTReceiver();
        receiver.setName(consigneeVO.getName());
        receiver.setMailbox(consigneeVO.getContactEmail());
        receiver.setMobile(consigneeVO.getContactNumber());
        receiver.setPhone(consigneeVO.getContactNumber());

        ThirdPartyAddressMapping addressMapping = thirdPartyAddressMappingRepository.loadByVirtualPostcode(thirdPartySystem.getSystemId(), consigneeVO.getVirtualPostcode())
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "映射地址不存在"));
        receiver.setPostcode(addressMapping.getPostCode());
        receiver.setProv(addressMapping.getProvince());
        receiver.setCity(addressMapping.getCity());
        receiver.setArea(addressMapping.getArea());
        receiver.setTown(consigneeVO.getAddrStreet());
        receiver.setAddress(consigneeVO.getAddrDetail());
        command.setReceiver(receiver);
    }

    @Override
    public void doCancel() {
        init();
        CancelOrderJTCommand command = buildCancelCommand();
        if (ObjectUtils.isEmpty(command)) {
            throw new ServiceException(LogisticsCenterErrorInfo.ERROR_5010_LOGISTICS_SERVICE_API_ERROR);
        }
        JTDataApiBaseResult<String> baseResult = client.doCancel(JSONObject.toJSONString(command));
        saveDockingResult(baseResult, command.getTxlogisticid(), ApiDockingResultType.JTCancel.name());
    }

    private CancelOrderJTCommand buildCancelCommand() {
        LogisticsCancelOrderCommand command = (LogisticsCancelOrderCommand) LogisticsServiceContext.getCurrentContext().get(LogisticsServiceConstants.LOGISTICS_CANCEL_DOMAIN);
        if (ObjectUtils.isEmpty(command)) {
            return null;
        }
        CancelOrderJTCommand ret = new CancelOrderJTCommand();
        ret.setEccompanyid(client.getParam().getEccompanyid());
        ret.setCustomerid(client.getParam().getCustomerid());
        ret.setTxlogisticid(command.getPushOrderId());
        ret.setCountry(command.getCountry());
        ret.setReason(command.getCancelReason());
        return ret;
    }


    @Override
    public void printSheet() {
        init();
        SheetOrderJTCommand command = buildPrintCommand();
        if (ObjectUtils.isEmpty(command)) {
            throw new ServiceException(LogisticsCenterErrorInfo.ERROR_5010_LOGISTICS_SERVICE_API_ERROR);
        }
        JTDataApiBaseResult<String> result = client.printSheet(JSONObject.toJSONString(command));
        JTQueryOrderResultResp queryOrderResultResp = JSONObject.parseObject(result.getResult(), JTQueryOrderResultResp.class);

        log.info("printSheetResult{}",JSONObject.toJSONString(result));

        if (CollectionUtils.isNotEmpty(queryOrderResultResp.getResponseitems())) {
            GenerateJTPdfParam param = new GenerateJTPdfParam();
            LogisticsPrintOrderCommand printOrderCommand = (LogisticsPrintOrderCommand) LogisticsServiceContext.getCurrentContext()
                    .get(LogisticsServiceConstants.LOGISTICS_PRINT_DOMAIN);
            buildGenerateSheetFileParams(queryOrderResultResp, param, printOrderCommand);

            try {
                buildPrintResponse(command, param);
            } catch (URISyntaxException e) {
                throw new ServiceException(LogisticsCenterErrorInfo.ERROR_5010_LOGISTICS_SERVICE_API_ERROR);
            }
            saveDockingResult(result, command.getSerialnumber(), ApiDockingResultType.JTPrintSheet.name());
        }
    }

    private void buildPrintResponse(SheetOrderJTCommand command, GenerateJTPdfParam param) throws URISyntaxException {
        URI uri = this.getClass().getProtectionDomain().getCodeSource().getLocation().toURI();
        String tempPath = ".";
        String sourceDir = "JT";  //资源文件夹
        String filePath = "";
        if (uri.toString().startsWith("file")) {
            // IDEA运行时，进行资源复制
            filePath = CreatePdfUtil.copyLocalResourcesFileToTemp(sourceDir + File.separator, "*", tempPath + File.separator + sourceDir);
        } else {
            // 获取jar包所在路径
            String jarPath = uri.toString();
            uri = URI.create(jarPath.substring(jarPath.indexOf("file:"),jarPath.indexOf(".jar") + 4));
            // 打成jar包后，进行资源复制
            filePath = CreatePdfUtil.copyJarResourcesFileToTemp(uri, tempPath, "BOOT-INF/classes/" + sourceDir);
        }
        //pdf地址响应
        ByteArrayOutputStream outputStream = GenerateJTLabelPdfUtil.generatePdf(param, filePath + File.separator);
        String sheetPdfFileUrl = this.generateSheetFile(command.getSerialnumber(), outputStream);
        LogisticsPrintSheetResp ret = new LogisticsPrintSheetResp();
        ret.setLabelBase64("");
        ret.setPdfFileUrl(sheetPdfFileUrl);
        log.info("printResp:{}", ret);
        LogisticsServiceContext.getCurrentContext().put(LogisticsServiceConstants.CALL_PRINT_RESPONSE, ret);
    }

    private void buildGenerateSheetFileParams(JTQueryOrderResultResp queryOrderResultResp, GenerateJTPdfParam param, LogisticsPrintOrderCommand printOrderCommand) {
        for (JTQueryOrderResultListDTO responseitem : queryOrderResultResp.getResponseitems()) {
            if ("true".equals(responseitem.getSuccess())) {
                for (JTQueryOrderResultItemDTO itemDTO : responseitem.getOrderList()) {
                    String[] split = itemDTO.getTxlogisticid().split("-");
                    TransportOrderPackageInfo packageInfo = transportOrderRpc.loadTransportPackage(split[0]);
                    //生成面单pdf
                    param.setWayBillNo(itemDTO.getMailno());
                    param.setOrderNo(itemDTO.getTxlogisticid());
                    param.setPickupNo(itemDTO.getSortingcode());
                    if (StringUtils.isNotBlank(itemDTO.getSortingNo())) {
                        param.setPickupNo(param.getPickupNo().concat("-").concat(itemDTO.getSortingNo()));
                    }
                    param.setIsCod(Double.parseDouble(itemDTO.getItemsvalue()) > 0);
                    param.setReceiverName(itemDTO.getReceiver().getName());
                    param.setReceiverAddress(buildReceiverAddress(itemDTO));
                    param.setReceiverAddressDetail(packageInfo.getConsignee().getAddrStreet());
                    param.setSenderName(itemDTO.getSender().getName());
                    param.setSenderAddress(buildSendAddress(itemDTO));
                    param.setCodPrice(itemDTO.getItemsvalue());
                    itemDTO.getItems().stream()
                            .findFirst()
                            .ifPresent(p -> {
                                if (CollectionUtils.isEmpty(packageInfo.getItems())) {
                                    throw new ServiceException(TransportOrderErrorInfo.ERROR_303_OPERATE_NOT_SUPPORT, "缺失运输单包裹明细");
                                }
                                TransportItemDTO transportItem = packageInfo.getItems().get(0);
                                String supplierSkuId = transportItem.getSupplierSkuId();
                                SupplierSkuDetailInfo supplierSku = productRpc.getSupplierSku(supplierSkuId);
                                param.setGoodsName(p.getItemname());
//                                BigDecimal multiply = transportItem.getSalePrice().multiply(new BigDecimal(transportItem.getQuantity()));
//                                //币种兑换
//                                BigDecimal phpAmount = exchangeRateRpc.currencyConvert(multiply, transportItem.getSalePriceCurrency(), "PHP");
//                                param.setPriceValue(phpAmount.toString());
                                if (ObjectUtils.isNotEmpty(supplierSku)) {
                                    param.setWeightValue(
                                            supplierSku.getSkuInfo().getGrossWeight() == null ?
                                                    "0" : supplierSku.getSkuInfo().getGrossWeight().multiply(new BigDecimal(transportItem.getQuantity())).toString());
                                }
                    });
                }
            }
        }
    }

    /**
     * base64 转成 pdf
     *
     * @param trackingId
     * @param byteArrayOutputStream
     * @return
     */
    private String generateSheetFile(String trackingId, ByteArrayOutputStream byteArrayOutputStream) {
        try {
            String fileId = spaceFileRpc.createSpaceFile(trackingId, byteArrayOutputStream.toByteArray(), ".pdf");
            Map<String, String> fileIdMap = spaceFileRpc.queryFileId4FileUrlMapByFileId(Collections.singletonList(fileId));
            String sheetPdfFileUrl = fileIdMap.get(fileId);
            return sheetPdfFileUrl;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    private String buildReceiverAddress(JTQueryOrderResultItemDTO itemDTO) {
        return itemDTO.getReceiver().getProv() + " "
                + itemDTO.getReceiver().getCity() + " "
                + itemDTO.getReceiver().getAddress();
    }

    private String buildSendAddress(JTQueryOrderResultItemDTO itemDTO) {
        return itemDTO.getSender().getProv() + " "
                + itemDTO.getSender().getCity() + " "
                + itemDTO.getSender().getArea() + " "
                + itemDTO.getSender().getAddress();
    }

    private SheetOrderJTCommand buildPrintCommand() {
        LogisticsPrintOrderCommand command = (LogisticsPrintOrderCommand) LogisticsServiceContext.getCurrentContext().get(LogisticsServiceConstants.LOGISTICS_PRINT_DOMAIN);
        if (ObjectUtils.isEmpty(command)) {
            return null;
        }
        SheetOrderJTCommand ret = new SheetOrderJTCommand();
        ret.setEccompanyid(client.getParam().getEccompanyid());
        ret.setCustomerid(client.getParam().getCustomerid());
        ret.setCommand(JTQueryOrderCommandType.BYBILLCODEQUERY.getValue());
        ret.setSerialnumber(command.getTrackingId());
        ret.setStartdate("");
        ret.setEnddate("");
        return ret;
    }

    @Override
    public void queryTrack() {
        init();
        TrackOrderJTCommand command = buildTrackCommand();
        if (ObjectUtils.isEmpty(command)) {
            throw new ServiceException(LogisticsCenterErrorInfo.ERROR_5010_LOGISTICS_SERVICE_API_ERROR);
        }
        JTDataApiBaseResult<String> baseResult = client.queryTrack(JSONObject.toJSONString(command));
//        saveDockingResult(baseResult, JSONObject.toJSONString(command.getBillcode()), ApiDockingResultType.JTTrack.name());
        buildTrackResponse(baseResult);
    }

    private void buildTrackResponse(JTDataApiBaseResult<String> baseResult) {
        JTTrackResultResp jtTrackResultResp = JSONObject.parseObject(baseResult.getResult(), JTTrackResultResp.class);
        LogisticsServiceContext.getCurrentContext().put(LogisticsServiceConstants.CALL_TRACK_DOMAIN, jtTrackResultResp);
    }

    private TrackOrderJTCommand buildTrackCommand() {
        LogisticsTrackOrderCommand command = (LogisticsTrackOrderCommand) LogisticsServiceContext.getCurrentContext().get(LogisticsServiceConstants.LOGISTICS_TRACK_DOMAIN);
        TrackOrderJTCommand ret = new TrackOrderJTCommand();
        ret.setBillcode(command.getTrackingId());
        ret.setLang(command.getLanguage());
        return ret;
    }


}
