package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp;

import java.util.List;

public class EcCangERPSyncPurchaseOrdersRequest {

    public  String action_type;
    public Integer warehouse_id;

    public String to_warehouse_id;

    public Integer po_type;
    public Integer suppiler_id;
    public String is_rebate_tax;
    /**
     * 采购员ID
     */
    public Integer operator_purchase;
    public Integer operation_type;
    public Integer account_type;
    public Integer account_proportion;
    public String payment_cycle;
    public String currency_code;

    public Integer pay_type;
    public Integer supplier_pay_type;
    public Integer shipping_method_id_head;
    public String alipay_account;
    public Integer bk_id;
    public Integer ba_id;
    public String alipay_supplier_account;
    public Integer supplier_bk_id;
    public Integer supplier_ba_id;

    public Integer pts_oprater;
    public String po_code;
    public Integer po_status;

    public String company;
    public Integer receiver;
    public String tracking_no;
    public String ref_no;

    public String date_eta;
    public Double pay_ship_amount;

    public Integer transaction_no;
    public String date_expected;
    public String date_create;
    public Integer po_is_net;

    public List<String> single_net_number;
    public Integer po_is_has_tax;
    public String organization_id;
    public String pro_code;

    public Integer po_platform_1688;

    public List<ProductList> productList;

    public static class ProductList {

        public String product_sku;
        public Integer qty_expected;
        public String currency_code;
        public Double unit_price;
        public Integer sp_reference_unit_price;
        public Integer is_qc;
        public Integer is_free;
        public String qty_free;
        public Double po_tax_rate;
        public String pop_external_number;
        public String note;
        public String reason;
    }


}
