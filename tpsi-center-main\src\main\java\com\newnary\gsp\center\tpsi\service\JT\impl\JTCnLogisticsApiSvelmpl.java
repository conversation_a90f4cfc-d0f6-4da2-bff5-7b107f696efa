package com.newnary.gsp.center.tpsi.service.JT.impl;

import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.tpsi.api.common.enums.ThirdPartEventType;
import com.newnary.gsp.center.tpsi.api.jt.request.cn.*;
import com.newnary.gsp.center.tpsi.api.jt.response.cn.*;
import com.newnary.gsp.center.tpsi.infra.client.jt.JTCnApiClient;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyPushLogInfo;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartyAddressMappingRepository;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.ThirdPartyPushLogManager;
import com.newnary.gsp.center.tpsi.infra.rpc.ExchangeRateRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.SpaceFileRpc;
import com.newnary.gsp.center.tpsi.service.impl.SystemClientSve;
import com.newnary.spring.cloud.anno.Validation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.text.SimpleDateFormat;
import java.util.List;

@Slf4j
@Component
public class JTCnLogisticsApiSvelmpl extends SystemClientSve {

    private static final String thirdPartySystemId = "JT_CN";

    private static final String JTCnVersion = "1.0.0";

    final Long currentTimeMillis = System.currentTimeMillis();

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private static ThirdPartySystem thirdPartySystem;

    @Resource
    private ThirdPartyPushLogManager thirdPartyPushLogManager;
    @Resource
    private SpaceFileRpc spaceFileRpc;
    @Resource
    private ExchangeRateRpc exchangeRateRpc;
    @Resource
    private IThirdPartyAddressMappingRepository thirdPartyAddressMappingRepository;

    private JTCnApiClient client;

    private void init() {
        thirdPartySystem = loadSystem(thirdPartySystemId);
        //获取apiClient
        client = getClient(thirdPartySystem.getParams());
    }

    private JTCnApiClient getClient(String jtParams) {
        return new JTCnApiClient(jtParams);
    }


    private void savePushLog(ThirdPartEventType eventType,
                             String eventBizId,
                             String eventData,
                             String reqData,
                             String sysRemark

    ) {
        ThirdPartyPushLogInfo pushLogInfo = new ThirdPartyPushLogInfo();
        pushLogInfo.setEventType(eventType.name());
        pushLogInfo.setEventBizId(eventBizId);
        pushLogInfo.setEventData(eventData);
        pushLogInfo.setReqData(reqData);
        pushLogInfo.setSysRemark(sysRemark);
        thirdPartyPushLogManager.insertOrUpdate(pushLogInfo);
    }

    private void savePushResponseLog(JTCnApiBaseResult<String> result,
                                     ThirdPartEventType eventType,
                                     String eventBizId,
                                     Integer responseState,
                                     String sysRemark
    ) {
        ThirdPartyPushLogInfo pushLogInfo = new ThirdPartyPushLogInfo();
        pushLogInfo.setEventType(eventType.name());
        pushLogInfo.setEventBizId(eventBizId);
        pushLogInfo.setEventState(responseState == 200 ? 1 : 0);
        pushLogInfo.setEventBizState(responseState);
        pushLogInfo.setRespData(result.getResult());
        pushLogInfo.setSysRemark(sysRemark);
        thirdPartyPushLogManager.insertOrUpdateV2(pushLogInfo);
    }

    @Validation
    public JTCnCreateResultResp createOrder(@NotNull CreateOrderCnJTCommand command) {
        this.init();

        this.savePushLog(ThirdPartEventType.TMS_TO_CN_JT_CREATE_ORDER, command.getCustomerOrderNo(), JSONObject.toJSONString(command), null, "准备创建&预报订单");
        final JTCnApiBaseResult<String> apiBaseResult = client.createOrder(JSONObject.toJSONString(command));

        this.savePushResponseLog(apiBaseResult, ThirdPartEventType.TMS_TO_CN_JT_CREATE_ORDER, command.getCustomerOrderNo(), apiBaseResult.getCode(), "完成创建&预报订单");
        checkSuccessful(apiBaseResult);
        return JSONObject.parseObject(apiBaseResult.getResult(), JTCnCreateResultResp.class);

    }

    private static void checkSuccessful(JTCnApiBaseResult<String> apiBaseResult) {
        if (apiBaseResult.getCode() != 0) {
            throw new ServiceException(CommonErrorInfo.ERROR_104_SERVICE_CALL_ERROR, apiBaseResult.getMessage());
        }
    }


    /**
     * todo 记得合并 PDF
     *
     * @param command
     */
    @Validation
    public JTCnPrintSheetResp printSheet(@NotNull SheetOrderCnJTCommand command) {
        this.init();

        this.savePushLog(ThirdPartEventType.TMS_TO_CN_JT_QUERY_LABEL, command.getNos().get(0), JSONObject.toJSONString(command), null, "准备打印面单");
        final JTCnApiBaseResult<String> apiBaseResult = client.printSheet(JSONObject.toJSONString(command));

        this.savePushResponseLog(apiBaseResult, ThirdPartEventType.TMS_TO_CN_JT_QUERY_LABEL, command.getNos().get(0), apiBaseResult.getCode(), "完成打印面单");
        checkSuccessful(apiBaseResult);
        return JSONObject.parseObject(apiBaseResult.getResult(), JTCnPrintSheetResp.class);

    }


    @Validation
    public List<JTCnLogisticsResp> queryLogistics() {
        this.init();

        final JTCnApiBaseResult<String> apiBaseResult = client.queryLogistics();
//        checkSuccessful(apiBaseResult);
        return JSONObject.parseArray(apiBaseResult.getResult(), JTCnLogisticsResp.class);
    }


    @Validation
    public List<JTCnTrackResultResp> queryTrack(TrackOrderCnJTCommand command) {
        this.init();


        this.savePushLog(ThirdPartEventType.TMS_TO_CN_JT_QUERY_TRACK,  currentTimeMillis+ "", JSONObject.toJSONString(command), null, "准备获取轨迹");
        final JTCnApiBaseResult<String> apiBaseResult = client.queryTrack(JSONObject.toJSONString(command));

        this.savePushResponseLog(apiBaseResult, ThirdPartEventType.TMS_TO_CN_JT_QUERY_TRACK, currentTimeMillis + "", apiBaseResult.getCode(), "完成获取轨迹");
//        checkSuccessful(apiBaseResult);
        return JSONObject.parseArray(apiBaseResult.getResult(), JTCnTrackResultResp.class);
    }


    @Validation
    public void cancelOrder(CancelOrderCnJTCommand command) {
        init();

        this.savePushLog(ThirdPartEventType.TMS_TO_CN_JT_CANCEL_ORDER, command.getCustomerOrderNo(), JSONObject.toJSONString(command), null, "准备取消订单");
        final JTCnApiBaseResult<String> apiBaseResult = client.cancelOrder(JSONObject.toJSONString(command));

        this.savePushResponseLog(apiBaseResult, ThirdPartEventType.TMS_TO_CN_JT_CANCEL_ORDER, command.getCustomerOrderNo(), apiBaseResult.getCode(), "完成取消订单");
        checkSuccessful(apiBaseResult);
    }


    @Validation
    public JTCnQueryOrderBillResp queryBill(BillOrderCnJTCommand command) {
        init();

        this.savePushLog(ThirdPartEventType.TMS_TO_CN_JT_CANCEL_ORDER, command.getCustomerOrderNo(), JSONObject.toJSONString(command), null, "准备查询订单账单");
        final JTCnApiBaseResult<String> apiBaseResult = client.queryBill(JSONObject.toJSONString(command));

        this.savePushResponseLog(apiBaseResult, ThirdPartEventType.TMS_TO_CN_JT_CANCEL_ORDER, command.getCustomerOrderNo(), apiBaseResult.getCode(), "准备查询订单账单");
//        checkSuccessful(apiBaseResult);
        return JSONObject.parseObject(apiBaseResult.getResult(), JTCnQueryOrderBillResp.class);
    }

}
