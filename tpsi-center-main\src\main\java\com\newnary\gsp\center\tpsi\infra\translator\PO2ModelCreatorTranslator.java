package com.newnary.gsp.center.tpsi.infra.translator;

import com.newnary.gsp.center.tpsi.infra.mapper.ApiDockingResultMapper;
import com.newnary.gsp.center.tpsi.infra.mapper.ApiRequestParamsMapper;
import com.newnary.gsp.center.tpsi.infra.mapper.ThirdPartyAddressMappingMapper;
import com.newnary.gsp.center.tpsi.infra.mapper.ThirdPartySystemMapper;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartyAddressMapping;
import com.newnary.gsp.center.tpsi.infra.model.creator.ApiDockingResultCreator;
import com.newnary.gsp.center.tpsi.infra.model.creator.ApiRequestParamsCreator;
import com.newnary.gsp.center.tpsi.infra.model.creator.ThirdPartyAddressMappingCreator;
import com.newnary.gsp.center.tpsi.infra.model.creator.ThirdPartySystemCreator;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.ApiDockingResultPO;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.ApiRequestParamsPO;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyAddressMappingPO;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartySystemPO;

/**
 * <AUTHOR>
 * @Date 2021/12/14 14:52
 */
public class PO2ModelCreatorTranslator {

    public static ThirdPartySystemCreator transThirdPartySystemCreator(ThirdPartySystemPO po) {
        return ThirdPartySystemMapper.INSTANCE.po2ModelCreator(po);
    }

    public static ApiDockingResultCreator transApiDockingResultCreator(ApiDockingResultPO po) {
        return ApiDockingResultMapper.INSTANCE.po2ModelCreator(po);
    }

    public static ApiRequestParamsCreator transApiRequestParamsCreator(ApiRequestParamsPO po) {
        return ApiRequestParamsMapper.INSTANCE.po2ModelCreator(po);
    }

    public static ThirdPartyAddressMappingCreator transThirdPartyAddressMappingCreator(ThirdPartyAddressMappingPO po) {
        return ThirdPartyAddressMappingMapper.INSTANCE.po2ModelCreator(po);
    }
}
