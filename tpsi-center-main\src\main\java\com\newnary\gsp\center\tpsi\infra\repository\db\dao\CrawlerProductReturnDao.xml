<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.newnary.gsp.center.tpsi.infra.repository.db.dao.CrawlerProductReturnDao">

    <resultMap id="crawlerProductPOResult" type="com.newnary.gsp.center.tpsi.infra.repository.db.po.CrawlerProductPO">
        <result column="id" property="id"/>
        <result column="crawler_return" property="crawlerReturn"/>
        <result column="product_id" property="productId"/>
        <result column="state" property="state"/>
        <result column="begin_page" property="beginPage"/>
        <result column="flush_version" property="flushVersion"/>
        <result column="category_name" property="categoryName"/>
        <result column="page_size" property="pageSize"/>
        <result column="flush_state" property="flushState"/>
        <result column="crawler_product_id" property="crawlerProductId"/>
        <result column="crawler_return_state" property="crawlerReturnState"/>
        <result column="fail_reason" property="failReason"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <insert id="fillCrawlerProduct" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.CrawlerProductPO">
        insert into crawler_product_detail
            (product_id, crawler_return)
        values (#{productId}, #{crawlerReturn})
    </insert>

    <select id="loadCrawlerDetailReturn" resultMap="crawlerProductPOResult"
            resultType="com.newnary.gsp.center.tpsi.infra.repository.db.po.CrawlerProductPO">
    select
    id,product_id,crawler_return
    from crawler_product_detail
    where product_id = #{productId}
    </select>

    <update id="updateByProductId">
        update crawler_product
        <trim prefix="set" suffixOverrides=",">
            gmt_modified = REPLACE(unix_timestamp(NOW(3)),'.',''),

            <if test="crawlerReturnState != null">
                crawler_return_state = #{crawlerReturnState},
            </if>

            <if test="state != null">
                `state` = #{state},
            </if>

            <if test="flushState != null">
                `flush_state` = #{flushState},
            </if>

            <if test="flushVersion != null">
                `flush_version` = #{flushVersion},
            </if>


            <if test="failReason != null">
                fail_reason = #{failReason},
            </if>

        </trim>

        where product_id = #{productId}
    </update>

    <select id="queryByCategoryNameList" resultMap="crawlerProductPOResult"
            resultType="com.newnary.gsp.center.tpsi.infra.repository.db.po.CrawlerProductPO">

        select
        cp.id,
        cp.state,
        cp.flush_state,
        cp.flush_version,
        cp.crawler_return_state,
        cp.category_name,
        cp.product_id,
        cp.gmt_create,
        cp.gmt_modified
        from crawler_product cp
        where 1 = 1
        <if test="state != null">
            and `state` = #{state}
        </if>
        <if test="flushState != null">
            and `flush_state` = #{flushState}
        </if>
        <if test="categoryList != null and categoryList.size() > 0">
            AND category_name in
            <foreach collection="categoryList" item="categoryName" open="(" separator="," close=")">
                #{categoryName}
            </foreach>
        </if>

        <if test="crawlerReturnState != null">
            and crawler_return_state = #{crawlerReturnState}
        </if>
        order by gmt_modified
        limit 0,#{size}

    </select>
</mapper>
