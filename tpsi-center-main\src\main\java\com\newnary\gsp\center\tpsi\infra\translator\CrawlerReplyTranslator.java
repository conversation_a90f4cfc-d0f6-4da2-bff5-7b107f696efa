package com.newnary.gsp.center.tpsi.infra.translator;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.newnary.common.utils.locale.CountryCode;
import com.newnary.common.utils.locale.LanguageLocaleType;
import com.newnary.gsp.center.product.api.common.dto.MultimediaInfo;
import com.newnary.gsp.center.product.api.common.dto.MultimediaType;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierAdjustSupplyPriceReq;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierItemCreateCombineDetailInfo;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierUpdateStockReq;
import com.newnary.gsp.center.product.api.product.request.SupplierSkuCreateInfo;
import com.newnary.gsp.center.product.api.product.request.SupplierSkuMainSpecInfo;
import com.newnary.gsp.center.product.api.product.request.SupplierSkuSpecInfo;
import com.newnary.gsp.center.product.api.product.request.SupplierSpuCreateV2Command;
import com.newnary.gsp.center.product.api.product.vo.SupplierSpuDescInfo;
import com.newnary.gsp.center.product.api.product.vo.SupplierSpuParamInfo;
import com.newnary.gsp.center.product.api.product.vo.SupplierSpuParamValueInfo;
import com.newnary.gsp.center.product.api.product.vo.SupplierSpuParamsInfo;
import com.newnary.gsp.center.tpsi.infra.model.vo.ProductMappingInfo;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

public class CrawlerReplyTranslator {
    public static void fill1688ProductDetailsReply(SupplierSpuCreateV2Command createCommand, JSONObject replyData, String productId, ProductMappingInfo mappingInfo, String supplierId){

        createCommand.setSupplierId(supplierId);
        createCommand.setOperator("系统自动");
        createCommand.setIsAutoAuditPass(true);
        createCommand.setCustomCode(replyData.getJSONObject("offerDetail").getString("offerId"));
        createCommand.setDefaultLocale(LanguageLocaleType.zh_CN);

        createCommand.setDescInfos(new ArrayList<SupplierSpuDescInfo>(){
            {
                SupplierSpuDescInfo descInfo = new SupplierSpuDescInfo();
                descInfo.setLocale(LanguageLocaleType.zh_CN);
                descInfo.setTitle(replyData.getJSONObject("offerDetail").getString("subject"));
                descInfo.setCustomBrandName(null);
                descInfo.setCustomCategoryName(null);
                descInfo.setCustomMeasuringUnit(replyData.getJSONObject("tradeModel").getString("unit"));
                add(descInfo);
            }
        });

        createCommand.setCustomCategoryId(replyData.getJSONObject("offerDetail").getString("leafCategoryId"));

        SupplierSpuParamsInfo paramsInfo = new SupplierSpuParamsInfo();
        paramsInfo.setCustomParams(replyData.getJSONObject("offerDetail").getJSONArray("featureAttributes").stream().map(attribute -> {
            JSONObject attr = (JSONObject) attribute;
            SupplierSpuParamInfo paramInfo = new SupplierSpuParamInfo();
            paramInfo.setParamName(attr.getString("name"));
            paramInfo.setValues(new ArrayList<SupplierSpuParamValueInfo>(){
                {
                    Optional<JSONArray> values = Optional.ofNullable(attr.getJSONArray("values"));
                    values.ifPresent(val ->{
                        JSONArray vals = (JSONArray) val;
                        if (CollectionUtils.isNotEmpty(vals)){
                            vals.forEach(v->{
                                if (v!=null){
                                    SupplierSpuParamValueInfo paramValueInfo = new SupplierSpuParamValueInfo();
                                    paramValueInfo.setParamValue(v.toString());
                                    add(paramValueInfo);
                                }
                            });
                        }
                    });
                }
            });
            return paramInfo;
        }).collect(Collectors.toList()));
        createCommand.setParamsInfo(paramsInfo);

        createCommand.setCountryOfOriginCode(CountryCode.CN);

        //主图
        createCommand.setMainImages(replyData.getJSONObject("offerDetail").getJSONArray("imageList").stream().map(mainImage->{
            JSONObject main = (JSONObject) mainImage;
            MultimediaInfo multimediaInfo = new MultimediaInfo();
            multimediaInfo.setType(MultimediaType.IMAGE);
            multimediaInfo.setExternalUrl(main.getString("fullPathImageURI"));
            return  multimediaInfo;
        }).collect(Collectors.toList()));

        //详情页
        createCommand.setDetailImages(replyData.getJSONObject("offerDetail").getJSONArray("detailUrl").stream().map(detailUrl->{
            MultimediaInfo multimediaInfo = new MultimediaInfo();
            multimediaInfo.setType(MultimediaType.IMAGE);
            multimediaInfo.setExternalUrl(detailUrl.toString());
            return multimediaInfo;
        }).collect(Collectors.toList()));

        //加入vodio
        createCommand.setVideos(new ArrayList<MultimediaInfo>(){
            {
                Optional.ofNullable((replyData.getJSONObject("offerDetail").getJSONObject("wirelessVideo"))).ifPresent(vd->{
                    Optional.ofNullable(vd.getJSONObject("videoUrls")).ifPresent(v->{
                        {
                            if (!StringUtils.isEmpty(v.getString("android"))){
                                MultimediaInfo multimediaInfo = new MultimediaInfo();
                                multimediaInfo.setType(MultimediaType.VIDEO);
                                multimediaInfo.setExternalUrl(v.getString("android"));
                                add(multimediaInfo);
                            }
                        }
                        {
                            if (!StringUtils.isEmpty(v.getString("ios"))){
                                MultimediaInfo multimediaInfo = new MultimediaInfo();
                                multimediaInfo.setType(MultimediaType.VIDEO);
                                multimediaInfo.setExternalUrl(v.getString("ios"));
                                add(multimediaInfo);
                            }
                        }
                    });
                });
            }
        });

        Map<String,String> skuImageMap = new HashMap<>();
        Optional<JSONArray> jsonArray = Optional.ofNullable(replyData.getJSONObject("offerDetail").getJSONArray("skuProps"));
        jsonArray.ifPresent(s->{
            JSONArray skuPros = (JSONArray)s;
            skuPros.forEach(skuProp->{
                JSONObject skuPro = (JSONObject) skuProp;
                skuPro.getJSONArray("value").stream().forEach(prop->{
                    JSONObject pro = (JSONObject) prop;
                    Optional<String> imageUrl = Optional.ofNullable(pro.getString("imageUrl"));
                    if (imageUrl.isPresent()){
                        skuImageMap.put(pro.getString("name"),pro.getString("imageUrl"));
                    }
                });
            });
        });

        JSONArray skuArray = replyData.getJSONObject("tradeModel").getJSONArray("skuMap");
        //添加sku
        if (CollectionUtils.isNotEmpty(skuArray)){
            createCommand.setSkuList(replyData.getJSONObject("tradeModel").getJSONArray("skuMap").stream().map(skuInfo -> {
                JSONObject sku = (JSONObject) skuInfo;
                mappingInfo.getStockMap().put(sku.getString("skuId"),sku.getInteger("canBookCount"));
                SupplierSkuCreateInfo supplierSkuCreateInfo = new SupplierSkuCreateInfo();
                supplierSkuCreateInfo.setCustomCode(sku.getString("skuId"));
                supplierSkuCreateInfo.setRefProductLink("https://detail.1688.com/offer/"+productId+".html");
                supplierSkuCreateInfo.setMoq(((JSONObject) replyData.getJSONObject("tradeModel").getJSONObject("offerPriceModel").getJSONArray("currentPrices").get(0)).getInteger("beginAmount"));
                supplierSkuCreateInfo.setDeliveryDays(null);
                supplierSkuCreateInfo.setGuarantyPeriod(null);
                supplierSkuCreateInfo.setLeadTime(null);
                //重量
                Optional<BigDecimal> weight = Optional.ofNullable(replyData.getJSONObject("detailDescription").getJSONObject("freightInfo").getBigDecimal("unitWeight"));
                weight.ifPresent(w->{
                    supplierSkuCreateInfo.setGrossWeight(w.setScale(3,BigDecimal.ROUND_UP));
                    supplierSkuCreateInfo.setNetWeight(w.setScale(3,BigDecimal.ROUND_UP));
                });

                //设置价格
                String[] price = replyData.getJSONObject("tradeModel").getString("priceDisplay").split("-");
                Optional<BigDecimal> skuPrice = Optional.ofNullable(sku.getBigDecimal("price"));
                //supplierSkuCreateInfo.setLowestRetailPrice(new BigDecimal(price[0]));
                //supplierSkuCreateInfo.setHighestRetailPrice(new BigDecimal(price.length >1? price[1] : price[0]));
                //supplierSkuCreateInfo.setSuggestedRetailPrice(new BigDecimal(price.length >1? price[1] : price[0]));
                skuPrice.ifPresent(skuP->{
                    mappingInfo.getPriceMap().put(sku.getString("skuId"),skuP);
                });

                mappingInfo.setSupplyPrice(new BigDecimal(price.length >1? price[1] : price[0]));

                supplierSkuCreateInfo.setRetailPriceCurrency("CNY");
                supplierSkuCreateInfo.setMeasuringUnit(replyData.getJSONObject("tradeModel").getString("unit"));


                List<SupplierSkuSpecInfo> supplierSkuSpecInfoList = new ArrayList<SupplierSkuSpecInfo>();
                for (int i = 0; i < replyData.getJSONObject("offerDetail").getJSONArray("skuProps").size(); i++) {
                    JSONObject skuPro = (JSONObject) replyData.getJSONObject("offerDetail").getJSONArray("skuProps").get(i);
                    SupplierSkuSpecInfo skuSpecInfo = new SupplierSkuSpecInfo();
                    skuSpecInfo.setSpecName(skuPro.getString("prop"));
                    skuSpecInfo.setSpecValue(sku.getString("specAttrs").split("&gt;")[i]);
                    supplierSkuSpecInfoList.add(skuSpecInfo);
                }
                supplierSkuCreateInfo.setSpecs(supplierSkuSpecInfoList);
                //主图信息
                SupplierSkuMainSpecInfo supplierSkuMainSpecInfo = new SupplierSkuMainSpecInfo();
                MultimediaInfo multimediaInfo = new MultimediaInfo();
                multimediaInfo.setType(MultimediaType.IMAGE);
                multimediaInfo.setExternalUrl(skuImageMap.get(sku.getString("specAttrs").split("&gt;")[0]));
                supplierSkuMainSpecInfo.setImage(multimediaInfo);
                SupplierSkuSpecInfo supplierSkuSpecInfo = new SupplierSkuSpecInfo();
                String specAttrs = sku.getString("specAttrs").split("&gt;")[0];
                supplierSkuSpecInfoList.stream().filter(specInfo->StringUtils.equals(specInfo.getSpecValue(),specAttrs)).findAny().ifPresent(spec->{
                    supplierSkuSpecInfo.setSpecName(spec.getSpecName());
                    supplierSkuSpecInfo.setSpecValue(spec.getSpecValue());
                });
                supplierSkuMainSpecInfo.setSpec(supplierSkuSpecInfo);
                supplierSkuCreateInfo.setMainSpecInfo(supplierSkuMainSpecInfo);

                return supplierSkuCreateInfo;
            }).collect(Collectors.toList()));
        }else{
            createCommand.setSkuList(new ArrayList<SupplierSkuCreateInfo>(){
                {
                    SupplierSkuCreateInfo supplierSkuCreateInfo = new SupplierSkuCreateInfo();
                    //supplierSkuCreateInfo.setSupplierSkuId(sku.getString("skuId"));
                    supplierSkuCreateInfo.setCustomCode(replyData.getJSONObject("offerDetail").getString("offerId"));
                    supplierSkuCreateInfo.setRefProductLink("https://detail.1688.com/offer/"+productId+".html");
                    Optional<JSONArray> currentPricesList = Optional.ofNullable(replyData.getJSONObject("tradeModel").getJSONObject("offerPriceModel").getJSONArray("currentPrices"));
                    currentPricesList.ifPresent(currentPrices->{
                        supplierSkuCreateInfo.setMoq(((JSONObject)currentPrices.get(0)).getInteger("beginAmount"));
                    });
//                    supplierSkuCreateInfo.setMoq(((JSONObject) replyData.getJSONObject("tradeModel").getJSONObject("offerPriceModel").getJSONArray("currentPrices").get(0)).getInteger("beginAmount"));
                    supplierSkuCreateInfo.setDeliveryDays(null);
                    supplierSkuCreateInfo.setGuarantyPeriod(null);
                    supplierSkuCreateInfo.setLeadTime(null);
                    //重量
                    Optional<BigDecimal> weight = Optional.ofNullable(replyData.getJSONObject("detailDescription").getJSONObject("freightInfo").getBigDecimal("unitWeight"));
                    weight.ifPresent(w->{
                        supplierSkuCreateInfo.setGrossWeight(w.setScale(3,BigDecimal.ROUND_UP));
                        supplierSkuCreateInfo.setNetWeight(w.setScale(3,BigDecimal.ROUND_UP));
                    });
                    supplierSkuCreateInfo.setRetailPriceCurrency("CNY");
                    supplierSkuCreateInfo.setMeasuringUnit(replyData.getJSONObject("tradeModel").getString("unit"));

                    supplierSkuCreateInfo.setSpecs(null);
                    supplierSkuCreateInfo.setMainSpecInfo(null);

                    //设置价格
                    String[] price = replyData.getJSONObject("tradeModel").getString("priceDisplay").split("-");
                    mappingInfo.setSupplyPrice(new BigDecimal(price.length >1? price[1] : price[0]));

                    add(supplierSkuCreateInfo);
                }
            });
        }

        // 设置物流信息
        createCommand.setLogisticsAttrInfo(null);

    }

    public static void fill1688OpenSupplierAdjustSupplyPrice(SupplierSkuCreateInfo sku, OpenSupplierAdjustSupplyPriceReq supplyPriceReq, String supplierId,ProductMappingInfo mappingInfo) {
        supplyPriceReq.setSupplierId(supplierId);
        supplyPriceReq.setCustomSkuCode(sku.getCustomCode());
        supplyPriceReq.setCountry("CN");
        Map<String, BigDecimal> priceMap = mappingInfo.getPriceMap();
        if (priceMap.size()>0){
            supplyPriceReq.setSupplyPrice(priceMap.get(sku.getCustomCode()));
        }else{
            supplyPriceReq.setSupplyPrice(mappingInfo.getSupplyPrice());
        }
        supplyPriceReq.setSupplyPriceCurrency("CNY");
        List<OpenSupplierItemCreateCombineDetailInfo> items = new ArrayList<OpenSupplierItemCreateCombineDetailInfo>();
        OpenSupplierItemCreateCombineDetailInfo itemCreateCombineDetailInfo = new OpenSupplierItemCreateCombineDetailInfo();
        itemCreateCombineDetailInfo.setCombineNum(1);
        itemCreateCombineDetailInfo.setSupplyPrice(supplyPriceReq.getSupplyPrice());
        itemCreateCombineDetailInfo.setSupplyPriceCurrency("CNY");
        items.add(itemCreateCombineDetailInfo);
        supplyPriceReq.setCombineDetails(items);
    }

    public static void fill1688OpenSupplierUpdateStock(SupplierSkuCreateInfo sku, ProductMappingInfo mappingInfo, OpenSupplierUpdateStockReq updateStockReq, String supplierId, String customWarehouseCode) {
        updateStockReq.setSupplierId(supplierId);
        updateStockReq.setBizSerialNumber("STOCK" + System.currentTimeMillis());
        updateStockReq.setCustomSkuCode(sku.getCustomCode());
        Integer stockNum = mappingInfo.getStockMap().get(sku.getCustomCode());
        updateStockReq.setStockNum(stockNum == null? 0 : stockNum);
        updateStockReq.setCustomWarehouseCode(customWarehouseCode);
    }

}
