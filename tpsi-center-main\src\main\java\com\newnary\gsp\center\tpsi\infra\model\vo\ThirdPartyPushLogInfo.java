package com.newnary.gsp.center.tpsi.infra.model.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ThirdPartyPushLogInfo {
    /** 事件类型 */
    @NotBlank(message = "事件类型（必填）")
    private String eventType;
    /** 事件业务id */
    @NotBlank(message = "事件业务id（必填）")
    private String eventBizId;
    /** 事件处理数据 */
    private String eventData;
    /** 事件状态 */
    private Integer eventState = 0;
    /** 事件业务状态 */
    private Integer eventBizState;
    /** 请求信息 */
    private String reqData;
    /** 响应信息 */
    private String respData;
    /** 系统备注 */
    private String sysRemark;
    /** 人工备注 */
    private String manualRemark;
}
