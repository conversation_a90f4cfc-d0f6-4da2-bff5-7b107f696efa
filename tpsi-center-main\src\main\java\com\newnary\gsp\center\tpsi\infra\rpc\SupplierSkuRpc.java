package com.newnary.gsp.center.tpsi.infra.rpc;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.api.base.common.PageList;
import com.newnary.gsp.center.product.api.product.SupplierSkuApi;
import com.newnary.gsp.center.product.api.product.request.SupplierSkuPageQueryCommand;
import com.newnary.gsp.center.product.api.product.request.SupplierSkuSearchDetailsCommand;
import com.newnary.gsp.center.product.api.product.response.SupplierSkuDetailInfo;
import com.newnary.gsp.center.product.api.product.response.SupplierSkuInfo;
import com.newnary.gsp.center.product.api.product.response.SupplierSkuQueryLiteInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class SupplierSkuRpc {
    @Resource
    private SupplierSkuApi supplierSkuApiImpl;

    public List<SupplierSkuInfo> getSkuInfo(SupplierSkuSearchDetailsCommand command){
        CommonResponse<List<SupplierSkuDetailInfo>> details = supplierSkuApiImpl.searchDetails(command);
        List<SupplierSkuDetailInfo> list = details.getBody();
        return list.stream().map(SupplierSkuDetailInfo::getSkuInfo).collect(Collectors.toList());
    }

    public List<SupplierSkuQueryLiteInfo> pageQuery(SupplierSkuPageQueryCommand command) {
        return supplierSkuApiImpl.pageQuery(command).getBody().getItems();
    }
}
