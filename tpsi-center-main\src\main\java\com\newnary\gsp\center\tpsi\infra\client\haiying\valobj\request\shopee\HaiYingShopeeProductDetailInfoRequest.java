package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.shopee;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingShopeeProductDetailInfoRequest {

    /**
     * 站点(默认:马来西亚站)
     */
    @NotNull(message = "站点不能为空")
    private String station;

    /**
     * 商品id和店铺id(string型)
     * (以指定json数组的格式，单次最多100个pid和shop_id组合)
     * (如：[{"pid":"xxx","shop_id":"xxx"}])
     */
    @NotNull(message = "商品id和店铺id不能为空")
    private String pid_and_shop_ids;

}
