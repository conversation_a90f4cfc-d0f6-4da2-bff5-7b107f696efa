package com.newnary.gsp.center.tpsi.api.haiying.response.lazada;

import lombok.Data;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingLazadaCategoryInfoDTO {

    /**
     * 类目名
     */
    private String cname;

    /**
     * 类目等级
     */
    private Integer level;

    /**
     * 是否叶子类目
     * (0:否   1:是)
     */
    private Boolean is_leaf;

    /**
     * 商品所属类目链接
     */
    private String curl;

    /**
     * 1级类目名称
     */
    private String p_l1_name;

    /**
     * 2级类目名称
     */
    private String p_l2_name;

    /**
     * 3级类目名称
     */
    private String p_l3_name;

    /**
     * 4级类目名称
     */
    private String p_l4_name;

    /**
     * 5级类目名称
     */
    private String p_l5_name;

    /**
     * 6级类目名称
     */
    private String p_l6_name;

    /**
     * 7级类目名称
     */
    private String p_l7_name;

    /**
     * 8级类目名称
     */
    private String p_l8_name;

    /**
     * 9级类目名称
     */
    private String p_l9_name;

    /**
     * 10级类目名称
     */
    private String p_l10_name;

    /**
     * 类目前7天销量
     */
    private Integer category_seven_days_sold;

    /**
     * 类目商品前7天新增评论数
     */
    private Double category_seven_days_payment;

    /**
     * 类目商品总数(海鹰统计)
     */
    private Integer category_product_sum;

    /**
     * 类目统计时间
     */
    private Long stat_time;

    /**
     * 类目总销售件数
     */
    private Integer category_sold;

    /**
     *类目路径
     */
    private String cname_path;

    /**
     * 类目商品总数(抓取)
     */
    private Integer item_size;

}
