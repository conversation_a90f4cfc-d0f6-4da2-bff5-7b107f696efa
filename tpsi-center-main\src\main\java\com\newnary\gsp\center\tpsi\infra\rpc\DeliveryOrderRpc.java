package com.newnary.gsp.center.tpsi.infra.rpc;

import com.newnary.api.base.common.PageList;
import com.newnary.gsp.center.logistics.api.delivery.feign.DeliveryOrderFeignApi;
import com.newnary.gsp.center.logistics.api.delivery.request.DeliveryOrderGetCommand;
import com.newnary.gsp.center.logistics.api.delivery.request.DeliveryOrderPageQueryCommand;
import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderDetailInfo;
import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderLiteInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/1
 */
@Component
public class DeliveryOrderRpc {
    
    @Resource
    private DeliveryOrderFeignApi deliveryOrderFeignApi;

    public DeliveryOrderDetailInfo getDetailInfo(String deliveryOrderId) {
        DeliveryOrderGetCommand deliveryOrderGetCommand = new DeliveryOrderGetCommand();
        deliveryOrderGetCommand.setDeliveryOrderId(deliveryOrderId);
        return deliveryOrderFeignApi.get(deliveryOrderGetCommand).mustSuccessOrThrowOriginal();
    }

    public DeliveryOrderLiteInfo getLiteInfo(String deliveryOrderId) {
        DeliveryOrderGetCommand deliveryOrderGetCommand = new DeliveryOrderGetCommand();
        deliveryOrderGetCommand.setDeliveryOrderId(deliveryOrderId);
        return deliveryOrderFeignApi.getLite(deliveryOrderGetCommand).mustSuccessOrThrowOriginal();
    }

    public List<DeliveryOrderDetailInfo> getDetailsByOrderId(String orderId) {
//        DeliveryOrderPageQueryCommand command = new DeliveryOrderPageQueryCommand();
//        command.setPageCondition(new PageCondition(1, 50));
//        command.setTradeOrderId(orderId);
//        PageList<DeliveryOrderLiteInfo> deliveryOrderLiteInfoPageList = deliveryOrderFeignApi.pageQuery(command).mustSuccessOrThrowOriginal();
        List<DeliveryOrderDetailInfo> details = new ArrayList<>();
//        if (ObjectUtils.isNotEmpty(deliveryOrderLiteInfoPageList)) {
//            List<DeliveryOrderLiteInfo> items = deliveryOrderLiteInfoPageList.getItems();
//            if (CollectionUtils.isNotEmpty(items)) {
//                items.forEach(order-> {
//                    DeliveryOrderDetailInfo detailInfo = getDetailInfo(order.getDeliveryOrderId());
//                    details.add(detailInfo);
//                });
//            }
//        }
        List<String> deliveryOrderIds = deliveryOrderFeignApi.queryDeliveryOrderIdsByTradeOrderId(orderId).mustSuccessOrThrowOriginal();
        deliveryOrderIds.forEach(deliveryOrderId -> {
            details.add(getDetailInfo(deliveryOrderId));
        });
        return details;
    }

//    public List<DeliveryOrderDetailInfo> getDeliveryOrderListDetails(DeliveryOrderPageQueryCommand command) {
//        PageList<DeliveryOrderLiteInfo> deliveryOrderLiteInfoPageList = deliveryOrderFeignApi.pageQuery(command).mustSuccessOrThrowOriginal();
//        List<DeliveryOrderDetailInfo> details = new ArrayList<>();
//        if (ObjectUtils.isNotEmpty(deliveryOrderLiteInfoPageList)) {
//            List<DeliveryOrderLiteInfo> items = deliveryOrderLiteInfoPageList.getItems();
//            if (CollectionUtils.isNotEmpty(items)) {
//                items.forEach(order-> {
//                    DeliveryOrderDetailInfo detailInfo = getDetailInfo(order.getDeliveryOrderId());
//                    details.add(detailInfo);
//                });
//            }
//        }
//        return details;
//    }

    public PageList<DeliveryOrderLiteInfo> pageQuery(DeliveryOrderPageQueryCommand command) {
        return deliveryOrderFeignApi.pageQuery(command).mustSuccessOrThrowOriginal();
    }
}
