package com.newnary.gsp.center.tpsi.pgl;

import com.alibaba.fastjson.JSONObject;
import com.newnary.gsp.center.tpsi.api.logisticsService.request.LogisticsPrintOrderCommand;
import com.newnary.gsp.center.tpsi.api.logisticsService.response.LogisticsPrintSheetResp;
import com.newnary.gsp.center.tpsi.app.service.transport.TransportOrderCommandApp;
import com.newnary.gsp.center.tpsi.infra.model.vo.LogisticsServiceConstants;
import com.newnary.gsp.center.tpsi.infra.model.vo.LogisticsServiceContext;
import com.newnary.gsp.center.tpsi.service.PGL.impl.PGLLogisticsApiSveImpl;
import com.newnary.test.starter.BaseTestInjectTenant;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * @Author: jack
 * @CreateTime: 2024/1/22
 */
public class PGLApiTest extends BaseTestInjectTenant {

    @Resource
    private TransportOrderCommandApp transportOrderCommandApp;
    @Resource
    private PGLLogisticsApiSveImpl pglLogisticsApiSvelmpl;

    @Override
    protected String tenantId() {
        return "TENANT3924117688555169382400";
    }

    @Test
    public void test_thirdPush() {
        transportOrderCommandApp.thirdPush("TO150801781686298", "PGL");
    }

    @Test
    public void test_printSheet() {
        LogisticsPrintOrderCommand printSheetReq = new LogisticsPrintOrderCommand();
        printSheetReq.setTrackingId("TO150790111035413");
        LogisticsServiceContext.getCurrentContext().put(LogisticsServiceConstants.LOGISTICS_PRINT_DOMAIN, printSheetReq);
        pglLogisticsApiSvelmpl.printSheet();
        LogisticsPrintSheetResp printSheetResp = (LogisticsPrintSheetResp) LogisticsServiceContext.getCurrentContext().get(LogisticsServiceConstants.CALL_PRINT_RESPONSE);
        System.out.println(JSONObject.toJSONString(printSheetResp));
    }

}
