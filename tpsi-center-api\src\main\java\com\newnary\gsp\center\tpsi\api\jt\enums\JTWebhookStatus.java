package com.newnary.gsp.center.tpsi.api.jt.enums;

import lombok.Getter;
import org.springframework.util.StringUtils;

/**
 * @Author: jack
 * @CreateTime: 2023-8-15
 */
@Getter
public enum JTWebhookStatus {

    OrderCreated("Order Created", "创建订单",
            "Order has been confirmed and is pending pickup", "订单确认并等待揽件"),
    SuccessfulPickup("Picked Up", "取件成功",
            "Proof of pickup is now ready", "揽件证明已生成"),
    PickedUpFail("Picked Up Fail", "取件失败",
            "Picked Up Fail", "包裹揽收失败"),
    SuccessfulDelivery("Delivered", "派件成功",
            "Proof of delivery is now ready", "收件证明已生成"),
    DeliveryFail("Delivery Fail", "派件失败",
            "Proof of pickup is now ready", "揽件证明已生成"),
    ReturnInitiated("Return Initiated", "发起退件",
            "Order is on van, en-route to return to sender", "订单已在退仓途中"),
    ReturnedToSender("Return Delivered", "退件签收",
            "Delivery of Order has failed repeatedly, Returned to Sender", "已退仓"),
    Departure("Departure", "发件",
            "Departure", "发件"),
    Arrival("Arrival", "到件",
            "Arrival", "到件"),
    OnDelivery("On Delivery", "派件",
            "On Delivery", "派件"),
    ReturnRegister("Return Register", "退件登记",
            "Return Register", "触发异常，生成售后单，异常原因取【问题件/疑难件】"),
    Problematic("Problematic", "问题件/疑难件",
            "Problematic", "运输过程中出现异常（1、2次联系客户失败，派送失败等），但未产生退件"),
    ;

    private String name;

    private String nameCn;

    private String description;

    private String descriptionCn;

    JTWebhookStatus(String name, String nameCn, String description, String descriptionCn) {
        this.name = name;
        this.nameCn = nameCn;
        this.description = description;
        this.descriptionCn = descriptionCn;
    }

    public static JTWebhookStatus getByName(String name) {
        if (!StringUtils.isEmpty(name)) {
            for (JTWebhookStatus status : values()) {
                String webhookName = name.replaceAll(" ", "");
                String statusName = status.name.replaceAll(" ", "");
                if (statusName.equalsIgnoreCase(webhookName)) {
                    return status;
                }
            }
        }
        return null;
    }

}
