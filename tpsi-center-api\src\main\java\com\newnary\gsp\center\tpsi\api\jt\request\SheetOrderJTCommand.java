package com.newnary.gsp.center.tpsi.api.jt.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 「获取面单」JT物流运输单，请求体
 *
 * <AUTHOR>
 * @since Created on 2023-08-11
 **/
@Data
public class SheetOrderJTCommand {

    /**
     * 消息提供者ID
     **/
    @NotBlank(message = "能者物流追踪号不能为空")
    private String eccompanyid;

    /**
     * 客户编号
     **/
    @NotBlank(message = "能者物流追踪号不能为空")
    private String customerid;

    /**
     * 查询命令 1.按订单编号查询   2.按运单编号查询   3.按下单时间段查询
     **/
    @NotBlank(message = "能者物流追踪号不能为空")
    private String command;

    /**
     * 编号(多个编号用逗号隔开) 当command=1 编号指的是订单编号 当command=2 编号指的是运单编号
     **/
    @NotBlank(message = "能者物流追踪号不能为空")
    private String serialnumber;

    /**
     * 开始时间(24小时制: yyyy-MM-dd HH:mm:ss)
     **/
    @NotBlank(message = "能者物流追踪号不能为空")
    private String startdate;

    /**
     * 结束时间(24小时制: yyyy-MM-dd HH:mm:ss)
     **/
    @NotBlank(message = "能者物流追踪号不能为空")
    private String enddate;

}
