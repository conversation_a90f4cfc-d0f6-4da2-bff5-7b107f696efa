package com.newnary.gsp.center.tpsi.service.haiying.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.tpsi.api.haiying.request.lazada.HaiYingLazadaProductListCommand;
import com.newnary.gsp.center.tpsi.infra.client.haiying.HaiYingDataApiClient;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.HaiYingDataApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.lazada.*;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.service.haiying.IHaiYingDataLazadaApiSve;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Slf4j
@Component
public class HaiYingDataLazadaApiSveImpl implements IHaiYingDataLazadaApiSve {

    private static final String thirdPartySystemId = "TEST_HAIYINGDATE_API";

    private static final String haiyingVersion = "hysj_v6";

    private static final String haiyingPlatform = "lazada_api";

    private static final Integer pageLimit = 100000;

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    private ThirdPartySystem thirdPartySystem;

    private String lazadaApiUrl;

    private HaiYingDataApiClient haiYingDataApiClient;

    private static final SerializerFeature[] features = {SerializerFeature.WriteMapNullValue};

    public HaiYingDataLazadaApiSveImpl() {
        lazadaApiUrl = haiyingVersion.concat(File.separator).concat(haiyingPlatform).concat(File.separator);
    }

    private void init() {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        //获取apiClient
        haiYingDataApiClient = getClient(thirdPartySystem.getParams());
    }

    @Override
    public HaiYingDataApiBaseResult<String> getProductList(HaiYingLazadaProductListRequest productsInfoRequest) {
        if (StringUtils.isNotEmpty(productsInfoRequest.getPage_size()) && StringUtils.isNotEmpty(productsInfoRequest.getCurrent_page())) {
            Integer pageSize = Integer.valueOf(productsInfoRequest.getPage_size());
            Integer currentPage = Integer.valueOf(productsInfoRequest.getCurrent_page());
            if (pageSize * currentPage > pageLimit) {
                return commonSendMethod("item_infos", JSON.toJSONString(productsInfoRequest, features));
            }
        }
        return commonSendMethod("item_infos_query", JSON.toJSONString(productsInfoRequest, features));
    }

    @Override
    public HaiYingDataApiBaseResult<String> getProductDetailInfo(HaiYingLazadaProductDetailInfoRequest productDetailInfoRequest) {
        return commonSendMethod("item_info", JSON.toJSONString(productDetailInfoRequest, features));
    }

    @Override
    public HaiYingDataApiBaseResult<String> getProductExtInfo(HaiYingLazadaProductExtInfoRequest productExtInfoRequest) {
        return commonSendMethod("monitor/item_details", JSON.toJSONString(productExtInfoRequest, features));
    }

    @Override
    public HaiYingDataApiBaseResult<String> getProductHistoryInfo(HaiYingLazadaProductHistoryInfoRequest productHistoryInfoRequest) {
        return commonSendMethod("get_items_his", JSON.toJSONString(productHistoryInfoRequest, features));
    }

    @Override
    public HaiYingDataApiBaseResult<String> getProductHistoryDailyReview(HaiYingLazadaProductHistoryDailyReviewRequest productHistoryDailyReviewRequest) {
        return commonSendMethod("get_items_review_his", JSON.toJSONString(productHistoryDailyReviewRequest, features));
    }

    @Override
    public HaiYingDataApiBaseResult<String> getCategoryTree(HaiYingLazadaCategoryTreeRequest categoryTreeRequest) {
        return commonSendMethod("cate_tree", JSON.toJSONString(categoryTreeRequest, features));
    }

    @Override
    public HaiYingDataApiBaseResult<String> getTopCategoryInfo(HaiYingLazadaTopCategoryInfoRequest topCategoryInfoRequest) {
        return commonSendMethod("top_cate_stat", JSON.toJSONString(topCategoryInfoRequest, features));
    }

    @Override
    public HaiYingDataApiBaseResult<String> getSubCategoryInfo(HaiYingLazadaSubCategoryInfoRequest subCategoryInfoRequest) {
        return commonSendMethod("sub_cate_stat", JSON.toJSONString(subCategoryInfoRequest, features));
    }

    private HaiYingDataApiBaseResult<String> commonSendMethod(String serviceName, String requestJson) {
        if (null == haiYingDataApiClient) {
            init();
        }
        return haiYingDataApiClient.sendLazadaRequest(lazadaApiUrl.concat(serviceName),"application/json", JSON.parseObject(requestJson));
    }


    public ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }

    private HaiYingDataApiClient getClient(String ecCangParams) {
        return new HaiYingDataApiClient(ecCangParams);
    }

}
