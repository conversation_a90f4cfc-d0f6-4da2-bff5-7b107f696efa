package com.newnary.gsp.center.tpsi.api.jt.request;

import com.newnary.gsp.center.tpsi.api.jt.enums.JTDeliveryType;
import com.newnary.gsp.center.tpsi.api.jt.enums.JTEnvironment;
import com.newnary.gsp.center.tpsi.api.jt.enums.JTOrderType;
import com.newnary.gsp.center.tpsi.api.jt.enums.JTServiceType;
import com.newnary.gsp.center.tpsi.api.jt.vo.CreateJTOrderItems;
import com.newnary.gsp.center.tpsi.api.jt.vo.CreateOrderJTReceiver;
import com.newnary.gsp.center.tpsi.api.jt.vo.CreateOrderJTSender;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 「创建」极兔物流运输单，请求体
 *
 * <AUTHOR>
 * @since Created on 2023-08-11
 **/
@Data
public class CreateOrderJTCommand {

    @Size(max = 40, message = "msg_type(最大40个字符)")
    @NotBlank(message = "msg_type(不能为空)")
    private String msg_type;

    @Size(max = 50, message = "actiontype(最大50个字符)")
    @NotBlank(message = "actiontype(不能为空)")
    private String actiontype;

    /**
     * 是否测试数据（staging：是，production：否（默认））
     */
    @Size(max = 50, message = "environment(最大50个字符)")
    @NotBlank(message = "environment(不能为空)")
    private String environment;

    /**
     * 消息提供者ID    由平台分配给客户
     */
    @Size(max = 50, message = "eccompanyid(最大50个字符)")
    @NotBlank(message = "eccompanyid(不能为空)")
    private String eccompanyid;

    @Size(max = 20, message = "eccompanyid(最大20个字符)")
    @NotBlank(message = "customerid(不能为空)")
    private String customerid;

    /**
     * 物流订单号    传客户自己系统的订单号
     */
    @Size(max = 20, message = "txlogisticid(最大20个字符)")
    @NotBlank(message = "txlogisticid(不能为空)")
    private String txlogisticid;

    /**
     * 运单编号    当重新下单时传入
     */
    private String mailno;

    /**
     * 订单类型    1=普通订单  2=退货订单
     */
    @Size(max = 1, message = "ordertype(最大1个字符)")
    @NotBlank(message = "ordertype(不能为空)")
    private String ordertype = JTOrderType.NORMAL_ORDER.getValue();

    /**
     * 服务类型    1=上门取件  6=上门寄件
     */
    @Size(max = 1, message = "servicetype(最大1个字符)")
    @NotBlank(message = "servicetype(不能为空)")
    private String servicetype = JTServiceType.HOME_PICKUP.getValue();

    /**
     * 派送类型    1=正常派送  2=自提件
     */
    @Size(max = 1, message = "deliverytype(最大1个字符)")
    @NotBlank(message = "deliverytype(不能为空)")
    private String deliverytype = JTDeliveryType.NORMAL_DELIVERY.getValue();

    @NotNull(message = "sender(不能为空)")
    private CreateOrderJTSender sender;

    @NotNull(message = "receiver(不能为空)")
    private CreateOrderJTReceiver receiver;

    /**
     * 物流公司上门取货开始时间
     */
    @Size(max = 19, message = "createordertime(最大19个字符)")
    @NotBlank(message = "createordertime(不能为空)")
    private String createordertime;

    /**
     * 物流公司上门取货结束时间
     */
    @Size(max = 19, message = "sendstarttime(最大19个字符)")
    @NotBlank(message = "sendstarttime(不能为空)")
    private String sendstarttime;

    @Size(max = 19, message = "createordertime(最大19个字符)")
    @NotBlank(message = "createordertime(不能为空)")
    private String sendendtime;

    /**
     * 支付方式    1=寄付月结     默认传1
     */
    @Size(max = 1, message = "paytype(最大1个字符)")
    @NotBlank(message = "paytype(不能为空)")
    private String paytype = "1";

    /**
     * 单位kg
     */
    @Size(max = 10, message = "weight(最大10个字符)")
    @NotBlank(message = "weight(不能为空)")
    private String weight;

    @Size(max = 10, message = "volume(最大10个字符)")
    private String volume;

    /**
     * 代收货款金额(菲律宾货币:PHP)
     */
    @Size(max = 20, message = "itemsvalue(最大20个字符)")
    @NotBlank(message = "itemsvalue(不能为空)")
    private String itemsvalue;

    @Size(max = 10, message = "totalquantity(最大10个字符)")
    @NotBlank(message = "totalquantity(不能为空)")
    private String totalquantity;

    /**
     * 保价费
     */
    @Size(max = 20, message = "offerFee(最大20个字符)")
    private String offerFee;

    @Size(max = 19, message = "createordertime(最大19个字符)")
    private String remark;


    @NotEmpty(message = "items(不能为空)")
    private List<CreateJTOrderItems> items;


    @Size(max = 50, message = "sellerID(最大50个字符)")
    private String sellerID;
    @Size(max = 500, message = "sellerEmail(最大500个字符)")
    private String sellerEmail;

    /**
     * 是否保价( 0:不保价 1:保价(默认) )
     */
    @Size(max = 1, message = "isInsured(最大1个字符)")
    private String isInsured;


}
