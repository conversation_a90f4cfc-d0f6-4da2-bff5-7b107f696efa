package com.newnary.gsp.center.tpsi.infra.rpc;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.gsp.center.logistics.api.route.feign.RouteInfoFeignApi;
import com.newnary.gsp.center.logistics.api.route.request.RouteInfoAddEventCommand;
import com.newnary.gsp.center.logistics.api.route.request.RouteInfoQueryCommand;
import com.newnary.gsp.center.logistics.api.route.response.RouteInfoResp;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class RouteInfoRpc {

    @Resource
    private RouteInfoFeignApi routeInfoFeignApi;

    public void addRouteInfo(RouteInfoAddEventCommand command){
        routeInfoFeignApi.addRouteInfo(command);
    }

    public CommonResponse<RouteInfoResp> query(RouteInfoQueryCommand command){
        return routeInfoFeignApi.paramQuery(command);
    }
}
