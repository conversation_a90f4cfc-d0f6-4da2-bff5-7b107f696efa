package com.newnary.gsp.center.tpsi.service.lingxing.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Data
public class QueryLingXingProductListResponse {

    //状态码，0 成功
    private Integer code;
    //消息提示
    private String message;
    //错误信息
    private List<String> error_details;
    //请求链路id
    private String request_id;
    //响应时间
    private String response_time;
    //总数
    private Integer total;
    //响应数据
    private List<Product> data;

    @Setter
    @Getter
    public static class Product{

        //本地产品id
        private Integer id;

        //类别id
        private Integer cid;

        //类别
        private String category_name;

        //品牌id
        private Integer bid;

        //品牌
        private String brand_name;

        //本地产品SKU
        private String sku;

        //品名
        private String product_name;

        //	图片链接
        private String pic_url;

        //SPU唯一id
        private Integer ps_id;

        //SPU
        private String spu;

        //采购：交期
        private Integer cg_delivery;

        //采购：运输成本
        private BigDecimal cg_transport_costs;

        //采购备注
        private String purchase_remark;

        //	采购：采购成本（人民币）
        private BigDecimal cg_price;

        //产品是否启用：0 停用，1 启用
        private Integer open_status;

        //状态：0 停售，1 在售，2 开发中，3 清仓
        private Integer status;

        //	状态文本
        private String status_text;

        //是否为组合产品：0 否，1 是
        private Integer is_combo;

        //创建时间
        private Integer create_time;

        //更新时间
        private Integer update_time;

        //开发人员id
        private String product_developer_uid;

        //开发人员名称
        private String product_developer;

        //采购：采购员id
        private String cg_opt_uid;

        //采购：采购员名称
        private String cg_opt_username;

        //供应商报价信息
        private List<SupplierQuote> supplier_quote;

        @Getter
        @Setter
        public static class SupplierQuote{

            //供应商报价id
            private String psq_id;

            //产品id
            private Integer product_id;

            //供应商id
            private Integer supplier_id;

            //是否为首选供应商：0 否，1 是
            private Integer is_primary;

            //采购链接
            private String supplier_product_url;

            //供应商报价备注
            private String quote_remark;

            //采购成本
            private BigDecimal cg_price;

            //采购成本币种符号
            private String cg_currency_icon;

            //供应商代码
            private String supplier_code;

            //级别
            private String level_text;

            //规模
            private String employees_text;

            //供应商备注
            private String remark;

            //供应商名称
            private String supplier_name;

            //报价数据
            private List<Quote> quotes;
            @Setter
            @Getter
            public static class Quote{

                //报价币种
                private String currency;

                //报价币种符号
                private String currency_icon;

                //是否含税：0 否 1 是
                private int is_tax;

                //税率（百分比）
                private BigDecimal tax_rate;

                //报价梯度
                private List<StepPrice> step_prices;

                @Setter
                @Getter
                public static class StepPrice {

                    //最小采购量
                    private Integer moq;

                    //不含税单价
                    private BigDecimal price;

                    //含税单价
                    private BigDecimal price_with_tax;
                }

            }

        }
    }

}
