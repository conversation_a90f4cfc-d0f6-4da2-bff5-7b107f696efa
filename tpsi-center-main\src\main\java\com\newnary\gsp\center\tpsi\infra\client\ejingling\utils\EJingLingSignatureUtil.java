package com.newnary.gsp.center.tpsi.infra.client.ejingling.utils;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.security.MessageDigest;

public class EJingLingSignatureUtil {

    private final static Logger LOGGER = LoggerFactory.getLogger(EJingLingSignatureUtil.class);

    public static boolean verify(String paramStr, String secret, String methodName, long timestamp, String signature) throws IOException {
        if (StringUtils.isBlank(signature)) return false;
        String interSignature = sign(paramStr, secret, methodName, timestamp);
        return signature.equals(interSignature);
    }

    public static String sign(String paramStr, String secret, String methodName, long timestamp) throws IOException {
        StringBuilder sb = new StringBuilder();
        sb.append(secret).append(paramStr).append(methodName).append(timestamp);
        byte[] bytes = encryptMD5(sb.toString());
        return byte2hex(bytes);
    }

    public static byte[] encryptMD5(String data) throws IOException {
        return encryptMD5(data.getBytes("UTF-8"));
    }

    public static byte[] encryptMD5(byte[] data) throws IOException {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] bytes = md.digest(data);
            return bytes;
        }
        catch (GeneralSecurityException g) {
            throw new IOException(g.toString());
        }
    }

    public static String byte2hex(byte[] bytes) {
        StringBuilder sign = new StringBuilder();
        for (int i = 0; i < bytes.length; ++i) {
            String hex = Integer.toHexString(bytes[i] & 255);
            if (hex.length() == 1) {
                sign.append("0");
            }
            sign.append(hex.toUpperCase());
        }
        return sign.toString();
    }

    public static void main(String[] args) {
        try {
/*            JSONObject jsonObject = new JSONObject();
            jsonObject.put("page", 1);
            jsonObject.put("size", 100);
            Date date =new Date();
            System.out.println();
            jsonObject.put("createdTime",date);
            System.out.println(jsonObject.toJSONString());
            String sign = sign(jsonObject.toString(), "3F9E4065D61C6F4A5834E25A9BCA3D92", "outerOrderCreate", date);
            System.out.println(sign);
            SimpleDateFormat formatTime= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            boolean flag = verify(jsonObject.toString(), "3F9E4065D61C6F4A5834E25A9BCA3D92", "outerOrderCreate", date, sign);
            System.out.println(flag);*/
        }
        catch (Exception e) {
            e.printStackTrace();
        }
    }

}
