package com.newnary.gsp.center.tpsi.api.haiying.request.lazada;

import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingStation;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-8-31
 */
@Data
public class HaiYingLazadaProductHistoryDailyReviewCommand {

    /**
     * 站点
     */
    @NotNull(message = "站点不能为空")
    private HaiYingStation station;

    /**
     * 商品id(string型)
     * (多个商品id用逗号分隔，最多100个商品id)
     */
    @NotNull(message = "商品id不能为空")
    private List<String> item_ids;

    /**
     * 商品统计日期起始值(string型,格式:年-月-日)
     * (默认查询近一个月的数据)
     */
    private Long stat_date_start;

    /**
     * 商品抓取时间结束值(string型,格式:年-月-日)
     * (默认查询近一个月的数据)
     */
    private Long stat_date_end;

}
