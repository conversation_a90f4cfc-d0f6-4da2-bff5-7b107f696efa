package com.newnary.gsp.center.tpsi.service.haiying.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.tpsi.infra.client.haiying.HaiYingDataApiClient;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.HaiYingDataApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.ebay.*;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.service.haiying.IHaiYingDataEbayApiSve;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Slf4j
@Component
public class HaiYingDataEbayApiSveImpl implements IHaiYingDataEbayApiSve {

    private static final String thirdPartySystemId = "TEST_HAIYINGDATE_API";

    private static final String haiyingVersion = "hysj_v2";

    private static final String haiyingPlatform = "ebay_api";

    private static final Integer pageLimit = 100000;

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    private ThirdPartySystem thirdPartySystem;

    private String ebayApiUrl;

    private HaiYingDataApiClient haiYingDataApiClient;

    private static final SerializerFeature[] features = {SerializerFeature.WriteMapNullValue};

    public HaiYingDataEbayApiSveImpl() {
        ebayApiUrl = haiyingVersion.concat(File.separator).concat(haiyingPlatform).concat(File.separator);
    }

    private void init() {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        //获取apiClient
        haiYingDataApiClient = getClient(thirdPartySystem.getParams());
    }

    @Override
    public HaiYingDataApiBaseResult<String> getProductList(HaiYingEbayProductListRequest productListRequest) {
        if (StringUtils.isNotEmpty(productListRequest.getPage_size()) && StringUtils.isNotEmpty(productListRequest.getCurrent_page())) {
            Integer pageSize = Integer.valueOf(productListRequest.getPage_size());
            Integer currentPage = Integer.valueOf(productListRequest.getCurrent_page());
            if (pageSize * currentPage > pageLimit) {
                return commonSendMethod("item_infos", JSON.toJSONString(productListRequest, features));
            }
        }
        return commonSendMethod("item_infos_query", JSON.toJSONString(productListRequest, features));
    }

    @Override
    public HaiYingDataApiBaseResult<String> getProductDetailInfo(HaiYingEbayProductDetailInfoRequest productDetailInfoRequest) {
        return commonSendMethod("item_info", JSON.toJSONString(productDetailInfoRequest, features));
    }

    @Override
    public HaiYingDataApiBaseResult<String> getCategoryTree(HaiYingEbayCategoryTreeRequest categoryTreeRequest) {
        return commonSendMethod("cate_tree", JSON.toJSONString(categoryTreeRequest, features));
    }

    @Override
    public HaiYingDataApiBaseResult<String> getCategoryDetail(HaiYingEbayCategoryDetailRequest categoryDetailRequest) {
        return commonSendMethod("cate_infos", JSON.toJSONString(categoryDetailRequest, features));
    }

    @Override
    public HaiYingDataApiBaseResult<String> getTopCategoryInfo(HaiYingEbayTopCategoryInfoRequest topCategoryInfoRequest) {
        return commonSendMethod("top_cate_infos", JSON.toJSONString(topCategoryInfoRequest, features));
    }

    private HaiYingDataApiBaseResult<String> commonSendMethod(String serviceName, String requestJson) {
        if (null == haiYingDataApiClient) {
            init();
        }
        return haiYingDataApiClient.sendRequest(ebayApiUrl.concat(serviceName), JSON.parseObject(requestJson));
    }


    public ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }

    private HaiYingDataApiClient getClient(String ecCangParams) {
        return new HaiYingDataApiClient(ecCangParams);
    }

}
