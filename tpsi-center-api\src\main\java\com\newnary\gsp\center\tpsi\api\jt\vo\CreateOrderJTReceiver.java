package com.newnary.gsp.center.tpsi.api.jt.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 2023-12-26
 * 收件信息对象
 * <AUTHOR>
 */
@Data
public class CreateOrderJTReceiver {

    @Size(max = 100,message = "name(最大100个字符)")
    @NotBlank(message = "name(不能为空)")
    private String name;

    @Size(max = 50,message = "postcode(最大50个字符)")
    private String postcode;


    @Size(max = 40,message = "mailbox(最大40个字符)")
    @NotBlank(message = "mailbox(不能为空)")
    private String mailbox;


    @Size(max = 50,message = "mobile(最大50个字符)")
    private String mobile;


    @Size(max = 50,message = "phone(最大50个字符)")
    @NotBlank(message = "phone(不能为空)")
    private String phone;

    @Size(max = 50,message = "prov(最大50个字符)")
    @NotBlank(message = "prov(不能为空)")
    private String prov;

    @Size(max = 100,message = "city(最大100个字符)")
    @NotBlank(message = "city(不能为空)")
    private String city;

    @Size(max = 50,message = "area(最大50个字符)")
    @NotBlank(message = "area(不能为空)")
    private String area;

    @Size(max = 50,message = "town(最大50个字符)")
    private String town;


    @Size(max = 300,message = "address(最大300个字符)")
    @NotBlank(message = "address(不能为空)")
    private String address;


}
