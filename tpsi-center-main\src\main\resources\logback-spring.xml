<?xml version="1.0" encoding="UTF-8"?>

<configuration>

    <springProperty scope="context" name="APP_NAME" source="spring.application.name"/>
    <property name="APP_NAME" value="${APP_NAME}"/>
    <property name="LOG_FILE" value="${HOME}/logs/${APP_NAME}/app.log}"/>
    <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36}-Line%line -%msg%n"/>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">

        <file>${LOG_FILE}</file>

        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>

        <!-- 可让每天产生一个日志文件，最多 10 个，自动回滚 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE}-%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>20MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>10</maxHistory>
        </rollingPolicy>
        <encoder>
            <Pattern>${LOG_PATTERN}</Pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <!-- 输出到文件，可定义更多的 Appender -->
    <root level="info">
        <appender-ref ref="FILE"/>
    </root>


    <!-- 下面配置一些第三方包的日志过滤级别，用于避免刷屏 -->
    <logger name="org.hibernate" level="INFO"/>
    <logger name="org.springframework" level="INFO"/>
    <logger name="org.jboss" level="INFO"/>
    <logger name="org.apache" level="INFO"/>
    <logger name="org.I0Itec.zkclient" level="INFO"/>
    <logger name="com.opensymphony" level="INFO"/>
    <logger name="com.apache.dubbo" level="INFO"/>
    <logger name="springfox.documentation" level="INFO"/>
    <logger name="javax.management" level="INFO"/>

</configuration>
