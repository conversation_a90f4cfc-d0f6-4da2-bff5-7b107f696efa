package com.newnary.gsp.center.tpsi.infra.client.tk.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class StockUpdateReq {
    public String product_id;
    public List<Sku> skus;

    @Getter
    @Setter
    public static class Sku {
        public String id;
        public List<StockInfo> stock_infos;
    }

    @Getter
    @Setter
    public static class StockInfo {
        public String warehouse_id;
        public String available_stock;
    }
}
