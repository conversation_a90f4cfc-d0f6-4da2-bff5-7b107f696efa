package com.newnary.gsp.center.tpsi.api.haiying.request.ebay;

import com.newnary.api.base.common.PageCondition;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingEbayProductListOrderBy;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingOrderByType;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingStation;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingEbayProductListCommand {

    /**
     * 站点
     */
    @NotNull(message = "站点不能为空")
    private HaiYingStation station;

    /**
     * 商品id(string型)
     * (多个商品id用逗号分隔,单次最多500个商品id)
     */
    private List<String> item_ids;

    /**
     * 商品标题(string型)
     */
    private String title;

    /**
     * 商品标题查询类型(int 型):
     * 1:整句语句(默认)
     * 2:多个搜索词,”与”关系
     * 3:多个搜索词,”或”关系
     */
    private Integer title_type;

    /**
     * 商品不包含标题(string型)
     */
    private String not_exist_title;

    /**
     * 商品不包含标题查询类型(int 型):
     * 1:整句语句(默认)
     * 2:多个搜索词,”与”关系
     * 3:多个搜索词,”或”关系
     */
    private Integer not_exist_title_type;

    /**
     * 商品item地点(string型)
     * (多个以英文逗号分隔)
     */
    private List<String> item_locations;

    /**
     * 卖家名称(string型)
     * (多个seller以逗号分隔)
     */
    private List<String> sellers;

    /**
     * 店铺名称(string型)
     */
    private String store;

    /**
     * 店铺注册地址(string型)
     * (多个以英文逗号分隔)
     */
    private List<String> store_locations;

    /**
     * 商品总销售件数起始值(int 型)
     */
    private Integer sold_start;

    /**
     * 商品总销售件数结束值(int 型)
     */
    private Integer sold_end;

    /**
     * 商品前1天销售件数起始值(int 型)
     */
    private Integer sold_the_previous_day_start;

    /**
     * 商品前1天销售件数结束值(int 型)
     */
    private Integer sold_the_previous_day_end;

    /**
     * 商品前1天销售金额起始值(double型)
     */
    private BigDecimal payment_the_previous_day_start;

    /**
     * 商品前1天销售金额结束值(double型)
     */
    private BigDecimal payment_the_previous_day_end;

    /**
     * 商品前3天销售件数起始值(int 型)
     */
    private Integer sales_three_day1_start;

    /**
     * 商品前3天销售件数结束值(int 型)
     */
    private Integer sales_three_day1_end;

    /**
     * 商品前3天销售金额起始值(double型)
     */
    private BigDecimal payment_three_day1_start;

    /**
     * 商品前3天销售金额结束值(double型)
     */
    private BigDecimal payment_three_day1_end;

    /**
     * 商品前3天销售增幅起始值(int 型)
     */
    private Integer sales_three_day_growth_start;

    /**
     * 商品前3天销售增幅结束值(int 型)
     */
    private Integer sales_three_day_growth_end;

    /**
     * 一级类目id(string型)
     */
    private String p_l1_id;

    /**
     * 子类类目id(string型)
     */
    private String sub_cate_id;

    /**
     * 商品上架时间起始值(string型,格式:年-月-日)
     */
    private Long gen_time_start;

    /**
     * 商品上架时间结束值(string型,格式:年-月-日)
     */
    private Long gen_time_end;

    /**
     * 商品价格起始值(double型)
     */
    private BigDecimal price_start;

    /**
     * 商品价格结束值(double型)
     */
    private BigDecimal price_end;

    /**
     * 收藏数起始值(int 型)
     */
    private Integer watchers_start;

    /**
     * 收藏数结束值(int 型)
     */
    private Integer watchers_end;

    /**
     * 商品前1天销售增幅起始值(int 型)
     */
    private Integer sold_the_previous_growth_start;

    /**
     * 商品前1天销售增幅结束值(int 型)
     */
    private Integer sold_the_previous_growth_end;

    /**
     * 商品总浏览数起始值(int 型)
     */
    private Integer visit_start;

    /**
     * 商品总浏览数结束值(int 型)
     */
    private Integer visit_end;

    /**
     * 前三天是否连续出单
     * (0: 否   1: 是   其它:全部)
     */
    private Integer sales_three_day_flag;

    /**
     * 商品前7天销售件数起始值(int 型)
     */
    private Integer sales_week1_start;

    /**
     * 商品前7天销售件数结束值(int 型)
     */
    private Integer sales_week1_end;

    /**
     * 商品前7天销售增幅起始值(int 型)
     */
    private Integer sales_week_growth_start;

    /**
     * 商品前7天销售增幅结束值(int 型)
     */
    private Integer sales_week_growth_end;

    /**
     * 商品Popular Item标识(int 型)
     * 默认:全部    1:有标识
     */
    private Integer popular_status;

    /**
     * 商品上架地区(string型)
     * (多个以英文逗号分隔)
     */
    private List<String> marketplaces;

    /**
     * 商品最新抓取时间起始值(string型,格式:年-月-日 时:分:秒)
     */
    private Long last_modi_time_start;

    /**
     * 商品最新抓取时间结束值(string型,格式:年-月-日 时:分:秒)
     */
    private Long last_modi_time_end;

    /**
     * 商品品牌
     */
    private String brand;

    /**
     * 排序方式:
     * price(商品价格)
     * sold(商品总销售件数)
     * sold_the_previous_day(商品前1天销售件数)
     * payment_the_previous_day(商品前1天销售金额)
     * sales_three_day1(商品前3天销售件数)
     * payment_three_day1(商品前3天销售金额)
     * sales_three_day_growth(商品前3天销售增幅)
     * gen_time(商品上架时间)
     * watchers(收藏数)
     * sold_the_previous_growth(商品前1天销售增幅)
     * visit(商品总浏览数)
     * sales_week1(商品前7天销售件数)
     * sales_week_growth(商品前7天销售增幅)
     */
    private HaiYingEbayProductListOrderBy order_by;

    /**
     * 排序类型:
     * ASC 升序
     * DESC 降序
     */
    private HaiYingOrderByType order_by_type;

    /**
     * 每一页的商品数(默认海鹰设置)(int 型)
     * 数值范围[1-1000]
     */
    private PageCondition pageCondition;

}
