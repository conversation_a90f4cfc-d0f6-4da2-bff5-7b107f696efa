package com.newnary.gsp.center.tpsi.infra.rpc;


import com.newnary.api.base.common.PageCondition;
import com.newnary.api.base.common.PageList;
import com.newnary.gsp.center.wms.api.stock.feign.WmsBatchStockFeignApi;
import com.newnary.gsp.center.wms.api.stock.request.BatchBinLocationStockPageQueryReq;
import com.newnary.gsp.center.wms.api.stock.response.BatchStockRes;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;

@Component
public class WmsBatchStockRpc {

    @Resource
    private WmsBatchStockFeignApi wmsBatchStockFeignApi;


    public PageList<BatchStockRes> findStockBySupplierSkuId(String supplierSkuId) {
        BatchBinLocationStockPageQueryReq req = new BatchBinLocationStockPageQueryReq();
        req.setSkus(Collections.singletonList(supplierSkuId));
        req.setPageCondition(new PageCondition(1,1));
        return wmsBatchStockFeignApi.queryPageByBatchBLStock(req).mustSuccessOrThrowOriginal();
    }

    public PageList<BatchStockRes> queryPageByBatchBLStock(BatchBinLocationStockPageQueryReq req) {
        return wmsBatchStockFeignApi.queryPageByBatchBLStock(req).mustSuccessOrThrowOriginal();
    }
}
