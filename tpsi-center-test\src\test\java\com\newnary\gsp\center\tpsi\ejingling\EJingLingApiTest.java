package com.newnary.gsp.center.tpsi.ejingling;

import com.alibaba.fastjson.JSON;
import com.jayway.jsonpath.internal.function.numeric.Max;
import com.jayway.jsonpath.internal.function.numeric.Min;
import com.newnary.gsp.center.tpsi.app.job.EJingLingJobManager;
import com.newnary.gsp.center.tpsi.infra.client.ejingling.utils.DateUtils;
import com.newnary.gsp.center.tpsi.infra.client.ejingling.valobj.request.*;
import com.newnary.gsp.center.tpsi.infra.client.ejingling.valobj.response.*;
import com.newnary.gsp.center.tpsi.service.ejingling.IEJingLingApiSev;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.test.starter.BaseTestInjectTenant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.*;

import static java.util.stream.Collectors.groupingBy;

public class EJingLingApiTest extends BaseTestInjectTenant {
    @Override
    protected String tenantId() {
        return "TENANT3924117688555169382400";
    }

    @Resource
    IEJingLingApiSev eJingLingApiSevImpl;

    @Resource
    EJingLingJobManager eJingLingJobManager;

    @Test
    public void testFullGoodsList() {
        EJingLingFullGoodsListReq req = new EJingLingFullGoodsListReq();
        req.setCreatedTime("2022-01-01 00:00:00");

        EJingLingGoodsListResponse response = new EJingLingGoodsListResponse();
        response.setOuterGoodsVoList(new ArrayList<>());
        int page = 0;
        int count = 0;
        String lastPage = "0";
        do {
            req.setPage(page++);
            req.setSize(500);
            response = eJingLingApiSevImpl.fullGoodsList("TESTEJJINGLING0001", req);
            if (null != response && CollectionUtils.isNotEmpty(response.getOuterGoodsVoList())) {
                lastPage = response.getLastPage();
                count += response.getOuterGoodsVoList().stream().distinct().count();
                long upload = response.getOuterGoodsVoList().stream().filter(vo -> vo.getStatus() == 3).count();
                long download = response.getOuterGoodsVoList().stream().filter(vo -> vo.getStatus() == 2).count();
                //String maxCreateTime = response.getOuterGoodsVoList().stream().max(Comparator.comparing(EJingLingGoodsListResponse.OuterGoodsVo::getCreateTime)).get().getCreateTime();
                String maxCreateTime = response.getOuterGoodsVoList().stream().max(Comparator.comparingLong(vo -> DateUtils.DateString2TimeStamp(vo.getCreateTime()))).get().getCreateTime();
                /*
                  Stream<UserEntity> stream = userEntities.stream();
                  Optional<UserEntity> max = stream.min((o1, o2) -> o1.getAge() - o2.getAge());
                  System.out.println(max.get());*/
                //String minCreateTime = response.getOuterGoodsVoList().stream().min(Comparator.comparing(EJingLingGoodsListResponse.OuterGoodsVo::getCreateTime)).get().getCreateTime();
                String minCreateTime = response.getOuterGoodsVoList().stream().min(Comparator.comparingLong(vo -> DateUtils.DateString2TimeStamp(vo.getCreateTime()))).get().getCreateTime();
                System.out.println("当前页数: ".concat(String.valueOf(page)).concat(" 当前页商品数量: ".concat(String.valueOf(response.getOuterGoodsVoList().size()))).concat(" 上架数量: ".concat(String.valueOf(upload))).concat(" 下架数量: ".concat(String.valueOf(download))).concat(" 当前页时间范围: ".concat(minCreateTime).concat(" - ").concat(maxCreateTime)));
            }
        }while (ObjectUtils.isNotEmpty(response) && "0".equals(lastPage));
        System.out.println("总page: "+page+" 总count: "+count);
    }

    @Test
    public void testShopFullGoodsList() {
        EJingLingShopFullGoodsListReq req = new EJingLingShopFullGoodsListReq();
        req.setCreatedTime("2023-01-01 00:00:00");
        req.setPage(1);
        req.setSize(20);
        EJingLingGoodsListResponse response = eJingLingApiSevImpl.shopFullGoodsList("TESTEJJINGLING0001", req);
    }

    @Test
    public void tesIncreGoodsList() {
        EJingLingIncreGoodsListReq req = new EJingLingIncreGoodsListReq();
        req.setPage(1);
        req.setSize(20);
        req.setStartLastUpdatedTime(DateUtils.Date2FormatString("yyyy-MM-dd HH:mm:ss",new Date()));
        req.setEndLastUpdatedTime(DateUtils.Date2FormatString("yyyy-MM-dd HH:mm:ss",new Date()));
        eJingLingApiSevImpl.increGoodsList("TESTEJJINGLING0001", req);
    }

    @Test
    public void testShopIncreGoodsList() {
        EJingLingShopIncreGoodsListReq req = new EJingLingShopIncreGoodsListReq();
        req.setPage(1);
        req.setSize(20);
        req.setStartLastUpdatedTime(DateUtils.Date2FormatString("yyyy-MM-dd HH:mm:ss",new Date()));
        req.setEndLastUpdatedTime(DateUtils.Date2FormatString("yyyy-MM-dd HH:mm:ss",new Date()));
        eJingLingApiSevImpl.shopIncreGoodsList("TESTEJJINGLING0001", req);
    }

    @Test
    public void testCreateOrder() {
        EJingLingCreateOrderReq req = new EJingLingCreateOrderReq();
        req.setCourierId("1");
        req.setOuterOrderNo("TS124690933353680896");
        req.setOuterOrderId("124690933353680896");
        req.setCourierId("1");
        req.setGoodsAmount(400.00);
        req.setPostAmount(16.00);
        EJingLingCreateOrderReq.SkuParam skuParam = new EJingLingCreateOrderReq.SkuParam();
        skuParam.setGoodsSkuId(19200281L);
        skuParam.setSkuNum(10);
        List<EJingLingCreateOrderReq.SkuParam> skuParams = new ArrayList<>();
        skuParams.add(skuParam);
        req.setSkuParams(skuParams);
        EJingLingCreateOrderReq.ReceiveInfo receiveInfo = new EJingLingCreateOrderReq.ReceiveInfo();
        receiveInfo.setProvince("广西");
        receiveInfo.setCity("柳州市");
        receiveInfo.setDistrict("柳南区");
        receiveInfo.setReceiverAddress("城中街道");
        receiveInfo.setReceiverMobile("15219867089");
        receiveInfo.setReceiverName("lisi");
        req.setReceiveInfo(receiveInfo);

        List<EJingLingCreateOrderResponse> result = eJingLingApiSevImpl.createOrder("TESTEJJINGLING0001", req);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testGetGoodsByGoodsId() {
        List<EJingLingGoodsListResponse.OuterGoodsVo> goods = eJingLingApiSevImpl.getGoodsByGoodsId("TESTEJJINGLING0001", "2104967");
        System.out.println(JSON.toJSONString(goods));
    }

    @Test
    public void testOrderDeliver() {
        EJingLingOrderDeliverReq req = new EJingLingOrderDeliverReq();
        req.setOrderNo("2098719");
        req.setCourierName("顺丰");
        req.setCourierOrderNo("3");
        req.setType(1);
        eJingLingApiSevImpl.orderDeliver("TESTEJJINGLING0001", req);
    }

    @Test
    public void testGetCourier() {
        EJingLingGetCourierReq eJingLingGetCourierReq = new EJingLingGetCourierReq();
        List<EJingLingGetCourierResponse> list = eJingLingApiSevImpl.getCourier("TESTEJJINGLING0001", eJingLingGetCourierReq);
        System.out.println(JSON.toJSONString(list));
    }

    @Test
    public void testGetLogisticsFirm() {
        List<EJingLingLogisticsFirmResponse> list = eJingLingApiSevImpl.getLogisticsFirm("TESTEJJINGLING0001");
        System.out.println(JSON.toJSONString(list));
    }

    @Test
    public void testCreateProduct() {
        ReturnT<String> returnT = eJingLingJobManager.createEJingLingProduct("{\"thirdPartySystemId\":\"TESTEJJINGLING0001\",\"time\":\"1440\"}");
    }

    @Test
    public void testUpdateStock() {
        ReturnT<String> returnT = eJingLingJobManager.updateEJingLingProductStock("{\"thirdPartySystemId\":\"TESTEJJINGLING0001\"}");
    }

    @Test
    public void testUpdatePrice() {
        ReturnT<String> returnT = eJingLingJobManager.updateEJINGLINGProductPrice("{\"thirdPartySystemId\":\"TESTEJJINGLING0001\"}");
    }

    @Test
    public void testGetOrderByOrderNo() {
        List<EJingLingGetOrderResponse> list = eJingLingApiSevImpl.getOrderByOrderNo("TESTEJJINGLING0001", "2098705");
        System.out.println(JSON.toJSONString(list));

    }

}
