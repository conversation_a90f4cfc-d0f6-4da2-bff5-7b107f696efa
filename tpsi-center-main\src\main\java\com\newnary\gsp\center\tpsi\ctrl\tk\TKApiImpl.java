package com.newnary.gsp.center.tpsi.ctrl.tk;

import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.common.PageList;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.logistics.api.delivery.dto.TrackInfoDTO;
import com.newnary.gsp.center.logistics.api.delivery.request.TransportOrderUpdateLabelCommand;
import com.newnary.gsp.center.logistics.api.delivery.request.TransportOrderUpdateTrackCommand;
import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderDetailInfo;
import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderDetailItemInfo;
import com.newnary.gsp.center.logistics.api.delivery.response.TransportOrderPackageInfo;
import com.newnary.gsp.center.product.api.product.request.ChannelSaleItemPageQueryDetailCommand;
import com.newnary.gsp.center.product.api.product.response.ChannelSaleItemDetailInfo;
import com.newnary.gsp.center.product.api.product.response.SupplierSpuBaseInfo;
import com.newnary.gsp.center.tpsi.infra.client.tk.TKClient;
import com.newnary.gsp.center.tpsi.infra.client.tk.converter.TKConverter;
import com.newnary.gsp.center.tpsi.infra.client.tk.dto.*;
import com.newnary.gsp.center.tpsi.infra.client.tk.dto.vo.TKOrderExtInfo;
import com.newnary.gsp.center.tpsi.infra.client.tk.dto.vo.TrackInfo;
import com.newnary.gsp.center.tpsi.infra.client.tk.dto.vo.TrackInfoList;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingInfo;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingType;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.ThirdPartyMappingManager;
import com.newnary.gsp.center.tpsi.infra.rpc.SaleItemRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.TradeOrderRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.TransportOrderRpc;
import com.newnary.gsp.center.tpsi.service.impl.SystemClientSve;
import com.newnary.gsp.center.trade.api.order.request.command.OrderCancelReq;
import com.newnary.gsp.center.trade.api.order.request.command.OrderCreateReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j

public class TKApiImpl extends SystemClientSve {
    private static final String TARGET_SYSTEM_PROVIDER = "GSP";
    @Resource
    private TradeOrderRpc tradeOrderRpc;

    @Resource
    private SaleItemRpc saleItemRpc;

    @Resource
    private ThirdPartyMappingManager thirdPartyMappingManager;

    @Resource
    private TransportOrderRpc transportOrderRpc;


    public void doFetchOrder(TKFetchOrderReq req, String thirdPartySystemId) {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);

        List<JSONObject> orders = new ArrayList<>();
        if (req.getOrder_status() != null) {
            orders.addAll(getOrders(req, thirdPartySystem));
        } else {
            Integer[] states = {111,140};
            for (Integer state : states) {
                req.setOrder_status(state);
                orders.addAll(getOrders(req, thirdPartySystem));
            }
        }

        if (CollectionUtils.isNotEmpty(orders)) {
            orders.forEach(item -> {
                try {
                    OrderCreateReq createReq = TKConverter.convert2ErpOrderCreateReq(item, thirdPartySystem.getBizId());
                    String orderId = tradeOrderRpc.syncOrder(createReq);
                    if (StringUtils.isBlank(orderId)) {
                        return;
                    }
                    // 存 todo linjiamei-tiktok check 保存信息是否完成，可供后续使用
                    TKOrderExtInfo tkOrderExtInfo = TKConverter.buildOrderExtInfo(item);
                    thirdPartyMappingManager.insertOrUpdate("GSP", thirdPartySystem.getBizId(), orderId, createReq.getChannelOrderNo(), ThirdPartyMappingType.ORDER_ID.name(), tkOrderExtInfo);

                } catch (Exception e) {
                    log.error("tiktok 同步单个订单异常", e);
                }

                thirdPartyMappingManager.tryUpdateByTarget(item.getString("order_id"), thirdPartySystem.getBizId(), "GSP", ThirdPartyMappingType.ORDER_ID, TKConverter.buildOrderExtInfo(item));
            });
        }
    }

    public void refreshToken(String thirdPartySystemId) throws IOException{
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        TKApiParam tkApiParam = JSONObject.parseObject(thirdPartySystem.getParams(), TKApiParam.class);
        TKClient.refreshToken(tkApiParam, thirdPartySystemId);
    }

    public void syncProducts(ChannelSaleItemPageQueryDetailCommand req, String thirdPartySystemId) {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);

        PageList<ChannelSaleItemDetailInfo> channelSaleItemDetailInfoPageList = saleItemRpc.pageQueryDetail(req);
        List<ChannelSaleItemDetailInfo> detailInfos = channelSaleItemDetailInfoPageList.getItems();

        while (detailInfos.size() > 0) {
            Map<String, String> productIdMapping = thirdPartyMappingManager.batchGetIdMappingBySource("GSP", thirdPartySystem.getBizId(), getSpuIds(detailInfos), ThirdPartyMappingType.PRODUCT_ID);
            //  todo linjiamei-tiktok check 平台共用还是店铺私有
            Map<String, ThirdPartyMappingInfo> categoryIdMapping = thirdPartyMappingManager.batchGetIdMappingInfoBySource("GSP", "TIKTOK", detailInfos.stream().map(item -> item.getSkuDetailInfo().getSpuBaseInfo().getCategoryId()).collect(Collectors.toList()), ThirdPartyMappingType.CATEGORY);
            Map<String, String> brandIdMapping = thirdPartyMappingManager.batchGetIdMappingBySource("GSP", "TIKTOK", getBrandIds(detailInfos), ThirdPartyMappingType.BRAND);

            Set<String> saleItemIds = getRandomSaleItemIdWithSameSpuId(detailInfos);
            saleItemIds.forEach(saleItemId -> syncProduct(thirdPartySystem, productIdMapping, categoryIdMapping, brandIdMapping, saleItemId));

            int pageNum = req.getPageCondition().getPageNum() + 1;
            req.pageCondition.setPageNum(pageNum);
            channelSaleItemDetailInfoPageList = saleItemRpc.pageQueryDetail(req);
            detailInfos = channelSaleItemDetailInfoPageList.getItems();
        }
    }


    public void notifyOrderSplit(List<DeliveryOrderDetailInfo> packageList, String thirdPartyBizId) {
        if (CollectionUtils.isEmpty(packageList)) {
            return;
        }
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartyBizId);
        String tradeOrderId = packageList.get(0).getTradeOrderId();
        ThirdPartyMappingInfo mappingInfo = thirdPartyMappingManager.getInfoBySource(tradeOrderId, "GSP", thirdPartySystem.getBizId(), ThirdPartyMappingType.ORDER_ID);
        if (ObjectUtils.isEmpty(mappingInfo) || StringUtils.isBlank(mappingInfo.getExtendBizInfo())) {
            log.warn("tiktok 通知订单拆分，缺失必要信息，不处理, tradeOrderId={}", tradeOrderId);
            return;
        }

        Map<String, String> deliverOrderAndPackageIdMapping = new HashMap<>();
        TKOrderExtInfo extInfo = JSONObject.parseObject(mappingInfo.getExtendBizInfo(), TKOrderExtInfo.class);

        List<TKOrderExtInfo.OrderLine> orderLines = extInfo.getOrder_line_list();
        try {
            TKClient.verifyOrderSplit(Collections.singletonList(mappingInfo.getTargetId()), thirdPartySystem.getParams(), thirdPartySystem.getBizId());
            // todo linjiamei-tiktok orderLine 和sku先按1:1 比例进行匹配
            List<TKOrderExtInfo.OrderLine> orderLineList = orderLines;
            Map<String, List<String>> orderLineGroup = new HashMap<>();

            // index 作为索引，抖音接口字段不能传字符串限制
            for (int i = 0; i < packageList.size(); i++) {
                DeliveryOrderDetailInfo item = packageList.get(i);
                List<DeliveryOrderDetailItemInfo> items = item.getItems();
                List<String> saleItemIds = items.stream().map(DeliveryOrderDetailItemInfo::getSaleItemCode).collect(Collectors.toList());
                List<String> orderLineIds = orderLineList.stream().filter(line -> saleItemIds.contains(line.getSeller_sku())).map(TKOrderExtInfo.OrderLine::getOrder_line_id).collect(Collectors.toList());
                orderLineGroup.put(String.valueOf(i), orderLineIds);
            }

            deliverOrderAndPackageIdMapping = TKClient.confirmOrderSplit(orderLineGroup, extInfo.getOrder_id(), thirdPartySystem.getParams(), thirdPartySystem.getBizId());

        } catch (Exception e) {
            log.error("通知tiktok 包裹已拆分异常", e);
        }

        if (deliverOrderAndPackageIdMapping.isEmpty() && CollectionUtils.isNotEmpty(orderLines)) {
            for (int i = 0; i < packageList.size(); i++) {
                DeliveryOrderDetailInfo deliveryOrderDetailInfo = packageList.get(i);
                List<String> saleItemIds = deliveryOrderDetailInfo.getItems().stream().map(DeliveryOrderDetailItemInfo::getSaleItemCode).collect(Collectors.toList());
                TKOrderExtInfo.OrderLine orderLine = orderLines.stream().filter(item -> saleItemIds.contains(item.getSeller_sku()) && StringUtils.isNotBlank(item.getPackage_id())).findAny().orElse(null);
                if (orderLine != null) {
                    deliverOrderAndPackageIdMapping.put(String.valueOf(i), orderLine.getPackage_id());
                }
            }
        }

        Map<String, String> finalDeliverOrderAndPackageIdMapping = deliverOrderAndPackageIdMapping;
        List<TKOrderExtInfo.PackageInfo> packageInfos = deliverOrderAndPackageIdMapping.keySet()
                .stream()
                .map(key -> {
                    DeliveryOrderDetailInfo deliveryOrderDetailInfo = packageList.get(Integer.parseInt(key));

                    TKOrderExtInfo.PackageInfo packageInfo = new TKOrderExtInfo.PackageInfo();
                    packageInfo.setPre_split_pkg_id(deliveryOrderDetailInfo.getDeliveryOrderId());
                    packageInfo.setPackage_id(finalDeliverOrderAndPackageIdMapping.get(key));
                    return packageInfo;
                }).collect(Collectors.toList());


        // 尝试获取面单信息
        try {
            packageInfos.forEach(packageInfo -> {
                // todo linjiamei-tiktok 面单尺寸，大小
                String pdfUrl = TKClient.getPackageShippingPdf(
                        packageInfo.getPackage_id(),
                        null,
                        0,
                        thirdPartySystem.getParams(),
                        thirdPartySystem.getBizId()
                );
                if (StringUtils.isNotBlank(pdfUrl)) {
                    packageInfo.setLabel_url(pdfUrl);
                }
            });
        } catch (Exception e) {
            log.warn("tiktok 通知订单拆分结果后尝试获取面单号失败, tradeOrderId={}", tradeOrderId, e);
        }

        extInfo.setPackageInfos(packageInfos);

        thirdPartyMappingManager.insertOrUpdate(mappingInfo.getSourceBizId(), mappingInfo.getTargetBizId(), mappingInfo.getSourceId(), mappingInfo.getTargetId(), mappingInfo.getBizType(), extInfo);

    }


    private Set<String> getRandomSaleItemIdWithSameSpuId(List<ChannelSaleItemDetailInfo> detailInfos) {
        Map<String, List<ChannelSaleItemDetailInfo>> spuItemMapping = detailInfos.stream().collect(Collectors.groupingBy(item -> item.getSkuDetailInfo().getSpuBaseInfo().getSupplierSpuId()));
        Set<String> saleItemIds = new HashSet<>();
        for (String key : spuItemMapping.keySet()) {
            saleItemIds.add(spuItemMapping.get(key).get(0).getSaleItemInfo().getSaleItemId());
        }
        return saleItemIds;
    }


    private void syncProduct(ThirdPartySystem thirdPartySystem,
                             Map<String, String> productIdMapping,
                             Map<String, ThirdPartyMappingInfo> categoryIdMapping,
                             Map<String, String> brandIdMapping,
                             String saleItemId) {
        try {
            List<ChannelSaleItemDetailInfo> items = saleItemRpc.findItemsGroupBySpuId(thirdPartySystem.getBizId(), saleItemId);
            SupplierSpuBaseInfo spuBaseInfo = items.get(0).getSkuDetailInfo().getSpuBaseInfo();

            Map<String, String> imageIdMapping = batchUploadImage(spuBaseInfo, thirdPartySystem.getParams(), thirdPartySystem.getBizId());

            String productId = productIdMapping.get(spuBaseInfo.getSupplierSpuId());
            TKProductMainInfo tkProductMainInfo;
            if (StringUtils.isBlank(productId)) {
                TKProductCreateReq tkProductCreateReq = TKConverter.buildProduct(items, categoryIdMapping, brandIdMapping, imageIdMapping, thirdPartySystem.getParams());
                tkProductMainInfo = TKClient.createProduct(tkProductCreateReq, thirdPartySystem.getParams(), thirdPartySystem.getBizId());
            } else {
                TKProductUpdateReq updateReq = TKConverter.buildProduct(items, categoryIdMapping, brandIdMapping, imageIdMapping, thirdPartySystem.getParams());
                updateReq.setProduct_id(productId);
                tkProductMainInfo = TKClient.updateProduct(updateReq, thirdPartySystem.getParams(), thirdPartySystem.getBizId());
            }

            // 存tk商品ID || 以及sku和item对应关系
            thirdPartyMappingManager.insertOrUpdate("GSP", thirdPartySystem.getBizId(), spuBaseInfo.getSupplierSpuId(), tkProductMainInfo.product_id, ThirdPartyMappingType.PRODUCT_ID.name(), tkProductMainInfo);
        } catch (Exception e) {
            log.error("tiktok 商品同步失败, saleItemId={}", saleItemId, e);
        }

    }

    private Map<String, String> batchUploadImage(SupplierSpuBaseInfo spuBaseInfo, String params, String bizId) {

        List<String> detailImageUrls = spuBaseInfo.getDetailImageUrls();
        List<String> mainImageUrls = spuBaseInfo.getMainImageUrls();
        List<ImageVO> uploadImages = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(detailImageUrls)) {
            for (String detailImageUrl : detailImageUrls) {
                uploadImages.add(new ImageVO(2, detailImageUrl));
            }
        }

        if (CollectionUtils.isNotEmpty(mainImageUrls)) {
            for (String mainImageUrl : mainImageUrls) {
                uploadImages.add(new ImageVO(2, mainImageUrl));
            }
        }

        return TKClient.batchUploadImages(uploadImages, params, bizId);
    }


    private List<String> getSpuIds(List<ChannelSaleItemDetailInfo> detailInfos) {
        List<String> spuIds = new ArrayList<>();
        for (ChannelSaleItemDetailInfo detailInfo : detailInfos) {
            spuIds.add(detailInfo.getSkuDetailInfo().getSpuBaseInfo().getSupplierSpuId());
        }
        return spuIds.stream().distinct().collect(Collectors.toList());
    }

    private List<String> getCategoryIds(List<ChannelSaleItemDetailInfo> detailInfos) {
        List<String> categoryIds = new ArrayList<>();
        for (ChannelSaleItemDetailInfo detailInfo : detailInfos) {
            categoryIds.add(detailInfo.getSkuDetailInfo().getSpuBaseInfo().getCategoryId());
        }
        return categoryIds.stream().distinct().collect(Collectors.toList());
    }


    private List<String> getBrandIds(List<ChannelSaleItemDetailInfo> detailInfos) {
        List<String> brandIds = new ArrayList<>();
        for (ChannelSaleItemDetailInfo detailInfo : detailInfos) {
            brandIds.add(detailInfo.getSkuDetailInfo().getSpuBaseInfo().getBrandInfo().getBrandId());
        }
        return brandIds.stream().distinct().collect(Collectors.toList());
    }

    public void shippingPackage(String tradeOrderId, String transportOrderId, String deliveryOrderId, String channelId, TransportOrderPackageInfo transportPackageInfo, List<DeliveryOrderDetailItemInfo> items) {

        ThirdPartySystem thirdPartySystem = loadSystem(channelId);
        ThirdPartyMappingInfo mappingInfo = thirdPartyMappingManager.getInfoBySource(tradeOrderId, "GSP", thirdPartySystem.getBizId(), ThirdPartyMappingType.ORDER_ID);
        String extendBizInfo = mappingInfo.getExtendBizInfo();
        TKOrderExtInfo info = JSONObject.parseObject(extendBizInfo, TKOrderExtInfo.class);


        // 预先通知渠道已发货，实际没发货
        notifyOrderShippedAndReturnGSPLabel(tradeOrderId, deliveryOrderId, transportPackageInfo, thirdPartySystem.getBizId(), items);


//        // 回传面单信息和运单号
//        // todo linjiamei-tiktok 面单尺寸、大小获取,出问题再说
//        String labelUrl = TKClient.getOrderShippingPdf(mappingInfo.getTargetId(),
//                null,
//                0,
//                thirdPartySystem.getParams(),
//                thirdPartySystem.getBizId());
//        if (StringUtils.isNotBlank(labelUrl)) {
//            TransportOrderUpdateTrackCommand updateTrackCommand = new TransportOrderUpdateTrackCommand();
//            updateTrackCommand.setTransportOrderId(transportOrderId);
//
//            List<String> saleItemIds = items.stream().map(DeliveryOrderDetailItemInfo::getSaleItemCode).collect(Collectors.toList());
//            List<TKOrderExtInfo.OrderLine> orderLines = info.getOrder_line_list().stream().filter(item -> saleItemIds.contains(item.getSeller_sku())).collect(Collectors.toList());
//            TrackInfoDTO trackInfoDTO = new TrackInfoDTO(orderLines.get(0).getShipping_provider_name(), orderLines.get(0).getTracking_number());
//            updateTrackCommand.setTrackInfos(Collections.singletonList(trackInfoDTO));
//            transportOrderRpc.updateTrack(updateTrackCommand);
//        }

    }


    public String getLabel(String tradeOrderId, String deliveryOrderId, String transportOrderId, String channelId, List<String> saleItemIds) {
        ThirdPartySystem system = loadSystem(channelId);
        ThirdPartyMappingInfo mappingInfo = thirdPartyMappingManager.getInfoBySource(tradeOrderId, "GSP", channelId, ThirdPartyMappingType.ORDER_ID);
        String extendBizInfo = mappingInfo.getExtendBizInfo();
        if (StringUtils.isBlank(extendBizInfo)) {
            return null;
        }

        TKOrderExtInfo extInfo = JSONObject.parseObject(extendBizInfo, TKOrderExtInfo.class);
        // todo linjiamei-tiktok 面单尺寸、大小获取,出问题再说

        return TKClient.getOrderShippingPdf(mappingInfo.getTargetId(),
                null,
                0,
                system.getParams(),
                system.getBizId());
    }

    public PackageDetail getPackageDetail(String tradeOrderId, String deliveryOrderId, String channelId, List<String> packageSaleItemCodes) {
        ThirdPartySystem system = loadSystem(channelId);
        ThirdPartyMappingInfo mappingInfo = thirdPartyMappingManager.getInfoBySource(tradeOrderId, "GSP", channelId, ThirdPartyMappingType.ORDER_ID);
        if (null == mappingInfo) {
            return null;
        }
        String extendBizInfo = mappingInfo.getExtendBizInfo();
        if (StringUtils.isBlank(extendBizInfo)) {
            return null;
        }

        TKOrderExtInfo extInfo = JSONObject.parseObject(extendBizInfo, TKOrderExtInfo.class);
        List<TKOrderExtInfo.OrderLine> orderLines = extInfo.getOrder_line_list().stream().filter(item -> packageSaleItemCodes.contains(item.getSeller_sku())).collect(Collectors.toList());

        JSONObject packageDetail = TKClient.getPackageDetail(orderLines.get(0).getPackage_id(), system.getParams(), system.getBizId());
        if (null != packageDetail) {
            return JSONObject.parseObject(packageDetail.toJSONString(), PackageDetail.class);
        }
        return null;
    }

    //包裹物流轨迹
    public List<TrackInfo> getPackageShippingInfo(String tradeOrderId, String deliveryOrderId, String channelId, List<String> packageSaleItemCodes) {
        ThirdPartySystem system = loadSystem(channelId);
        ThirdPartyMappingInfo mappingInfo = thirdPartyMappingManager.getInfoBySource(tradeOrderId, "GSP", channelId, ThirdPartyMappingType.ORDER_ID);
        if (null == mappingInfo) {
            return new ArrayList<>();
        }
        if (StringUtils.isBlank(mappingInfo.getExtendBizInfo())) {
            return new ArrayList<>();
        }
        TKOrderExtInfo extInfo = JSONObject.parseObject(mappingInfo.getExtendBizInfo(), TKOrderExtInfo.class);
        List<TKOrderExtInfo.OrderLine> orderLines = extInfo.getOrder_line_list().stream().filter(item -> packageSaleItemCodes.contains(item.getSeller_sku())).collect(Collectors.toList());

        return TKClient.getPackageShippingInfo(orderLines.get(0).getPackage_id(), system.getParams(), system.getBizId());
    }

    //订单物流轨迹
    public TrackInfoList getOrderShippingInfo(String tradeOrderId, String deliveryOrderId, String channelId, List<String> packageSaleItemCodes) {
        ThirdPartySystem system = loadSystem(channelId);
        ThirdPartyMappingInfo mappingInfo = thirdPartyMappingManager.getInfoBySource(tradeOrderId, "GSP", channelId, ThirdPartyMappingType.ORDER_ID);
        if (null == mappingInfo) {
            return new TrackInfoList();
        }
        if (StringUtils.isBlank(mappingInfo.getExtendBizInfo())) {
            return new TrackInfoList();
        }
        TKOrderExtInfo extInfo = JSONObject.parseObject(mappingInfo.getExtendBizInfo(), TKOrderExtInfo.class);

        return TKClient.getOrderShippingInfo(extInfo.getOrder_id(), system.getParams(), system.getBizId());
    }

    public void notifyOrderShippedAndReturnGSPLabel(String tradeOrderId, String deliveryOrderId, TransportOrderPackageInfo transportPackageInfo, String thirdPartySystemId, List<DeliveryOrderDetailItemInfo> items) {


        ThirdPartySystem system = loadSystem(thirdPartySystemId);

        ThirdPartyMappingInfo mappingInfo = thirdPartyMappingManager.getInfoBySource(tradeOrderId, "GSP", system.getBizId(), ThirdPartyMappingType.ORDER_ID);
        // fixme: 直接从数据库中获取老数据
        String extendBizInfo = mappingInfo.getExtendBizInfo();
        if (StringUtils.isBlank(extendBizInfo)) {
            return;
        }

        List<String> saleItemIds = items.stream().map(DeliveryOrderDetailItemInfo::getSaleItemCode).collect(Collectors.toList());
        TKOrderExtInfo extInfo = JSONObject.parseObject(extendBizInfo, TKOrderExtInfo.class);
        List<TKOrderExtInfo.OrderLine> orderLines = extInfo.getOrder_line_list().stream()
                .filter(item -> saleItemIds.contains(item.getSeller_sku()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(orderLines)) {
            return;
        }


        ShippingPackageReq req = new ShippingPackageReq();
        String packageId = orderLines.get(0).getPackage_id();
        req.setPackage_id(packageId);
        req.setPick_up_type(2);

        ShippingPackageReq.SelfShipment selfShipment = new ShippingPackageReq.SelfShipment();

        // todo linjiamei-tiktok 发货商ID 获取,出问题再说
        selfShipment.setShipping_provider_id(orderLines.get(0).getShipping_provider_id());
        selfShipment.setTracking_number(orderLines.get(0).getTracking_number());
        req.setSelf_shipment(selfShipment);

        boolean shippingResult = TKClient.shippingPackage(req, system.getParams(), system.getBizId());
        if (shippingResult) {
            String labelUrl = TKClient.getOrderShippingPdf(mappingInfo.getTargetId(), null, 0, system.getParams(), system.getBizId());
            if (StringUtils.isNotBlank(labelUrl)) {

                TransportOrderUpdateTrackCommand updateTrackCommand = new TransportOrderUpdateTrackCommand();
                updateTrackCommand.setTransportOrderPackageId(transportPackageInfo.getTransportOrderPackageId());
                TrackInfoDTO trackInfoDTO = new TrackInfoDTO(orderLines.get(0).getShipping_provider_name(), orderLines.get(0).getTracking_number());
                updateTrackCommand.setTrackInfo(trackInfoDTO);
                transportOrderRpc.updateTrack(updateTrackCommand);

                TransportOrderUpdateLabelCommand updateLabelCommand = new TransportOrderUpdateLabelCommand();
                updateLabelCommand.setLabelUrl(labelUrl);
                updateLabelCommand.setTransportOrderPackageId(transportPackageInfo.getTransportOrderPackageId());
                transportOrderRpc.updateLabel(updateLabelCommand);

                List<TKOrderExtInfo.PackageInfo> packageInfos = extInfo.getPackageInfos();
                if (CollectionUtils.isEmpty(packageInfos)) {
                    packageInfos = new ArrayList<>();
                }
                Optional<TKOrderExtInfo.PackageInfo> existPackageInfo = packageInfos.stream().filter(item -> StringUtils.equals(item.getPre_split_pkg_id(), deliveryOrderId))
                        .peek(item -> item.setLabel_url(labelUrl))
                        .findAny();
                if (!existPackageInfo.isPresent()) {
                    TKOrderExtInfo.PackageInfo packageInfo = new TKOrderExtInfo.PackageInfo();
                    packageInfo.setPackage_id(packageId);
                    packageInfo.setPre_split_pkg_id(deliveryOrderId);
                    packageInfo.setLabel_url(labelUrl);
                    packageInfos.add(packageInfo);
                }
                extInfo.setPackageInfos(packageInfos);
                thirdPartyMappingManager.insertOrUpdate("GSP", system.getBizId(), tradeOrderId, mappingInfo.getTargetId(), ThirdPartyMappingType.ORDER_ID.name(), extInfo);
            }
        }
    }

    private String convertCarrierCode(String carrierCode, String params) {
        TKApiParam apiParam = JSONObject.parseObject(params, TKApiParam.class);
        if (apiParam.getCarrierCodeMapping() == null || StringUtils.isBlank(apiParam.getCarrierCodeMapping().getString(carrierCode))) {
            throw new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "tiktok 渠道物流编码未映射, carrierCode={}");
        }
        return apiParam.getCarrierCodeMapping().getString(carrierCode);
    }

    public void doFetchSaleAfterOrder(TKFetchOrderReq req, String bizId) {
        ThirdPartySystem system = loadSystem(bizId);
        List<JSONObject> orders = getOrders(req, system);
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        for (JSONObject order : orders) {
            cancelOrder(order, system);
        }
    }

    private void cancelOrder(JSONObject order, ThirdPartySystem system) {
        try {
            ThirdPartyMappingInfo mappingInfo = thirdPartyMappingManager.getNewestInfoByTarget(system.getBizId(), TARGET_SYSTEM_PROVIDER, order.getString("order_id"), ThirdPartyMappingType.ORDER_ID);
            if (null == mappingInfo) {
                throw new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "tiktok 取消订单号-未获取到GSP 订单号");
            }
            OrderCancelReq req = new OrderCancelReq();
            req.setOrderId(mappingInfo.getSourceId());
            req.setCancelType("SALES_CHANNEL_CANCEL");
            req.setCancelReason("自动取消");
            tradeOrderRpc.cancelOrder(req);
        } catch (Exception e) {
            log.error("tiktok -取消订单异常", e);
        }

    }


    private List<JSONObject> getOrders(TKFetchOrderReq req, ThirdPartySystem thirdPartySystem) {
        if (req.getUpdate_time_from() == null && req.getUpdate_time_to() == null) {
            req.update_time_from = (int) (System.currentTimeMillis() / 1000 - 48 * 60 * 60);
            req.update_time_to = (int) (System.currentTimeMillis() / 1000);
        }

        if (req.getPage_size() == null || req.getPage_size() > 50) {
            req.setPage_size(50);
        }
        return TKClient.getOrderList(req, thirdPartySystem.getParams(), thirdPartySystem.getBizId());
    }

    public void finishGspOrder(String tradeOrderId,
                               String deliveryOrderId,
                               TransportOrderPackageInfo transportPackageInfo,
                               String bizId,
                               List<DeliveryOrderDetailItemInfo> items,
                               String params, String channelCode) {
        ThirdPartyMappingInfo mappingInfo = thirdPartyMappingManager.getInfoBySource(tradeOrderId, "GSP", bizId, ThirdPartyMappingType.ORDER_ID);
        if (null == mappingInfo) {
            return;
        }
        if (StringUtils.isBlank(mappingInfo.getExtendBizInfo())) {
            return;
        }
        List<String> saleItemIds = items.stream().map(DeliveryOrderDetailItemInfo::getSaleItemCode).collect(Collectors.toList());
        TKOrderExtInfo extInfo = JSONObject.parseObject(mappingInfo.getExtendBizInfo(), TKOrderExtInfo.class);
        List<TKOrderExtInfo.OrderLine> orderLines = extInfo.getOrder_line_list().stream().filter(item -> saleItemIds.contains(item.getSeller_sku())).collect(Collectors.toList());
        JSONObject packageInfo = TKClient.getPackageDetail(orderLines.get(0).getPackage_id(), params, channelCode);
        if (packageInfo != null && packageInfo.getInteger("package_status") == 4) {
            transportOrderRpc.finishedByPackage(transportPackageInfo.getTransportOrderPackageId());
        }
    }

    public void fetchTrackInfo2GSP(String tradeOrderId, TransportOrderPackageInfo transportPackageInfo, String deliveryOrderId, String bizId, List<DeliveryOrderDetailItemInfo> items) {
        ThirdPartyMappingInfo mappingInfo = thirdPartyMappingManager.getInfoBySource(tradeOrderId, "GSP", bizId, ThirdPartyMappingType.ORDER_ID);
        if (mappingInfo == null || StringUtils.isBlank(mappingInfo.getExtendBizInfo())) {
            return;
        }
        TKOrderExtInfo extInfo = JSONObject.parseObject(mappingInfo.getExtendBizInfo(), TKOrderExtInfo.class);

        // 预先调用tiktok 发货接口, 实际没发货
        notifyOrderShippedAndReturnGSPLabel(tradeOrderId, deliveryOrderId, transportPackageInfo, bizId, items);

        // 回传面单信息和运单号
        // todo linjiamei-tiktok 面单尺寸、大小获取,出问题再说
        TransportOrderUpdateTrackCommand updateTrackCommand = new TransportOrderUpdateTrackCommand();
        updateTrackCommand.setTransportOrderPackageId(transportPackageInfo.getTransportOrderPackageId());

        List<String> saleItemIds = items.stream().map(DeliveryOrderDetailItemInfo::getSaleItemCode).collect(Collectors.toList());
        List<TKOrderExtInfo.OrderLine> orderLines = extInfo.getOrder_line_list().stream().filter(item -> saleItemIds.contains(item.getSeller_sku())).collect(Collectors.toList());
        TrackInfoDTO trackInfoDTO = new TrackInfoDTO(orderLines.get(0).getShipping_provider_name(), orderLines.get(0).getTracking_number());
        updateTrackCommand.setTrackInfo(trackInfoDTO);
        transportOrderRpc.updateTrack(updateTrackCommand);

    }

}
