package com.newnary.gsp.center.tpsi.ctrl.haiying;

import com.newnary.api.base.common.PageList;
import com.newnary.api.base.common.PageMeta;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingStation;
import com.newnary.gsp.center.tpsi.api.haiying.response.ebay.*;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.ebay.*;
import com.newnary.gsp.center.tpsi.infra.mapper.HaiYingEbayDataMapper;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/07/14 13:45
 */
public class HaiYingEbayResponse2DTOTranslator {

    public static PageList<HaiYingEbayProductListDTO> transEbayProductListList(HaiYingStation station, List<HaiYingEbayProductListResponse> response, PageMeta pageMeta) {
        PageList<HaiYingEbayProductListDTO> ret = new PageList<>();
        List<HaiYingEbayProductListDTO> list = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingEbayProductListDTO dto = HaiYingEbayDataMapper.INSTANCE.transEbayProductListDTO(resp);
            switch (station) {
                case EBAY_UK:
                    dto.setCurrency("GBP");
                    break;
                case EBAY_DE:
                    dto.setCurrency("EUR");
                    break;
                case EBAY_US:
                    dto.setCurrency("USD");
                    break;
                case EBAY_AU:
                    dto.setCurrency("AUD");
                    break;
                default:
                    break;
            }

            list.add(dto);
        });
        ret.setItems(list);
        ret.setPageMeta(pageMeta);
        return ret;
    }

    public static List<HaiYingEbayProductDetailInfoDTO> transEbayProductDetailInfoList(List<HaiYingEbayProductDetailInfoResponse> response) {
        List<HaiYingEbayProductDetailInfoDTO> ret = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingEbayProductDetailInfoDTO dto = HaiYingEbayDataMapper.INSTANCE.transEbayProductDetailDTO(resp);

            ret.add(dto);
        });
        return ret;
    }

    public static List<HaiYingEbayCategoryTreeDTO> transEbayCategoryTreeList(List<HaiYingEbayCategoryTreeResponse> response) {
        List<HaiYingEbayCategoryTreeDTO> ret = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingEbayCategoryTreeDTO dto = HaiYingEbayDataMapper.INSTANCE.transEbayCategoryTreeDTO(resp);

            ret.add(dto);
        });
        return ret;
    }

    public static List<HaiYingEbayTopCategoryInfoDTO> transEbayTopCategoryInfoList(List<HaiYingEbayTopCategoryInfoResponse> response) {
        List<HaiYingEbayTopCategoryInfoDTO> ret = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingEbayTopCategoryInfoDTO dto = HaiYingEbayDataMapper.INSTANCE.transEbayTopCategoryDTO(resp);

            ret.add(dto);
        });
        return ret;
    }

    public static PageList<HaiYingEbayCategoryDetailDTO> transEbayCategoryDetailList(List<HaiYingEbayCategoryDetailResponse> response, PageMeta pageMeta) {
        PageList<HaiYingEbayCategoryDetailDTO> ret = new PageList<>();
        List<HaiYingEbayCategoryDetailDTO> list = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingEbayCategoryDetailDTO dto = HaiYingEbayDataMapper.INSTANCE.transEbayCategoryDetailDTO(resp);

            list.add(dto);
        });
        ret.setItems(list);
        ret.setPageMeta(pageMeta);
        return ret;
    }

}
