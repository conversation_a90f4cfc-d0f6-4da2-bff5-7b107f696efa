package com.newnary.gsp.center.tpsi.api.common.enums;

/**
 * 三方对接事件状态
 */
public enum ThirdPartyEventState {

    SUCCESS("成功",1),FAIL("失败",0);

    private String name;
    private Integer value;

    ThirdPartyEventState(String name, Integer value){
        this.name = name;
        this.value = value;
    }

    public Integer getValue(){
        return this.value;
    }

    public static ThirdPartyEventState getByValue(Integer value) {

        switch (value) {
            case 0:
                return FAIL;
            case 1:
                return SUCCESS;
        }
        throw new IllegalArgumentException("Invalid enum value: " + value);
    }
}
