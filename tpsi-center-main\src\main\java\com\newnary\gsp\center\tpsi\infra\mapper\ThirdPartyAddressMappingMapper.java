package com.newnary.gsp.center.tpsi.infra.mapper;

import com.newnary.gsp.center.tpsi.infra.model.creator.ThirdPartyAddressMappingCreator;
import com.newnary.gsp.center.tpsi.infra.model.vo.SystemId;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyAddressMappingPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @Date 2021/12/14 14:55
 */
@Mapper
public interface ThirdPartyAddressMappingMapper {

    ThirdPartyAddressMappingMapper INSTANCE = Mappers.getMapper(ThirdPartyAddressMappingMapper.class);

    ThirdPartyAddressMappingCreator po2ModelCreator(ThirdPartyAddressMappingPO po);


    default SystemId transformSystemId(String systemId){
        return new SystemId(systemId);
    }
}
