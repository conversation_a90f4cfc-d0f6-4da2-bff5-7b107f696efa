package com.newnary.gsp.center.tpsi.infra.rpc;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.gsp.center.user.api.identity.feign.UserCoreFeignApi;
import com.newnary.gsp.center.user.api.identity.response.UserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class UserRpc {

    @Resource
    private UserCoreFeignApi userCoreFeignApi;

    public CommonResponse<UserDTO> getUser(String userId){
        return userCoreFeignApi.get(userId);
    }
}
