package com.newnary.gsp.center.tpsi.api.rongmao;

import com.newnary.api.base.common.CommonResponse;
import org.springframework.web.bind.annotation.RequestMapping;

@RequestMapping("tpsi-center/rongmao/operateThirdPartyProduct")
public interface ThirdPartyProductOperateApi {
    CommonResponse<String> create(String thirdPartySystemId, String productId);
    CommonResponse<String> delete(String thirdPartySystemId,String productId);
    CommonResponse<String> update(String thirdPartySystemId,String productId);
}
