package com.newnary.gsp.center.tpsi.ctrl.haiying;

import com.alibaba.fastjson.JSON;
import com.newnary.gsp.center.tpsi.api.haiying.request.shopee.*;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.shopee.*;
import com.newnary.gsp.center.tpsi.infra.mapper.HaiYingShopeeDataMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @Date 2021/12/14 14:52
 */
public class HaiYingShopeeCommand2RequestTranslator {

    public static HaiYingShopeeKeywordListRequest transShopeeKeywordList(HaiYingShopeeKeywordListCommand command) {
        HaiYingShopeeKeywordListRequest request = HaiYingShopeeDataMapper.INSTANCE.transShopeeKeywordListRequest(command);

        return request;
    }

    public static HaiYingShopeeKeywordInfoRequest transShopeeKeywordInfo(HaiYingShopeeKeywordInfoCommand command) {
        HaiYingShopeeKeywordInfoRequest request = HaiYingShopeeDataMapper.INSTANCE.transShopeeKeywordInfoRequest(command);

        return request;
    }

    public static HaiYingShopeeProductListRequest transShopeeProductList(HaiYingShopeeProductListCommand command) {
        HaiYingShopeeProductListRequest request = HaiYingShopeeDataMapper.INSTANCE.transShopeeProductListRequest(command);
        if (StringUtils.isNotEmpty(command.getP_l1_id()))
            request.setP_l1_id(command.getP_l1_id());
        if (StringUtils.isNotEmpty(command.getP_l1_id()) && StringUtils.isNotEmpty(command.getP_l2_id()))
            request.setP_l2_id(command.getP_l1_id().concat(command.getP_l2_id()));
        if (StringUtils.isNotEmpty(command.getP_l1_id()) && StringUtils.isNotEmpty(command.getP_l2_id()) && StringUtils.isNotEmpty(command.getP_l3_id()))
            request.setP_l3_id(command.getP_l1_id().concat(command.getP_l2_id()).concat(command.getP_l3_id()));

        return request;
    }

    public static HaiYingShopeeProductDetailInfoRequest transShopeeProductDetailInfo(HaiYingShopeeProductDetailInfoCommand command) {
        HaiYingShopeeProductDetailInfoRequest request = HaiYingShopeeDataMapper.INSTANCE.transShopeeProductDetailInfoRequest(command);
        if (CollectionUtils.isNotEmpty(command.getPid_and_shop_ids())) {
            request.setPid_and_shop_ids(JSON.toJSONString(command.getPid_and_shop_ids()));
        }
        return request;
    }

    public static HaiYingShopeeProductExtInfoRequest transShopeeProductExtInfo(HaiYingShopeeProductExtInfoCommand command) {
        HaiYingShopeeProductExtInfoRequest request = HaiYingShopeeDataMapper.INSTANCE.transShopeeProductExtInfoRequest(command);

        return request;
    }

    public static HaiYingShopeeProductHistoryInfoRequest transShopeeProductHistoryInfo(HaiYingShopeeProductHistoryInfoCommand command) {
        HaiYingShopeeProductHistoryInfoRequest request = HaiYingShopeeDataMapper.INSTANCE.transShopeeProductHistoryInfoRequest(command);

        return request;
    }

    public static HaiYingShopeeCategoryTreeRequest transShopeeCategoryTree(HaiYingShopeeCategoryTreeCommand command) {
        HaiYingShopeeCategoryTreeRequest request = HaiYingShopeeDataMapper.INSTANCE.transShopeeCategoryTreeRequest(command);

        return request;
    }

    public static HaiYingShopeeTopCategoryInfoRequest transShopeeTopCategoryInfo(HaiYingShopeeTopCategoryInfoCommand command) {
        HaiYingShopeeTopCategoryInfoRequest request = HaiYingShopeeDataMapper.INSTANCE.transShopeeTopCategoryInfoRequest(command);

        return request;
    }

    public static HaiYingShopeeSubCategoryInfoRequest transShopeeSubCategoryInfo(HaiYingShopeeSubCategoryInfoCommand command) {
        HaiYingShopeeSubCategoryInfoRequest request = HaiYingShopeeDataMapper.INSTANCE.transShopeeSubCategoryInfoRequest(command);

        return request;
    }

}
