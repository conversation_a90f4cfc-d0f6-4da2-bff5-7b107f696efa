package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.erp;

import lombok.Data;

@Data
public class EcCangERPGetWarehouseLocationResponse {

    //库位代码
    private String lcCode;

    //库位描述
    private String lcNote;

    //状态：-1
    //已废弃,0
    //不可用,1
    //可用
    private Integer lcStatus;

    //仓库ID
    private Integer warehouseId;

    //库位类型代码
    private String ltCode;

    //上架优先级
    private Integer lcSort;

    //库位ID
    private Integer lcId;

    //拣货顺序
    private Integer pickingSort;

    //通道
    private Integer point;

    //库位锁 ：0
    //无,1
    //锁
    private Integer lcLock;

    //冻结:0
    //无,1
    //冻结
    private Integer lcHold;

    //分区代码
    private String waCode;

    //仓库分区名称
    private String waName;

    //仓库分区英文名称
    private String waNameEn;

/*    -	分区类型：1
    标准,2
    不良品,3
    退货区,4
    中转区*/
    private Integer waType;

    //产品品类ID
    private Integer pcId;

    //最后更新时间
    private String lcUpdateTime;
}
