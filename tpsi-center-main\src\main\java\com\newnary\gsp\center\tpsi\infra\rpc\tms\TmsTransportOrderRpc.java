package com.newnary.gsp.center.tpsi.infra.rpc.tms;

import com.newnary.api.base.common.PageList;
import com.newnary.gsp.center.tms.api.smallparcel.feign.TmsTransportOrderFeignApi;
import com.newnary.gsp.center.tms.api.smallparcel.request.TransportOrderPageQueryReq;
import com.newnary.gsp.center.tms.api.smallparcel.response.TransportOrderPageQueryResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/6/1
 */
@Component
@Slf4j
public class TmsTransportOrderRpc {

//    @Resource
//    private TmsTransportOrderFeignApi transportOrderFeignApi;


    public PageList<TransportOrderPageQueryResp> pageQuery(TransportOrderPageQueryReq command) {
//        return transportOrderFeignApi.pageQuery(command).mustSuccessOrThrowOriginal();
        return null;
    }



}
