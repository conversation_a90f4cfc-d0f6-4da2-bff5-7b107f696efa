package com.newnary.gsp.center.tpsi.app.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.common.PageCondition;
import com.newnary.api.base.common.PageList;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.logistics.api.LogisticsCenterErrorInfo;
import com.newnary.gsp.center.logistics.api.carrier.response.LogisticsInfo;
import com.newnary.gsp.center.logistics.api.carrier.response.LogisticsServiceMappingInfo;
import com.newnary.gsp.center.logistics.api.delivery.dto.TrackInfoDTO;
import com.newnary.gsp.center.logistics.api.delivery.dto.TransportOrderPackageDTO;
import com.newnary.gsp.center.logistics.api.delivery.enums.TransportExceptionType;
import com.newnary.gsp.center.logistics.api.delivery.request.TransportOrderPageQueryCommand;
import com.newnary.gsp.center.logistics.api.delivery.request.TransportOrderUpdateTrackCommand;
import com.newnary.gsp.center.logistics.api.delivery.response.TransportOrderDetailInfo;
import com.newnary.gsp.center.logistics.api.route.dto.RouteNodeDetailDTO;
import com.newnary.gsp.center.logistics.api.route.enums.RouteNode;
import com.newnary.gsp.center.logistics.api.route.enums.RouteSidePerspective;
import com.newnary.gsp.center.logistics.api.route.request.RouteInfoAddEventCommand;
import com.newnary.gsp.center.logistics.api.route.request.RouteInfoQueryCommand;
import com.newnary.gsp.center.logistics.api.route.response.RouteInfoResp;
import com.newnary.gsp.center.tpsi.api.jt.enums.JTWebhookStatus;
import com.newnary.gsp.center.tpsi.api.jt.response.JTTrackDetailDTO;
import com.newnary.gsp.center.tpsi.api.jt.response.JTTrackResultItemsDTO;
import com.newnary.gsp.center.tpsi.api.jt.response.JTTrackResultResp;
import com.newnary.gsp.center.tpsi.api.logisticsService.request.LogisticsTrackOrderCommand;
import com.newnary.gsp.center.tpsi.api.ninjavan.vo.NinjavanWebhookMsg;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangOrderStatusEnum;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.LogisticsServiceConstants;
import com.newnary.gsp.center.tpsi.infra.model.vo.LogisticsServiceContext;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.infra.rpc.*;
import com.newnary.gsp.center.tpsi.service.JT.impl.JTLogisticsApiSvelmpl;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.job.core.handler.annotation.Job;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


@Component
@Slf4j
public class JTTMSJobManager {

    @Resource
    private TransportOrderRpc transportOrderRpc;

    @Resource
    private JTLogisticsApiSvelmpl jtLogisticsApiSvelmpl;

    @Resource
    private DeliveryOrderRpc deliveryOrderRpc;

    @Resource
    private TradeOrderRpc tradeOrderRpc;

    @Resource
    private RouteInfoRpc routeInfoRpc;

    @Resource
    private LogisticsRpc logisticsRpc;

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Job("autoJTQueryTrack")
    public ReturnT<String> queryTrack(String param) {
        log.info("[拉取JT运单轨迹任务] 定时任务开始, param={}", param);
        try {
            //根据参数获取需要执行的第三方系统id
            JSONObject paramObject = JSONObject.parseObject(param);
            String thirdPartySystemId = paramObject.getString("thirdPartySystemId");

            //根据thirdPartySystemId获取第三方系统参数
            ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);

            //step 1 获取未完成的运单
            Integer pageNum = 1;
            while (true) {
                TransportOrderPageQueryCommand tranQueryCom = new TransportOrderPageQueryCommand();
                tranQueryCom.setPageCondition(new PageCondition(pageNum, 40));
                tranQueryCom.setPushState("PUSHED");
//                tranQueryCom.setPackageState("PACKED");
                tranQueryCom.setNotEqualOrderStates(Arrays.asList("FINISHED", "CLOSED"));
//                tranQueryCom.setTradeOrderTransportType("LOCAL");
                tranQueryCom.setIsDomestic(false);

                List<LogisticsServiceMappingInfo> logisticsServiceMappingInfos = logisticsRpc.queryLogisticServiceMappingByProvider(thirdPartySystem.getProvider().name());
                if (CollectionUtils.isEmpty(logisticsServiceMappingInfos)) {
                    throw new ServiceException(LogisticsCenterErrorInfo.ERROR_5010_LOGISTICS_SERVICE_API_ERROR, "物流服务未配置");
                }
                Set<String> logisticIdSet = new HashSet<>();
                logisticsServiceMappingInfos.forEach(logisticService -> {
                    List<LogisticsInfo> logisticList = logisticsRpc.queryLogisticListByCode(logisticService.getLogisticsCode());
                    logisticList.forEach(logisticsInfo -> {
                        logisticIdSet.add(logisticsInfo.getLogisticsId());
                    });
                });
                tranQueryCom.setLogisticsIds(logisticIdSet.stream().collect(Collectors.toList()));

//                tranQueryCom.setCarrierCode(thirdPartySystem.getProvider().name());
                if (ObjectUtils.isNotEmpty(paramObject.get("transportOrderIds"))) {
                    final JSONArray transportOrderIds = (JSONArray) paramObject.get("transportOrderIds");
                    tranQueryCom.setTransportOrderIds(transportOrderIds.toJavaList(String.class));
                }
                PageList<TransportOrderDetailInfo> pageList = transportOrderRpc.pageQuery(tranQueryCom);

                if (CollectionUtils.isEmpty(pageList.getItems())) {
                    break;
                }

                List<String> trackList = pageList.getItems().stream().flatMap(transportOrderDetailInfo -> transportOrderDetailInfo.getOrderPackages().stream()).map(TransportOrderPackageDTO::getTrackingNo).collect(Collectors.toList());
                //                        if (transportOrderPackageDTO.getCarrierCode().equalsIgnoreCase(thirdPartySystem.getProvider().name())) {
                //                        }

                //step 3 批量获取轨迹
                LogisticsTrackOrderCommand command = new LogisticsTrackOrderCommand();
                command.setTrackingId(String.join(",", trackList.stream().map(String::valueOf).collect(Collectors.toList())));
                command.setLanguage("en");
                LogisticsServiceContext.getCurrentContext().put(LogisticsServiceConstants.LOGISTICS_TRACK_DOMAIN, command);
                jtLogisticsApiSvelmpl.queryTrack();
                JTTrackResultResp resultResp = (JTTrackResultResp) LogisticsServiceContext.getCurrentContext().get(LogisticsServiceConstants.CALL_TRACK_DOMAIN);

                //step 4 写入轨迹，变更状态
                if (CollectionUtils.isNotEmpty(resultResp.getResponseitems())) {
                    for (JTTrackResultItemsDTO responseitem : resultResp.getResponseitems()) {
                        String billcode = responseitem.getBillcode();
                        String transportOrderPackageId = transportOrderRpc.getOrderPackageIdByLastLogisticsNum(billcode);

                        responseitem.getDetails()
                                .sort(Comparator.comparing(JTTrackDetailDTO::getScantime));

                        for (JTTrackDetailDTO detail : responseitem.getDetails()) {
                            if (ObjectUtils.isNotEmpty(detail.getScantype())) {
                                //4.1 去重轨迹
                                if (distinctRouteInfo(billcode, detail)) continue;

                                switch (detail.getScantype()) {
                                    //@import com.newnary.gsp.center.tpsi.api.jt.enums.JTWebhookStatus
//                                    case "Order Created":
//                                    case "Picked Up Fail":
//                                    case "Delivery Fail":
//                                    case "Return Initiated": {
//                                        buildRouteInfo(billcode, detail, Objects.requireNonNull(JTWebhookStatus.getByName(detail.getScantype())), RouteNode.LOCAL_LOGISTICS_DELIVERY);
//                                        break;
//                                    }
                                    case "Picked Up": {
                                        buildRouteInfo(billcode, detail, JTWebhookStatus.SuccessfulPickup, RouteNode.PLATFORM_DELIVER);
                                        if (StringUtils.isNotEmpty(transportOrderPackageId)) {
                                            TransportOrderUpdateTrackCommand updateTrackCommand = new TransportOrderUpdateTrackCommand();
                                            updateTrackCommand.setTrackInfo(new TrackInfoDTO("JT", billcode));
                                            updateTrackCommand.setTransportOrderPackageId(transportOrderPackageId);
                                            transportOrderRpc.updateTrack(updateTrackCommand);
                                            transportOrderRpc.confirmDelivery(transportOrderPackageId);
                                        }
                                    }
                                    break;
                                    case "Departure":
                                    case "Arrival":
                                    case "On Delivery":
                                    case "Problematic" : {
                                        buildRouteInfo(billcode, detail, JTWebhookStatus.getByName(detail.getScantype()), RouteNode.LOCAL_LOGISTICS_DELIVERY);
                                        if (StringUtils.isNotEmpty(transportOrderPackageId)) {
                                            TransportOrderUpdateTrackCommand updateTrackCommand = new TransportOrderUpdateTrackCommand();
                                            updateTrackCommand.setTrackInfo(new TrackInfoDTO("JT", billcode));
                                            updateTrackCommand.setTransportOrderPackageId(transportOrderPackageId);
                                            transportOrderRpc.updateTrack(updateTrackCommand);
                                            transportOrderRpc.localTransit(transportOrderPackageId);
                                        }
                                    }
                                    break;
                                    case "Delivered": {
                                        // 用户签收，记录轨迹
                                        buildRouteInfo(billcode, detail, JTWebhookStatus.SuccessfulDelivery, RouteNode.CUSTOMER_RECEIVE);
                                        if (StringUtils.isNotEmpty(transportOrderPackageId)) {
                                            transportOrderRpc.finishedByPackage(transportOrderPackageId);
                                        }
                                    }
                                    break;
                                    case "Return Register": {
                                        buildRouteInfo(billcode, detail, JTWebhookStatus.ReturnRegister, RouteNode.EXCEPTION_DEAL);
                                        if (StringUtils.isNotEmpty(transportOrderPackageId)) {
                                            transportOrderRpc.pushException(transportOrderPackageId, TransportExceptionType.RETURN_TO_WAREHOUSE_EXCEPTION);
                                        }
                                    }
                                    break;
                                    case "Return Delivered": {
                                        buildRouteInfo(billcode, detail, JTWebhookStatus.ReturnedToSender, RouteNode.EXCEPTION_DEAL);
                                        if (StringUtils.isNotEmpty(transportOrderPackageId)) {
                                            transportOrderRpc.dealException(transportOrderPackageId);
                                        }
                                    }
                                    break;
                                    default: {
                                        buildRouteInfo(billcode, detail, detail.getScantype(), RouteNode.LOCAL_LOGISTICS_DELIVERY);
                                    }
                                    break;
                                }
                            } else {
                                log.warn("新增JT轨迹消息:{}", detail.getScantype());
                            }
                        }
                    }
                }
                pageNum++;
            }
        } catch (Exception e) {
            log.error("[拉取JT运单轨迹任务] 定时任务异常, param={}", param, e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    private boolean distinctRouteInfo(String billcode, JTTrackDetailDTO detail) {
        RouteInfoQueryCommand queryCommand = new RouteInfoQueryCommand();
        queryCommand.setBizOrderNumber(billcode);
        queryCommand.setOrderPackageNumber(billcode);
        queryCommand.setSidePerspective(RouteSidePerspective.OPERATOR.name());
        RouteInfoResp routeInfoResp = routeInfoRpc.query(queryCommand).mustSuccessOrThrowOriginal();
        if (ObjectUtils.isNotEmpty(routeInfoResp)) {
            log.info("routeInfoResp{},detail{}",routeInfoResp,detail);
            if (routeInfoResp.getRouteInfos().stream().anyMatch(p -> p.getNodeDetail().getEvent().equals(detail.getScantype()))) {
                return true;
            }
            if (ObjectUtils.isNotEmpty(JTWebhookStatus.getByName(detail.getScantype()))
                && routeInfoResp.getRouteInfos()
                        .stream()
                        .anyMatch(l -> l.getNodeDetail().getEvent().equals(Objects.requireNonNull(JTWebhookStatus.getByName(detail.getScantype())).name()))){
                return true;
            }
        }
        return false;
    }

    private void buildRouteInfo(String billcode, JTTrackDetailDTO detail, JTWebhookStatus status, RouteNode localLogisticsDelivery) {
        NinjavanWebhookMsg msg = new NinjavanWebhookMsg();
        msg.setTracking_id(billcode);
        msg.setTimestamp(detail.getScantime());
        msg.setStatus(status.name());
        addRouteInfo(msg, localLogisticsDelivery);
    }

    private void buildRouteInfo(String billcode, JTTrackDetailDTO detail, String status, RouteNode localLogisticsDelivery) {
        NinjavanWebhookMsg msg = new NinjavanWebhookMsg();
        msg.setTracking_id(billcode);
        msg.setTimestamp(detail.getScantime());
        msg.setStatus(status);
        msg.setRemark(detail.getDesc());
        addRouteInfo(msg, localLogisticsDelivery);
    }

    private Set<RouteSidePerspective> tranSidePerspective(Set<String> sidePerspectives) {
        Set<RouteSidePerspective> ret = new HashSet<>();
        sidePerspectives.forEach(sidePerspective -> {
            ret.add(RouteSidePerspective.getByName(sidePerspective));
        });
        return ret;
    }

    private String convert2ErpOrderStatus(String status) {
        return EcCangOrderStatusEnum.getByValue(status).getErpOrderStatus();
    }

    public ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }

    private void addRouteInfo(NinjavanWebhookMsg msg, RouteNode routeNode) {
        RouteInfoAddEventCommand command = new RouteInfoAddEventCommand();
        command.bizOrderNumber = msg.getTracking_id();
        command.orderPackageNumber = msg.getTracking_id();
        command.bizId = msg.getTracking_id();
        command.bizSystem = "LOGISTICS-CENTER";
        command.bizModule = "TRANSPORT";
        command.sidePerspectives = new HashSet<>(Arrays.asList(RouteSidePerspective.OPERATOR, RouteSidePerspective.CUSTOMER));
        command.routeNode = routeNode;

        RouteNodeDetailDTO detail = new RouteNodeDetailDTO();
        detail.setLocation("JT");
        detail.setOperator("JT");

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (StringUtils.isNotBlank(msg.getTimestamp())) {
            try {
                long time = dateFormat.parse(msg.getTimestamp())
                        .getTime();
                detail.setGmtOperation(time);
            } catch (ParseException e) {
                e.printStackTrace();
            }

        } else {
            detail.setGmtOperation(System.currentTimeMillis());
        }
        detail.setEvent(msg.getStatus());
        try {
            detail.setRemark(JTWebhookStatus.valueOf(msg.getStatus()).getDescription());
        } catch (Exception e) {
            detail.setRemark(msg.getRemark());
        }

        command.detail = detail;

        routeInfoRpc.addRouteInfo(command);
    }

}
