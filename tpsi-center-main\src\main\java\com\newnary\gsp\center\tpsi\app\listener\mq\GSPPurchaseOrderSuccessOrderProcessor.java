package com.newnary.gsp.center.tpsi.app.listener.mq;


import com.newnary.gsp.center.tpsi.api.eccang.request.GspSyncPo2EccangCommand;
import com.newnary.gsp.center.tpsi.app.service.eccang.EccangPoMgmtApp;
import com.newnary.gsp.center.tpsi.infra.mq.consumer.GSPPurchaseOrderMQConsumer;
import com.newnary.messagebody.gsp.purchase.GSPPurchaseOrderTopic;
import com.newnary.messagebody.gsp.purchase.mo.GspPurchaseOrderTakeStockMO;
import com.newnary.mq.starter.consumer.AbstractMQProcessor;
import com.newnary.mq.starter.consumer.MQMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class GSPPurchaseOrderSuccessOrderProcessor extends AbstractMQProcessor<GspPurchaseOrderTakeStockMO> {

    @Resource
    private EccangPoMgmtApp eccangPoMgmtApp;

    @Override
    public boolean doProcess(MQMessage<GspPurchaseOrderTakeStockMO> message) {

        GspPurchaseOrderTakeStockMO mo = message.getContent();
        log.info("采购单部分收货强制完成ecang入库单消息消费开始：purchaseOrderId: {}", mo.getPurchaseOrderId());

        // 消息处理
        GspSyncPo2EccangCommand command = new GspSyncPo2EccangCommand();
        command.setPurchaseOrderId(mo.getPurchaseOrderId());
        command.setCurrOptUserId(mo.getCurrOptUserId());
        try {
            eccangPoMgmtApp.successEcOrder(command);
        } catch (Exception e) {
            log.info("采购单部分收货强制完成ecang入库单消息消费异常！purchaseOrderId={}, e={}", mo.getPurchaseOrderId(), e);
        }

        return true;
    }

    /**
     * 消费者类型
     *
     * @return
     */
    @Override
    public Class<?> consumerClz() {
        return GSPPurchaseOrderMQConsumer.class;
    }

    /**
     * 标签
     *
     * @return
     */
    @Override
    public String tag() {
        return GSPPurchaseOrderTopic.Tag.SUCCESS_ORDER;
    }
}
