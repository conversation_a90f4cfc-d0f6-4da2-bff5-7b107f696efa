package com.newnary.gsp.center.tpsi.api.haiying;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.api.base.common.PageList;
import com.newnary.gsp.center.tpsi.api.haiying.request.lazada.*;
import com.newnary.gsp.center.tpsi.api.haiying.response.lazada.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-13
 */
@RequestMapping("tpsi-center/haiying/lazada")
public interface HaiYingLazadaApi {

    @PostMapping("getLazadaProductList")
    CommonResponse<PageList<HaiYingLazadaProductListDTO>> getLazadaProductList(@RequestBody HaiYingLazadaProductListCommand command);

    @PostMapping("getLazadaProductDetailInfo")
    CommonResponse<List<HaiYingLazadaProductDetailInfoDTO>> getLazadaProductDetailInfo(@RequestBody HaiYingLazadaProductDetailInfoCommand command);

    @PostMapping("getLazadaProductExtInfo") //todo 暂时收起来
    CommonResponse<List<HaiYingLazadaProductExtInfoDTO>> getLazadaProductExtInfo(@RequestBody HaiYingLazadaProductExtInfoCommand command);

    @PostMapping("getLazadaProductHistoryInfo")
    CommonResponse<List<HaiYingLazadaProductHistoryInfoDTO>> getLazadaProductHistoryInfo(@RequestBody HaiYingLazadaProductHistoryInfoCommand command);

    @PostMapping("getLazadaProductHistoryDailyReview")//todo 待删除
    CommonResponse<List<HaiYingLazadaProductHistoryDailyReviewDTO>> getLazadaProductHistoryDailyReview(@RequestBody HaiYingLazadaProductHistoryDailyReviewCommand command);

    @PostMapping("getLazadaCategoryTree")
    CommonResponse<List<HaiYingLazadaCategoryTreeDTO>> getLazadaCategoryTree(@RequestBody HaiYingLazadaCategoryTreeCommand command);

    @PostMapping("getLazadaTopCategoryInfo")
    CommonResponse<List<HaiYingLazadaCategoryInfoDTO>> getLazadaTopCategoryInfo(@RequestBody HaiYingLazadaTopCategoryInfoCommand command);

    @PostMapping("getLazadaSubCategoryInfo")
    CommonResponse<PageList<HaiYingLazadaCategoryInfoDTO>> getLazadaSubCategoryInfo(@RequestBody HaiYingLazadaSubCategoryInfoCommand command);

}
