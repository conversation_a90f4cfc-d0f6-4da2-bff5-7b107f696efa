package com.newnary.gsp.center.tpsi.infra.client.mabang.valobj;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class MaBangStockSku {

    private String classType;

    private String salesSku;
    private String stockSku;
    private String nameCN;
    private String nameEN;
    private String defaultCost;
    private String forecastDaySale;
    private Integer hasBattery;
    private Integer isNewType;
    private Integer isTort;
    private Integer livenessType;
    private Integer status;
    private String timeCreated;
    private String timeModify;
    private String originalSku;
    private String brandName;
    private String parentCategoryName;
    private String categoryName;
    private String salePrice;
    private String declareValue;
    private String stockPicture;
    private String salePicture;
    private List<String> stockDetailImg;
    private String length;
    private String width;
    private String height;
    private String weight;
    private String declareName;
    private String declareEname;
    private String remark;
    private String saleRemark;
    private String purchaseRemark;
    @JSONField(name = "package")
    private String packingType;
    private String declareCode;
    private Integer isGift;
    private Integer magnetic;
    private Integer powder;
    private String purchasePrice;
    private String developerId;
    private String developerName;
    private Integer buyerId;
    private String buyerName;
    private Integer artDesignerId;
    private String artDesignerName;
    private List<String> sales;
    private List<String> virtualSku;
    private String provider;
    private String productLinkAddress;
    private List<MaBangStockSkuWarehouseInfo> warehouse;
    private List<String> label;
    private List<MaBangStockSkuAttributes> attributes;
    private String financial;
}
