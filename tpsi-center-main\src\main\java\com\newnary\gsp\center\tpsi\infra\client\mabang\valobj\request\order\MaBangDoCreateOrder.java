package com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.order;

import lombok.Data;

import java.util.List;

@Data
public class MaBangDoCreateOrder {

    /**
     * 订单编号 (必填)
     */
    private String platformOrderId;

    /**
     * 店铺名称 (必填)
     */
    private String shopName;

    /**
     * 买家编号 (必填)
     */
    private String buyerUserId;

    /**
     * 买家电话一 (必填)
     */
    private String phone1;

    /**
     * 买家国家，和countryCode俩者至少一个必填 (必填)
     */
    private String country;

    /**
     * 买家国家二字码，填写时country可不写 (必填)
     */
    private String countryCode;

    /**
     * 买家地址一 (必填)
     */
    private String street1;

    /**
     * 订单币种 (必填)
     */
    private String currencyId;

    /**
     * 付款时间 (必填)
     */
    private String paidTime;

    /**
     * 商品信息 (必填)
     */
    private List<MaBangDoCreateOrderOrderItem> orderItemList;

    /**
     * 平台编号,不传按订单存储
     */
    private String salesRecordNumber;

    /**
     * 平台跟踪单号
     */
    private String platformTrackNumber;

    /**
     * 发货类型
     */
    private String shippingType;

    /**
     * 发货仓库名称
     */
    private String warehouseName;

    /**
     * Erp物流渠道编号
     */
    private String myLogisticsChannelId;

    /**
     * 内部单号
     */
    private String trackNumber1;

    /**
     * 虚拟单号
     */
    private String trackNumber2;

    /**
     * 买家名称
     */
    private String buyerName;

    /**
     * 买家电话二
     */
    private String phone2;

    /**
     * 买家所属区域
     */
    private String district;

    /**
     * 买家所在省份
     */
    private String province;

    /**
     * 买家所在城市
     */
    private String city;

    /**
     * 买家地址二
     */
    private String street2;

    /**
     * VAT税号
     */
    private String abnnumber;

    /**
     * 买家邮箱
     */
    private String email;

    /**
     * 买家门牌号
     */
    private String doorcode;

    /**
     * 买家邮编
     */
    private String postCode;

    /**
     * 买家留言
     */
    private String buyerMessage;

    /**
     * 优惠金额
     */
    private String voucherPrice;

    /**
     * 运费支出
     */
    private String shippingCost;

    /**
     * 邮费
     */
    private String shippingFee;

    /**
     * 平台费
     */
    private String platformFee;

    /**
     * 商品总售价，如果填写值，将不按照商品售价计算
     */
    private String itemTotal;

    /**
     * 其他收入
     */
    private String otherIncome;

    /**
     * 其他支出
     */
    private String otherExpend;

    /**
     * 原始保险费
     */
    private String insuranceFee;

    /**
     * 原始资金转账费
     */
    private String paypalFee;

    /**
     * 支付方式
     */
    private String paymentName;

    /**
     * 10.cod订单 11.wish WE订单
     */
    private String codflag;

    /**
     * 买家自选物流
     */
    private String shippingService;

    /**
     * 是否自提信息
     */
    private String dlypName;

    /**
     * paypal编号
     */
    private String paypalId;

    /**
     * 转账流水号
     */
    private String transNumber;

    /**
     * 发货备注
     */
    private String deliveryRemark;

    /**
     * 订单备注
     */
    private String remark;

}
