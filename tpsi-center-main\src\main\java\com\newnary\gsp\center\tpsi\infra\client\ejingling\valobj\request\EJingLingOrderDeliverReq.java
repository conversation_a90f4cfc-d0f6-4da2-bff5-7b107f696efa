package com.newnary.gsp.center.tpsi.infra.client.ejingling.valobj.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class EJingLingOrderDeliverReq {
    /**
     *订单编码
     */
    @NotNull(message = "订单编码不能为空")
    private String orderNo;

    /**
     *发货类型，1-整单发货  2-缺货发货，需传明细发货信息
     */
    @NotNull(message = "发货类型不能为空")
    private Integer type;

    /**
     *快递公司
     */
    @NotNull(message = "快递公司不能为空")
    private String courierName;

    /**
     *快递单号
     */
    @NotNull(message = "快递单号不能为空")
    private String courierOrderNo;

    /**
     * 发货单明细列表
     */
    private List<OrderDetailDeliver> orderDetailDeliverList;

    @Getter
    @Setter
    public static class OrderDetailDeliver {
        /**
         * 外部sku id
         */
        private Long outerSkuId;

        /**
         * 发货数量
         */
        private Integer deliverNum;
    }

}
