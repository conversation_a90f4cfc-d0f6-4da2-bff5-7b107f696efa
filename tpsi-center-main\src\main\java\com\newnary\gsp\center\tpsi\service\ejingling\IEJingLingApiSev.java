package com.newnary.gsp.center.tpsi.service.ejingling;

import com.newnary.gsp.center.tpsi.infra.client.ejingling.valobj.request.*;
import com.newnary.gsp.center.tpsi.infra.client.ejingling.valobj.response.*;

import java.util.List;

public interface IEJingLingApiSev {
    //全量商品列表
    EJingLingGoodsListResponse fullGoodsList(String thirdPartySystemId, EJingLingFullGoodsListReq req);
    //店铺全量商品列表
    EJingLingGoodsListResponse shopFullGoodsList(String thirdPartySystemId, EJingLingShopFullGoodsListReq req);

    //查询商品信息
    List<EJingLingGoodsListResponse.OuterGoodsVo> getGoodsByGoodsId(String thirdPartySystemId, String ids);

    //增量商品列表
    EJingLingGoodsListResponse increGoodsList(String thirdPartySystemId, EJingLingIncreGoodsListReq req);
    //增量店铺商品列表
    EJingLingGoodsListResponse shopIncreGoodsList(String thirdPartySystemId, EJingLingShopIncreGoodsListReq req);

    //创建订单
    List<EJingLingCreateOrderResponse> createOrder(String thirdPartySystemId, EJingLingCreateOrderReq req);
    //订单发货
    String orderDeliver(String thirdPartySystemId, EJingLingOrderDeliverReq req);

    //获取支持的快递公司
    List<EJingLingGetCourierResponse> getCourier(String thirdPartySystemId, EJingLingGetCourierReq req);

    //获取物流公司列表
    List<EJingLingLogisticsFirmResponse> getLogisticsFirm(String thirdPartySystemId);

    //通过订单编码查询订单
    List<EJingLingGetOrderResponse> getOrderByOrderNo(String thirdPartySystemId, String orderNo);
}
