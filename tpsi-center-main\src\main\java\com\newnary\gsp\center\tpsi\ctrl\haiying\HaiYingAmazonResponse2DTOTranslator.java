package com.newnary.gsp.center.tpsi.ctrl.haiying;

import com.newnary.api.base.common.PageList;
import com.newnary.api.base.common.PageMeta;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingStation;
import com.newnary.gsp.center.tpsi.api.haiying.response.amazon.HaiYingAmazonCategoryTreeDTO;
import com.newnary.gsp.center.tpsi.api.haiying.response.amazon.HaiYingAmazonProductDetailInfoDTO;
import com.newnary.gsp.center.tpsi.api.haiying.response.amazon.HaiYingAmazonProductHistoryInfoDTO;
import com.newnary.gsp.center.tpsi.api.haiying.response.amazon.HaiYingAmazonProductListDTO;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.amazon.HaiYingAmazonCategoryTreeResponse;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.amazon.HaiYingAmazonProductDetailInfoResponse;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.amazon.HaiYingAmazonProductHistoryInfoResponse;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.amazon.HaiYingAmazonProductListResponse;
import com.newnary.gsp.center.tpsi.infra.mapper.HaiYingAmazonDataMapper;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/07/14 13:45
 */
public class HaiYingAmazonResponse2DTOTranslator {

    public static PageList<HaiYingAmazonProductListDTO> transAmazonProductListList(HaiYingStation station, List<HaiYingAmazonProductListResponse> response, PageMeta pageMeta) {
        PageList<HaiYingAmazonProductListDTO> ret = new PageList<>();
        List<HaiYingAmazonProductListDTO> list = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingAmazonProductListDTO dto = HaiYingAmazonDataMapper.INSTANCE.transAmazonProductListDTO(resp);
            switch (station) {
                case AMAZON_UK:
                    dto.setCurrency("GBP");
                    break;
                case AMAZON_DE:
                    dto.setCurrency("EUR");
                    break;
                case AMAZON_US:
                    dto.setCurrency("USD");
                    break;
                case AMAZON_JP:
                    dto.setCurrency("JPY");
                    break;
                default:
                    break;
            }

            list.add(dto);
        });
        ret.setItems(list);
        ret.setPageMeta(pageMeta);
        return ret;
    }

    public static List<HaiYingAmazonProductDetailInfoDTO> transAmazonProductDetailInfoList(List<HaiYingAmazonProductDetailInfoResponse> response) {
        List<HaiYingAmazonProductDetailInfoDTO> ret = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingAmazonProductDetailInfoDTO dto = HaiYingAmazonDataMapper.INSTANCE.transAmazonProductDetailDTO(resp);

            ret.add(dto);
        });
        return ret;
    }

    public static List<HaiYingAmazonProductHistoryInfoDTO> transAmazonProductHistoryInfoList(List<HaiYingAmazonProductHistoryInfoResponse> response) {
        List<HaiYingAmazonProductHistoryInfoDTO> ret = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingAmazonProductHistoryInfoDTO dto = HaiYingAmazonDataMapper.INSTANCE.transAmazonProductHistoryDTO(resp);

            ret.add(dto);
        });
        return ret;
    }

    public static List<HaiYingAmazonCategoryTreeDTO> transAmazonCategoryTreeList(List<HaiYingAmazonCategoryTreeResponse> response) {
        List<HaiYingAmazonCategoryTreeDTO> ret = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingAmazonCategoryTreeDTO dto = HaiYingAmazonDataMapper.INSTANCE.transAmazonCategoryTreeDTO(resp);

            ret.add(dto);
        });
        return ret;
    }

}
