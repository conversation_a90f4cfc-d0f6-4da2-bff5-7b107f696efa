package com.newnary.gsp.center.tpsi.infra.mq.consumer;

import com.newnary.messagebody.gsp.logistics.GSPTransportOrderCancelTopic;
import com.newnary.mq.starter.consumer.AbstractMQConsumer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class TransportOrderCancelConsumer extends AbstractMQConsumer {

    @Value("${gsp.transport-order-cancel.mq.consumer-id}")
    private String group;

    @Override
    public String topic() {
        return GSPTransportOrderCancelTopic.TOPIC;
    }

    @Override
    public String consumerGroup() {
        return group;
    }
}
