package com.newnary.gsp.center.tpsi.api.haiying.response.ebay;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingEbayTopCategoryInfoDTO {

    /**
     * 类目ID
     */
    private String cid;

    /**
     * 类目名称
     */
    private String cname;

    /**
     * 有销量的商品总数
     */
    private Integer product_count;

    /**
     * 店铺总数
     */
    private Integer merchant_count;

    /**
     * 前1天类目销售件数
     */
    private Integer sold_the_previous_day;

    /**
     * 前1天类目销售金额
     */
    private BigDecimal payment_the_previous_day;

    /**
     * 前1天类目销售增幅
     */
    private BigDecimal sold_the_previous_growth;

    /**
     * 前1-7天类目销售件数
     */
    private Integer sales_week1;

    /**
     * 前8-14天类目销售件数
     */
    private Integer sales_week2;

    /**
     * 前1-7天类目销售金额
     */
    private BigDecimal payment_week1;

    /**
     * 前7天类目销售增幅
     */
    private BigDecimal sales_week_growth;

    /**
     * 统计时间
     */
    private Long stat_date;

    /**
     * 存入时间
     */
    private Long ins_date;

    /**
     * 前1-3天类目销售件数
     */
    private Integer sales_three_day1;

    /**
     * 前4-6天类目销售件数
     */
    private Integer sales_three_day2;

    /**
     * 前1-3天类目销售金额
     */
    private BigDecimal payment_three_day1;

    /**
     * 前3天类目销售增幅
     */
    private BigDecimal sales_three_day_growth;

    /**
     * 商品总数(已取消)
     */
    @Deprecated
    private Integer all_product_count;

    /**
     * 销量为0的商品总数(已取消)
     */
    @Deprecated
    private Integer unsold_product_count;

}
