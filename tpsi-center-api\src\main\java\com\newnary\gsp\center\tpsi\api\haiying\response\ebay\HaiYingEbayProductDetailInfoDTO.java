package com.newnary.gsp.center.tpsi.api.haiying.response.ebay;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingEbayProductDetailInfoDTO {

    /**
     * 商品状态码
     * (200:正常 100:商品未收录)
     */
    private Integer code;

    /**
     * 商品id
     */
    private String item_id;

    /**
     * 商品运费(已取消,预留)
     */
    @Deprecated
    private BigDecimal ship_price;

    /**
     * 商品SPU(已取消,预留)
     */
    @Deprecated
    private String spu;

    /**
     * 商品主副图
     * (多张图片以逗号分隔)(已取消,预留)
     */
    @Deprecated
    private List<String> main_images;

    /**
     * 商品描述(已取消,预留)
     */
    @Deprecated
    private String description;

    /**
     * 商品sku列表(已取消,预留)
     */
    @Deprecated
    private HaiYingEbayProductSkuInfoDTO[] sku_list;

    /**
     * 商品历史走势
     */
    private HaiYingEbayProductHistoryDataInfoDTO[] main_his;

    /**
     * 商品每日销量走势(已取消)
     */
    @Deprecated
    private HaiYingEbayProductSoldHistoryInfoDTO[] sold_his;

    /**
     * 海鹰统计商品每日销量走势
     */
    private HaiYingEbayProductSoldHistoryInfo2DTO[] sold_his_v2;

    /**
     * ebay商品链接
     */
    private String item_url;

    /**
     * ebay店铺链接
     */
    private String seller_url;

    /**
     * 货源链接
     */
    private String supply_url;

    /**
     * 商品上架时间
     */
    private Long listed_time;

    /**
     * 商品最近抓取时间
     */
    private Long last_modi_time;

    /**
     * 商品上架地区
     */
    private String marketplace;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品类型
     *（1New，
     * 2New other，
     * 3New other (see details)，
     * 4New with box，
     * 5New with defects，
     * 6New with tags，
     * 7New without box，
     * 8New without tags，
     * 9Brand New，
     * 10Like New）
     */
    private Integer condition_type;

    /**
     * 商品品牌
     */
    private String brand;

    /**
     * 商品价格
     */
    private BigDecimal price;

    /**
     * 商品销量
     */
    private Integer sold;

    /**
     * 商品收藏
     */
    private Integer watchers;

    /**
     * 商品浏览
     */
    private String visit;

    /**
     * 商品所属类目id
     */
    private String cids;

    /**
     * 商品所属卖家名称
     */
    private String seller;

    /**
     * 商品item地点
     */
    private String item_location;

    /**
     * 商品主图
     */
    private String main_image;

    /**
     * 商品下架时间
     */
    private Long ended_time;

    /**
     * 商品小火苗
     */
    private String popular;

    /**
     * status:商品状态
     * (0正常，1异常)
     */
    private Integer status;

    /**
     * 商品存在
     * (0存在，1不存在)
     */
    private Boolean not_exist;

    /**
     * 完整类目路径(多个;分隔)
     */
    private List<String> category_structure;

    /**
     * 商品附属适用信息
     */
    private HaiYingEbayProductCompatibleInfoDTO[] pro_compatible_list;

}
