package com.newnary.gsp.center.tpsi.infra.rpc;

import com.newnary.api.base.common.PageList;
import com.newnary.gsp.center.product.api.product.feign.ChannelSaleItemFeignApi;
import com.newnary.gsp.center.product.api.product.request.ChannelSaleItemGetDetailCommand;
import com.newnary.gsp.center.product.api.product.request.ChannelSaleItemGetDetailsWithSameSpuBySaleItemIdCommand;
import com.newnary.gsp.center.product.api.product.request.ChannelSaleItemPageQueryDetailCommand;
import com.newnary.gsp.center.product.api.product.response.ChannelSaleItemDetailInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class SaleItemRpc {

    @Resource
    private ChannelSaleItemFeignApi channelSaleItemFeignApi;

    public ChannelSaleItemDetailInfo querySaleItem(String channel, String saleItemId) {
        ChannelSaleItemGetDetailCommand command = new ChannelSaleItemGetDetailCommand();
        command.channelId = channel;
        command.saleItemId = saleItemId;
        return channelSaleItemFeignApi.getDetail(command).mustSuccessOrThrowOriginal();
    }

    public PageList<ChannelSaleItemDetailInfo> pageQueryDetail(ChannelSaleItemPageQueryDetailCommand command) {
        return channelSaleItemFeignApi.pageQueryDetail(command).mustSuccessOrThrowOriginal();
    }

    // todo linjiamei-tiktok 需要product_center 提供相关整合接口
    public List<ChannelSaleItemDetailInfo> findItemsGroupBySpuId(String channelId, String saleItemId) {
        ChannelSaleItemGetDetailsWithSameSpuBySaleItemIdCommand command = new ChannelSaleItemGetDetailsWithSameSpuBySaleItemIdCommand();
        command.setChannelId(channelId);
        command.setSaleItemId(saleItemId);
        return channelSaleItemFeignApi.getDetailsWithSameSpuBySaleItemId(command).mustSuccessOrThrowOriginal();
    }

}
