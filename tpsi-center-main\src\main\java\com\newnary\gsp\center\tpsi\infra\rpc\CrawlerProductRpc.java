package com.newnary.gsp.center.tpsi.infra.rpc;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.gsp.center.crawlerproxy.api.crawler.feign.Ali1688CrawlerFeignApi;
import com.newnary.gsp.center.crawlerproxy.api.crawler.request.AliProductCategoyNameCommand;
import com.newnary.gsp.center.crawlerproxy.api.crawler.request.AliProductDetailCommand;
import com.newnary.gsp.center.crawlerproxy.api.crawler.request.AliProductSearchCommand;
import com.newnary.gsp.center.crawlerproxy.api.crawler.response.AliProductCrawlerReturnInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/03/25
 */
@Component
public class CrawlerProductRpc {

    @Resource
    private Ali1688CrawlerFeignApi ali1688CrawlerFeignApi;

    public List<String> getProductId(AliProductSearchCommand aliProductSearchCommand) throws InterruptedException {
        return  ali1688CrawlerFeignApi.productIdDetail(aliProductSearchCommand).mustSuccessOrThrowOriginal();
    }

    public AliProductCrawlerReturnInfo getProductDetail(AliProductDetailCommand command) {
        return ali1688CrawlerFeignApi.productDetailInfo(command).mustSuccessOrThrowOriginal();
    }

}
