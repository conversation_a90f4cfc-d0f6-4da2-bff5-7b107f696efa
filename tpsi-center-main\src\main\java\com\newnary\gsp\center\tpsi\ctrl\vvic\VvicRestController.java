package com.newnary.gsp.center.tpsi.ctrl.vvic;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.newnary.api.base.common.CommonResponse;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.distributed.tools.concurrent.template.DConcurrentTemplate;
import com.newnary.gsp.center.product.api.open.request.*;
import com.newnary.gsp.center.tpsi.api.vvic.VvicOpenApi;
import com.newnary.gsp.center.tpsi.infra.client.vvic.params.VVICBaseParam;
import com.newnary.gsp.center.tpsi.infra.client.vvic.utils.VVICSignUtil;
import com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.request.VVICGetItemDetialReq;
import com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.response.VVICGetItemDetialResponse;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.infra.rpc.OpenSupplierProductRpc;
import com.newnary.gsp.center.tpsi.service.vvic.VVICApiSev;
import com.newnary.tenant.context.TenantCarrier;
import com.newnary.tenant.context.TenantID;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: jack
 * @CreateTime: 2023-4-13
 */
@RestController
@Slf4j
public class VvicRestController implements VvicOpenApi {
    @Resource
    private VVICApiSev vvicApiSevImpl;

    @Resource
    private OpenSupplierProductRpc openSupplierProductRpc;

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Override
    public CommonResponse<String> vvicWebhooks(String tenantID, String bizId) {
        log.info("搜款网推送商品信息请求开始，tenantID={}，bizId={}", tenantID, bizId);
        if (null == RequestContextHolder.getRequestAttributes()) {
            return CommonResponse.success("request is null");
        }
        if (StringUtils.isBlank(tenantID)) {
            return CommonResponse.success("租户id必填");
        }else {
            TenantCarrier.setTenantID(new TenantID(tenantID));
        }
        if (StringUtils.isBlank(bizId)) {
            return CommonResponse.success("业务id必填");
        }

        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String vvicMessage;
        try {
            vvicMessage = this.readRequestBody(request.getInputStream());
            VVICMessageReq vvicMessageReq = JSON.parseObject(vvicMessage, VVICMessageReq.class);
            if (null != vvicMessageReq) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("msgId",vvicMessageReq.getMsgId());
                ThirdPartySystem thirdPartySystem = loadSystem(bizId);
                if (!verificationSign(thirdPartySystem, vvicMessageReq)) {
                    jsonObject.put("message","The parameter named sign is illegal.");
                    return CommonResponse.success(jsonObject.toJSONString());
                }
                VVICMessageReq.Data data = vvicMessageReq.getData();
                if (null != data && StringUtils.isNotBlank(data.getItemVid()) && StringUtils.isNotBlank(data.getType())) {
                    return CommonResponse.success(dealMessage(data.getType(), data.getItemVid(), data.getChangedFields(), bizId, tenantID));
                }

            }

        } catch (IOException e) {
            e.printStackTrace();
            return CommonResponse.success(e.getMessage());
        }finally {
            Optional<TenantID> optionalTenantID = TenantCarrier.getTenantID();
            if (optionalTenantID.isPresent()) {
                TenantCarrier.clearTenantID();
            }
        }
        log.info("收到VVIC的消息: {} - {} ", tenantID, vvicMessage);

        return CommonResponse.success("");
    }

    //处理vvic消息
    private String dealMessage(String type, String itemVid, List<String> changedFields, String bizId, String tenantID) {
        return DConcurrentTemplate.tryLockMode(
                "VVIC:MESSAGE".concat(type).concat(itemVid),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    String rep;
                    ThirdPartySystem thirdPartySystem = loadSystem(bizId);
                    if (ObjectUtils.isEmpty(thirdPartySystem)) {
                        rep = "第三方平台数据为空";
                        return rep;
                    }
                    switch (type) {
                        //sku更新
                        case "vvic_item_sku_change":
                            if (CollectionUtils.isNotEmpty(changedFields) && changedFields.contains("price")) {
                                VVICGetItemDetialReq changeReq = new VVICGetItemDetialReq();
                                changeReq.setItem_vid(itemVid);
                                VVICGetItemDetialResponse changeItemDetial = vvicApiSevImpl.getItemDetial(thirdPartySystem.getBizId(), changeReq);
                                if (null != changeItemDetial && changeItemDetial.getItem_list() != null) {
                                    changeItemDetial.getItem_list().forEach(item -> updatePrice(thirdPartySystem, item));
                                }
                            }
                            rep = "success to item_sku_change";
                            break;
                        //spu更新
                        case "vvic_item_update":
                            if (CollectionUtils.isNotEmpty(changedFields) && changedFields.contains("price")) {
                                VVICGetItemDetialReq changeReq = new VVICGetItemDetialReq();
                                changeReq.setItem_vid(itemVid);
                                VVICGetItemDetialResponse changeItemSkuDetial = vvicApiSevImpl.getItemDetial(thirdPartySystem.getBizId(), changeReq);
                                if (null != changeItemSkuDetial && changeItemSkuDetial.getItem_list() != null) {
                                    changeItemSkuDetial.getItem_list().forEach(item -> updatePrice(thirdPartySystem, item));
                                }
                            }
                            rep = "success to item_update";
                            break;
                        //spu下架
                        case "vvic_item_downshelf":
                            stopSpuSupply(thirdPartySystem, itemVid);
                            rep = "success to item_downshelf";
                            break;
                        //spu下架再上架
                        case "vvic_item_upshelf":
                            startSpuSupply(thirdPartySystem, itemVid);
                            rep = "success to item_upshelf";
                            break;
                        //sku下架
                        case "vvic_item_sku_downshelf":
                            VVICGetItemDetialReq downshelfReq = new VVICGetItemDetialReq();
                            downshelfReq.setItem_vid(itemVid);
                            VVICGetItemDetialResponse downshelfItemDetial = vvicApiSevImpl.getItemDetial(thirdPartySystem.getBizId(), downshelfReq);
                            if (null != downshelfItemDetial && downshelfItemDetial.getItem_list() != null) {
                                stopSupply(thirdPartySystem, downshelfItemDetial.getItem_list().get(0));
                            }
                            rep = "success to item_sku_downshelf";
                            break;
                        //sku下架再上架
                        case "vvic_item_sku_upshelf":
                            VVICGetItemDetialReq upshelfReq = new VVICGetItemDetialReq();
                            upshelfReq.setItem_vid(itemVid);
                            VVICGetItemDetialResponse upshelfItemDetial = vvicApiSevImpl.getItemDetial(thirdPartySystem.getBizId(), upshelfReq);
                            if (null != upshelfItemDetial && upshelfItemDetial.getItem_list() != null) {
                                startSupply(thirdPartySystem, upshelfItemDetial.getItem_list().get(0));
                            }
                            rep = "success to item_sku_upshelf";
                            break;
                        //sku缺货
                        case "vvic_item_sku_lackstatus":
                            VVICGetItemDetialReq lackstatusReq = new VVICGetItemDetialReq();
                            lackstatusReq.setItem_vid(itemVid);
                            VVICGetItemDetialResponse lackstatusItemDetial = vvicApiSevImpl.getItemDetial(thirdPartySystem.getBizId(), lackstatusReq);
                            if (null != lackstatusItemDetial && lackstatusItemDetial.getItem_list() != null) {
                                lackstatusItemDetial.getItem_list().forEach(item -> updateStock(thirdPartySystem, item));
                            }
                            rep = "success";
                            break;
                        default:
                            rep = "Unexpected value: ".concat(type);
                    }
                    log.info("req:{}", rep);
                    return rep;
                });
    }

    //更新商品库存
    private void updateStock(ThirdPartySystem thirdPartySystem, VVICGetItemDetialResponse.Item item) {
        if (null != item && CollectionUtils.isNotEmpty(item.getSkuList())) {
            OpenSupplierUpdateStockReq req = new OpenSupplierUpdateStockReq();
            req.setBizSerialNumber(UUID.randomUUID().toString().replace("-", "").substring(0, 18));
            VVICBaseParam vvicBaseParam = JSON.parseObject(thirdPartySystem.getParams(), VVICBaseParam.class);
            String supplierId = vvicBaseParam.getSupplierId();
            req.setSupplierId(supplierId);
            req.setCustomWarehouseCode(vvicBaseParam.getWarehouse());
            req.setStockNum(0);
            List<VVICGetItemDetialResponse.Sku> skuList = item.getSkuList().stream().filter(sku -> sku.getIs_lack() == 1).collect(Collectors.toList());
            skuList.forEach(sku -> {
                req.setCustomSkuCode(sku.getSku_vid());
                try {
                    openSupplierProductRpc.updateStock(req);
                } catch (ServiceException e) {
                    log.error("更新库存失败：supplier: {}  supplierSkuId: {} message: {}", supplierId, sku.getSku_vid(), e.getMessage());
                }
            });
        }
    }

    //sku商品停止供应（下架）
    private void stopSupply(ThirdPartySystem thirdPartySystem, VVICGetItemDetialResponse.Item item) {
        OpenSupplierSkuStopSupplyReq req = new OpenSupplierSkuStopSupplyReq();
        VVICBaseParam vvicBaseParam = JSON.parseObject(thirdPartySystem.getParams(), VVICBaseParam.class);
        req.setSupplierId(vvicBaseParam.getSupplierId());
        if (null != item && CollectionUtils.isNotEmpty(item.getSkuList())) {
            List<VVICGetItemDetialResponse.Sku> skuList = item.getSkuList().stream().filter(sku -> sku.getStatus() == 0).collect(Collectors.toList());
            skuList.forEach(sku -> {
                req.setCustomSkuCode(sku.getSku_vid());
                openSupplierProductRpc.stopSupply(req);
            });
        }
    }

    //sku商品开始供应（上架）
    private void startSupply(ThirdPartySystem thirdPartySystem, VVICGetItemDetialResponse.Item item) {
        OpenSupplierSkuStartSupplyReq req = new OpenSupplierSkuStartSupplyReq();
        VVICBaseParam vvicBaseParam = JSON.parseObject(thirdPartySystem.getParams(), VVICBaseParam.class);
        req.setSupplierId(vvicBaseParam.getSupplierId());
        if (null != item && CollectionUtils.isNotEmpty(item.getSkuList())) {
            List<VVICGetItemDetialResponse.Sku> skuList = item.getSkuList().stream().filter(sku -> sku.getStatus() == 1).collect(Collectors.toList());
            skuList.forEach(sku -> {
                req.setCustomSkuCode(sku.getSku_vid());
                openSupplierProductRpc.startSupply(req);
            });
        }
    }

    //spu商品停止供应
    private void stopSpuSupply(ThirdPartySystem thirdPartySystem, String itemId) {
        OpenSupplierSpuOptSupplyStateReq req = new OpenSupplierSpuOptSupplyStateReq();
        VVICBaseParam vvicBaseParam = JSON.parseObject(thirdPartySystem.getParams(), VVICBaseParam.class);
        req.setSupplierId(vvicBaseParam.getSupplierId());
        req.setCustomCode(itemId);
        openSupplierProductRpc.stopSpuSupply(req);
    }

    //spu商品开始供应
    private void startSpuSupply(ThirdPartySystem thirdPartySystem, String itemId) {
        OpenSupplierSpuOptSupplyStateReq req = new OpenSupplierSpuOptSupplyStateReq();
        VVICBaseParam vvicBaseParam = JSON.parseObject(thirdPartySystem.getParams(), VVICBaseParam.class);
        req.setSupplierId(vvicBaseParam.getSupplierId());
        req.setCustomCode(itemId);
        openSupplierProductRpc.startSpuSupply(req);
    }

    //更新商品价格
    private void updatePrice(ThirdPartySystem thirdPartySystem, VVICGetItemDetialResponse.Item item) {
        if (null != item && CollectionUtils.isNotEmpty(item.getSkuList())) {
            OpenSupplierAdjustSupplyPriceReq req = new OpenSupplierAdjustSupplyPriceReq();
            JSONObject jsonObject = JSON.parseObject(thirdPartySystem.getParams());
            String supplierId = jsonObject.get("supplierId").toString();
            req.setSupplierId(supplierId);
            req.setCountry("CN");
            req.setSupplyPriceCurrency("CNY");

            item.getSkuList().forEach(sku -> {
                req.setCustomSkuCode(sku.getSku_vid());
                req.setSupplyPrice(sku.getPrice());
                //供货明细表
                List<OpenSupplierItemCreateCombineDetailInfo> detailInfos = new ArrayList<>();
                OpenSupplierItemCreateCombineDetailInfo info = new OpenSupplierItemCreateCombineDetailInfo();
                info.setCombineNum(1);
                info.setSupplyPriceCurrency("CNY");
                detailInfos.add(info);
                info.setSupplyPrice(sku.getPrice());
                req.setCombineDetails(detailInfos);
                try {
                    openSupplierProductRpc.adjustSupplyPrice(req);
                } catch (ServiceException e) {
                    log.error("更新商品价格：supplier: {}  supplierSkuId: {} message: {}", supplierId, sku.getSku_vid(), e.getMessage());
                }
            });
        }
    }

    private String readRequestBody(InputStream inputStream) throws IOException {
        char[] buffer = new char[1024];
        final StringBuilder out = new StringBuilder();
        try (Reader in = new InputStreamReader(inputStream, StandardCharsets.UTF_8)) {
            for (; ; ) {
                int rsz = in.read(buffer, 0, buffer.length);
                if (rsz < 0)
                    break;
                out.append(buffer, 0, rsz);
            }
        }
        return out.toString();
    }

    public ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }

    private static Map<String, String> toSignParam(VVICMessageReq req) {
        Map<String, String> params = Maps.newHashMap();
        params.put("msgId",req.getMsgId());
        params.put("appId",req.getAppId());
        params.put("timestamp",String.valueOf(req.getTimestamp()));
        params.put("type",req.getType());
        params.put("data", JSON.toJSONString(req.getData()));
        return params;
    }

    private boolean verificationSign(ThirdPartySystem thirdPartySystem, VVICMessageReq req) {
        VVICBaseParam vvicBaseParam = JSON.parseObject(thirdPartySystem.getParams(), VVICBaseParam.class);
        return VVICSignUtil.openVerificationSign(vvicBaseParam.getApp_secret(),req.getSign(),toSignParam(req));
    }

    public static void main(String[] args) {
        VVICMessageReq vvicMessageReq = new VVICMessageReq();
        VVICMessageReq.Data data = new VVICMessageReq.Data();
        List<String> list = new ArrayList<>();
        list.add("list_grid_image");
        list.add("item_view_image");
        data.setChangedFields(list);
        data.setItemVid("643c98382f999500087496bf");
        data.setTime(1681697676968L);
        data.setType("vvic_item_update");
        vvicMessageReq.setData(data);
        vvicMessageReq.setMsgId("AC110BD900082A18F23C549872A9F180");
        vvicMessageReq.setAppId("2823100221003210");
        vvicMessageReq.setSign("6b666cf39ad77c56ff4fcdbfb9ff0367cf0aff5be6889245f471f80ef92ff74d");
        vvicMessageReq.setTimestamp(1681697677030L);
        vvicMessageReq.setType("VVIC_MESSAGE_PRODUCT_MODIFY");
        boolean b = VVICSignUtil.openVerificationSign("759c4ec8e0c280e0a54916d5e78b21f2", vvicMessageReq.getSign(), toSignParam(vvicMessageReq));
        System.out.println(b);
    }
}
