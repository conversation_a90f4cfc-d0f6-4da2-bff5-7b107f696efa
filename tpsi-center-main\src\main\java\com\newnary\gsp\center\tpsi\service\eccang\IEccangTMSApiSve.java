package com.newnary.gsp.center.tpsi.service.eccang;

import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderDetailInfo;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;

/**
 *
 * <AUTHOR>
 * @date 2022/3/30
 */
public interface IEccangTMSApiSve {

    EcCangApiBaseResult<Object> createOrder(ThirdPartySystem thirdPartySystem, String transportOrderId, DeliveryOrderDetailInfo deliveryOrderDetailInfo, String param);

    EcCangApiBaseResult<Object> queryLogisticsInfo(ThirdPartySystem thirdPartySystem, String transportOrderId, String param);

    EcCangApiBaseResult<Object> queryLogisticsTrackInfo(ThirdPartySystem thirdPartySystem, String code, String param);

    EcCangApiBaseResult<Object> queryOrderInfo(ThirdPartySystem thirdPartySystem, String transportOrderId, String param);

    EcCangApiBaseResult<Object> cancelOrder(ThirdPartySystem thirdPartySystem, String transportOrderId, String param);

}
