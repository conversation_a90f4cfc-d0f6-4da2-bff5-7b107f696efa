package com.newnary.gsp.center.tpsi.app.listener.mq;

import com.newnary.gsp.center.tpsi.app.service.wms.StockSyncMgmtApp;
import com.newnary.gsp.center.tpsi.infra.mq.consumer.WmsStockChangeSyncConsumer;
import com.newnary.messagebody.wms.WMSBatchStockTopic;
import com.newnary.messagebody.wms.mo.StockChangeMO;
import com.newnary.mq.starter.consumer.AbstractMQProcessor;
import com.newnary.mq.starter.consumer.MQMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class WmsStockChangeSyncProcessor extends AbstractMQProcessor<StockChangeMO> {

    @Resource
    private StockSyncMgmtApp stockSyncMgmtApp;

    @Override
    public boolean doProcess(MQMessage<StockChangeMO> mqMessage) {
        StockChangeMO mo = mqMessage.getContent();

        log.info("wms库存变动同步供应商商品库存消息消费开始：supplierSkuId: {}", mo.getSupplierSkuId());
        try {
            // 消息处理
            stockSyncMgmtApp.syncSupplierSkuStock(mo.getSupplierSkuId(),mo.getWarehouseId(),mo.getShipperId());
        } catch (Exception e) {
            log.info("wms库存变动同步供应商商品库存消息消费失败！purchaseOrderId={}, e={}", mo.getSupplierSkuId(), e);
        }
        return true;
    }

    @Override
    public Class<?> consumerClz() {
        return WmsStockChangeSyncConsumer.class;
    }

    @Override
    public String tag() {
        return WMSBatchStockTopic.Tag.USABLE;
    }
}
