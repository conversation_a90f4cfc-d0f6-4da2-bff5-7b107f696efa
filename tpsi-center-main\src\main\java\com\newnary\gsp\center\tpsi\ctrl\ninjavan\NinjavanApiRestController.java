//package com.newnary.gsp.center.tpsi.ctrl.ninjavan;
//
//import com.newnary.api.base.common.CommonResponse;
//import com.newnary.gsp.center.tpsi.api.ninjavan.NinjavanApi;
//import com.newnary.gsp.center.tpsi.api.ninjavan.request.CancelOrderNinJavanCommand;
//import com.newnary.gsp.center.tpsi.api.ninjavan.request.CreateOrderNinJavanCommand;
//import com.newnary.gsp.center.tpsi.api.ninjavan.request.SheetOrderNinJavanCommand;
//import com.newnary.gsp.center.tpsi.api.ninjavan.response.NinJavanOrderResp;
//import com.newnary.gsp.center.tpsi.api.ninjavan.response.NinJavanPrintSheetResp;
//import com.newnary.gsp.center.tpsi.service.ILogisticsApiSve;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//
///**
// * @Author: WangRuTao
// * @CreateTime: 2023-8-9
// */
//@RestController
//@Slf4j
//public class NinjavanApiRestController implements NinjavanApi {
//
//    @Resource
//    private ILogisticsApiSve iLogisticsApiSve;
//
//
//    @Override
//    public CommonResponse<NinJavanOrderResp> createOrder(CreateOrderNinJavanCommand command) {
//        return CommonResponse.success(iLogisticsApiSve.createOrder(command));
//    }
//
//    @Override
//    public CommonResponse<Void> doCancel(CancelOrderNinJavanCommand command) {
//        iLogisticsApiSve.doCancel(command);
//        return CommonResponse.successWithoutBody();
//    }
//
//    @Override
//    public CommonResponse<NinJavanPrintSheetResp> printSheet(SheetOrderNinJavanCommand command) {
//        return CommonResponse.success(iLogisticsApiSve.printSheet(command));
//    }
//}
