package com.newnary.gsp.center.tpsi.service.lingxing.request;

import lombok.Data;

import java.util.List;

@Data
public class QueryLingXingProductListRequest {

    //偏移量（页数）
    private Integer  offset;
    //每页数量
    private Integer  length;
    //更新时间-开始时间【时间戳，单位：秒，左闭右开】
    private Integer  update_time_start;
    //更新时间-结束时间【时间戳，单位：秒，左闭右开】
    private Integer  update_time_end;
    //创建时间-开始时间【时间戳，单位：秒，左闭右开】
    private Integer  create_time_start;
    //创建时间-结束时间【时间戳，单位：秒，左闭右开】
    private Integer  create_time_end;
    //本地产品sku
    private List<String> sku_list;
}
