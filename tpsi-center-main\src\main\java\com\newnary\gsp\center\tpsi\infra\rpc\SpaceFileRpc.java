package com.newnary.gsp.center.tpsi.infra.rpc;

import com.google.common.collect.Lists;
import com.newnary.api.base.common.CommonResponse;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.distributed.tools.cache.DistributedCache;
import com.newnary.gsp.center.basicdata.api.resources.feign.ResourcesFeignApi;
import com.newnary.gsp.center.basicdata.api.resources.feign.SpaceFileFeignApi;
import com.newnary.gsp.center.basicdata.api.resources.request.ResourcesCreateCommand;
import com.newnary.gsp.center.basicdata.api.resources.request.SpaceFileCreateCommand;
import com.newnary.gsp.center.basicdata.api.resources.response.ResourcesDTO;
import com.newnary.gsp.center.basicdata.api.resources.response.SpaceFileDTO;
import com.newnary.gsp.center.basicdata.api.resources.response.SpaceFileParamDTO;
import com.newnary.oss.starter.constants.BucketACL;
import com.newnary.oss.starter.provider.OSSClient;
import com.newnary.oss.starter.request.UploadRequest;
import com.newnary.oss.starter.response.UploadResponse;
import com.newnary.spring.cloud.extend.SpringContext;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 文件服务器RPC
 */
@Component
public class SpaceFileRpc   {

    @Resource
    private SpaceFileFeignApi spaceFileFeignApi;

    @Resource
    private ResourcesFeignApi resourcesFeignApi;

    @Resource
    private OSSClient ossClient;


    public Map<String, String> queryFileId4FileUrlMapByFileId(List<String> fileIds) {

        CommonResponse<List<SpaceFileDTO>> result = spaceFileFeignApi.queryByFileIds(fileIds);
        List<SpaceFileDTO> spaceFileDTOs = result.mustSuccessOrThrowOriginal();
        return spaceFileDTOs.stream().collect(Collectors.toMap(SpaceFileDTO::getFileId, SpaceFileDTO::getLink));
    }


    public String createSpaceFile(String fileName, byte[] data, String extensionType) {
        int maxLength = 20 * 1024 * 1024;
        if (data.length > maxLength) {
            throw new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "文件大小不能超过20M");
        }
        // 读取数据
        if (data.length == 0) {
            throw new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "文件为空");
        }

        // 生成指纹
        String fingerprint = genFingerprint(data);


        // 执行上传
        String link = doUpload(data, fingerprint, extensionType,fileName);

        // 创建空间文件
        String fileId = createSpaceFile(fileName, extensionType, null, fingerprint, link, data.length);
        return fileId;
    }

    private String doUpload(byte[] data, String fingerprint, String extension,String fileName) {
        String link;

        // 文件上传
        UploadRequest uploadRequest = new UploadRequest(
                buildFullPath(fileName + extension),
                new ByteArrayInputStream(data),
                BucketACL.PUBLIC_READ
        );
        UploadResponse uploadResponse = ossClient.upload(uploadRequest);
        link = uploadResponse.getUrl();
        // 资源存储
        createResources(fingerprint, uploadResponse.getUrl(), data.length);

        return link;
    }


    private String getCurrentDateStr() {
        return DateFormatUtils.format(new Date(), "yyyyMMdd");
    }


    private String buildFullPath(String key) {
        return "common/space/" + getCurrentDateStr() + "/" + System.currentTimeMillis() + "/" + key;
    }


    private void createResources(String fingerprint, String link, Integer size) {
        ResourcesCreateCommand command = new ResourcesCreateCommand();
        command.fingerprint = fingerprint;
        command.link = link;
        command.size = size;
        resourcesFeignApi.create(command).assertSuccess();
    }


    private String genFingerprint(byte[] data) {
        return DigestUtils.md5Hex(data);
    }


    private String createSpaceFile(
            String fileName, String extensionSuffix, Map<String, String> paramMap,
            String fingerprint, String link, Integer size) {

        List<SpaceFileParamDTO> params = Lists.newArrayList();
        if (MapUtils.isNotEmpty(paramMap)) {
            paramMap.forEach((k, v) -> {
                SpaceFileParamDTO dto = new SpaceFileParamDTO();
                dto.name = k;
                dto.value = v;
                params.add(dto);
            });
        }

        SpaceFileCreateCommand command = new SpaceFileCreateCommand();
        command.name = fileName;
        command.params = params;
        command.extensionSuffix = extensionSuffix;
        command.fingerprint = fingerprint;
        command.link = link;
        command.size = size;
        command.setOssKey(buildFullPath(fingerprint + extensionSuffix));

        return spaceFileFeignApi.create(command)
                .mustSuccessOrElseThrow(status -> new ServiceException(CommonErrorInfo.ERROR_104_SERVICE_CALL_ERROR, status.getMessage()));
    }


}
