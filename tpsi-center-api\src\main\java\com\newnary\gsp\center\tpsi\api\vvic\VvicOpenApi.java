package com.newnary.gsp.center.tpsi.api.vvic;

import com.newnary.api.base.common.CommonResponse;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @Author: jack
 * @CreateTime: 2023-4-13
 */
@RequestMapping("tpsi-center/vvic")
public interface VvicOpenApi {

    @PostMapping("vvic-webhooks/{tenantID}/{bizId}")
    CommonResponse<String> vvicWebhooks(@PathVariable(value = "tenantID") String tenantID,@PathVariable(value = "bizId") String bizId);

}
