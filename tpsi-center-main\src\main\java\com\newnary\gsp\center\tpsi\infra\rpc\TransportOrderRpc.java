package com.newnary.gsp.center.tpsi.infra.rpc;

import com.newnary.api.base.common.PageList;
import com.newnary.gsp.center.logistics.api.delivery.enums.TransportExceptionType;
import com.newnary.gsp.center.logistics.api.delivery.enums.TransportPushState;
import com.newnary.gsp.center.logistics.api.delivery.feign.TransportOrderFeignApi;
import com.newnary.gsp.center.logistics.api.delivery.request.*;
import com.newnary.gsp.center.logistics.api.delivery.response.TransportOrderDetailInfo;
import com.newnary.gsp.center.logistics.api.delivery.response.TransportOrderLiteInfo;
import com.newnary.gsp.center.logistics.api.delivery.response.TransportOrderPackageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/1
 */
@Component
@Slf4j
public class TransportOrderRpc {

    @Resource
    private TransportOrderFeignApi transportOrderFeignApi;

    public List<TransportOrderLiteInfo> getListWithDelivery(TransportOrderWithDeliveryQueryCommand command) {
        return transportOrderFeignApi.getListWithDelivery(command).mustSuccessOrThrowOriginal();
    }

    public PageList<TransportOrderDetailInfo> pageQuery(TransportOrderPageQueryCommand command) {
        return transportOrderFeignApi.pageQuery(command).mustSuccessOrThrowOriginal();
    }

    public void thirdPushed(TransportOrderThirdPushedCommand command) {
        transportOrderFeignApi.thirdPushed(command).mustSuccessOrThrowOriginal();
    }

    public void updateTrack(TransportOrderUpdateTrackCommand command) {
        transportOrderFeignApi.updateTrack(command).mustSuccessOrThrowOriginal();
    }

    public void updateLabel(TransportOrderUpdateLabelCommand command) {
        transportOrderFeignApi.updateLabel(command).mustSuccessOrThrowOriginal();
    }

    public void updateProperty(TransportOrderUpdatePropertyCommand command) {
        transportOrderFeignApi.updateProperty(command).mustSuccessOrThrowOriginal();
    }

    public void updateReferenceId(String transportOrderPackageId, String referenceId) {
        TransportPackageUpdateReferenceIdCommand command = new TransportPackageUpdateReferenceIdCommand();
        command.setTransportOrderPackageId(transportOrderPackageId);
        command.setReferenceId(referenceId);
        transportOrderFeignApi.updateReferenceId(command).mustSuccessOrThrowOriginal();
    }

    public void confirmCancel(TransportOrderConfirmCancelCommand command) {
        transportOrderFeignApi.confirmCancel(command).mustSuccessOrThrowOriginal();
    }

    public void doCancel(TransportOrderDoCancelCommand command) {
        transportOrderFeignApi.doCancel(command).mustSuccessOrThrowOriginal();
    }

    public void rejectCancel(TransportOrderRejectCancelCommand command) {
        transportOrderFeignApi.rejectCancel(command).mustSuccessOrThrowOriginal();
    }

    public TransportOrderPackageInfo loadTransportPackage(String transportOrderPackageId) {
        TransportOrderPackageIdCommand command = new TransportOrderPackageIdCommand();
        command.setTransportOrderPackageId(transportOrderPackageId);
        return transportOrderFeignApi.getPackageInfoDetail(command).mustSuccessOrThrowOriginal();
    }

    public void resetPushInfo(String transportOrderPackageId, TransportPushState transportPushState, String thirdCancelReason) {
        TransportOrderThirdResetV2Command command = new TransportOrderThirdResetV2Command();
        command.setTransportOrderPackageId(transportOrderPackageId);
        command.setState(transportPushState);
        command.setThirdCancelReason(thirdCancelReason);
        transportOrderFeignApi.resetPushThirdParty(command).mustSuccessOrThrowOriginal();
    }

    public void localReceive(String transportOrderPackageId) {
        TransportPackageOperateCommand command = new TransportPackageOperateCommand();
        command.setTransportOrderPackageId(transportOrderPackageId);
        transportOrderFeignApi.receivedByPackage(command);
    }

    public void localPacked(String transportOrderPackageId) {
        TransportPackageOperateCommand command = new TransportPackageOperateCommand();
        command.setTransportOrderPackageId(transportOrderPackageId);
        transportOrderFeignApi.packedByPackage(command);
    }

    public void confirmDelivery(String transportOrderPackageId) {
        TransportPackageOperateCommand command = new TransportPackageOperateCommand();
        command.setTransportOrderPackageId(transportOrderPackageId);
        transportOrderFeignApi.deliveredByPackage(command);
    }

    public void localTransit(String transportOrderPackageId) {
        TransportPackageOperateCommand command = new TransportPackageOperateCommand();
        command.setTransportOrderPackageId(transportOrderPackageId);
        transportOrderFeignApi.transitByPackage(command);
    }

//    public void localDispatch(String transportOrderId, String ninjavan) {
//        TransportOrderUpdateCommand command = new TransportOrderUpdateCommand();
//        command.setTransportOrderId(transportOrderId);
//        command.setTransportOrderState(TransportOrderState.DISPATCH.name());
//        transportOrderFeignApi.updateState(command);
//    }

    public void finishedByPackage(String transportOrderPackageId) {
        TransportPackageOperateCommand command = new TransportPackageOperateCommand();
        command.setTransportOrderPackageId(transportOrderPackageId);
        transportOrderFeignApi.finishedByPackage(command).mustSuccessOrThrowOriginal();
    }

    public TransportOrderDetailInfo getDetail(String transportOrderId) {
        TransportOrderIdCommand command = new TransportOrderIdCommand();
        command.setTransportOrderId(transportOrderId);
        return transportOrderFeignApi.getDetail(command).mustSuccessOrThrowOriginal();
    }

    public String getOrderPackageIdByLastLogisticsNum(String trackNum) {
        String transportOrderPackageId = "";
        try {
            transportOrderPackageId = transportOrderFeignApi.getTransportPackageIdByTrackNum(trackNum).mustSuccessOrThrowOriginal();
        } catch (Exception e) {
            log.error("未找到对应单号", e);
        }
        return transportOrderPackageId;
    }

    public void pushException(String transportOrderPackageId, TransportExceptionType transportExceptionType) {
        TransportPackagePushExceptionCommand command = new TransportPackagePushExceptionCommand();
        command.setTransportOrderPackageId(transportOrderPackageId);
        command.setExceptionType(transportExceptionType);
        transportOrderFeignApi.submitException(command);
    }

    public void dealException(String transportOrderPackageId) {
        TransportPackageOperateCommand command = new TransportPackageOperateCommand();
        command.setTransportOrderPackageId(transportOrderPackageId);
        transportOrderFeignApi.dealException(command);
    }

}
