package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.shopee;

import lombok.Data;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingShopeeProductDetailInfoResponse {

    /**
     * 商品状态码
     * (200:正常 100:商品未收录)
     */
    private String code;

    /**
     * 商品id
     */
    private String pid;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品默认价
     */
    private String price;

    /**
     * 商品最低价
     */
    private String min_price;

    /**
     * 商品最高价
     */
    private String max_price;

    /**
     * 商品月销量
     */
    private String sold;

    /**
     * 商品总库存数
     */
    private String stock;

    /**
     * 商品总销量
     */
    private String historical_sold;

    /**
     * 商品评分
     */
    private String rating;

    /**
     * 商品评分数
     */
    private String rating_count;

    /**
     * 商品浏览人数
     */
    private String view_count;

    /**
     * 商品喜欢人数
     */
    private String liked_count;

    /**
     * 商品预计到货时间
     */
    private String estimated_days;

    /**
     * 商品是否热销
     * 0:非热销   1:热销
     */
    private String is_hot_sales;

    /**
     * 商品是否虾皮优选
     * 0:非优选   1:优选
     */
    private String is_shopee_verified;

    /**
     * 商品主图
     */
    private String image;

    /**
     * 商品上架时间
     */
    private String gen_time;

    /**
     * 商品所属店铺id
     */
    private String shop_id;

    /**
     * 商品所属店铺是否官方店铺
     * 0:否   1:是
     */
    private String is_official_shop;

    /**
     * 商品类目路径
     */
    private String cid_namespace;

    /**
     * 商品类目名称路径
     */
    private String cname_namespace;

    /**
     * 商品状态
     */
    private String status;

    /**
     * 商品是否存在
     * （0存在，1不存在）
     */
    private String not_exist;

    /**
     * 商品最近抓取时间
     */
    private String last_modi_time;

    /**
     * 店铺所在地
     */
    private String shop_location;

    /**
     * 商品链接
     */
    private String product_url;

}
