package com.newnary.gsp.center.tpsi.service.vvic.impl;

import com.alibaba.fastjson.JSON;
import com.newnary.gsp.center.tpsi.infra.client.vvic.VVICClient;
import com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.VVICBaseApiResult;
import com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.request.*;
import com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.response.*;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.service.impl.SystemClientSve;
import com.newnary.gsp.center.tpsi.service.vvic.VVICApiSev;
import com.newnary.spring.cloud.anno.Validation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Component
public class VVICApiSevImpl extends SystemClientSve implements VVICApiSev {

    @Validation
    @Override
    public VVICGetItemListResponse getItemList(String thirdPartySystemId, VVICGetItemListReq req) {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        VVICClient vvicClient = new VVICClient(thirdPartySystem.getParams());
        VVICBaseApiResult<String> itemList = vvicClient.getItemList(req);
        if (ObjectUtils.isNotEmpty(itemList) && StringUtils.isNotBlank(itemList.getData())) {
            return buildVVICGetItemListResponse(itemList);
        }
        return new VVICGetItemListResponse();
    }

    @Override
    @Validation
    public VVICGetItemDetialResponse getItemDetial(String thirdPartySystemId, VVICGetItemDetialReq req) {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        VVICClient vvicClient = new VVICClient(thirdPartySystem.getParams());
        VVICBaseApiResult<String> item = vvicClient.getItemDetail(req);
        if (ObjectUtils.isNotEmpty(item) && StringUtils.isNotBlank(item.getData())) {
            VVICGetItemDetialResponse vvicGetItemDetialResponse = JSON.parseObject(item.getData(), VVICGetItemDetialResponse.class);
            if (vvicGetItemDetialResponse != null && CollectionUtils.isNotEmpty(vvicGetItemDetialResponse.getItem_list())) {
                List<VVICGetItemDetialResponse.Item> itemList = vvicGetItemDetialResponse.getItem_list();
                Iterator<VVICGetItemDetialResponse.Item> iterator = itemList.iterator();
                while (iterator.hasNext()) {
                    VVICGetItemDetialResponse.Item next = iterator.next();
                    String desc = next.getDesc();
                    if (StringUtils.isNotBlank(desc)) {
                        if (!(desc.contains("https") || desc.contains("http"))) {
                            if (desc.contains("<img") || desc.contains("<div") || desc.contains("<p>")) {
                                String replace = desc.replace("src=\"", "src=\"http:");
                                next.setDesc(replace);
                            }else {
                                iterator.remove();
                            }
                        } else if (desc.contains("链接") || desc.contains("提取码") || desc.contains("网盘")) {
                            Document parse = Jsoup.parse(desc);//html为内容
                            Elements imgs = parse.getElementsByTag("img");
                            if (CollectionUtils.isNotEmpty(imgs)) {
                                StringBuilder stringBuilder = new StringBuilder();
                                imgs.forEach(img -> {
                                    String src = img.attr("src");
                                    Pattern pattern = Pattern.compile("\\w+.(jpg|png|jpeg|bmp|webp|JPG|PNG|JPEG|BMP|WEBP)$");
                                    Matcher matcher = pattern.matcher(src);
                                    if (matcher.find()) {
                                        String outerHtml = img.outerHtml();
                                        if (!(outerHtml.contains("http") || outerHtml.contains("https"))) {
                                            stringBuilder.append(outerHtml.replace("src=\"", "src=\"http:"));
                                        } else {
                                            stringBuilder.append(outerHtml);
                                        }
                                    }
                                });
                                next.setDesc(stringBuilder.toString());
                            } else {
                                iterator.remove();
                            }
                        } else {
                            if (!(desc.contains("src=\"https") || desc.contains("src=\"http"))) {
                                desc = desc.replace("src=\"", "src=\"http:");
                            }
                            next.setDesc(desc);
                        }
                    }
                }
                vvicGetItemDetialResponse.setItem_list(itemList);
                return vvicGetItemDetialResponse;
            }
        }
        return new VVICGetItemDetialResponse();
    }

    @Override
    @Validation
    public VVICGetItemStatusResponse getItemStatus(String thirdPartySystemId, VVICGetItemStatusReq req) {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        VVICClient vvicClient = new VVICClient(thirdPartySystem.getParams());
        VVICBaseApiResult<String> item = vvicClient.getItemStatus(req);
        if (ObjectUtils.isNotEmpty(item) && StringUtils.isNotBlank(item.getData())) {
            return JSON.parseObject(item.getData(), VVICGetItemStatusResponse.class);
        }
        return new VVICGetItemStatusResponse();
    }

    @Override
    @Validation
    public VVICCreateOrderResponse createOrder(String thirdPartySystemId, VVICCreateOrderReq req) {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        VVICClient vvicClient = new VVICClient(thirdPartySystem.getParams());
        VVICBaseApiResult<String> result = vvicClient.createOrder(req);
        if (ObjectUtils.isNotEmpty(result) && StringUtils.isNotBlank(result.getData())) {
            return JSON.parseObject(result.getData(), VVICCreateOrderResponse.class);
        }
        return new VVICCreateOrderResponse();
    }

    @Override
    @Validation
    public VVICCancelOrderResponse cancelOrder(String thirdPartySystemId, VVICCancelOrderReq req) {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        VVICClient vvicClient = new VVICClient(thirdPartySystem.getParams());
        VVICBaseApiResult<String> item = vvicClient.cancelOrder(req);
        if (ObjectUtils.isNotEmpty(item) && StringUtils.isNotBlank(item.getData())) {
            return JSON.parseObject(item.getData(), VVICCancelOrderResponse.class);
        }
        return new VVICCancelOrderResponse();
    }

    @Override
    @Validation
    public VVICGetOrderStatusResponse getOrderStatus(String thirdPartySystemId, VVICGetOrderStatusReq req) {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        VVICClient vvicClient = new VVICClient(thirdPartySystem.getParams());
        VVICBaseApiResult<String> item = vvicClient.getOrderStatus(req);
        if (ObjectUtils.isNotEmpty(item) && StringUtils.isNotBlank(item.getData())) {
            return JSON.parseObject(item.getData(), VVICGetOrderStatusResponse.class);
        }
        return new VVICGetOrderStatusResponse();
    }

    @Override
    public VVICGetOrderListResponse getOrderList(String thirdPartySystemId, VVICGetOrderListReq req) {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        VVICClient vvicClient = new VVICClient(thirdPartySystem.getParams());
        VVICBaseApiResult<String> item = vvicClient.getOrderList(req);
        if (ObjectUtils.isNotEmpty(item) && StringUtils.isNotBlank(item.getData())) {
            return JSON.parseObject(item.getData(), VVICGetOrderListResponse.class);
        }
        return new VVICGetOrderListResponse();
    }

    @Override
    public VVICGetVasResponse getVas(String thirdPartySystemId, VVICGetVasReq req) {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        VVICClient vvicClient = new VVICClient(thirdPartySystem.getParams());
        VVICBaseApiResult<String> item = vvicClient.getVas(req);
        if (ObjectUtils.isNotEmpty(item) && StringUtils.isNotBlank(item.getData())) {
            return JSON.parseObject(item.getData(), VVICGetVasResponse.class);
        }
        return new VVICGetVasResponse();
    }

    @Override
    public VVICGetExpressResponse getExpressList(String thirdPartySystemId, VVICGetExpressReq req) {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        VVICClient vvicClient = new VVICClient(thirdPartySystem.getParams());
        VVICBaseApiResult<String> item = vvicClient.getExpressList(req);
        if (ObjectUtils.isNotEmpty(item) && StringUtils.isNotBlank(item.getData())) {
            return JSON.parseObject(item.getData(), VVICGetExpressResponse.class);
        }
        return new VVICGetExpressResponse();
    }

    @Override
    public VVICGetShopItemListResponse getShopItemList(String thirdPartySystemId, VVICGetShopItemListReq req) {
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        VVICClient vvicClient = new VVICClient(thirdPartySystem.getParams());
        VVICBaseApiResult<String> item = vvicClient.getShopItemList(req);
        if (ObjectUtils.isNotEmpty(item) && StringUtils.isNotBlank(item.getData())) {
            return JSON.parseObject(item.getData(), VVICGetShopItemListResponse.class);
        }
        return new VVICGetShopItemListResponse();
    }

    private VVICGetItemListResponse buildVVICGetItemListResponse(VVICBaseApiResult<String> itemList) {
        return JSON.parseObject(itemList.getData(), VVICGetItemListResponse.class);
    }
}
