package com.newnary.gsp.center.tpsi.ctrl.mabang;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.gsp.center.tpsi.api.mabang.MaBangApi;
import com.newnary.gsp.center.tpsi.api.mabang.request.*;
import com.newnary.gsp.center.tpsi.api.mabang.response.MaBangLogisticsChannelList;
import com.newnary.gsp.center.tpsi.api.mabang.response.MaBangWarehouseList;
import com.newnary.gsp.center.tpsi.service.ISyncProductBizSve;
import com.newnary.gsp.center.tpsi.service.ISyncStockBizSve;
import com.newnary.gsp.center.tpsi.service.mabang.*;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class MaBangApiImpl implements MaBangApi {

    @Resource
    private IMaBangDevApiSve maBangDevApiSve;
    @Resource
    private IMaBangStockApiSve maBangStockApiSve;
    @Resource
    private IMaBangWarehouseApiSve maBangWarehouseApiSve;
    @Resource
    private IMaBangOrderApiSve maBangOrderApiSve;
    @Resource
    private IMaBangSystemService maBangSystemService;
    @Resource
    private IMaBangWlApiSve maBangWlApiSve;

    @Resource
    private ISyncProductBizSve syncProductBizSve;

    @Resource
    private ISyncStockBizSve syncStockBizSve;

    @Override
    public CommonResponse<Void> syncProductFromMaBang(SyncProductFromMaBangCommand req) {
        syncProductBizSve.syncProductFromMaBang(req);
        return CommonResponse.successWithoutBody();
    }

    @Override
    public CommonResponse<String> pushOrder2MaBang(PushOrder2MaBangCommand req) {
        return CommonResponse.success(
                maBangOrderApiSve.orderDoCreateOrder(req)
        );
    }

    @Override
    public CommonResponse<Void> syncStockQuantityFromMaBang(SyncStockQuantityFromMaBangCommand req) {
        syncStockBizSve.syncStockQuantityFromMaBang(req);
        return CommonResponse.successWithoutBody();
    }

    @Override
    public CommonResponse<MaBangWarehouseList> syncWarehouseFromMaBang(String thirdPartySystemId) {
        return CommonResponse.success(
                maBangSystemService.sysGetWarehouseList(thirdPartySystemId)
        );
    }

    @Override
    public CommonResponse<MaBangLogisticsChannelList> syncLogisticschannelFromMaBang(String thirdPartySystemId) {
        return CommonResponse.success(
                maBangWlApiSve.wlGetMylogisticschannel(thirdPartySystemId)
        );
    }

    @Override
    public CommonResponse<String> doDeliverOrderInMaBang(DoDeliverOrderInMaBangCommand req) {
        return CommonResponse.success(
                maBangOrderApiSve.orderDoDeliverOrder(req)
        );
    }

    @Override
    public CommonResponse<String> syncToProductLibraryV2(SyncToProductLibraryV2Command req) {
        return CommonResponse.success(
                maBangDevApiSve.devSyncToProductLibrary(req)
        );
    }

    @Override
    public CommonResponse<Void> syncOrderFromMaBang(SyncOrderFromMaBangCommand req) {
        maBangOrderApiSve.orderGetOrderList(req);
        return CommonResponse.successWithoutBody();
    }

/*

    @Override
    public CommonResponse<String> warehouseDoAddStorage(String thirdPartySystemId, String context) {
        return CommonResponse.success(
                maBangWarehouseApiSve.warehouseDoAddStorage(thirdPartySystemId, context)
        );
    }

    @Override
    public CommonResponse<Void> orderGetOrderList(String thirdPartySystemId, String context) {

    }

    }*/

}
