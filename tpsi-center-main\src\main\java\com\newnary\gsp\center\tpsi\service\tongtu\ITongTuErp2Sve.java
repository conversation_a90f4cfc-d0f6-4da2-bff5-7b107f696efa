package com.newnary.gsp.center.tpsi.service.tongtu;

import com.newnary.gsp.center.tpsi.api.tongtu.request.SyncStockFromTongTuCommand;

public interface ITongTuErp2Sve {

    Integer stocksQuery(SyncStockFromTongTuCommand req);

    /**
     * 仓库列表查询
     * @param thirdPartySystemId
     * @return 返回结果列表转成的字符串
     */
    String warehouseQuery(String thirdPartySystemId);

    String ordersQuery(String thirdPartySystemId, String context);

    String orderImport(String thirdPartySystemId, String context);
}
