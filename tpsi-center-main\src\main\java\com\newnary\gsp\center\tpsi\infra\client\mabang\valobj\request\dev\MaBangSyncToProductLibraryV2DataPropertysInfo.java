package com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.dev;

import lombok.Data;

import java.util.List;

@Data
public class MaBangSyncToProductLibraryV2DataPropertysInfo {

    /**
     * 库存仓库
     */
    private String inventoryWarehouse;

    /**
     * 关键字
     */
    private String keywords;

    /**
     * 子SKU
     */
    private String sku;

    /**
     * 多属性商品备注
     */
    private String productremark;

    /**
     * 子标题
     */
    private String title;

    /**
     * 属性信息
     */
    private List<MaBangSyncToProductLibraryV2DataPropertysInfoPropertyInfo> property;

    /**
     * 多属性尺寸信息
     */
    private MaBangSyncToProductLibraryV2DataSize size;

    /**
     *
     */
    private MaBangSyncToProductLibraryV2DataPropertysInfoSales sales;

    /**
     * 图片
     */
    private String image;

    /**
     * 价格
     */
    private String price;

    /**
     * 库存
     */
    private String quantity;

    /**
     * 重量
     */
    private String weight;

    private String UENumber;

    private String UENumberType;
}
