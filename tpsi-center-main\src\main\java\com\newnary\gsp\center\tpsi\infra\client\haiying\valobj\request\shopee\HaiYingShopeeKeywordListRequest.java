package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.shopee;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingShopeeKeywordListRequest {

    /**
     * 站点(默认:马来西亚站)
     */
    @NotNull(message = "站点不能为空")
    private String station;

    /**
     * 当前页码(int 型)(默认第一页)
     */
    private String current_page;

    /**
     * 每一页的商品数(默认海鹰设置1000)(int 型)
     * 数值范围[1-5000]
     */
    private String page_size;

    /**
     * 关键词(string型)
     */
    private String keyword;

    /**
     * 搜索量起始值(int 型)
     */
    private String search_volume_start;

    /**
     * 搜索量结束值(int 型)
     */
    private String search_volume_end;

    /**
     * 出价起始值(double型)
     */
    private String recommend_price_start;

    /**
     * 出价结束值(double型)
     */
    private String recommend_price_end;

    /**
     * 搜索类型
     * 1: 精确搜索
     * 2: 模糊搜索
     * 3: 分词搜索
     */
    private String search_type;

    /**
     * 排序方式:
     * search_volume(搜索量)
     * recommend_price(价格)
     * update_time(最新更新时间)
     */
    private String order_by;

    /**
     * 排序类型:
     * ASC 升序
     * DESC 降序
     */
    private String order_by_type;

}
