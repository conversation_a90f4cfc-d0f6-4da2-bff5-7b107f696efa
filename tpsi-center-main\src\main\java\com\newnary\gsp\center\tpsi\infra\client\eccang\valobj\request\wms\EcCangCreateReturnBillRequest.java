package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.wms;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Getter
@Setter
public class EcCangCreateReturnBillRequest {
    @NotNull(message = "跟踪号不能为空")
    private String tracking_no;
    @NotNull(message = "仓库编码不能为空")
    private String warehouse_code;
    @NotNull(message = "退件类型不能为空")
    private String return_type;
    private String verify;
    private String reference_no;
    private String order_code;
    private String claim_code;
    private Date expected_date;
    private String return_desc;
    private String operation_desc;
    private String buyer_name;
    private String buyers_ein;
    @NotNull(message = "退件产品明细不能为空")
    private Items items;
    private Images images;

    public static class Items{
        @NotNull(message = "产品sku不能为空")
        public String product_sku;
        @NotNull(message = "产品数量不能为空")
        public Integer quantity;
        @NotNull(message = "产品处理方式不能为空")
        public String process;
        public String note;

    }

    public enum Process{
        RE_SELF("1"),RETURN_TO_CHINA("2"),REJECTS("3"),DESTROY("4"),TOCHECKED("5"),BID_CHANG("6"),PRODUCT_UPGRADE("8");
        private String code;
        private Process(String code){
            this.code = code;
        }
    }

    public static class Images{
        @NotNull(message = "图片类型不能为空")
        public String file_type;
        @NotNull(message = "base64图片数据流不能为空")
        public String file_data;
    }
}
