package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp;

import lombok.Data;

@Data
public class EcCangERPGetProductListRequest {

    /**
     * -	产品SKU 如：["SKU1","SKU2"] ，最大1000个SKU或者 SKU1
     */
    private String productSku;

    /**
     * 产品SKU模糊查询
     */
    private String productSkuLike;

    /**
     * 产品款式代码
     */
    private String productSpu;

    /**
     * 产品名称
     */
    private String productTitle;

    /**
     * 产品名称模糊查询
     */
    private String productTitleLike;

    /**
     * 仓库条码
     */
    private String warehouseBarcode;

    /**
     * 仓库条码模糊查询
     */
    private String warehouseBarcodeLike;

    /**
     * 产品状态:0 不可用,1可用,2:开发产品,
     */
    private Integer productStatus;

    /**
     * 产品销售状态Id
     */
    private Integer saleStatus;

    /**
     * 默认供应商代码。提供接口getAllSupplier查询
     */
    private String defaultSupplierCode;

    /**
     *是否质检
     * 0
     * 否
     * 1
     * 是
     */
    private Integer isQc;

    /**
     * 是否存在有效期
     * 0
     * 否
     * 1
     * 是
     */
    private Integer isExpDate;

    /**
     *-	是否赠品
     * 0
     * 否
     * 1
     * 是
     */
    private Integer isGift;

    /**
     * 品类ID    提供接口categotyList查询中的pc_id
     */
    private Integer categoryId;

    /**
     * 采购负责人Id
     */
    private Integer personOpraterId;

    /**
     * -	产品等级Id，数据可自定义。提供接口getProductLevel查询
     */
    private Integer prl_id;

    /**
     * 产品创建时间-开始时间
     */
    private String productAddTimeFrom;

    /**
     * -	产品创建时间-截止时间
     */
    private String productAddTimeTo;

    /**
     * 产品更新时间-开始时间
     */
    private String productUpdateTimeFrom;

    /**
     * 产品更新时间-截止时间
     */
    private String productUpdateTimeTo;

    /**
     * 审核时间-开始时间
     */
    private String productReleaseTimeFrom;

    /**
     * 审核时间-截止时间
     */
    private String productReleaseTimeTo;

    /**
     *是否是组合产品 ，
     * 0
     * 否
     * 1
     * 是
     */
    private Integer isCombination;

    /**
     *获取组合产品明细
     * 0
     * 否
     * 1
     * 是
     */
    private Integer getProductCombination;

    /**
     *获取产品箱规信息
     * 0
     * 否
     * 1
     * 是
     */
    private Integer getProductBox;

    /**
     *获取产品自定义属性
     * 0
     * 否
     * 1
     * 是
     */
    private Integer getProperty;

    /**
     *获取产品自定义分类
     * 0
     * 否
     * 1
     * 是
     */
    private Integer getProductCustomCategory;

    /**
     * 获取默认采购仓库
     */
    private Integer defaulBuyWarehouseId;

    /**
     * -	获取产品物流属性
     */
    private String logisticAttribute;

    /**
     * 成本更新时间-开始  2021-06-23 00:00:00
     */
    private String defaultCostUpdateFrom;

    /**
     * 成本更新时间-结束 2021-06-23 00:00:00
     */
    private String defaultCostUpdateTo;

    /**
     * 当前页
     */
    private Integer page;

    /**
     * 每一页条数，最大1000
     */
    private Integer pageSize;


}
