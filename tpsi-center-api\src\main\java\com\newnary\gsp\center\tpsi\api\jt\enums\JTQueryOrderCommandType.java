package com.newnary.gsp.center.tpsi.api.jt.enums;

import lombok.Getter;

/**
 * @Author: jack
 * @CreateTime: 2023-8-15
 */
@Getter
public enum JTQueryOrderCommandType {

    BYORDERQUERY("按订单查询","1"),
    BYBILLCODEQUERY("按运单查询","2"),
    BYTIMECONDITQUERY("按时间范围查询","3");

    private String name;
    private String value;

    JTQueryOrderCommandType(String name, String value){
        this.name = name;
        this.value = value;
    }

    public String getValue(){
        return this.value;
    }


}
