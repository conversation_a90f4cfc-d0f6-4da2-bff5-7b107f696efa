package com.newnary.gsp.center.tpsi.api.haiying.request.shopee;

import com.newnary.api.base.common.PageCondition;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingOrderByType;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingShopeeKeywordListOrderBy;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingStation;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingShopeeKeywordListCommand {

    /**
     * 站点(默认:马来西亚站)
     */
    @NotNull(message = "站点不能为空")
    private HaiYingStation station;

    /**
     * 关键词(string型)
     */
    private String keyword;

    /**
     * 搜索类型
     * 1: 精确搜索
     * 2: 模糊搜索
     * 3: 分词搜索
     */
    private Integer search_type;

    /**
     * 搜索量起始值(int 型)
     */
    private Integer search_volume_start;

    /**
     * 搜索量结束值(int 型)
     */
    private Integer search_volume_end;

    /**
     * 出价起始值(double型)
     */
    private BigDecimal recommend_price_start;

    /**
     * 出价结束值(double型)
     */
    private BigDecimal recommend_price_end;

    /**
     * 排序方式:
     * search_volume(搜索量)
     * recommend_price(价格)
     * update_time(最新更新时间)
     */
    private HaiYingShopeeKeywordListOrderBy order_by;

    /**
     * 排序类型:
     * ASC 升序
     * DESC 降序
     */
    private HaiYingOrderByType order_by_type;

    /**
     * 每一页的商品数(默认海鹰设置1000)(int 型)
     * 数值范围[1-5000]
     */
    private PageCondition pageCondition;

}
