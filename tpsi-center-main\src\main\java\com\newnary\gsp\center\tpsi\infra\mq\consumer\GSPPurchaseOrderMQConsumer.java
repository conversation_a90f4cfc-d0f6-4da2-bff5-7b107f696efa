package com.newnary.gsp.center.tpsi.infra.mq.consumer;

import com.newnary.messagebody.gsp.purchase.GSPPurchaseOrderTopic;
import com.newnary.mq.starter.consumer.AbstractMQConsumer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class GSPPurchaseOrderMQConsumer extends AbstractMQConsumer {

    @Value("${purchase-center.mq.consumer-id.purchase-order}")
    private String group;

    @Override
    public String topic() {
        return GSPPurchaseOrderTopic.TOPIC;
    }

    @Override
    public String consumerGroup() {
        return group;
    }
}
