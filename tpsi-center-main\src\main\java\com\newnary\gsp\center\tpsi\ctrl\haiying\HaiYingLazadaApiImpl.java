package com.newnary.gsp.center.tpsi.ctrl.haiying;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.common.CommonResponse;
import com.newnary.api.base.common.PageCondition;
import com.newnary.api.base.common.PageList;
import com.newnary.api.base.common.PageMeta;
import com.newnary.gsp.center.tpsi.api.haiying.HaiYingLazadaApi;
import com.newnary.gsp.center.tpsi.api.haiying.request.lazada.*;
import com.newnary.gsp.center.tpsi.api.haiying.response.lazada.*;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.HaiYingDataApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.lazada.*;
import com.newnary.gsp.center.tpsi.service.haiying.IHaiYingDataLazadaApiSve;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-13
 */
@RestController
@Slf4j
public class HaiYingLazadaApiImpl implements HaiYingLazadaApi {

    private static final Integer pageLimit = 100000;

    @Resource
    private IHaiYingDataLazadaApiSve haiYingLazadaDataApiSve;

    @Override
    public CommonResponse<PageList<HaiYingLazadaProductListDTO>> getLazadaProductList(HaiYingLazadaProductListCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingLazadaDataApiSve.getProductList(HaiYingLazadaCommand2RequestTranslator.transLazadaProductList(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingLazadaProductListResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingLazadaProductListResponse.class);
            if (apiBaseResult.getTotalSize() > pageLimit)
                apiBaseResult.setTotalSize(pageLimit); //TODO 因海鹰api限制返回前10w条
            return CommonResponse.success(HaiYingLazadaResponse2DTOTranslator.transLazadaProductListList(command.getStation(), responseList, getResultPageMeta(command.getPageCondition(), apiBaseResult)));
        } else {
            log.error("{}获取海鹰lazada商品列表失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            PageList<HaiYingLazadaProductListDTO> ret = new PageList<>();
            PageMeta pageMeta = new PageMeta();
            pageMeta.pageNum = command.getPageCondition().pageNum;
            pageMeta.pageSize = command.getPageCondition().pageSize;
            ret.setPageMeta(pageMeta);
            return CommonResponse.success(ret);
        }
    }

    @Override
    public CommonResponse<List<HaiYingLazadaProductDetailInfoDTO>> getLazadaProductDetailInfo(HaiYingLazadaProductDetailInfoCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingLazadaDataApiSve.getProductDetailInfo(HaiYingLazadaCommand2RequestTranslator.transLazadaProductDetailInfo(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingLazadaProductDetailInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingLazadaProductDetailInfoResponse.class);
            return CommonResponse.success(HaiYingLazadaResponse2DTOTranslator.transLazadaProductDetailInfoList(responseList));
        } else {
            log.error("{}获取海鹰lazada商品详情信息失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            return CommonResponse.success(new ArrayList<>());
        }
    }

    @Override
    public CommonResponse<List<HaiYingLazadaProductExtInfoDTO>> getLazadaProductExtInfo(HaiYingLazadaProductExtInfoCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingLazadaDataApiSve.getProductExtInfo(HaiYingLazadaCommand2RequestTranslator.transLazadaProductExtInfo(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingLazadaProductExtInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingLazadaProductExtInfoResponse.class);
            return CommonResponse.success(HaiYingLazadaResponse2DTOTranslator.transLazadaProductExtInfoList(responseList));
        } else {
            log.error("{}获取海鹰lazada商品附加信息失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            return CommonResponse.success(new ArrayList<>());
        }
    }

    @Override
    public CommonResponse<List<HaiYingLazadaProductHistoryInfoDTO>> getLazadaProductHistoryInfo(HaiYingLazadaProductHistoryInfoCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingLazadaDataApiSve.getProductHistoryInfo(HaiYingLazadaCommand2RequestTranslator.transLazadaProductHistoryInfo(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingLazadaProductHistoryInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingLazadaProductHistoryInfoResponse.class);
            return CommonResponse.success(HaiYingLazadaResponse2DTOTranslator.transLazadaProductHistoryInfoList(responseList));
        } else {
            log.error("{}获取海鹰lazada商品历史信息失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            return CommonResponse.success(new ArrayList<>());
        }
    }

    @Override
    public CommonResponse<List<HaiYingLazadaProductHistoryDailyReviewDTO>> getLazadaProductHistoryDailyReview(HaiYingLazadaProductHistoryDailyReviewCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingLazadaDataApiSve.getProductHistoryDailyReview(HaiYingLazadaCommand2RequestTranslator.transLazadaProductHistoryDailyReview(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingLazadaProductHistoryDailyReviewResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingLazadaProductHistoryDailyReviewResponse.class);
            return CommonResponse.success(HaiYingLazadaResponse2DTOTranslator.transLazadaProductHistoryDailyReview(responseList));
        } else {
            log.error("{}获取海鹰lazada商品历史信息失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            return CommonResponse.success(new ArrayList<>());
        }
    }

    @Override
    public CommonResponse<List<HaiYingLazadaCategoryTreeDTO>> getLazadaCategoryTree(HaiYingLazadaCategoryTreeCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingLazadaDataApiSve.getCategoryTree(HaiYingLazadaCommand2RequestTranslator.transLazadaCategoryTree(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingLazadaCategoryTreeResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingLazadaCategoryTreeResponse.class);
            return CommonResponse.success(HaiYingLazadaResponse2DTOTranslator.transLazadaCategoryTreeList(responseList));
        } else {
            log.error("{}获取海鹰lazada类目树失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            return CommonResponse.success(new ArrayList<>());
        }
    }

    @Override
    public CommonResponse<List<HaiYingLazadaCategoryInfoDTO>> getLazadaTopCategoryInfo(HaiYingLazadaTopCategoryInfoCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingLazadaDataApiSve.getTopCategoryInfo(HaiYingLazadaCommand2RequestTranslator.transLazadaTopCategoryInfo(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingLazadaCategoryInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingLazadaCategoryInfoResponse.class);
            return CommonResponse.success(HaiYingLazadaResponse2DTOTranslator.transLazadaCategoryInfoList(responseList));
        } else {
            log.error("{}获取海鹰lazada一级类目信息失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            return CommonResponse.success(new ArrayList<>());
        }
    }

    @Override
    public CommonResponse<PageList<HaiYingLazadaCategoryInfoDTO>> getLazadaSubCategoryInfo(HaiYingLazadaSubCategoryInfoCommand command) {
        HaiYingDataApiBaseResult<String> apiBaseResult = haiYingLazadaDataApiSve.getSubCategoryInfo(HaiYingLazadaCommand2RequestTranslator.transLazadaSubCategoryInfo(command));
        if (apiBaseResult.getCode() == 200 && apiBaseResult.getStatus().equalsIgnoreCase("success")) {
            List<HaiYingLazadaCategoryInfoResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingLazadaCategoryInfoResponse.class);
            return CommonResponse.success(HaiYingLazadaResponse2DTOTranslator.transLazadaCategoryInfoPageList(responseList, getResultPageMeta(command.getPageCondition(), apiBaseResult)));
        } else {
            log.error("{}获取海鹰lazada子类目信息失败{}", JSON.toJSONString(command), apiBaseResult.getMessage());
            return CommonResponse.success(new PageList<>());
        }
    }

    private PageMeta getResultPageMeta(PageCondition pageCondition, HaiYingDataApiBaseResult<String> apiBaseResult) {
        if (apiBaseResult.getTotalSize() > 0) {
            PageMeta pageMeta = new PageMeta();
            if (null != pageCondition) {
                pageMeta.pageNum = pageCondition.pageNum;
            } else {
                pageMeta.pageNum = 1;
            }
            pageMeta.pageSize = apiBaseResult.getSize();
            if (apiBaseResult.getTotalSize() % apiBaseResult.getSize() == 0) {
                pageMeta.pages = apiBaseResult.getTotalSize() / apiBaseResult.getSize();
            } else {
                pageMeta.pages = (apiBaseResult.getTotalSize() / apiBaseResult.getSize()) + 1;
            }
            pageMeta.total = apiBaseResult.getTotalSize();
            return pageMeta;
        }
        return null;
    }

}
