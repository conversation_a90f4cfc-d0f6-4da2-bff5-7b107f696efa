package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.shopee;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingShopeeCategoryTreeRequest {

    /**
     * 站点(默认:马来西亚站)
     */
    @NotNull(message = "站点不能为空")
    private String station;


    private Integer level;

    private String cid;

    /**
     * 父类目
     */
    private String p_l1_id;
    private String p_l2_id;
    private String p_l3_id;

}
