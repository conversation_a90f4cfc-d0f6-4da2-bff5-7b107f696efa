package com.newnary.gsp.center.tpsi.ctrl.haiying;

import com.newnary.gsp.center.tpsi.api.haiying.request.amazon.HaiYingAmazonCategoryTreeCommand;
import com.newnary.gsp.center.tpsi.api.haiying.request.amazon.HaiYingAmazonProductDetailInfoCommand;
import com.newnary.gsp.center.tpsi.api.haiying.request.amazon.HaiYingAmazonProductHistoryInfoCommand;
import com.newnary.gsp.center.tpsi.api.haiying.request.amazon.HaiYingAmazonProductListCommand;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.amazon.HaiYingAmazonCategoryTreeRequest;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.amazon.HaiYingAmazonProductDetailInfoRequest;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.amazon.HaiYingAmazonProductHistoryInfoRequest;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.amazon.HaiYingAmazonProductListRequest;
import com.newnary.gsp.center.tpsi.infra.mapper.HaiYingAmazonDataMapper;

/**
 * <AUTHOR>
 * @Date 2021/12/14 14:52
 */
public class HaiYingAmazonCommand2RequestTranslator {

    public static HaiYingAmazonProductListRequest transAmazonProductList(HaiYingAmazonProductListCommand command) {
        HaiYingAmazonProductListRequest request = HaiYingAmazonDataMapper.INSTANCE.transAmazonProductListRequest(command);

        return request;
    }

    public static HaiYingAmazonProductDetailInfoRequest transAmazonProductDetailInfo(HaiYingAmazonProductDetailInfoCommand command) {
        HaiYingAmazonProductDetailInfoRequest request = HaiYingAmazonDataMapper.INSTANCE.transAmazonProductDetailRequest(command);

        return request;
    }

    public static HaiYingAmazonProductHistoryInfoRequest transAmazonProductHistoryInfo(HaiYingAmazonProductHistoryInfoCommand command) {
        HaiYingAmazonProductHistoryInfoRequest request = HaiYingAmazonDataMapper.INSTANCE.transAmazonProductHistoryRequest(command);

        return request;
    }

    public static HaiYingAmazonCategoryTreeRequest transAmazonCategoryTree(HaiYingAmazonCategoryTreeCommand command) {
        HaiYingAmazonCategoryTreeRequest request = HaiYingAmazonDataMapper.INSTANCE.transAmazonCategoryTreeRequest(command);

        return request;
    }

}
