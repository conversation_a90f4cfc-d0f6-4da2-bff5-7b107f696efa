package com.newnary.gsp.center.tpsi.api.haiying.enums;

/**
 * @Author: jack
 * @CreateTime: 2022-7-13
 */
public enum HaiYingEbayCategoryListOrderBy {

    all_product_count("商品总数"),
    merchant_count("店铺总数"),
    sold_the_previous_day("前1天类目销售件数"),
    payment_the_previous_day("前1天类目销售金额"),
    sold_the_previous_growth("前1天类目销售增幅"),
    sales_three_day1("前3天类目销售件数"),
    payment_three_day1("前3天类目销售金额"),
    sales_three_day_growth("前3天类目销售增幅"),
    ;
    private String description;

    HaiYingEbayCategoryListOrderBy(String description) {
        this.description = description;
    }

}
