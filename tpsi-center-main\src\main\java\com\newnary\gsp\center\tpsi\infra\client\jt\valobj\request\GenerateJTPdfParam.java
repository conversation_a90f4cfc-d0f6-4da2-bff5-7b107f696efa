package com.newnary.gsp.center.tpsi.infra.client.jt.valobj.request;

import lombok.Data;


@Data
public class GenerateJTPdfParam {
    /**
     * 跟踪号
     */
    private String wayBillNo;

    /**
     * 跟踪号
     */
    private String orderNo;

    /**
     * 跟踪号
     */
    private String pickupNo;

    /**
     * 跟踪号
     */
    private Boolean isCod;

    /**
     * 跟踪号
     */
    private String receiverName;

    /**
     * 跟踪号
     */
    private String receiverAddress;

    /**
     * 跟踪号
     */
    private String receiverAddressDetail;

    /**
     * 跟踪号
     */
    private String senderName;

    /**
     * 跟踪号
     */
    private String senderAddress;

    /**
     * 跟踪号
     */
    private String codPrice;

    /**
     * 跟踪号
     */
    private String goodsName;

    /**
     * 跟踪号
     */
    private String priceValue;

    /**
     * 跟踪号
     */
    private String weightValue;


}
