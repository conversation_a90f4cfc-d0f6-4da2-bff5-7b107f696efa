package com.newnary.gsp.center.tpsi.infra.mapper;


import com.newnary.gsp.center.tpsi.api.haiying.request.shopee.*;
import com.newnary.gsp.center.tpsi.api.haiying.response.shopee.*;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.shopee.*;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.shopee.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Author: jack
 * @CreateTime: 2022-9-5
 */
@Mapper
public interface HaiYingShopeeDataMapper extends HaiYingDataMapper {

    HaiYingShopeeDataMapper INSTANCE = Mappers.getMapper(HaiYingShopeeDataMapper.class);

    @Mapping(target = "station", source = "station", qualifiedByName = "haiyingStation2Str")
    @Mapping(target = "current_page", source = "pageCondition", qualifiedByName = "pageCondition2CurrentPage")
    @Mapping(target = "page_size", source = "pageCondition", qualifiedByName = "pageCondition2PageSize")
    HaiYingShopeeKeywordInfoRequest transShopeeKeywordInfoRequest(HaiYingShopeeKeywordInfoCommand command);

    @Mapping(target = "station", source = "station", qualifiedByName = "haiyingStation2Str")
    @Mapping(target = "current_page", source = "pageCondition", qualifiedByName = "pageCondition2CurrentPage")
    @Mapping(target = "page_size", source = "pageCondition", qualifiedByName = "pageCondition2PageSize")
    HaiYingShopeeKeywordListRequest transShopeeKeywordListRequest(HaiYingShopeeKeywordListCommand command);

    @Mapping(target = "station", source = "station", qualifiedByName = "haiyingStation2Str")
    HaiYingShopeeCategoryTreeRequest transShopeeCategoryTreeRequest(HaiYingShopeeCategoryTreeCommand command);

    @Mapping(target = "station", source = "station", qualifiedByName = "haiyingStation2Str")
    HaiYingShopeeTopCategoryInfoRequest transShopeeTopCategoryInfoRequest(HaiYingShopeeTopCategoryInfoCommand command);

    @Mapping(target = "station", source = "station", qualifiedByName = "haiyingStation2Str")
    @Mapping(target = "stat_time_start", source = "stat_time_start", qualifiedByName = "simpleTimeLong2Str")
    @Mapping(target = "stat_time_end", source = "stat_time_end", qualifiedByName = "simpleTimeLong2Str")
    @Mapping(target = "current_page", source = "pageCondition", qualifiedByName = "pageCondition2CurrentPage")
    @Mapping(target = "page_size", source = "pageCondition", qualifiedByName = "pageCondition2PageSize")
    HaiYingShopeeSubCategoryInfoRequest transShopeeSubCategoryInfoRequest(HaiYingShopeeSubCategoryInfoCommand command);

    @Mapping(target = "station", source = "station", qualifiedByName = "haiyingStation2Str")
    @Mapping(target = "shop_ids", source = "shop_ids", qualifiedByName = "list2StrWithComma")
    @Mapping(target = "not_exist_shop_ids", source = "not_exist_shop_ids", qualifiedByName = "list2StrWithComma")
    @Mapping(target = "cids", source = "cids", qualifiedByName = "list2StrWithSemicolon")
    @Mapping(target = "pid", source = "pids", qualifiedByName = "list2StrWithComma")
    @Mapping(target = "gen_time_from", source = "gen_time_from", qualifiedByName = "simpleDateLong2Str")
    @Mapping(target = "gen_time_end", source = "gen_time_to", qualifiedByName = "simpleDateLong2Str")
    @Mapping(target = "stat_time_start", source = "stat_time_start", qualifiedByName = "simpleTimeLong2Str")
    @Mapping(target = "stat_time_end", source = "stat_time_end", qualifiedByName = "simpleTimeLong2Str")
    @Mapping(target = "approved_date_start", source = "approved_date_start", qualifiedByName = "simpleDateLong2Str")
    @Mapping(target = "approved_date_end", source = "approved_date_end", qualifiedByName = "simpleDateLong2Str")
    @Mapping(target = "last_modi_time_start", source = "last_modi_time_start", qualifiedByName = "simpleDateLong2Str")
    @Mapping(target = "last_modi_time_end", source = "last_modi_time_end", qualifiedByName = "simpleDateLong2Str")
    @Mapping(target = "current_page", source = "pageCondition", qualifiedByName = "pageCondition2CurrentPage")
    @Mapping(target = "page_size", source = "pageCondition", qualifiedByName = "pageCondition2PageSize")
    @Mapping(target = "is_hot_sales", source = "is_hot_sales", qualifiedByName = "boolean2Str")
    @Mapping(target = "is_official_shop", source = "is_official_shop", qualifiedByName = "boolean2Str")
    HaiYingShopeeProductListRequest transShopeeProductListRequest(HaiYingShopeeProductListCommand command);

    @Mapping(target = "station", source = "station", qualifiedByName = "haiyingStation2Str")
    @Mapping(target = "pid_and_shop_ids", ignore = true)
    HaiYingShopeeProductDetailInfoRequest transShopeeProductDetailInfoRequest(HaiYingShopeeProductDetailInfoCommand command);

    @Mapping(target = "station", source = "station", qualifiedByName = "haiyingStation2Str")
    @Mapping(target = "pids", source = "pids", qualifiedByName = "list2StrWithComma")
    HaiYingShopeeProductExtInfoRequest transShopeeProductExtInfoRequest(HaiYingShopeeProductExtInfoCommand command);

    @Mapping(target = "station", source = "station", qualifiedByName = "haiyingStation2Str")
    @Mapping(target = "pids", source = "pids", qualifiedByName = "list2StrWithComma")
    @Mapping(target = "time_start", source = "time_start", qualifiedByName = "simpleTimeLong2Str")
    @Mapping(target = "time_end", source = "time_end", qualifiedByName = "simpleTimeLong2Str")
    HaiYingShopeeProductHistoryInfoRequest transShopeeProductHistoryInfoRequest(HaiYingShopeeProductHistoryInfoCommand command);



    @Mapping(target = "update_time", source = "update_time", qualifiedByName = "simpleTimeStr2Long")
    HaiYingShopeeKeywordInfoDTO transShopeeKeywordInfoDTO(HaiYingShopeeKeywordInfoResponse response);

    @Mapping(target = "insert_time", source = "insert_time", qualifiedByName = "simpleTimeStr2Long")
    @Mapping(target = "is_leaf", source = "is_leaf", qualifiedByName = "str2Boolean")
    HaiYingShopeeCategoryTreeDTO transShopeeCategoryTreeDTO(HaiYingShopeeCategoryTreeResponse response);

    @Mapping(target = "ins_time", source = "ins_time", qualifiedByName = "simpleTimeStr2Long")
    HaiYingShopeeTopCategoryInfoDTO transShopeeTopCategoryInfoDTO(HaiYingShopeeTopCategoryInfoResponse response);

    @Mapping(target = "insert_time", source = "insert_time", qualifiedByName = "simpleTimeStr2Long")
    @Mapping(target = "stat_time", source = "stat_time", qualifiedByName = "simpleTimeStr2Long")
    HaiYingShopeeSubCategoryInfoDTO transShopeeSubCategoryInfoDTO(HaiYingShopeeSubCategoryInfoResponse response);

    @Mapping(target = "cids", source = "cid", qualifiedByName = "str2ListWithComma")
    @Mapping(target = "gen_time", source = "gen_time", qualifiedByName = "simpleDateStr2Long")
    @Mapping(target = "approved_date", source = "approved_date", qualifiedByName = "simpleDateStr2Long")
    @Mapping(target = "last_modi_time", source = "last_modi_time", qualifiedByName = "simpleTimeStr2Long")
    @Mapping(target = "stat_time", source = "stat_time", qualifiedByName = "simpleTimeStr2Long")
    @Mapping(target = "not_exist", source = "not_exist", qualifiedByName = "str2Boolean")
    @Mapping(target = "is_hot_sales", source = "is_hot_sales", qualifiedByName = "str2Boolean")
    @Mapping(target = "is_shopee_verified", source = "is_shopee_verified", qualifiedByName = "str2Boolean")
    @Mapping(target = "is_official_shop", source = "is_official_shop", qualifiedByName = "str2Boolean")
    HaiYingShopeeProductListDTO transShopeeProductListDTO(HaiYingShopeeProductListResponse response);

    @Mapping(target = "gen_time", source = "gen_time", qualifiedByName = "millTimeStr2Long")
    @Mapping(target = "last_modi_time", source = "last_modi_time", qualifiedByName = "millTimeStr2Long")
    @Mapping(target = "is_hot_sales", source = "is_hot_sales", qualifiedByName = "str2Boolean")
    @Mapping(target = "is_shopee_verified", source = "is_shopee_verified", qualifiedByName = "str2Boolean")
    @Mapping(target = "is_official_shop", source = "is_official_shop", qualifiedByName = "str2Boolean")
    @Mapping(target = "not_exist", source = "not_exist", qualifiedByName = "str2Boolean")
    HaiYingShopeeProductDetailInfoDTO transShopeeProductDetailInfoDTO(HaiYingShopeeProductDetailInfoResponse response);

    HaiYingShopeeProductExtInfoDTO transShopeeProductExtInfoDTO(HaiYingShopeeProductExtInfoResponse response);

    HaiYingShopeeProductHistoryInfoDTO transShopeeProductHistoryInfoDTO(HaiYingShopeeProductHistoryInfoResponse response);

}
