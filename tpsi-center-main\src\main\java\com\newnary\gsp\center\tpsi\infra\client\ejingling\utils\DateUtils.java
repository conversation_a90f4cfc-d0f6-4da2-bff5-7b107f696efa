package com.newnary.gsp.center.tpsi.infra.client.ejingling.utils;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

@Log4j2
public class DateUtils {
    /**
     * 时间字符串转时间戳
     * @param pattern
     * @param dateString
     * @return
     */
    public static long DateString2TimeStamp(String pattern,String dateString){
        if (StringUtils.isBlank(pattern)) {
            log.info("请输入格式");
            return 0;
        }
        if (StringUtils.isBlank(dateString)) {
            log.info("请输入时间字符串");
            return 0;
        }
        SimpleDateFormat formatTime= new SimpleDateFormat(pattern);
        long time = 0;
        try {
            time = formatTime.parse(dateString).getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return time;
    }

    /**
     * 时间字符串转时间戳
     * @param dateString
     * @return
     */
    public static long DateString2TimeStamp(String dateString){
        if (StringUtils.isBlank(dateString)) {
            log.info("请输入时间字符串");
            return 0;
        }
        SimpleDateFormat formatTime= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long time = 0;
        try {
            time = formatTime.parse(dateString).getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return time;
    }

    /**
     * 时间转换指定格式的字符串
     * @param pattern
     * @param date
     * @return
     */
    public static String Date2FormatString(String pattern, Date date) {
        if (StringUtils.isBlank(pattern)) {
            log.info("请输入格式");
            return "";
        }
        if (ObjectUtils.isEmpty(date)) {
            log.info("请输入时间date");
            return "";
        }
        SimpleDateFormat formatTime= new SimpleDateFormat(pattern);
        return formatTime.format(date);
    }

    /**
     * 时间字符串转时间戳
     * @param pattern
     * @param dateString
     * @return
     */
    public static Date DateString2Date(String pattern,String dateString){
        if (StringUtils.isBlank(pattern)) {
            log.info("请输入格式");
            return null;
        }
        if (StringUtils.isBlank(dateString)) {
            log.info("请输入时间字符串");
            return null;
        }
        SimpleDateFormat formatTime= new SimpleDateFormat(pattern);
        Date time = null;
        try {
            time = formatTime.parse(dateString);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return time;
    }

    public static String timeStamp2DateString(String pattern,Long timeStamp) {
        SimpleDateFormat formatTime= new SimpleDateFormat(pattern);
        Date date = new Date(timeStamp);
        return formatTime.format(date);
    }

    public static void main(String[] args) {
        Date date = DateUtils.DateString2Date("yyyy-MM-dd HH:mm:ss", "2023-01-01 00:00:00");

    }
}
