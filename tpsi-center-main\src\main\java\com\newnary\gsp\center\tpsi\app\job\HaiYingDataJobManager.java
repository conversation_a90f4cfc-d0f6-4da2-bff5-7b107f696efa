package com.newnary.gsp.center.tpsi.app.job;

import com.alibaba.fastjson.JSONObject;
import com.newnary.distributed.tools.concurrent.template.DConcurrentTemplate;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingPlatform;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingStation;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.HaiYingDataApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.shopee.HaiYingShopeeCategoryTreeRequest;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.shopee.HaiYingShopeeCategoryTreeResponse;
import com.newnary.gsp.center.tpsi.service.haiying.IHaiYingDataShopeeApiSve;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.job.core.handler.annotation.Job;
import com.newnary.spring.cloud.extend.SpringExtendLoader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Author: jack
 * @CreateTime: 2022-7-13
 */
@Component
@Slf4j
public class HaiYingDataJobManager {

    private final static String AUTO_SYNC_HAIYING_CATEGORY_TREE = "AUTO_SYNC_HAIYING_CATEGORY_TREE";

    @Job("autoSyncHaiYingCategoryTree")
    public ReturnT<String> autoSyncHaiYingCategoryTree(String param) {
        log.info("定时任务同步海鹰数据的类目树 - 开始 --- {}", new Date());

        for (HaiYingPlatform platform : HaiYingPlatform.values()) {
            IHaiYingDataShopeeApiSve haiYingDataApiSve = SpringExtendLoader.getExtensionLoader(IHaiYingDataShopeeApiSve.class)
                    .getExtension(platform.name());

            if (null != haiYingDataApiSve) {
                for (HaiYingStation station : HaiYingStation.values()) {
                    DConcurrentTemplate.tryLockMode(
                            AUTO_SYNC_HAIYING_CATEGORY_TREE.concat("-").concat(platform.name()).concat("|").concat(station.name()),
                            lock -> lock.tryLock(3, TimeUnit.SECONDS),
                            () -> {
                                HaiYingShopeeCategoryTreeRequest request = new HaiYingShopeeCategoryTreeRequest();
                                request.setStation(station.name());
                                HaiYingDataApiBaseResult<String> apiBaseResult = haiYingDataApiSve.getCategoryTree(request);
                                if (apiBaseResult.getCode().equals("200") && apiBaseResult.getStatus().equals("success")) {
                                    List<HaiYingShopeeCategoryTreeResponse> responseList = JSONObject.parseArray(apiBaseResult.getResult(), HaiYingShopeeCategoryTreeResponse.class);
                                    if (CollectionUtils.isNotEmpty(responseList)) {
                                        responseList.forEach(category -> {

                                        });
                                    }
                                } else {
                                    log.error("{}获取海鹰类目树失败{}", platform.name().concat("-").concat(station.name()), apiBaseResult.getMessage());
                                }
                            }
                    );
                }
            }
        }
        log.info("定时任务同步海鹰数据的类目树 - 结束 --- {}", new Date());
        return ReturnT.SUCCESS;
    }

}
