package com.newnary.gsp.center.tpsi.api.jt.request.cn;

import com.newnary.gsp.center.tpsi.api.jt.vo.cn.CreateOrderCnJTConsignee;
import com.newnary.gsp.center.tpsi.api.jt.vo.cn.CreateOrderCnJTPackage;
import com.newnary.gsp.center.tpsi.api.jt.vo.cn.CreateOrderCnJTShipper;
import com.newnary.gsp.center.tpsi.api.jt.vo.cn.CreateOrderCnJTrDeclare;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * 创建极兔物流运输单，请求体
 *
 * <AUTHOR>
 * @since Created on 2023-08-11
 **/
@Data
@Valid
public class CreateOrderCnJTCommand {

    @Size(max = 32, message = "customerOrderNo(最大32个字符)")
    @NotBlank(message = "customerOrderNo(不能为空)")
    private String customerOrderNo;

    @Size(max = 32, message = "logisticsProductCode(最大32个字符)")
    @NotBlank(message = "logisticsProductCode(不能为空)")
    private String logisticsProductCode;

    @Size(max = 36, message = "waybillNo(最大36个字符)")
    private String waybillNo;

    @Size(max = 36, message = "deliveryNo(最大36个字符)")
    private String deliveryNo;

    /**
     * 取值范围[10:包裹/11:PAK 袋/12:文件]
     */
    @NotNull(message = "parcelType(不能为空)")
    private Integer parcelType;


    /**
     * 货物类别。取值范围[11:礼物/12:文件/13:商业样本/14:回货品/15:其他]
     */
    private Integer goodsCategory;

    /**
     * 默认美元
     */
    private String declareCurrency;

    private BigDecimal insuredAmountD;

    private String insuredCurrency;

    private BigDecimal codAmountD = BigDecimal.ZERO;

    private String codCurrency;

    private Integer taxPayMode;

    private Integer iossTaxType;

    @Size(max = 32, message = "iossNo(最大32个字符)")
    private String iossNo;

    @Size(max = 32, message = "eoriNo(最大32个字符)")
    private String eoriNo;

    @Size(max = 32, message = "vatNo(最大32个字符)")
    private String vatNo;

    @Size(max = 255, message = "vatCompanyEnName(最大255个字符)")
    private String vatCompanyEnName;

    @Size(max = 2, message = "vatRegisterCountry(最大2个字符)")
    private String vatRegisterCountry;

    @Size(max = 255, message = "vatRegisterAddress(最大255个字符)")
    private String vatRegisterAddress;

    @Size(max = 255, message = "salesUrl(最大255个字符)")
    private String salesUrl;

    @Size(max = 100, message = "customerRemark(最大100个字符)")
    private String customerRemark;

    @Valid
    @NotNull(message = "shipper(不能为空)")
    private CreateOrderCnJTShipper shipper;

    @Valid
    @NotNull(message = "consignee(不能为空)")
    private CreateOrderCnJTConsignee consignee;

    @Valid
    @NotEmpty(message = "orderDeclareList(不能为空)")
    private List<CreateOrderCnJTrDeclare> orderDeclareList;

    @Valid
    @NotEmpty(message = "packageList(不能为空)")
    private List<CreateOrderCnJTPackage> packageList;


}
