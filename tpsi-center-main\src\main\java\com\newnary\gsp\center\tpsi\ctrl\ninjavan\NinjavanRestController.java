package com.newnary.gsp.center.tpsi.ctrl.ninjavan;

import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.common.CommonResponse;
import com.newnary.distributed.tools.concurrent.template.DConcurrentTemplate;
import com.newnary.gsp.center.logistics.api.delivery.dto.TrackInfoDTO;
import com.newnary.gsp.center.logistics.api.delivery.enums.TransportExceptionType;
import com.newnary.gsp.center.logistics.api.delivery.request.TransportOrderUpdateTrackCommand;
import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderLiteInfo;
import com.newnary.gsp.center.logistics.api.delivery.response.TransportOrderPackageInfo;
import com.newnary.gsp.center.logistics.api.route.dto.RouteNodeDetailDTO;
import com.newnary.gsp.center.logistics.api.route.enums.RouteNode;
import com.newnary.gsp.center.logistics.api.route.enums.RouteSidePerspective;
import com.newnary.gsp.center.logistics.api.route.request.RouteInfoAddEventCommand;
import com.newnary.gsp.center.tpsi.api.ninjavan.NinjavanOpenApi;
import com.newnary.gsp.center.tpsi.api.ninjavan.enums.NinjavanWebhookStatus;
import com.newnary.gsp.center.tpsi.api.ninjavan.vo.NinjavanWebhookMsg;
import com.newnary.gsp.center.tpsi.infra.rpc.DeliveryOrderRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.RouteInfoRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.TradeOrderRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.TransportOrderRpc;
import com.newnary.gsp.center.trade.api.order.response.OrderDTO;
import com.newnary.tenant.context.TenantCarrier;
import com.newnary.tenant.context.TenantID;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.concurrent.TimeUnit;

/**
 * @Author: jack
 * @CreateTime: 2023-8-9
 */
@RestController
@Slf4j
public class NinjavanRestController implements NinjavanOpenApi {

    private static final String PREFIX = "NINJAVAN_OPEN_API";

    @Resource
    private RouteInfoRpc routeInfoRpc;
    @Resource
    private TradeOrderRpc tradeOrderRpc;
    @Resource
    private DeliveryOrderRpc deliveryOrderRpc;
    @Resource
    private TransportOrderRpc transportOrderRpc;

    public void testDealStatus(String payload) {
        NinjavanWebhookMsg ninjavanMsg = JSONObject.parseObject(payload, NinjavanWebhookMsg.class);
        dealStatus(ninjavanMsg, null);
    }

    @Override
    public CommonResponse<String> ninjavanWebhooks(String tenantID) {
        if (null == RequestContextHolder.getRequestAttributes()) {
            return CommonResponse.success("request is null");
        }
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        if (null == request) {
            return CommonResponse.success("request is null");
        }
        String payload;
        try {
            payload = this.readRequestBody(request.getInputStream());
        } catch (IOException e) {
            e.printStackTrace();
            return CommonResponse.success(e.getMessage());
        }
        log.info("收到Ninjavan - {}的消息 - {}", tenantID, payload);
        NinjavanWebhookMsg ninjavanMsg = JSONObject.parseObject(payload, NinjavanWebhookMsg.class);
        dealStatus(ninjavanMsg, tenantID);
        return null;
    }

    private String readRequestBody(InputStream inputStream) throws IOException {
        char[] buffer = new char[1024];
        final StringBuilder out = new StringBuilder();
        try (Reader in = new InputStreamReader(inputStream, "UTF-8")) {
            for (; ; ) {
                int rsz = in.read(buffer, 0, buffer.length);
                if (rsz < 0)
                    break;
                out.append(buffer, 0, rsz);
            }
        }
        return out.toString();
    }

    private void dealStatus(NinjavanWebhookMsg ninjavanMsg, String tenantId) {
        if (StringUtils.isEmpty(ninjavanMsg.getTracking_id())) {
            log.error("能者推送消息内容缺失");
            return;
        }
        DConcurrentTemplate.tryLockMode(
                PREFIX.concat("ninjavan-webhooks".concat(ninjavanMsg.getTracking_id())),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    if (ObjectUtils.isNotEmpty(tenantId)) {
                        TenantCarrier.setTenantID(new TenantID(tenantId));
                    }
                    NinjavanWebhookStatus status = NinjavanWebhookStatus.getByName(ninjavanMsg.getStatus());
                    String transportOrderPackageId = transportOrderRpc.getOrderPackageIdByLastLogisticsNum(ninjavanMsg.getTracking_id());
                    if (ObjectUtils.isNotEmpty(status)) {
                        switch (status) {
                            case PendingPickup:
                                {
                                    AddRouteInfo(ninjavanMsg, RouteNode.PLATFORM_DELIVER);
                                }
                                break;
                            case EnRouteToSortingHub:
                            case PendingPickupAtDistributionPoint:
                            case ParcelWeight:
                            case ParcelMeasurementsUpdate:
                            case ParcelSize:
                            case Cancelled:
                                {
                                    //TODO 暂不处理
                                }
                                break;
                            case SuccessfulPickup:
                            case ArrivedAtSortingHub:
                            case OnVehicleForDelivery:
                            case PendingReschedule:
                            case ArrivedAtDistributionPoint:
                                {
                                    AddRouteInfo(ninjavanMsg, RouteNode.LOCAL_LOGISTICS_DELIVERY);
                                    if (StringUtils.isNotEmpty(transportOrderPackageId)) {
                                        TransportOrderUpdateTrackCommand updateTrackCommand = new TransportOrderUpdateTrackCommand();
                                        updateTrackCommand.setTrackInfo(new TrackInfoDTO("Ninjavan", ninjavanMsg.getTracking_id()));
                                        updateTrackCommand.setTransportOrderPackageId(transportOrderPackageId);
                                        transportOrderRpc.updateTrack(updateTrackCommand);
                                        transportOrderRpc.localTransit(transportOrderPackageId);
//                                        tradeOrderRpc.orderCarrierReceive(orderId, ninjavanMsg.getTracking_id(), "Ninjavan");
                                    }
                                }
                                break;
                            case ReturnToSenderTriggered:
                                {
                                    // 记录轨迹
                                    AddRouteInfo(ninjavanMsg, RouteNode.EXCEPTION_DEAL);
                                    if (StringUtils.isNotEmpty(transportOrderPackageId)) {
                                        transportOrderRpc.pushException(transportOrderPackageId, TransportExceptionType.RETURN_TO_WAREHOUSE_EXCEPTION);
                                    }
                                }
                                break;
                            case RTS:
                            case ReturnedToSender: {
                                //记录轨迹
                                AddRouteInfo(ninjavanMsg, RouteNode.EXCEPTION_DEAL);
                                if (StringUtils.isNotEmpty(transportOrderPackageId)) {
                                    transportOrderRpc.dealException(transportOrderPackageId);
                                }
                            }
                                break;
                            case SuccessfulDelivery:
                            case Completed:
                                {
                                    // 用户签收，记录轨迹
                                    AddRouteInfo(ninjavanMsg, RouteNode.CUSTOMER_RECEIVE);
                                    if (StringUtils.isNotEmpty(transportOrderPackageId)) {
//                                        OrderDTO order = tradeOrderRpc.getTradeOrder(orderId);
//                                        tradeOrderRpc.orderCustomerReceive(orderId, "PHP", order.paymentPrice, "Ninjavan");
                                        transportOrderRpc.finishedByPackage(transportOrderPackageId);
                                    }
                                }
                                break;
                            default:
                                break;
                        }
                    } else {
                        log.warn("新增ninjavan轨迹消息:{}", ninjavanMsg.getStatus());
                    }
                    if (ObjectUtils.isNotEmpty(tenantId)) {
                        TenantCarrier.clearTenantID();
                    }
                }
        );
    }

    private void AddRouteInfo(NinjavanWebhookMsg ninjavanMsg, RouteNode routeNode) {
        RouteInfoAddEventCommand command = new RouteInfoAddEventCommand();
//        command.bizOrderNumber = ninjavanMsg.getShipper_order_ref_no();
//        command.orderPackageNumber = ninjavanMsg.getShipper_order_ref_no();
        command.bizOrderNumber = ninjavanMsg.getTracking_id();
        command.orderPackageNumber = ninjavanMsg.getTracking_id();
        command.bizId = ninjavanMsg.getTracking_id();
        command.bizSystem = "LOGISTICS-CENTER";
        command.bizModule = "TRANSPORT";
        command.sidePerspectives = new HashSet<>(Arrays.asList(RouteSidePerspective.OPERATOR, RouteSidePerspective.CUSTOMER));
        command.routeNode = routeNode;

        RouteNodeDetailDTO detail = new RouteNodeDetailDTO();
        detail.setLocation("Ninjavan");
        detail.setOperator("Ninjavan");
        if (StringUtils.isNotBlank(ninjavanMsg.getTimestamp())) {
            DateTimeFormatter dtf = null;
            if (StringUtils.isNotBlank(ninjavanMsg.getTimezone())) {
                dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").withZone(ZoneId.of(ninjavanMsg.getTimezone()));
            } else {
                dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss+0800");
            }
            LocalDateTime parse = LocalDateTime.parse(ninjavanMsg.getTimestamp(), dtf);
            detail.setGmtOperation(LocalDateTime.from(parse).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        } else {
            detail.setGmtOperation(new Date().getTime());
        }
        detail.setEvent(ninjavanMsg.getStatus());
        detail.setRemark(NinjavanWebhookStatus.getByName(ninjavanMsg.getStatus()).getDescription());
        command.detail = detail;

        routeInfoRpc.addRouteInfo(command);
    }

}
