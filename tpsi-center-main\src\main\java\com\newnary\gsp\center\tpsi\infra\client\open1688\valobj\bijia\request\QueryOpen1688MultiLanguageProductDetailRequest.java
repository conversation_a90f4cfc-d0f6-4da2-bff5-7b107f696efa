package com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.bijia.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

// 多语言商详
@Data
public class QueryOpen1688MultiLanguageProductDetailRequest {

    @NotNull(message = "输入参数不允许为空")
    private OfferDetailParam offerDetailParam;

    @Getter
    @Setter
    public static class OfferDetailParam{
        /**
         * 1688商品id
         */
        private String offerId;

        /**
         * 商品资料语言
         */
        private String country;
    }

}
