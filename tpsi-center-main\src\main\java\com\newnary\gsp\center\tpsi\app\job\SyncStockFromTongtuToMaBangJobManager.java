package com.newnary.gsp.center.tpsi.app.job;

import com.alibaba.fastjson.JSONObject;
import com.newnary.gsp.center.tpsi.service.mabang.IMaBangStockApiSve;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.job.core.handler.annotation.Job;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class SyncStockFromTongtuToMaBangJobManager {

    @Resource
    private IMaBangStockApiSve maBangStockApiSve;

    @Job("autoDoSyncStockFromTongtuToMaBang")
    public ReturnT<String> autoDoSyncStockFromTongtuToMaBang(String param) {
        JSONObject paramObject = JSONObject.parseObject(param);

        String maBangThirdPartySystemId = paramObject.getString("maBangThirdPartySystemId");
        String tongtuThirdPartySystemId = paramObject.getString("tongtuThirdPartySystemId");

        //一、从马帮查询商品列表
        maBangStockApiSve.stockDoSearchSkuListForSyncStock(maBangThirdPartySystemId, "SYNC_STOCK_FROM_TONGTU_TO_MABNAG", "", tongtuThirdPartySystemId);

        //在接收者中完成二三步骤
        //二、遍历在马帮上的商品列表,查询在通途上的库存
        //三、把通途库存数量推去马帮
        return ReturnT.SUCCESS;
    }
}
