package com.newnary.gsp.center.tpsi.service.mabang.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.newnary.api.base.exception.BaseErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.common.utils.locale.LanguageLocaleType;
import com.newnary.gsp.center.product.api.common.dto.MultimediaInfo;
import com.newnary.gsp.center.product.api.common.dto.MultimediaType;
import com.newnary.gsp.center.product.api.open.request.*;
import com.newnary.gsp.center.product.api.product.enums.DPSOuterCodeType;
import com.newnary.gsp.center.product.api.product.enums.SupplierItemSaleMode;
import com.newnary.gsp.center.product.api.product.enums.SupplierSkuSaleState;
import com.newnary.gsp.center.product.api.product.request.*;
import com.newnary.gsp.center.product.api.product.vo.SupplierSpuDescInfo;
import com.newnary.gsp.center.tpsi.api.mabang.request.SyncProductFromMaBangCommand;
import com.newnary.gsp.center.tpsi.infra.client.eccang.params.EcCangWMSParams;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.erp.EcCangERPGetProductListResponse;
import com.newnary.gsp.center.tpsi.infra.client.mabang.MaBangGWApiClient;
import com.newnary.gsp.center.tpsi.infra.client.mabang.MaBangParams;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.*;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.stock.MaBangDoSearchSalesSku;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.stock.MaBangDoSearchSkuList;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.stock.MaBangGetStockQuantity;
import com.newnary.gsp.center.tpsi.infra.client.open1688.util.StringUtil;
import com.newnary.gsp.center.tpsi.infra.model.ApiDockingResult;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.creator.ApiDockingResultCreator;
import com.newnary.gsp.center.tpsi.infra.model.vo.ApiDockingResultType;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingInfo;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingType;
import com.newnary.gsp.center.tpsi.infra.repository.IApiDockingResultRepository;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.ThirdPartyMappingManager;
import com.newnary.gsp.center.tpsi.infra.rpc.OpenSupplierProductRpc;
import com.newnary.gsp.center.tpsi.service.IMaBangApiDataTranslateSve;
import com.newnary.gsp.center.tpsi.service.impl.SystemClientSve;
import com.newnary.gsp.center.tpsi.service.mabang.IMaBangStockApiSve;
import com.newnary.tenant.context.TenantCarrier;
import com.newnary.tenant.context.TenantID;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MaBangStockApiSveImpl extends SystemClientSve implements IMaBangStockApiSve {

    public static final Logger LOGGER = LoggerFactory.getLogger(MaBangStockApiSveImpl.class);

    @Resource
    private IApiDockingResultRepository apiDockingResultRepository;

    @Resource
    private IMaBangApiDataTranslateSve maBangApiDataTranslateSve;

    @Override
    public void stockDoSearchSkuListForSyncStock(String thirdPartySystemId, String messageTag, String context,
                                                 String tongTuThirdPartySystemId) {
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);

        //获取apiClient
        MaBangGWApiClient maBangGWApiClient = getClient(thirdPartySystem.getParams());

        //处理马帮查询商品请求对象
        MaBangDoSearchSkuList param = maBangApiDataTranslateSve.buildMaBangDoSearchSkuList();

        //调用马帮获取商品
        int pageCount;
        int page = 1;

        do {
            LOGGER.info("现在获取第{}页", page);
            param.setPage(page);
            //循环获取分页
            MaBangApiBaseResult<String> ret = maBangGWApiClient.stockDoSearchSkuList(param);

            JSONArray productList = JSON.parseObject(ret.getData()).getJSONArray("data");
            pageCount = JSON.parseObject(ret.getData()).getInteger("totalPage");
            // 三、遍历参数并发送订单创建的mq消息(消费者中进行创建商品)
            productList.forEach(item -> {
                JSONObject itemObject = JSONObject.parseObject(item.toString());
                String stockSku = itemObject.getString("stockSku");
                JSONObject messageObject = new JSONObject();
                messageObject.put("stockSku", stockSku);
                messageObject.put("maBangThirdPartySystemId", thirdPartySystemId);
                messageObject.put("tongTuThirdPartySystemId", tongTuThirdPartySystemId);

                this.broadcast(thirdPartySystemId, messageTag, messageObject.toString());
            });
            page++;
        } while (page <= pageCount);
    }

    @Override
    public Integer stockGetStockQuantity(String thirdPartySystemId, String sku, String warehouse) {
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = this.loadSystem(thirdPartySystemId);
        //获取apiClient
        MaBangGWApiClient maBangGWApiClient = getClient(thirdPartySystem.getParams());

        //构建马帮请求对象
        MaBangGetStockQuantity maBangGetStockQuantity = new MaBangGetStockQuantity();
        maBangGetStockQuantity.setStockSkus(sku);
        maBangGetStockQuantity.setWarehouseName(warehouse);

        // 二、调用马帮接口获取库存数量
        MaBangApiBaseResult<String> ret = maBangGWApiClient.stockGetStockQuantity(maBangGetStockQuantity);
        //处理结果
        JSONArray data = JSON.parseObject(ret.getData()).getJSONArray("data");
        return data.getJSONObject(0).getInteger("stockQuantity");
    }

    @Override
    public MaBangStockQauntityList stockGetStockQuantity(String thirdPartySystemId, String sku) {
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        //获取apiClient
        MaBangGWApiClient maBangGWApiClient = getClient(thirdPartySystem.getParams());

        //构建马帮请求对象
        MaBangGetStockQuantity maBangGetStockQuantity = new MaBangGetStockQuantity();
        maBangGetStockQuantity.setStockSkus(sku);

        // 二、调用马帮接口获取库存数量
        MaBangApiBaseResult<String> ret = maBangGWApiClient.stockGetStockQuantity(maBangGetStockQuantity);
        if (ret != null) {
            if (!ret.getCode().equals("200") || (Objects.isNull(ret.getData()) || !JSON.parseObject(ret.getData()).containsKey("data"))) {
                log.info("库存商品数量信息为{}", JSON.toJSONString(ret));
                log.info("跳过");
                return null;
            } else {
                log.info("库存sku：{}获取成功", sku);
                return JSON.parseObject(ret.getData(), MaBangStockQauntityList.class);
            }
        } else {
            log.info("库存商品{}数量信息为空", sku);
            return null;
        }

    }

    @Override
    public MaBangStockSku stockDoSearchSkuList(String thirdPartySystemId, String stockSku) {
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
        //获取apiClient
        MaBangGWApiClient maBangGWApiClient = getClient(thirdPartySystem.getParams());
        //处理马帮查询商品请求对象
        MaBangDoSearchSkuList param = maBangApiDataTranslateSve.buildMaBangDoSearchSkuList();
        param.setStockSku(stockSku);
        param.setPage(1);
        //调用马帮获取商品
        log.info("现在获取仓库sku：{}", stockSku);
        MaBangApiBaseResult<String> ret = maBangGWApiClient.stockDoSearchSkuList(param);
        if (ret != null) {
            if (!ret.getCode().equals("200") || (Objects.isNull(ret.getData()) || !JSON.parseObject(ret.getData()).containsKey("data"))) {
                log.info("库存商品信息为{}", JSON.toJSONString(ret));
                log.info("跳过");
                return null;
            } else {
                JSONArray productList = JSON.parseObject(ret.getData()).getJSONArray("data");
                log.info("库存sku：{}获取成功", stockSku);
                return JSON.parseObject(productList.getJSONObject(0).toString(), MaBangStockSku.class);
            }
        } else {
            log.info("库存商品{}信息为空", stockSku);
            return null;
        }
    }

    @Override
    public void syncSalesSku(SyncProductFromMaBangCommand req) {
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(req.getThirdPartySystemId());

        //获取apiClient
        MaBangGWApiClient maBangGWApiClient = getClient(thirdPartySystem.getParams());

        //处理马帮查询主商品请求对象
        MaBangDoSearchSalesSku maBangDoSearchSalesSku = new MaBangDoSearchSalesSku();
        maBangDoSearchSalesSku.setRowsPerPage(req.getPageSize());

        //调用马帮获取商品
        int endPage = req.getSpuEndPage() != null ? req.spuEndPage : 0;
        int startPage = req.getSpuStartPage() != null ? req.spuStartPage : 1;
        if (StringUtils.isNotEmpty(req.getAppointSpu())) {
            maBangDoSearchSalesSku.setSalesSku(req.getAppointSpu());
            endPage = 1;
            startPage = 1;
        }

        int currentPage = startPage;
        do {
            log.info("现在获取主商品第{}页", currentPage);
            maBangDoSearchSalesSku.setPage(currentPage);
            //循环获取分页
            MaBangApiBaseResult<String> ret = maBangGWApiClient.stockDoSearchSalesSku(maBangDoSearchSalesSku);
            if (ret != null) {
                log.info("主商品第{}页信息已获取", currentPage);
                if (!ret.getCode().equals("200") || (Objects.isNull(ret.getData()) || !JSON. parseObject(ret.getData()).containsKey("data"))) {
                    log.info("主商品信息为{}", JSON.toJSONString(ret));
                    log.info("跳过");
                } else {
                    JSONArray productList = JSON.parseObject(ret.getData()).getJSONArray("data");
                    //处理总页数以及请求结束页数
                    int totalPage = JSON.parseObject(ret.getData()).getInteger("pageCount");
                    endPage = Math.min(endPage, totalPage);
                    //处理主商品返回结果
                    try {
                        //processBatch(productList,20,req,thirdPartySystem);
                        this.doHandleSalesSkuResponse(productList, req, thirdPartySystem);
                    } catch (Exception e) {
                        log.error("马帮商品同步失败：{}",e.getMessage(),e);
                    }
                }
            } else {
                log.info("主商品第{}页信息为空", currentPage);
            }

            currentPage++;
        } while ((currentPage-startPage) <= endPage);
    }

    private final ThreadPoolExecutor processExecutor = new ThreadPoolExecutor(
            5,
            5,
            20L, TimeUnit.SECONDS,
            new LinkedBlockingDeque<>(100),
            new ThreadPoolExecutor.CallerRunsPolicy());
    private void processBatch(JSONArray productList, Integer perGroupCount,SyncProductFromMaBangCommand req,ThirdPartySystem thirdPartySystem) throws Exception {
        final Optional<TenantID> tenantIdKept = TenantCarrier.getTenantID();
        // 1. 批量任务分组
        //List<String> skuIdList = productList.stream().map(EcCangERPGetProductListResponse::getProductSku).collect(Collectors.toList());
        List<List<Object>> groupSkuIds = ListUtils.partition(productList, perGroupCount);
        CountDownLatch processCountDownLatch = new CountDownLatch(groupSkuIds.size());

        // 2. 线程池执行
        groupSkuIds.forEach(groupItem -> {

            CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> {
                boolean tenantIdMiss = false;
                try {
                    if (!TenantCarrier.getTenantID().isPresent()) {
                        TenantID tenantID = tenantIdKept.orElse(null);
                        TenantCarrier.setTenantID(tenantID);
                        tenantIdMiss = tenantID != null;
                    }
                    this.doHandleSalesSkuResponse(JSONArray.parseArray(JSON.toJSONString(groupItem)), req, thirdPartySystem);
                } catch (Exception e) {
                    log.error("[{}] 易仓商品同步任务异常！e={}", "易仓商品同步任务", e);
                } finally {
                    processCountDownLatch.countDown();
                    if (tenantIdMiss) {
                        TenantCarrier.clearTenantID();
                    }
                }
            }, processExecutor);

        });

        // 3. 等待批次商品同步处理完成
        processCountDownLatch.await();
    }

    @Override
    public void syncStockSku(SyncProductFromMaBangCommand req) {
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(req.getThirdPartySystemId());

        //获取apiClient
        MaBangGWApiClient maBangGWApiClient = getClient(thirdPartySystem.getParams());

        //处理马帮查询商品请求对象
        MaBangDoSearchSkuList param = maBangApiDataTranslateSve.buildMaBangDoSearchSkuList();

        //调用马帮获取商品
        int endPage = req.getSkuEndPage() != null ? req.skuEndPage : 0;
        int startPage = req.getSkuStartPage() != null ? req.skuStartPage : 1;
        if (StringUtils.isNotEmpty(req.getAppointSku())) {
            param.setStockSku(req.getAppointSku());
            endPage = 1;
            startPage = 1;
        }

        do {
            LOGGER.info("现在获取库存商品第{}页", startPage);
            param.setPage(startPage);
            //循环获取分页
            MaBangApiBaseResult<String> ret = maBangGWApiClient.stockDoSearchSkuList(param);
            log.info("库存商品第{}页信息已获取", startPage);
            JSONArray productList = JSON.parseObject(ret.getData()).getJSONArray("data");
            //处理总页数以及请求结束页数
            int totalPage = JSON.parseObject(ret.getData()).getInteger("totalPage");
            endPage = (endPage == 0 || totalPage < endPage) ? totalPage : endPage;
            //处理库存商品返回结果
            this.doHandleStockSkuResponse(productList, req, thirdPartySystem);
            startPage++;
        } while (startPage <= endPage);
    }

    /**
     * 处理主商品返回结果
     *
     * @param productList
     * @param req
     * @param thirdPartySystem
     */
    private void doHandleSalesSkuResponse(JSONArray productList, SyncProductFromMaBangCommand req, ThirdPartySystem thirdPartySystem) {
        productList.forEach(item -> {
            MaBangSalesSku maBangSalesSku = JSON.parseObject(item.toString(), MaBangSalesSku.class);
            LOGGER.info("主商品{}", maBangSalesSku.getSalesSku());

            //过滤TZ开头的
            if (maBangSalesSku.getSalesSku().startsWith("TZ")) {
                saveApiDockingResult(maBangSalesSku, maBangSalesSku.getSalesSku(), "skip-because-TZ", ApiDockingResultType.MaBangSalesSku);
            } else {
                //如果有限定类目，则筛选类目
                if (req.getCategoryNameSet() != null) {
                    if (maBangSalesSku.getParentCategory() != null && maBangSalesSku.getCategory() != null) {
                        if (req.getCategoryNameSet().contains(maBangSalesSku.getParentCategory().getNameCN()) ||
                                req.getCategoryNameSet().contains(maBangSalesSku.getCategory().getNameCN())) {
                            //判断该请求结果是否已经存在
                            Optional<ApiDockingResult> existResult = apiDockingResultRepository.loadByKeyAndType(maBangSalesSku.getSalesSku(), ApiDockingResultType.MaBangSalesSku.name());
                            if (existResult.isPresent() && existResult.get().getDataStatus().equals("saved")) {
                                //TODO 发请求更新,接口未提供前不更新数据
                            } else {
                                //执行请求构建
                                doMaBangProductSyncSalesSku(thirdPartySystem, maBangSalesSku);
                            }
                        } else {
                            saveApiDockingResult(maBangSalesSku, maBangSalesSku.getSalesSku(), "skip-because-categoryNotEqual", ApiDockingResultType.MaBangSalesSku);
                        }
                    } else {
                        saveApiDockingResult(maBangSalesSku, maBangSalesSku.getSalesSku(), "skip-because-categoryIsNull", ApiDockingResultType.MaBangSalesSku);
                    }
                } else {
                    //不限制获取的品类，执行请求构建
                    doMaBangProductSyncSalesSku(thirdPartySystem, maBangSalesSku);
                }
            }
        });
    }

    private void doHandleStockSkuResponse(JSONArray productList, SyncProductFromMaBangCommand req, ThirdPartySystem thirdPartySystem) {
        productList.forEach(item -> {
            MaBangStockSku maBangStockSku = JSON.parseObject(item.toString(), MaBangStockSku.class);
            LOGGER.info("库存商品{}", maBangStockSku.getStockSku());
            //过滤TZ开头的
            if (maBangStockSku.getStockSku().startsWith("TZ")) {
                saveApiDockingResult(maBangStockSku, maBangStockSku.getStockSku(), "skip-because-TZ", ApiDockingResultType.MaBangStockSku);
            } else {
                Optional<ApiDockingResult> existResult = apiDockingResultRepository.loadByKeyAndType(maBangStockSku.getStockSku(), ApiDockingResultType.MaBangStockSku.name());
                //如果没有销售sku，才进行判断
                if (StringUtils.isEmpty(maBangStockSku.getSalesSku())) {
                    //如果有限定类目，则筛选类目
                    if (req.getCategoryNameSet() != null) {
                        if (req.getCategoryNameSet().contains(maBangStockSku.getParentCategoryName()) ||
                                req.getCategoryNameSet().contains(maBangStockSku.getCategoryName())) {
                            if (existResult.isPresent() && existResult.get().getDataStatus().equals("saved")) {
                                //TODO 发请求更新,接口未提供前不更新数据
                            } else {
                                //执行请求构建
                                doMaBangProductSyncStockSku(thirdPartySystem, maBangStockSku);
                            }
                        } else {
                            saveApiDockingResult(maBangStockSku, maBangStockSku.getStockSku(), "skip-because-categoryNotEqual", ApiDockingResultType.MaBangStockSku);
                        }
                    } else {
                        //不限制获取的品类，执行请求构建
                        doMaBangProductSyncStockSku(thirdPartySystem, maBangStockSku);
                    }
                } else {
                    if (!existResult.isPresent()) {
                        //有spu但是之前没保存
                        saveApiDockingResult(maBangStockSku, maBangStockSku.getStockSku(), "skip-because-have-spu", ApiDockingResultType.MaBangStockSku);
                    }
                }
            }
        });
    }

    @Resource
    private ThirdPartyMappingManager thirdPartyMappingManager;
    @Resource
    private OpenSupplierProductRpc openSupplierProductRpc;

    private void doMaBangProductSyncSalesSku(ThirdPartySystem thirdPartySystem, MaBangSalesSku maBangSalesSku) {
        log.info("构建主sku：{}请求开始", maBangSalesSku.getSalesSku());
        try {
            MaBangParams params = JSON.parseObject(thirdPartySystem.getParams(), MaBangParams.class);
            //OpenSupplierProductCreateReq req = buildErpProductCreateRequestForMaBangSalesSku(thirdPartySystem, maBangSalesSku);
            SupplierSpuCreateV2Command req = buildSupplierSpuCreateV2Command(thirdPartySystem, maBangSalesSku);
            ThirdPartyMappingInfo newestInfoByTarget = thirdPartyMappingManager.getNewestInfoByTarget("MABANG", "GSP", req.getCustomCode(), ThirdPartyMappingType.PRODUCT_ID);
            if (newestInfoByTarget != null) {
                return;
            }
            String spuId = openSupplierProductRpc.createSpu4StockAsync(req);
            if (StringUtils.isNotBlank(spuId)) {
                thirdPartyMappingManager.insertOrUpdate("GSP", "MABANG", spuId, req.getCustomCode(), ThirdPartyMappingType.PRODUCT_ID.name(), req);
                //saveApiDockingResult(valueJson, valueKey, "saved", type);
                List<SupplierSkuCreateInfo> skuList = req.getSkuList();
                int count = 0;
                for (SupplierSkuCreateInfo skuCreateInfo : skuList){
                    if (SupplierSkuSaleState.OFF_SALE.name().equals(skuCreateInfo.getSaleState().name())) {
                        count +=1 ;
                    }
                }
                if (count == skuList.size()){
                    OpenSupplierSpuOptSupplyStateReq openSupplierSpuOptSupplyStateReq = new OpenSupplierSpuOptSupplyStateReq();
                    openSupplierSpuOptSupplyStateReq.setSupplierId(params.getSupplierId());
                    openSupplierSpuOptSupplyStateReq.setCustomCode(maBangSalesSku.getSalesSku());
                    openSupplierProductRpc.stopSpuSupply(openSupplierSpuOptSupplyStateReq);
                }

            }
/*            if (CollectionUtils.isEmpty(req.getSkuList())) {
                log.info("创建商品请求失败");
                saveApiDockingResult(maBangSalesSku, maBangSalesSku.getSalesSku(), "创建商品请求失败", ApiDockingResultType.MaBangSalesSku);
                return;
            }
            log.info("主sku：{}商品请求构建完毕，发送商品创建消息", maBangSalesSku.getSalesSku());
            this.broadcastProductCreate("PRODUCT_CREATE", JSONObject.toJSONString(req), maBangSalesSku.getSalesSku(), JSON.toJSONString(maBangSalesSku), ApiDockingResultType.MaBangSalesSku.name());*/

        } catch (Exception e) {
            e.printStackTrace();
            log.info(e.getMessage());
            saveApiDockingResult(maBangSalesSku, maBangSalesSku.getSalesSku(), e.getMessage(), ApiDockingResultType.MaBangSalesSku);
        }
        log.info("处理主sku：{}结束", maBangSalesSku.getSalesSku());
    }

    private OpenSupplierProductCreateReq buildErpProductCreateRequestForMaBangSalesSku(ThirdPartySystem system, MaBangSalesSku maBangSalesSku) {
        MaBangParams params = JSON.parseObject(system.getParams(), MaBangParams.class);
        log.info("现在处理主sku{}", maBangSalesSku.getSalesSku());
        OpenSupplierProductCreateReq req = new OpenSupplierProductCreateReq();
        req.supplierId = params.getSupplierId();
        req.title = maBangSalesSku.getSalesName();
        req.zhTitle = maBangSalesSku.getSalesName();
        req.enTitle = maBangSalesSku.getSalesNameEn();
        req.customSpuCode = maBangSalesSku.getSalesSku();
        // req.customBrandName = ""; TODO 待添加
        req.customCategoryName = maBangSalesSku.getCategory().getNameCN();
        req.mainImageUrls = Lists.newArrayList(maBangSalesSku.getStockPicture());
        //TODO 分类id [临时代码]
        req.setCategoryId("PMC0394192409536269647872");

        //构建skuList
        List<OpenSupplierSkuCreateInfo> skuList = new ArrayList<>();

        OpenSupplierSpuLogisticsAttrInfo openSupplierSpuLogisticsAttrInfo = new OpenSupplierSpuLogisticsAttrInfo();

        maBangSalesSku.getStockSku().forEach(stockSkuJson -> {
            JSONObject stockSkuJO = JSONObject.parseObject(stockSkuJson);
            String stockSku = stockSkuJO.getString("stockSku");
            log.info("处理库存sku:{}", stockSku);
            OpenSupplierSkuCreateInfo openSupplierSkuCreateInfo = this.buildErpProductSkuForMaBangSalesSku(system.getBizId(), stockSku, openSupplierSpuLogisticsAttrInfo);
            if (openSupplierSkuCreateInfo != null) {
                skuList.add(openSupplierSkuCreateInfo);
            } else {
                log.info("子sku{}找不到", stockSku);
            }
        });
        if (CollectionUtils.isEmpty(skuList)) {
            log.info("主Sku{}下的子sku全部找不到", maBangSalesSku.getSalesSku());
            return null;
        }
        req.skuList = skuList;

        openSupplierSpuLogisticsAttrInfo.isCharged = maBangSalesSku.getHasBattery() == 1 ? Boolean.TRUE : Boolean.FALSE;
        req.logisticsAttrInfo = openSupplierSpuLogisticsAttrInfo;

        req.isAutoAuditPass = true;
        return req;
    }

    public SupplierSpuCreateV2Command buildSupplierSpuCreateV2Command(ThirdPartySystem system, MaBangSalesSku maBangSalesSku) {
        MaBangParams params = JSON.parseObject(system.getParams(), MaBangParams.class);
        SupplierSpuCreateV2Command spuCreateV2Command = new SupplierSpuCreateV2Command();
        spuCreateV2Command.setSupplierId(params.getSupplierId());
        spuCreateV2Command.setDescInfos(buildSpuInfos(maBangSalesSku));
        spuCreateV2Command.setDefaultLocale(LanguageLocaleType.zh_CN);
        spuCreateV2Command.setCustomCode(maBangSalesSku.getSalesSku());

        spuCreateV2Command.setCategoryId(params.getCategory());
        if (maBangSalesSku.getCategory() != null) {
            spuCreateV2Command.setCustomCategoryId(String.valueOf(maBangSalesSku.getCategory().getId()));
        }

        //主图
        String stockPicture = maBangSalesSku.getStockPicture();
        List<MultimediaInfo> mainImages = new ArrayList<>();
        if (StringUtils.isNotBlank(stockPicture)) {
            String[] detailImageUrls = stockPicture.split(",");
            if (detailImageUrls.length > 0) {
                for (String url : detailImageUrls) {
                    MultimediaInfo mainImage = new MultimediaInfo();
                    mainImage.setType(MultimediaType.IMAGE);
                    mainImage.setFileUrl(url);
                    mainImages.add(mainImage);
                }
            }
        }
        spuCreateV2Command.setMainImages(mainImages);

        //sku
        spuCreateV2Command.setSkuList(buildSkuList(system,spuCreateV2Command, mainImages, maBangSalesSku));

        if (CollectionUtils.isEmpty(mainImages)) {
            MultimediaInfo mainImage = new MultimediaInfo();
            mainImage.setType(MultimediaType.IMAGE);
            mainImage.setFileUrl("");
            mainImages.add(mainImage);
        }

        spuCreateV2Command.setOperator("tpsi");

        spuCreateV2Command.isTryCreate = true;

        return spuCreateV2Command;
    }

    public List<SupplierSpuDescInfo> buildSpuInfos(MaBangSalesSku maBangSalesSku) {
        List<SupplierSpuDescInfo> spuDescInfos = new ArrayList<>();
        SupplierSpuDescInfo spuInfo = new SupplierSpuDescInfo();
        spuInfo.setLocale(LanguageLocaleType.zh_CN);
        spuInfo.setTitle(maBangSalesSku.getSalesName());
        if (maBangSalesSku.getCategory() != null) {
            spuInfo.setCustomCategoryName(maBangSalesSku.getCategory().getNameCN());
        }
        spuInfo.setTransSourceType("tpsi");
        spuInfo.setTransProvider("MaBang");
        spuInfo.setTransBaseLocate(LanguageLocaleType.zh_CN);
        spuDescInfos.add(spuInfo);
        return spuDescInfos;
    }

    public List<SupplierSkuCreateInfo> buildSkuList(ThirdPartySystem system,SupplierSpuCreateV2Command spuCreateV2Command, List<MultimediaInfo> mainImages, MaBangSalesSku maBangSalesSku) {
        MaBangParams params = JSON.parseObject(system.getParams(), MaBangParams.class);
        List<SupplierSkuCreateInfo> skuCreateInfos = new ArrayList<>();
        List<String> stockSku = maBangSalesSku.getStockSku();
        if (CollectionUtils.isNotEmpty(stockSku)) {
            stockSku.forEach(sku -> {
                JSONObject stockSkuJO = JSONObject.parseObject(sku);
                String skuCode = stockSkuJO.getString("stockSku");
                log.info("处理库存sku:{}", skuCode);
                //查询stockSku
                MaBangStockSku maBangStockSku = this.stockDoSearchSkuList(system.getBizId(), skuCode);
                if (maBangStockSku == null) {
                    log.info("maBangStockSku为空");
                    return;
                }
                if (skuCode.contains("delete_")) {
                    return;
                }
                SupplierSkuCreateInfo skuCreateInfo = new SupplierSkuCreateInfo();
                skuCreateInfo.setCustomCode(maBangStockSku.getStockSku());
                skuCreateInfo.setRefProductLink(maBangStockSku.getProductLinkAddress());
                //长度的单位需要转换，供应链分销ERP侧单位为 米（m）
                BigDecimal bigDecimal = new BigDecimal("100");
                if (StringUtils.isNotBlank(maBangStockSku.getLength())) {
                    skuCreateInfo.sizeLength = new BigDecimal(maBangSalesSku.getLength()).divide(bigDecimal,3, RoundingMode.HALF_UP);
                }

                if (StringUtils.isNotBlank(maBangStockSku.getWidth())) {
                    skuCreateInfo.sizeWidth = new BigDecimal(maBangSalesSku.getWidth()).divide(bigDecimal,3, RoundingMode.HALF_UP);;
                }

                if (StringUtils.isNotBlank(maBangStockSku.getHeight())) {
                    skuCreateInfo.sizeHeight = new BigDecimal(maBangSalesSku.getHeight()).divide(bigDecimal,3, RoundingMode.HALF_UP);;
                }
                skuCreateInfo.packingLength = skuCreateInfo.sizeLength;
                skuCreateInfo.packingWidth = skuCreateInfo.sizeWidth;
                skuCreateInfo.packingHeight = skuCreateInfo.sizeHeight;

                //马帮默认为g
                if (StringUtils.isNotBlank(maBangStockSku.getWeight())) {
                    BigDecimal weight = new BigDecimal(maBangStockSku.getWeight()).divide(BigDecimal.valueOf(1000), 3, BigDecimal.ROUND_HALF_UP);
                    skuCreateInfo.grossWeight = weight;
                    skuCreateInfo.netWeight = weight;
                }
                skuCreateInfo.setMoq(1);

                //主规格
                SupplierSkuMainSpecInfo mainSpecInfo = new SupplierSkuMainSpecInfo();
                MultimediaInfo mainImage = new MultimediaInfo();
                mainImage.setType(MultimediaType.IMAGE);
                mainImage.setFileUrl(StringUtils.isBlank(maBangStockSku.getStockPicture()) ? maBangStockSku.getSalePicture() : maBangStockSku.getStockPicture());
                mainSpecInfo.setImage(mainImage);
                skuCreateInfo.mainSpecInfo = mainSpecInfo;
                if(CollectionUtils.isEmpty(mainImages)){
                    mainImages.add(mainImage);
                }

                //规格
                List<MaBangStockSkuAttributes> attributes = maBangStockSku.getAttributes();
                List<SupplierSkuSpecInfo> specs = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(attributes)) {

                    attributes.forEach(attr -> {
                        SupplierSkuSpecInfo supplierSkuSpecInfo = new SupplierSkuSpecInfo();
                        supplierSkuSpecInfo.setSpecValue(attr.getValue());
                        supplierSkuSpecInfo.setSpecName(attr.getName());
                        if (mainSpecInfo.getSpec() == null) {
                            mainSpecInfo.setSpec(supplierSkuSpecInfo);
                        }
                        specs.add(supplierSkuSpecInfo);
                    });
                } else {
                    SupplierSkuSpecInfo supplierSkuSpecInfo = new SupplierSkuSpecInfo();
                    supplierSkuSpecInfo.setSpecName("规格");
                    supplierSkuSpecInfo.setSpecValue(maBangStockSku.getNameCN());
                    if (mainSpecInfo.getSpec() == null) {
                        mainSpecInfo.setSpec(supplierSkuSpecInfo);
                    }
                    specs.add(supplierSkuSpecInfo);
                }
                skuCreateInfo.setSpecs(specs);

                SupplierSkuCreateExtendInfo extendInfo = new SupplierSkuCreateExtendInfo();
                extendInfo.setReferenceStockNum(0);
                List<SupplierItemCreateOrUpdate4SpuCommand> supplierItems = new ArrayList<>();
                SupplierItemCreateOrUpdate4SpuCommand command = new SupplierItemCreateOrUpdate4SpuCommand();

                command.setCountry("CN");
                command.setSaleMode(SupplierItemSaleMode.COUNTRY);
                command.setWarehouseId(params.getWarehouse());
                command.setSupplyPriceCurrency("CNY");
                //采购价
                String purchasePrice = maBangStockSku.getPurchasePrice();
                //成本价
                String defaultCost = maBangStockSku.getDefaultCost();
                if (StringUtils.isNotBlank(defaultCost) && Double.parseDouble(defaultCost) > 0) {
                    command.setSupplyPrice(new BigDecimal(defaultCost));
                } else {
                    command.setSupplyPrice(new BigDecimal(purchasePrice));
                }
                BigDecimal lin = BigDecimal.valueOf(0);
                if (command.getSupplyPrice() == null || (command.getSupplyPrice().compareTo(lin) == 0 || command.getSupplyPrice().compareTo(lin) < 0)) {
                    throw new ServiceException(new BaseErrorInfo("PRICE_CODE", "商品价格不合法"));
                }

                supplierItems.add(command);
                extendInfo.setSupplierItems(supplierItems);
                skuCreateInfo.setExtendInfo(extendInfo);

                //设置默认供应商
                if(StringUtils.isBlank(spuCreateV2Command.getDpsName())){
                    //spuCreateV2Command.setDpsOuterCodeType(DPSOuterCodeType.valueOf("MaBang"));
                    spuCreateV2Command.setDpsName(maBangStockSku.getProvider());
                }
                //是否销售
                if (maBangStockSku.getStatus() != null && maBangStockSku.getStatus().equals(5)) {
                    skuCreateInfo.setSaleState(SupplierSkuSaleState.OFF_SALE);
                }
                skuCreateInfos.add(skuCreateInfo);
            });
        }
        if (CollectionUtils.isEmpty(skuCreateInfos)) {
            throw new ServiceException(new BaseErrorInfo("MABANG_SKU_ISNULL", "马帮商品sku为空"));
        }
        return skuCreateInfos;
    }

    private OpenSupplierSkuCreateInfo buildErpProductSkuForMaBangSalesSku(String thirdPartySystemId, String stockSku, OpenSupplierSpuLogisticsAttrInfo openSupplierSpuLogisticsAttrInfo) {
        //查询stockSku
        MaBangStockSku maBangStockSku = this.stockDoSearchSkuList(thirdPartySystemId, stockSku);

        if (maBangStockSku == null) {
            log.info("maBangStockSku为空");
            return null;
        }

        OpenSupplierSkuCreateInfo supplierSkuCreateInfo = new OpenSupplierSkuCreateInfo();
        supplierSkuCreateInfo.customSkuCode = maBangStockSku.getStockSku();
        supplierSkuCreateInfo.specs = maBangStockSku.getAttributes().stream().map(this::buildOpenSupplierSkuSpecInfo).collect(Collectors.toList());

        //长度的单位需要转换，供应链分销ERP侧单位为 米（m）
        supplierSkuCreateInfo.sizeLength = String.valueOf(Double.parseDouble(maBangStockSku.getLength()) / 100);
        supplierSkuCreateInfo.sizeWidth = String.valueOf(Double.parseDouble(maBangStockSku.getWidth()) / 100);
        supplierSkuCreateInfo.sizeHeight = String.valueOf(Double.parseDouble(maBangStockSku.getHeight()) / 100);
        supplierSkuCreateInfo.packingLength = supplierSkuCreateInfo.sizeLength;
        supplierSkuCreateInfo.packingWidth = supplierSkuCreateInfo.sizeWidth;
        supplierSkuCreateInfo.packingHeight = supplierSkuCreateInfo.sizeHeight;
        //统一用“件”做计量单位
        supplierSkuCreateInfo.setMeasuringUnit("件");
        supplierSkuCreateInfo.mainSpecImageUrl = maBangStockSku.getStockPicture();
        //马帮默认为g
        String weight = new BigDecimal(maBangStockSku.getWeight()).divide(BigDecimal.valueOf(1000), 3, BigDecimal.ROUND_HALF_UP).toString();
        supplierSkuCreateInfo.grossWeight = weight;
        supplierSkuCreateInfo.netWeight = weight;
        //supplierSkuCreateInfo.retailPriceCurrency = swGoodsInfo.getCurrency();

        openSupplierSpuLogisticsAttrInfo.zhDeclaredName = maBangStockSku.getDeclareName();
        openSupplierSpuLogisticsAttrInfo.enDeclaredName = maBangStockSku.getDeclareEname();
        openSupplierSpuLogisticsAttrInfo.hscode = maBangStockSku.getDeclareCode();

        //记录请求结果
        saveApiDockingResult(maBangStockSku, maBangStockSku.getStockSku(), "child-saved", ApiDockingResultType.MaBangStockSku);
        return supplierSkuCreateInfo;
    }

    private void doMaBangProductSyncStockSku(ThirdPartySystem thirdPartySystem, MaBangStockSku maBangStockSku) {
        log.info("构建库存sku：{}请求开始", maBangStockSku.getStockSku());
        try {
            OpenSupplierProductCreateReq req = buildErpProductCreateRequestForMaBangStockSku(thirdPartySystem, maBangStockSku);
            log.info("库存sku：{}商品请求构建完毕，发送商品创建消息", maBangStockSku.getSalesSku());
            this.broadcastProductCreate("PRODUCT_CREATE", JSONObject.toJSONString(req), maBangStockSku.getStockSku(), JSON.toJSONString(maBangStockSku), ApiDockingResultType.MaBangStockSku.name());
        } catch (Exception e) {
            e.printStackTrace();
            log.info(e.getMessage());
            saveApiDockingResult(maBangStockSku, maBangStockSku.getStockSku(), e.getMessage(), ApiDockingResultType.MaBangStockSku);
        }
        log.info("处理库存sku：{}结束", maBangStockSku.getSalesSku());
    }

    private OpenSupplierProductCreateReq buildErpProductCreateRequestForMaBangStockSku(ThirdPartySystem system, MaBangStockSku maBangSku) {
        MaBangParams params = JSON.parseObject(system.getParams(), MaBangParams.class);
        log.info("现在处理库存sku{}", maBangSku.getSalesSku());
        OpenSupplierProductCreateReq req = new OpenSupplierProductCreateReq();
        req.supplierId = params.getSupplierId();
        req.title = StringUtils.isEmpty(maBangSku.getNameEN()) ? maBangSku.getNameCN() : maBangSku.getNameEN();
        req.zhTitle = maBangSku.getNameCN();
        req.enTitle = StringUtils.isEmpty(maBangSku.getNameEN()) ? maBangSku.getNameCN() : maBangSku.getNameEN();
        req.customSpuCode = maBangSku.getStockSku();
        req.customCategoryName = maBangSku.getCategoryName();
        req.detailImageUrls = Lists.newArrayList(maBangSku.getStockPicture());
        req.packingType = maBangSku.getPackingType();
        //TODO 分类id [临时代码]
        req.setCategoryId("PMC0394192409536269647872");

        OpenSupplierSkuCreateInfo supplierSkuCreateInfo = buildErpProductSkuForMaBangStockSku(maBangSku);
        req.skuList = Lists.newArrayList(supplierSkuCreateInfo);

        OpenSupplierSpuLogisticsAttrInfo openSupplierSpuLogisticsAttrInfo = new OpenSupplierSpuLogisticsAttrInfo();
        openSupplierSpuLogisticsAttrInfo.isCharged = maBangSku.getHasBattery() == 1 ? Boolean.TRUE : Boolean.FALSE;
        openSupplierSpuLogisticsAttrInfo.zhDeclaredName = maBangSku.getDeclareName();
        openSupplierSpuLogisticsAttrInfo.enDeclaredName = maBangSku.getDeclareEname();
        openSupplierSpuLogisticsAttrInfo.hscode = maBangSku.getDeclareCode();
        req.logisticsAttrInfo = openSupplierSpuLogisticsAttrInfo;

        req.isAutoAuditPass = true;
        return req;
    }

    private OpenSupplierSkuCreateInfo buildErpProductSkuForMaBangStockSku(MaBangStockSku maBangSku) {
        OpenSupplierSkuCreateInfo supplierSkuCreateInfo = new OpenSupplierSkuCreateInfo();
        supplierSkuCreateInfo.customSkuCode = maBangSku.getStockSku();
        supplierSkuCreateInfo.specs = maBangSku.getAttributes().stream().map(this::buildOpenSupplierSkuSpecInfo).collect(Collectors.toList());
        supplierSkuCreateInfo.sizeLength = String.valueOf(Double.parseDouble(maBangSku.getLength()) / 100);
        supplierSkuCreateInfo.sizeWidth = String.valueOf(Double.parseDouble(maBangSku.getWidth()) / 100);
        supplierSkuCreateInfo.sizeHeight = String.valueOf(Double.parseDouble(maBangSku.getHeight()) / 100);
        supplierSkuCreateInfo.packingLength = supplierSkuCreateInfo.sizeLength;
        supplierSkuCreateInfo.packingWidth = supplierSkuCreateInfo.sizeWidth;
        supplierSkuCreateInfo.packingHeight = supplierSkuCreateInfo.sizeHeight;
        //统一用“件”做计量单位
        supplierSkuCreateInfo.setMeasuringUnit("件");
        supplierSkuCreateInfo.mainSpecImageUrl = maBangSku.getStockPicture();
        //马帮默认为g
        String weight = new BigDecimal(maBangSku.getWeight()).divide(BigDecimal.valueOf(1000), 3, BigDecimal.ROUND_HALF_UP).toString();
        supplierSkuCreateInfo.grossWeight = weight;
        supplierSkuCreateInfo.netWeight = weight;
        return supplierSkuCreateInfo;
    }

    /**
     * 保存请求记录
     *
     * @param obj
     * @param valueKey
     * @param dataStatus
     * @param type
     */
    private void saveApiDockingResult(Object obj, String valueKey, String dataStatus, ApiDockingResultType type) {
        ApiDockingResultCreator apiDockingResultCreator = new ApiDockingResultCreator();
        apiDockingResultCreator.setValueKey(valueKey);
        apiDockingResultCreator.setValueJson(JSON.toJSONString(obj));
        apiDockingResultCreator.setValueType(type.name());
        apiDockingResultCreator.setDataStatus(StringUtils.isEmpty(dataStatus) ? "nullMessage" : dataStatus);
        Optional<ApiDockingResult> existResult = apiDockingResultRepository.loadByKeyAndType(apiDockingResultCreator.getValueKey(), apiDockingResultCreator.getValueType());
        if (existResult.isPresent()) {
            ApiDockingResult apiDockingResult = existResult.get();
            apiDockingResult.setValueKey(apiDockingResultCreator.getValueKey());
            apiDockingResult.setValueType(ApiDockingResultType.valueOf(apiDockingResultCreator.getValueType()));
            apiDockingResult.setValueJson(apiDockingResult.getValueJson());
            apiDockingResult.setDataStatus(apiDockingResultCreator.getDataStatus());
            apiDockingResultRepository.store(apiDockingResult);
        } else {
            ApiDockingResult apiDockingResult = ApiDockingResult.createWith(apiDockingResultCreator);
            apiDockingResultRepository.store(apiDockingResult);
        }
    }

    /**
     * 构建规格信息
     *
     * @param attributes MaBangStockSkuAttributes
     * @return OpenSupplierSkuSpecInfo
     */
    private OpenSupplierSkuSpecInfo buildOpenSupplierSkuSpecInfo(MaBangStockSkuAttributes attributes) {
        OpenSupplierSkuSpecInfo spec = new OpenSupplierSkuSpecInfo();
        spec.specName = attributes.getName();
        spec.specValue = attributes.getValue();
        return spec;
    }

    private MaBangGWApiClient getClient(String maBangParams) {
        return new MaBangGWApiClient(maBangParams);
    }

}

