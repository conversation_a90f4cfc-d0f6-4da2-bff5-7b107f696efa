package com.newnary.gsp.center.tpsi.ctrl.haiying;

import com.newnary.api.base.common.PageList;
import com.newnary.api.base.common.PageMeta;
import com.newnary.gsp.center.tpsi.api.haiying.enums.HaiYingStation;
import com.newnary.gsp.center.tpsi.api.haiying.response.lazada.*;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.lazada.*;
import com.newnary.gsp.center.tpsi.infra.mapper.HaiYingLazadaDataMapper;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/07/14 13:45
 */
public class HaiYingLazadaResponse2DTOTranslator {

    public static PageList<HaiYingLazadaProductListDTO> transLazadaProductListList(HaiYingStation station, List<HaiYingLazadaProductListResponse> response, PageMeta pageMeta) {
        PageList<HaiYingLazadaProductListDTO> ret = new PageList<>();
        List<HaiYingLazadaProductListDTO> list = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingLazadaProductListDTO dto = HaiYingLazadaDataMapper.INSTANCE.transLazadaProductListDTO(resp);
            if (dto.getMain_image()!=null && !dto.getMain_image().contains("http")) {
                dto.setMain_image("https:".concat(dto.getMain_image()));
            }
            switch (station) {
                case LAZADA_MY:
                    dto.setCurrency("MYR");
                    break;
                case LAZADA_ID:
                    dto.setCurrency("IDR");
                    break;
                case LAZADA_PH:
                    dto.setCurrency("PHP");
                    break;
                case LAZADA_TH:
                    dto.setCurrency("THB");
                    break;
                default:
                    break;
            }

            list.add(dto);
        });
        ret.setItems(list);
        ret.setPageMeta(pageMeta);
        return ret;
    }

    public static List<HaiYingLazadaProductDetailInfoDTO> transLazadaProductDetailInfoList(List<HaiYingLazadaProductDetailInfoResponse> response) {
        List<HaiYingLazadaProductDetailInfoDTO> ret = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingLazadaProductDetailInfoDTO dto = HaiYingLazadaDataMapper.INSTANCE.transLazadaProductDetailDTO(resp);

            ret.add(dto);
        });
        return ret;
    }

    public static List<HaiYingLazadaProductExtInfoDTO> transLazadaProductExtInfoList(List<HaiYingLazadaProductExtInfoResponse> response) {
        List<HaiYingLazadaProductExtInfoDTO> ret = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingLazadaProductExtInfoDTO dto = HaiYingLazadaDataMapper.INSTANCE.transLazadaProductExtDTO(resp);

            ret.add(dto);
        });
        return ret;
    }

    public static List<HaiYingLazadaProductHistoryInfoDTO> transLazadaProductHistoryInfoList(List<HaiYingLazadaProductHistoryInfoResponse> response) {
        List<HaiYingLazadaProductHistoryInfoDTO> ret = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingLazadaProductHistoryInfoDTO dto = HaiYingLazadaDataMapper.INSTANCE.transLazadaProductHistoryDTO(resp);

            ret.add(dto);
        });
        return ret;
    }

    public static List<HaiYingLazadaProductHistoryDailyReviewDTO> transLazadaProductHistoryDailyReview(List<HaiYingLazadaProductHistoryDailyReviewResponse> response) {
        List<HaiYingLazadaProductHistoryDailyReviewDTO> ret = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingLazadaProductHistoryDailyReviewDTO dto = HaiYingLazadaDataMapper.INSTANCE.transLazadaProductHistoryDailyReviewDTO(resp);

            ret.add(dto);
        });
        return ret;
    }

    public static List<HaiYingLazadaCategoryTreeDTO> transLazadaCategoryTreeList(List<HaiYingLazadaCategoryTreeResponse> response) {
        List<HaiYingLazadaCategoryTreeDTO> ret = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingLazadaCategoryTreeDTO dto = HaiYingLazadaDataMapper.INSTANCE.transLazadaCategoryTreeDTO(resp);

            ret.add(dto);
        });
        return ret;
    }

    public static List<HaiYingLazadaCategoryInfoDTO> transLazadaCategoryInfoList(List<HaiYingLazadaCategoryInfoResponse> response) {
        List<HaiYingLazadaCategoryInfoDTO> ret = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingLazadaCategoryInfoDTO dto = HaiYingLazadaDataMapper.INSTANCE.transLazadaCategoryInfoDTO(resp);

            ret.add(dto);
        });
        return ret;
    }

    public static PageList<HaiYingLazadaCategoryInfoDTO> transLazadaCategoryInfoPageList(List<HaiYingLazadaCategoryInfoResponse> response, PageMeta pageMeta) {
        PageList<HaiYingLazadaCategoryInfoDTO> ret = new PageList<>();
        List<HaiYingLazadaCategoryInfoDTO> list = new ArrayList<>();
        response.forEach(resp -> {
            HaiYingLazadaCategoryInfoDTO dto = HaiYingLazadaDataMapper.INSTANCE.transLazadaCategoryInfoDTO(resp);

            list.add(dto);
        });
        ret.setItems(list);
        ret.setPageMeta(pageMeta);
        return ret;
    }

}
