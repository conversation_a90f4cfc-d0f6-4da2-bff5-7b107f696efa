package com.newnary.gsp.center.tpsi.infra.client.tongtu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.common.utils.Md5Utils;
import com.newnary.common.utils.httpmethod.ApiBaseResult;
import com.newnary.common.utils.httpmethod.HttpMethodUtil;
import com.newnary.gsp.center.tpsi.infra.client.tongtu.valobj.TongTuApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.tongtu.valobj.TongTuAppBuyer;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/12/10 11:34
 */
public class TongTuApiClient {

    public static final Logger LOGGER = LoggerFactory.getLogger(TongTuApiClient.class);

    public static final String BASE_URL = "https://open.tongtool.com";

    private String accessKey;
    private String secretAccessKey;

    private String listingAccessKey;
    private String listingSecretAccessKey;

    private String warehouse;

    private String token;
    private String merchantId;

    private String listingToken;
    private String listingMerchantId;

    public TongTuApiClient(String tongTuParams) {
        TongTuParams params = JSON.parseObject(tongTuParams, TongTuParams.class);
        this.accessKey = params.getAccessKey();
        this.secretAccessKey = params.getSecretAccessKey();
        this.listingAccessKey = params.getListingAccessKey();
        this.listingSecretAccessKey = params.getListingSecretAccessKey();
        this.warehouse = params.getWarehouse();

        this.token = this.getToken(accessKey, secretAccessKey);
        this.merchantId = this.getMerchantId(token, secretAccessKey);

        if (StringUtils.isNotEmpty(listingAccessKey) && StringUtils.isNotEmpty(listingSecretAccessKey)) {
            this.listingToken = this.getToken(listingAccessKey, listingSecretAccessKey);
            this.listingMerchantId = this.getMerchantId(listingToken, listingSecretAccessKey);
        }
    }

    private String getMerchantId(String token, String secretAccessKey) {
        TongTuApiBaseResult<JSONArray> appBuyerListResult = this.getAppBuyerList(token, secretAccessKey);
        TongTuAppBuyer appBuyer = JSONObject.parseObject(appBuyerListResult.getDatas().getJSONObject(0).toString(), TongTuAppBuyer.class);
        return appBuyer.getPartnerOpenId();
    }


    public TongTuApiBaseResult<String> syncPostMethod(String targetUrl, Map<String, Object> bodyParas) {
        long now = System.currentTimeMillis();
        Map<String, String> header = new HashMap<String, String>(1);
        header.put("api_version", "3.0");

        Map<String, Object> urlParas = new HashMap<>(4);
        urlParas.put("app_token", token);
        urlParas.put("timestamp", now);
        urlParas.put("sign", sign(token, now, secretAccessKey));
        try {
            ApiBaseResult apiBaseResult = HttpMethodUtil.syncPostMethod(BASE_URL,
                    3,
                    targetUrl,
                    "application/json",
                    header, urlParas, bodyParas);

            return JSON.parseObject(apiBaseResult.getRet(), new TypeReference<TongTuApiBaseResult<String>>() {
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public TongTuApiBaseResult<String> listingSyncPostMethod(String targetUrl, Map<String, Object> bodyParas) {
        long now = System.currentTimeMillis();
        Map<String, String> header = new HashMap<String, String>(1);
        header.put("api_version", "3.0");

        Map<String, Object> urlParas = new HashMap<>(4);
        urlParas.put("app_token", listingToken);
        urlParas.put("timestamp", now);
        urlParas.put("sign", sign(listingToken, now, listingSecretAccessKey));
        try {
            ApiBaseResult apiBaseResult = HttpMethodUtil.syncPostMethod(BASE_URL,
                    3,
                    targetUrl,
                    "application/json",
                    header, urlParas, bodyParas);

            return JSON.parseObject(apiBaseResult.getRet(), new TypeReference<TongTuApiBaseResult<String>>() {
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取sign参数
     *
     * @param appToken        token
     * @param timestamp       时间戳
     * @param secretAccessKey 密码
     * @return 加密后的sign参数
     */
    private String sign(String appToken, long timestamp, String secretAccessKey) {
        //拼接参数sign的md5加密前的数据
        String strBeforeMd5 = "app_token".concat(appToken)
                .concat("timestamp").concat(String.valueOf(timestamp))
                .concat(secretAccessKey);
        //使用nacos的公共工具进行MD5并返回
        return Md5Utils.getMD5(strBeforeMd5, "UTF-8");
    }

    /**
     * 获取token
     *
     * @param accessKey       accessKey
     * @param secretAccessKey secretAccessKey
     * @return
     */
    public String getToken(String accessKey, String secretAccessKey) {
        Map<String, Object> urlParas = new HashMap<>(2);
        urlParas.put("accessKey", accessKey);
        urlParas.put("secretAccessKey", secretAccessKey);

        try {
            ApiBaseResult apiBaseResult = HttpMethodUtil.syncGetMethod(BASE_URL, 3,
                    "/open-platform-service/devApp/appToken", "application/json",
                    null, urlParas);

            TongTuApiBaseResult<String> ret = JSON.parseObject(apiBaseResult.getRet(), new TypeReference<TongTuApiBaseResult<String>>() {
            });
            return ret.getDatas();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * partner-open-info-controller
     *
     * @param appToken
     * @param secretAccessKey
     * @return
     */
    public TongTuApiBaseResult getAppBuyerList(String appToken, String secretAccessKey) {
        long now = System.currentTimeMillis();

        Map<String, Object> urlParas = new HashMap<>(4);
        urlParas.put("app_token", appToken);
        urlParas.put("timestamp", now);
        urlParas.put("sign", sign(appToken, now, secretAccessKey));

        try {
            ApiBaseResult apiBaseResult = HttpMethodUtil.syncGetMethod(BASE_URL,
                    3,
                    "/open-platform-service/partnerOpenInfo/getAppBuyerList",
                    "application/json",
                    null,
                    urlParas);

            TongTuApiBaseResult<JSONArray> ret = JSONObject.toJavaObject(JSON.parseObject(apiBaseResult.getRet()), TongTuApiBaseResult.class);
            return ret;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 商品信息查询
     *
     * @param bodyParas
     * @return
     */
    public TongTuApiBaseResult<String> goodsQuery(Map<String, Object> bodyParas) {
        bodyParas.put("merchantId", merchantId);
        return syncPostMethod("/api-service/openapi/tongtool/goodsQuery", bodyParas);
    }

    public TongTuApiBaseResult<String> stocksQuery(Map<String, Object> bodyParas) {
        bodyParas.put("merchantId", merchantId);
        return syncPostMethod("/api-service/openapi/tongtool/stocksQuery", bodyParas);
    }

    public TongTuApiBaseResult<String> warehouseQuery(Map<String, Object> bodyParas) {
        //bodyParas.put("warehouseName", warehouse);
        bodyParas.put("merchantId", merchantId);
        return syncPostMethod("/api-service/openapi/tongtool/warehouseQuery", bodyParas);
    }

    public TongTuApiBaseResult<String> ordersQuery(Map<String, Object> bodyParas) {
        bodyParas.put("merchantId", merchantId);
        return syncPostMethod("/api-service/openapi/tongtool/ordersQuery", bodyParas);
    }

    public TongTuApiBaseResult<String> orderImport(Map<String, Object> bodyParas) {
        bodyParas.put("merchantId", merchantId);
        return syncPostMethod("/api-service/openapi/tongtool/orderImport", bodyParas);
    }

    public TongTuApiBaseResult<String> listingSaleProductQuery(Map<String, Object> bodyParas) {
        bodyParas.put("merchantId", listingMerchantId);
        return listingSyncPostMethod("/api-service/openapi/tongtool/listing/saleProduct/getProductInfoByParam", bodyParas);
    }

    public TongTuApiBaseResult<String> listingProductQuery(Map<String, Object> bodyParas) {
        bodyParas.put("merchantId", listingMerchantId);
        return listingSyncPostMethod("/api-service/openapi/tongtool/listing/product/getProductInfoByParam", bodyParas);
    }

    public TongTuApiBaseResult<String> listProductIdQuery(Map<String, Object> bodyParas) {
        bodyParas.put("merchantId", listingMerchantId);
        return listingSyncPostMethod("/api-service/openapi/tongtool/listing/saleProduct/listProductId", bodyParas);
    }

}
