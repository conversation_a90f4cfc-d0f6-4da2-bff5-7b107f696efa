package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.wms;

import java.util.List;

public class EcCangFreightGetOrderInfoResponse {
    /**
     * 平台订单号、参考号
     */
    public String freight_order_code;

    /**
     * wms订单号
     */
    public String order_code;

    public Integer warehouse_id;
    /**
     * 订单状态
     * 1、待签收：订单的初始状态；
     * 2、部分签收：集运订单多包裹，部分被签收的状态；
     * 3、待验货：集运订单所有包裹被签收，待拆包验货的状态；
     * 4、待打单：集运订单所有包裹产品都被验货，待打印面单标签的状态；
     * 5、待装箱：集运订单已打印面单标签，等待装箱出库的状态；
     * 6、已取消：集运订单被取消的状态；
     * 7、已完成：集运订单被完结的状态（订单装箱完成）
     */
    public Integer order_status;

    /**
     * 出库面单号
     */
    public String order_label_code;

    /**
     * 装箱单号
     */
    public String box_code;

    public Double weight;
    public String volume;
    public Double volume_weight;

    public List<String> box_attachment;

    public Integer is_abnormal;

    public String box_time;
    public String customer_code;

    public List<ProductData> product_data;

    public List<CostLog> cost_log;

    public static class ProductData {
        public String product_id;
        public String child_code;
        public String package_code;
        public String product_standard;
        public Integer product_count;
        public String product_name_cn;
        public String product_name_en;
        public Double product_declared;
        public String hs_code;
        public String back_label_code;
        public Integer product_status;
        public String lc_code;
        public String abnormal_note;
        public String abnormal_remark;
        public List<String> abnormal_attachment;
        public List<String> product_img;
        public String sign_time;
        public String box_create_time;
    }

    public static class CostLog {
        public String ft_name_cn;
        public String cbl_note;
        public String cbl_transaction_value;
        public String cbl_value;
        public String currency_code;
        public String ft_code;
        public String cbl_add_time;
    }
}
