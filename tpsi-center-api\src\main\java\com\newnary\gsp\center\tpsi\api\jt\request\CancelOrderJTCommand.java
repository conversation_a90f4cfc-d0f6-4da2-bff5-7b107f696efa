package com.newnary.gsp.center.tpsi.api.jt.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 「取消」能者物流运输单，请求体
 *
 * <AUTHOR>
 * @since Created on 2023-08-11
 **/
@Data
public class CancelOrderJTCommand {

    @NotBlank(message = "eccompanyid(不能为空)")
    @Size(max = 20,message = "最大字符(20)")
    private String eccompanyid;

    /**
     * 客户标识
     */
    @NotBlank(message = "customerid(不能为空)")
    @Size(max = 20,message = "最大字符(20)")
    private String customerid;

    /**
     * 物流订单号
     */
    @NotBlank(message = "txlogisticid(不能为空)")
    @Size(max = 40,message = "最大字符(40)")
    private String txlogisticid;

    /**
     * 所属国家
     */
    @NotBlank(message = "country(不能为空)")
    @Size(max = 40,message = "最大字符(40)")
    private String country;

    /**
     * 取消原因
     **/
    @NotBlank(message = "reason(不能为空)")
    @Size(max = 40,message = "最大字符(40)")
    private String reason;



}
