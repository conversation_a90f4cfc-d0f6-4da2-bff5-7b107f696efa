package com.newnary.gsp.center.tpsi.service.mabang;

import com.newnary.gsp.center.tpsi.api.mabang.request.DoDeliverOrderInMaBangCommand;
import com.newnary.gsp.center.tpsi.api.mabang.request.PushOrder2MaBangCommand;
import com.newnary.gsp.center.tpsi.api.mabang.request.SyncOrderFromMaBangCommand;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.MaBangOrder;
import com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.order.MaBangDoCreateOrder;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;

public interface IMaBangOrderApiSve {

    void orderGetOrderList(SyncOrderFromMaBangCommand req);

    /**
     * 接收者处理订单同步时调用，负责创建订单
     *
     * @param thirdPartySystem
     * @param data
     */
    void doOrderSync(ThirdPartySystem thirdPartySystem, String data);

    String orderDoCreateOrder(PushOrder2MaBangCommand req);

    String orderDoDeliverOrder(DoDeliverOrderInMaBangCommand req);

    String orderDoCreateOrder(String thirdPartySystemId, MaBangDoCreateOrder req);

    /**
     * 根据id和状态查询具体马帮订单
     *
     * @param thirdPartySystemId
     * @param platformOrderId
     * @return
     */
    MaBangOrder orderGetOrderListByIdStatus(String thirdPartySystemId, String platformOrderId, String status);

    /**
     * 作废订单
     */
    String invalidOrder(String thirdPartySystemId, String platformOrderId);
}
