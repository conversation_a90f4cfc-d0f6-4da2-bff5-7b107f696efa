<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.newnary.gsp</groupId>
        <artifactId>parent</artifactId>
        <version>1.0.0</version>
    </parent>

    <groupId>com.newnary.gsp.center.tpsi</groupId>
    <artifactId>tpsi-center</artifactId>
    <packaging>pom</packaging>
    <version>2.0.0-SNAPSHOT</version>
    <name>tpsi-center</name>
    <modules>
        <module>tpsi-center-api</module>
        <module>tpsi-center-main</module>
        <module>tpsi-center-test</module>
    </modules>

    <properties>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.source>1.8</maven.compiler.source>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <!--<lombok.version>1.18.8</lombok.version>-->
        <slf4j.version>1.7.25</slf4j.version>
        <junit.version>4.13.1</junit.version>
        <jacoco.version>0.8.5</jacoco.version>
        <poi.version>3.10-FINAL</poi.version>
        <!--<spring.version>4.3.12.RELEASE</spring.version>-->
        <!--<mapstruct.version>1.3.1.Final</mapstruct.version>-->

        <lombok.version>1.18.16</lombok.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <!--<spring.version>4.3.12.RELEASE</spring.version>-->
        <org.mapstruct.version>1.4.2.Final</org.mapstruct.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-framework-bom</artifactId>
                <version>5.2.10.RELEASE</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--project modules-->
            <dependency>
                <groupId>com.newnary.gsp.center.tpsi</groupId>
                <artifactId>tpsi-center-api</artifactId>
                <version>2.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.newnary.gsp.center.tpsi</groupId>
                <artifactId>tpsi-center-main</artifactId>
                <version>2.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.newnary.gsp.center.tpsi</groupId>
                <artifactId>tpsi-center-test</artifactId>
                <version>2.0.0-SNAPSHOT</version>
                <scope>test</scope>
            </dependency>

            <!-- 2 party common sdk -->
            <dependency>
                <groupId>com.newnary</groupId>
                <artifactId>api-base</artifactId>
                <version>1.0.3-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.newnary</groupId>
                <artifactId>spring-cloud-starter</artifactId>
                <version>1.0.3-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.newnary</groupId>
                <artifactId>test-starter</artifactId>
                <version>1.0.3-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.newnary</groupId>
                <artifactId>dao-starter</artifactId>
                <version>1.0.2-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.newnary</groupId>
                <artifactId>distributed-tools-starter</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.newnary</groupId>
                <artifactId>mq-starter</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.newnary</groupId>
                <artifactId>job-starter</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.newnary</groupId>
                <artifactId>common-utils</artifactId>
                <version>1.0.2-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.newnary</groupId>
                <artifactId>message-body</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.newnary</groupId>
                <artifactId>oss-starter</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.newnary.gsp.center.product</groupId>
                <artifactId>product-center-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.newnary.gsp.transfer.sunway</groupId>
                <artifactId>sunway-transfer-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.newnary.gsp.center.trade</groupId>
                <artifactId>trade-center-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.newnary.gsp.center.logistics</groupId>
                <artifactId>logistics-center-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.newnary.gsp.center.basicdata</groupId>
                <artifactId>basic-data-center-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.newnary.gsp.center.crawlerproxy</groupId>
                <artifactId>crawler-proxy-center-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.newnary.gsp.center.purchase</groupId>
                <artifactId>purchase-center-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.newnary.gsp.center.wms</groupId>
                <artifactId>wms-center-api</artifactId>
                <version>1.3.7-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.newnary.gsp.center.stock</groupId>
                <artifactId>stock-center-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>


            <dependency>
                <groupId>com.newnary.gsp.center.user</groupId>
                <artifactId>user-center-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <!-- third party sdk -->
            <dependency>
                <groupId>com.newnary</groupId>
                <artifactId>newnary-monitor-starter</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.newnary.gsp.center.tms</groupId>
                <artifactId>tms-center-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.12</version>
                <scope>compile</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <defaultGoal>package</defaultGoal>

        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <!-- 是否替换资源中的属性-->
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.21.0</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
        <testSourceDirectory>/src/test/java</testSourceDirectory>
    </build>
</project>