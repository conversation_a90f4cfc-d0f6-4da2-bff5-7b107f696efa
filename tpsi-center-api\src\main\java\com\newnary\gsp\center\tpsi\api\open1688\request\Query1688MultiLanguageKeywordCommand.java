package com.newnary.gsp.center.tpsi.api.open1688.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

// 多语言关键词搜索
@Data
public class Query1688MultiLanguageKeywordCommand {

    /**
     * 搜索关键词
     */
    @NotBlank(message = "关键词不能为空")
    private String keyword;

    /**
     * 商品资料语言
     */
    @NotBlank(message = "商品资料语言不能为空")
    private String country;

    /**
     * 分页起始页
     */
    @NotBlank(message = "分页起始页不能为空")
    private Integer beginPage;

    /**
     * 分页大小
     */
    @NotBlank(message = "分页大小不能为空")
    private Integer pageSize;

}
