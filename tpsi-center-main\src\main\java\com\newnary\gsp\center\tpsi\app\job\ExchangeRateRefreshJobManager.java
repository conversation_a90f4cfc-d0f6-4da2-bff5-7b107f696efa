package com.newnary.gsp.center.tpsi.app.job;

import com.alibaba.fastjson.JSONObject;
import com.newnary.gsp.center.basicdata.api.forexrate.feign.ForexRateFeignApi;
import com.newnary.gsp.center.basicdata.api.forexrate.request.ForexRateCreateCommand;
import com.newnary.gsp.center.tpsi.infra.client.exchangeRate.ExchangeRateClient;
import com.newnary.gsp.center.tpsi.service.exchangeRate.ExchangeRateRefreshSve;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.job.core.handler.annotation.Job;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ExchangeRateRefreshJobManager {

    @Resource
    private ExchangeRateRefreshSve exchangeRateRefreshSve;
    @Resource
    private ExchangeRateClient exchangeRateClient;
    @Resource
    private ForexRateFeignApi forexRateFeignApi;

    @Job("autoRefreshExchangeRate")
    public ReturnT<String> autoRefreshExchangeRate(String param) {
        log.info("定时任务刷新汇率 - 开始 --- {}", new Date());
        //刷新汇率数据
        exchangeRateRefreshSve.refreshExchangeRate("CNY");
        log.info("定时任务刷新汇率  - 结束 --- {}", new Date());
        return ReturnT.SUCCESS;
    }

    @Job("autoRefreshBasicForexRate")
    public ReturnT<String> autoRefreshBasicForexRate(String params) {
        JSONObject paramsJson = JSONObject.parseObject(params);
        Integer effectiveDays = Optional.ofNullable(paramsJson.getInteger("effectiveDays")).orElse(3);
        String sourceCode = paramsJson.getString("sourceCode");
        Set<String> targetCodeSet = new HashSet<>(paramsJson.getJSONArray("targetCodes").toJavaList(String.class));

        String data = exchangeRateClient.getExchangeRateBySourceCurrency(sourceCode);
        log.info("获取到最新汇率表: {}", data);

        if (StringUtils.isEmpty(data) || !data.startsWith("{")) {
            log.info("获取最新汇率失败, 没有返回数据或不是JSON");
            return ReturnT.FAIL;
        }

        JSONObject dataJson = JSONObject.parseObject(data);
        if ("success".equals(dataJson.getString("result"))) {
            Currency source = Currency.getInstance(dataJson.getString("base_code"));
            Long timeLastUpdateUnix = dataJson.getLong("time_last_update_unix") * 1000;

            JSONObject conversionRates = dataJson.getJSONObject("conversion_rates");
            Set<String> keySet = conversionRates.keySet();
            Set<ForexRateCreateCommand> commandSet = keySet.stream()
                    .filter(targetCodeSet::contains)
                    .map(target -> {
                        BigDecimal rate = conversionRates.getBigDecimal(target);

                        ForexRateCreateCommand command = new ForexRateCreateCommand();
                        command.setSource(source);
                        command.setTarget(Currency.getInstance(target));
                        command.setReleaseTime(timeLastUpdateUnix);
                        command.setRate(rate);
                        command.setEffectiveBegin(Instant.now().toEpochMilli());
                        command.setEffectiveEnd(Instant.now().plus(Duration.ofDays(effectiveDays)).toEpochMilli());
                        return command;
                    }).collect(Collectors.toSet());

            List<String> forexRateIds = forexRateFeignApi.batchCreate(commandSet).mustSuccessOrThrowOriginal();
            log.info("最新汇率ID集: {}", forexRateIds);
            return ReturnT.SUCCESS;
        } else {
            log.info("获取最新汇率失败, result不是success");
            return ReturnT.FAIL;
        }
    }

}
