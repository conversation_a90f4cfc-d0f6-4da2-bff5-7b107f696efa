package com.newnary.gsp.center.tpsi.infra.mapper;

import com.newnary.gsp.center.tpsi.infra.model.creator.ApiRequestParamsCreator;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.ApiRequestParamsPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ApiRequestParamsMapper {

    ApiRequestParamsMapper INSTANCE = Mappers.getMapper(ApiRequestParamsMapper.class);

    ApiRequestParamsCreator po2ModelCreator(ApiRequestParamsPO po);
}
