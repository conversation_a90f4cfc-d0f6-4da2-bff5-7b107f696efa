package com.newnary.gsp.center.tpsi.app.service.eccang;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.distributed.tools.concurrent.template.DConcurrentTemplate;
import com.newnary.gsp.center.purchase.api.order.request.BatchTakeStockCommand;
import com.newnary.gsp.center.purchase.api.order.request.EntryScanningCommand;
import com.newnary.gsp.center.purchase.api.order.response.PurchaseOrderGoodsInfo;
import com.newnary.gsp.center.purchase.api.order.response.PurchaseOrderInfo;
import com.newnary.gsp.center.purchase.api.product.request.UpdateGoodsSizeCommand;
import com.newnary.gsp.center.purchase.api.product.response.SkuDetailInfo;
import com.newnary.gsp.center.purchase.api.product.response.SkuInfo;
import com.newnary.gsp.center.purchase.api.supplier.response.SupplierGoodsInfo;
import com.newnary.gsp.center.tpsi.api.common.enums.Gsp2EccangPOCreateEventBizState;
import com.newnary.gsp.center.tpsi.api.common.enums.Gsp2EccangPOSyncTrackingNumberEventBizState;
import com.newnary.gsp.center.tpsi.api.common.enums.ThirdPartEventType;
import com.newnary.gsp.center.tpsi.api.common.enums.ThirdPartyEventState;
import com.newnary.gsp.center.tpsi.api.eccang.request.GspSyncPo2EccangCommand;
import com.newnary.gsp.center.tpsi.infra.client.eccang.mapping.EcCangERPMapping;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp.*;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.erp.*;
import com.newnary.gsp.center.tpsi.infra.model.ApiRequestParams;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.ApiRequestParamsType;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingInfo;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingType;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyPushLogInfo;
import com.newnary.gsp.center.tpsi.infra.repository.IApiRequestParamsRepository;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.ThirdPartyMappingManager;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.ThirdPartyPushLogManager;
import com.newnary.gsp.center.tpsi.infra.rpc.PurchaseRpc;
import com.newnary.gsp.center.tpsi.service.eccang.IEccangERPApiSve;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class EccangPoMgmtApp {

    @Value("${tpsi.bizIds}")
    private String bizIds;

    private static String MGMT_PREFIX = EccangPoMgmtApp.class.getName();

    @Resource
    private ThirdPartyPushLogManager thirdPartyPushLogManager;
    @Resource
    private IApiRequestParamsRepository apiOrderCreateParamsRepository;
    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;
    @Resource
    private PurchaseRpc purchaseRpc;
    @Resource
    private IEccangERPApiSve eccangERPApiSveImpl;
    @Resource
    private ThirdPartyMappingManager thirdPartyMappingManager;

    /**
     * 同步采购单到易仓
     *
     * @param command
     */
    public void doSync(GspSyncPo2EccangCommand command) {
        JSONObject jsonObject = JSON.parseObject(bizIds);
        String bizId = jsonObject.getString("ECCANG");
        ThirdPartySystem thirdPartySystem = loadSystem(bizId);
        if (null == thirdPartySystem) {
            log.info("易仓采购收货消息处理，未匹配到易仓对接信息，消息无需处理！");
            return;
        }
        DConcurrentTemplate.tryLockMode(
                MGMT_PREFIX.concat(command.getPurchaseOrderId()),
                lock -> lock.tryLock(20, TimeUnit.SECONDS),
                () -> {
                    // 构造日志记录
                    ThirdPartyPushLogInfo orderCreateLogInfo = new ThirdPartyPushLogInfo();
                    orderCreateLogInfo.setEventType(ThirdPartEventType.GSP_2_ECCANG_PO_CREATE.name());
                    orderCreateLogInfo.setEventBizId(command.getPurchaseOrderId());
                    orderCreateLogInfo.setEventData(JSON.toJSONString(command));
                    orderCreateLogInfo.setEventState(ThirdPartyEventState.FAIL.getValue());
                    try {
                        // 0. 同步初始化
                        orderCreateLogInfo.setEventBizState(Gsp2EccangPOCreateEventBizState.SYNC_PO_INIT.getValue());
                        orderCreateLogInfo.setSysRemark("准备处理消息");
                        thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                        // 1. 获取新采购系统采购详情
                        PurchaseOrderInfo purchaseOrderInfo = purchaseRpc.queryOrderDetail(Long.valueOf(command.getPurchaseOrderId()));
                        // 2. 获取易仓采购单详情
                        ApiRequestParams orderParams = apiOrderCreateParamsRepository.loadBySystemBizIdAndType(bizId, ApiRequestParamsType.PURCHASE_ORDER.name()).get();
                        String ecCangReceivingCode = getReceivingCodeInEcCang(orderCreateLogInfo, thirdPartySystem, orderParams, purchaseOrderInfo);
                        if (StringUtils.isBlank(ecCangReceivingCode)) {
//                            log.error("采购单获取或创建失败，拿不到入库单号，消息不消费，直接丢弃，后续通过补偿操作！id: {}", command.getPurchaseOrderId());
                            orderCreateLogInfo.setSysRemark("采购单获取或创建失败，拿不到入库单号，消息不消费，直接丢弃，后续通过补偿操作");
                            orderCreateLogInfo.setEventBizState(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_FAILED.getValue());
                            thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                            return;
                        } else {
                            orderCreateLogInfo.setEventBizState(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_CREATED.getValue());
                            thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                        }
                        // 3. 执行入库操作
                        ThirdPartyMappingInfo userMapping = thirdPartyMappingManager.getNewestInfoBySource("ECCANG", "GSP", command.getCurrOptUserId(), ThirdPartyMappingType.USER_ID2CODE);
                        String ecReceivingUserCode = null;
                        if (userMapping != null) {
                            ecReceivingUserCode = userMapping.getTargetId();
                        }
                        if (StringUtils.isBlank(ecReceivingUserCode)) {
                            // TODO 暂时设置一个默认的
                            ecReceivingUserCode = "newnary";
                        }
                        doPurchaseOrderReceiving(thirdPartySystem, ecCangReceivingCode, purchaseOrderInfo, ecReceivingUserCode, orderCreateLogInfo);
                        //4.更新商品的包装尺寸
                        updateEcProductInfo(thirdPartySystem, purchaseOrderInfo);
                    } catch (Exception e) {
                        log.error("易仓同步采购订单失败：id: {}", command.getPurchaseOrderId(), e);

                        orderCreateLogInfo.setSysRemark("未知异常");
                        orderCreateLogInfo.setEventState(ThirdPartyEventState.FAIL.getValue());
                        thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                    }
                }
        );
    }

    private void updateEcProductInfo(ThirdPartySystem thirdPartySystem, PurchaseOrderInfo purchaseOrderInfo) {
        List<String> skuIds = purchaseOrderInfo.getPurchaseOrderGoodsList().stream().map(PurchaseOrderGoodsInfo::getSku).collect(Collectors.toList());
        skuIds.forEach(sku -> {
            SkuDetailInfo skuDetailInfo = purchaseRpc.getBySku(sku);
            SkuInfo skuInfo = skuDetailInfo.getSkuInfo();

            // 检查是否存在已同步的, 减少易仓API调用次数
            ThirdPartyPushLogInfo thirdPartyPushLogInfo = thirdPartyPushLogManager.queryByEventBizId(ThirdPartEventType.GSP_2_ECCANG_PO_CREATE_PRODUCT_SUB.name(), skuInfo.getSkuId());
            if (thirdPartyPushLogInfo != null && Objects.equals(String.valueOf(skuInfo.getGmtModified()), thirdPartyPushLogInfo.getSysRemark())) {
                return;
            }

            EcCangERPSyncProductRequest request = new EcCangERPSyncProductRequest();
            request.actionType = "EDIT";
            request.productSku = skuInfo.getSku();
            if (ObjectUtils.allNotNull(skuInfo.getSizeLength(), skuInfo.getSizeHeight(), skuInfo.getSizeWidth(), skuInfo.getNetWeight())) {
                request.productLength = skuInfo.getSizeLength().doubleValue();
                request.productWidth = skuInfo.getSizeWidth().doubleValue();
                request.productHeight = skuInfo.getSizeHeight().doubleValue();
                request.pdNetWeight = skuInfo.getNetWeight().doubleValue();
                EcCangApiBaseResult<String> result = eccangERPApiSveImpl.syncProduct(thirdPartySystem, request);

                if (StringUtils.equals(result.getMessage(), "Success")) {
                    // 记录易仓API调用日志
                    ThirdPartyPushLogInfo skuSyncLogInfo = new ThirdPartyPushLogInfo();
                    skuSyncLogInfo.setEventType(ThirdPartEventType.GSP_2_ECCANG_PO_CREATE_PRODUCT_SUB.name());
                    skuSyncLogInfo.setEventBizId(skuInfo.getSkuId());
                    skuSyncLogInfo.setEventState(ThirdPartyEventState.SUCCESS.getValue());
                    skuSyncLogInfo.setSysRemark(String.valueOf(skuInfo.getGmtModified()));
                    thirdPartyPushLogManager.insertOrUpdate(skuSyncLogInfo);
                }
            }
        });
    }

    /**
     * 获取易仓入库单号
     *
     * @param gspPurchaseOrderInfo
     * @return
     */
    private String getReceivingCodeInEcCang(ThirdPartyPushLogInfo orderCreateLogInfo, ThirdPartySystem thirdPartySystem, ApiRequestParams orderParams, PurchaseOrderInfo gspPurchaseOrderInfo) {
        EcCangERPGetPurchaseOrdersResponse response = null;
        // 1. 第一次查询（根据采购系统orderCode）
        EcCangApiBaseResult<String> purchaseOrders = eccangERPApiSveImpl.getPurchaseOrdersNoDecode(gspPurchaseOrderInfo.getOrderCode(), thirdPartySystem, null, null);
        if (StringUtils.isNotBlank(purchaseOrders.getData()) && purchaseOrders.getCode().equals("200")) {
            List<EcCangERPGetPurchaseOrdersResponse> responseList = JSON.parseArray(purchaseOrders.getData(), EcCangERPGetPurchaseOrdersResponse.class);
            if (CollectionUtils.isNotEmpty(responseList)) {
                response = responseList.get(0);
                orderCreateLogInfo.setSysRemark("通过OrderCode获取到，之前已创建过");
                return response.getReceiving_code();
            }
        } else if (StringUtils.equals("500002", purchaseOrders.getCode()) || purchaseOrders.getCode().equalsIgnoreCase("equalsIgnoreCase")) {
            //调用频繁创建拦截,防止重复创建
            throw new ServiceException(CommonErrorInfo.ERROR_111_RATE_LIMIT_ERROR, "易仓调用频繁:" + purchaseOrders.getMessage());
        }
        // 2. 第二次查询（根据采购系统主键id）（兼容逻辑，23/11/10 23点前，可能用的是主键id作为易仓采购单的参考号）
        if (gspPurchaseOrderInfo.getGmtCreate() < 1699628400000L) {
            log.info("采购单命中查询兼容逻辑（23-11-10 23:00:00）前采购系统创建的订单，可能是通过采购单主键id关联的，需要兼容再次查询下！orderId={}", gspPurchaseOrderInfo.getId());
            purchaseOrders = eccangERPApiSveImpl.getPurchaseOrders(String.valueOf(gspPurchaseOrderInfo.getId()), thirdPartySystem);
            if (StringUtils.isNotBlank(purchaseOrders.getData())) {
                List<EcCangERPGetPurchaseOrdersResponse> responseList = JSON.parseArray(purchaseOrders.getData(), EcCangERPGetPurchaseOrdersResponse.class);
                if (CollectionUtils.isNotEmpty(responseList)) {
                    response = responseList.get(0);
                    orderCreateLogInfo.setSysRemark("通过OrderId获取到，之前已创建过");
                    return response.getReceiving_code();
                }
            }
        }
        // 3. 不存在直接创建（修改对应易仓采购单的状态为已审核【3】）
        return createEcangPurchaseOrder(orderCreateLogInfo, thirdPartySystem, orderParams, gspPurchaseOrderInfo);
    }

    /**
     * 创建易仓订单
     *
     * @param thirdPartySystem
     * @param apiRequestParams
     * @param purchaseOrderInfo
     * @return
     */
    private String createEcangPurchaseOrder(ThirdPartyPushLogInfo orderCreateLogInfo, ThirdPartySystem thirdPartySystem, ApiRequestParams apiRequestParams, PurchaseOrderInfo purchaseOrderInfo) {
        log.info("GSP采购单数据！purchaseOrderInfo={}", JSON.toJSONString(purchaseOrderInfo));
        // 1. 执行同步创建
        EcCangERPSyncPurchaseOrdersRequest request = buildEcCangERPSyncPurchaseOrdersRequest(thirdPartySystem, apiRequestParams, purchaseOrderInfo);
        EcCangApiBaseResult<String> result = eccangERPApiSveImpl.syncPurchaseOrders(thirdPartySystem, request);
        if ("200".equals(result.getCode())) {
            orderCreateLogInfo.setRespData(result.getData());
            orderCreateLogInfo.setSysRemark("在易仓创建采购单成功");
            //TODO 后续需校验是否绑定采购单三方单号成功
            purchaseRpc.updateThirdOrderCode(purchaseOrderInfo.getId(), JSON.parseObject(result.getData()).getString("poCode"));
            return JSON.parseObject(result.getData()).getString("receivingCode");
        }
        // 2. 异常场景
//        if (result.getMessage().contains("查无产品")) {
//            // 说明采购系统供应商名称和易仓供应商名称可以匹配上，但是易仓采购供应商下没有这些商品，需要进行创建
//            ThirdPartyMappingInfo supplierMapping = thirdPartyMappingManager.getNewestInfoBySource("ECCANG", "SHENGWEI", purchaseOrderInfo.getSupplierName(), ThirdPartyMappingType.GSP_EC_SUP_N_ID);
//            for (String sku : result.getMessage().split("\\|")) {
//                try {
//                    // 在易仓sku下，新增【默认供应商】绑定，临时逻辑
//                    EcCangERPSyncSupplierProductRequest ecCangERPSyncSupplierProductRequest = new EcCangERPSyncSupplierProductRequest();
//                    ecCangERPSyncSupplierProductRequest.setActionType("ADD");
//                    ecCangERPSyncSupplierProductRequest.setSupplierId(Integer.valueOf(supplierMapping.getTargetId()));
//                    ecCangERPSyncSupplierProductRequest.setSpSupplierProductCode(sku.substring(sku.lastIndexOf("查无产品") + 4));
//                    ecCangERPSyncSupplierProductRequest.setSpPurchaseUnit(0);
//                    ecCangERPSyncSupplierProductRequest.setSpDefault(0);
//                    ecCangERPSyncSupplierProductRequest.setSpMinQty(1);
//                    ecCangERPSyncSupplierProductRequest.setSpEtaTime(1);
//                    ecCangERPSyncSupplierProductRequest.setBuyerId(855); // 默认设置newnary的信息
//                    ecCangERPSyncSupplierProductRequest.setCurrencyCode("RMB");
//                    EcCangApiBaseResult<String> reSyncProductResult = eccangERPApiSveImpl.syncSupplierProduct(thirdPartySystem, ecCangERPSyncSupplierProductRequest);
//                    log.info("在易仓与采购系统同名供应商下新增sku报价绑定成功！skuId={}", sku.substring(sku.lastIndexOf("查无产品") + 4));
//                } catch (Exception e) {
//                    log.error("在易仓与采购系统同名供应商下新增sku报价绑定异常！skuId={}", sku.substring(sku.lastIndexOf("查无产品") + 4));
//                }
//            }
//            // 3. 再次创建
//            EcCangApiBaseResult<String> reCreateResult = eccangERPApiSveImpl.syncPurchaseOrders(thirdPartySystem, request);
//            if ("200".equals(reCreateResult.getCode())) {
//                orderCreateLogInfo.setRespData(reCreateResult.getData());
//                orderCreateLogInfo.setSysRemark("查无产品，通过绑定供应商商品，在易仓再次创建采购单成功");
//                //TODO 后续需校验是否绑定采购单三方单号成功
//                purchaseRpc.updateThirdOrderCode(purchaseOrderInfo.getId(), JSON.parseObject(result.getData()).getString("poCode"));
//                return JSON.parseObject(reCreateResult.getData()).getString("receivingCode");
//            }
//        }
        return null;
    }

    private EcCangERPSyncPurchaseOrdersRequest buildEcCangERPSyncPurchaseOrdersRequest(ThirdPartySystem thirdPartySystem, ApiRequestParams apiRequestParams, PurchaseOrderInfo purchaseOrderInfo) {
        String purchaserId = getUserId(purchaseOrderInfo.getPurchaseUid());
        if (StringUtils.isBlank(purchaserId)) {
            throw new ServiceException(CommonErrorInfo.ERROR_101_QUERY_DATA_ERROR, "找不到对应采购用户映射信息-" + purchaserId);
        }
        String supplierId = getSupplierId(thirdPartySystem, purchaseOrderInfo);
        if (StringUtils.isBlank(supplierId)) {
            throw new ServiceException(CommonErrorInfo.ERROR_101_QUERY_DATA_ERROR, "找不到对应采购供应商映射信息-" + purchaseOrderInfo.getSupplierId());
        }
        String warehouseId = getWarehouseId(purchaseOrderInfo.getPurchaseWarehouse());
        if (StringUtils.isBlank(warehouseId)) {
            throw new ServiceException(CommonErrorInfo.ERROR_101_QUERY_DATA_ERROR, "找不到对应采购仓库信息-" + purchaseOrderInfo.getPurchaseWarehouse());
        }
        fillSupplierProduct(thirdPartySystem, purchaseOrderInfo, supplierId, purchaserId);

        EcCangERPSyncPurchaseOrdersRequest request = JSON.parseObject(apiRequestParams.getParams(), EcCangERPSyncPurchaseOrdersRequest.class);
        request.warehouse_id = Integer.valueOf(warehouseId);
        request.ref_no = String.valueOf(purchaseOrderInfo.getOrderCode());
        request.suppiler_id = Integer.valueOf(supplierId);
        request.operator_purchase = Integer.valueOf(purchaserId);
        request.pts_oprater = Integer.valueOf(purchaserId);
        request.account_type = EcCangERPMapping.AccountTypeMapping.get(purchaseOrderInfo.getSettleType());
        request.currency_code = "RMB";
        request.action_type = "ADD";
        if (null != purchaseOrderInfo.getPlatformShippingFee()) {
            request.pay_ship_amount = purchaseOrderInfo.getPlatformShippingFee().doubleValue();
        }
        // 设置网采单号
        if (StringUtils.isNotBlank(purchaseOrderInfo.getPlatformOrderCode())) {
            List<String> numberList = new ArrayList<>();
            numberList.add(purchaseOrderInfo.getPlatformOrderCode());
            request.single_net_number = numberList;
        }
        // 设置物流跟踪号
        if (StringUtils.isNotBlank(purchaseOrderInfo.getTrackingNumber())) {
            request.tracking_no = purchaseOrderInfo.getTrackingNumber();
        }
        //直接已确认
        request.po_status = 3;
        //支付方式
        if (StringUtils.isNotBlank(purchaseOrderInfo.getAccount())) {
            request.pay_type = 2;
            request.supplier_pay_type = 2;
            if (StringUtils.isNotBlank(purchaseOrderInfo.getBankName())) {
                request.pay_type = 1;
                request.supplier_pay_type = 1;
            }
        } else {
            request.pay_type = 1;
            request.supplier_pay_type = 1;
        }
        List<EcCangERPSyncPurchaseOrdersRequest.ProductList> productListList = new ArrayList<>();
        purchaseOrderInfo.getPurchaseOrderGoodsList().forEach(goods -> {
            EcCangERPSyncPurchaseOrdersRequest.ProductList product = new EcCangERPSyncPurchaseOrdersRequest.ProductList();
            product.product_sku = goods.getSku();
            product.qty_expected = Math.toIntExact(goods.getPurchaseQuantity());
            product.currency_code = "RMB";
            if (Objects.nonNull(goods.getNeedQc())) {
                product.is_qc = goods.getNeedQc() ? 1 : 0;
            }
            product.unit_price = goods.getCurrentPurchasePrice().doubleValue();
            productListList.add(product);
        });
        request.productList = productListList;
        return request;
    }

    private String getWarehouseId(String purchaseWarehouse) {
        ThirdPartyMappingInfo warehouseMapping = thirdPartyMappingManager.getNewestInfoBySource("ECCANG", "PMS", purchaseWarehouse, ThirdPartyMappingType.WAREHOUSE);
        if (null == warehouseMapping) {
            return null;
        }
        return warehouseMapping.getTargetId();
    }

    private String getUserId(String purchaserId) {
        ThirdPartyMappingInfo userMapping = thirdPartyMappingManager.getNewestInfoBySource("ECCANG", "GSP", purchaserId, ThirdPartyMappingType.USER_ID);
        if (null == userMapping) {
            return null;
        }
        return userMapping.getTargetId();
    }

    private void fillSupplierProduct(ThirdPartySystem thirdPartySystem, PurchaseOrderInfo purchaseOrderInfo, String thirdPartySupplierId, String thirdPartyOrderPurchaserId) {
        List<SkuDetailInfo> skuList = purchaseRpc.getBySkuList(purchaseOrderInfo.getPurchaseOrderGoodsList().stream().map(PurchaseOrderGoodsInfo::getSku).collect(Collectors.toList()));
        Map<String, SkuDetailInfo> skuMap = skuList.stream().collect(Collectors.toMap(s -> s.getSkuInfo().getSku(), s -> s, (key1, key2) -> key1));
        if (CollectionUtils.isEmpty(skuList) || skuMap.isEmpty()) {
            throw new ServiceException(CommonErrorInfo.ERROR_101_QUERY_DATA_ERROR, "找不到对应采购商品信息-" + purchaseOrderInfo.getPurchaseOrderGoodsList().stream().map(PurchaseOrderGoodsInfo::getSku).collect(Collectors.toList()));
        }
        EcCangERPGetSupplierProductRequest getSupplierProductRequest = new EcCangERPGetSupplierProductRequest();
        getSupplierProductRequest.setSupplierId(Integer.valueOf(thirdPartySupplierId));
        getSupplierProductRequest.setPageSize(skuMap.size());
        getSupplierProductRequest.setSku(StringUtils.join(skuMap.keySet(), " "));
        EcCangApiBaseResult<String> getSupplierProductResp = eccangERPApiSveImpl.getSupplierProductList(thirdPartySystem, getSupplierProductRequest);
        Map<String, EcCangERPGetSupplierProductResponse> supplierProductMap = new ConcurrentHashMap<>();
        if (StringUtils.equals(getSupplierProductResp.getCode(), "200")) {
            List<EcCangERPGetSupplierProductResponse> getSupplierProductListResponse = JSON.parseArray(getSupplierProductResp.getData(), EcCangERPGetSupplierProductResponse.class);
            for (EcCangERPGetSupplierProductResponse getSupplierProductResponse : getSupplierProductListResponse) {
                supplierProductMap.put(getSupplierProductResponse.getProduct_sku(), getSupplierProductResponse);
            }
        } else if (!StringUtils.equals(getSupplierProductResp.getCode(), "10003")) {
            throw new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "获取易仓供应商商品列表失败-" + getSupplierProductResp.getMessage());
        }
        List<SupplierGoodsInfo> supplierProductList = purchaseRpc.getBySupplierId(purchaseOrderInfo.getSupplierId());
        Map<String, SupplierGoodsInfo> supplierProductInfoMap = supplierProductList.stream().collect(Collectors.toMap(s -> s.getGoodsSku(), s -> s, (key1, key2) -> key1));
        for (PurchaseOrderGoodsInfo skuInfo : purchaseOrderInfo.getPurchaseOrderGoodsList()) {
            SupplierGoodsInfo supplierGoodsInfo = supplierProductInfoMap.get(skuInfo.getSku());
            if (Objects.isNull(supplierGoodsInfo)) {
                throw new ServiceException(CommonErrorInfo.ERROR_101_QUERY_DATA_ERROR, "找不到对应供应商商品信息-" + skuInfo.getSku());
            }
            //先获取易仓的供应商商品是否存在映射
            EcCangERPGetSupplierProductResponse getSupplierProductResponse = supplierProductMap.get(skuInfo.getSku());
            //没有就创建，这里不考虑更新逻辑
            if (Objects.isNull(getSupplierProductResponse)) {
                try {
                    SkuDetailInfo skuDetailInfo = skuMap.get(skuInfo.getSku());
                    if (Objects.isNull(skuDetailInfo)) {
                        throw new ServiceException(CommonErrorInfo.ERROR_101_QUERY_DATA_ERROR, "找不到对应商品信息-" + skuInfo.getSku());
                    }
                    String skuPurchaserId = getUserId(skuDetailInfo.getSkuInfo().getPurchaser());

                    EcCangERPSyncSupplierProductRequest ecCangERPSyncSupplierProductRequest = new EcCangERPSyncSupplierProductRequest();
                    ecCangERPSyncSupplierProductRequest.setActionType("ADD");
                    ecCangERPSyncSupplierProductRequest.setSupplierId(Integer.valueOf(thirdPartySupplierId));
                    ecCangERPSyncSupplierProductRequest.setSpSupplierProductCode(skuInfo.getSku());
                    ecCangERPSyncSupplierProductRequest.setSpUnitPrice(supplierGoodsInfo.getPurchasePrice().floatValue());
                    ecCangERPSyncSupplierProductRequest.setSpDefault(supplierGoodsInfo.getIsDefault());
                    ecCangERPSyncSupplierProductRequest.setSpMinQty(NumberUtils.max(ObjectUtils.defaultIfNull(supplierGoodsInfo.getMinPurchaseQuantity(), 0), 1)); // MOQ必填
                    ecCangERPSyncSupplierProductRequest.setBuyerId(StringUtils.isNotBlank(skuPurchaserId) ? Integer.valueOf(skuPurchaserId) : Integer.valueOf(thirdPartyOrderPurchaserId));
                    String currencyCode = supplierGoodsInfo.getCurrency();
                    if (StringUtils.isBlank(currencyCode) || StringUtils.equals(currencyCode, "CNY")) {
                        currencyCode = "RMB";
                    }
                    ecCangERPSyncSupplierProductRequest.setCurrencyCode(currencyCode);
                    if (StringUtils.isNotBlank(supplierGoodsInfo.getAliLink())) {
                        ecCangERPSyncSupplierProductRequest.setSpProductAddress(Arrays.asList(supplierGoodsInfo.getAliLink()));
                    }
                    EcCangApiBaseResult<String> result = eccangERPApiSveImpl.syncSupplierProduct(thirdPartySystem, ecCangERPSyncSupplierProductRequest);
                    if (StringUtils.equals(result.getCode(), "200")) {
                        log.info("在易仓供应商{}下新增sku报价绑定成功！skuId={}", purchaseOrderInfo.getSupplierName(), skuInfo.getSku());
                    } else {
                        log.error("在易仓供应商{}下新增sku报价绑定失败！skuId={}", purchaseOrderInfo.getSupplierName(), skuInfo.getSku());
                    }
                } catch (Exception e) {
                    log.error("在易仓供应商{}下新增sku报价绑定异常！skuId={}", purchaseOrderInfo.getSupplierName(), skuInfo.getSku());
                }
            }
        }
    }

    private String getSupplierId(ThirdPartySystem thirdPartySystem, PurchaseOrderInfo purchaseOrderInfo) {
        //先从数据库映射中查询
        ThirdPartyMappingInfo supplierMapping = thirdPartyMappingManager.getNewestInfoBySource("ECCANG", "PMS", purchaseOrderInfo.getSupplierId(), ThirdPartyMappingType.SUPPLIER_ID);
        String supplierId = null;
        if (null != supplierMapping) {  //有则直接返回
            supplierId = supplierMapping.getTargetId();
        } else {
            // 供应商管理混乱重复数据多, 易仓不区分中英文符号, 处理前后空格, 替换英文括号
            String supplierName = purchaseOrderInfo.getSupplierName().trim();
            if (supplierName.contains("(")) {
                supplierName = supplierName.replace("(", "（");
            }
            if (supplierName.contains(")")) {
                supplierName = supplierName.replace(")", "）");
            }

            //没有的话再查一次易仓采购供应商
            EcCangERPGetSupplierListRequest getSupplierListRequest = new EcCangERPGetSupplierListRequest();
            EcCangERPGetSupplierListRequest.Condition condition = new EcCangERPGetSupplierListRequest.Condition();
            condition.setSupplierName(supplierName);
            getSupplierListRequest.setCondition(condition);
            EcCangApiBaseResult<String> getSupplierListResp = eccangERPApiSveImpl.getSupplierList(thirdPartySystem, getSupplierListRequest);
            if (StringUtils.equals(getSupplierListResp.getCode(), "200")) {
                List<EcCangERPGetSupplierListResponse> getSupplierListResponse = JSON.parseArray(getSupplierListResp.getData(), EcCangERPGetSupplierListResponse.class);
                if (CollectionUtils.isNotEmpty(getSupplierListResponse)) {
                    //易仓采购供应商查到记录
                    EcCangERPGetSupplierListResponse supplier = getSupplierListResponse.get(0);
                    supplierId = supplier.getSupplierId();

                    // 倒序遍历使得按最后修改查询时能取回第一个
                    Collections.reverse(getSupplierListResponse);
                    for (EcCangERPGetSupplierListResponse item : getSupplierListResponse) {
                        //补充数据库映射
                        thirdPartyMappingManager.insertOrUpdate("PMS", "ECCANG", purchaseOrderInfo.getSupplierId(), supplierId, ThirdPartyMappingType.SUPPLIER_ID.name(), JSON.toJSONString(item));
                    }
                } else {
                    //易仓供应商没有就创建
                    EcCangERPSyncSupplierRequest syncSupplier = new EcCangERPSyncSupplierRequest();
                    syncSupplier.setActionType("ADD");
                    EcCangERPSyncSupplierRequest.Supplier supplier = new EcCangERPSyncSupplierRequest.Supplier();
                    supplier.setSupplierCode(supplierName);
                    supplier.setSupplierName(supplierName);
                    supplier.setLevel("A");
                    supplier.setSupplierTeamworkType(0);
                    supplier.setSupplierType(2);
                    supplier.setPcId(1);
                    supplier.setAccountType(1);
                    supplier.setPayType(2);
                    supplier.setSupplierCarrier(1);
                    supplier.setShippingMethodIdHead(3);
                    supplier.setSupplierShipPayType(1);
                    supplier.setSupplierQcException(1);
                    supplier.setBuyerId(1);
                    syncSupplier.setSupplier(supplier);
                    EcCangApiBaseResult<String> syncSupplierResp = eccangERPApiSveImpl.syncSupplier(thirdPartySystem, syncSupplier);
                    if (StringUtils.equals(syncSupplierResp.getCode(), "200")) {
                        EcCangERPSyncSupplierResponse syncSupplierResponse = JSON.parseObject(syncSupplierResp.getData(), EcCangERPSyncSupplierResponse.class);
                        supplierId = String.valueOf(syncSupplierResponse.getSupplierId());
                        //创建完插入数据库
                        thirdPartyMappingManager.insertOrUpdate("PMS", "ECCANG", purchaseOrderInfo.getSupplierId(), supplierId, ThirdPartyMappingType.SUPPLIER_ID.name(), JSON.toJSONString(syncSupplierResponse));
                    } else {
                        log.error("创建易仓采购供应商{}失败！", purchaseOrderInfo.getSupplierName());
                    }
                }
            }
        }
        return supplierId;
    }

    /**
     * 进行入库收货
     *
     * @param thirdPartySystem
     * @param receivingCode     易仓入库单号
     * @param purchaseOrderInfo 采购系统订单信息
     */
    private void doPurchaseOrderReceiving(ThirdPartySystem thirdPartySystem, String receivingCode, PurchaseOrderInfo purchaseOrderInfo, String ecReceivingUserCode, ThirdPartyPushLogInfo orderCreateLogInfo) {
        // 1. 查询入库单
        EcCangERPStockinOrderGetReceivingRequest request = new EcCangERPStockinOrderGetReceivingRequest();
        request.setReceiving_code(receivingCode);
        EcCangApiBaseResult<String> result = eccangERPApiSveImpl.getReceiving(thirdPartySystem, request);
        if (!StringUtils.equals(result.getMessage(), "Success")) {
            log.error("获取易仓采购入库单失败！receivingCode={}", receivingCode);
            orderCreateLogInfo.setEventBizState(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_GET_RECEIVING_FAILED.getValue());
            orderCreateLogInfo.setSysRemark("已创建采购单，同步收货信息前，获取易仓入库单请求失败");
            thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
            return;
        }
        // 2. 比较收货差异
        List<EcCangERPStockinOrderGetReceivingResponse> receivingResponseList = JSON.parseArray(result.getData(), EcCangERPStockinOrderGetReceivingResponse.class);
        if (CollectionUtils.isEmpty(receivingResponseList)) {
            log.error("获取易仓采购入库单失败，未查询到入库单信息！receivingCode={}", receivingCode);
            orderCreateLogInfo.setEventBizState(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_GET_RECEIVING_FAILED.getValue());
            orderCreateLogInfo.setSysRemark("已创建采购单，同步收货信息前，获取易仓入库单为空");
            thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
            return;
        }
        EcCangERPStockinOrderGetReceivingResponse receivingResponse = receivingResponseList.get(0);
        Map<String, Integer> ecSku4ReceivingCountMap = new HashMap<>();
        receivingResponse.getProduct_info().stream().forEach(item -> {
            ecSku4ReceivingCountMap.put(item.product_barcode, item.rd_received_qty);
        });
        Map<String, Integer> gspSku4ReceivingCountMap = new HashMap<>();
        Map<String, Integer> gspSku4AllReceivingCountMap = new HashMap<>();
        purchaseOrderInfo.getPurchaseOrderGoodsList().stream().forEach(item -> {
            gspSku4AllReceivingCountMap.put(item.getSku(), item.getPurchaseQuantity().intValue());
            if (item.getArrivalQuantity() == 0) {
                return;
            }
            gspSku4ReceivingCountMap.put(item.getSku(), item.getArrivalQuantity().intValue());
        });
        if (MapUtils.isEmpty(gspSku4ReceivingCountMap)) {
            orderCreateLogInfo.setEventBizState(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_RECEIVING_WAIT_GSP.getValue());
            orderCreateLogInfo.setSysRemark("已创建采购单，同步收货信息前，GSP采购系统暂无收货记录");
            thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
            return;
        }
        Map<String, Integer> waitSyncSku4CountMap = new HashMap<>();
        gspSku4ReceivingCountMap.keySet().forEach(skuCode -> {
            Integer gspCount = gspSku4ReceivingCountMap.get(skuCode);
            Integer ecCount = ecSku4ReceivingCountMap.get(skuCode);
            if (gspCount > ecCount) {
                waitSyncSku4CountMap.put(skuCode, gspCount - ecCount);
            }
        });
        Gsp2EccangPOCreateEventBizState receivingEventBizState = convertPOCreateEventBizState(gspSku4AllReceivingCountMap, ecSku4ReceivingCountMap, waitSyncSku4CountMap);
        log.info("准备推送易仓采购入库收货明细！receivingCode={}, waitSyncSku4CountMap={}", receivingCode, JSON.toJSONString(waitSyncSku4CountMap));
        if (MapUtils.isEmpty(waitSyncSku4CountMap)) {
            orderCreateLogInfo.setSysRemark("已创建采购单，对比推送，".concat(receivingEventBizState.getDesc()));
            if (receivingEventBizState == Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_RECEIVING_ALL) {
                // 如果全部收货，标记成功
                orderCreateLogInfo.setEventState(ThirdPartyEventState.SUCCESS.getValue());
            }
            orderCreateLogInfo.setEventBizState(receivingEventBizState.getValue());
            thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
            return;
        }
        // 3. 根据差异，操作采购收货
        EcCangERPPurchaseOrderReceivingRequest orderReceivingRequest = new EcCangERPPurchaseOrderReceivingRequest();
        orderReceivingRequest.receivingCode = receivingCode;
        orderReceivingRequest.userCode = ecReceivingUserCode;
        List<EcCangERPPurchaseOrderReceivingRequest.Product> productList = new ArrayList<>();
        waitSyncSku4CountMap.keySet().forEach(skuCode -> {
            EcCangERPPurchaseOrderReceivingRequest.Product product = new EcCangERPPurchaseOrderReceivingRequest.Product();
            product.productSku = skuCode;
            product.receivedQty = waitSyncSku4CountMap.get(skuCode);
            productList.add(product);
        });
        orderReceivingRequest.productList = productList;
        log.info("准备同步采购收货！orderReceivingRequest={}", orderReceivingRequest);
        EcCangApiBaseResult<String> receivingResult = eccangERPApiSveImpl.purchaseOrderReceiving(thirdPartySystem, orderReceivingRequest);
        if (StringUtils.equals(receivingResult.getCode(), "200")) {
            orderCreateLogInfo.setSysRemark("已创建采购单，对比推送，".concat(receivingEventBizState.getDesc()));
            orderCreateLogInfo.setEventBizState(receivingEventBizState.getValue());
            if (receivingEventBizState == Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_RECEIVING_ALL) {
                orderCreateLogInfo.setEventState(ThirdPartyEventState.SUCCESS.getValue());
            }
        } else {
            orderCreateLogInfo.setSysRemark(receivingResult.getData());
        }
        thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
    }

    private Gsp2EccangPOCreateEventBizState convertPOCreateEventBizState(Map<String, Integer> gspSku4AllReceivingCountMap, Map<String, Integer> ecSku4ReceivingCountMap, Map<String, Integer> waitSyncSku4CountMap) {
        Gsp2EccangPOCreateEventBizState eventBizState = Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_RECEIVING_ALL;
        for (String sku : gspSku4AllReceivingCountMap.keySet()) {
            Integer allCount = gspSku4AllReceivingCountMap.get(sku);
            Integer syncedCount = (ecSku4ReceivingCountMap.get(sku) != null) ? ecSku4ReceivingCountMap.get(sku) : 0;
            Integer waitSyncCount = (waitSyncSku4CountMap.get(sku) != null) ? waitSyncSku4CountMap.get(sku) : 0;
            if (allCount != (syncedCount + waitSyncCount)) {
                eventBizState = Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_RECEIVING_PART;
            }
        }
        return eventBizState;
    }

    /**
     * 由采购系统售后触发不等待剩余，强制关闭易仓采购单
     *
     * @param command
     */
    public void successEcOrder(GspSyncPo2EccangCommand command) {
        JSONObject jsonObject = JSON.parseObject(bizIds);
        String bizId = jsonObject.getString("ECCANG");
        ThirdPartySystem thirdPartySystem = loadSystem(bizId);
        if (null == thirdPartySystem) {
            log.info("易仓采购收货消息处理，未匹配到易仓对接信息，消息无需处理！");
            return;
        }
        DConcurrentTemplate.tryLockMode(
                MGMT_PREFIX.concat(command.getPurchaseOrderId()),
                lock -> lock.tryLock(20, TimeUnit.SECONDS),
                () -> {
                    // 构造日志记录
                    ThirdPartyPushLogInfo orderCreateLogInfo = new ThirdPartyPushLogInfo();
                    try {
                        // 1. 获取新采购系统采购详情
                        PurchaseOrderInfo purchaseOrderInfo = purchaseRpc.queryOrderDetail(Long.valueOf(command.getPurchaseOrderId()));
                        orderCreateLogInfo.setEventType(ThirdPartEventType.GSP_2_ECCANG_PO_CREATE.name());
                        orderCreateLogInfo.setEventBizId(command.getPurchaseOrderId());
                        orderCreateLogInfo.setEventData(JSON.toJSONString(command));
                        orderCreateLogInfo.setEventState(ThirdPartyEventState.FAIL.getValue());
                        // 2. 获取易仓采购单详情
                        ApiRequestParams orderParams = apiOrderCreateParamsRepository.loadBySystemBizIdAndType(bizId, ApiRequestParamsType.PURCHASE_ORDER.name()).get();
                        String ecCangReceivingCode = getReceivingCodeInEcCang(orderCreateLogInfo, thirdPartySystem, orderParams, purchaseOrderInfo);
                        if (StringUtils.isBlank(ecCangReceivingCode)) {
                            //查询不到入库单号，不做任何操作
                            return;
                        }
                        log.info("准备强制完成易仓入库单！receivingCode={}", ecCangReceivingCode);
                        EcCangERPPurchaseOrderSyncConfirmReceivingRequest request = new EcCangERPPurchaseOrderSyncConfirmReceivingRequest();
                        request.setReason("采购单产生售后，部分到货且不等待剩余，强制完成此入库单");
                        request.setReceiving_code(ecCangReceivingCode);
                        EcCangApiBaseResult<String> receivingResult = eccangERPApiSveImpl.syncConfirmReceiving(thirdPartySystem, request);
                        if (StringUtils.equals(receivingResult.getCode(), "200")) {
                            orderCreateLogInfo.setSysRemark("已创建采购单，对比推送，".concat(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_RECEIVING_NO_WAIT_PART.getDesc()));
                            orderCreateLogInfo.setEventState(ThirdPartyEventState.SUCCESS.getValue());
                        } else {
                            //强制完成失败，记录异常
                            orderCreateLogInfo.setSysRemark("强制完成采购入库单操作异常：".concat(receivingResult.getMessage()));
                        }
                        orderCreateLogInfo.setEventBizState(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_RECEIVING_NO_WAIT_PART.getValue());
                        thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                    } catch (Exception e) {
                        log.error("易仓强制完成采购入库单失败：id: {}", command.getPurchaseOrderId(), e);
                        orderCreateLogInfo.setSysRemark("未知异常");
                        thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                    }
                }
        );
    }

    public void syncCreateEccangPurchaseOrder(GspSyncPo2EccangCommand command) {
        JSONObject jsonObject = JSON.parseObject(bizIds);
        String bizId = jsonObject.getString("ECCANG");
        ThirdPartySystem thirdPartySystem = loadSystem(bizId);
        if (null == thirdPartySystem) {
            log.info("易仓采购单创建消息处理，未匹配到易仓对接信息，消息无需处理！");
            return;
        }
        DConcurrentTemplate.tryLockMode(
                MGMT_PREFIX.concat(command.getPurchaseOrderId()),
                lock -> lock.tryLock(60, TimeUnit.SECONDS),
                () -> {
                    //查询是否有过创建,存在则不创建
                    ThirdPartyPushLogInfo thirdPartyPushLogInfo = thirdPartyPushLogManager.queryByEventBizId(ThirdPartEventType.GSP_2_ECCANG_PO_CREATE.name(), command.getPurchaseOrderId());
                    if (Objects.nonNull(thirdPartyPushLogInfo) && thirdPartyPushLogInfo.getEventBizState() >= Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_CREATED.getValue()) {
                        log.info("[{}]易仓采购单已经创建,无需重复创建！", thirdPartyPushLogInfo.getEventBizId());
                        return;
                    }
                    // 构造日志记录
                    ThirdPartyPushLogInfo orderCreateLogInfo = new ThirdPartyPushLogInfo();
                    orderCreateLogInfo.setEventType(ThirdPartEventType.GSP_2_ECCANG_PO_CREATE.name());
                    orderCreateLogInfo.setEventBizId(command.getPurchaseOrderId());
                    orderCreateLogInfo.setEventData(JSON.toJSONString(command));
                    orderCreateLogInfo.setEventState(ThirdPartyEventState.FAIL.getValue());
                    try {
                        // 0. 同步初始化
                        orderCreateLogInfo.setEventBizState(Gsp2EccangPOCreateEventBizState.SYNC_PO_INIT.getValue());
                        orderCreateLogInfo.setSysRemark("准备处理消息");
                        thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                        // 1. 获取新采购系统采购详情
                        PurchaseOrderInfo purchaseOrderInfo = purchaseRpc.queryOrderDetail(Long.valueOf(command.getPurchaseOrderId()));
                        // 2. 获取易仓采购单详情
                        ApiRequestParams orderParams = apiOrderCreateParamsRepository.loadBySystemBizIdAndType(bizId, ApiRequestParamsType.PURCHASE_ORDER.name()).get();
                        String ecCangReceivingCode = getReceivingCodeInEcCang(orderCreateLogInfo, thirdPartySystem, orderParams, purchaseOrderInfo);
                        if (StringUtils.isBlank(ecCangReceivingCode)) {
//                            log.error("采购单获取或创建失败，拿不到入库单号，消息不消费，直接丢弃，后续通过补偿操作！id: {}", command.getPurchaseOrderId());
                            orderCreateLogInfo.setSysRemark("采购单获取或创建失败，拿不到入库单号，消息不消费，直接丢弃，后续通过补偿操作");
                            orderCreateLogInfo.setEventBizState(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_FAILED.getValue());
                            thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                            return;
                        } else {
                            orderCreateLogInfo.setEventBizState(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_CREATED.getValue());
                            thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                        }
                    } catch (Exception e) {
                        if (e instanceof ServiceException) {
                            if (CommonErrorInfo.ERROR_111_RATE_LIMIT_ERROR.getErrorCode().equals(((ServiceException) e).getErrorInfo().getErrorCode())) {
                                log.warn("易仓同步采购订单失败：id: {}", command.getPurchaseOrderId(), e);
                            }
                        } else {
                            log.error("易仓同步采购订单失败：id: {}", command.getPurchaseOrderId(), e);
                        }
                        orderCreateLogInfo.setSysRemark("未知异常: " + e.getMessage());
                        orderCreateLogInfo.setEventState(ThirdPartyEventState.FAIL.getValue());
                        thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                    }
                }
        );
    }

    public void doSync2GspOrder(GspSyncPo2EccangCommand command, ThirdPartyPushLogInfo pushLogInfo) {
        JSONObject jsonObject = JSON.parseObject(bizIds);
        String bizId = jsonObject.getString("ECCANG");
        ThirdPartySystem thirdPartySystem = loadSystem(bizId);
        if (null == thirdPartySystem) {
            log.info("易仓采购单收货消息处理，未匹配到易仓对接信息，消息无需处理！");
            return;
        }
        DConcurrentTemplate.tryLockMode(
                MGMT_PREFIX.concat(command.getPurchaseOrderId()),
                lock -> lock.tryLock(20, TimeUnit.SECONDS),
                () -> {
                    // 构造日志记录
                    ThirdPartyPushLogInfo orderCreateLogInfo = new ThirdPartyPushLogInfo();
                    orderCreateLogInfo.setEventType(ThirdPartEventType.GSP_2_ECCANG_PO_CREATE.name());
                    orderCreateLogInfo.setEventBizId(command.getPurchaseOrderId());
                    orderCreateLogInfo.setEventData(JSON.toJSONString(command));
                    orderCreateLogInfo.setRespData(pushLogInfo.getRespData());
                    orderCreateLogInfo.setEventBizState(pushLogInfo.getEventBizState());
                    orderCreateLogInfo.setEventState(ThirdPartyEventState.FAIL.getValue());
                    try {
                        // 1. 获取新采购系统采购详情
                        PurchaseOrderInfo purchaseOrderInfo = purchaseRpc.queryOrderDetail(Long.valueOf(command.getPurchaseOrderId()));
                        log.info("易仓采购单收货消息处理，采购单号为[{}]处理开始", purchaseOrderInfo.getOrderCode());
                        if (purchaseOrderInfo.getOrderStatus().equals(55)) {
                            // 如果产生售后，不等待剩余，则优先标记成功
                            orderCreateLogInfo.setSysRemark("已创建采购单，对比推送，".concat(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_RECEIVING_NO_WAIT_PART.getDesc()));
                            orderCreateLogInfo.setEventBizState(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_RECEIVING_NO_WAIT_PART.getValue());
                            orderCreateLogInfo.setEventState(ThirdPartyEventState.SUCCESS.getValue());
                            thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                            return;
                        }
                        if (purchaseOrderInfo.getOrderStatus().equals(50)) {
                            // 如果存在某种原因在采购系统手动操作完成，任务也执行完毕
                            orderCreateLogInfo.setSysRemark("已创建采购单，对比推送，".concat(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_RECEIVING_ALL.getDesc()));
                            orderCreateLogInfo.setEventBizState(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_RECEIVING_ALL.getValue());
                            orderCreateLogInfo.setEventState(ThirdPartyEventState.SUCCESS.getValue());
                            thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                            return;
                        }
                        log.info("易仓采购单收货消息处理，采购单号为[{}]状态非全部到货或不等待剩余", purchaseOrderInfo.getOrderCode());
                        Map<String, PurchaseOrderGoodsInfo> orderGoodsInfoMap = purchaseOrderInfo.getPurchaseOrderGoodsList().stream().collect(Collectors.toMap(PurchaseOrderGoodsInfo::getSku, item -> item));
                        // 2. 获取易仓采购单详情
                        EcCangApiBaseResult<String> purchaseOrdersResult = eccangERPApiSveImpl.getPurchaseOrdersNoDecode(purchaseOrderInfo.getOrderCode(), thirdPartySystem, null, null);
                        if (purchaseOrdersResult.getCode().equals("200")) {
                            log.info("易仓采购单收货消息处理，采购单号为[{}]获取易仓采购单成功", purchaseOrderInfo.getOrderCode());
                            log.info("易仓采购单收货消息处理，采购单号为[{}]获取易仓采购单数据为{}", purchaseOrderInfo.getOrderCode(), purchaseOrdersResult.getData());
                            //更新关联信息
                            List<EcCangERPGetPurchaseOrdersResponse> responsePurchaseOrders = JSONObject.parseArray(purchaseOrdersResult.getData(), EcCangERPGetPurchaseOrdersResponse.class);
                            //过滤掉强制完成的订单
                            if (responsePurchaseOrders.size() > 1) {
                                responsePurchaseOrders = responsePurchaseOrders.stream().filter(item -> StringUtils.equals("0", item.getPo_complete_type())).collect(Collectors.toList());
                            }
                            log.info("易仓采购单收货消息处理，采购单号为[{}]获取易仓采购单数量为{}", purchaseOrderInfo.getOrderCode(), responsePurchaseOrders.size());
                            if (CollectionUtils.isNotEmpty(responsePurchaseOrders)) {
                                List<EcCangERPGetPurchaseOrdersResponse.Detail> orderDetail = responsePurchaseOrders.get(0).getDetail();
                                log.info("易仓采购单收货消息处理，采购单号为[{}]获取易仓采购单号{}", purchaseOrderInfo.getOrderCode(), responsePurchaseOrders.get(0).getPo_code());
                                Long totalArrivalQuantity = 0L;
                                for (EcCangERPGetPurchaseOrdersResponse.Detail detail : orderDetail) {
                                    PurchaseOrderGoodsInfo info = orderGoodsInfoMap.get(detail.getProduct_sku());
                                    //采购单此sku已收到货的数量
                                    Long arrivalQuantity = Objects.nonNull(info.getArrivalQuantity()) ? info.getArrivalQuantity() : 0L;
                                    //采购单总收货数量
                                    totalArrivalQuantity += arrivalQuantity;
                                    //易仓的收货数量
                                    Long ecReceving = Long.valueOf(detail.getQty_receving());
                                    //gsp采购单需要收货数量
                                    long receivedQuantity = ecReceving - arrivalQuantity;
                                    log.info("易仓采购单收货消息处理，采购单号为[{}]获取易仓采购单商品{}，易仓收货数量为{}, 采购系统收货数量为{}", purchaseOrderInfo.getOrderCode(), detail.getProduct_sku(), ecReceving, arrivalQuantity);
                                    if (receivedQuantity > 0) {
                                        //2.1签收
                                        if (StringUtils.isNotBlank(purchaseOrderInfo.getTrackingNumber()) || CollectionUtils.isNotEmpty(purchaseOrderInfo.getTrackingNumbers())) {
                                            log.info("易仓采购单收货消息处理，采购单号为[{}]签收", purchaseOrderInfo.getOrderCode());
                                            if (StringUtils.isNotBlank(purchaseOrderInfo.getTrackingNumber())) {
                                                EntryScanningCommand scanningCommand = new EntryScanningCommand();
                                                scanningCommand.setScanArrivalType("trackingNumber");
                                                scanningCommand.setCode(purchaseOrderInfo.getTrackingNumber());
                                                purchaseRpc.scanSignList4ThirdParty(scanningCommand);
                                            } else if (CollectionUtils.isNotEmpty(purchaseOrderInfo.getTrackingNumbers())) {
                                                purchaseOrderInfo.getTrackingNumbers().forEach(trackingNumber -> {
                                                    EntryScanningCommand scanningCommand = new EntryScanningCommand();
                                                    scanningCommand.setScanArrivalType("trackingNumber");
                                                    scanningCommand.setCode(trackingNumber);
                                                    purchaseRpc.scanSignList4ThirdParty(scanningCommand);
                                                });
                                            }
                                            log.info("易仓采购单收货消息处理，采购单号为[{}]更新入库商品尺寸", purchaseOrderInfo.getOrderCode());
                                            //2.2 查看易仓sku信息，填写寸尺
                                            EcCangERPGetProductBySkuRequest request = new EcCangERPGetProductBySkuRequest();
                                            request.setProductSku(Collections.singletonList(info.getSku()));
                                            EcCangApiBaseResult<String> result = eccangERPApiSveImpl.getProductBySku(thirdPartySystem, request);
                                            List<EcCangERPGetProductBySkuResponse> ecCangERPGetProductBySkuResponses = JSON.parseArray(result.getData(), EcCangERPGetProductBySkuResponse.class);
                                            if (CollectionUtils.isNotEmpty(ecCangERPGetProductBySkuResponses)) {
                                                EcCangERPGetProductBySkuResponse ecCangERPGetProductBySkuResponse = ecCangERPGetProductBySkuResponses.get(0);
                                                UpdateGoodsSizeCommand updateCommand = new UpdateGoodsSizeCommand();
                                                updateCommand.setSku(ecCangERPGetProductBySkuResponse.getProductSku());
                                                updateCommand.setSizeLength(ecCangERPGetProductBySkuResponse.getProductLength());
                                                updateCommand.setSizeWidth(ecCangERPGetProductBySkuResponse.getProductWidth());
                                                updateCommand.setSizeHeight(ecCangERPGetProductBySkuResponse.getProductHeight());
                                                updateCommand.setNetWeight(ecCangERPGetProductBySkuResponse.getProductWeight().multiply(new BigDecimal(1000)));
                                                purchaseRpc.updateGoodsSize(updateCommand);
                                            }
                                            log.info("易仓采购单收货消息处理，采购单号为[{}]收货入库", purchaseOrderInfo.getOrderCode());
                                            //2.3 收货
                                            BatchTakeStockCommand takeStockCommand = new BatchTakeStockCommand();
                                            List<PurchaseOrderGoodsInfo> goodsInfos = new ArrayList<>();
                                            info.setReceivingQuantity(receivedQuantity);
                                            goodsInfos.add(info);
                                            takeStockCommand.setOrderGoodsList(goodsInfos);
                                            purchaseRpc.batchTakeStock4ThirdParty(takeStockCommand);
                                            //2.4 查看采购单信息,同步日志状态
                                            PurchaseOrderInfo purchaseOrderInfoAfter = purchaseRpc.queryOrderDetail(Long.valueOf(command.getPurchaseOrderId()));
                                            Integer orderStatus = purchaseOrderInfoAfter.getOrderStatus();
                                            if (orderStatus.equals(45)) {
                                                orderCreateLogInfo.setSysRemark("已创建采购单，对比推送，".concat(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_RECEIVING_PART.getDesc()));
                                                orderCreateLogInfo.setEventBizState(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_RECEIVING_PART.getValue());
                                            }
                                            if (orderStatus.equals(50)) {
                                                orderCreateLogInfo.setSysRemark("已创建采购单，对比推送，".concat(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_RECEIVING_ALL.getDesc()));
                                                orderCreateLogInfo.setEventBizState(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_RECEIVING_ALL.getValue());
                                                // 如果全部收货，标记成功
                                                orderCreateLogInfo.setEventState(ThirdPartyEventState.SUCCESS.getValue());
                                            }
                                            thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                                        }
                                    } else {
                                        log.info("易仓采购单收货消息处理，采购单号为[{}]签收数量小于1", purchaseOrderInfo.getOrderCode());
                                        if (totalArrivalQuantity == 0) {
                                            // 易仓未收到货
                                            orderCreateLogInfo.setSysRemark("已创建采购单，对比推送，".concat(Gsp2EccangPOCreateEventBizState.SYNC_GSP_PO_RECEIVING_WAIT_EC.getDesc()));
                                            orderCreateLogInfo.setEventBizState(Gsp2EccangPOCreateEventBizState.SYNC_GSP_PO_RECEIVING_WAIT_EC.getValue());
                                            thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                                        }
                                    }
                                }
                            }
                        } else {
                            log.warn("易仓同步收货信息到采购订单失败：id: {}", command.getPurchaseOrderId());
                            orderCreateLogInfo.setSysRemark("未知异常".concat(":").concat(purchaseOrdersResult.getMessage()));
                            orderCreateLogInfo.setEventState(ThirdPartyEventState.FAIL.getValue());
                            thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                        }
                    } catch (Exception e) {
                        log.warn("易仓同步收货信息到采购订单失败：id: {}", command.getPurchaseOrderId(), e);
                        orderCreateLogInfo.setSysRemark("未知异常".concat(":" + e.getMessage()));
                        orderCreateLogInfo.setEventState(ThirdPartyEventState.FAIL.getValue());
                        thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                    }
                }
        );
    }

    public void syncTrackingNumber2EcangPurchaseOrder(GspSyncPo2EccangCommand command) {
        JSONObject jsonObject = JSON.parseObject(bizIds);
        String bizId = jsonObject.getString("ECCANG");
        ThirdPartySystem thirdPartySystem = loadSystem(bizId);
        if (null == thirdPartySystem) {
            log.info("易仓采购同步快递单号消息处理，未匹配到易仓对接信息，消息无需处理！");
            return;
        }
        DConcurrentTemplate.tryLockMode(
                MGMT_PREFIX.concat(command.getPurchaseOrderId()),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    // 构造日志记录
                    ThirdPartyPushLogInfo orderCreateLogInfo = new ThirdPartyPushLogInfo();
                    orderCreateLogInfo.setEventType(ThirdPartEventType.GSP_2_ECCANG_SYNC_TRACKING_NUMBER.name());
                    orderCreateLogInfo.setEventBizId(command.getPurchaseOrderId());
                    orderCreateLogInfo.setEventData(JSON.toJSONString(command));
                    orderCreateLogInfo.setEventState(ThirdPartyEventState.FAIL.getValue());
                    try {
                        // 0. 同步初始化
                        orderCreateLogInfo.setEventBizState(Gsp2EccangPOSyncTrackingNumberEventBizState.SYNC_GSP_TRACKING_NUMBER_IN_PROGRESS.getValue());
                        orderCreateLogInfo.setSysRemark("准备处理信息");
                        thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                        // 1. 获取新采购系统采购详情
                        PurchaseOrderInfo purchaseOrderInfo = purchaseRpc.queryOrderDetail(Long.valueOf(command.getPurchaseOrderId()));
                        // 2. 获取易仓采购单详情
                        ThirdPartyPushLogInfo thirdPartyPushLogInfo = thirdPartyPushLogManager.queryByEventBizId(ThirdPartEventType.GSP_2_ECCANG_PO_CREATE.name(), command.getPurchaseOrderId());
                        if (Objects.nonNull(thirdPartyPushLogInfo) && Objects.nonNull(JSON.parseObject(thirdPartyPushLogInfo.getRespData()))) {
                            String poCode = JSON.parseObject(thirdPartyPushLogInfo.getRespData()).getString("poCode");
                            orderCreateLogInfo.setRespData(poCode);
                            if (StringUtils.isNotBlank(purchaseOrderInfo.getTrackingNumber()) || CollectionUtils.isNotEmpty(purchaseOrderInfo.getTrackingNumbers())) {
                                //3. 同步快递单号到易仓
                                EcCangERPPurchaseOrderSyncPurchaseTrackingNoteRequest request = new EcCangERPPurchaseOrderSyncPurchaseTrackingNoteRequest();
                                request.setPoCode(poCode);
                                request.setSupplierMethodId("2");
                                request.setPurchaseShipperId(27);
                                request.setOperationType(1);
                                request.setTrackNote(purchaseOrderInfo.getMemo());
                                if (CollectionUtils.isNotEmpty(purchaseOrderInfo.getTrackingNumbers())) {
                                    request.setTrackingNoteRecord(purchaseOrderInfo.getTrackingNumbers().stream().map(EcCangERPPurchaseOrderSyncPurchaseTrackingNoteRequest.TrackingNoteRecord::new).collect(Collectors.toList()));
                                } else if (StringUtils.isNotBlank(purchaseOrderInfo.getTrackingNumber())) {
                                    request.setTrackingNoteRecord(Collections.singletonList(new EcCangERPPurchaseOrderSyncPurchaseTrackingNoteRequest.TrackingNoteRecord(purchaseOrderInfo.getTrackingNumber())));
                                }
                                EcCangApiBaseResult<String> stringEcCangApiBaseResult = eccangERPApiSveImpl.syncPurchaseTrackingNote(thirdPartySystem, request);
                                if (stringEcCangApiBaseResult.getCode().equals("200")) {
                                    orderCreateLogInfo.setEventState(ThirdPartyEventState.SUCCESS.getValue());
                                    orderCreateLogInfo.setSysRemark("同步物流运单号，对比推送，".concat(Gsp2EccangPOSyncTrackingNumberEventBizState.SYNC_GSP_TRACKING_NUMBER_SUCCESS.getDesc()));
                                    orderCreateLogInfo.setEventBizState(Gsp2EccangPOSyncTrackingNumberEventBizState.SYNC_GSP_TRACKING_NUMBER_SUCCESS.getValue());
                                    thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                                } else {
                                    orderCreateLogInfo.setSysRemark(stringEcCangApiBaseResult.getMessage());
                                    orderCreateLogInfo.setEventBizState(Gsp2EccangPOSyncTrackingNumberEventBizState.SYNC_GSP_TRACKING_NUMBER_FAIL.getValue());
                                    thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                                }
                            }
                        } else {
                            EcCangApiBaseResult<String> purchaseOrdersResult = eccangERPApiSveImpl.getPurchaseOrders(purchaseOrderInfo.getOrderCode(), thirdPartySystem);
                            if (purchaseOrdersResult.getCode().equals("200")) {
                                //更新关联信息
                                List<EcCangERPGetPurchaseOrdersResponse> responsePurchaseOrders = JSONObject.parseArray(purchaseOrdersResult.getData(), EcCangERPGetPurchaseOrdersResponse.class);
                                if (CollectionUtils.isNotEmpty(responsePurchaseOrders)) {
                                    EcCangERPGetPurchaseOrdersResponse ecCangERPGetPurchaseOrdersResponse = responsePurchaseOrders.get(0);
                                    orderCreateLogInfo.setRespData(ecCangERPGetPurchaseOrdersResponse.getPo_code());
                                    if (CollectionUtils.isNotEmpty(purchaseOrderInfo.getTrackingNumbers())
                                            && !purchaseOrderInfo.getTrackingNumbers().contains(ecCangERPGetPurchaseOrdersResponse.getTracking_no())) {
                                        EcCangERPPurchaseOrderSyncPurchaseTrackingNoteRequest request = new EcCangERPPurchaseOrderSyncPurchaseTrackingNoteRequest();
                                        request.setPoCode(ecCangERPGetPurchaseOrdersResponse.getPo_code());
                                        request.setSupplierMethodId("2");
                                        request.setPurchaseShipperId(27);
                                        request.setTrackNote(purchaseOrderInfo.getMemo());
                                        request.setOperationType(1);
                                        request.setTrackingNoteRecord(purchaseOrderInfo.getTrackingNumbers().stream().map(EcCangERPPurchaseOrderSyncPurchaseTrackingNoteRequest.TrackingNoteRecord::new).collect(Collectors.toList()));
                                        EcCangApiBaseResult<String> stringEcCangApiBaseResult = eccangERPApiSveImpl.syncPurchaseTrackingNote(thirdPartySystem, request);
                                        if (stringEcCangApiBaseResult.getCode().equals("200")) {
                                            orderCreateLogInfo.setEventState(ThirdPartyEventState.SUCCESS.getValue());
                                            orderCreateLogInfo.setSysRemark("同步物流运单号，对比推送，".concat(Gsp2EccangPOSyncTrackingNumberEventBizState.SYNC_GSP_TRACKING_NUMBER_SUCCESS.getDesc()));
                                            orderCreateLogInfo.setEventBizState(Gsp2EccangPOSyncTrackingNumberEventBizState.SYNC_GSP_TRACKING_NUMBER_FAIL.getValue());
                                            thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                                        } else {
                                            orderCreateLogInfo.setSysRemark(stringEcCangApiBaseResult.getMessage());
                                            orderCreateLogInfo.setEventBizState(Gsp2EccangPOSyncTrackingNumberEventBizState.SYNC_GSP_TRACKING_NUMBER_FAIL.getValue());
                                            thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                                        }
                                    } else if (StringUtils.isNotBlank(purchaseOrderInfo.getTrackingNumber())
                                            && !StringUtils.equals(purchaseOrderInfo.getTrackingNumber(), ecCangERPGetPurchaseOrdersResponse.getTracking_no())) {
                                        //3. 同步快递单号到易仓
                                        EcCangERPPurchaseOrderSyncPurchaseTrackingNoteRequest request = new EcCangERPPurchaseOrderSyncPurchaseTrackingNoteRequest();
                                        request.setPoCode(ecCangERPGetPurchaseOrdersResponse.getPo_code());
                                        request.setSupplierMethodId("2");
                                        request.setPurchaseShipperId(27);
                                        request.setTrackNote(purchaseOrderInfo.getMemo());
                                        request.setOperationType(1);
                                        request.setTrackingNoteRecord(Collections.singletonList(new EcCangERPPurchaseOrderSyncPurchaseTrackingNoteRequest.TrackingNoteRecord(purchaseOrderInfo.getTrackingNumber())));
                                        EcCangApiBaseResult<String> stringEcCangApiBaseResult = eccangERPApiSveImpl.syncPurchaseTrackingNote(thirdPartySystem, request);
                                        if (stringEcCangApiBaseResult.getCode().equals("200")) {
                                            orderCreateLogInfo.setEventState(ThirdPartyEventState.SUCCESS.getValue());
                                            orderCreateLogInfo.setSysRemark("同步物流运单号，对比推送，".concat(Gsp2EccangPOSyncTrackingNumberEventBizState.SYNC_GSP_TRACKING_NUMBER_SUCCESS.getDesc()));
                                            orderCreateLogInfo.setEventBizState(Gsp2EccangPOSyncTrackingNumberEventBizState.SYNC_GSP_TRACKING_NUMBER_FAIL.getValue());
                                            thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                                        } else {
                                            orderCreateLogInfo.setSysRemark(stringEcCangApiBaseResult.getMessage());
                                            orderCreateLogInfo.setEventBizState(Gsp2EccangPOSyncTrackingNumberEventBizState.SYNC_GSP_TRACKING_NUMBER_FAIL.getValue());
                                            thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                                        }
                                    }
                                }
                            } else {
//                            log.error("易仓采购单详情获取失败，原因:" + purchaseOrdersResult.getMessage() + "，消息不消费，直接丢弃，后续通过补偿操作！id: {}", command.getPurchaseOrderId());
                                orderCreateLogInfo.setSysRemark("易仓采购单详情获取失败，原因:" + purchaseOrdersResult.getMessage() + "，消息不消费，直接丢弃，后续通过补偿操作");
                                orderCreateLogInfo.setEventBizState(Gsp2EccangPOSyncTrackingNumberEventBizState.SYNC_GSP_TRACKING_NUMBER_FAIL.getValue());
                                thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                            }
                        }
                    } catch (Exception e) {
                        log.error("易仓采购同步快递单号消息处理失败：id: {}", command.getPurchaseOrderId(), e);
                        orderCreateLogInfo.setSysRemark("执行失败，异常信息：" + e.getMessage());
                        orderCreateLogInfo.setEventBizState(Gsp2EccangPOSyncTrackingNumberEventBizState.SYNC_GSP_TRACKING_NUMBER_FAIL.getValue());
                        orderCreateLogInfo.setEventState(ThirdPartyEventState.FAIL.getValue());
                        thirdPartyPushLogManager.insertOrUpdate(orderCreateLogInfo);
                    }
                }
        );
    }

    public void completedEcOrder(String orderRefNo) {
        JSONObject jsonObject = JSON.parseObject(bizIds);
        String bizId = jsonObject.getString("ECCANG");
        ThirdPartySystem thirdPartySystem = loadSystem(bizId);
        if (null == thirdPartySystem) {
            log.info("易仓采购单收货消息处理，未匹配到易仓对接信息，消息无需处理！");
            return;
        }
        int count = 0;
        for (int i = 1; i <= 3; i++) {
            EcCangApiBaseResult<String> purchaseOrdersResult = eccangERPApiSveImpl.getPurchaseOrdersNoDecode(orderRefNo, thirdPartySystem, 8, i);
            if (purchaseOrdersResult.getCode().equals("200")) {
                //更新关联信息
                List<EcCangERPGetPurchaseOrdersResponse> ecCangERPGetPurchaseOrdersResponses = JSONObject.parseArray(purchaseOrdersResult.getData(), EcCangERPGetPurchaseOrdersResponse.class);
                if (CollectionUtils.isNotEmpty(ecCangERPGetPurchaseOrdersResponses)) {
                    for (EcCangERPGetPurchaseOrdersResponse response : ecCangERPGetPurchaseOrdersResponses) {
                        try {
                            if (StringUtils.isNotBlank(response.getRef_no())) {
                                PurchaseOrderInfo purchaseOrderInfo = purchaseRpc.queryOrderDetailByOrderCode(response.ref_no);
                                if (Objects.nonNull(purchaseOrderInfo) && !purchaseOrderInfo.getOrderStatus().equals(50) && !purchaseOrderInfo.getOrderStatus().equals(55)) {
                                    ThirdPartyPushLogInfo thirdPartyPushLogInfo = thirdPartyPushLogManager.queryByEventBizId(ThirdPartEventType.GSP_2_ECCANG_PO_CREATE.name(), purchaseOrderInfo.getId().toString());
                                    if (Objects.nonNull(thirdPartyPushLogInfo) && thirdPartyPushLogInfo.getEventState().equals(ThirdPartyEventState.SUCCESS.getValue())) {
                                        continue;
                                    }
                                    count = syncSuccessCount(thirdPartySystem, count, response, purchaseOrderInfo, thirdPartyPushLogInfo);
                                }
                            }
                        } catch (Exception e) {
                            log.info("易仓成功收货采购单同步收货消息单条处理失败，单号：{} 失败原因：{}", response.getRef_no(), e.getMessage());
                        }
                    }
                }
            }
        }
        log.info("易仓成功收货采购单同步收货消息处理成功数量为：{}", count);
    }

    public void partialArrivalEcOrder() {
        JSONObject jsonObject = JSON.parseObject(bizIds);
        String bizId = jsonObject.getString("ECCANG");
        ThirdPartySystem thirdPartySystem = loadSystem(bizId);
        if (null == thirdPartySystem) {
            log.info("易仓采购单收货消息处理，未匹配到易仓对接信息，消息无需处理！");
            return;
        }
        int count = 0;
        EcCangApiBaseResult<String> purchaseOrdersResult = eccangERPApiSveImpl.getPurchaseOrdersNoDecode(null, thirdPartySystem, 5, null);
        if (purchaseOrdersResult.getCode().equals("200")) {
            //更新关联信息
            List<EcCangERPGetPurchaseOrdersResponse> ecCangERPGetPurchaseOrdersResponses = JSONObject.parseArray(purchaseOrdersResult.getData(), EcCangERPGetPurchaseOrdersResponse.class);
            if (CollectionUtils.isNotEmpty(ecCangERPGetPurchaseOrdersResponses)) {
                for (EcCangERPGetPurchaseOrdersResponse response : ecCangERPGetPurchaseOrdersResponses) {
                    try {
                        if (StringUtils.isNotBlank(response.getRef_no()) && response.getRef_no().startsWith("R")) {
                            PurchaseOrderInfo purchaseOrderInfo = purchaseRpc.queryOrderDetailByOrderCode(response.ref_no);
                            if (Objects.nonNull(purchaseOrderInfo) && !purchaseOrderInfo.getOrderStatus().equals(50) && !purchaseOrderInfo.getOrderStatus().equals(55)) {
                                ThirdPartyPushLogInfo thirdPartyPushLogInfo = thirdPartyPushLogManager.queryByEventBizId(ThirdPartEventType.GSP_2_ECCANG_PO_CREATE.name(), purchaseOrderInfo.getId().toString());
                                if (Objects.nonNull(thirdPartyPushLogInfo) && thirdPartyPushLogInfo.getEventState().equals(ThirdPartyEventState.SUCCESS.getValue())) {
                                    continue;
                                }
                                count = syncSuccessCount(thirdPartySystem, count, response, purchaseOrderInfo, thirdPartyPushLogInfo);
                            }
                        }
                    } catch (Exception e) {
                        log.info("易仓部分收货采购单同步收货消息单条处理失败，单号：{} 失败原因：{}", response.getRef_no(), e.getMessage());
                    }
                }
            }
        }
        log.info("易仓部分收货采购单同步收货消息单条处理成功：{}", count);
    }

    public void gspStatusChangeEcCompletedOrder(String params) {
        JSONObject jobParam = JSONObject.parseObject(params);
        JSONObject jsonObject = JSON.parseObject(bizIds);
        String bizId = jsonObject.getString("ECCANG");
        ThirdPartySystem thirdPartySystem = loadSystem(bizId);
        if (null == thirdPartySystem) {
            log.info("易仓采购单收货消息处理，未匹配到易仓对接信息，消息无需处理！");
            return;
        }
        List<PurchaseOrderInfo> purchaseOrderInfos = purchaseRpc.queryPurchaseOrderListDetailByCreateTime(jobParam);
        // 打乱排序，随机获取
        Collections.shuffle(purchaseOrderInfos);
        log.info("采购单近{}天内已付款，部分收货单据数量:{}", jobParam.get("startDay"), purchaseOrderInfos.size());
        int count = 0;
        for (PurchaseOrderInfo purchaseOrderInfo : purchaseOrderInfos) {
            EcCangApiBaseResult<String> purchaseOrdersResult = eccangERPApiSveImpl.getPurchaseOrdersNoDecode(purchaseOrderInfo.getOrderCode(), thirdPartySystem, null, null);
            if (purchaseOrdersResult.getCode().equals("200")) {
                //更新关联信息
                List<EcCangERPGetPurchaseOrdersResponse> ecCangERPGetPurchaseOrdersResponses = JSONObject.parseArray(purchaseOrdersResult.getData(), EcCangERPGetPurchaseOrdersResponse.class);
//                log.info("易仓回传信息{}", JSON.toJSONString(ecCangERPGetPurchaseOrdersResponses));
                if (CollectionUtils.isNotEmpty(ecCangERPGetPurchaseOrdersResponses)) {
                    EcCangERPGetPurchaseOrdersResponse response = ecCangERPGetPurchaseOrdersResponses.get(0);
                    try {
                        if (StringUtils.isNotBlank(response.getRef_no()) && response.getRef_no().equals(purchaseOrderInfo.getOrderCode())) {
                            ThirdPartyPushLogInfo thirdPartyPushLogInfo = thirdPartyPushLogManager.queryByEventBizId(ThirdPartEventType.GSP_2_ECCANG_PO_CREATE.name(), purchaseOrderInfo.getId().toString());
                            if (Objects.nonNull(thirdPartyPushLogInfo) && ThirdPartyEventState.SUCCESS.getValue().equals(thirdPartyPushLogInfo.getEventState())) {
                                continue;
                            }
                            log.info("采购单开始同步收货信息:{}", purchaseOrderInfo.getOrderCode());
                            count = syncSuccessCountByStatus(thirdPartySystem, count, response, purchaseOrderInfo, thirdPartyPushLogInfo);
                        }
                    } catch (Exception e) {
                        log.info("易仓成功收货采购单同步收货消息单条处理失败，单号：{} 失败原因：{}", response.getRef_no(), e.getMessage());
                    }
                }
            }
        }
        log.info("易仓成功收货采购单同步收货消息处理成功数量为：{}", count);
    }

    public void gspReSyncCancelStockOrderAndEcSuccessJob(String params) {
        JSONObject jobParam = JSONObject.parseObject(params);
        JSONObject jsonObject = JSON.parseObject(bizIds);
        String bizId = jsonObject.getString("ECCANG");
        ThirdPartySystem thirdPartySystem = loadSystem(bizId);
        if (null == thirdPartySystem) {
            log.info("补发易仓采购单收货消息处理，未匹配到易仓对接信息，消息无需处理！");
            return;
        }
        List<PurchaseOrderInfo> purchaseOrderInfos = purchaseRpc.queryCancelStockOrder(jobParam);
        int count = 0;
        for (PurchaseOrderInfo purchaseOrderInfo : purchaseOrderInfos) {
            EcCangApiBaseResult<String> purchaseOrdersResult = eccangERPApiSveImpl.getPurchaseOrdersNoDecode(purchaseOrderInfo.getOrderCode(), thirdPartySystem, 8, null);
            if (purchaseOrdersResult.getCode().equals("200")) {
                //更新关联信息
                List<EcCangERPGetPurchaseOrdersResponse> ecCangERPGetPurchaseOrdersResponses = JSONObject.parseArray(purchaseOrdersResult.getData(), EcCangERPGetPurchaseOrdersResponse.class);
                if (CollectionUtils.isNotEmpty(ecCangERPGetPurchaseOrdersResponses)) {
                    EcCangERPGetPurchaseOrdersResponse response = ecCangERPGetPurchaseOrdersResponses.get(0);
                    try {
                        if (StringUtils.isNotBlank(response.getRef_no()) && response.getRef_no().equals(purchaseOrderInfo.getOrderCode())) {
                            ThirdPartyPushLogInfo thirdPartyPushLogInfo = thirdPartyPushLogManager.queryByEventBizId(ThirdPartEventType.GSP_2_ECCANG_PO_CREATE.name(), purchaseOrderInfo.getId().toString());
                            if (!(Objects.nonNull(thirdPartyPushLogInfo) && ThirdPartyEventState.SUCCESS.getValue().equals(thirdPartyPushLogInfo.getEventState()))) {
                                continue;
                            }
                            log.info("采购单开始同步补发收货信息:{}", purchaseOrderInfo.getOrderCode());
                            count = syncSuccessCount(thirdPartySystem, count, response, purchaseOrderInfo, thirdPartyPushLogInfo);
                        }
                    } catch (Exception e) {
                        log.info("补发易仓成功收货采购单同步收货消息单条处理失败，单号：{} 失败原因：{}", response.getRef_no(), e.getMessage());
                    }
                }
            }
        }
        log.info("补发易仓成功收货采购单同步收货消息处理成功数量为：{}", count);
    }

    private int syncSuccessCount(ThirdPartySystem thirdPartySystem, int count, EcCangERPGetPurchaseOrdersResponse response, PurchaseOrderInfo purchaseOrderInfo, ThirdPartyPushLogInfo thirdPartyPushLogInfo) {
        List<EcCangERPGetPurchaseOrdersResponse.Detail> orderDetail = response.getDetail();
        Long totalArrivalQuantity = 0L;
        for (EcCangERPGetPurchaseOrdersResponse.Detail detail : orderDetail) {
            Map<String, PurchaseOrderGoodsInfo> orderGoodsInfoMap = purchaseOrderInfo.getPurchaseOrderGoodsList().stream().collect(Collectors.toMap(PurchaseOrderGoodsInfo::getSku, item -> item));
            PurchaseOrderGoodsInfo info = orderGoodsInfoMap.get(detail.getProduct_sku());
            //采购单此sku已收到货的数量
            Long arrivalQuantity = info.getArrivalQuantity();
            //采购单总收货数量
            totalArrivalQuantity += arrivalQuantity;
            //易仓的收货数量
            Long ecReceving = Long.valueOf(detail.getQty_receving());
            //gsp采购单需要收货数量
            long receivedQuantity = ecReceving - arrivalQuantity;
            log.info("准备开始签收:{}", response.getRef_no());
            if (receivedQuantity > 0) {
                //2.1签收
                if (StringUtils.isNotBlank(purchaseOrderInfo.getTrackingNumber()) || CollectionUtils.isNotEmpty(purchaseOrderInfo.getTrackingNumbers())) {
                    if (StringUtils.isNotBlank(purchaseOrderInfo.getTrackingNumber())) {
                        EntryScanningCommand scanningCommand = new EntryScanningCommand();
                        scanningCommand.setScanArrivalType("trackingNumber");
                        scanningCommand.setCode(purchaseOrderInfo.getTrackingNumber());
                        purchaseRpc.scanSignList4ThirdParty(scanningCommand);
                    } else if (CollectionUtils.isNotEmpty(purchaseOrderInfo.getTrackingNumbers())) {
                        purchaseOrderInfo.getTrackingNumbers().forEach(trackingNumber -> {
                            EntryScanningCommand scanningCommand = new EntryScanningCommand();
                            scanningCommand.setScanArrivalType("trackingNumber");
                            scanningCommand.setCode(trackingNumber);
                            purchaseRpc.scanSignList4ThirdParty(scanningCommand);
                        });
                    }
                    //2.2 查看易仓sku信息，填写寸尺
                    EcCangERPGetProductBySkuRequest request = new EcCangERPGetProductBySkuRequest();
                    request.setProductSku(Collections.singletonList(info.getSku()));
                    EcCangApiBaseResult<String> result = eccangERPApiSveImpl.getProductBySku(thirdPartySystem, request);
                    List<EcCangERPGetProductBySkuResponse> ecCangERPGetProductBySkuResponses = JSON.parseArray(result.getData(), EcCangERPGetProductBySkuResponse.class);
                    if (CollectionUtils.isNotEmpty(ecCangERPGetProductBySkuResponses)) {
                        EcCangERPGetProductBySkuResponse ecCangERPGetProductBySkuResponse = ecCangERPGetProductBySkuResponses.get(0);
                        UpdateGoodsSizeCommand updateCommand = new UpdateGoodsSizeCommand();
                        updateCommand.setSku(ecCangERPGetProductBySkuResponse.getProductSku());
                        updateCommand.setSizeLength(ecCangERPGetProductBySkuResponse.getProductLength());
                        updateCommand.setSizeWidth(ecCangERPGetProductBySkuResponse.getProductWidth());
                        updateCommand.setSizeHeight(ecCangERPGetProductBySkuResponse.getProductHeight());
                        updateCommand.setNetWeight(ecCangERPGetProductBySkuResponse.getProductWeight().multiply(new BigDecimal(1000)));
                        purchaseRpc.updateGoodsSize(updateCommand);
                    }
                    //2.3收货
                    BatchTakeStockCommand takeStockCommand = new BatchTakeStockCommand();
                    List<PurchaseOrderGoodsInfo> goodsInfos = new ArrayList<>();
                    info.setReceivingQuantity(receivedQuantity);
                    goodsInfos.add(info);
                    takeStockCommand.setOrderGoodsList(goodsInfos);
                    purchaseRpc.batchTakeStock4ThirdParty(takeStockCommand);
                    //2.4查看采购单信息,同步日志状态
                    PurchaseOrderInfo purchaseOrderInfoAfter = purchaseRpc.queryOrderDetail(purchaseOrderInfo.getId());
                    Integer orderStatus = purchaseOrderInfoAfter.getOrderStatus();
                    if (orderStatus.equals(45)) {
                        thirdPartyPushLogInfo.setSysRemark("已创建采购单，对比推送，".concat(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_RECEIVING_PART.getDesc()));
                        thirdPartyPushLogInfo.setEventBizState(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_RECEIVING_PART.getValue());
                    }
                    if (orderStatus.equals(50)) {
                        thirdPartyPushLogInfo.setSysRemark("已创建采购单，对比推送，".concat(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_RECEIVING_ALL.getDesc()));
                        thirdPartyPushLogInfo.setEventBizState(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_RECEIVING_ALL.getValue());
                        // 如果全部收货，标记成功
                        thirdPartyPushLogInfo.setEventState(ThirdPartyEventState.SUCCESS.getValue());
                        count++;
                    }
                    thirdPartyPushLogManager.insertOrUpdate(thirdPartyPushLogInfo);
                }
            } else {
                if (totalArrivalQuantity == 0) {
                    // 易仓未收到货
                    thirdPartyPushLogInfo.setSysRemark("已创建采购单，对比推送，".concat(Gsp2EccangPOCreateEventBizState.SYNC_GSP_PO_RECEIVING_WAIT_EC.getDesc()));
                    thirdPartyPushLogInfo.setEventBizState(Gsp2EccangPOCreateEventBizState.SYNC_GSP_PO_RECEIVING_WAIT_EC.getValue());
                    thirdPartyPushLogManager.insertOrUpdate(thirdPartyPushLogInfo);
                }
            }
        }
        return count;
    }

    private int syncSuccessCountByStatus(ThirdPartySystem thirdPartySystem, int count, EcCangERPGetPurchaseOrdersResponse response, PurchaseOrderInfo purchaseOrderInfo, ThirdPartyPushLogInfo thirdPartyPushLogInfo) {
        List<EcCangERPGetPurchaseOrdersResponse.Detail> orderDetail = response.getDetail();
        Long totalArrivalQuantity = 0L;
        Long totalEcQuantity = 0L;
        List<PurchaseOrderGoodsInfo> orderGoodsInfos = new ArrayList<>();
        BatchTakeStockCommand batchTakeStockCommand = new BatchTakeStockCommand();
        for (EcCangERPGetPurchaseOrdersResponse.Detail detail : orderDetail) {
            Map<String, PurchaseOrderGoodsInfo> orderGoodsInfoMap = purchaseOrderInfo.getPurchaseOrderGoodsList().stream().collect(Collectors.toMap(PurchaseOrderGoodsInfo::getSku, item -> item));
            PurchaseOrderGoodsInfo info = orderGoodsInfoMap.get(detail.getProduct_sku());
            //采购单此sku已收到货的数量
            Long arrivalQuantity = info.getArrivalQuantity();
            //采购单总收货数量
            totalArrivalQuantity += arrivalQuantity;
            //易仓的收货数量
            Long ecReceving = Long.valueOf(detail.getQty_receving());
            //易仓的总收货数量
            totalEcQuantity = totalEcQuantity + ecReceving;
            //gsp采购单需要收货数量
            long receivedQuantity = ecReceving - arrivalQuantity;
            if (receivedQuantity > 0) {
                //2.1签收
                if (StringUtils.isNotBlank(purchaseOrderInfo.getTrackingNumber()) || CollectionUtils.isNotEmpty(purchaseOrderInfo.getTrackingNumbers())) {
                    if (StringUtils.isNotBlank(purchaseOrderInfo.getTrackingNumber())) {
                        EntryScanningCommand scanningCommand = new EntryScanningCommand();
                        scanningCommand.setScanArrivalType("trackingNumber");
                        scanningCommand.setCode(purchaseOrderInfo.getTrackingNumber());
                        purchaseRpc.scanSignList4ThirdParty(scanningCommand);
                    } else if (CollectionUtils.isNotEmpty(purchaseOrderInfo.getTrackingNumbers())) {
                        purchaseOrderInfo.getTrackingNumbers().forEach(trackingNumber -> {
                            EntryScanningCommand scanningCommand = new EntryScanningCommand();
                            scanningCommand.setScanArrivalType("trackingNumber");
                            scanningCommand.setCode(trackingNumber);
                            purchaseRpc.scanSignList4ThirdParty(scanningCommand);
                        });
                    }
                    //2.2 查看易仓sku信息，填写寸尺
                    EcCangERPGetProductBySkuRequest request = new EcCangERPGetProductBySkuRequest();
                    request.setProductSku(Collections.singletonList(info.getSku()));
                    EcCangApiBaseResult<String> result = eccangERPApiSveImpl.getProductBySku(thirdPartySystem, request);
                    List<EcCangERPGetProductBySkuResponse> ecCangERPGetProductBySkuResponses = JSON.parseArray(result.getData(), EcCangERPGetProductBySkuResponse.class);
                    if (!result.getCode().equalsIgnoreCase("Failure")) {
                        if (CollectionUtils.isNotEmpty(ecCangERPGetProductBySkuResponses)) {
                            EcCangERPGetProductBySkuResponse ecCangERPGetProductBySkuResponse = ecCangERPGetProductBySkuResponses.get(0);
                            UpdateGoodsSizeCommand updateCommand = new UpdateGoodsSizeCommand();
                            updateCommand.setSku(ecCangERPGetProductBySkuResponse.getProductSku());
                            updateCommand.setSizeLength(ecCangERPGetProductBySkuResponse.getProductLength());
                            updateCommand.setSizeWidth(ecCangERPGetProductBySkuResponse.getProductWidth());
                            updateCommand.setSizeHeight(ecCangERPGetProductBySkuResponse.getProductHeight());
                            updateCommand.setNetWeight(ecCangERPGetProductBySkuResponse.getProductWeight().multiply(new BigDecimal(1000)));
                            purchaseRpc.updateGoodsSize(updateCommand);
                        }
                    }
                    //2.3收货
                    BatchTakeStockCommand takeStockCommand = new BatchTakeStockCommand();
                    List<PurchaseOrderGoodsInfo> goodsInfos = new ArrayList<>();
                    info.setReceivingQuantity(receivedQuantity);
                    goodsInfos.add(info);
                    takeStockCommand.setOrderGoodsList(goodsInfos);
                    purchaseRpc.batchTakeStock4ThirdParty(takeStockCommand);
                    //2.4查看采购单信息,同步日志状态
                    PurchaseOrderInfo purchaseOrderInfoAfter = purchaseRpc.queryOrderDetail(purchaseOrderInfo.getId());
                    Integer orderStatus = purchaseOrderInfoAfter.getOrderStatus();
                    if (orderStatus.equals(45)) {
                        thirdPartyPushLogInfo.setSysRemark("已创建采购单，对比推送，".concat(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_RECEIVING_PART.getDesc()));
                        thirdPartyPushLogInfo.setEventBizState(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_RECEIVING_PART.getValue());
                    }
                    if (orderStatus.equals(50)) {
                        thirdPartyPushLogInfo.setSysRemark("已创建采购单，对比推送，".concat(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_RECEIVING_ALL.getDesc()));
                        thirdPartyPushLogInfo.setEventBizState(Gsp2EccangPOCreateEventBizState.SYNC_EC_PO_RECEIVING_ALL.getValue());
                        // 如果全部收货，标记成功
                        thirdPartyPushLogInfo.setEventState(ThirdPartyEventState.SUCCESS.getValue());
                        count++;
                    }
                    thirdPartyPushLogManager.insertOrUpdate(thirdPartyPushLogInfo);
                }
            } else {
                if (totalArrivalQuantity == 0) {
                    // 易仓未收到货
                    thirdPartyPushLogInfo.setSysRemark("已创建采购单，对比推送，".concat(Gsp2EccangPOCreateEventBizState.SYNC_GSP_PO_RECEIVING_WAIT_EC.getDesc()));
                    thirdPartyPushLogInfo.setEventBizState(Gsp2EccangPOCreateEventBizState.SYNC_GSP_PO_RECEIVING_WAIT_EC.getValue());
                    thirdPartyPushLogManager.insertOrUpdate(thirdPartyPushLogInfo);
                }
            }
            orderGoodsInfos.add(info);
            batchTakeStockCommand.setOrderGoodsList(orderGoodsInfos);
        }
        log.info("采购单明细数量:{},易仓明细数量:{}", totalArrivalQuantity, totalEcQuantity);
        if (totalArrivalQuantity.equals(totalEcQuantity) && totalEcQuantity > 0 && totalArrivalQuantity > 0) {
            log.info("采购单批量盘点入库更新:{}", purchaseOrderInfo.getOrderCode());
            purchaseRpc.batchTakeStock4ThirdParty(batchTakeStockCommand);
        }
        return count;
    }

    private ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }

}
