package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.shopee;

import com.newnary.gsp.center.tpsi.api.haiying.response.shopee.HaiYingShopeeProductHistoryDailyDataInfoDTO;
import com.newnary.gsp.center.tpsi.api.haiying.response.shopee.HaiYingShopeeProductHistoryDataInfoDTO;
import lombok.Data;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingShopeeProductHistoryInfoResponse {

    /**
     * 商品id
     */
    private String pid;

    /**
     * 商品历史数据
     */
    private HaiYingShopeeProductHistoryDataInfoDTO[] data;

    /**
     * 商品每日数据
     */
    private HaiYingShopeeProductHistoryDailyDataInfoDTO[] daily_data;

}
