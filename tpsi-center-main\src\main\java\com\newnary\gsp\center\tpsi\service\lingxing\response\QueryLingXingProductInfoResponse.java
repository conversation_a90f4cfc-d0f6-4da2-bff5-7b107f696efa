package com.newnary.gsp.center.tpsi.service.lingxing.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Data
public class QueryLingXingProductInfoResponse {

    //状态码，0成功
    private Integer code;

    //	提示信息
    private String message;

    //错误提示
    private List<String> error_details;

    //	请求链路id
    private String request_id;

    //响应时间
    private String response_time;

    //响应数据
    private ProductInfo data;

    @Setter
    @Getter
    public static class ProductInfo {

        //产品id
        private Integer id;

        //产品名称
        private String product_name;

        //产品sku
        private String sku;

        //上传的图片地址
        private String pic_url;

        //产品图片数组
        private List<Picture> picture_list;

        @Setter
        @Getter
        public static class Picture {

            //图片链接
            private String pic_url;

            //	是否产品主图：0 否，1 是
            private Integer is_primary;
        }

        //产品型号
        private String model;

        //商品单位：套、个、台
        private String unit;

        //状态：0 停售，1 在售，2 开发中，3 清仓
        private Integer status;

        //	分类id
        private Integer cid;

        //品牌id
        private Integer bid;

        //开发者
        private String product_developer;

        //开发人
        private String product_developer_uid;

        //负责人数组
        private List<PermissionUserInfo> permission_user_info;
        @Setter
        @Getter
        public static class PermissionUserInfo {

            //负责人id
            private String permission_user_name;

            //负责人名称
            private Integer permission_uid;
        }

        //产品描述
        private String description;

        //是否组合产品：0否，1是
        private Integer is_combo;

        //中国官方汇率code
        private String currency;

        //采购：采购员
        private String cg_opt_username;

        //采购：交期
        private Integer cg_delivery;

        //采购：采购成本（人民币）
        private BigDecimal cg_price;

        //采购备注
        private String purchase_remark;

        //采购：材质
        private String cg_product_material;

        //采购：产品规格（CM）
        private BigDecimal cg_product_length;

        //采购：产品规格（CM）
        private BigDecimal cg_product_width;

        //采购：产品规格（CM）
        private BigDecimal cg_product_height;

        //采购：包装规格（CM）
        private BigDecimal cg_package_length;

        //采购：包装规格（CM）
        private BigDecimal cg_package_width;

        //采购：包装规格（CM）
        private BigDecimal cg_package_height;

        //采购：包装规格（CM）
        private BigDecimal cg_box_length;

        //采购：包装规格（CM）
        private BigDecimal cg_box_width;

        //采购：包装规格（CM）
        private BigDecimal cg_box_height;

        //采购：产品净重（G）
        private BigDecimal cg_product_net_weight;

        //采购：产品毛重（G）
        private BigDecimal cg_product_gross_weight;

        //采购：外箱实重（KG）
        private BigDecimal cg_box_weight;

        //采购：单箱数量（包装数量）
        private Integer cg_box_pcs;

        //报关：申报品名（中文）【中文报关名】
        private String bg_customs_export_name;

        //报关：申报品名（英文）【英文报关名】
        private String bg_customs_import_name;

        //报关：申报金额（进口国）【申报单价】
        private BigDecimal bg_customs_import_price;

        //报关：HSCode（出口国）【中国HSCode】
        private String bg_export_hs_code;

        //报关：HSCode（进口国）【美国HSCode】
        private String bg_import_hs_code;

        //【已废弃字段】报关：税率【美国税率】
        private BigDecimal bg_tax_rate;

        //品牌名称
        private String brand_name;

        //分类名称
        private String category_name;

        //附件id
        private List<String> attachment_id;

        //质检标准
        private QcStandard qc_standard;
        @Setter
        @Getter
        public static class QcStandard {

            //自定义质检标准
            private CustomQcTemplate custom_qc_template;
            @Setter
            @Getter
            public static class CustomQcTemplate {

                //质检图片
                private List<QcImage> qc_image;
                @Setter
                @Getter
                public static class QcImage {

                    //领星文件id
                    private String customer_url;

                    //客户的图片URL
                    private Integer file_id;
                }

            }
        }

        //供应商报价数据
        private List<SupplierQuote> supplier_quote;
        @Setter
        @Getter
        public static class SupplierQuote {

            //供应商报价id
            private String psq_id;

            //产品id
            private Integer product_id;

            //供应商id
            private Integer supplier_id;

            //供应商名称
            private String supplier_name;

            //是否为首选供应商：0否，1是
            private Integer is_primary;

            //报价备注
            private String quote_remark;

            //采购链接
            private List<String> supplier_product_url;

            //报价数据
            private List<Quote> quotes;
            @Setter
            @Getter
            public static class Quote {

                //报价币种
                private String currency;

                //报价币种符号
                private String currency_icon;

                //是否含税：0否，1是
                private Integer is_tax;

                //税率（百分比）
                private BigDecimal tax_rate;

                //阶梯报价
                private List<StepPrice> step_prices;
                @Setter
                @Getter
                public static class StepPrice {

                    //最小采购量
                    private Integer moq;

                    //不含税单价
                    private BigDecimal price;

                    //含税单价
                    private BigDecimal price_with_tax;
                }

            }
        }

        //组合商品列表
        private List<ComboProduct> combo_product_list;
        @Setter
        @Getter
        public static class ComboProduct {

            //本地产品id
            private Integer product_id;

            //数量
            private Integer quantity;

            //SKU
            private String sku;
        }

        //物料关联【XX为国家简码，比如美国 US】
        private List<ProductLogisticsRelation> product_logistics_relation;
        @Setter
        @Getter
        public static class ProductLogisticsRelation {

            //默认头程成本(含税)
            private BigDecimal XX_cg_transport_costs;

            //官方汇率code
            private String XX_currency;

            //清关价格
            private BigDecimal XX_clearance_price;

            //清关价格币种
            private String XX_clearance_price_currency;

            //报关：HSCode（进口国）
            private String XX_bg_import_hs_code;

            //报关：税率
            private BigDecimal XX_bg_tax_rate;
        }

        //产品特殊属性：
        //1 含电
        //2 纯电
        //3 液体
        //4 粉末
        //5 膏体
        //6 带磁
        private List<String> special_attr;

        //报关数据
        private Declaration declaration;
        @Setter
        @Getter
        public static class Declaration {

            //报关单位
            private String customs_declaration_unit;

            //规格型号
            private String customs_declaration_spec;

            //报关：原厂国（地区）
            private String customs_declaration_origin_produce;

            //报关：境内货源地
            private String customs_declaration_inlands_source;

            //报关：征免
            private String customs_declaration_exempt;
        }

        //清关数据
        private Clearance clearance;
        @Setter
        @Getter
        public static class Clearance {

            //清关：材质
            private String customs_clearance_material;

            //清关：用途
            private String customs_clearance_usage;

            //清关：内部编码
            private String customs_clearance_internal_code;

            //	清关：出口享惠情况：
            //1 不享惠
            //2 享惠
            //3 不确定享惠情况
            private Integer customs_clearance_preferential;

            //清关：品牌类型：
            //1 无品牌
            //2 境内自主品牌
            //3 境内收购品牌
            //4 境外品牌（贴牌生产）
            //5 境外品牌（其他）
            private Integer customs_clearance_brand_type;

            //清关：产品型号
            private String customs_clearance_product_pattern;

            //清关：配货备注
            private String allocation_remark;

            //织造方式：1 针织，2 梭织
            private Integer weaving_mode;

            //清关：清关图片
            private String customs_clearance_pic_url;
        }
    }

}
