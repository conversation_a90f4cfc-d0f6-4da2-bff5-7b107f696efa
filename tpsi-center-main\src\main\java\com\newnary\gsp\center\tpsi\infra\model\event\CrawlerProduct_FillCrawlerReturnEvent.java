package com.newnary.gsp.center.tpsi.infra.model.event;

import com.newnary.gsp.center.tpsi.infra.model.CrawlerProduct;
import com.newnary.spring.cloud.domain.event.AbstractAggregateEvent;

/**
 * <AUTHOR>
 * @since Created on 2023-03-22
 **/
public class CrawlerProduct_FillCrawlerReturnEvent extends AbstractAggregateEvent<CrawlerProduct> {
    public CrawlerProduct_FillCrawlerReturnEvent(CrawlerProduct source, String identify) {
        super(source, identify);
    }
}
