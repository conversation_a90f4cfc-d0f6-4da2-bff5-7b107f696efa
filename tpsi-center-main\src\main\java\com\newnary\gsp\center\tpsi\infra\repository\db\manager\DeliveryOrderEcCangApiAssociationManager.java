package com.newnary.gsp.center.tpsi.infra.repository.db.manager;

import com.newnary.dao.base.helper.DaoHelper;
import com.newnary.dao.base.po.BaseQuery;
import com.newnary.gsp.center.tpsi.infra.repository.db.dao.DeliveryOrderEcCangApiAssociationDao;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.DeliveryOrderEcCangApiAssociationPO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class DeliveryOrderEcCangApiAssociationManager {

    @Resource
    private DeliveryOrderEcCangApiAssociationDao deliveryOrderEcCangApiAssociationDao;

    public DeliveryOrderEcCangApiAssociationPO getByDeliveryOrderId(String deliveryOrderId) {
        BaseQuery<DeliveryOrderEcCangApiAssociationPO> query = new BaseQuery<>(new DeliveryOrderEcCangApiAssociationPO());
        query.getData().setDeliveryOrderId(deliveryOrderId);
        List<DeliveryOrderEcCangApiAssociationPO> result = deliveryOrderEcCangApiAssociationDao.query(query);
        if (CollectionUtils.isNotEmpty(result)) {
            return result.get(0);
        }
        return null;
    }

    public DeliveryOrderEcCangApiAssociationPO getByStockoutOrderId(String stockoutOrderId) {
        BaseQuery<DeliveryOrderEcCangApiAssociationPO> query = new BaseQuery<>(new DeliveryOrderEcCangApiAssociationPO());
        query.getData().setStockoutOrderId(stockoutOrderId);
        List<DeliveryOrderEcCangApiAssociationPO> result = deliveryOrderEcCangApiAssociationDao.query(query);
        if (CollectionUtils.isNotEmpty(result)) {
            return result.get(0);
        }
        return null;
    }

    public List<DeliveryOrderEcCangApiAssociationPO> getByTransportOrderId(String transportOrderId) {
        BaseQuery<DeliveryOrderEcCangApiAssociationPO> query = new BaseQuery<>(new DeliveryOrderEcCangApiAssociationPO());
        query.getData().setTransportOrderId(transportOrderId);
        return deliveryOrderEcCangApiAssociationDao.query(query);
    }

    public DeliveryOrderEcCangApiAssociationPO getByAssociationId(String associationId) {
        BaseQuery<DeliveryOrderEcCangApiAssociationPO> query = new BaseQuery<>(new DeliveryOrderEcCangApiAssociationPO());
        query.getData().setAssociationId(associationId);
        List<DeliveryOrderEcCangApiAssociationPO> result = deliveryOrderEcCangApiAssociationDao.query(query);
        if (CollectionUtils.isNotEmpty(result)) {
            return result.get(0);
        }
        return null;
    }

    public DeliveryOrderEcCangApiAssociationDao getDao() {
        return deliveryOrderEcCangApiAssociationDao;
    }

    public static void main(String[] args) {
        DaoHelper.genXMLWithFeature(
                "D:\\NewnaryWorkspace\\tpsi-center\\tpsi-center-main\\src\\main\\java\\com\\newnary\\gsp\\center\\tpsi\\infra\\repository\\db\\dao\\DeliveryOrderEcCangApiAssociationDao.xml",
                DeliveryOrderEcCangApiAssociationDao.class,
                DeliveryOrderEcCangApiAssociationPO.class,
                "delivery_order_eccang_api_association",
                false);
    }

}
