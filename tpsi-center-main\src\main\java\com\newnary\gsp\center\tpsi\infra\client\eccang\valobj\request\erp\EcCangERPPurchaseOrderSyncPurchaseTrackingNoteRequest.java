package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.request.erp;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.List;

@Data
public class EcCangERPPurchaseOrderSyncPurchaseTrackingNoteRequest {

    /** 采购单单号 */
    @NotBlank(message = "采购单单号不能为空")
    private String poCode;

    /** 供应商运输方式 id */
    @NotBlank(message = "供应商运输方式 id不能为空")
    private String supplierMethodId;

    /** 承运商 id */
    @NotBlank(message = "承运商 id不能为空")
    private Integer purchaseShipperId;

    /**
     * 支付单号
     */
    private String transactionNo;

    /** 备注 */
    private String trackNote;

    /**
     * 运费
     */
    private BigDecimal payShipAmount;

    /**
     * 操作类型：0新增、1覆盖，默认新增
     */
    private Integer operationType;

    /** 跟踪记录*/
    private List<TrackingNoteRecord> trackingNoteRecord;

    public static class TrackingNoteRecord {

        /**
         * 签收时间 格式：'0000-00-00 00:00:00'
         */
        public String signTime;

        /** 跟踪单号 */
        public String trackingNo;

        public TrackingNoteRecord(String trackingNo) {
            this.trackingNo = trackingNo;
        }
    }
}
