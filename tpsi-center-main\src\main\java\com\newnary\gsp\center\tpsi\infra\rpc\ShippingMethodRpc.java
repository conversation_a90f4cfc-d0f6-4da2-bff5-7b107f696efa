package com.newnary.gsp.center.tpsi.infra.rpc;


import com.newnary.gsp.center.logistics.api.carrier.feign.LogisticsFeignApi;
import com.newnary.gsp.center.logistics.api.carrier.feign.LogisticsProductFeignApi;
import com.newnary.gsp.center.logistics.api.carrier.response.LogisticsChannelMappingInfo;
import com.newnary.gsp.center.logistics.api.carrier.response.LogisticsInfo;
import com.newnary.gsp.center.logistics.api.carrier.response.LogisticsProductInfo;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class ShippingMethodRpc {

    @Resource
    LogisticsProductFeignApi logisticsProductFeignApi;

    @Resource
    LogisticsFeignApi logisticsFeignApi;

    public String getLogisticsProductCodeById(String logisticsProductId) {
        LogisticsProductInfo logisticsProductInfo = logisticsProductFeignApi.get(logisticsProductId).getBody();
        if (ObjectUtils.isNotEmpty(logisticsProductInfo)) {
            return logisticsProductInfo.getProductCode();
        } else {
            return null;
        }
    }

    public String getLogisticsCodeById(String logisticsId) {
        LogisticsInfo logisticsInfo = logisticsFeignApi.get(logisticsId).getBody();
        if (ObjectUtils.isNotEmpty(logisticsInfo)) {
            return logisticsInfo.getLogisticsCode();
        } else {
            return null;
        }
    }

    public Map<String, String> getLogisticsChannelMappingList(String logisticsCode) {
        List<LogisticsChannelMappingInfo> logisticsChannelMappingInfos = logisticsFeignApi.queryLogisticsChannelMappingList(logisticsCode).mustSuccessOrThrowOriginal();
        return logisticsChannelMappingInfos
                .stream()
                .collect(Collectors.toMap(LogisticsChannelMappingInfo::getLogisticsCode,LogisticsChannelMappingInfo::getChannelCode));
    }
}
