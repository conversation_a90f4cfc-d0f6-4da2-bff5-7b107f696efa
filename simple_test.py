#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
用于快速测试PaddleOCR是否正常工作
"""

import sys
import traceback

def test_basic_imports():
    """测试基本导入"""
    print("测试基本包导入...")
    
    try:
        import numpy as np
        print("✓ numpy 导入成功")
    except ImportError as e:
        print(f"✗ numpy 导入失败: {e}")
        return False
    
    try:
        import pandas as pd
        print("✓ pandas 导入成功")
    except ImportError as e:
        print(f"✗ pandas 导入失败: {e}")
        return False
    
    try:
        from PIL import Image
        print("✓ Pillow 导入成功")
    except ImportError as e:
        print(f"✗ Pillow 导入失败: {e}")
        return False
    
    try:
        import cv2
        print("✓ opencv-python 导入成功")
    except ImportError as e:
        print(f"✗ opencv-python 导入失败: {e}")
        return False
    
    try:
        import fitz
        print("✓ PyMuPDF 导入成功")
    except ImportError as e:
        print(f"✗ PyMuPDF 导入失败: {e}")
        return False
    
    return True

def test_paddle_imports():
    """测试PaddlePaddle相关导入"""
    print("\n测试PaddlePaddle相关包...")
    
    try:
        import paddle
        print(f"✓ paddlepaddle 导入成功，版本: {paddle.__version__}")
    except ImportError as e:
        print(f"✗ paddlepaddle 导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ paddlepaddle 导入异常: {e}")
        return False
    
    try:
        import paddleocr
        print(f"✓ paddleocr 导入成功")
    except ImportError as e:
        print(f"✗ paddleocr 导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ paddleocr 导入异常: {e}")
        return False
    
    return True

def test_paddleocr_init():
    """测试PaddleOCR初始化"""
    print("\n测试PaddleOCR初始化...")
    
    try:
        from paddleocr import PaddleOCR
        
        print("正在初始化PaddleOCR（这可能需要一些时间，特别是首次运行）...")
        
        # 使用最简单的配置
        ocr = PaddleOCR(
            use_textline_orientation=True, 
            lang='ch', 
            use_gpu=False,
            show_log=False  # 减少日志输出
        )
        
        print("✓ PaddleOCR初始化成功")
        return True, ocr
        
    except Exception as e:
        print(f"✗ PaddleOCR初始化失败: {e}")
        print("\n详细错误信息:")
        traceback.print_exc()
        return False, None

def test_simple_ocr():
    """测试简单OCR功能"""
    print("\n测试简单OCR功能...")
    
    success, ocr = test_paddleocr_init()
    if not success:
        return False
    
    try:
        # 创建一个简单的测试图片
        import numpy as np
        from PIL import Image, ImageDraw, ImageFont
        
        # 创建白色背景图片
        img = Image.new('RGB', (300, 100), color='white')
        draw = ImageDraw.Draw(img)
        
        # 添加一些中文文字
        try:
            # 尝试使用系统字体
            font = ImageFont.truetype("simhei.ttf", 24)
        except:
            try:
                font = ImageFont.truetype("arial.ttf", 24)
            except:
                font = ImageFont.load_default()
        
        draw.text((10, 30), "测试文字123", fill='black', font=font)
        
        # 转换为numpy数组
        img_array = np.array(img)
        
        print("正在进行OCR识别...")
        result = ocr.ocr(img_array, cls=True)
        
        if result and result[0]:
            print("✓ OCR识别成功")
            for line in result[0]:
                if len(line) >= 2:
                    print(f"识别文字: {line[1][0]}")
            return True
        else:
            print("✗ OCR识别失败：未返回结果")
            return False
            
    except Exception as e:
        print(f"✗ OCR测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("发票OCR工具简单测试")
    print("=" * 50)
    
    print(f"Python版本: {sys.version}")
    print()
    
    # 测试基本导入
    if not test_basic_imports():
        print("\n❌ 基本包导入失败，请先安装依赖包")
        print("运行: python install_dependencies.py")
        return
    
    # 测试PaddlePaddle导入
    if not test_paddle_imports():
        print("\n❌ PaddlePaddle相关包导入失败")
        print("请尝试重新安装:")
        print("pip install paddlepaddle>=2.5.0")
        print("pip install paddleocr>=2.7.0")
        return
    
    # 测试OCR功能
    if test_simple_ocr():
        print("\n🎉 所有测试通过！发票OCR工具可以正常使用。")
    else:
        print("\n❌ OCR功能测试失败")
        print("可能的解决方案:")
        print("1. 检查网络连接（首次运行需要下载模型）")
        print("2. 重新安装PaddleOCR: pip install --upgrade paddleocr")
        print("3. 检查系统内存是否充足")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
