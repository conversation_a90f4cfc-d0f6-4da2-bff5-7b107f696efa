package com.newnary.gsp.center.tpsi.ctrl.open1688;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.api.base.constants.HeaderNameConstants;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.product.api.product.request.SupplierSpuCreateV2Command;
import com.newnary.gsp.center.product.api.product.request.SupplierSpuUpdateV2Command;
import com.newnary.gsp.center.tpsi.api.open1688.Operate1688ProductApi;
import com.newnary.gsp.center.tpsi.app.job.Open1688JobManager;
import com.newnary.gsp.center.tpsi.ctrl.basesystembiz.BaseSystemBizId;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.request.QueryProductRequest;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.response.QueryProductResponse;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingInfo;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingType;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.ThirdPartyMappingManager;
import com.newnary.gsp.center.tpsi.service.open1688.Open1688Service;
import com.newnary.spring.cloud.context.RequestContext;
import com.newnary.tenant.context.TenantCarrier;
import com.newnary.tenant.context.TenantID;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@RestController
@Slf4j
public class Operate1688ProductApiImpl implements Operate1688ProductApi {
    @Resource
    private Open1688Service open1688ServiceImpl;

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Resource
    private BaseSystemBizId baseSystemBizId;

    @Resource
    private ThirdPartyMappingManager thirdPartyMappingManager;

    @Autowired
    private Open1688JobManager open1688JobManager;

    @Override
    public CommonResponse<String> updateProductInfo(@RequestBody List<String> customCodeList) {
        if (CollectionUtils.isNotEmpty(customCodeList)) {

            customCodeList.forEach(customCode -> {
                QueryProductRequest queryProductRequest = new QueryProductRequest();
                queryProductRequest.setOfferId(customCode);
                try {
                    TenantCarrier.setTenantID(new TenantID(RequestContext.assertHeader(HeaderNameConstants.TENANT_ID)));
                    ThirdPartySystem thirdPartySystem = loadSystem(baseSystemBizId.get("OPEN1688"));
                    ThirdPartyMappingInfo thirdPartyMappingInfo = thirdPartyMappingManager.getNewestInfoByTarget("OPEN1688", "GSP", customCode, ThirdPartyMappingType.PRODUCT_ID);
                    if (ObjectUtils.isEmpty(thirdPartyMappingInfo)) {
                        log.info("1688分销商品不存在 customCode={}", customCode);
                        return;
                    }
                    QueryProductResponse queryProductResponse = open1688ServiceImpl.queryProductById(thirdPartySystem, queryProductRequest);
                    SupplierSpuCreateV2Command spuCreateV2Command = open1688JobManager.buildSupplierSpuCreateV2Command(thirdPartySystem, queryProductResponse);
                    SupplierSpuUpdateV2Command supplierSpuUpdateV2Command = buildSupplierSpuUpdateV2Command(thirdPartyMappingInfo.getSourceId(), spuCreateV2Command);
                    //TODO 执行商品更新

                    //更新商品库存
                    open1688JobManager.updateStock(thirdPartySystem, queryProductResponse);
                    //更新商品价格
                    open1688JobManager.updatePrice(thirdPartySystem, queryProductResponse);
                } catch (Exception e) {
                    log.info("1688商品更新失败 customCode={}", customCode);
                } finally {
                    Optional<TenantID> optionalTenantID = TenantCarrier.getTenantID();
                    if (optionalTenantID.isPresent()) {
                        TenantCarrier.clearTenantID();
                    }
                }
            });
           return CommonResponse.success("更新成功");
        }
        return CommonResponse.success("customCodeList 不能为空");
    }

    private SupplierSpuUpdateV2Command buildSupplierSpuUpdateV2Command(String supplierSpuId, SupplierSpuCreateV2Command spuCreateV2Command) {
        SupplierSpuUpdateV2Command supplierSpuUpdateV2Command = new SupplierSpuUpdateV2Command();
        supplierSpuUpdateV2Command.setSupplierSpuId(supplierSpuId);
        supplierSpuUpdateV2Command.setSupplierId(spuCreateV2Command.getSupplierId());
        supplierSpuUpdateV2Command.setDescInfos(spuCreateV2Command.getDescInfos());
        supplierSpuUpdateV2Command.setDefaultLocale(spuCreateV2Command.getDefaultLocale());
        supplierSpuUpdateV2Command.setCustomCode(spuCreateV2Command.getCustomCode());
        supplierSpuUpdateV2Command.setCustomBrandId(spuCreateV2Command.getCustomBrandId());
        supplierSpuUpdateV2Command.setCategoryId(spuCreateV2Command.getCategoryId());
        supplierSpuUpdateV2Command.setCustomCategoryId(spuCreateV2Command.getCustomCategoryId());
        supplierSpuUpdateV2Command.setMgmtCategoryLevel(spuCreateV2Command.getMgmtCategoryLevel());
        supplierSpuUpdateV2Command.setMgmtCategoryId(spuCreateV2Command.getMgmtCategoryId());
        supplierSpuUpdateV2Command.setMainImages(spuCreateV2Command.getMainImages());
        supplierSpuUpdateV2Command.setDetailImages(spuCreateV2Command.getDetailImages());
        supplierSpuUpdateV2Command.setVideos(spuCreateV2Command.getVideos());
        supplierSpuUpdateV2Command.setCountryOfOriginCode(spuCreateV2Command.getCountryOfOriginCode());
        supplierSpuUpdateV2Command.setLogisticsAttrInfo(spuCreateV2Command.getLogisticsAttrInfo());
        supplierSpuUpdateV2Command.setSkuList(spuCreateV2Command.getSkuList());
        supplierSpuUpdateV2Command.setParamsInfo(spuCreateV2Command.getParamsInfo());
        supplierSpuUpdateV2Command.setIsAutoAuditPass(spuCreateV2Command.getIsAutoAuditPass());
        supplierSpuUpdateV2Command.setIsCheckAttribute(spuCreateV2Command.getIsCheckAttribute());
        supplierSpuUpdateV2Command.setOperator(spuCreateV2Command.getOperator());

        return supplierSpuUpdateV2Command;
    }


    public ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }
}
