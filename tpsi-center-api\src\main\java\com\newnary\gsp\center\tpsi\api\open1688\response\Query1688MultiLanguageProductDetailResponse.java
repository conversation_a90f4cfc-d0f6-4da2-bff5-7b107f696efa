package com.newnary.gsp.center.tpsi.api.open1688.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

// 多语言商详响应结果
@Data
public class Query1688MultiLanguageProductDetailResponse {

    /**
     * 商品id
     */
    private Long offerId;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 中文标题
     */
    private String subject;

    /**
     * 译文标题
     */
    private String subjectTrans;

    /**
     * 详情描述
     */
    private String description;

    /**
     * 主视频
     */
    private String mainVideo;

    /**
     * 详情视频
     */
    private String detailVideo;

    /**
     * 图片模型
     */
    private ProductImage productImage;

    /**
     * 商品CPV属性
     */
    private List<ProductAttribute> productAttribute;

    /**
     * 商品SKU
     */
    private List<ProductSkuInfo> productSkuInfos;

    /**
     * 商品销售信息
     */
    private ProductSaleInfo productSaleInfo;

    /**
     * 商品包裹配送相关数据
     */
    private ProductShippingInfo productShippingInfo;

    /**
     * 是否精选货源
     */
    private Boolean isJxhy;

    /**
     * 商家加密ID
     */
    private String sellerOpenId;

    /**
     * 最小起批量模型
     */
    private String minOrderQuantity;

    /**
     * 一手数量
     */
    private String batchNumber;

    /**
     * 商品状态
     */
    private String status;

    /**
     * 商品服务标签
     */
    private List<TagInfo> tagInfoList;

    /**
     * 打点信息，用于向1688上报打点数据
     */
    private String traceInfo;

    /**
     * 卖家混批配置
     */
    private SellerMixSetting sellerMixSetting;

    /**
     * 商品货号
     */
    private String productCargoNumber;

    /**
     * 商家属性数据
     */
    private SellerDataInfo sellerDataInfo;

    /**
     * 商品销量
     */
    private String soldOut;

    /**
     * 渠道价格数据
     */
    private ChannelPrice channelPrice;

    /**
     * 营销
     */
    private PromotionModel promotionModel;

    /**
     * 商品交易评分
     */
    private String tradeScore;

    /**
     * 一级类目
     */
    private Long topCategoryId;

    /**
     * 二级类目
     */
    private Long secondCategoryId;

    /**
     * 三级类目
     */
    private Long thirdCategoryId;

    /**
     * 多语言卖点
     */
    private String sellingPoint;

    /**
     * 商家身份
     */
    private String offerIdentities;

    /**
     * 创建时间
     */
    private String createDate;

    /**
     * 跨境select货盘
     */
    private Boolean isSelect;

    @Getter
    @Setter
    public static class ProductImage {
        /**
         * 图片
         */
        private String[] images;

        /**
         * 白底图
         */
        private String whiteImage;
    }

    @Getter
    @Setter
    public static class ProductAttribute {
        /**
         * 属性id
         */
        private String attributeId;

        /**
         * 属性名称
         */
        private String attributeName;

        /**
         * 属性值
         */
        private String value;

        /**
         * 属性名称翻译
         */
        private String attributeNameTrans;

        /**
         * 属性值翻译
         */
        private String valueTrans;
    }

    @Getter
    @Setter
    public static class ProductSkuInfo {
        /**
         * 库存
         */
        private Integer amountOnSale;

        /**
         * 价格
         */
        private String price;

        /**
         * 精选货源价格
         */
        private String jxhyPrice;

        /**
         * sku
         */
        private Long skuId;

        /**
         * specid
         */
        private String specId;

        /**
         * 属性
         */
        private List<SkuAttribute> skuAttributes;

        /**
         * 皮阿法精选货源价
         */
        private String pfJxhyPrice;

        /**
         * 分销价格，一件代发价
         */
        private String consignPrice;

        /**
         * sku级别
         */
        private String cargoNumber;

        /**
         * 营销价
         */
        private String promotionPrice;
    }

    @Getter
    @Setter
    public static class SkuAttribute {
        /**
         * 属性id
         */
        private Long attributeId;

        /**
         * 属性名
         */
        private String attributeName;

        /**
         * 属性名翻译
         */
        private String attributeNameTrans;

        /**
         * 值
         */
        private String value;

        /**
         * 值翻译
         */
        private String valueTrans;

        /**
         * sku图片
         */
        private String skuImageUrl;
    }

    @Getter
    @Setter
    public static class ProductSaleInfo {
        /**
         * 商品库存
         */
        private Integer amountOnSale;

        /**
         * 价格区间
         */
        private List<PriceRange> priceRangeList;

        /**
         * 报价类型
         */
        private Integer quoteType;

        /**
         * 分销价，一件代发价格
         */
        private String consignPrice;

        /**
         * 精选货源价格
         */
        private String jxhyPrice;

        /**
         * 单位信息
         */
        private UnitInfo unitInfo;
    }

    @Getter
    @Setter
    public static class PriceRange {
        /**
         * 起批量
         */
        private Integer startQuantity;

        /**
         * 批发价
         */
        private String price;

        /**
         * 营销价
         */
        private String promotionPrice;
    }

    @Getter
    @Setter
    public static class UnitInfo {
        /**
         * 中文单位
         */
        private String unit;

        /**
         * 译文单位
         */
        private String transUnit;
    }

    @Getter
    @Setter
    public static class ProductShippingInfo {
        /**
         * 地址
         */
        private String sendGoodsAddressText;

        /**
         * 重，单位kg
         */
        private Double weight;

        /**
         * 宽，单位cm
         */
        private Double width;

        /**
         * 高，单位cm
         */
        private Double height;

        /**
         * 长，单位cm
         */
        private Double length;

        /**
         * sku件重尺集合
         */
        private List<SkuShippingDetail> skuShippingDetails;
    }

    @Getter
    @Setter
    public static class SkuShippingDetail {
        /**
         * skuId
         */
        private String skuId;

        /**
         * 宽，单位cm
         */
        private Double width;

        /**
         * 长，单位cm
         */
        private Double length;

        /**
         * 高，单位cm
         */
        private Double height;

        /**
         * 重，单位g
         */
        private Double weight;

        /**
         * 件重尺来源
         */
        private String pkgSizeSource;
    }

    @Getter
    @Setter
    public static class TagInfo {
        /**
         * 服务名
         */
        private String key;

        /**
         * 是否开通
         */
        private Boolean value;
    }

    @Getter
    @Setter
    public static class SellerMixSetting {
        /**
         * 是否普通混批
         */
        private Boolean generalHunpi;

        /**
         * 混批金额
         */
        private Double mixAmount;

        /**
         * 混批数量
         */
        private Integer mixNumber;
    }

    @Getter
    @Setter
    public static class SellerDataInfo {
        /**
         * 卖家交易勋章
         */
        private String tradeMedalLevel;

        /**
         * 综合服务分
         */
        private Double compositeServiceScore;

        /**
         * 物流体验分
         */
        private Double logisticsExperienceScore;

        /**
         * 纠纷解决分
         */
        private Double disputeComplaintScore;

        /**
         * 商品体验分
         */
        private Double offerExperienceScore;

        /**
         * 咨询体验分
         */
        private Double consultingExperienceScore;

        /**
         * 卖家回头率
         */
        private Double repeatPurchasePercent;

        /**
         * 退换体验分
         */
        private Double afterSalesExperienceScore;
    }

    @Getter
    @Setter
    public static class ChannelPrice {
        /**
         * 渠道sku价格列表
         */
        private List<ChannelSkuPrice> channelSkuPriceList;
    }

    @Getter
    @Setter
    public static class ChannelSkuPrice {
        /**
         * sku id
         */
        private Long skuId;

        /**
         * 渠道价格
         */
        private Double currentPrice;
    }

    @Getter
    @Setter
    public static class PromotionModel {
        /**
         * 是否有营销
         */
        private Boolean hasPromotion;

        /**
         * 营销类型
         */
        private String promotionType;
    }
}
