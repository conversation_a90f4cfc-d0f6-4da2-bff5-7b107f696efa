package com.newnary.gsp.center.tpsi.app.listener.mq.domain;

import com.newnary.distributed.tools.concurrent.template.DConcurrentTemplate;
import com.newnary.gsp.center.tpsi.infra.model.CrawlerProduct;
import com.newnary.gsp.center.tpsi.infra.model.event.CrawlerProduct_StoreProductIdEvent;
import com.newnary.gsp.center.tpsi.infra.repository.ICrawProductRepository;
import com.newnary.spring.cloud.domain.DomainEventListener;
import com.newnary.spring.cloud.domain.DomainEventListenerMode;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since Created on 2023-03-17
 **/
@Component
public class CrawlerProduct_StoreProductIdEventListener implements DomainEventListener<CrawlerProduct_StoreProductIdEvent> {

    @Resource
    private ICrawProductRepository crawProductRepository;

    @Override
    public void onListen(CrawlerProduct_StoreProductIdEvent crawlerProduct_storeProductIdEvent) {
        //创建商品id
        CrawlerProduct source = crawlerProduct_storeProductIdEvent.source();
        DConcurrentTemplate.tryLockMode(
                CrawlerProduct.CRAWLER_PRODUCT_LOCK_PREFIX.concat(source.getProductId()),
                lock -> lock.tryLock(1, TimeUnit.SECONDS),
                () -> {
                    crawProductRepository.store(source);
                }
        );
    }

    @Override
    public Class<CrawlerProduct_StoreProductIdEvent> eventClz() {
        return CrawlerProduct_StoreProductIdEvent.class;
    }

    @Override
    public DomainEventListenerMode mode() {
        return DomainEventListenerMode.ASYNCHRONOUS;
    }
}
