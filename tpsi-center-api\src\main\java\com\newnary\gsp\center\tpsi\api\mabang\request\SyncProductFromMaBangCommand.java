package com.newnary.gsp.center.tpsi.api.mabang.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Set;

@Data
public class SyncProductFromMaBangCommand {

    @NotBlank(message = "第三方系统ID（必填）")
    public String thirdPartySystemId;

    /**
     * 马帮类目名称集合
     */
    public Set<String> categoryNameSet;

    /**
     * 获取主spu请求起始页
     */
    public Integer spuStartPage;

    /**
     * 获取主spu请求结束页
     */
    public Integer spuEndPage;

    /**
     * 获取库存sku请求结束页
     */
    public Integer skuStartPage;

    /**
     * 获取库存sku请求结束页
     */
    public Integer skuEndPage;

    /**
     * 指定spu
     */
    public String appointSpu;

    /**
     * 指定sku
     */
    public String appointSku;

    /**
     * 指定每页插入数量
     */
    public Integer pageSize;

}
