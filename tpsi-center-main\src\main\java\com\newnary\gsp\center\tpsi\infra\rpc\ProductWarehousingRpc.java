package com.newnary.gsp.center.tpsi.infra.rpc;

import com.newnary.api.base.common.CommonResponse;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.product.api.open.feign.OpenSupplierProductFeignApi;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierAdjustSupplyPrice4BatchReq;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierAdjustSupplyPriceReq;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierUpdateStock4BatchReq;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierUpdateStockReq;
import com.newnary.gsp.center.product.api.product.request.SupplierSkuCreateInfo;
import com.newnary.gsp.center.product.api.product.request.SupplierSpuCreateV2Command;
import com.newnary.gsp.center.tpsi.infra.model.CrawlerProduct;
import com.newnary.gsp.center.tpsi.infra.model.vo.ProductMappingInfo;
import com.newnary.gsp.center.tpsi.infra.translator.CrawlerReplyTranslator;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/03/25
 */
@Component
public class ProductWarehousingRpc {

    @Resource
    private OpenSupplierProductFeignApi openSupplierProductFeignApi;

    public void productWarehousing(CrawlerProduct crawlerProduct, ProductMappingInfo mappingInfo, SupplierSpuCreateV2Command createCommand, String supplierId, String customWarehouseCode) {
        CommonResponse<String> response = openSupplierProductFeignApi.createSpu(createCommand);
        if (response.isSuccess()) {
            changePriceAndSetInventory(mappingInfo, createCommand, supplierId, customWarehouseCode);
        } else {
            crawlerProduct.setFailReason(response.getStatus().getMessage());
            crawlerProduct.failWarehoused();
            //创建失败
            throw new ServiceException(CommonErrorInfo.ERROR_100_SYSTEM_ERROR, "商品" + crawlerProduct.getProductId() + "入库失败， createSpu失败原因:" + response.getStatus().getMessage());
        }
    }

    public void changePriceAndSetInventory(ProductMappingInfo mappingInfo, SupplierSpuCreateV2Command createCommand, String supplierId, String customWarehouseCode) {
        List<SupplierSkuCreateInfo> skuList = createCommand.getSkuList();

        // 设置价格集合
        List<OpenSupplierAdjustSupplyPriceReq> supplyPriceList = new ArrayList<OpenSupplierAdjustSupplyPriceReq>();
        // 设置库存集合
        List<OpenSupplierUpdateStockReq> updateStockList = new ArrayList<OpenSupplierUpdateStockReq>();

        skuList.forEach(sku->{
            //设置价格
            OpenSupplierAdjustSupplyPriceReq supplyPriceReq = new OpenSupplierAdjustSupplyPriceReq();
            CrawlerReplyTranslator.fill1688OpenSupplierAdjustSupplyPrice(sku, supplyPriceReq, supplierId,mappingInfo);
            supplyPriceList.add(supplyPriceReq);
            //设置库存
            OpenSupplierUpdateStockReq updateStockReq = new OpenSupplierUpdateStockReq();
            CrawlerReplyTranslator.fill1688OpenSupplierUpdateStock(sku, mappingInfo, updateStockReq, supplierId, customWarehouseCode);
            updateStockList.add(updateStockReq);
        });

        //更新价格
        OpenSupplierAdjustSupplyPrice4BatchReq openSupplierAdjustSupplyPrice4BatchReq = new OpenSupplierAdjustSupplyPrice4BatchReq();
        openSupplierAdjustSupplyPrice4BatchReq.setReqs(supplyPriceList);
        openSupplierProductFeignApi.adjustSupplyPrice4Batch(openSupplierAdjustSupplyPrice4BatchReq);

        //更新库存
        OpenSupplierUpdateStock4BatchReq openSupplierUpdateStock4BatchReq = new OpenSupplierUpdateStock4BatchReq();
        openSupplierUpdateStock4BatchReq.setReqs(updateStockList);
        openSupplierProductFeignApi.updateStock4Batch(openSupplierUpdateStock4BatchReq);

    }

}
