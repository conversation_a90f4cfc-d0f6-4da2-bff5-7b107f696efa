<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.newnary.gsp.center.tpsi.infra.repository.db.dao.DeliveryOrderEcCangApiAssociationDao">

<resultMap id="deliveryOrderEcCangApiAssociationPOResult" type="com.newnary.gsp.center.tpsi.infra.repository.db.po.DeliveryOrderEcCangApiAssociationPO">
    <result column="stockout_order_id" property="stockoutOrderId"/>
    <result column="erp_order_status" property="erpOrderStatus"/>
    <result column="erp_order_shipping_method_no" property="erpOrderShippingMethodNo"/>
    <result column="wms_volume_weight" property="wmsVolumeWeight"/>
    <result column="transport_order_id" property="transportOrderId"/>
    <result column="wms_freignt_order_status" property="wmsFreigntOrderStatus"/>
    <result column="wms_warehouse_id" property="wmsWarehouseId"/>
    <result column="wms_cbl_value_currency" property="wmsCblValueCurrency"/>
    <result column="delivery_order_id" property="deliveryOrderId"/>
    <result column="wms_order_label_code" property="wmsOrderLabelCode"/>
    <result column="erp_order_sale_order_code" property="erpOrderSaleOrderCode"/>
    <result column="wms_ft_name_cn" property="wmsFtNameCn"/>
    <result column="erp_tps_id" property="erpTpsId"/>
    <result column="wms_volume" property="wmsVolume"/>
    <result column="association_id" property="associationId"/>
    <result column="trade_order_id" property="tradeOrderId"/>
    <result column="wms_order_code" property="wmsOrderCode"/>
    <result column="wms_box_code" property="wmsBoxCode"/>
    <result column="wms_cbl_value" property="wmsCblValue"/>
    <result column="wms_tps_id" property="wmsTpsId"/>
    <result column="wms_weight" property="wmsWeight"/>
    <result column="order_ref_no" property="orderRefNo"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="id" property="id"/>
    <result column="gmt_create" property="gmtCreate"/>
    <result column="gmt_modified" property="gmtModified"/>
</resultMap>

<sql id="deliveryOrderEcCangApiAssociationPO_columns">
    stockout_order_id,
    erp_order_status,
    erp_order_shipping_method_no,
    wms_volume_weight,
    transport_order_id,
    wms_freignt_order_status,
    wms_warehouse_id,
    wms_cbl_value_currency,
    delivery_order_id,
    wms_order_label_code,
    erp_order_sale_order_code,
    wms_ft_name_cn,
    erp_tps_id,
    wms_volume,
    association_id,
    trade_order_id,
    wms_order_code,
    wms_box_code,
    wms_cbl_value,
    wms_tps_id,
    wms_weight,
    order_ref_no,
    tenant_id,
    id,
    gmt_create,
    gmt_modified
</sql>

<sql id="deliveryOrderEcCangApiAssociationPO_sqlForInsert">
    stockout_order_id,
    erp_order_status,
    erp_order_shipping_method_no,
    wms_volume_weight,
    transport_order_id,
    wms_freignt_order_status,
    wms_warehouse_id,
    wms_cbl_value_currency,
    delivery_order_id,
    wms_order_label_code,
    erp_order_sale_order_code,
    wms_ft_name_cn,
    erp_tps_id,
    wms_volume,
    association_id,
    trade_order_id,
    wms_order_code,
    wms_box_code,
    wms_cbl_value,
    wms_tps_id,
    wms_weight,
    order_ref_no,
    tenant_id,
    id,
    gmt_create,
    gmt_modified
</sql>

<sql id="deliveryOrderEcCangApiAssociationPO_columnsForInsert">
    #{stockoutOrderId},
    #{erpOrderStatus},
    #{erpOrderShippingMethodNo},
    #{wmsVolumeWeight},
    #{transportOrderId},
    #{wmsFreigntOrderStatus},
    #{wmsWarehouseId},
    #{wmsCblValueCurrency},
    #{deliveryOrderId},
    #{wmsOrderLabelCode},
    #{erpOrderSaleOrderCode},
    #{wmsFtNameCn},
    #{erpTpsId},
    #{wmsVolume},
    #{associationId},
    #{tradeOrderId},
    #{wmsOrderCode},
    #{wmsBoxCode},
    #{wmsCblValue},
    #{wmsTpsId},
    #{wmsWeight},
    #{orderRefNo},
    #{tenantId},
    #{id},
    REPLACE(unix_timestamp(NOW(3)),'.',''),
    REPLACE(unix_timestamp(NOW(3)),'.','')
</sql>

<sql id="deliveryOrderEcCangApiAssociationPO_setterForUpdate">
    <set>
        gmt_modified = REPLACE(unix_timestamp(NOW(3)),'.',''),
        stockout_order_id = #{stockoutOrderId},
        erp_order_status = #{erpOrderStatus},
        erp_order_shipping_method_no = #{erpOrderShippingMethodNo},
        wms_volume_weight = #{wmsVolumeWeight},
        transport_order_id = #{transportOrderId},
        wms_freignt_order_status = #{wmsFreigntOrderStatus},
        wms_warehouse_id = #{wmsWarehouseId},
        wms_cbl_value_currency = #{wmsCblValueCurrency},
        delivery_order_id = #{deliveryOrderId},
        wms_order_label_code = #{wmsOrderLabelCode},
        erp_order_sale_order_code = #{erpOrderSaleOrderCode},
        wms_ft_name_cn = #{wmsFtNameCn},
        erp_tps_id = #{erpTpsId},
        wms_volume = #{wmsVolume},
        association_id = #{associationId},
        trade_order_id = #{tradeOrderId},
        wms_order_code = #{wmsOrderCode},
        wms_box_code = #{wmsBoxCode},
        wms_cbl_value = #{wmsCblValue},
        wms_tps_id = #{wmsTpsId},
        wms_weight = #{wmsWeight},
        order_ref_no = #{orderRefNo},
    </set>
</sql>

<sql id="deliveryOrderEcCangApiAssociationPO_selector">
    select
    <include refid="deliveryOrderEcCangApiAssociationPO_columns"/>
    from delivery_order_eccang_api_association
</sql>

<sql id="deliveryOrderEcCangApiAssociationPO_query_segment">
    <trim prefix="WHERE" prefixOverrides="AND|OR">
        <if test="data.stockoutOrderId != null">
            AND stockout_order_id = #{data.stockoutOrderId}
        </if>
        <if test="data.erpOrderStatus != null">
            AND erp_order_status = #{data.erpOrderStatus}
        </if>
        <if test="data.erpOrderShippingMethodNo != null">
            AND erp_order_shipping_method_no = #{data.erpOrderShippingMethodNo}
        </if>
        <if test="data.wmsVolumeWeight != null">
            AND wms_volume_weight = #{data.wmsVolumeWeight}
        </if>
        <if test="data.transportOrderId != null">
            AND transport_order_id = #{data.transportOrderId}
        </if>
        <if test="data.wmsFreigntOrderStatus != null">
            AND wms_freignt_order_status = #{data.wmsFreigntOrderStatus}
        </if>
        <if test="data.wmsWarehouseId != null">
            AND wms_warehouse_id = #{data.wmsWarehouseId}
        </if>
        <if test="data.wmsCblValueCurrency != null">
            AND wms_cbl_value_currency = #{data.wmsCblValueCurrency}
        </if>
        <if test="data.deliveryOrderId != null">
            AND delivery_order_id = #{data.deliveryOrderId}
        </if>
        <if test="data.wmsOrderLabelCode != null">
            AND wms_order_label_code = #{data.wmsOrderLabelCode}
        </if>
        <if test="data.erpOrderSaleOrderCode != null">
            AND erp_order_sale_order_code = #{data.erpOrderSaleOrderCode}
        </if>
        <if test="data.wmsFtNameCn != null">
            AND wms_ft_name_cn = #{data.wmsFtNameCn}
        </if>
        <if test="data.erpTpsId != null">
            AND erp_tps_id = #{data.erpTpsId}
        </if>
        <if test="data.wmsVolume != null">
            AND wms_volume = #{data.wmsVolume}
        </if>
        <if test="data.associationId != null">
            AND association_id = #{data.associationId}
        </if>
        <if test="data.tradeOrderId != null">
            AND trade_order_id = #{data.tradeOrderId}
        </if>
        <if test="data.wmsOrderCode != null">
            AND wms_order_code = #{data.wmsOrderCode}
        </if>
        <if test="data.wmsBoxCode != null">
            AND wms_box_code = #{data.wmsBoxCode}
        </if>
        <if test="data.wmsCblValue != null">
            AND wms_cbl_value = #{data.wmsCblValue}
        </if>
        <if test="data.wmsTpsId != null">
            AND wms_tps_id = #{data.wmsTpsId}
        </if>
        <if test="data.wmsWeight != null">
            AND wms_weight = #{data.wmsWeight}
        </if>
        <if test="data.orderRefNo != null">
            AND order_ref_no = #{data.orderRefNo}
        </if>
        <if test="data.id != null">
            AND id = #{data.id}
        </if>
        <if test="data.gmtCreate != null">
            AND gmt_create = #{data.gmtCreate}
        </if>
        <if test="data.gmtModified != null">
            AND gmt_modified = #{data.gmtModified}
        </if>
        <if test="ins != null and ins.size() > 0">
            AND
            <foreach collection="ins" item="item" open="(" separator="and" close=")">
                ${item.column} in
                <foreach collection="item.values" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </foreach>
        </if>
        <if test="notIns != null and notIns.size() > 0">
            AND
            <foreach collection="notIns" item="item" open="(" separator="and" close=")">
                ${item.column} not in
                <foreach collection="item.values" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </foreach>
        </if>
        <if test="equals != null and equals.size() > 0">
            AND
            <foreach collection="equals" item="item" open="(" separator="and" close=")">
                ${item.column} = #{item.value}
            </foreach>
        </if>
        <if test="notEquals != null and notEquals.size() > 0">
            AND
            <foreach collection="notEquals" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ <> ]]> #{item.value}
            </foreach>
        </if>
        <if test="lts != null and lts.size() > 0">
            AND
            <foreach collection="lts" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ < ]]> #{item.value}
            </foreach>
        </if>
        <if test="gts != null and gts.size() > 0">
            AND
            <foreach collection="gts" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ > ]]> #{item.value}
            </foreach>
        </if>
        <if test="likes != null and likes.size() > 0">
            AND
            <foreach collection="likes" item="item" open="(" separator="and" close=")">
                ${item.column} like concat('%', #{item.value}, '%')
            </foreach>
        </if>
        <if test="ltes != null and ltes.size() > 0">
            AND
            <foreach collection="ltes" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ <= ]]> #{item.value}
            </foreach>
        </if>
        <if test="gtes != null and gtes.size() > 0">
            AND
            <foreach collection="gtes" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ >= ]]> #{item.value}
            </foreach>
        </if>
        <if test="ranges != null and ranges.size() > 0">
            AND
            <foreach collection="ranges" item="item" open="(" separator="and" close=")">
                ${item.column} <![CDATA[ >= ]]> #{item.from} and ${item.column} <![CDATA[ < ]]> #{item.to}
            </foreach>
        </if>
        AND tenant_id = #{tenantId}
    </trim>
</sql>


<insert id="insert" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.DeliveryOrderEcCangApiAssociationPO">
    insert into delivery_order_eccang_api_association
    (
        <include refid="deliveryOrderEcCangApiAssociationPO_sqlForInsert"/>
    )
    values
    (
        <include refid="deliveryOrderEcCangApiAssociationPO_columnsForInsert"/>
    )
</insert>

<update id="update" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.DeliveryOrderEcCangApiAssociationPO">
    update delivery_order_eccang_api_association
    <include refid="deliveryOrderEcCangApiAssociationPO_setterForUpdate" />
    where id = #{id}
    and tenant_id = #{tenantId}
</update>


<update id="updateByQuery" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.DeliveryOrderEcCangApiAssociationPO">
    update delivery_order_eccang_api_association
    <set>
        gmt_modified = REPLACE(unix_timestamp(NOW(3)),'.',''),
        stockout_order_id = #{update.stockoutOrderId},
        erp_order_status = #{update.erpOrderStatus},
        erp_order_shipping_method_no = #{update.erpOrderShippingMethodNo},
        wms_volume_weight = #{update.wmsVolumeWeight},
        transport_order_id = #{update.transportOrderId},
        wms_freignt_order_status = #{update.wmsFreigntOrderStatus},
        wms_warehouse_id = #{update.wmsWarehouseId},
        wms_cbl_value_currency = #{update.wmsCblValueCurrency},
        delivery_order_id = #{update.deliveryOrderId},
        wms_order_label_code = #{update.wmsOrderLabelCode},
        erp_order_sale_order_code = #{update.erpOrderSaleOrderCode},
        wms_ft_name_cn = #{update.wmsFtNameCn},
        erp_tps_id = #{update.erpTpsId},
        wms_volume = #{update.wmsVolume},
        association_id = #{update.associationId},
        trade_order_id = #{update.tradeOrderId},
        wms_order_code = #{update.wmsOrderCode},
        wms_box_code = #{update.wmsBoxCode},
        wms_cbl_value = #{update.wmsCblValue},
        wms_tps_id = #{update.wmsTpsId},
        wms_weight = #{update.wmsWeight},
        order_ref_no = #{update.orderRefNo},
    </set>
    <include refid="deliveryOrderEcCangApiAssociationPO_query_segment"/>
</update>

<!-- Delete -->
<delete id="delete">
    delete from delivery_order_eccang_api_association
    <include refid="deliveryOrderEcCangApiAssociationPO_query_segment"/>
</delete>
<delete id="deleteById">
    delete from delivery_order_eccang_api_association
    where id = #{id}
    and tenant_id = #{tenantId}
</delete>





<!-- query class -->
<sql id="deliveryOrderEcCangApiAssociationPO_groupBy">
    <if test="groupBys != null and groupBys.size() > 0">
        group by
        <foreach collection="groupBys" item="item" open="" separator="," close="">
            #{item}
        </foreach>
    </if>
</sql>

<sql id="deliveryOrderEcCangApiAssociationPO_orderby">
    <if test="orderBys != null and orderBys.size() > 0">
        order by
        <foreach collection="orderBys" item="item" open="" separator="," close="">
            ${item.column} ${item.direction}
        </foreach>
    </if>
</sql>


<select id="query" resultMap="deliveryOrderEcCangApiAssociationPOResult" parameterType="com.newnary.gsp.center.tpsi.infra.repository.db.po.DeliveryOrderEcCangApiAssociationPO">
    <include refid="deliveryOrderEcCangApiAssociationPO_selector"/>
    <include refid="deliveryOrderEcCangApiAssociationPO_query_segment"/>
    <include refid="deliveryOrderEcCangApiAssociationPO_groupBy"/>
    <include refid="deliveryOrderEcCangApiAssociationPO_orderby"/>
</select>



<select id="count" resultType="java.lang.Long">
    SELECT COUNT(1) FROM delivery_order_eccang_api_association
    <include refid="deliveryOrderEcCangApiAssociationPO_query_segment"/>
</select>

<select id="getById" resultMap="deliveryOrderEcCangApiAssociationPOResult">
    <include refid="deliveryOrderEcCangApiAssociationPO_selector"/>
    where id = #{id}
    and tenant_id = #{tenantId}
</select>

<select id="getByIdForUpdate" resultMap="deliveryOrderEcCangApiAssociationPOResult">
    <include refid="deliveryOrderEcCangApiAssociationPO_selector"/>
    where id = #{id}
    and tenant_id = #{tenantId}
    for update
</select>

</mapper>
