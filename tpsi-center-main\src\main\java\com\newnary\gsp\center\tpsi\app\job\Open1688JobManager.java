package com.newnary.gsp.center.tpsi.app.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.common.PageCondition;
import com.newnary.api.base.common.PageList;
import com.newnary.api.base.exception.BaseErrorInfo;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.common.utils.locale.CountryCode;
import com.newnary.common.utils.locale.LanguageLocaleType;
import com.newnary.distributed.tools.concurrent.template.DConcurrentTemplate;
import com.newnary.gsp.center.product.api.category.common.CategoryLevel;
import com.newnary.gsp.center.product.api.category.request.CategoryPageQueryCommand;
import com.newnary.gsp.center.product.api.category.response.CategoryInfo;
import com.newnary.gsp.center.product.api.category.response.CategoryNode;
import com.newnary.gsp.center.product.api.common.dto.MultimediaInfo;
import com.newnary.gsp.center.product.api.common.dto.MultimediaType;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierAdjustSupplyPriceReq;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierItemCreateCombineDetailInfo;
import com.newnary.gsp.center.product.api.open.request.OpenSupplierUpdateStockReq;
import com.newnary.gsp.center.product.api.product.enums.SupplierItemSaleMode;
import com.newnary.gsp.center.product.api.product.enums.SupplierSpuPlatformSourceType;
import com.newnary.gsp.center.product.api.product.request.*;
import com.newnary.gsp.center.product.api.product.vo.SupplierSpuDescInfo;
import com.newnary.gsp.center.product.api.product.vo.SupplierSpuParamInfo;
import com.newnary.gsp.center.product.api.product.vo.SupplierSpuParamValueInfo;
import com.newnary.gsp.center.product.api.product.vo.SupplierSpuParamsInfo;
import com.newnary.gsp.center.tpsi.infra.client.open1688.params.ClientBaseParams;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.request.QueryOpen1688JXHYProductRequest;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.request.QueryProductRequest;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.request.SearchByKeywordsRequest;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.response.*;
import com.newnary.gsp.center.tpsi.infra.client.vvic.params.VVICBaseParam;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingInfo;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingType;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.ThirdPartyMappingManager;
import com.newnary.gsp.center.tpsi.infra.rpc.CategoryRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.OpenSupplierProductRpc;
import com.newnary.gsp.center.tpsi.service.open1688.Open1688Service;
import com.newnary.job.core.biz.model.ReturnT;
import com.newnary.job.core.handler.annotation.Job;
import com.newnary.tenant.context.TenantCarrier;
import com.newnary.tenant.context.TenantID;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class Open1688JobManager {

    @Resource
    private OpenSupplierProductRpc openSupplierProductRpc;

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Resource
    private ThirdPartyMappingManager thirdPartyMappingManager;

    @Resource
    private CategoryRpc categoryRpc;

    @Resource
    private Open1688Service open1688ServiceImpl;

    private final ThreadPoolExecutor processExecutor = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors() * 20,
            Runtime.getRuntime().availableProcessors() * 20,
            20L, TimeUnit.SECONDS,
            new LinkedBlockingDeque<>(100),
            new ThreadPoolExecutor.CallerRunsPolicy());

    //private String supplierId = "VD5785344146823126061056";
    static String categoryId;

    @Job("autoCreateOpen1688Product")
    public ReturnT<String> createOpen1688Product(String param) {
        DConcurrentTemplate.tryLockMode(
                "OPEN1688:FETCH_PRODUCT",
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    log.info("1688分销商品同步定时任务开始, param={}", param);
                    JSONObject paramObject = JSON.parseObject(param);
                    String thirdPartySystemId = paramObject.getString("thirdPartySystemId");
                    //根据thirdPartySystemId获取第三方系统参数
                    ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);
                    CategoryPageQueryCommand categoryPageQueryCommand = new CategoryPageQueryCommand();
                    PageCondition pageCondition = new PageCondition();
                    int pageSize = Integer.parseInt(paramObject.getString("pageSize"));
                    int insertNum = Integer.parseInt(paramObject.getString("insertNum"));
                    pageCondition.setPageSize(pageSize);
                    categoryPageQueryCommand.setRefTemplate("1688_230509");
                    categoryPageQueryCommand.setIsLeaf(1);
                    categoryPageQueryCommand.setQueryAllChildrenNodes(true);
                    categoryPageQueryCommand.setPageCondition(pageCondition);
                    int pageNum = Integer.parseInt(paramObject.getString("pageNum"));
                    int perGroupCount = Integer.parseInt(paramObject.getString("perGroupCount"));
                    //int perGroupCount = 5;
                    int endPageNum = Integer.parseInt(paramObject.getString("endPageNum"));
                    categoryId = paramObject.getString("categoryId");
                    PageList<CategoryNode> categoryNodePageList = null;
                    int categoryIdx = 0;
                    try {
                        do {
                            pageCondition.setPageNum(pageNum += 1);
                            categoryNodePageList = categoryRpc.pageQuerySubCategory(categoryPageQueryCommand);
                            log.info("类目分页信息, pageNum={}, totalPage={}, PageMeta={}", pageCondition.getPageNum(), categoryNodePageList.getPageMeta().getPages(), JSON.toJSONString(categoryNodePageList.getPageMeta()));
                            List<CategoryNode> items = categoryNodePageList.getItems();

                            if (CollectionUtils.isNotEmpty(items)) {
                                //这里决定从哪个类目开始
                                if (categoryIdx == 0 && StringUtils.isNotBlank(categoryId)) {
                                    for (CategoryNode item : items) {
                                        if (categoryId.equals(item.getCategoryId())) {
                                            break;
                                        } else {
                                            categoryIdx++;
                                        }
                                    }
                                    items = items.subList(categoryIdx, items.size());
                                }
                                for (CategoryNode item : items) {
                                    categoryId = item.getCategoryId();
                                    SearchByKeywordsRequest searchByKeywordsRequest = new SearchByKeywordsRequest();
                                    searchByKeywordsRequest.setScenario("all");
                                    SearchByKeywordsRequest.Param queryParam = new SearchByKeywordsRequest.Param();
                                    queryParam.setCategoryIds(item.getCategoryId());
                                    SearchByKeywordsResponse.PageInfo pageInfo = null;
                                    long page = 0;
                                    queryParam.setPageSize("100");
                                    int visitTime = 0;
                                    do {
                                        queryParam.setPageNum(String.valueOf(page += 1));
                                        searchByKeywordsRequest.setParam(queryParam);
                                        SearchByKeywordsResponse response = open1688ServiceImpl.searchByKeywords(thirdPartySystem, searchByKeywordsRequest);
                                        if (response == null || response.getResult() == null) continue;
                                        pageInfo = response.getPageInfo();
                                        List<SearchByKeywordsResult> result = response.getResult();
                                        if (insertNum <= visitTime) {
                                            return;
                                        }
                                        if (CollectionUtils.isNotEmpty(result)) {
                                            try {
                                                processBatch(result, perGroupCount, thirdPartySystem);
                                            } catch (Exception e) {
                                                e.printStackTrace();
                                                log.info("[{}] 1688商品批量同步异常：e={}", "", e);
                                            }
                                            visitTime += result.size();
                                        }

                                    } while (pageInfo != null && pageInfo.getTotalPage() != 0 && page != pageInfo.getTotalPage());
                                }
                            }
                        } while (pageCondition.getPageNum() != categoryNodePageList.getPageMeta().getPages() && pageCondition.getPageNum() != endPageNum);
                        log.info("1688商品同步任务执行完毕 pageNum={},endPageNum={},pageSize:{},insetNum:{}", pageNum, endPageNum, pageSize, insertNum);
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.info("1688商品同步任务执行失败 {}  {} pageNum={} categoryId={}", e.getMessage(), e.toString(), pageNum, categoryId);
                    }

                }
        );
        return ReturnT.SUCCESS;
    }

    private void processBatch(List<SearchByKeywordsResult> result, Integer perGroupCount, ThirdPartySystem thirdPartySystem) throws Exception {

        final Optional<TenantID> tenantIdKept = TenantCarrier.getTenantID();
        // 1. 批量任务分组
        List<List<SearchByKeywordsResult>> groupSpuIds = ListUtils.partition(result, perGroupCount);
        CountDownLatch processCountDownLatch = new CountDownLatch(groupSpuIds.size());

        // 2. 线程池执行
        groupSpuIds.stream().forEach(groupItem -> {

            CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> {
                boolean tenantIdMiss = false;
                try {
                    if (!TenantCarrier.getTenantID().isPresent()) {
                        TenantID tenantID = tenantIdKept.orElse(null);
                        TenantCarrier.setTenantID(tenantID);
                        tenantIdMiss = tenantID != null;
                    }

                    //批量同步
                    processPerBatch(groupItem, thirdPartySystem);

                } catch (Exception e) {
                    log.error("[{}] 1688商品同步任务异常！e={}", "1688商品同步任务", e);
                } finally {
                    processCountDownLatch.countDown();
                    if (tenantIdMiss) {
                        TenantCarrier.clearTenantID();
                    }
                }
            }, processExecutor);

        });

        // 3. 等待批次商品同步处理完成
        processCountDownLatch.await();
    }

    private void processPerBatch(List<SearchByKeywordsResult> result, ThirdPartySystem thirdPartySystem) {
        result.forEach(searchByKeywordsResult -> {
            //判断商品是否存在
            ThirdPartyMappingInfo newestInfoByTarget = thirdPartyMappingManager.getNewestInfoByTarget("OPEN1688", "GSP", searchByKeywordsResult.getOfferId(), ThirdPartyMappingType.PRODUCT_ID);
            if (ObjectUtils.isNotEmpty(newestInfoByTarget)) return;
            QueryProductRequest queryProductRequest = new QueryProductRequest();
            queryProductRequest.setOfferId(searchByKeywordsResult.getOfferId());
            //查询商品明细
            QueryProductResponse queryProductResponse = open1688ServiceImpl.queryProductById(thirdPartySystem, queryProductRequest);
            if (null == queryProductResponse) return;
            if ("published".equals(queryProductResponse.getStatus())) {
                try {
                    //创建商品
                    String spuId = openSupplierProductRpc.createSpu4StockAsync(buildSupplierSpuCreateV2Command(thirdPartySystem, queryProductResponse));

                    if (StringUtils.isNotBlank(spuId)) {
                        //保存商品映射关系
                        thirdPartyMappingManager.insertOrUpdate("GSP", "OPEN1688", spuId, searchByKeywordsResult.getOfferId(), ThirdPartyMappingType.PRODUCT_ID.name(), queryProductResponse);
/*                                                            //更新商品价格
                                                            updatePrice(thirdPartySystem,queryProductResponse);
                                                            //更新商品库存
                                                            updateStock(thirdPartySystem, queryProductResponse);*/
                    }
                } catch (Exception e) {
                    log.info("1688分销商品创建失败 {}  {}", e.getMessage(), e.toString());
                }

            }
        });
    }


    public SupplierSpuCreateV2Command buildSupplierSpuCreateV2Command(ThirdPartySystem thirdPartySystem, QueryProductResponse queryProductResponse) {
        SupplierSpuCreateV2Command spuCreateV2Command = new SupplierSpuCreateV2Command();
        if (CollectionUtils.isEmpty(queryProductResponse.getProductSkuInfos()) || queryProductResponse.getProductSkuInfos().size() == 0) {
            throw new ServiceException(new BaseErrorInfo("OPEN_1688_CREATE_FAIL_BYPRODUCTS", "1688商品sku为空 offerId = ".concat(queryProductResponse.getProductID().toString())));
        }

        ClientBaseParams clientBaseParams = JSON.parseObject(thirdPartySystem.getParams(), ClientBaseParams.class);

        //供应商id
        spuCreateV2Command.setSupplierId(clientBaseParams.getSupplierId());

        //spu信息
        spuCreateV2Command.setDescInfos(buildSupplierSpuDescInfos(queryProductResponse));

        //默认语言
        spuCreateV2Command.setDefaultLocale(LanguageLocaleType.zh_CN);

        //商家商品id
        spuCreateV2Command.setCustomCode(String.valueOf(queryProductResponse.getProductID()));

        //品牌id
        //spuCreateV2Command.setBrandId(clientBaseParams.getBrandId());

        //类目id
        CategoryInfo categoryInfo = categoryRpc.getCategoryById(queryProductResponse.getCategoryID().toString());
        if (ObjectUtils.isEmpty(categoryInfo)) {
            throw new ServiceException(new BaseErrorInfo("OPEN_1688_CREATE_FAIL_BYCATEGORY", "1688类目不存在"));
        }
        spuCreateV2Command.setCategoryId(categoryInfo.getCategoryId());
        spuCreateV2Command.setMgmtCategoryLevel(CategoryLevel.getByValue(categoryInfo.getCategoryLevel()));

        //商品主图列表
        List<MultimediaInfo> mainImages = new ArrayList<>();
        QueryProductResponse.ProductImage productImage = queryProductResponse.getProductImage();
        if (null != productImage) {
            List<String> images = productImage.getImages();
            if (CollectionUtils.isNotEmpty(images)) {
                images.forEach(imageUrl -> {
                    MultimediaInfo mainImage = new MultimediaInfo();
                    mainImage.setFileUrl(imageUrl);
                    mainImage.setType(MultimediaType.IMAGE);
                    mainImages.add(mainImage);
                });
            }
        }
        spuCreateV2Command.setMainImages(mainImages);

        //视频
        String mainVedio = queryProductResponse.getMainVedio();
        if (null != mainVedio && !"".equals(mainVedio.trim())) {
            List<MultimediaInfo> videos = new ArrayList<>();
            MultimediaInfo video = new MultimediaInfo();
            video.setFileUrl(mainVedio);
            video.setType(MultimediaType.VIDEO);
            videos.add(video);
            spuCreateV2Command.setVideos(videos);
        }


        //原产国
        spuCreateV2Command.setCountryOfOriginCode(CountryCode.CN);

        //sku列表
        spuCreateV2Command.setSkuList(buildSkuList(queryProductResponse, clientBaseParams));

        //产品参数信息
        SupplierSpuParamsInfo supplierSpuParamsInfo = new SupplierSpuParamsInfo();
        List<QueryProductResponse.ProductAttribute> productAttributes = queryProductResponse.getProductAttribute();
        if (CollectionUtils.isNotEmpty(productAttributes)) {
            List<SupplierSpuParamInfo> customParams = new ArrayList<>();
            productAttributes.forEach(productAttribute -> {
                if ("规格".equals(productAttribute.getAttributeName()) || "包装规格".equals(productAttribute.getAttributeName()) || "品牌".equals(productAttribute.getAttributeName()) || "商品条形码".equals(productAttribute.getAttributeName())) {
                    return;
                }
                List<SupplierSpuParamInfo> collect = customParams.stream().filter(supplierSpuParamInfo -> productAttribute.getAttributeName().equals(supplierSpuParamInfo.getParamName())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    SupplierSpuParamInfo supplierSpuParamInfo = collect.get(0);
                    List<SupplierSpuParamValueInfo> values = supplierSpuParamInfo.getValues();
                    SupplierSpuParamValueInfo supplierSpuParamValueInfo = new SupplierSpuParamValueInfo();
                    supplierSpuParamValueInfo.setParamValue(productAttribute.getValue());
                    values.add(supplierSpuParamValueInfo);
                } else {
                    SupplierSpuParamInfo supplierSpuParamInfo = new SupplierSpuParamInfo();
                    supplierSpuParamInfo.setParamName(productAttribute.getAttributeName());
                    List<SupplierSpuParamValueInfo> values = new ArrayList<>();
                    SupplierSpuParamValueInfo supplierSpuParamValueInfo = new SupplierSpuParamValueInfo();
                    supplierSpuParamValueInfo.setParamValue(productAttribute.getValue());
                    values.add(supplierSpuParamValueInfo);
                    supplierSpuParamInfo.setValues(values);
                    customParams.add(supplierSpuParamInfo);
                }
            });
            supplierSpuParamsInfo.setCustomParams(customParams);
        }
        spuCreateV2Command.setParamsInfo(supplierSpuParamsInfo);

        //是否尝试创建
        spuCreateV2Command.setIsTryCreate(true);

        //是否自动审核通过
        spuCreateV2Command.setIsAutoAuditPass(true);

        //来源平台
        spuCreateV2Command.setPlatformSourceType(SupplierSpuPlatformSourceType.CN_1688_2);

        //操作人
        spuCreateV2Command.setOperator("tpsi");

        return spuCreateV2Command;
    }

    private List<SupplierSpuDescInfo> buildSupplierSpuDescInfos(QueryProductResponse queryProductResponse) {
        List<SupplierSpuDescInfo> list = new ArrayList<>();
        QueryProductResponse.ProductSaleInfo productSaleInfo = queryProductResponse.getProductSaleInfo();
        SupplierSpuDescInfo supplierSpuDescInfo = new SupplierSpuDescInfo();
        //商家spuid
        supplierSpuDescInfo.setSupplierSpuId(String.valueOf(queryProductResponse.getProductID()));

        //默认语言
        supplierSpuDescInfo.setLocale(LanguageLocaleType.zh_CN);

        //标题
        supplierSpuDescInfo.setTitle(queryProductResponse.getSubject());

        //计量单位
        supplierSpuDescInfo.setCustomMeasuringUnit(productSaleInfo.getUnit());

        //类目名称
        supplierSpuDescInfo.setCustomCategoryName(queryProductResponse.getCategoryName());

        //文本描述
        supplierSpuDescInfo.setTextDesc(queryProductResponse.getDescription());

        //翻译来源
        supplierSpuDescInfo.setTransSourceType("中文");

        //翻译语言
        supplierSpuDescInfo.setTransBaseLocate(LanguageLocaleType.zh_CN);

        //翻译提供方
        supplierSpuDescInfo.setTransProvider("open1688");
        list.add(supplierSpuDescInfo);
        return list;
    }

    private List<SupplierSkuCreateInfo> buildSkuList(QueryProductResponse queryProductResponse, ClientBaseParams clientBaseParams) {
        List<SupplierSkuCreateInfo> skuList = new ArrayList<>();
        List<QueryProductResponse.ProductSkuInfo> productSkuInfos = queryProductResponse.getProductSkuInfos();
        QueryProductResponse.ProductShippingInfo productShippingInfo = queryProductResponse.getProductShippingInfo();
        QueryProductResponse.ProductSaleInfo productSaleInfo = queryProductResponse.getProductSaleInfo();
        if (CollectionUtils.isNotEmpty(productSkuInfos)) {
            productSkuInfos.forEach(productSkuInfo -> {
                SupplierSkuCreateInfo skuCreateInfo = new SupplierSkuCreateInfo();

                //本商品规格唯一编码
                skuCreateInfo.setCustomCode(productSkuInfo.getSkuId().toString());

                //moq
                if (ObjectUtils.isNotEmpty(productSaleInfo)) {
                    skuCreateInfo.setMoq(productSaleInfo.getMinOrderQuantity());

                    //计量单位
                    skuCreateInfo.setMeasuringUnit(productSaleInfo.getUnit());
                } else {
                    skuCreateInfo.setMoq(1);
                }

/*                //最低零售价
                skuCreateInfo.setLowestRetailPrice(productSaleInfo.getPriceRanges().get(0).getPrice());

                //建议售价
                skuCreateInfo.setSuggestedRetailPrice(productSaleInfo.getPriceRanges().get(0).getPrice());*/

                if (ObjectUtils.isNotEmpty(productShippingInfo)) {
                    //备货周期
                    skuCreateInfo.setDeliveryDays(productShippingInfo.getHandlingTime());
                    //毛重
                    BigDecimal bigDecimal = new BigDecimal("100");
                    skuCreateInfo.setGrossWeight(productShippingInfo.getUnitWeight());
                    if (StringUtils.isNotBlank(productShippingInfo.getPackageSize())) {
                        String[] split = productShippingInfo.getPackageSize().split("x");
                        skuCreateInfo.setPackingLength(new BigDecimal(split[0]).divide(bigDecimal));
                        skuCreateInfo.setPackingWidth(new BigDecimal(split[1]).divide(bigDecimal));
                        skuCreateInfo.setPackingHeight(new BigDecimal(split[2]).divide(bigDecimal));
                    }
                    //净重
                    skuCreateInfo.setNetWeight(productShippingInfo.getSuttleWeight());
                    skuCreateInfo.setSizeLength(productShippingInfo.getLength() == null ? null : productShippingInfo.getLength().divide(bigDecimal));
                    skuCreateInfo.setSizeWidth(productShippingInfo.getWidth() == null ? null : productShippingInfo.getWidth().divide(bigDecimal));
                    skuCreateInfo.setSizeHeight(productShippingInfo.getHeight() == null ? null : productShippingInfo.getHeight().divide(bigDecimal));
                }

                //sku规格
                List<QueryProductResponse.Attribute> attributes = productSkuInfo.getAttributes();
                if (CollectionUtils.isNotEmpty(attributes)) {
                    //主规格
                    QueryProductResponse.Attribute mainAttribute = attributes.get(0);
                    SupplierSkuMainSpecInfo supplierSkuMainSpecInfo = new SupplierSkuMainSpecInfo();
                    MultimediaInfo mainImage = new MultimediaInfo();
                    mainImage.setFileUrl(mainAttribute.getSkuImageUrl());
                    mainImage.setType(MultimediaType.IMAGE);
                    supplierSkuMainSpecInfo.setImage(mainImage);
                    SupplierSkuSpecInfo mainSpec = new SupplierSkuSpecInfo();
                    mainSpec.setSpecName(mainAttribute.getAttributeName());
                    mainSpec.setSpecValue(mainAttribute.getAttributeValue());
                    mainSpec.setCustomSpecId(productSkuInfo.getSpecId());
                    supplierSkuMainSpecInfo.setSpec(mainSpec);
                    skuCreateInfo.setMainSpecInfo(supplierSkuMainSpecInfo);

                    //规格
                    List<SupplierSkuSpecInfo> specs = new ArrayList<>();
                    attributes.forEach(attribute -> {
                        SupplierSkuSpecInfo spec = new SupplierSkuSpecInfo();
                        spec.setSpecName(attribute.getAttributeName());
                        spec.setSpecValue(attribute.getAttributeValue());
                        specs.add(spec);
                    });
                    skuCreateInfo.setSpecs(specs);
                }
                skuCreateInfo.setRetailPriceCurrency("CNY");

                //扩展信息
                SupplierSkuCreateExtendInfo createExtendInfo = new SupplierSkuCreateExtendInfo();
                SupplierItemCreateOrUpdate4SpuCommand command = new SupplierItemCreateOrUpdate4SpuCommand();
                //command.setSupplierSkuId(productSkuInfo.getSkuId().toString());
                command.setCountry("CN");
                command.setWarehouseId(clientBaseParams.getWarehouseId());
                command.setSupplyPriceCurrency("CNY");
                command.setSupplyPrice(productSaleInfo.getPriceRanges().get(0).getPrice());
                List<SupplierItemCreateOrUpdate4SpuCommand> supplierItems = new ArrayList<>();
                supplierItems.add(command);
                createExtendInfo.setReferenceStockNum(productSkuInfo.getAmountOnSale());
                createExtendInfo.setSupplierItems(supplierItems);
                skuCreateInfo.setExtendInfo(createExtendInfo);
                skuList.add(skuCreateInfo);
            });
        }

        return skuList;
    }

    //更新商品价格1
    public void updatePrice(ThirdPartySystem thirdPartySystem, QueryProductResponse queryProductResponse) {
        ClientBaseParams clientBaseParams = JSON.parseObject(thirdPartySystem.getParams(), ClientBaseParams.class);
        //根据商品skuid查询衫海精商品明细
        List<QueryProductResponse.ProductSkuInfo> productSkuInfos = queryProductResponse.getProductSkuInfos();
        if (CollectionUtils.isNotEmpty(productSkuInfos)) {
            productSkuInfos.forEach(productSkuInfo -> {
                updatePrice(thirdPartySystem, productSkuInfo);
            });
        }
    }

    //更新商品价格2
    private void updatePrice(ThirdPartySystem thirdPartySystem, QueryProductResponse.ProductSkuInfo productSkuInfo) {
        OpenSupplierAdjustSupplyPriceReq req = new OpenSupplierAdjustSupplyPriceReq();
        VVICBaseParam vvicBaseParam = JSON.parseObject(thirdPartySystem.getParams(), VVICBaseParam.class);
        String supplierId = vvicBaseParam.getSupplierId();
        req.setSupplierId(supplierId);
        req.setCountry("CN");
        req.setSupplyPriceCurrency("CNY");
        req.setCustomSkuCode(productSkuInfo.getSkuId().toString());
        req.setSupplyPrice(productSkuInfo.getConsignPrice());
        //供货明细表
        List<OpenSupplierItemCreateCombineDetailInfo> detailInfos = new ArrayList<>();
        OpenSupplierItemCreateCombineDetailInfo info = new OpenSupplierItemCreateCombineDetailInfo();
        info.setCombineNum(1);
        info.setSupplyPriceCurrency("CNY");
        info.setSupplyPrice(productSkuInfo.getConsignPrice());
        detailInfos.add(info);
        req.setCombineDetails(detailInfos);
        try {
            openSupplierProductRpc.adjustSupplyPrice(req);
        } catch (ServiceException e) {
            log.error("更新商品价格：supplier: {}  supplierSkuId: {} message: {}", supplierId, productSkuInfo.getSkuId(), e.getMessage());
        }
    }

    public void updateStock(ThirdPartySystem thirdPartySystem, QueryProductResponse queryProductResponse) {
        List<QueryProductResponse.ProductSkuInfo> productSkuInfos = queryProductResponse.getProductSkuInfos();
        if (CollectionUtils.isNotEmpty(productSkuInfos)) {
            productSkuInfos.forEach(productSkuInfo -> {
                updateStock(thirdPartySystem, productSkuInfo.getSkuId().toString(), productSkuInfo.getAmountOnSale());
            });
        }
    }

    //更新库存
    private void updateStock(ThirdPartySystem thirdPartySystem, String skuId, int stock) {
        OpenSupplierUpdateStockReq req = new OpenSupplierUpdateStockReq();
        req.setBizSerialNumber(UUID.randomUUID().toString().replace("-", "").substring(0, 18));
        ClientBaseParams clientBaseParams = JSON.parseObject(thirdPartySystem.getParams(), ClientBaseParams.class);
        String supplierId = clientBaseParams.getSupplierId();
        req.setSupplierId(supplierId);
        req.setCustomWarehouseCode(clientBaseParams.getWarehouse());
        req.setStockNum(stock);
        req.setCustomSkuCode(skuId);
        try {
            openSupplierProductRpc.updateStock(req);
        } catch (ServiceException e) {
            log.error("更新库存失败：supplier: {}  supplierSkuId: {} message: {}", supplierId, skuId, e.getMessage());
        }
    }


    //1688精选货源商品同步
    @Job("autoCreateOpen1688JXHYProduct")
    public ReturnT<String> createOpen1688JXHYProduct(String param) {
        log.info("1688分销精选货源商品同步定时任务开始, param={}", param);
        JSONObject paramObject = JSON.parseObject(param);
        String thirdPartySystemId = paramObject.getString("thirdPartySystemId");

        //指定插入页数
        Integer insertPage = paramObject.getInteger("insertPage");

        //指定插入数量
        Integer insertSize = paramObject.getInteger("insertSize");
        //根据thirdPartySystemId获取第三方系统参数
        ThirdPartySystem thirdPartySystem = loadSystem(thirdPartySystemId);

        //构建类目参数
        CategoryPageQueryCommand categoryPageQueryCommand = new CategoryPageQueryCommand();
        PageCondition pageCondition = new PageCondition();
        int pageSize = Integer.parseInt(paramObject.getString("pageSize"));
        pageCondition.setPageSize(pageSize);
        categoryPageQueryCommand.setRefTemplate("1688_230509");
        categoryPageQueryCommand.setIsLeaf(1);
        categoryPageQueryCommand.setQueryAllChildrenNodes(true);
        categoryPageQueryCommand.setPageCondition(pageCondition);

        DConcurrentTemplate.tryLockMode(
                "OPEN1688:FETCH_JXHY_PRODUCT",
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    //从某个类目开始
                    categoryId = paramObject.getString("categoryId");
                    //从第几页类目插入
                    int pageNum = Integer.parseInt(paramObject.getString("pageNum"));

                    //多线程分组数量
                    int perGroupCount = Integer.parseInt(paramObject.getString("perGroupCount"));

                    //类目结束页数
                    int endPageNum = Integer.parseInt(paramObject.getString("endPageNum"));
                    PageList<CategoryNode> categoryNodePageList = null;
                    int categoryIdx = 0;
                    //当前商品是第几页
                    int currentProductPage;
                    //当前插入数量
                    int currentInsertSize = 0;
                    try {
                        do {
                            pageCondition.setPageNum(pageNum += 1);
                            categoryNodePageList = categoryRpc.pageQuerySubCategory(categoryPageQueryCommand);
                            log.info("类目分页信息, pageNum={}, totalPage={}, PageMeta={}", pageCondition.getPageNum(), categoryNodePageList.getPageMeta().getPages(), JSON.toJSONString(categoryNodePageList.getPageMeta()));
                            List<CategoryNode> items = categoryNodePageList.getItems();

                            if (CollectionUtils.isNotEmpty(items)) {
                                //这里决定从哪个类目开始
                                if (categoryIdx == 0 && StringUtils.isNotBlank(categoryId)) {
                                    for (CategoryNode item : items) {
                                        if (categoryId.equals(item.getCategoryId())) {
                                            break;
                                        } else {
                                            categoryIdx++;
                                        }
                                    }
                                    items = items.subList(categoryIdx, items.size());
                                }
                                //遍历类目
                                for (CategoryNode item : items) {

                                    //根据类查询商品信息
                                    QueryOpen1688JXHYProductRequest queryOpen1688JXHYProductRequest = new QueryOpen1688JXHYProductRequest();
                                    queryOpen1688JXHYProductRequest.setCategoryId(Long.valueOf(item.getCategoryId()));

                                    //分页总数
                                    int totalPage = 0;

                                    if (paramObject.getString("categoryId").equals(categoryId) && insertPage > 0) {
                                        currentProductPage = insertPage;
                                    } else {
                                        currentProductPage = 0;
                                    }

                                    do {
                                        queryOpen1688JXHYProductRequest.setPageNo(currentProductPage += 1);
                                        queryOpen1688JXHYProductRequest.setPageSize(50);
                                        QueryOpen1688JXHYProductResponse queryOpen1688JXHYProductResponse = open1688ServiceImpl.queryJXHYProduct(thirdPartySystem, queryOpen1688JXHYProductRequest);
                                        log.info("1688分销精选商品：categoryPage: {}, categoryId: {}, currentProductPage: {}", pageCondition.pageNum, categoryId, currentProductPage);
                                        if (queryOpen1688JXHYProductResponse != null && queryOpen1688JXHYProductResponse.getSuccess()) {
                                            QueryOpen1688JXHYProductResponse.Result result = queryOpen1688JXHYProductResponse.getResult();
                                            //判断数量
                                            if (result.getTotalRecords() != null && result.getTotalRecords() > 0) {
                                                totalPage = result.getTotalRecords() % result.getSizePerPage() == 0 ? result.getTotalRecords() / result.getSizePerPage() : result.getTotalRecords() / result.getSizePerPage() + 1;
                                                currentProductPage = result.getPageIndex();
                                            }
                                            List<QueryOpen1688JXHYProductResponse.Result.QueryResult> resultList = result.getResultList();
                                            if (CollectionUtils.isNotEmpty(resultList)) {
                                                currentInsertSize += resultList.size();
                                                processBatchJXHY(resultList,perGroupCount,thirdPartySystem);
                                                //查询商品详情
/*                                                List<Long> collect = resultList.stream().map(QueryOpen1688JXHYProductResponse.Result.QueryResult::getItemId).collect(Collectors.toList());
                                                QueryJXHYProductDetailResponse queryJXHYProductDetailResponse = open1688ServiceImpl.queryJXHYProductDetail(thirdPartySystem, collect);
                                                if (queryJXHYProductDetailResponse != null && (queryJXHYProductDetailResponse.getSuccess() != null && queryJXHYProductDetailResponse.getSuccess()) && CollectionUtils.isNotEmpty(queryJXHYProductDetailResponse.getResult())) {
                                                    List<QueryJXHYProductDetailResponse.Result> queryJXHYProductDetailResponseResult = queryJXHYProductDetailResponse.getResult();
                                                    processBatchJXHY(queryJXHYProductDetailResponseResult, perGroupCount, thirdPartySystem);
                                                }*/
                                            }
                                        }
                                    } while (totalPage > 0 && totalPage > currentProductPage && (insertSize == 0 || currentInsertSize < insertSize));

                                    if (insertSize != 0 && currentInsertSize >= insertSize) {
                                        break;
                                    }
                                    categoryId = item.getCategoryId();
                                }
                            }
                            if (insertSize != 0 && currentInsertSize >= insertSize) {
                                break;
                            }
                        } while (pageCondition.getPageNum() != categoryNodePageList.getPageMeta().getPages() && pageCondition.getPageNum() < categoryNodePageList.getPageMeta().getPages() && pageCondition.getPageNum() != endPageNum);
                        log.info("1688精选商品同步任务执行完毕");
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.info("1688精选商品同步任务执行失败 {}  {} pageNum={} categoryId={}", e.getMessage(), e.toString(), pageNum, categoryId);
                    }
                });
        return ReturnT.SUCCESS;
    }

    //使用多线程进行同步
    private void processBatchJXHY(List<QueryOpen1688JXHYProductResponse.Result.QueryResult> resultList, Integer perGroupCount, ThirdPartySystem thirdPartySystem) throws Exception {
        List<Long> ids = resultList.stream().map(QueryOpen1688JXHYProductResponse.Result.QueryResult::getItemId).collect(Collectors.toList());
        final Optional<TenantID> tenantIdKept = TenantCarrier.getTenantID();
        // 1. 批量任务分组
        List<List<Long>> groupSpuIds = ListUtils.partition(ids, perGroupCount);
        CountDownLatch processCountDownLatch = new CountDownLatch(groupSpuIds.size());

        // 2. 线程池执行
        groupSpuIds.stream().forEach(groupItem -> {

            CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> {
                boolean tenantIdMiss = false;
                try {
                    if (!TenantCarrier.getTenantID().isPresent()) {
                        TenantID tenantID = tenantIdKept.orElse(null);
                        TenantCarrier.setTenantID(tenantID);
                        tenantIdMiss = tenantID != null;
                    }
                    QueryJXHYProductDetailResponse queryJXHYProductDetailResponse = open1688ServiceImpl.queryJXHYProductDetail(thirdPartySystem, groupItem);
                    if (queryJXHYProductDetailResponse != null && (queryJXHYProductDetailResponse.getSuccess() != null && queryJXHYProductDetailResponse.getSuccess()) && CollectionUtils.isNotEmpty(queryJXHYProductDetailResponse.getResult())) {
                        List<QueryJXHYProductDetailResponse.Result> queryJXHYProductDetailResponseResult = queryJXHYProductDetailResponse.getResult();
                        //批量同步
                        processPerBatchJXHY(queryJXHYProductDetailResponseResult, thirdPartySystem);
                    }


                } catch (Exception e) {
                    log.error("[{}] 1688商品同步任务异常！e={}", "1688商品同步任务", e);
                } finally {
                    processCountDownLatch.countDown();
                    if (tenantIdMiss) {
                        TenantCarrier.clearTenantID();
                    }
                }
            }, processExecutor);

        });

        // 3. 等待批次商品同步处理完成
        processCountDownLatch.await();
    }
    //使用多线程进行同步
/*    private void processBatchJXHY(List<QueryJXHYProductDetailResponse.Result> result, Integer perGroupCount,ThirdPartySystem thirdPartySystem) throws Exception{

        final Optional<TenantID> tenantIdKept = TenantCarrier.getTenantID();
        // 1. 批量任务分组
        List<List<QueryJXHYProductDetailResponse.Result>> groupSpuIds = ListUtils.partition(result, perGroupCount);
        CountDownLatch processCountDownLatch = new CountDownLatch(groupSpuIds.size());

        // 2. 线程池执行
        groupSpuIds.stream().forEach(groupItem -> {

            CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> {
                boolean tenantIdMiss = false;
                try {
                    if (!TenantCarrier.getTenantID().isPresent()) {
                        TenantID tenantID = tenantIdKept.orElse(null);
                        TenantCarrier.setTenantID(tenantID);
                        tenantIdMiss = tenantID != null;
                    }

                    //批量同步
                    processPerBatchJXHY(groupItem,thirdPartySystem);

                } catch (Exception e) {
                    log.error("[{}] 1688商品同步任务异常！e={}", "1688商品同步任务", e);
                } finally {
                    processCountDownLatch.countDown();
                    if (tenantIdMiss) {
                        TenantCarrier.clearTenantID();
                    }
                }
            }, processExecutor);

        });

        // 3. 等待批次商品同步处理完成
        processCountDownLatch.await();
    }*/

    private void processPerBatchJXHY(List<QueryJXHYProductDetailResponse.Result> resultList, ThirdPartySystem thirdPartySystem) {
        resultList.forEach(result -> {
            //判断商品是否存在
            ThirdPartyMappingInfo newestInfoByTarget = thirdPartyMappingManager.getNewestInfoByTarget("OPEN1688", "GSP", String.valueOf(result.getProductInfo().getProductID()), ThirdPartyMappingType.PRODUCT_ID);
            if (ObjectUtils.isNotEmpty(newestInfoByTarget)) return;

            if (result.getProductInfo() != null && "published".equals(result.getProductInfo().getStatus())) {
                try {
                    //创建商品
                    String spuId = openSupplierProductRpc.createSpu4StockAsync(buildJXHYSupplierSpuCreateV2Command(thirdPartySystem, result));

                    if (StringUtils.isNotBlank(spuId)) {
                        //保存商品映射关系
                        thirdPartyMappingManager.insertOrUpdate("GSP", "OPEN1688", spuId, String.valueOf(result.getProductInfo().getProductID()), ThirdPartyMappingType.PRODUCT_ID.name(), result);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.info("1688分销商品创建失败 {}  {} 商品id：{}", e.getMessage(), e.toString(),result.getProductInfo().getProductID());
                }

            }
        });
    }

    public SupplierSpuCreateV2Command buildJXHYSupplierSpuCreateV2Command(ThirdPartySystem thirdPartySystem, QueryJXHYProductDetailResponse.Result result) {
        SupplierSpuCreateV2Command spuCreateV2Command = new SupplierSpuCreateV2Command();
        if (CollectionUtils.isEmpty(result.getProductInfo().getSkuInfos()) || result.getProductInfo().getSkuInfos().size() == 0) {
            throw new ServiceException(new BaseErrorInfo("OPEN_1688_CREATE_FAIL_BYPRODUCTS", "1688精选商品sku为空 offerId = ".concat(result.getProductInfo().getProductID().toString())));
        }

        ClientBaseParams clientBaseParams = JSON.parseObject(thirdPartySystem.getParams(), ClientBaseParams.class);

        QueryJXHYProductDetailResponse.Result.ProductInfo productInfo = result.getProductInfo();

        //供应商id
        spuCreateV2Command.setSupplierId(clientBaseParams.getSupplierId());

        //spu描述信息
        spuCreateV2Command.setDescInfos(buildSpuInfo(productInfo));

        //默认语言
        spuCreateV2Command.setDefaultLocale(LanguageLocaleType.zh_CN);

        //供应商SPU自定义编码
        spuCreateV2Command.setCustomCode(String.valueOf(productInfo.getProductID()));

        //类目id
        CategoryInfo categoryInfo = categoryRpc.getCategoryById(productInfo.getCategoryID().toString());
        if (ObjectUtils.isEmpty(categoryInfo)) {
            throw new ServiceException(new BaseErrorInfo("OPEN_1688_CREATE_FAIL_BYCATEGORY", "1688类目不存在"));
        }
        spuCreateV2Command.setCategoryId(categoryInfo.getCategoryId());

        //主图信息
        QueryJXHYProductDetailResponse.Result.ProductInfo.Image image = productInfo.getImage();
        if (image != null) {
            String headUrl = "https://cbu01.alicdn.com/";
            List<String> images = image.getImages();
            if (CollectionUtils.isNotEmpty(images)) {
                List<MultimediaInfo> multimediaInfos = new ArrayList<>();
                for (String imageUrl : images) {
                    MultimediaInfo multimediaInfo = new MultimediaInfo();
                    multimediaInfo.setType(MultimediaType.IMAGE);
                    multimediaInfo.setFileUrl(headUrl.concat(imageUrl));
                    multimediaInfos.add(multimediaInfo);
                }
                spuCreateV2Command.setMainImages(multimediaInfos);
            }
        }

        //视频
        String video = productInfo.getDetailVedio();
        if (StringUtils.isNotBlank(video)) {
            List<MultimediaInfo> multimediaInfos = new ArrayList<>();
            MultimediaInfo multimediaInfo = new MultimediaInfo();
            multimediaInfo.setType(MultimediaType.VIDEO);
            multimediaInfo.setFileUrl(video);
            multimediaInfos.add(multimediaInfo);
            spuCreateV2Command.setVideos(multimediaInfos);
        }

        //原产国
        spuCreateV2Command.setCountryOfOriginCode(CountryCode.CN);

        //sku列表
        spuCreateV2Command.setSkuList(buildSkuInfos(clientBaseParams, productInfo));

        //产品参数
        SupplierSpuParamsInfo paramsInfo = new SupplierSpuParamsInfo();
        List<QueryJXHYProductDetailResponse.Result.ProductInfo.ProductAttribute> attributes = productInfo.getAttributes();
        if (CollectionUtils.isNotEmpty(attributes)) {
            List<SupplierSpuParamInfo> customParams = new ArrayList<>();
            attributes.forEach(productAttribute -> {
                if ("规格".equals(productAttribute.getAttributeName()) || "包装规格".equals(productAttribute.getAttributeName()) || "品牌".equals(productAttribute.getAttributeName()) || "商品条形码".equals(productAttribute.getAttributeName())) {
                    return;
                }
                List<SupplierSpuParamInfo> collect = customParams.stream().filter(supplierSpuParamInfo -> productAttribute.getAttributeName().equals(supplierSpuParamInfo.getParamName())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    SupplierSpuParamInfo supplierSpuParamInfo = collect.get(0);
                    List<SupplierSpuParamValueInfo> values = supplierSpuParamInfo.getValues();
                    SupplierSpuParamValueInfo supplierSpuParamValueInfo = new SupplierSpuParamValueInfo();
                    supplierSpuParamValueInfo.setParamValue(productAttribute.getValue());
                    values.add(supplierSpuParamValueInfo);
                } else {
                    SupplierSpuParamInfo supplierSpuParamInfo = new SupplierSpuParamInfo();
                    supplierSpuParamInfo.setParamName(productAttribute.getAttributeName());
                    List<SupplierSpuParamValueInfo> values = new ArrayList<>();
                    SupplierSpuParamValueInfo supplierSpuParamValueInfo = new SupplierSpuParamValueInfo();
                    supplierSpuParamValueInfo.setParamValue(productAttribute.getValue());
                    values.add(supplierSpuParamValueInfo);
                    supplierSpuParamInfo.setValues(values);
                    customParams.add(supplierSpuParamInfo);
                }
            });
            paramsInfo.setCustomParams(customParams);
        }
        spuCreateV2Command.setParamsInfo(paramsInfo);

        //是否尝试创建
        spuCreateV2Command.setIsTryCreate(true);

        //是否自动审核通过
        spuCreateV2Command.setIsAutoAuditPass(true);

        //来源平台
        spuCreateV2Command.setPlatformSourceType(SupplierSpuPlatformSourceType.CN_1688_3);

        //操作人
        spuCreateV2Command.setOperator("tpsi");

        // 设置标签
        List<String> tags = new ArrayList<>();
        tags.add("1688精选货源");
        spuCreateV2Command.setTags(tags);

        return spuCreateV2Command;
    }

    //spu描述信息
    private List<SupplierSpuDescInfo> buildSpuInfo(QueryJXHYProductDetailResponse.Result.ProductInfo productInfo) {
        List<SupplierSpuDescInfo> supplierSpuDescInfos = new ArrayList<>();
        SupplierSpuDescInfo supplierSpuDescInfo = new SupplierSpuDescInfo();

        //supplierSpuDescInfo.setSupplierSpuId(String.valueOf(productInfo.getProductID()));
        supplierSpuDescInfo.setLocale(LanguageLocaleType.zh_CN);
        supplierSpuDescInfo.setTitle(productInfo.getSubject());
        supplierSpuDescInfo.setCustomMeasuringUnit(productInfo.getSaleInfo().getSellunit());
        supplierSpuDescInfo.setCustomCategoryName(productInfo.getCategoryName());
        supplierSpuDescInfo.setTextDesc(productInfo.getDescription());
        supplierSpuDescInfo.setTransSourceType("中文");
        supplierSpuDescInfo.setTransBaseLocate(LanguageLocaleType.zh_CN);
        supplierSpuDescInfo.setTransProvider("open1688");

        supplierSpuDescInfos.add(supplierSpuDescInfo);
        return supplierSpuDescInfos;
    }

    //sku列表
    private List<SupplierSkuCreateInfo> buildSkuInfos(ClientBaseParams clientBaseParams, QueryJXHYProductDetailResponse.Result.ProductInfo productInfo) {
        List<SupplierSkuCreateInfo> supplierSkuCreateInfos = new ArrayList<>();

        List<QueryJXHYProductDetailResponse.Result.ProductInfo.ProductSKUInfo> skuInfos = productInfo.getSkuInfos();
        QueryJXHYProductDetailResponse.Result.ProductInfo.ProductSaleInfo saleInfo = productInfo.getSaleInfo();
        QueryJXHYProductDetailResponse.Result.ProductInfo.ProductShippingInfo shippingInfo = productInfo.getShippingInfo();
        if (CollectionUtils.isNotEmpty(skuInfos)) {
            BigDecimal bigDecimal = new BigDecimal("100");
            for (QueryJXHYProductDetailResponse.Result.ProductInfo.ProductSKUInfo productSKUInfo : skuInfos) {
                SupplierSkuCreateInfo skuCreateInfo = new SupplierSkuCreateInfo();

                skuCreateInfo.setCustomCode(String.valueOf(productSKUInfo.getSkuId()));
                skuCreateInfo.setRefProductLink(productInfo.getProductLine());
                skuCreateInfo.setMoq(saleInfo.getMinOrderQuantity());
                if (shippingInfo != null){
                    skuCreateInfo.setLeadTime(shippingInfo.getHandlingTime());

                    //净重
                    skuCreateInfo.setNetWeight(shippingInfo.getOfferSuttleWeight());

                    //毛重
                    skuCreateInfo.setGrossWeight(shippingInfo.getUnitWeight());

                    //包装尺寸
                    if (shippingInfo.getOfferHeight() != null) {
                        skuCreateInfo.setPackingHeight(shippingInfo.getOfferHeight().divide(bigDecimal));
                    }

                    if (shippingInfo.getOfferWidth() != null) {
                        skuCreateInfo.setPackingWidth(shippingInfo.getOfferWidth().divide(bigDecimal));
                    }

                    if (shippingInfo.getOfferLength() != null) {
                        skuCreateInfo.setPackingLength(shippingInfo.getOfferLength().divide(bigDecimal));
                    }
                }

                skuCreateInfo.setDeliveryDays(saleInfo.getDeliveryLimit());

                //货币编码
                skuCreateInfo.setRetailPriceCurrency("CNY");

                //单位
                skuCreateInfo.setMeasuringUnit(saleInfo.getUnit());

                //规格
                List<QueryJXHYProductDetailResponse.Result.ProductInfo.ProductSKUInfo.SKUAttrInfo> attributes = productSKUInfo.getAttributes();
                if (CollectionUtils.isNotEmpty(attributes)) {
                    List<SupplierSkuSpecInfo> specs = new ArrayList<>();
                    for (QueryJXHYProductDetailResponse.Result.ProductInfo.ProductSKUInfo.SKUAttrInfo skuAttrInfo : attributes) {
                        if (skuCreateInfo.getMainSpecInfo() == null && StringUtils.isNotBlank(skuAttrInfo.getSkuImageUrl())) {
                            String headUrl = "https://cbu01.alicdn.com/";
                            //主规格
                            SupplierSkuMainSpecInfo mainSpecInfo = new SupplierSkuMainSpecInfo();
                            MultimediaInfo image = new MultimediaInfo();
                            image.setType(MultimediaType.IMAGE);
                            if (skuAttrInfo.getSkuImageUrl().contains("http") || skuAttrInfo.getSkuImageUrl().contains("https")) {
                                image.setFileUrl(skuAttrInfo.getSkuImageUrl());
                            } else {
                                image.setFileUrl(headUrl.concat(skuAttrInfo.getSkuImageUrl()));
                            }
                            mainSpecInfo.setImage(image);
                            SupplierSkuSpecInfo supplierSkuSpecInfo = new SupplierSkuSpecInfo();
                            supplierSkuSpecInfo.setSpecName(skuAttrInfo.getAttributeName());
                            supplierSkuSpecInfo.setSpecValue(skuAttrInfo.getAttributeValue());
                            mainSpecInfo.setSpec(supplierSkuSpecInfo);

                            skuCreateInfo.setMainSpecInfo(mainSpecInfo);
                            specs.add(supplierSkuSpecInfo);
                        } else {
                            SupplierSkuSpecInfo supplierSkuSpecInfo = new SupplierSkuSpecInfo();
                            supplierSkuSpecInfo.setSpecName(skuAttrInfo.getAttributeName());
                            supplierSkuSpecInfo.setSpecValue(skuAttrInfo.getAttributeValue());
                            specs.add(supplierSkuSpecInfo);
                        }

                    }
                    skuCreateInfo.setSpecs(specs);
                }

                //扩展信息，价格和库存
                SupplierSkuCreateExtendInfo extendInfo = new SupplierSkuCreateExtendInfo();
                //库存
                extendInfo.setReferenceStockNum(productSKUInfo.getAmountOnSale());

                //供货信息
                List<SupplierItemCreateOrUpdate4SpuCommand> supplierItems = new ArrayList<>();
                SupplierItemCreateOrUpdate4SpuCommand supplierItemCreateOrUpdate4SpuCommand = new SupplierItemCreateOrUpdate4SpuCommand();
                supplierItemCreateOrUpdate4SpuCommand.setSaleMode(SupplierItemSaleMode.COUNTRY);
                supplierItemCreateOrUpdate4SpuCommand.setCountry("CN");
                supplierItemCreateOrUpdate4SpuCommand.setWarehouseId(clientBaseParams.getWarehouseId());
                supplierItemCreateOrUpdate4SpuCommand.setSupplyPriceCurrency("CNY");

                //普通报价-FIXED_PRICE("0"),SKU规格报价-SKU_PRICE("1"),SKU区间报价（商品维度）-SKU_PRICE_RANGE_FOR_OFFER("2"),SKU区间报价（SKU维度）-SKU_PRICE_RANGE("3")，国际站无需关注
                if (null != saleInfo.getQuoteType()) {

                    switch (saleInfo.getQuoteType()) {
                        case 0:
                        case 2:
                            if (CollectionUtils.isNotEmpty(saleInfo.getPriceRanges())) {
                                supplierItemCreateOrUpdate4SpuCommand.setSupplyPrice(saleInfo.getPriceRanges().get(0).getPrice());
                            }
                            break;
                        case 1:
                            if (productSKUInfo.getPrice() != null) {
                                supplierItemCreateOrUpdate4SpuCommand.setSupplyPrice(productSKUInfo.getPrice());
                            }
                            break;
                        case 3:
                            if (CollectionUtils.isNotEmpty(productSKUInfo.getPriceRange())) {
                                supplierItemCreateOrUpdate4SpuCommand.setSupplyPrice(productSKUInfo.getPriceRange().get(0).getPrice());
                            }
                            break;
                        default:
                            throw new ServiceException(new BaseErrorInfo("SUPPLYPRICE_ISNULL", "商品报价方式不明确 offerId:".concat(productInfo.getProductID().toString())));
                    }
                }

                if (supplierItemCreateOrUpdate4SpuCommand.getSupplyPrice() == null) {
                    throw new ServiceException(new BaseErrorInfo("SUPPLYPRICE_ISNULL", "商品供货价为空 offerId:".concat(productInfo.getProductID().toString())));
                }

                supplierItems.add(supplierItemCreateOrUpdate4SpuCommand);
                extendInfo.setSupplierItems(supplierItems);
                skuCreateInfo.setExtendInfo(extendInfo);

                supplierSkuCreateInfos.add(skuCreateInfo);
            }
        }

        return supplierSkuCreateInfos;
    }

    public ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }
}
