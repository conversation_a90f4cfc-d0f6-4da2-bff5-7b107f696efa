package com.newnary.gsp.center.tpsi.infra.client.ninjavan;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.common.utils.httpmethod.ApiBaseResult;
import com.newnary.common.utils.httpmethod.HttpMethodUtil;
import com.newnary.distributed.tools.cache.DistributedCache;
import com.newnary.distributed.tools.concurrent.template.DConcurrentTemplate;
import com.newnary.gsp.center.tpsi.api.ninjavan.response.NinJavanDataApiBaseResult;
import com.newnary.gsp.center.tpsi.api.ninjavan.vo.CreateNinJavanOrderReturn;
import com.newnary.gsp.center.tpsi.infra.client.ninjavan.dto.NinJavanAccessTokenDTO;
import com.newnary.gsp.center.tpsi.infra.client.ninjavan.params.NinJavanParam;
import com.newnary.gsp.center.tpsi.infra.rpc.SpaceFileRpc;
import com.newnary.spring.cloud.extend.SpringContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Author: WangRUTao
 */
@Slf4j
public class NinJavanApiClient {

    private static final DistributedCache distributedCache = SpringContext.context().getBean(DistributedCache.class);

    private static final String CACHE_TOKEN_KEY = "ACCESS_TOKEN";

    private static final SpaceFileRpc spaceFileRpc = SpringContext.context().getBean(SpaceFileRpc.class);

    /*获取token*/
    private static String loadTokenMethod = "/global/2.0/oauth/access_token";
    /*创建订单*/
    private static String createOrderMethod = "/1.0/international-orders";
    /*取消订单*/
    private static String cancelOrderMethod = "/1.0/international-orders/cancel";
    /*生成面单*/
    private static String printSheetOrderMethod = "/1.0/international-waybills";


    private NinJavanParam param;

    /*用户代码*/
    private String serviceCode;

    /*apiUrl*/
    private String baseUrl;


    /*token*/
    private String clientId;
    private String clientSecret;
    private String grantType;


    public NinJavanApiClient(String ninJavanJsonParam) {
        NinJavanParam params = JSON.parseObject(ninJavanJsonParam, NinJavanParam.class);
        this.baseUrl = params.getBaseUrl();
        this.clientId = params.getClientId();
        this.clientSecret = params.getClientSecret();
        this.grantType = params.getGrantType();
        this.serviceCode = params.getServiceCode();
        this.param = params;

    }


    /**
     * @return redis key
     */
    private static String createCacheKey() {
        return "TRADE:NINJAVAN" + ":" + CACHE_TOKEN_KEY;
    }


    /**
     * token 获取
     *
     * @param apiParam
     * @return
     */
    private static NinJavanAccessTokenDTO getNewToken(NinJavanParam apiParam) {
        Map<String, Object> bodyParas = new HashMap<>();
        bodyParas.put("client_id", apiParam.getClientId());
        bodyParas.put("client_secret", apiParam.getClientSecret());
        bodyParas.put("grant_type", apiParam.getGrantType());
        System.out.println(JSONObject.toJSONString(bodyParas));

        try {
            ApiBaseResult apiBaseResult = HttpMethodUtil.syncPostMethod(apiParam.getBaseUrl().concat(loadTokenMethod),
                    3,
                    null,
                    "application/json",
                    null, null, bodyParas);

            System.out.println(JSONObject.toJSONString(apiBaseResult));
            log.info("[NinJavaN token]请求结束, code={}, message={}, result={}, dataParas={}", apiBaseResult.getCode(), apiBaseResult.getMessage(), apiBaseResult.getRet(),  JSONObject.toJSONString(bodyParas));
            NinJavanAccessTokenDTO ninJavanAccessTokenDTO = JSON.parseObject(apiBaseResult.getRet(), NinJavanAccessTokenDTO.class);
            return ninJavanAccessTokenDTO;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 能者物流创建订单接口调用请求
     *
     * @param command 请求体
     * @return
     */
    public NinJavanDataApiBaseResult<String> createOrder(String command) {

        Map<String, Object> bodyParas = getParam(command);
        bodyParas.put("service_code",param.getServiceCode());

        Map<String, String> header = new HashMap<String, String>();
        header.put("Authorization", getAccessToken(this.param));
//        header.put("Authorization","SC2y047ylkfOU0Xo4gcx5LvOiaMjnMsn");
        header.put("Content-Type", "application/json");

        ApiBaseResult apiBaseResult = null;
        try {
            log.info("[NinJavaN 创建订单]{} "+JSONObject.toJSONString(bodyParas));

            apiBaseResult = HttpMethodUtil.syncPostMethod(baseUrl.concat(createOrderMethod), 3, null, "application/json", header, null, bodyParas);

            NinJavanDataApiBaseResult<String> baseResult = buildNinJavanDataBaseResult(apiBaseResult);

            System.out.println("[NinJavaN 创建订单]{}"+baseResult);
            log.info("[NinJavaN 订单创建]请求结束, code={}, message={}, result={}, dataParas={}", baseResult.getCode(), baseResult.getMessage(), baseResult.getResult(),  JSONObject.toJSONString(bodyParas));
            return baseResult;
        } catch (Exception e) {
            e.printStackTrace();
            NinJavanDataApiBaseResult<String> ret = new NinJavanDataApiBaseResult<>();
            ret.setCode(0);
            ret.setMessage("异常:".concat(e.getMessage()));
        }
        return new NinJavanDataApiBaseResult<>();
    }


    private Map<String, Object> getParam(String command) {

        Map<String,Object> paramMap = JSONObject.parseObject(command, Map.class);
        paramMap.entrySet().removeIf(entry -> ObjectUtils.isEmpty(entry.getValue()));

        // 此处 return 关键字，不能定义在类中，此处进行转换
        Object orderReturn = paramMap.get("order_return");
        paramMap.put("return",orderReturn);
        paramMap.entrySet().removeIf(entry ->  "order_return".equals(entry.getKey()));
        return paramMap;
    }


    private NinJavanDataApiBaseResult<String> buildNinJavanDataBaseResult(ApiBaseResult resultStr) {
        NinJavanDataApiBaseResult<String> apiBaseResult = new NinJavanDataApiBaseResult<>();

        if (resultStr.getCode() != 201 && resultStr.getCode() != 200) {
            throw new RuntimeException("系统异常{}"+resultStr.getRet());
        }

        apiBaseResult.setCode(resultStr.getCode());
        apiBaseResult.setResult(resultStr.getRet());

        return apiBaseResult;
    }


    public String loadAccessToken() {
        return this.getAccessToken(this.param);
    }

    /**
     * 有效期 12 个小时
     */
    public String getAccessToken(NinJavanParam apiParam) {
        NinJavanAccessTokenDTO ninJavanAccessTokenDTO = distributedCache.pojoHolder(createCacheKey(), NinJavanAccessTokenDTO.class).get();

        if (ObjectUtils.allNotNull(ninJavanAccessTokenDTO) && !ninJavanAccessTokenDTO.hasExpired()) {
            return ninJavanAccessTokenDTO.getAccessToken();
        } else {
            return DConcurrentTemplate.tryLockMode(
                    "NINJAVAN_ACCESS_TOKEN_GENERATE_LOCK".concat(createCacheKey()),
                    t -> t.tryLock(1, TimeUnit.SECONDS),
                    () -> {
                        NinJavanAccessTokenDTO again = distributedCache.pojoHolder(createCacheKey(), NinJavanAccessTokenDTO.class).get();
                        if (ObjectUtils.allNotNull(again) && !again.hasExpired()) {
                            return again.getAccessToken();
                        }

                        NinJavanAccessTokenDTO newToken = getNewToken(apiParam);
                        newToken.setExpiresIn(newToken.getExpiresIn()-360);
                        System.out.println(JSONObject.toJSONString(newToken));
                        assert newToken != null;
                        distributedCache.pojoHolder(createCacheKey(), NinJavanAccessTokenDTO.class).set(newToken, newToken.getExpiresIn(), TimeUnit.SECONDS);
                        return newToken.getAccessToken();
                    });
        }
    }


    public NinJavanDataApiBaseResult<String> doCancel(String command) {
        Map<String, Object> bodyParas = getParam(command);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Authorization", getAccessToken(this.param));
        header.put("Content-Type", "application/json");

        ApiBaseResult apiBaseResult = null;
        try {

            System.out.println("[NinJavaN 订单取消]{} "+JSONObject.toJSONString(bodyParas));

            apiBaseResult = HttpMethodUtil.syncPostMethod(baseUrl.concat(cancelOrderMethod), 3, null, "application/json", header, null, bodyParas);

            NinJavanDataApiBaseResult<String> baseResult = buildNinJavanDataBaseResult(apiBaseResult);

            log.info("[NinJavaN 订单取消]请求结束, code={}, message={}, result={}, dataParas={}", baseResult.getCode(), baseResult.getMessage(), baseResult.getResult(),  JSONObject.toJSONString(bodyParas));
            System.out.println("[NinJavaN 订单取消]请求结束,{}"+apiBaseResult );
            return baseResult;
        } catch (Exception e) {
            e.printStackTrace();
            NinJavanDataApiBaseResult<String> ret = new NinJavanDataApiBaseResult<>();
            ret.setCode(0);
            ret.setMessage("异常:".concat(e.getMessage()));
        }
        return new NinJavanDataApiBaseResult<>();
    }

    public NinJavanDataApiBaseResult<String> printSheet(String command) {
        Map<String, Object> bodyParas = getParam(command);

        Map<String, String> header = new HashMap<String, String>();
        header.put("Authorization", getAccessToken(this.param));
        header.put("Content-Type", "application/json");

        ApiBaseResult apiBaseResult = null;
        try {

            System.out.println("[NinJavaN 生成面单]{} "+JSONObject.toJSONString(bodyParas));

            apiBaseResult = HttpMethodUtil.syncPostMethod(baseUrl.concat(printSheetOrderMethod), 3, null, "application/json", header, null, bodyParas);

            NinJavanDataApiBaseResult<String> baseResult = buildNinJavanDataBaseResult(apiBaseResult);

            log.info("[NinJavaN 生成面单]请求结束, code={}, message={}, result={}, dataParas={}", baseResult.getCode(), baseResult.getMessage(), baseResult.getResult(),  JSONObject.toJSONString(bodyParas));
            return baseResult;

        } catch (Exception e) {
            e.printStackTrace();
            NinJavanDataApiBaseResult<String> ret = new NinJavanDataApiBaseResult<>();
            ret.setCode(0);
            ret.setMessage("异常:".concat(e.getMessage()));
        }
        return null;
    }




}
