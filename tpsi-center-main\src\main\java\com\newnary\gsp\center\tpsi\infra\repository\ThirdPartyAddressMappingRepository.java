package com.newnary.gsp.center.tpsi.infra.repository;

import com.newnary.dao.base.converter.POConverterFactory;
import com.newnary.dao.base.helper.DaoHelper;
import com.newnary.dao.base.po.BaseQuery;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartyAddressMapping;
import com.newnary.gsp.center.tpsi.infra.model.vo.SystemId;
import com.newnary.gsp.center.tpsi.infra.repository.db.converter.ThirdPartyAddressMappingPOConverter;
import com.newnary.gsp.center.tpsi.infra.repository.db.dao.ThirdPartyAddressMappingDao;
import com.newnary.gsp.center.tpsi.infra.repository.db.po.ThirdPartyAddressMappingPO;
import com.newnary.gsp.center.tpsi.infra.translator.PO2ModelCreatorTranslator;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2021/12/14 14:20
 */
@Component
public class ThirdPartyAddressMappingRepository implements IThirdPartyAddressMappingRepository {

    @Resource
    private ThirdPartyAddressMappingDao addressMappingDao;

    @Override
    public void store(ThirdPartyAddressMapping addressMapping) {
        ThirdPartyAddressMappingPO po = POConverterFactory.find(ThirdPartyAddressMappingPOConverter.class)
                .convert2PO(addressMapping);

        if (po.id != null) {
            addressMappingDao.update(po);
        } else {
            addressMappingDao.insert(po);
        }
    }

    @Override
    public Optional<ThirdPartyAddressMapping> loadByBizId(SystemId systemId) {
        if (ObjectUtils.isEmpty(systemId)) {
            return Optional.empty();
        }

        BaseQuery<ThirdPartyAddressMappingPO> query = new BaseQuery<>(new ThirdPartyAddressMappingPO());
        query.getData().setSystemId(systemId.getId());

        return loadByQuery(query);
    }

    @Override
    public Optional<ThirdPartyAddressMapping> loadByVirtualPostcode(SystemId systemId,String virtualPostcode) {
        if (ObjectUtils.isEmpty(systemId)) {
            return Optional.empty();
        }
        BaseQuery<ThirdPartyAddressMappingPO> query = new BaseQuery<>(new ThirdPartyAddressMappingPO());
        query.getData().setSystemId(systemId.getId());
        query.getData().setVirtualPostcode(virtualPostcode);
        return loadByQuery(query);
    }

    private Optional<ThirdPartyAddressMapping> loadByQuery(BaseQuery<ThirdPartyAddressMappingPO> query) {
        List<ThirdPartyAddressMappingPO> pos = addressMappingDao.query(query);
        if (CollectionUtils.isNotEmpty(pos)) {
            return pos.stream()
                    .map(po -> ThirdPartyAddressMapping.loadWith(PO2ModelCreatorTranslator.transThirdPartyAddressMappingCreator(po)))
                    .findAny();
        }
        return Optional.empty();
    }

    public static void main(String[] args) {
        DaoHelper.genXMLWithFeature(
                "/Users/<USER>/Desktop/ronghework/tpsi-center/tpsi-center-main/src/main/java/com/newnary/gsp/center/tpsi/infra/repository/db/dao/ThirdPartyAddressMappingDao.xml",
                ThirdPartyAddressMappingDao.class,
                ThirdPartyAddressMappingPO.class,
                "third_party_address_mapping",
                false);
    }
}
