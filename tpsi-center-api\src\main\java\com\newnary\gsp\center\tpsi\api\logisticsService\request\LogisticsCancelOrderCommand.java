package com.newnary.gsp.center.tpsi.api.logisticsService.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 物流服务取消请求体
 *
 * <AUTHOR>
 * @since Created on 2023-11-17
 **/
@Data
public class LogisticsCancelOrderCommand {

    /**
     * 请求的运单号。
     **/
    private String transportOrderPackageId;

    /**
     * 推送单号。
     **/
    private String pushOrderId;

    /**
     * 物流返回的追踪号。
     **/
    @NotBlank(message = "物流追踪号不能为空")
    private String trackingId;

    /**
     * 取消原因
     **/
    @NotBlank(message = "取消原因(不能为空)")
    private String cancelReason;


    /**
     * 所属国家
     */
    private String country;



}
