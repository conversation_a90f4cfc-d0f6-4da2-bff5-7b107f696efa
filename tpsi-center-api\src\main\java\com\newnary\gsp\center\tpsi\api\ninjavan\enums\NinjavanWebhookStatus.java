package com.newnary.gsp.center.tpsi.api.ninjavan.enums;

import lombok.Getter;
import org.springframework.util.StringUtils;

/**
 * @Author: jack
 * @CreateTime: 2023-8-15
 */
@Getter
public enum NinjavanWebhookStatus {

    PendingPickup("Pending Pickup", "未揽件",
            "Order has been confirmed and is pending pickup", "订单确认并等待揽件"),
    EnRouteToSortingHub("En-route to Sorting Hub", "回仓途中/退回站点",
            "Order has been picked up and is en-route to Sorting Hub", "包裹已经揽件并回仓中"),
    PendingPickupAtDistributionPoint("Pending pickup at Distribution Point", "于分货点点等待取货",
            "Merchant had drop off the parcels at PUDO point and pending for pickup", "商人已经把包裹放在PUDO站，等待取货"),
    SuccessfulPickup("Successful Pickup", "成功揽件",
            "Proof of pickup is now ready", "揽件证明已生成"),
    ParcelWeight("Parcel Weight", "包裹重量",
            "The parcel weight of an Order has been changed", "订单包裹重量已更改"),
    ParcelMeasurementsUpdate("Parcel Measurements Update", "包裹体积更新",
            "The parcel size, or parcel weight, or parcel dimensions of an Order has been changed", "每个订单的包裹的大小，或重量，或尺寸已更改"),
    ParcelSize("Parcel Size", "包裹大小",
            "The parcel size of an Order has been changed", "每个订单的包裹大小已更改"),
    ArrivedAtSortingHub("Arrived at Sorting Hub", "抵达仓库/抵达站点",
            "Order has arrived at Sorting Hub and has been processed successfully", "订单已到达分拣中心并已成功处理"),
    OnVehicleForDelivery("On Vehicle for Delivery", "派送中",
            "Order is on van, en-route to delivery", "订单已经在派送途中"),
    PendingReschedule("Pending Reschedule", "排期再派",
            "Delivery has failed and the Order is pending re-schedule", "派送失败并等待排期再派"),
    ReturnToSenderTriggered("Return to Sender Triggered", "退仓被触发",
            "Delivery of Order has failed repeatedly, Return to Sender request has been triggered", "重复派送失败，退仓指令触发"),
    RTS("On Vehicle for Delivery (RTS)", "在退仓途中",
            "Order is on van, en-route to return to sender", "订单已在退仓途中"),
    ReturnedToSender("Returned to Sender", "已退仓",
            "Delivery of Order has failed repeatedly, Returned to Sender", "重复派送失败，已退仓"),
    ArrivedAtDistributionPoint("Arrived at Distribution Point", "已到达站点",
            "Delivery has been arrived at collection point", "派送已到达收集点"),
    SuccessfulDelivery("Successful Delivery", "派送成功",
            "Proof of delivery is now ready", "收件证明已生成"),
    Completed("Completed", "完成",
            "Delivery has been successfully completed", "派送成功完成"),
    Cancelled("Cancelled", "取消",
            "Order has been cancelled", "订单已取消"),
    ;

    private String name;

    private String nameCn;

    private String description;

    private String descriptionCn;

    NinjavanWebhookStatus(String name, String nameCn, String description, String descriptionCn) {
        this.name = name;
        this.nameCn = nameCn;
        this.description = description;
        this.descriptionCn = descriptionCn;
    }

    public static  NinjavanWebhookStatus getByName(String name) {
        if (!StringUtils.isEmpty(name)) {
            for (NinjavanWebhookStatus status : values()) {
                String webhookName = name.replaceAll(" ", "");
                String statusName = status.name.replaceAll(" ", "");
                if (statusName.equalsIgnoreCase(webhookName)) {
                    return status;
                }
            }
        }
        return null;
    }

}
