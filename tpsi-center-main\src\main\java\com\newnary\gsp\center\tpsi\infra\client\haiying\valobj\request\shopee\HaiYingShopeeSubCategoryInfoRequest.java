package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.shopee;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingShopeeSubCategoryInfoRequest {

    /**
     * 站点(默认:马来西亚站)
     */
    @NotNull(message = "站点不能为空")
    private String station;

    /**
     * 类目前30天销售件数起始值(int 型)
     */
    private String sold_start;

    /**
     * 类目前30天销售件数结束值(int 型)
     */
    private String sold_end;

    /**
     * 类目前30天销售金额起始值(double型)
     */
    private String payment_start;

    /**
     * 类目前30天销售金额结束值(double型)
     */
    private String payment_end;

    /**
     * 类目前30天出过单的商品总数起始值(int 型)
     * (已取消)
     */
    @Deprecated
    private String products_sold_num_start;

    /**
     * 类目前30天出过单的商品总数结束值(int 型)
     * (已取消)
     */
    @Deprecated
    private String products_sold_num_end;

    /**
     * 类目统计时间起始值(格式:年-月-日时:分:秒)
     */
    private String stat_time_start;

    /**
     * 类目统计时间结束值(格式:年-月-日时:分:秒)
     */
    private String stat_time_end;

    /**
     * 一级类目id
     */
    private String p_l1_id;

    /**
     * 二级类目id
     */
    private String p_l2_id;

    /**
     * 类目id
     */
    private String cid;

    /**
     * 当前页码(int 型)
     */
    private String current_page;

    /**
     * 每一页的数据量(默认海鹰设置 全部)(int 型)
     * 数值范围[1-10000]
     */
    private String page_size;

    /**
     * 排序方式:
     * products_sold_num(类目的前30天出过单的商品总数)(已取消)
     * payment(类目前30天销售金额)
     * sold_sub_top_percent(类目销售件数占一级类目销售件数百分比)
     * payment_sub_top_percent(类目销售额占一级类目销售额百分比)
     * sold(类目商前30天销售件数)
     * products_num_sub_top_percent(类目销售件数占一级类目销售件数百分比)
     * historical_sold_sub_top_percent(类目销售额占一级类目销售额百分比)
     * products_num(类目商品总数)
     * local_products_num(类目商品总数(本地))
     * overseas_products_num(目商品总数(海外))
     * historical_sold(类目总销售件数)
     * local_historical_sold(类目总销售件数(本地))
     * overseas_historical_sold(类目总销售件数(海外))
     * sold_sub_top_percent(类目前30天总销售件数占一级类目前30天总销售件数百分比)
     * sold(类目前30天总销售件数)
     * local_sold(类目前30天总销售件数(本地))
     * overseas_sold(类目前30天总销售件数(海外))
     * Payment(类目前30天总销售金额)
     * local_payment(类目前30天总销售金额(本地))
     * overseas_payment(类目前30天总销售金额(海外))
     */
    private String order_by;

    /**
     * 排序类型:
     * ASC 升序
     * DESC 降序
     */
    private String order_by_type;

}
