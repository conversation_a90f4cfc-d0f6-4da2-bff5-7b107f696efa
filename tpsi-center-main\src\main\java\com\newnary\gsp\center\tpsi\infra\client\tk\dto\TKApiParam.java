package com.newnary.gsp.center.tpsi.infra.client.tk.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TKApiParam {
    private String url;
    private String app_key;
    private String shop_code;
    private Long shop_id;
    private String app_secret;

    // token 相关
    private String access_token;
    private String auth_code;
    // "refresh_token"、"authorized_code"
    private String grant_type;
    private String refresh_token;
    private String use_new_token;
    private String token_url;

    // 临时映射
    private JSONObject brandMapping;
    private JSONObject categoryMapping;
    private String defaultWarehouseId;
    private JSONObject carrierCodeMapping;
    private String labelType;
    private String labelSize;
}
