package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.ebay;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingEbayProductListRequest {

    /**
     * 站点
     */
    @NotNull(message = "站点不能为空")
    private String station;

    /**
     * 商品id(string型)
     * (多个商品id用逗号分隔,单次最多500个商品id)
     */
    private String item_ids;

    /**
     * 商品标题(string型)
     */
    private String title;

    /**
     * 商品标题查询类型(int 型):
     * 1:整句语句(默认)
     * 2:多个搜索词,”与”关系
     * 3:多个搜索词,”或”关系
     */
    private String title_type;

    /**
     * 商品不包含标题(string型)
     */
    private String not_exist_title;

    /**
     * 商品不包含标题查询类型(int 型):
     * 1:整句语句(默认)
     * 2:多个搜索词,”与”关系
     * 3:多个搜索词,”或”关系
     */
    private String not_exist_title_type;

    /**
     * 商品item地点(string型)
     * (多个以英文逗号分隔)
     */
    private String item_location;

    /**
     * 卖家名称(string型)
     * (多个seller以逗号分隔)
     */
    private String seller;

    /**
     * 店铺名称(string型)
     */
    private String store;

    /**
     * 店铺注册地址(string型)
     * (多个以英文逗号分隔)
     */
    private String store_location;

    /**
     * 商品总销售件数起始值(int 型)
     */
    private String sold_start;

    /**
     * 商品总销售件数结束值(int 型)
     */
    private String sold_end;

    /**
     * 商品前1天销售件数起始值(int 型)
     */
    private String sold_the_previous_day_start;

    /**
     * 商品前1天销售件数结束值(int 型)
     */
    private String sold_the_previous_day_end;

    /**
     * 商品前1天销售金额起始值(double型)
     */
    private String payment_the_previous_day_start;

    /**
     * 商品前1天销售金额结束值(double型)
     */
    private String payment_the_previous_day_end;

    /**
     * 商品前3天销售件数起始值(int 型)
     */
    private String sales_three_day1_start;

    /**
     * 商品前3天销售件数结束值(int 型)
     */
    private String sales_three_day1_end;

    /**
     * 商品前3天销售金额起始值(double型)
     */
    private String payment_three_day1_start;

    /**
     * 商品前3天销售金额结束值(double型)
     */
    private String payment_three_day1_end;

    /**
     * 商品前3天销售增幅起始值(int 型)
     */
    private String sales_three_day_growth_start;

    /**
     * 商品前3天销售增幅结束值(int 型)
     */
    private String sales_three_day_growth_end;

    /**
     * 一级类目id(string型)
     */
    private String p_l1_id;

    /**
     * 子类类目id(string型)
     */
    private String sub_cate_id;

    /**
     * 商品上架时间起始值(string型,格式:年-月-日)
     */
    private String gen_time_start;

    /**
     * 商品上架时间结束值(string型,格式:年-月-日)
     */
    private String gen_time_end;

    /**
     * 商品价格起始值(double型)
     */
    private String price_start;

    /**
     * 商品价格结束值(double型)
     */
    private String price_end;

    /**
     * 收藏数起始值(int 型)
     */
    private String watchers_start;

    /**
     * 收藏数结束值(int 型)
     */
    private String watchers_end;

    /**
     * 商品前1天销售增幅起始值(int 型)
     */
    private String sold_the_previous_growth_start;

    /**
     * 商品前1天销售增幅结束值(int 型)
     */
    private String sold_the_previous_growth_end;

    /**
     * 商品总浏览数起始值(int 型)
     */
    private String visit_start;

    /**
     * 商品总浏览数结束值(int 型)
     */
    private String visit_end;

    /**
     * 前三天是否连续出单
     * (0: 否   1: 是   其它:全部)
     */
    private String sales_three_day_flag;

    /**
     * 商品前7天销售件数起始值(int 型)
     */
    private String sales_week1_start;

    /**
     * 商品前7天销售件数结束值(int 型)
     */
    private String sales_week1_end;

    /**
     * 商品前7天销售增幅起始值(int 型)
     */
    private String sales_week_growth_start;

    /**
     * 商品前7天销售增幅结束值(int 型)
     */
    private String sales_week_growth_end;

    /**
     * 商品Popular Item标识(int 型)
     * 默认:全部    1:有标识
     */
    private String popular_status;

    /**
     * 商品上架地区(string型)
     * (多个以英文逗号分隔)
     */
    private String marketplace;

    /**
     * 商品最新抓取时间起始值(string型,格式:年-月-日 时:分:秒)
     */
    private String last_modi_time_start;

    /**
     * 商品最新抓取时间结束值(string型,格式:年-月-日 时:分:秒)
     */
    private String last_modi_time_end;

    /**
     * 商品品牌
     */
    private String brand;

    /**
     * 排序方式:
     * price(商品价格)
     * sold(商品总销售件数)
     * sold_the_previous_day(商品前1天销售件数)
     * payment_the_previous_day(商品前1天销售金额)
     * sales_three_day1(商品前3天销售件数)
     * payment_three_day1(商品前3天销售金额)
     * sales_three_day_growth(商品前3天销售增幅)
     * gen_time(商品上架时间)
     * watchers(收藏数)
     * sold_the_previous_growth(商品前1天销售增幅)
     * visit(商品总浏览数)
     * sales_week1(商品前7天销售件数)
     * sales_week_growth(商品前7天销售增幅)
     */
    private String order_by;

    /**
     * 排序类型:
     * ASC 升序
     * DESC 降序
     */
    private String order_by_type;

    /**
     * 当前页码(int 型)
     */
    private String current_page;

    /**
     * 每一页的商品数(默认海鹰设置)(int 型)
     * 数值范围[1-1000]
     */
    private String page_size;

}
