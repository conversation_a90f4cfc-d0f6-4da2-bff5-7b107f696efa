package com.newnary.gsp.center.tpsi.ctrl.ejingling;


import com.alibaba.fastjson.JSON;
import com.newnary.api.base.common.CommonResponse;
import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.gsp.center.product.api.product.request.SupplierSpuCreateV2Command;
import com.newnary.gsp.center.product.api.product.request.SupplierSpuUpdateV2Command;
import com.newnary.gsp.center.tpsi.api.ejingling.OperateEjingLingProductApi;
import com.newnary.gsp.center.tpsi.app.job.EJingLingJobManager;
import com.newnary.gsp.center.tpsi.infra.client.ejingling.valobj.response.EJingLingGoodsListResponse;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingInfo;
import com.newnary.gsp.center.tpsi.infra.model.vo.ThirdPartyMappingType;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.infra.repository.db.manager.ThirdPartyMappingManager;
import com.newnary.gsp.center.tpsi.infra.rpc.OpenSupplierProductRpc;
import com.newnary.gsp.center.tpsi.infra.rpc.SupplierSkuRpc;
import com.newnary.gsp.center.tpsi.service.ejingling.IEJingLingApiSev;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@Slf4j
public class OperateEjingLingProductApiImpl implements OperateEjingLingProductApi {

    @Resource
    private IEJingLingApiSev eJingLingApiSevImpl;

    @Resource
    private SupplierSkuRpc supplierSkuRpc;

    @Resource
    private OpenSupplierProductRpc openSupplierProductRpc;

    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Resource
    private ThirdPartyMappingManager thirdPartyMappingManager;

    @Autowired
    private EJingLingJobManager eJingLingJobManager;

    @Value("${tpsi.bizIds}")
    private String bizIds;

    @Override
    public CommonResponse<String> updateProductInfo(@RequestBody List<String> customCodeList) {
        if (CollectionUtils.isNotEmpty(customCodeList)) {
            String ejingling = JSON.parseObject(bizIds).getString("EJINGLING");
            ThirdPartySystem thirdPartySystem = loadSystem(ejingling);
            List<List<String>> lists = eJingLingJobManager.groupListByQuantity(customCodeList, 20);
            lists.forEach(list -> {
                StringBuilder stringBuilder = new StringBuilder();
                list.forEach(spuId -> stringBuilder.append(spuId).append(","));
                String spuIds = stringBuilder.toString();
                try {
                    List<EJingLingGoodsListResponse.OuterGoodsVo> voList = eJingLingApiSevImpl.getGoodsByGoodsId(ejingling, spuIds);
                    if (CollectionUtils.isNotEmpty(voList)) {
                        Map<String, ThirdPartyMappingInfo> categoryMapping = thirdPartyMappingManager.batchGetIdMappingInfoByTargetIds("GSP", "EJINGLING", voList.stream().map(item -> String.valueOf(item.getPlatformCategoryId())).distinct().collect(Collectors.toList()), ThirdPartyMappingType.CATEGORY);
                        if (null == categoryMapping || categoryMapping.size() == 0) {
                            log.error("类目映射map为空，categoryMapping：{}", categoryMapping);
                            return;
                        }
                        voList.forEach(vo -> {
                            try {
                                ThirdPartyMappingInfo newestInfoByTarget = thirdPartyMappingManager.getNewestInfoByTarget("EJINGLING", "GSP", String.valueOf(vo.getGoodsId()), ThirdPartyMappingType.PRODUCT_ID);
                                SupplierSpuCreateV2Command spuCreateV2Command = eJingLingJobManager.buildCreateSpuReq(thirdPartySystem, categoryMapping, vo);
                                SupplierSpuUpdateV2Command supplierSpuUpdateV2Command = buildSupplierSpuUpdateV2Command(newestInfoByTarget.getSourceId(), spuCreateV2Command);
                                //TODO 进行商品更新

                                //更新供货价
                                eJingLingJobManager.updatePrice(thirdPartySystem,vo);
                                //更新库存
                                eJingLingJobManager.updateStock(thirdPartySystem,vo);
                            } catch (Exception e) {
                                log.info("衫海精商品sku更新失败：{}",JSON.toJSONString(vo));
                            }
                        });
                    }
                } catch (Exception e) {
                    log.info("衫海精商品更新失败：{}",spuIds);
                }

            });
            return CommonResponse.success("商品更新成功");
        }
        return CommonResponse.success("customCodeList 不能为空");
    }

    private SupplierSpuUpdateV2Command buildSupplierSpuUpdateV2Command(String supplierSpuId, SupplierSpuCreateV2Command spuCreateV2Command) {
        SupplierSpuUpdateV2Command supplierSpuUpdateV2Command = new SupplierSpuUpdateV2Command();
        supplierSpuUpdateV2Command.setSupplierSpuId(supplierSpuId);
        supplierSpuUpdateV2Command.setSupplierId(spuCreateV2Command.getSupplierId());
        supplierSpuUpdateV2Command.setDescInfos(spuCreateV2Command.getDescInfos());
        supplierSpuUpdateV2Command.setDefaultLocale(spuCreateV2Command.getDefaultLocale());
        supplierSpuUpdateV2Command.setCustomCode(spuCreateV2Command.getCustomCode());
        supplierSpuUpdateV2Command.setCustomBrandId(spuCreateV2Command.getCustomBrandId());
        supplierSpuUpdateV2Command.setCategoryId(spuCreateV2Command.getCategoryId());
        supplierSpuUpdateV2Command.setCustomCategoryId(spuCreateV2Command.getCustomCategoryId());
        supplierSpuUpdateV2Command.setMgmtCategoryLevel(spuCreateV2Command.getMgmtCategoryLevel());
        supplierSpuUpdateV2Command.setMgmtCategoryId(spuCreateV2Command.getMgmtCategoryId());
        supplierSpuUpdateV2Command.setMainImages(spuCreateV2Command.getMainImages());
        supplierSpuUpdateV2Command.setDetailImages(spuCreateV2Command.getDetailImages());
        supplierSpuUpdateV2Command.setVideos(spuCreateV2Command.getVideos());
        supplierSpuUpdateV2Command.setCountryOfOriginCode(spuCreateV2Command.getCountryOfOriginCode());
        supplierSpuUpdateV2Command.setLogisticsAttrInfo(spuCreateV2Command.getLogisticsAttrInfo());
        supplierSpuUpdateV2Command.setSkuList(spuCreateV2Command.getSkuList());
        supplierSpuUpdateV2Command.setParamsInfo(spuCreateV2Command.getParamsInfo());
        supplierSpuUpdateV2Command.setIsAutoAuditPass(spuCreateV2Command.getIsAutoAuditPass());
        supplierSpuUpdateV2Command.setIsCheckAttribute(spuCreateV2Command.getIsCheckAttribute());
        supplierSpuUpdateV2Command.setOperator(spuCreateV2Command.getOperator());

        return supplierSpuUpdateV2Command;
    }

    public ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }
}
