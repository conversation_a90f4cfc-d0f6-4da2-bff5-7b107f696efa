package com.newnary.gsp.center.tpsi.infra.client.ejingling.valobj.request;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class EJingLingFullGoodsListReq {
    /**
     * 数量，最大500
     */
    @NotNull(message = "每页数量不能为空")
    private Integer size;

    /**
     * 数量，最大500
     */
    @NotNull(message = "当前页数不能为空")
    private Integer page;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空")
    private String createdTime;
}
