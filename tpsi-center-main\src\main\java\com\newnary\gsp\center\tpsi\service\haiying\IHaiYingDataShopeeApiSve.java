package com.newnary.gsp.center.tpsi.service.haiying;

import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.HaiYingDataApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.shopee.*;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
public interface IHaiYingDataShopeeApiSve {

    HaiYingDataApiBaseResult<String> getKeywordList(HaiYingShopeeKeywordListRequest keywordListRequest);

    HaiYingDataApiBaseResult<String> getKeywordInfo(HaiYingShopeeKeywordInfoRequest keywordInfoRequest);

    HaiYingDataApiBaseResult<String> getProductList(HaiYingShopeeProductListRequest productsInfoRequest);

    HaiYingDataApiBaseResult<String> getProductDetailInfo(HaiYingShopeeProductDetailInfoRequest productDetailInfoRequest);

    HaiYingDataApiBaseResult<String> getProductExtInfo(HaiYingShopeeProductExtInfoRequest productExtInfoRequest);

    HaiYingDataApiBaseResult<String> getProductHistoryInfo(HaiYingShopeeProductHistoryInfoRequest productHistoryInfoRequest);

    HaiYingDataApiBaseResult<String> getCategoryTree(HaiYingShopeeCategoryTreeRequest categoryTreeRequest);

    HaiYingDataApiBaseResult<String> getTopCategoryInfo(HaiYingShopeeTopCategoryInfoRequest topCategoryInfoRequest);

    HaiYingDataApiBaseResult<String> getSubCategoryInfo(HaiYingShopeeSubCategoryInfoRequest subCategoryInfoRequest);


}
