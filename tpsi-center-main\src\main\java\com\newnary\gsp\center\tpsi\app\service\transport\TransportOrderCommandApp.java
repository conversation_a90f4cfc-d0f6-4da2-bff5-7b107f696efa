package com.newnary.gsp.center.tpsi.app.service.transport;

import com.newnary.api.base.exception.CommonErrorInfo;
import com.newnary.api.base.exception.ServiceException;
import com.newnary.distributed.tools.concurrent.template.DConcurrentTemplate;
import com.newnary.gsp.center.logistics.api.TransportOrderErrorInfo;
import com.newnary.gsp.center.logistics.api.delivery.dto.TrackInfoDTO;
import com.newnary.gsp.center.logistics.api.delivery.dto.TransportItemDTO;
import com.newnary.gsp.center.logistics.api.delivery.enums.TransportPushState;
import com.newnary.gsp.center.logistics.api.delivery.request.*;
import com.newnary.gsp.center.logistics.api.delivery.response.DeliveryOrderLiteInfo;
import com.newnary.gsp.center.logistics.api.delivery.response.TransportOrderPackageInfo;
import com.newnary.gsp.center.logistics.api.warehouse.response.ReceiptWarehouseRes;
import com.newnary.gsp.center.product.api.category.response.CategoryInfo;
import com.newnary.gsp.center.tpsi.api.logisticsService.request.LogisticsCancelOrderCommand;
import com.newnary.gsp.center.tpsi.api.logisticsService.request.LogisticsPrintOrderCommand;
import com.newnary.gsp.center.tpsi.api.logisticsService.response.LogisticsOrderResp;
import com.newnary.gsp.center.tpsi.api.logisticsService.response.LogisticsPrintSheetResp;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangApiBaseResult;
import com.newnary.gsp.center.tpsi.infra.model.DeliveryOrderEcCangApiAssociation;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.infra.model.vo.LogisticsServiceConstants;
import com.newnary.gsp.center.tpsi.infra.model.vo.LogisticsServiceContext;
import com.newnary.gsp.center.tpsi.infra.repository.IDeliveryOrderEcCangApiAssociationRepository;
import com.newnary.gsp.center.tpsi.infra.repository.IThirdPartySystemRepository;
import com.newnary.gsp.center.tpsi.infra.rpc.*;
import com.newnary.gsp.center.tpsi.service.ILogisticsApiSve;
import com.newnary.gsp.center.tpsi.service.eccang.IEccangTMSApiSve;
import com.newnary.gsp.center.tpsi.service.eccang.IEccangWMSApiSve;
import com.newnary.gsp.center.tpsi.service.mabang.IMaBangOrderApiSve;
import com.newnary.gsp.center.trade.api.order.response.OrderDTO;
import com.newnary.messagebody.gsp.logistics.mo.TransportOrderThirdPartyMO;
import com.newnary.spring.cloud.anno.Validation;
import com.newnary.spring.cloud.extend.SpringExtendLoader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * @Author: jack
 * @CreateTime: 2023/11/29
 */
@Slf4j
@Component
public class TransportOrderCommandApp {

    private static final String PREFIX = "TRANSPORT:";

    @Resource
    private WarehouseRpc warehouseRpc;
    @Resource
    private DeliveryOrderRpc deliveryOrderRpc;
    @Resource
    private TransportOrderRpc transportOrderRpc;
    @Resource
    private TradeOrderRpc tradeOrderRpc;
    @Resource
    private ExchangeRateRpc exchangeRateRpc;
    @Resource
    private CategoryRpc categoryRpc;

    @Resource
    private IDeliveryOrderEcCangApiAssociationRepository deliveryOrderEcCangApiAssociationRepository;
    @Resource
    private IThirdPartySystemRepository thirdPartySystemRepository;

    @Resource
    private IMaBangOrderApiSve maBangOrderApiSve;
    @Resource
    private IEccangTMSApiSve eccangTMSApiSve;
    @Resource
    private IEccangWMSApiSve eccangWMSApiSve;

    @Transactional
    @Validation
    public void thirdPush(String transportOrderPackageId, String thirdPartyCode) {
        DConcurrentTemplate.tryLockMode(
                PREFIX.concat(transportOrderPackageId),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    try {
                        //1. 反查
                        TransportOrderPackageInfo info = transportOrderRpc.loadTransportPackage(transportOrderPackageId);
                        //2.build push command
                        buildCreateOrderCommand(info);

                        ILogisticsApiSve logisticsApiSve = SpringExtendLoader.getExtensionLoader(ILogisticsApiSve.class).getExtension(thirdPartyCode);
                        logisticsApiSve.createOrder();
                        LogisticsOrderResp logisticsOrderResp = (LogisticsOrderResp) LogisticsServiceContext.getCurrentContext().get(LogisticsServiceConstants.CALL_CREATE_RESPONSE);
                        if (ObjectUtils.isNotEmpty(logisticsOrderResp)) {
                            //3.write 跟踪号
                            TransportOrderThirdPushedCommand request = new TransportOrderThirdPushedCommand();
                            request.setTransportOrderPackageId(transportOrderPackageId);
                            request.setThirdOrderId(logisticsOrderResp.getTrackingId());
                            request.setInsuredValue(logisticsOrderResp.getInsuredValue());
                            request.setPushOrderId(logisticsOrderResp.getRequestedTrackingId());
                            transportOrderRpc.thirdPushed(request);

                            //4.write TrackInfo
                            TransportOrderUpdateTrackCommand trackCommand = new TransportOrderUpdateTrackCommand();
                            trackCommand.setTransportOrderPackageId(transportOrderPackageId);
                            trackCommand.setTrackInfo(new TrackInfoDTO(thirdPartyCode, logisticsOrderResp.getTrackingId()));
                            transportOrderRpc.updateTrack(trackCommand);

                            //5.写入面单地址
                            LogisticsPrintOrderCommand printSheetReq = new LogisticsPrintOrderCommand();
                            printSheetReq.setTrackingId(logisticsOrderResp.getTrackingId());
                            LogisticsServiceContext.getCurrentContext().put(LogisticsServiceConstants.LOGISTICS_PRINT_DOMAIN, printSheetReq);
                            logisticsApiSve.printSheet();
                            LogisticsPrintSheetResp printSheetResp = (LogisticsPrintSheetResp) LogisticsServiceContext.getCurrentContext().get(LogisticsServiceConstants.CALL_PRINT_RESPONSE);
                            if (ObjectUtils.isNotEmpty(printSheetResp)) {
                                TransportOrderUpdateLabelCommand labelCommand = new TransportOrderUpdateLabelCommand();
                                labelCommand.setTransportOrderPackageId(transportOrderPackageId);
                                labelCommand.setLabelUrl(printSheetResp.getPdfFileUrl());
                                transportOrderRpc.updateLabel(labelCommand);
                            }
                        }
                    } catch (Exception e) {
                        log.error("推送能者物流「异常」", e);
                        //5。重置推送状态
                        transportOrderRpc.resetPushInfo(transportOrderPackageId, TransportPushState.PUSH_FAIL, "");
                    }
                }
        );
    }

    private void buildCreateOrderCommand(TransportOrderPackageInfo info) {
        if (CollectionUtils.isEmpty(info.getItemReferenceIds())) {
            throw new ServiceException(TransportOrderErrorInfo.ERROR_303_OPERATE_NOT_SUPPORT, "缺失发货信息");
        }
        DeliveryOrderLiteInfo deliveryOrderLiteInfo = deliveryOrderRpc.getLiteInfo(info.getItemReferenceIds().get(0));

        OrderDTO tradeOrder = tradeOrderRpc.getTradeOrder(deliveryOrderLiteInfo.getTradeOrderId());
        LogisticsServiceContext.getCurrentContext().put(LogisticsServiceConstants.LOGISTICS_TRADE_DOMAIN, tradeOrder);

        List<ReceiptWarehouseRes> warehouse = warehouseRpc.getReceiptWarehouseList("warehouse");
        ReceiptWarehouseRes receiptWarehouseRes = warehouse.stream().filter(warehouseRes -> deliveryOrderLiteInfo.getFreightWarehouse().equals(warehouseRes.getName())).findAny()
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "目的仓库不存在"));
        if (!Objects.equals("LOCAL", tradeOrder.getTransportType())) {
            throw new ServiceException(TransportOrderErrorInfo.ERROR_303_OPERATE_NOT_SUPPORT, "非本地货!!");
        }
        LogisticsServiceContext.getCurrentContext().put(LogisticsServiceConstants.LOGISTICS_WAREHOUSE_DOMAIN, receiptWarehouseRes);

//        TransportOrderDetailInfo transportOrderInfo = transportOrderRpc.getDetail(info.getTransportOrderId());
        LogisticsServiceContext.getCurrentContext().put(LogisticsServiceConstants.LOGISTICS_TRANSPORT_DOMAIN, info);

        Map<String, CategoryInfo> categoryMap = new ConcurrentHashMap<>();
        for (TransportItemDTO item : info.getItems()) {
            CategoryInfo categoryInfo = categoryRpc.getCategoryById(item.getCategoryId());
            categoryMap.put(item.getCategoryId(), categoryInfo);
        }
        LogisticsServiceContext.getCurrentContext().put(LogisticsServiceConstants.LOGISTICS_CATEGORY_DOMAIN, categoryMap);
    }

    @Transactional
    @Validation
    public void thirdPrintLabel(String transportOrderPackageId, String thirdOrderId, String thirdPartyCode) {
        DConcurrentTemplate.tryLockMode(
                PREFIX.concat(transportOrderPackageId),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    //1。 写入面单地址
                    LogisticsPrintOrderCommand command = new LogisticsPrintOrderCommand();
                    command.setTrackingId(thirdOrderId);

                    ILogisticsApiSve logisticsApiSve = SpringExtendLoader.getExtensionLoader(ILogisticsApiSve.class).getExtension(thirdPartyCode);
                    LogisticsServiceContext.getCurrentContext().put(LogisticsServiceConstants.LOGISTICS_PRINT_DOMAIN, command);
                    logisticsApiSve.printSheet();
                    LogisticsPrintSheetResp printSheetResp = (LogisticsPrintSheetResp) LogisticsServiceContext.getCurrentContext().get(LogisticsServiceConstants.CALL_PRINT_RESPONSE);
//                  NinJavanPrintSheetResp ninJavanPrintSheetResp = nanJavanApiSve.printSheet(printSheetReq);

                    //2。 updateLabel
                    if (ObjectUtils.isNotEmpty(printSheetResp)) {
                        TransportOrderUpdateLabelCommand labelCommand = new TransportOrderUpdateLabelCommand();
                        labelCommand.setTransportOrderPackageId(transportOrderPackageId);
                        labelCommand.setLabelUrl(printSheetResp.getPdfFileUrl());
                        transportOrderRpc.updateLabel(labelCommand);
                    }
                }
        );
    }

    @Transactional
    @Validation
    public void thirdCancel(TransportOrderThirdPartyMO mo) {
        DConcurrentTemplate.tryLockMode(
                PREFIX.concat(mo.getTransportOrderPackageId()),
                lock -> lock.tryLock(3, TimeUnit.SECONDS),
                () -> {
                    //1。取消能者订单
                    LogisticsCancelOrderCommand command = new LogisticsCancelOrderCommand();
                    command.setTrackingId(mo.getThirdOrderId());
                    command.setCancelReason(mo.getCancelReason());
                    command.setTransportOrderPackageId(mo.getTransportOrderPackageId());
                    command.setPushOrderId(mo.getPushOrderId());

                    TransportOrderPackageInfo transportPackageInfo = transportOrderRpc.loadTransportPackage(mo.getTransportOrderPackageId());
                    command.setCountry(transportPackageInfo.getConsignee().getAddrCountry());

                    ILogisticsApiSve logisticsApiSve = SpringExtendLoader.getExtensionLoader(ILogisticsApiSve.class).getExtension(mo.getThirdPartyCode());
                    LogisticsServiceContext.getCurrentContext().put(LogisticsServiceConstants.LOGISTICS_CANCEL_DOMAIN, command);

                    logisticsApiSve.doCancel();

                    //2。重置运输单推送状态
                    transportOrderRpc.resetPushInfo(mo.getTransportOrderPackageId(), TransportPushState.PUSH_CANCEL, mo.getCancelReason());
                }
        );
    }

    @Transactional
    //TODO 没把具体第三方抽出来
    public void transportOrderCancel(String referenceId, String transportOrderId, String cancelType) {
        //查询发货单
        DeliveryOrderLiteInfo deliveryOrderLiteInfo = deliveryOrderRpc.getLiteInfo(referenceId);
        //TODO 根据订单类型判断在哪个第三方系统执行取消操作，后续还需改进
        if (!deliveryOrderLiteInfo.isLocalShip) {
            //代采-易仓逻辑
            Optional<DeliveryOrderEcCangApiAssociation> deliveryOrderEcCangApiAssociation = deliveryOrderEcCangApiAssociationRepository.loadByTransportOrderId(transportOrderId);
            if (deliveryOrderEcCangApiAssociation.isPresent()) {
                DeliveryOrderEcCangApiAssociation ecCangApiAssociation = deliveryOrderEcCangApiAssociation.get();
                //判断是否还能取消
                if (ecCangApiAssociation.getWmsFreigntOrderStatus() != 6 && ecCangApiAssociation.getWmsFreigntOrderStatus() != 7) {
                    ThirdPartySystem thirdPartySystem = loadSystem(ecCangApiAssociation.getWmsTpsId());
                    EcCangApiBaseResult<String> freightCancelOrderApiBaseResult = eccangWMSApiSve.freightCancelOrder(ecCangApiAssociation, thirdPartySystem);
                    if (freightCancelOrderApiBaseResult.getCode().equals("200") && freightCancelOrderApiBaseResult.getMessage().equals("Success")) {
                        log.info("易仓wms集运订单取消成功");
                        TransportOrderConfirmCancelCommand transportOrderConfirmCancelCommand = new TransportOrderConfirmCancelCommand();
                        transportOrderConfirmCancelCommand.setTransportOrderId(transportOrderId);
                        transportOrderConfirmCancelCommand.setCancelType(cancelType);
                        transportOrderRpc.confirmCancel(transportOrderConfirmCancelCommand);
                    } else {
                        log.error("易仓wms集运订单取消失败");
                        TransportOrderRejectCancelCommand transportOrderRejectCancelCommand = new TransportOrderRejectCancelCommand();
                        transportOrderRejectCancelCommand.setTransportOrderId(transportOrderId);
                        transportOrderRpc.rejectCancel(transportOrderRejectCancelCommand);
                        log.info("调用驳回取消接口");
                    }
                } else {
                    log.warn("易仓wms集运订单已取消或者已完成");
                    TransportOrderRejectCancelCommand transportOrderRejectCancelCommand = new TransportOrderRejectCancelCommand();
                    transportOrderRejectCancelCommand.setTransportOrderId(transportOrderId);
                    transportOrderRpc.rejectCancel(transportOrderRejectCancelCommand);
                    log.info("调用驳回取消接口");
                }
            }
        } else {
            //TODO 分销-易仓TMS取消逻辑
        }
    }

    private ThirdPartySystem loadSystem(String systemBizId) {
        return thirdPartySystemRepository.loadByBizId(systemBizId)
                .orElseThrow(() -> new ServiceException(CommonErrorInfo.ERROR_102_REQUEST_VALID_ERROR, "系统业务id不存在"));
    }

}
