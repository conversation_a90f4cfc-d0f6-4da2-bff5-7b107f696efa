package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.request.shopee;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingShopeeProductExtInfoRequest {

    /**
     * 站点(默认:马来西亚站)
     */
    @NotNull(message = "站点不能为空")
    private String station;

    /**
     * 商品id
     * (多个id用逗号分隔，单次最多100个id)
     */
    @NotNull(message = "商品id不能为空")
    private String pids;

}
