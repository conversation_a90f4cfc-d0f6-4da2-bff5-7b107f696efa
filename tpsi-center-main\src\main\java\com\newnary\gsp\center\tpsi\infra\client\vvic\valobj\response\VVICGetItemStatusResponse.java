package com.newnary.gsp.center.tpsi.infra.client.vvic.valobj.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class VVICGetItemStatusResponse {
    /**
     * 返回结果的语言，en-英文，cn-中文
     */
    private String lang;

    /**
     * 商品缺货信息列表
     */
    private List<Item> item_list;

    @Getter
    @Setter
    public static class Item {
        /**
         * 商品vid
         */
        private String item_vid;

        /**
         * 商品id，后续不再提供，建议使用item_vid
         */
        private Integer item_id;

        /**
         * 商品是否缺货，0、2-不缺货，1-缺货
         */
        private Integer is_lack;
    }

}
