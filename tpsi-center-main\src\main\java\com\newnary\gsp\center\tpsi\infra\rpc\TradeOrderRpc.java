package com.newnary.gsp.center.tpsi.infra.rpc;

import com.newnary.gsp.center.trade.api.order.dto.OrderGoodsDTO;
import com.newnary.gsp.center.trade.api.order.dto.OrderSubItemDTO;
import com.newnary.gsp.center.trade.api.order.feign.OrderFeignApi;
import com.newnary.gsp.center.trade.api.order.request.command.*;
import com.newnary.gsp.center.trade.api.order.response.OrderDTO;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/1
 */
@Component
public class TradeOrderRpc {

    @Resource
    private OrderFeignApi orderFeignApi;

    public void updatePurchaseShippingAmount(String deliveryOrderId, String purchaseShippingCurrency, BigDecimal purchaseShippingFee) {
        OrderPackageUpdatePurchaseShippingFeeByDeliveryCommand command = new OrderPackageUpdatePurchaseShippingFeeByDeliveryCommand();
        command.deliveryOrderId = deliveryOrderId;
        command.purchaseShippingCurrency = purchaseShippingCurrency;
        command.purchaseShippingFee = purchaseShippingFee;
        orderFeignApi.updateOrderPackagePurchaseShippingFee(command);
    }

    public OrderDTO getTradeOrder(String tradeOrderId) {
        return orderFeignApi.render(tradeOrderId).mustSuccessOrThrowOriginal();
    }

    public String syncOrder(OrderCreateReq createReq) {
        return orderFeignApi.create(createReq).mustSuccessOrThrowOriginal();
    }

    public void cancelOrder(OrderCancelReq req) {
        if (ObjectUtils.isEmpty(req)) {
            return;
        }
        orderFeignApi.doCancel(req).mustSuccessOrThrowOriginal();
    }

//    public void orderCarrierReceive(String orderId, String trackNum, String operator) {
//        OrderCarrierReceiveReq req = new OrderCarrierReceiveReq();
//        req.setOrderId(orderId);
//        req.setTrackingNumber(trackNum);
//        req.setOperator(operator);
//        orderFeignApi.orderCarrierReceive(req);
//    }
//
//    public void orderCarrierDelivery(String orderId, String operator) {
//        OrderOperateReq req = new OrderOperateReq();
//        req.setOrderId(orderId);
//        req.setOperator(operator);
//        orderFeignApi.orderCarrierDelivery(req);
//    }

    public void orderCustomerReceive(String orderId, String currency, BigDecimal paidAmount, String operator) {
        OrderCustomerReceiveReq req = new OrderCustomerReceiveReq();
        req.setOrderId(orderId);
        req.setCurrency(currency);
        req.setPaidAmount(paidAmount);
        req.setOperator(operator);
        orderFeignApi.orderCustomerReceive(req);
    }


    /**
     * 包裹能者退回包裹->生成售后
     */
    public void packageBack(OrderDTO order,List<String> referenceIds) {
//        List<OrderCancelReqV2> reqV2List = new ArrayList<>();
//        List<OrderSubItemDTO> collect = order.orderSubItemList
//                .stream()
//                .filter(p -> referenceIds.contains(p.getDeliveryOrderId()))
//                .collect(Collectors.toList());
//
//        List<String> subItemId = collect
//                .stream()
//                .map(OrderSubItemDTO::getSubItemId)
//                .collect(Collectors.toList());
//
//        List<String> saleIdList = new ArrayList<>();
//        for (OrderSubItemDTO subItemDTO : collect) {
//            List<String> collect1 = subItemDTO.getOrderGoodsList()
//                    .stream()
//                    .map(OrderGoodsDTO::getSaleItemCode)
//                    .collect(Collectors.toList());
//            saleIdList.addAll(collect1);
//        }
//
//        OrderCancelReqV2 orderCancelReqV2 = new OrderCancelReqV2();
//        orderCancelReqV2.setOrderId(order.getOrderId());
//        orderCancelReqV2.setCancelType("CARRIER_CANCEL");
//        orderCancelReqV2.setCancelReason("NinjavaN包裹退回");
//        orderCancelReqV2.setSubItemId(subItemId);
//        orderCancelReqV2.setSaleItemId(saleIdList);
//        reqV2List.add(orderCancelReqV2);
//        orderFeignApi.cdoCancelV2(reqV2List);
    }

}
