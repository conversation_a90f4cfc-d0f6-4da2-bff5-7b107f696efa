package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.erp;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class EcCangERPGetPurchaseOrdersResponse {

    public Integer po_id;
    public String po_code;
    public Integer warehouse_id;
    public Integer shipping_method_id_head;
    public String tracking_no;
    public String ref_no;
    public Integer suppiler_id;
    public Double payable_amount;
    public Double actually_amount;
    public String currency_code;
    public Integer pay_status;
    public Integer po_staus;
    public String date_create;
    public String date_eta;
    public String date_release;
    public String po_completion_time;
    public String po_complete_type;
    public Integer to_warehouse_id;
    public Integer receiving_exception;
    public Integer operator_purchase;
    public Integer receiving_exception_handle;
    public String return_verify;
    public Double pay_ship_amount;
    public Integer create_type;
    public String pts_status_sort;
    public Integer account_type;
    public Integer account_proportion;

    public String payment_cycle_type;
    public Integer pts_oprater;
    public String transaction_no;
    public Integer ps_id;
    public String po_remark;
    public Integer receiving_exception_status;
    public Integer qc_exception_status;
    public String supplier_name;
    public String supplier_code;
    public String warehouse_code;
    public String warehouse_desc;
    public String receiving_code;
    public Integer verify;
    public Integer mark_eta;
    public String pts_name;
    public String ps_name;
    public String ps_url;
    public String pt_note;
    public String pt_add_time;

    public Integer qty_expected_all;
    public Integer qty_receving_all;
    public Integer trackings;
    public Integer po_is_net;
    public Integer pay_type;
    public String bank_name;
    public String pay_account;
    public Double sum_amount;
    public String date_expected;
    public Integer po_type;
    public List<String> single_net_number;
    public Double total_tax_fee;

    public Track track;
    public SystemTrack systemTrack;
    public List<Detail> detail;

    public String payment_note;
    public String company;
    public String operator_create;

    public static class Track {
        public String name;
        public String name_en;
    }

    public static class SystemTrack {
        public String name;
        public String name_en;
    }

    @Getter
    @Setter
    public static class Detail {
        public Integer product_id;
        public Integer qty_expected;
        public Integer qty_pay;
        public Integer qty_eta;
        public Integer qty_receving;
        public Integer qty_free;
        public Double unit_price;
        public Double total_price;
        public String currency_code;
        public String product_sku;
        public String product_title;
        public String sp_supplier_sku;
        public String is_free;
        public String note;
        public String pop_external_number;
        public Integer transfer_qty;
        public Integer po_tax_rate;
        public String first_receive_time;
        public String pop_platform_product;
        public String pop_platform_sku;
        public String po_status;
    }
}
