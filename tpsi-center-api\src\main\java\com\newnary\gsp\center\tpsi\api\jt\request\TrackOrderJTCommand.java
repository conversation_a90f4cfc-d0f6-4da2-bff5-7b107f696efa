package com.newnary.gsp.center.tpsi.api.jt.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 物流服务轨迹请求体
 *
 * <AUTHOR>
 * @since Created on 2023-11-17
 **/
@Data
public class TrackOrderJTCommand {


    /**
     * 物流返回的追踪号。
     **/
    @NotBlank(message = "物流追踪号不能为空")
    private String billcode;


    /**
     * 语言
     */
    private String lang;



}
