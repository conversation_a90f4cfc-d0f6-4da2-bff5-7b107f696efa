package com.newnary.gsp.center.tpsi.infra.client.common;

/**
 * @Author: jack
 * @CreateTime: 2024/11/5
 */
public enum PDFDirection {

    UP(false),
    DOWN(true),
    LEFT(false),
    RIGHT(true),
    NEXT(true),
    NEXT_IN_LINE(true), // Like NEXT, but does not traverse into the current parent
    PREVIOUS(false);
    private final boolean forward;

    PDFDirection(boolean forward) {
        this.forward = forward;
    }

    public boolean isForward() {
        return forward;
    }

}
