package com.newnary.gsp.center.tpsi.api.haiying.response.amazon;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingAmazonProductDetailInfoDTO {

    /**
     * 单个商品状态码
     * (200:正常 100:商品未收录)
     */
    private Integer code;

    /**
     * 商品id
     */
    private String asin;

    /**
     * 父类asin
     */
    private String parent_asin;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品品牌
     */
    private String brand;

    /**
     * 商品评分
     */
    private BigDecimal score;

    /**
     * 商品价格区间
     */
    private BigDecimal asin_price;

    /**
     * 用户评论数
     */
    private Integer customer_reviews;

    /**
     * 用户问答数
     */
    private Integer answered_questions;

    /**
     * 商品合身率
     */
    private BigDecimal fit;

    /**
     * 商品跟卖数
     */
    private Integer follow_sellers_num;

    /**
     * 商品主图
     */
    private String img_url;

    /**
     * 商品要点
     */
    private String bullet_point;

    /**
     * 商品要点原始内容
     */
    private String original_bullet_point;

    /**
     * 商品描述
     */
    private String prod_desc;

    /**
     * 商品运输信息
     */
    private String ship_info;

    /**
     * 店铺名
     */
    private String merchant;

    /**
     * 店铺编码
     */
    private String merchant_code;

    /**
     * 商品卖家类型
     */
    private String saler_type;

    /**
     * 商品最初上架时间
     */
    private Long fir_arrival;

    /**
     * 商品最新上架时间
     */
    private Long fir_arrival_newest;

    /**
     * 商品类目名
     */
    private String top_category;

    /**
     * 商品最新排名
     */
    private Integer top_sellers_rank;

    /**
     * 商品TOP排名HTML文本
     */
    private String best_sellers_rank;

    /**
     * 商品跟卖信息
     */
    private HaiYingAmazonFollowInfoDTO[] amazon_follows;

    /**
     * 商品关键词
     */
    private String keywords;

    /**
     * 商品运费
     */
    private BigDecimal ship_price;

    /**
     * 商品附图
     */
    private String[] imgs_url;

    /**
     * FBM(中国卖家)
     */
    private String chinese_sellers;

    /**
     * FBM(商家列表中出现中国卖家)
     */
    private String chinese_sellers_in_merhants;

    /**
     * 支持Prime
     * (0:否   1:是)
     */
    private Boolean is_prime;

    /**
     * Best Seller
     * (-1:不确定  0:否   1:是)
     */
    private Boolean is_best_seller;

    /**
     * Amazon's Choice
     * (0:否   1:是)
     */
    private Boolean is_ama_choice;

    /**
     * 属性
     * (null-无属性)
     */
    private String dimensions_display;

    /**
     * 属性值
     * (null-无属性)
     */
    private String dimensions;

    /**
     * 1688货源链接
     */
    private String supply_url;

    /**
     * amazon商品链接
     */
    private String asin_url;

    /**
     * 商品近一个月排名的抓取纪录
     */
    private HaiYingAmazonCateRankHistoryDTO[] cate_rank_his;

    /**
     * 商品近一个月商品部分走势纪录
     */
    private HaiYingAmazonAsinInfoHistoryDTO[] asin_info_his;

    /**
     * 国家
     * 美国US    英国UK
     * 德国DE    日本JP
     */
    private String station;

    /**
     * 产品重量
     */
    private BigDecimal shipping_weight;

    /**
     * 商品品牌是否注册(wipo)
     * 0为未注册      1为注册
     * 2为无品牌      3为未核对
     */
    private Integer is_registered_wipo;

    /**
     * 商品品牌是否注册(tmview)(已取消)
     * 0为未注册      1为注册
     * 2为无品牌      3为未核对
     */
    @Deprecated
    private Boolean is_registered_tmview;

    /**
     * 商品最新抓取时间
     */
    private Long last_upd_date;

    /**
     * 商品去掉html信息的bsr信息
     */
    private String show_best_sellers_rank;

    /**
     * 商品品牌的链接
     */
    private String brand_url;

    /**
     * 公司注册地
     * HK，CN，GB，DE，FR等等国家代码
     * NOMESSAGE无信息
     * NOCHECKED未核对
     */
    private String registration;

    /**
     * 商品关联的asin Id(多个以逗号分隔)
     */
    private List<String> listing_asins;

    /**
     * 商品关联的asin数量
     */
    private Integer listing_asins_number;

    /**
     * 商品日销量
     */
    private Integer day_sales;

    /**
     * 商品月销量
     */
    private Integer month_sales;

    /**
     * 当前asin的 parent_asin的最新排名信息
     */
    private HaiYingAmazonParentAsinRankInfoDTO[] parent_asin_newest_rank;

    /**
     * 当前asin的 parent_asin的排名历史信息
     */
    private HaiYingAmazonParentAsinRankInfoDTO[] parent_asin_rank_his;

    /**
     * 商品包装尺寸
     */
    private String package_dimensions;

    /**
     * 商品尺寸
     */
    private String item_dimensions;

    /**
     * 商品重量
     */
    private BigDecimal item_weight;

    /**
     * 商品包装重量
     */
    private BigDecimal package_weight;

    /**
     *
     */
    private String category_path_id;

    /**
     *
     */
    private String category_path_name;

    /**
     *
     */
    private String follow_seller_min_price;

    /**
     *
     */
    private String delivery_msg;

    /**
     *
     */
    private String extra_saving_str;

    /**
     *
     */
    private String coupon_info;

    /**
     *
     */
    private String business_addr;

    /**
     *
     */
    private Boolean is_registered;

    /**
     *
     */
    private HaiYingAmazonProductRankInfoDTO[] amazon_product_rank;

    /**
     *
     */
    private HaiYingAmazonProductSkuInfoDTO[] sku_list;

}
