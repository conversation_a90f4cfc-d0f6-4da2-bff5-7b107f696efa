package com.newnary.gsp.center.tpsi.service.open1688.impl;

import com.newnary.gsp.center.tpsi.infra.client.open1688.Open1688CrossProcurementAgencyClient;
import com.newnary.gsp.center.tpsi.infra.client.open1688.Open1688CrossProcurementAgencyComparePricesClient;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.bijia.request.QueryOpen1688MultiLanguageProductDetailRequest;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.bijia.response.QueryOpen1688MultiLanguageProductDetailResponse;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.comon.response.Category1688;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.bijia.request.QueryOpen1688MultiLanguageKeywordRequest;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.bijia.response.QueryOpen1688MultiLanguageKeywordResponse;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.request.*;
import com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.response.*;
import com.newnary.gsp.center.tpsi.infra.model.ThirdPartySystem;
import com.newnary.gsp.center.tpsi.service.impl.SystemClientSve;
import com.newnary.gsp.center.tpsi.service.open1688.Open1688Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.validation.Valid;
import java.util.List;

@Component
@Slf4j
public class Open1688ServiceImpl extends SystemClientSve implements Open1688Service {
    @Override
    public Category1688 getCategoryById(ThirdPartySystem thirdPartySystem, String categoryId) {
        String params = thirdPartySystem.getParams();
        Open1688CrossProcurementAgencyClient open1688Client = new Open1688CrossProcurementAgencyClient(params);
        Category1688 category1688 = open1688Client.getCategoryById("0");
        if (!ObjectUtils.isEmpty(category1688)) {
            return category1688;
        }
        return null;
    }

    @Override
    @Valid
    public SearchByKeywordsResponse searchByKeywords(ThirdPartySystem thirdPartySystem, SearchByKeywordsRequest searchByKeywordsRequest) {
        String params = thirdPartySystem.getParams();
        Open1688CrossProcurementAgencyClient open1688Client = new Open1688CrossProcurementAgencyClient(params);
        return open1688Client.searchByKeywords(searchByKeywordsRequest);
    }

    @Override
    public QueryProductResponse queryProductById(ThirdPartySystem thirdPartySystem, QueryProductRequest queryProductRequest) {
        String params = thirdPartySystem.getParams();
        Open1688CrossProcurementAgencyClient open1688Client = new Open1688CrossProcurementAgencyClient(params);
        return open1688Client.queryProductById(queryProductRequest);
    }

    @Override
    public CreateOpen1688OrderResponse createOpen1688Order(ThirdPartySystem thirdPartySystem, CreateOpen1688OrderRequest createOpen1688OrderRequest) {
        String params = thirdPartySystem.getParams();
        Open1688CrossProcurementAgencyClient open1688Client = new Open1688CrossProcurementAgencyClient(params);
        return open1688Client.createOpen1688Order(createOpen1688OrderRequest);
    }

    @Override
    public QueryOpen1688OrderDetailsResponse getOpen1688OrderDetail(ThirdPartySystem thirdPartySystem, QueryOpen1688OrderDetailsRequest queryOpen1688OrderDetailsRequest) {
        String params = thirdPartySystem.getParams();
        Open1688CrossProcurementAgencyClient open1688Client = new Open1688CrossProcurementAgencyClient(params);
        return open1688Client.getOpen1688OrderDetail(queryOpen1688OrderDetailsRequest);
    }

    @Override
    public Boolean closeOpen1688Order(ThirdPartySystem thirdPartySystem, CloseOpen1688OrderRequest closeOpen1688OrderRequest) {
        String params = thirdPartySystem.getParams();
        Open1688CrossProcurementAgencyClient open1688Client = new Open1688CrossProcurementAgencyClient(params);
        return open1688Client.closeOpen1688Order(closeOpen1688OrderRequest);
    }

    @Override
    public GetOpen1688OrderLogisticsInfoResponse getOpen1688OrderLogisticsInfo(ThirdPartySystem thirdPartySystem, GetOpen1688OrderLogisticsInfoRequest request) {
        String params = thirdPartySystem.getParams();
        Open1688CrossProcurementAgencyClient open1688Client = new Open1688CrossProcurementAgencyClient(params);
        return open1688Client.getOpen1688OrderLogisticsInfo(request);
    }

    @Override
    public GetOpen1688OrderLogisticsTraceInfoResponse getOpen1688OrderLogisticsTraceInfo(ThirdPartySystem thirdPartySystem, GetOpen1688OrderLogisticsTraceInfoRequest request) {
        String params = thirdPartySystem.getParams();
        Open1688CrossProcurementAgencyClient open1688Client = new Open1688CrossProcurementAgencyClient(params);
        return open1688Client.getOpen1688OrderLogisticsTraceInfo(request);
    }

    @Override
    public QueryOpen1688ProductByImageUrlResponse queryOpen1688ProductByImageUrl(ThirdPartySystem thirdPartySystem, QueryOpen1688ProductByImageUrlRequest request) {
        String params = thirdPartySystem.getParams();
        Open1688CrossProcurementAgencyClient open1688Client = new Open1688CrossProcurementAgencyClient(params);
        return open1688Client.queryOpen1688ProductByImageUrl(request);
    }

    @Override
    public QueryOpen1688JXHYProductResponse queryJXHYProduct(ThirdPartySystem thirdPartySystem, QueryOpen1688JXHYProductRequest request) {
        String params = thirdPartySystem.getParams();
        Open1688CrossProcurementAgencyClient open1688Client = new Open1688CrossProcurementAgencyClient(params);
        return open1688Client.getJXHYProduct(request);
    }

    @Override
    public QueryJXHYProductDetailResponse queryJXHYProductDetail(ThirdPartySystem thirdPartySystem, List<Long> offerIds) {
        String params = thirdPartySystem.getParams();
        Open1688CrossProcurementAgencyClient open1688Client = new Open1688CrossProcurementAgencyClient(params);
        return open1688Client.getJXHYProductDetail(offerIds);
    }

    @Override
    public QueryOpen1688MultiLanguageKeywordResponse queryMultiLanguageKeyWords(ThirdPartySystem thirdPartySystem, QueryOpen1688MultiLanguageKeywordRequest dyyKeywordRequest) {
        String params = thirdPartySystem.getParams();
        Open1688CrossProcurementAgencyComparePricesClient open1688Client = new Open1688CrossProcurementAgencyComparePricesClient(params);
        return open1688Client.getMultiLanguageKeyWords(dyyKeywordRequest);
    }

    @Override
    public QueryOpen1688MultiLanguageProductDetailResponse queryMultiLanguageProductDetail(ThirdPartySystem thirdPartySystem, QueryOpen1688MultiLanguageProductDetailRequest request) {
        String params = thirdPartySystem.getParams();
        Open1688CrossProcurementAgencyComparePricesClient open1688Client = new Open1688CrossProcurementAgencyComparePricesClient(params);
        return open1688Client.getMultiLanguageProductDetail(request);
    }
}
