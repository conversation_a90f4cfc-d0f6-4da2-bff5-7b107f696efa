package com.newnary.gsp.center.tpsi.alibaba;

import com.alibaba.fastjson.JSONObject;
import com.newnary.api.base.common.PageCondition;
import com.newnary.gsp.center.tpsi.api.externalproduct.request.ExternalProductImageSearchCommand;
import com.newnary.gsp.center.tpsi.api.externalproduct.response.ExternalProductSearchInfo;
import com.newnary.gsp.center.tpsi.app.job.ProductDetailsJobManager;
import com.newnary.gsp.center.tpsi.infra.model.CrawlerProduct;
import com.newnary.gsp.center.tpsi.service.alibaba.OceanProductSearchService;
import com.newnary.gsp.center.tpsi.service.product.ISync1688ProductApiSve;
import com.newnary.test.starter.BaseTestInjectTenant;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since Created on 2022-05-12
 **/
public class OceanProductSearchTest extends BaseTestInjectTenant {

    @Resource
    private OceanProductSearchService oceanProductSearchService;

    @Resource
    private ProductDetailsJobManager productDetailsJobManager;

    @Resource
    private ISync1688ProductApiSve i1688ProductApiSve;

    @Test
    public void testIDruku(){
//        i1688ProductApiSve.storeProductDetail("************","{\"detailDescription\":{\"dataDesc\":{\"payOrder30DayStr\":\"0\"},\"freightInfo\":{\"unitWeight\":1,\"location\":\"浙江 衢州\",\"totalCost\":2004.0}},\"offerDetail\":{\"subject\":\"装饰圣诞树商场柜台装饰品杭州圣诞树Christmas tree圣诞树工厂\",\"featureAttributes\":[{\"values\":[\"PVC\"],\"name\":\"材质\"},{\"values\":[\"否\"],\"name\":\"是否专利货源\"},{\"values\":[\"常规\"],\"name\":\"品牌\"},{\"values\":[\"否\"],\"name\":\"是否IP授权\"},{\"values\":[\"0.3-3.8\"],\"name\":\"高度\"},{\"values\":[\"常规\"],\"name\":\"产品编号\"}],\"offerId\":************,\"detailUrl\":[\"https://cbu01.alicdn.com/img/ibank/O1CN01oYMo842KIASNArYqN_!!*********-0-cib.jpg?__r__=*************\",\"https://cbu01.alicdn.com/img/ibank/O1CN01pM9wt82KIAR5H50Sa_!!*********-0-cib.jpg?__r__=*************\",\"https://cbu01.alicdn.com/img/ibank/O1CN01tEATTU2KIASbPS6Pi_!!*********-0-cib.jpg?__r__=*************\",\"https://cbu01.alicdn.com/img/ibank/O1CN01vIhuu82KIASVlySuI_!!*********-0-cib.jpg?__r__=*************\",\"https://cbu01.alicdn.com/img/ibank/O1CN01RsaHXd2KIASXnd5B6_!!*********-0-cib.jpg?__r__=*************\",\"https://cbu01.alicdn.com/img/ibank/2020/146/818/16500818641_944169006.jpg\",\"https://cbu01.alicdn.com/img/ibank/2020/975/128/16500821579_944169006.jpg\"],\"leafCategoryId\":*********,\"imageList\":[{\"fullPathImageURI\":\"https://cbu01.alicdn.com/img/ibank/O1CN01AZugeW2KIAVPPOu34_!!*********-0-cib.jpg\"},{\"fullPathImageURI\":\"https://cbu01.alicdn.com/img/ibank/O1CN01ipEsbF2KIASMPsndk_!!*********-0-cib.jpg\"},{\"fullPathImageURI\":\"https://cbu01.alicdn.com/img/ibank/O1CN01twbLyC2KIASTRovLJ_!!*********-0-cib.jpg\"},{\"fullPathImageURI\":\"https://cbu01.alicdn.com/img/ibank/O1CN01vIhuu82KIASVlySuI_!!*********-0-cib.jpg\"}],\"wirelessVideo\":{\"coverUrl\":\"https://cbu01.alicdn.com/img/ibank/O1CN01VgVPq11Bs2m0fw9Y5_!!0-0-cib.jpg\",\"videoId\":************,\"state\":6,\"title\":\"圣诞树\",\"videoUrls\":{\"android\":\"https://cloud.video.taobao.com/play/u/*********/p/2/e/6/t/1/************.mp4\",\"ios\":\"https://cloud.video.taobao.com/play/u/*********/p/2/e/6/t/1/************.mp4\"}}},\"sellerModel\":{\"userId\":*********},\"tradeModel\":{\"unit\":\"棵\",\"priceDisplay\":\"110.00\",\"offerPriceModel\":{\"currentPrices\":[{\"price\":\"110.00\",\"beginAmount\":1000}],\"priceDisplayType\":\"rangePrice\"},\"saleCount\":0,\"skuMap\":[]}}");
//        List<CrawlerProduct> crawlerProducts = i1688ProductApiSve.loadCrawlerProductByCategoryList(null, 10, null, 0, null);
//        System.out.println(crawlerProducts);
        CrawlerProduct crawlerProduct = i1688ProductApiSve.loadCrawlerDetailReturn("************");
        System.out.println(crawlerProduct);
    }

    @Test
    public void fanye() throws InterruptedException {
        productDetailsJobManager.autoDoSync1688ProductId("{\n" +
                "    \"keywords\": \"火柴\",\n" +
                "    \"beginPage\": 1,\n" +
                "    \"pageSize\": 20,\n" +
                "    \"sourceType\": \"ALI_1688\",\n" +
                "    \"psOptBizId\": \"USER6625304286001382166528-1677663139662-91e10022536835e470c88b4c513064b1\",\n" +
                "    \"resultCount\": 100,\n" +
                "    \"turnPage\":1\n" +
                "}");
        Thread.sleep(1100000L);
    }

    @Test
    public void flush() throws InterruptedException {
        productDetailsJobManager.autoDoChangePriceAndSetInventory("{\n" +
                "\"categoryName\":\"\",\n" +
                "\"limit\" : 10\n" +
                "}");
        Thread.sleep(1100000L);
    }

    @Test
    public void testCrawlerReturn() throws InterruptedException {
        productDetailsJobManager.autoDoSyncWarehousingCrawlerReturn("{\n" +
                "\"categoryName\":\"\",\n" +
                "\"limit\" : 20\n" +
                "}");
        Thread.sleep(1100000L);
    }

    @Test
    public void testruku() throws InterruptedException {
        productDetailsJobManager.autoDoSyncWarehousingProductDetail("{\n" +
                "\"categoryName\":\"\",\n" +
                "\"limit\" : 10\n" +
                "}");
        Thread.sleep(1100000L);
    }

    @Test
    public void testName() {
        System.out.println("??????????????????");

        ExternalProductImageSearchCommand command = new ExternalProductImageSearchCommand();
        command.setImageUrl("https://cbu01.alicdn.com/img/ibank/O1CN01t5uUoD2BunH3VGEWQ_!!*************-0-cib.jpg_220x220.jpg");
        command.setPageCondition(new PageCondition());

        ExternalProductSearchInfo info = oceanProductSearchService.imageSearch(command);
        System.out.println(JSONObject.toJSONString(info));

    }

    @Test
    public void refreshTokenTest() {
        String accessToken = oceanProductSearchService.getAccessToken(null);
        System.out.println(accessToken);
    }

    @Test
    public void testBatch() {
        CompletableFuture[] completableFutures = Stream.of("1", "2", "3", "4")
                .parallel()
                .map(value -> {
                    return CompletableFuture.runAsync(() -> {
                        ExternalProductImageSearchCommand command = new ExternalProductImageSearchCommand();
                        command.setImageUrl("https://cbu01.alicdn.com/img/ibank/O1CN01t5uUoD2BunH3VGEWQ_!!*************-0-cib.jpg_220x220.jpg");
                        command.setPageCondition(new PageCondition());

                        ExternalProductSearchInfo info = oceanProductSearchService.imageSearch(command);
                        System.out.println(value + ">>>>" + JSONObject.toJSONString(info));
                    });
                }).toArray(CompletableFuture[]::new);

        CompletableFuture.allOf(
                Stream.of(
                                "1",
                                "2",
                                "3",
                                "4"
                        )
                        .parallel()
                        .map(value -> {
                            return CompletableFuture.runAsync(() -> {
                                ExternalProductImageSearchCommand command = new ExternalProductImageSearchCommand();
                                command.setImageUrl("https://cbu01.alicdn.com/img/ibank/O1CN01t5uUoD2BunH3VGEWQ_!!*************-0-cib.jpg_220x220.jpg");
                                command.setPageCondition(new PageCondition());

                                ExternalProductSearchInfo info = oceanProductSearchService.imageSearch(command);
                                System.out.println(value + ">>>>" + JSONObject.toJSONString(info));
                            });
                        }).toArray(CompletableFuture[]::new)
        ).join();
    }

    @Override
    protected String tenantId() {
        return "TENANT3924117688555169382400";
    }
}
