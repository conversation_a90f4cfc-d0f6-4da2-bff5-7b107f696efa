package com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.response.erp;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Data
public class EcCangERPGetProductBySkuResponse {

    //-	产品SKU代码
    private String productSku;

    //名称CN
    private String productTitle;

    //名称EN
    private String productTitleEn;

    //-	规格
    private String productSpecs;

    //申报品名CN
    private String pdOverseaTypeCn;

    //-	申报品名EN
    private String pdOverseaTypeEn;

    //申报价值
    private BigDecimal productDeclaredValue;

    //（产品开发阶段的）申报币种：例如USD
    private String pdDeclareCurrencyCode;

    //-	默认采购单价
    private BigDecimal productPurchaseValue;

    //（确认提交后的）申报币种
    private String currencyCode;

    //默认供应商采购币种
    private String purcurrencycode;

    //重量，单位Kg
    private BigDecimal productWeight;

    //默认供应商代码。提供接口getAllSupplier查询
    private String defaultSupplierCode;

    //-	供应商品号
    private String supplierSku;

    //-	商品单价
    private BigDecimal productPrice;

    //一级品类代码
    private String procutCategoryCode1;

    //二级品类代码
    private String procutCategoryCode2;

    //三级品类代码
    private String procutCategoryCode3;

    //一级品类名称
    private String procutCategoryName1;

    //二级品类名称
    private String procutCategoryName2;

    //三级品类名称
    private String procutCategoryName3;

    //运营方式：1
    //代运营、2
    //自运营
    private Integer oprationType;

    //产品销售状态，数据可自定义。提供接口getSaleStatus查询
    private Integer saleStatus;

    //-	海关编码
    private String hsCode;

    //产品包装尺寸长，单位CM
    private BigDecimal productLength;

    //产品包装尺寸度，单位CM
    private BigDecimal productWidth;

    //产品包装尺寸度，单位CM
    private BigDecimal productHeight;

    //产品净尺寸长，单位CM
    private BigDecimal pdNetLength;

    //产品净尺寸长，单位CM
    private BigDecimal pdNetWidth;

    //-	产品净尺寸长，单位CM
    private BigDecimal pdNetHeight;

    //重量允许浮动值,单位Kg
    private BigDecimal allowFloatWeight;

    //-	FOB税率，例如0.034
    private BigDecimal fboTaxRate;

    //进口申报价值（币种固定是USD  ）
    private BigDecimal productImportDeclareValue;

    //开始设计时间，格式为2016-08-09 13:34:56
    private String designerStartTime;

    //截止设计时间
    private String designerEndTime;

    //设计师Id
    private Integer designerId;

    //采购负责人Id
    private Integer personOpraterId;

    //销售负责人Id
    private Integer personSellerId;

    //开发负责人Id
    private Integer personDevelopId;

    //产品颜色Id，数据可自定义。提供接口getProductColor查询
    private Integer productColorId;

    //产品颜色名称
    private String productColorName;

    //产品颜色英文名称
    private String productColorNameEn;

    //产品尺寸Id，数据可自定义。提供接口getProductSize查询
    private Integer productSizeId;

    //产品尺寸名称
    private String productSizeName;

    //产品尺寸英文名称
    private String productSizeNameEn;

    //upcCode
    private String upcCode;

    //中文材质
    private String productMaterial;

    //英文材质
    private String productMaterialEn;

    //中文用途
    private String use;

    //-	英文用途
    private String useEn;

    //-	申报说明
    private String pdDeclarationStatement;

    //参考采购链接
    private String refUrl;

    //FBA仓租尺寸类型：0
    //无 1
    //标准尺寸 2
    //超标尺寸
    private Integer productFbaSizeType;

    //供应商交期，单位天
    private Integer spEtaTime;

    //供应商最小采购量
    private Integer spMinQty;

    //供应商产品地址，例如：["https://detail.1688.com/offer/556360444352.html?spm=b26110380.sw1688.mof001.10.76f24de0YBBgDc"]
    private List<String> spProductAddress;

    //采购参考价
    private BigDecimal spReferenceUnitPrice;

    //产品等级Id，数据可自定义。提供接口getProductLevel查询
    private Integer prlId;

    //产品款式Id，数据可自定义。提供接口getProductParent查询
    private Integer parentProductId;

    //是否成品。1
    //是 0
    //否
    private Integer isEndProduct;

    //是否含电池。1
    //是 0
    //否
    private Integer containBattery;

    //是否为仿制品。1
    //是 0
    //否
    private Integer isImitation;

    //是否需要质检。1
    //需要 0
    //不需要
    private Integer isQc;

    //是否组合产品。1
    //是 0
    //否
    private Integer productIsCombination;

    //贴标容易度；1
    //简单 2
    //普通 3
    //困难
    private String labelingType;

    //自定义属性：查看对象说明selfPropertyList
    private JSONObject selfPropertyList;

    //品牌代码
    private String brandCode;

    //品牌名称
    private String brandName;

    //是否存在有效期。0
    //无 1
    //有
    private Integer isExpDate;

    //有效期天数
    private Integer expDate;

    //仓库条码
    private String warehouseBarcode;

    //原产地
    private String productOrigin;

    //-	毛利，例如：0.05
    private BigDecimal grossProfit;

    //是否赠品。1
    //是 0
    //否
    private Integer isGift;

    //税率，例如：0.05
    private BigDecimal taxRate;

    //组织机构ID
    private Integer userOrganizationId;

    //默认发货仓库ID
    private Integer defaultWarehouseId;

    //-	EAN码
    private String eanCode;

    //产品创建时间
    private String productAddTime;

    //更新时间时间
    private String productUpdateTime;

    //组合产品明细 getProductCombination
    //为1
    //时返回
    private ProductCombination productCombination;

    @Getter
    @Setter
    public static class ProductCombination{

        //FNSKU
        private String pcrFnsku;

        //FBA-ASIN
        private String pcrFbaAsin;

        //仓库Id，0为全部仓库
        private String warehouseId;

        //-	创建时间
        private String pcrAddTime;

        //更新时间
        private String pcrUpdateTime;

        //组合子产品SKU明细
        private SubProducts subProducts;
        @Getter
        @Setter
        public static class SubProducts{
            //组合子产品SKU
            private String pcrProductSku;

            //组合子产品数量
            private Integer pcrQty;
        }

    }

    //自定义分类 getProductCustomCategory
    //为1
    //时返回
    private ProductCustomCategory productCustomCategory;
    @Getter
    @Setter
    public static class ProductCustomCategory{
        //自定义分类名称
        private String pucName;
    }

    //产品图片
    private String mainImg;

    //-	描述
    private List<ProductDescription> productDescriptions;
    @Getter
    @Setter
    public static class ProductDescription{
        //描述语言
        private String language;

        //描述内容
        private String pdd_description;
    }

    //默认采购员名字
    private String defaultBuyerName;

    //-	产品包材
    private List<ProductPackage> productPackage;
    @Getter
    @Setter
    public static class ProductPackage{
        //包材代码
        private String packageCode;

        //包材名称
        private String packageName;

        // 数量
        private String packageQty;

        //仓库
        private String packageWarehouse;
    }

    //产品成本
    private List<ProductCost> productCost;

    @Getter
    @Setter
    public static class ProductCost{

        //FNSKU
        private String psSku;

        //-	FBA-ASIN
        private String psCountry;

        //默认采购成本
        private BigDecimal psDefaultPurchaseCost;

        //默认采购运费
        private BigDecimal psDefaultPurchaseFreight;

        //默认头程成本
        private BigDecimal psDefaultFirstJourneyCost;

        //默认关税成本
        private BigDecimal psDefaultTariffCost;

        //
        private String updateTime;
    }

    //产品详细描述
    private String pdDesc;


}
