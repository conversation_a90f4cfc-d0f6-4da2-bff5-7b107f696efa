package com.newnary.gsp.center.tpsi.infra.client.tk.dto.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class TKOrderExtInfo {
    public String order_id;
    public List<OrderLine> order_line_list;
    public List<PackageInfo> packageInfos;

    @Getter
    @Setter
    public static class OrderLine {
        public String order_line_id;
        public String sku_id;
        public String seller_sku;
        public String package_id;
        public String tracking_number;
        public String shipping_provider_id;
        public String shipping_provider_name;
    }

    @Getter
    @Setter
    public static class PackageInfo {
        public String package_id;
        // GSP系统里面的发货单ID
        public String pre_split_pkg_id;
        public String label_url;
    }
}
