package com.newnary.gsp.center.tpsi.tiktok;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.newnary.gsp.center.tpsi.app.job.TKChannelJobManager;
import com.newnary.gsp.center.tpsi.ctrl.tk.TKApiImpl;
import com.newnary.gsp.center.tpsi.infra.client.tk.TKClient;
import com.newnary.gsp.center.tpsi.infra.client.tk.dto.TKApiParam;
import com.newnary.gsp.center.tpsi.infra.client.tk.dto.TKFetchOrderReq;
import com.newnary.gsp.center.tpsi.infra.client.tk.dto.vo.SaleAttribute;
import com.newnary.gsp.center.tpsi.infra.client.tk.dto.vo.TrackInfoList;
import com.newnary.test.starter.BaseTestInjectTenant;
import org.junit.Test;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TKTest extends BaseTestInjectTenant {

    @Resource
    private TKChannelJobManager tkChannelJobManager;

    @Resource
    private TKApiImpl tkApi;

    private  static final String apiParam = "{\"access_token\":\"ROW_dlKjJwAAAACPzLTWU6C9wBVOCfTvnn8nhpcauvBetXJZnY1_D6KfyAIQuy0HxkZ0ozfFxH2xeoq1sySLipB9GKsQgptyPM3lhDiWra3qdCavDuuHP7uw7w\",\"app_key\":\"67q49sc9tm7q4\",\"app_secret\":\"cd7bba8d1fc7e5ab1426f5efcb8d93fa8bb8a61b\",\"auth_code\":\"ROW_dlKjJwAAAACPzLTWU6C9wBVOCfTvnn8nhpcauvBetXJZnY1_D6KfyAIQuy0HxkZ0ozfFxH2xeoq1sySLipB9GKsQgptyPM3lhDiWra3qdCavDuuHP7uw7w\",\"brandMapping\":{\"PMB2095302178241014272000\":\"7111595400213071622\"},\"categoryMapping\":{\"PMC845988527528951545856\":\"815240\"},\"defaultWarehouseId\":\"7200337037899220762\",\"grant_type\":\"authorized_code\",\"shop_code\":\"PHLC8HLW3W\",\"token_url\":\"https://auth-sandbox.tiktok-shops.com\",\"url\":\"https://open-api.tiktokglobalshop.com\",\"labelSize\":\"A6\",\"labelType\":\"SHIPPING_LABEL\",\"shop_id\":7494934350475528981}";
    public static void testGetToken() throws Exception {
        TKApiParam tkApiParam = new TKApiParam();
        tkApiParam.setApp_key("67q49sc9tm7q4");
        tkApiParam.setApp_secret("cd7bba8d1fc7e5ab1426f5efcb8d93fa8bb8a61b");
        tkApiParam.setUrl("https://open-api.tiktokglobalshop.com");
        tkApiParam.setShop_code("PHLC8HLW3W");
        tkApiParam.setToken_url("https://auth.tiktok-shops.com");
        tkApiParam.setAuth_code("ROW_HiyDjgAAAABInQ3DLHkijRii6eQE-6kZJcTvKRu__qqc51Jorg4K7QjwaaG1UsObDWfRIKxWVprFXoDOnOVP2fIPVeJJXA1W");
        tkApiParam.setGrant_type("authorized_code");
        tkApiParam.setUse_new_token("T");

        System.out.println(JSON.toJSONString(tkApiParam));
        String tiktok = TKClient.getToken(tkApiParam, "TIKTOK");
        System.out.println(tiktok);
    }

    public static void testGetSandboxToken() throws Exception {
        TKApiParam tkApiParam = new TKApiParam();
        tkApiParam.setApp_key("67q49sc9tm7q4");
        tkApiParam.setApp_secret("cd7bba8d1fc7e5ab1426f5efcb8d93fa8bb8a61b");
        tkApiParam.setUrl("https://open-api-sandbox.tiktokglobalshop.com");
        tkApiParam.setAccess_token("ROW_EH4v0wAAAAAoC8HnFARP2S268dFWqDocLZfz_x7SDCz5-JeJG6B6AoLGIsAAo80bsl-P0DLYRhqUMinPq3pz9MRPFr9eL-7ruYUepWgOtrV31Mlz5IpIoA");
        //tkApiParam.setAccess_token("ROW_BbONuwAAAAAoC8HnFARP2S268dFWqDocCEEsh2EaNMcnvgOk4eWePlIvGiPhitNbzYazEy3TWQxL6UdtxJCzHxqyjSdusVE41C4EE3yiOuyCrDUw9spiDw");
        tkApiParam.setShop_code("GBLC9LWLWH");
        tkApiParam.setToken_url("https://auth-sandbox.tiktok-shops.com");
        tkApiParam.setAuth_code("ROW_ObMvnwAAAAB2uO_6bf0KOSskkHd1f5XHUYmH-8ZhsljNf7-1rYMDpf39jViDbJE-1yNiv7ihAogy5OgQB1RJkTc20_enNOmw");
        tkApiParam.setGrant_type("authorized_code");
        tkApiParam.setUse_new_token("T");

        System.out.println(JSON.toJSONString(tkApiParam));
        TKClient.getShop(JSON.toJSONString(tkApiParam), null);
//        String tiktok = TKClient.getToken(tkApiParam, "TIKTOK");
//        System.out.println(tiktok);
    }


    private static String getSandboxAuthCodeUrl() {
        return "https://auth-sandbox.tiktok-shops.com/oauth/authorize?app_key=67q49sc9tm7q4&state=123";
    }


    @Test
    public void testFetchOrder() {

        tkChannelJobManager.autoFetchOrder2Erp("{\"order_status\":112}");
    }
    @Test
    public void testFetchOrderLabel() {

        tkChannelJobManager.autoFetchOrderLabel(null);
    }

    @Test
    public void testSyncProduct() {
        tkChannelJobManager.autoSyncProducts2TK(null);
    }


    @Test
    public void autoNotifyOrderShipped() {
        JSONObject paramObj = new JSONObject();
        tkChannelJobManager.autoNotifyOrderShipped(paramObj.toJSONString());
    }

    @Test
    public void testAutoNotifyGspOrderFinished() {
        tkChannelJobManager.autoNotifyGspOrderFinished(null);
    }

    @Test
    public void testAutoRefreshToken() {
        tkChannelJobManager.autoRefreshToken(null);
    }

    @Test
    public void testFetchOrderShippingInfo() {
        TrackInfoList shippingInfo = tkApi.getOrderShippingInfo("PO9534311545536102666240", null, "C7356302200292441722880", null);
    }


    public static void main(String[] args) throws Exception {
        //getBrands();
        //getApiParam();
        //getCategory();
        //getAttributes();
        //getTkFetchReq();

        //getAttributes();
        //getShop();

        //testGetToken();

        //testGetSandboxToken();

        //testGetSandboxToken();


        testGetWarehouseList();
    }

    private static void getShop() {
        TKClient.getShop(apiParam,  "C7356302200292441722880");
    }

    private static void getTkFetchReq() {
        TKFetchOrderReq req = new TKFetchOrderReq();
        req.setPage_size(50);
        System.out.println(JSON.toJSONString(req));
    }

    public static void getAttributes() {
        List<SaleAttribute> categoryAttributes = TKClient.getCategoryAttributes("815240", apiParam, "1");
        System.out.println(JSON.toJSONString(categoryAttributes));
    }


    public static void getApiParam() {
        TKApiParam tkApiParam = new TKApiParam();
        tkApiParam.setApp_key("67q49sc9tm7q4");
        tkApiParam.setApp_secret("cd7bba8d1fc7e5ab1426f5efcb8d93fa8bb8a61b");
        tkApiParam.setUrl("https://open-api.tiktokglobalshop.com");
        tkApiParam.setAccess_token("ROW_dlKjJwAAAACPzLTWU6C9wBVOCfTvnn8nhpcauvBetXJZnY1_D6KfyAIQuy0HxkZ0ozfFxH2xeoq1sySLipB9GKsQgptyPM3lhDiWra3qdCavDuuHP7uw7w");
        tkApiParam.setShop_code("PHLC8HLW3W");
        tkApiParam.setToken_url("https://auth-sandbox.tiktok-shops.com");
        tkApiParam.setAuth_code("ROW_dlKjJwAAAACPzLTWU6C9wBVOCfTvnn8nhpcauvBetXJZnY1_D6KfyAIQuy0HxkZ0ozfFxH2xeoq1sySLipB9GKsQgptyPM3lhDiWra3qdCavDuuHP7uw7w");
        tkApiParam.setGrant_type("authorized_code");

        JSONObject brandMapping = new JSONObject();
        brandMapping.put("PMB1923128593730813956096", "7111595400213071622");
        tkApiParam.setBrandMapping(brandMapping);

        JSONObject categoryMapping = new JSONObject();
        categoryMapping.put("PMC585188526569512894464", "815240");
        tkApiParam.setCategoryMapping(categoryMapping);
        tkApiParam.setDefaultWarehouseId("7200337037899220762");

        System.out.println(JSON.toJSONString(tkApiParam));
    }


    public static void testGetWarehouseList() {
        String apiParam = "{\"access_token\":\"ROW_BbONuwAAAAAoC8HnFARP2S268dFWqDocCEEsh2EaNMcnvgOk4eWePlIvGiPhitNbzYazEy3TWQxL6UdtxJCzHxqyjSdusVE41C4EE3yiOuyCrDUw9spiDw\",\"app_key\":\"67q49sc9tm7q4\",\"app_secret\":\"cd7bba8d1fc7e5ab1426f5efcb8d93fa8bb8a61b\",\"auth_code\":\"ROW_dlKjJwAAAACPzLTWU6C9wBVOCfTvnn8nhpcauvBetXJZnY1_D6KfyAIQuy0HxkZ0ozfFxH2xeoq1sySLipB9GKsQgptyPM3lhDiWra3qdCavDuuHP7uw7w\",\"brandMapping\":{\"PMB2095302178241014272000\":\"7111595400213071622\",\"PMB1923128593730813956096\":\"7111595400213071622\"},\"categoryMapping\":{\"PMC585188526569512894464\":\"815240\"},\"defaultWarehouseId\":\"7200337037899220762\",\"grant_type\":\"authorized_code\",\"shop_code\":\"GBLC9LWLWH\",\"shop_id\":7494936650479995030,\"token_url\":\"https://auth-sandbox.tiktok-shops.com\",\"url\":\"https://open-api-sandbox.tiktokglobalshop.com\"}";
        TKClient.getWarehouseList(apiParam, "C7356302200292441722880");
    }

    public static void getSandboxApiParam() {
        TKApiParam tkApiParam = new TKApiParam();
        tkApiParam.setApp_key("67q49sc9tm7q4");
        tkApiParam.setApp_secret("cd7bba8d1fc7e5ab1426f5efcb8d93fa8bb8a61b");
        tkApiParam.setUrl("https://open-api-sandbox.tiktokglobalshop.com");
        tkApiParam.setAccess_token("ROW_BbONuwAAAAAoC8HnFARP2S268dFWqDocCEEsh2EaNMcnvgOk4eWePlIvGiPhitNbzYazEy3TWQxL6UdtxJCzHxqyjSdusVE41C4EE3yiOuyCrDUw9spiDw");
        tkApiParam.setShop_code("GBLC9LWLWH");
        tkApiParam.setToken_url("https://auth-sandbox.tiktok-shops.com");
        tkApiParam.setAuth_code("ROW_PFzcvgAAAAB2uO_6bf0KOSskkHd1f5XHUYmH-8ZhsljNf7-1rYMDpWwF1lYBoCIgnCq_JjzecD024cmg5zPfJWRBsTU1wFay");
        tkApiParam.setGrant_type("authorized_code");
        tkApiParam.setShop_id(7204745753850332934L);

        JSONObject brandMapping = new JSONObject();
        brandMapping.put("PMB1923128593730813956096", "7111595400213071622");
        brandMapping.put("PMB2095302178241014272000", "7111595400213071622");
        tkApiParam.setBrandMapping(brandMapping);


        JSONObject categoryMapping = new JSONObject();
        categoryMapping.put("PMC585188526569512894464", "815240");
        tkApiParam.setCategoryMapping(categoryMapping);
        tkApiParam.setDefaultWarehouseId("7200337037899220762");

        TKClient.getShop(JSON.toJSONString(tkApiParam), "");
        System.out.println(JSON.toJSONString(tkApiParam));
    }


    public static void getCategory() {
        JSONArray ddd = TKClient.getCategories(apiParam, "C9217299671055683948544");
        System.out.println(JSON.toJSONString(ddd));
    }

    public static void getBrands() {
        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("page_size", 200);
        queryParam.put("page_number", 1);
        TKClient.getBrands(queryParam, apiParam, "dddd");
    }

    private static void getOrderQueryReq() {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            TKFetchOrderReq req = new TKFetchOrderReq();
            // 查询指定时间段
            String startTime = "2023-02-16 00:00:00";
            String endTime = "2023-02-16 23:59:59";
            req.setCreate_time_from((int) sdf.parse(startTime).getTime() / 1000);
            req.setCreate_time_to((int) sdf.parse(endTime).getTime() / 1000);
            req.setPage_size(50);
            System.out.println(JSON.toJSONString(req));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Override
    protected String tenantId() {
        return "TENANT3924117688555169382400";
    }
}
