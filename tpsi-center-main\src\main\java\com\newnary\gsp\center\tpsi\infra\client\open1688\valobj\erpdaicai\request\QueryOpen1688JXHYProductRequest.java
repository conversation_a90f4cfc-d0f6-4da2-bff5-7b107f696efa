package com.newnary.gsp.center.tpsi.infra.client.open1688.valobj.erpdaicai.request;

import lombok.Data;

import java.util.List;

@Data
public class QueryOpen1688JXHYProductRequest {

    /**
     * 选品规划id
     */
    private Long ruleId;

    /**
     * 1688类目id
     */
    private Long categoryId;

    /**
     * 商品关键词
     */
    private String keywords;

    /**
     * 商品置顶，只会在第一页置顶，和分页大小相同最多50个
     */
    private List<Long> topOfferIds;

    /**
     * 非必填，默认为1
     */
    private Integer pageNo;

    /**
     * 非必填，默认为20
     */
    private Integer pageSize;

    /**
     * 筛选出包邮商品
     */
    private Boolean filterFreePostage;

    /**
     * 筛选出一件代发商品
     */
    private Boolean filterYjdf;

    /**
     * 筛选出7天无理由包退商品
     */
    private Boolean filter7dNoReasonReturn;

    /**
     * 筛选出48h发货商品
     */
    private Boolean filter48hShip;

    /**
     * 筛选出最近30天48小时内揽收率商品。小数，“-”分隔，左边为区间最小值，右边为区间最大值
     */
    private String filterOrder48HPickUpRate;

    /**
     * 筛选出最近30天24小时内揽收率商品。小数，“-”分隔，左边为区间最小值，右边为区间最大值
     */
    private String filterOrder24HPickUpRate;

    /**
     * 筛选出支持抖音密文下单商品
     */
    private Boolean filterDyEncryptOrder;

    /**
     * 筛选出支持快手密文下单商品
     */
    private Boolean filterKsEncryptOrder;

    /**
     * 筛选出可获取推广服务费
     */
    private Boolean filterCommission;

    /**
     * 列表排序字段。最近30天旺旺3分钟响应率：ww30DResponseRate；1688新灯塔纠纷分：newTowerDisputeScore；最近30天成交回头率：order30DTurnoverRate。不传默认商品90天销量排序
     */
    private String sortByField;

    /**
     * 排序类型：desc、asc。默认desc
     */
    private String sortByType;

}
