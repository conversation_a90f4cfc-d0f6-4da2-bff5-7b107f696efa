package com.newnary.gsp.center.tpsi.infra.client.haiying.valobj.response.lazada;

import lombok.Data;

/**
 * @Author: jack
 * @CreateTime: 2022-7-11
 */
@Data
public class HaiYingLazadaProductListResponse {

    /**
     * 商品id
     */
    private String item_id;

    /**
     * 商品状态(0正常,1异常)
     */
    private String status;

    /**
     * 商品存在(0存在，1不存在)
     */
    private String not_exist;

    /**
     * 商品前7天销量
     */
    private String seven_days_sold;

    /**
     * 商品前7天销售额
     */
    private String seven_days_payment;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品品牌
     */
    private String brand;

    /**
     * 商品价格
     */
    private String price;

    /**
     * 商品评分
     */
    private String rating;

    /**
     * 商品评论数
     */
    private String review;

    /**
     * 商品问答数
     */
    private String qna;

    /**
     * 商品所属类目链接
     */
    private String curl;

    /**
     * 商品主图
     */
    private String main_image;

    /**
     * 商品最新抓取时间
     */
    private String last_modi_time;

    /**
     * 商品首次发现时间
     */
    private String insert_time;

    /**
     * 商品店铺名称
     */
    @Deprecated
    private String shop;

    /**
     * 商品所属卖家名称
     */
    private String seller;

    /**
     * 商品发货地
     */
    private String location;

    /**
     * 商品海鹰类目id路径(多个路径英文逗号分隔, 类目间用中横杠分隔)
     */
    private String category_path;

    /**
     * 商品类目名路径(多个路径<br/>分隔, 类目间用中横杠分隔)
     */
    private String category_structure;

    /**
     * 类目路径
     */
    private String cname_path;

    /**
     * 商品链接
     */
    private String item_url;

}
