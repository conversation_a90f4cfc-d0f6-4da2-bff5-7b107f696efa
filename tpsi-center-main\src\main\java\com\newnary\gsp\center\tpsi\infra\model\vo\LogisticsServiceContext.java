package com.newnary.gsp.center.tpsi.infra.model.vo;

import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: jack
 * @CreateTime: 2023-11-17
 */
public class LogisticsServiceContext extends ConcurrentHashMap<String, Object> {

    private static final ThreadLocal<LogisticsServiceContext> threadLocal = ThreadLocal.withInitial(LogisticsServiceContext::new);

    public static LogisticsServiceContext getCurrentContext() {
        return threadLocal.get();
    }

}
