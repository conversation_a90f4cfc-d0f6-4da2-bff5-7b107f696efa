package com.newnary.gsp.center.tpsi.infra.client.eccang;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newnary.common.utils.httpmethod.ApiBaseResult;
import com.newnary.common.utils.httpmethod.HttpMethodUtil;
import com.newnary.common.utils.httpmethod.XmlHandleUtil;
import com.newnary.common.utils.xml.JaxbUtil;
import com.newnary.gsp.center.tpsi.common.QpsCounter;
import com.newnary.gsp.center.tpsi.infra.client.eccang.params.EcCangERPParams;
import com.newnary.gsp.center.tpsi.infra.client.eccang.valobj.EcCangErpBaseRequest;
import lombok.extern.slf4j.Slf4j;

import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class EcCangERPApiClient {

    private String userName;

    private String userPass;

    private String ebUrl;

    private String wmsUrl;

    private static Integer RETRY_COUNT = 3;

    public EcCangERPApiClient(String ecCangERPParams) {
        EcCangERPParams params = JSON.parseObject(ecCangERPParams, EcCangERPParams.class);
        this.userName = params.getUserName();
        this.userPass = params.getUserPass();
        this.ebUrl = params.getEbUrl();
        this.wmsUrl = params.getWmsUrl();
    }

    public EcCangERPApiClient(EcCangERPParams params) {
        this.userName = params.getUserName();
        this.userPass = params.getUserPass();
        this.ebUrl = params.getEbUrl();
        this.wmsUrl = params.getWmsUrl();
    }

    public String sendRequest(Object param, String service, String type) {
        EcCangErpBaseRequest baseRequest = getERPBaseRequest(param, service);

        String requestXml = JaxbUtil.toXml(baseRequest);
//        log.info("[ecangERP] 请求参数 requestXml={}", requestXml);
        ApiBaseResult apiBaseResult = null;
        try {
            // 统计请求速率
            String qpsKey = type + ":" + service;
            QpsCounter.record(qpsKey);
            QpsCounter.record("ECAPI");

            apiBaseResult = HttpMethodUtil.syncPostMethodBySetBodyPara(type.equalsIgnoreCase("EB") ? ebUrl : wmsUrl,
                    0,
                    null,
                    "application/xml",
                    null, null, requestXml);
            log.info("[ecangERP] 请求结果解析前数据, apiBaseResult={}, param={}, service={}, type={}", apiBaseResult, JSON.toJSONString(param), service, type);
            // 状态码429, 请求频繁(code=429, ret=<h3 align="center">请求频率超限，请控制请求速度</h3>, message=null)
            if (apiBaseResult.getCode() == 429) {
                log.warn("请求频率超限: {} QPS ≈ {} last 5s QPS ≈ {} all ≈ {}", qpsKey, QpsCounter.getQps(qpsKey), QpsCounter.getQps(qpsKey, 5), QpsCounter.getQps("ECAPI"));
                return "";
            }
            //返回的是xml,需要进行处理得到里面的response
            JSONObject o = XmlHandleUtil.xmlHandle(apiBaseResult.getRet());
//            log.info("[ecangERP] 请求结果, response={}", o.toJSONString());
            String responseStr = o.getJSONObject("Body").getJSONObject("callServiceResponse").getJSONObject("response").toString();
            responseStr = decodeUnicode(responseStr);
//            responseStr = responseStr.replaceAll("%(?![0-9a-fA-F]{2})", "%25");
            if ("getProductList".equals(service)) {
                return responseStr;
            }
//            responseStr = URLDecoder.decode(responseStr, "UTF-8");
//            log.info("[ecangERP] 请求结束, requestXml={}, service={}, response={}", requestXml, service, responseStr);
            return responseStr;
        } catch (Exception e) {
            log.warn("请求或返回出错：{}", e.getMessage());
            return "";
        }
    }

    /**
     * 不进行解码，由于商品备注
     *
     * @param param
     * @param service
     * @param type
     * @return
     */
    public String sendRequestNoDecode(Object param, String service, String type) {
        EcCangErpBaseRequest baseRequest = getERPBaseRequest(param, service);

        String requestXml = JaxbUtil.toXml(baseRequest);
//        log.info("[ecangERP] 请求参数 requestXml={}", requestXml);
        ApiBaseResult apiBaseResult = null;
        try {
            apiBaseResult = HttpMethodUtil.syncPostMethodBySetBodyPara(type.equalsIgnoreCase("EB") ? ebUrl : wmsUrl,
                    0,
                    null,
                    "application/xml",
                    null, null, requestXml);
            // 状态码429, 请求频繁(code=429, ret=<h3 align="center">请求频率超限，请控制请求速度</h3>, message=null)
            if (apiBaseResult.getCode() == 429) {
                log.warn("请求频率超限");
                log.info("[ecangERP] 请求结果解析前数据, apiBaseResult={}, param={}, service={}, type={}", apiBaseResult, JSON.toJSONString(param), service, type);
                return "";
            }
            //返回的是xml,需要进行处理得到里面的response
            JSONObject o = XmlHandleUtil.xmlHandle(apiBaseResult.getRet());
//            log.info("[ecangERP] 请求结果, response={}", o.toJSONString());
            String responseStr = o.getJSONObject("Body").getJSONObject("callServiceResponse").getJSONObject("response").toString();
            responseStr = decodeUnicode(responseStr);
            responseStr = responseStr.replaceAll("%(?![0-9a-fA-F]{2})", "%25");
            if ("getProductList".equals(service)) {
                return responseStr;
            }
//            log.info("[ecangERP] 请求结束, requestXml={}, service={}, response={}", requestXml, service, responseStr);
            return responseStr;
        } catch (Exception e) {
            log.error("请求或返回出错：", e);
            return "";
        }
    }

    public EcCangErpBaseRequest getERPBaseRequest(Object param, String service) {
        EcCangErpBaseRequest request = new EcCangErpBaseRequest();
        request.body = new EcCangErpBaseRequest.RequestBody();
        request.body.callService = new EcCangErpBaseRequest.CallService();
        request.body.callService.userName = userName;
        request.body.callService.userPass = userPass;
        request.body.callService.service = service;
        if (null != param) {
            request.body.callService.paramJson = JSON.toJSONString(param);
        }
        return request;
    }

    private String decodeUnicode(String str) {
        Charset set = Charset.forName("UTF-16");
        Pattern p = Pattern.compile("\\\\u([0-9a-fA-F]{4})");
        Matcher m = p.matcher(str);
        int start = 0;
        int start2 = 0;
        StringBuffer sb = new StringBuffer();
        while (m.find(start)) {
            start2 = m.start();
            if (start2 > start) {
                String seg = str.substring(start, start2);
                sb.append(seg);
            }
            String code = m.group(1);
            int i = Integer.valueOf(code, 16);
            byte[] bb = new byte[4];
            bb[0] = (byte) ((i >> 8) & 0xFF);
            bb[1] = (byte) (i & 0xFF);
            ByteBuffer b = ByteBuffer.wrap(bb);
            sb.append(String.valueOf(set.decode(b)).trim());
            start = m.end();
        }
        start2 = str.length();
        if (start2 > start) {
            String seg = str.substring(start, start2);
            sb.append(seg);
        }
        return sb.toString();
    }

}
