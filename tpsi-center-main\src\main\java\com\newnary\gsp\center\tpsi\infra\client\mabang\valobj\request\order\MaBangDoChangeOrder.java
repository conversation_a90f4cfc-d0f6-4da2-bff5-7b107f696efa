package com.newnary.gsp.center.tpsi.infra.client.mabang.valobj.request.order;

import lombok.Data;

@Data
public class MaBangDoChangeOrder {

    /**
     * 订单编号 (必填)
     */
    private String platformOrderId;

    private String remark;

    private String buyerName;

    private String country;

    private String countryCode;

    private String province;

    private String city;

    private String postCode;

    private String email;

    private String doorcode;

    private String street1;

    private String street2;

    private String orderStatus;

    private String phone1;

    private String phone2;

    private String abnnumber;

    private String stockData;

    private String extendAttr;

    private String shippingCost;

    private String otherExpend;
}